/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   factory_mudule.h
@Time    :   2025/01/21 14:53:30
<AUTHOR>   nullptr
*
**************************************************************************/

#ifndef _FACTORY_MODULE_H_
#define _FACTORY_MODULE_H_

#include <stdint.h>
#include "cmsis_os2.h"
// #include "qw_general.h"
#include "qw_fs.h"

#ifdef SIMULATOR
#include <Windows.h>
#include <direct.h>
#else
#endif

#ifdef __cplusplus
extern "C"
{
#endif

#define FACTORY_MODE_ENABLE_  //禁止修改

#define FAT_DATA_FILE_LEN 128
#define FAT_DATA_LEN      252

#ifdef SIMULATOR
#define FAT_DATA_PATH "./iGPSPORT/System/factory_data"
#else
#define FAT_DATA_PATH "0:/iGPSPORT/System/factory_data"
#endif

//工模界面枚举
typedef enum {
    FAT_MENU_TYPE_SELFCHECK,       // 传感器开机自检
    FAT_MENU_TYPE_BAT,             // 电池数据
    FAT_MENU_TYPE_CURRENT,         // 电流测试
    FAT_MENU_TYPE_BRIGHTBESS,      // 亮度
    FAT_MENU_TYPE_SCREENCOLOR,     // 屏幕颜色
    FAT_MENU_TYPE_TOUCHPANEL,      // 触摸屏
    FAT_MENU_TYPE_LIGHT,           // 光感
    FAT_MENU_TYPE_KEY,             // 按键和光旋扭
    FAT_MENU_TYPE_BEEP,            // 蜂鸣器
    FAT_MENU_TYPE_MOTOR,           // 马达
    FAT_MENU_TYPE_BARO,            // 气压计
    FAT_MENU_TYPE_MAG,             // 电子罗盘
    FAT_MENU_TYPE_ATT,             // 六轴传感器
    FAT_MENU_TYPE_GOMORE,          // GOMORE
    FAT_MENU_TYPE_GPS,             // GPS
    FAT_MENU_TYPE_GPS_START,       // GPS_START
    FAT_MENU_TYPE_ANT,             // ANT
    FAT_MENU_TYPE_ANT_TEST,        // ANT_TEST
    FAT_MENU_TYPE_BURN_TEST,       // BURN_TEST
    FAT_MENU_TYPE_MODE,            // MODE
    FAT_MENU_TYPE_QRCODE,          // QRCODE
    FAT_MENU_TYPE_TIME,            // TIME
    FAT_MENU_TYPE_IMAGE,           // IMAGE
    FAT_MENU_TYPE_DEVELOP,         // DEVELOP
    FAT_MENU_TYPE_ABOUT,           // ABOUT
    FAT_MENU_TYPE_MAX,             //
} fat_menu_t;

typedef enum
{
    factory_close,//退出
    factory_runging,//正常运行
    factory_breaths_creen,//power息屏触发
    factory_max,
}factory_state_t;


//修改时需同步修改 file_name_arry
typedef enum {
    FAT_DATA_TYPE_LIGHT,        // 光感数据
    FAT_DATA_TYPE_KNOB,      // 光旋扭数据
    FAT_DATA_TYPE_BAT,       //电池数据
    FAT_DATA_TYPE_BARO,       //气压数据
    FAT_DATA_TYPE_MXA,     // 字符串数据
} fat_data_t;

typedef enum {
    FAT_FILE_UNKNOW,
    FAT_FILE_OPEN_SUC,   //打开成功
    FAT_FILE_WR_RUN,     //写入中
    FAT_FILE_WR_OVER,    //写入完成
    FAT_FILE_CLOSE,      //关闭

} fat_file_state_t;

typedef struct
{
    QW_FIL* fp[FAT_DATA_TYPE_MXA];       // 文件句柄
    char filename[FAT_DATA_TYPE_MXA][FAT_DATA_FILE_LEN];   // 文件名
    osMutexId_t mutex[FAT_DATA_TYPE_MXA];       // 互斥锁
    fat_file_state_t file_state[FAT_DATA_TYPE_MXA];        //文件状态
} fat_file_t;

//工模模块：初始化工厂模块服务
void factory_module_init(void);

//工模模块：反初始化工厂模块服务
void factory_module_deinit(void);

//工模模块：打开文件（a）
void factory_module_open_file(QW_FIL** fp ,fat_data_t type);

//工模模块：写文件（a）
void factory_module_write_file(fat_data_t type, const char *format, ...);

//工模模块：关闭文件（a）
void factory_module_close_file(fat_data_t type);

//工模模块：获取工模当前状态
factory_state_t factory_module_get_state(void);

//工模模块：设置工模当前状态
void factory_module_set_state(factory_state_t state);

//工模模块：获取文件句柄
QW_FIL** factory_module_get_file_handle(fat_data_t type);

//工模模块：按键事件处理 false全局按键服务不可执行，true全局按键服务可执行
bool factory_module_upload_key_event(uint8_t *key, const char *page_name);

//强制常亮备份，恢复
void factory_module_sleep(bool s);

//工模界面字符串
extern const char *g_factory_page_list[];
extern const char *g_factory_main_page;

//产测模式
typedef enum {
    STEP_NULL,  /* 组件来料测试 */
    STEP_PCBA,  /* PCBA单板测试 */
    STEP_SFG,   /* 半成品测试 */
    STEP_FG,    /* 成品测试 */
    STEP_OLD,   /* 老化测试 */
    STEP_PK,    /* 出厂 */
    STEP_CP = STEP_NULL, /* 组件来料测试 */
} fat_step_t;

typedef enum {
    CP_STEP_NULL,  /* 未知 */
    CP_STEP_BAT,   /* 电池 */
    CP_STEP_LCD,   /* 屏幕 */
    CP_STEP_PPG,   /* PPG小板 */
} fat_cp_step_t;

typedef struct {
    uint8_t mode;       // 标记正在测试的大类
    uint8_t sub_mode;   // 标记正在测试的小类
    uint8_t item;       // 标记正在测试的项
    bool    busy;       // 标记正在测试中
} factory_test_info_t;

typedef struct {
    uint32_t pcba;      // 单板测试结果   0 NG  1 PASS
    uint32_t sfg;       // 半成品测试结果 0 NG  1 PASS
    uint32_t fg;        // 成品测试结果   0 NG  1 PASS
    uint32_t pk;        // 出厂测试结果   0 NG  1 PASS
} factory_test_result_t;

typedef struct {
    uint32_t pcba;      // 单板测试标记   0 未测试  1 已测试
    uint32_t sfg;       // 半成品测试标记 0 未测试  1 已测试
    uint32_t fg;        // 成品测试标记   0 未测试  1 已测试
    uint32_t pk;        // 出厂测试标记   0 未测试  1 已测试
} factory_test_mark_t;

typedef struct {
    uint32_t pcba;      // 单板测试时间戳
    uint32_t sfg;       // 半成品测试时间戳
    uint32_t fg;        // 成品测试时间戳
    uint32_t pk;        // 出厂测试时间戳
} factory_test_time_t;

typedef struct {
    factory_test_info_t info;        // 当前测试信息
    factory_test_mark_t mark;        // 测试完成标记
    factory_test_time_t timestamp;   // 测试记录时间
    factory_test_result_t result;    // 测试结果
} factory_test_t;


#ifdef __cplusplus
}
#endif

#endif

