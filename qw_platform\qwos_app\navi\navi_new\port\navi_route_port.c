/************************************************************************​
*Copyright(c) 2024, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#include <string.h>
#include <stddef.h>
#include <stdbool.h>
#include "navi_util.h"
#include "navi_route_port.h"

// #define NAVI_NEW

#ifdef NAVI_NEW
#include "rtthread.h"
#endif

#ifdef IGS_DEV
#include "mem_section.h"
#endif

#ifdef IGS_DEV
static TcnxReader s_tcnx_reader;
static TcnxWriter s_tcnx_writer;
#else
static TcnxReader s_tcnx_reader = { 0 };
static TcnxWriter s_tcnx_writer = { 0 };
#endif

static NaviRouteData s_navi_route_data = { 0 };

//导航线路模块所需使用的子模块
//
//导航线路数据计算器
static NaviRouteDataCalculator s_navi_route_data_calculator = { 0 };

//导航线路路点采样器
#ifdef IGS_DEV
L2_RET_BSS_SECT_BEGIN(wp_sampler)
ALIGN(8) static NaviWaypointDc s_sample_wpdc_buf[NAVI_ROUTE_WP_SAMPLES_NUM];
ALIGN(4) static float s_dd_buf[NAVI_ROUTE_WP_SAMPLES_NUM];
L2_RET_BSS_SECT_END
static NaviRouteWpSampler s_navi_route_wp_sampler;
#else
static NaviWaypointDc s_sample_wpdc_buf[NAVI_ROUTE_WP_SAMPLES_NUM] = { 0 };
static float s_dd_buf[NAVI_ROUTE_WP_SAMPLES_NUM] = { 0 };
static NaviRouteWpSampler s_navi_route_wp_sampler = {
    .wpdc_buf = s_sample_wpdc_buf,
    .dd_buf = s_dd_buf,
    .capacity = NAVI_ROUTE_WP_SAMPLES_NUM,
};
#endif

//导航线路路点压缩器
#ifdef IGS_DEV
L2_RET_BSS_SECT_BEGIN(wp_compressor)
ALIGN(8) static NaviWaypointDc s_compressor_wpdc_buf[NAVI_ROUTE_WP_COMPRESS_BUF_SIZE];
L2_RET_BSS_SECT_END
static NaviRouteWpCompressor s_navi_route_wp_compressor;
#else
static NaviWaypointDc s_compressor_wpdc_buf[NAVI_ROUTE_WP_COMPRESS_BUF_SIZE] = { 0 };
static NaviRouteWpCompressor s_navi_route_wp_compressor = {
    .buf = s_compressor_wpdc_buf,
    .capacity = NAVI_ROUTE_WP_COMPRESS_BUF_SIZE,
    .h_thres = NAVI_ROUTE_WP_COMPRESS_H_THRES,
};
#endif

//导航线路路点分段器
#ifdef IGS_DEV
L2_RET_BSS_SECT_BEGIN(route_segmentor)
ALIGN(8) static NaviRouteSegment s_seg_buf[NAVI_ROUTE_SEGMENTS_NUM];
L2_RET_BSS_SECT_END
static NaviRouteSegmentor s_navi_route_segmentor;
#else
static NaviRouteSegment s_seg_buf[NAVI_ROUTE_SEGMENTS_NUM] = { 0 };
static NaviRouteSegmentor s_navi_route_segmentor = {
    .seg_buf = s_seg_buf,
    .capacity = NAVI_ROUTE_SEGMENTS_NUM,
    .INTERVAL = NAVI_ROUTE_SEGMENT_INTERVAL,
};
#endif

//导航线路模块所需使用的数据
//
//导航线路路点采样
#ifdef IGS_DEV
static NaviRouteWpSample s_navi_route_wp_sample;
#else
static NaviRouteWpSample s_navi_route_wp_sample = {
    .wpdc_buf = s_sample_wpdc_buf,
    .len = NAVI_ROUTE_WP_SAMPLES_NUM,
};
#endif

//导航线路分段
#ifdef IGS_DEV
static NaviRouteSegmentArray s_navi_route_segment_array;
#else
static NaviRouteSegmentArray s_navi_route_segment_array = {
    .segments = s_seg_buf,
    .len = NAVI_ROUTE_SEGMENTS_NUM,
};
#endif

//导航线路list
#ifdef IGS_DEV
L2_RET_BSS_SECT_BEGIN(wp_list)
ALIGN(8) static NaviWaypointDc s_wpdc_cache[NAVI_ROUTE_WP_CACHE_BUF_NUM][NAVI_ROUTE_WP_CACHE_BUF_SIZE];
L2_RET_BSS_SECT_END
static NaviWaypointDcBuf s_wpdc_buf[NAVI_ROUTE_WP_CACHE_BUF_NUM];
static NaviRouteWpList s_navi_route_wp_list;
#else
static NaviWaypointDc s_wpdc_cache[NAVI_ROUTE_WP_CACHE_BUF_NUM][NAVI_ROUTE_WP_CACHE_BUF_SIZE] = { 0 };;
static NaviWaypointDcBuf s_wpdc_buf[NAVI_ROUTE_WP_CACHE_BUF_NUM] = {
    [0] = {
        .buf = *(s_wpdc_cache + 0),
        .capacity = NAVI_ROUTE_WP_CACHE_BUF_SIZE,
    },
    [1] = {
        .buf = *(s_wpdc_cache + 1),
        .capacity = NAVI_ROUTE_WP_CACHE_BUF_SIZE,
    },
    [2] = {
        .buf = *(s_wpdc_cache + 2),
        .capacity = NAVI_ROUTE_WP_CACHE_BUF_SIZE,
    },
    [3] = {
        .buf = *(s_wpdc_cache + 3),
        .capacity = NAVI_ROUTE_WP_CACHE_BUF_SIZE,
    },
    [4] = {
        .buf = *(s_wpdc_cache + 4),
        .capacity = NAVI_ROUTE_WP_CACHE_BUF_SIZE,
    },
    [5] = {
        .buf = *(s_wpdc_cache + 5),
        .capacity = NAVI_ROUTE_WP_CACHE_BUF_SIZE,
    },
    [6] = {
        .buf = *(s_wpdc_cache + 6),
        .capacity = NAVI_ROUTE_WP_CACHE_BUF_SIZE,
    },
    [7] = {
        .buf = *(s_wpdc_cache + 7),
        .capacity = NAVI_ROUTE_WP_CACHE_BUF_SIZE,
    },
};
static NaviRouteWpList s_navi_route_wp_list = {
    .cache = {
        .wpdc_buf = s_wpdc_buf,
        .tcnx_reader = &s_tcnx_reader,
        .capacity = NAVI_ROUTE_WP_CACHE_BUF_NUM,
    },
    .len = 0,
};
#endif

//保存指定路点前后的路点的缓冲区
#ifdef IGS_DEV
L2_RET_BSS_SECT_BEGIN(wp_nearby)
ALIGN(8) static NaviWaypointDc s_navi_route_wp_nearby_buf[NAVI_ROUTE_WP_NEARBY_BUF_SIZE];
L2_RET_BSS_SECT_END

//导航线路路点加载器
static NaviRouteWpLoader s_navi_route_wp_loader;
//指定路点前后的路点
static NaviRouteWpNearby s_navi_route_wp_nearby;
#else
static NaviWaypointDc s_navi_route_wp_nearby_buf[NAVI_ROUTE_WP_NEARBY_BUF_SIZE] = { 0 };

//导航线路路点加载器
static NaviRouteWpLoader s_navi_route_wp_loader = {
    .wp_list = &s_navi_route_wp_list,
    .route_data = &s_navi_route_data,
    .buf = s_navi_route_wp_nearby_buf,
    .capacity = NAVI_ROUTE_WP_NEARBY_BUF_SIZE,
};

//指定路点前后的路点
static NaviRouteWpNearby s_navi_route_wp_nearby = {
    .buf = s_navi_route_wp_nearby_buf,
    .len = 0,
};
#endif

#ifdef IGS_DEV
//导航线路匹配器
static NaviRouteMatcher s_navi_route_matcher;
//导航线路查找器
static NaviRouteFinder s_navi_route_finder;
#else
//导航线路匹配器
static NaviRouteMatcher s_navi_route_matcher = {
    .wp_list = &s_navi_route_wp_list,
    .seg_array = &s_navi_route_segment_array,
    .route_data = &s_navi_route_data,
};

//导航线路查找器
static NaviRouteFinder s_navi_route_finder = {
    .wp_list = &s_navi_route_wp_list,
    .seg_array = &s_navi_route_segment_array,
    .route_data = &s_navi_route_data,
};
#endif

static char s_tcnx_path[100] = { 0 };

//每次启用一个线路时，必须重置后使用，否则内部一些状态数据可能仍是上一条线路的数据
static void navi_route_reset(void)
{
    navi_route_data_calculator_reset(&s_navi_route_data_calculator);
    navi_route_wp_sampler_reset(&s_navi_route_wp_sampler);
    navi_route_wp_compressor_reset(&s_navi_route_wp_compressor);
    navi_route_segmentor_reset(&s_navi_route_segmentor);
    navi_route_wp_list_reset(&s_navi_route_wp_list);
    navi_route_matcher_reset(&s_navi_route_matcher);
    navi_route_wp_loader_reset(&s_navi_route_wp_loader);

    //如果已有打开的文件，则关闭之，这是因为之前可能已经启用了一个路书
    if (s_tcnx_reader.fp != NULL)
    {
        qw_f_close(s_tcnx_reader.fp);
        s_tcnx_reader.fp = NULL;
    }
}

//上电后调用，初始化导航线路处理模块
void navi_route_init(void)
{
#ifdef IGS_DEV
    //初始化导航线路模块所需使用的子模块
    //
    //初始化tcnx读写
    s_tcnx_writer.fp = NULL;
    s_tcnx_reader.fp = NULL;

    navi_route_data_calculator_reset(&s_navi_route_data_calculator);

    //初始化导航线路路点采样器
    s_navi_route_wp_sampler.wpdc_buf = s_sample_wpdc_buf;
    s_navi_route_wp_sampler.dd_buf = s_dd_buf;
    s_navi_route_wp_sampler.capacity = NAVI_ROUTE_WP_SAMPLES_NUM;
    navi_route_wp_sampler_reset(&s_navi_route_wp_sampler);

    //初始化路点压缩器
    s_navi_route_wp_compressor.buf = s_compressor_wpdc_buf;
    s_navi_route_wp_compressor.capacity = NAVI_ROUTE_WP_COMPRESS_BUF_SIZE;
    s_navi_route_wp_compressor.h_thres = NAVI_ROUTE_WP_COMPRESS_H_THRES;
    navi_route_wp_compressor_reset(&s_navi_route_wp_compressor);

    //初始化导航线路分段器
    s_navi_route_segmentor.seg_buf = s_seg_buf;
    s_navi_route_segmentor.capacity = NAVI_ROUTE_SEGMENTS_NUM;
    s_navi_route_segmentor.INTERVAL = NAVI_ROUTE_SEGMENT_INTERVAL;
    navi_route_segmentor_reset(&s_navi_route_segmentor);

    //初始化导航线路模块所需使用的数据
    //
    //初始化导航线路路点采样
    s_navi_route_wp_sample.wpdc_buf = s_sample_wpdc_buf;
    s_navi_route_wp_sample.len = NAVI_ROUTE_WP_SAMPLES_NUM;

    //初始化导航线路分段
    s_navi_route_segment_array.segments = s_seg_buf;
    s_navi_route_segment_array.len = NAVI_ROUTE_SEGMENTS_NUM;

    //初始化路点列表
    for (uint32_t i = 0; i < NAVI_ROUTE_WP_CACHE_BUF_NUM; i++)
    {
        s_wpdc_buf[i].buf = *(s_wpdc_cache + i);
        s_wpdc_buf[i].capacity = NAVI_ROUTE_WP_CACHE_BUF_SIZE;
    }
    s_navi_route_wp_list.cache.wpdc_buf = s_wpdc_buf;
    s_navi_route_wp_list.cache.tcnx_reader = &s_tcnx_reader;
    s_navi_route_wp_list.cache.capacity = NAVI_ROUTE_WP_CACHE_BUF_NUM;
    s_navi_route_wp_list.len = 0;
    navi_route_wp_list_reset(&s_navi_route_wp_list);

    //初始化附近路点及其加载器
    s_navi_route_wp_loader.wp_list = &s_navi_route_wp_list;
    s_navi_route_wp_loader.route_data = &s_navi_route_data;
    s_navi_route_wp_loader.buf = s_navi_route_wp_nearby_buf;
    s_navi_route_wp_loader.capacity = NAVI_ROUTE_WP_NEARBY_BUF_SIZE;
    navi_route_wp_loader_reset(&s_navi_route_wp_loader);

    s_navi_route_wp_nearby.buf = s_navi_route_wp_nearby_buf;
    s_navi_route_wp_nearby.len = 0;

    //初始化导航线路匹配器和查找器
    s_navi_route_matcher.wp_list = &s_navi_route_wp_list;
    s_navi_route_matcher.seg_array = &s_navi_route_segment_array;
    s_navi_route_matcher.route_data = &s_navi_route_data;
    navi_route_matcher_reset(&s_navi_route_matcher);

    s_navi_route_finder.wp_list = &s_navi_route_wp_list;
    s_navi_route_finder.seg_array = &s_navi_route_segment_array;
    s_navi_route_finder.route_data = &s_navi_route_data;

#else
    navi_route_reset();
#endif
}

//关机前调用，执行一些清理操作
void navi_route_uninit(void)
{
    if (s_tcnx_reader.fp != NULL)
    {
        qw_f_close(s_tcnx_reader.fp);
        s_tcnx_reader.fp = NULL;
    }
}

//检查导航文件对应的tcnx文件是否有效，有效则后续可以直接使用，不必再进行计算
//true - 有效
//false - 无效
int navi_route_is_tcnx_valid(const char *path)
{
    if (path == NULL || strlen(path) > MAX_ROUTE_PATHNAME_LEN)
    {
        return false;
    }

    //逻辑上s_tcnx_reader.fp必须为NULL
    if (s_tcnx_reader.fp != NULL)
    {
        return false;
    }

    uint8_t header[32] = { 0 };

    sprintf(s_tcnx_path, "%s.tcnx", path);

    //文件打开失败（通常是因为不存在），则视为无效
    if (qw_f_open(&s_tcnx_reader.fp, s_tcnx_path, QW_FA_READ) != QW_OK)
    {
        s_tcnx_reader.fp = NULL;
        return false;
    }

    //文件头读取失败（文件损坏），则视为无效
    if (tcnx_header_read(&s_tcnx_reader, header) != 0)
    {
        goto tcnx_invalid;
    }

    //文件标识符不一致，则文件无效
    if (header[0] != 'T' || header[1] != 'C' || header[2] != 'N' || header[3] != 'X')
    {
        goto tcnx_invalid;
    }

    //文件版本不一致，则文件无效
    if (header[4] != NAVI_FILE_MAJOR_VERSION || header[5] != NAVI_FILE_MINOR_VERSION)
    {
        goto tcnx_invalid;
    }

    qw_f_close(s_tcnx_reader.fp);
    s_tcnx_reader.fp = NULL;

    return true;

tcnx_invalid:
    qw_f_close(s_tcnx_reader.fp);
    s_tcnx_reader.fp = NULL;
    return false;
}

//tcnx文件有效，则启用导航文件时只需加载tcnx文件即可，无需再次计算
int navi_route_tcnx_load(const char *path)
{
    if (path == NULL || strlen(path) > MAX_ROUTE_PATHNAME_LEN)
    {
        return -1;
    }

    navi_route_reset();

    sprintf(s_tcnx_path, "%s.tcnx", path);

    if (qw_f_open(&s_tcnx_reader.fp, s_tcnx_path, QW_FA_READ) != QW_OK)
    {
        s_tcnx_reader.fp = NULL;
        return -1;
    }

    if (tcnx_route_data_read(&s_tcnx_reader, &s_navi_route_data) != 0)
    {
        goto err_handler;
    }

    if (tcnx_route_wp_sample_read(&s_tcnx_reader, &s_navi_route_wp_sample) != 0)
    {
        goto err_handler;
    }

    if (tcnx_route_segment_array_read(&s_tcnx_reader, &s_navi_route_segment_array) != 0)
    {
        goto err_handler;
    }

    if (tcnx_route_wp_compressed_num_read(&s_tcnx_reader, &s_navi_route_wp_list.len) != 0)
    {
        goto err_handler;
    }

    s_navi_route_wp_list.cache.tcnx_reader = &s_tcnx_reader;

    return 0;

err_handler:
    qw_f_close(s_tcnx_reader.fp);
    s_tcnx_reader.fp = NULL;
    return -1;
}

//导航线路处理开始时调用，执行一些初始化操作
int navi_route_process_start(const char *path)
{
    if (path == NULL || strlen(path) > MAX_ROUTE_PATHNAME_LEN)
    {
        return -1;
    }

    sprintf(s_tcnx_path, "%s.tcnx", path);

#ifdef NAVI_NEW
    rt_kprintf("[NAVI_NEW] tcnx start, writer.fp = %x, reader.fp = %x\n", s_tcnx_writer.fp, s_tcnx_reader.fp);
#endif

    if (qw_f_open(&s_tcnx_writer.fp, s_tcnx_path, QW_FA_CREATE_ALWAYS | QW_FA_WRITE) != QW_OK)
    {
        s_tcnx_writer.fp = NULL;
        return -1;
    }

#ifdef NAVI_NEW
    rt_kprintf("[NAVI_NEW] tcnx created, writer.fp = %x, reader.fp = %x\n", s_tcnx_writer.fp, s_tcnx_reader.fp);
#endif

    s_navi_route_wp_sample.len = NAVI_ROUTE_WP_SAMPLES_NUM;
    s_navi_route_segment_array.len = NAVI_ROUTE_SEGMENTS_NUM;
    s_navi_route_wp_list.len = 0;

    //写入占位用的数据
    //当前使用的文件系统FatFs不支持fseek到一个未写入的位置，所以只能先写入占位用的数据
    if (tcnx_header_placeholder_write(&s_tcnx_writer) != 0)
    {
        goto err_handler;
    }
    if (tcnx_route_data_write(&s_tcnx_writer, &s_navi_route_data) != 0)
    {
        goto err_handler;
    }
    if (tcnx_route_wp_sample_write(&s_tcnx_writer, &s_navi_route_wp_sample) != 0)
    {
        goto err_handler;
    }
    if (tcnx_route_segment_array_write(&s_tcnx_writer, &s_navi_route_segment_array) != 0)
    {
        goto err_handler;
    }
    if (tcnx_route_wp_compressed_num_write(&s_tcnx_writer, 0) != 0)
    {
        goto err_handler;
    }

    navi_route_reset();

    return 0;

err_handler:
    qw_f_close(s_tcnx_writer.fp);
    s_tcnx_writer.fp = NULL;
    qw_f_unlink(s_tcnx_path);
    return -1;
}

//导航线路处理
int navi_route_process(const NaviWaypointAdc *wpadc)
{
    if (wpadc == NULL)
    {
        return -1;
    }

    const NaviWaypointAd wpad = {
        .lng = wpadc->lng,
        .lat = wpadc->lat,
        .alt = wpadc->alt,
        .dist = wpadc->dist,
    };
    const NaviWaypointDc wpdc_in = {
        .lng = wpadc->lng,
        .lat = wpadc->lat,
        .dist = wpadc->dist,
        .course = wpadc->course,
    };

    NaviWaypointDc wpdc_out = { 0 };

    //计算线路数据
    navi_route_data_calculator_exec(&s_navi_route_data_calculator, &wpad);

    //全局路点采样
    navi_route_wp_sampler_exec(&s_navi_route_wp_sampler, &wpdc_in);

    //线路压缩
    if (navi_route_wp_compressor_exec(&s_navi_route_wp_compressor, &wpdc_in, &wpdc_out) != 0)
    {
        return 0;
    }

    //将压缩得到的路点写入文件
    if (tcnx_route_wp_compressed_data_write(&s_tcnx_writer, &wpdc_out) != 0)
    {
        goto err_handler;
    }

    s_navi_route_wp_list.len += 1;

    //线路分段
    navi_route_segmentor_exec(&s_navi_route_segmentor, &wpdc_out);

    return 0;

err_handler:
    qw_f_close(s_tcnx_writer.fp);
    s_tcnx_writer.fp = NULL;
    qw_f_unlink(s_tcnx_path);
    return -1;
}

//导航线路处理结束时调用，负责处理最后一个路点
int navi_route_process_end(const NaviWaypointAdc *wpadc)
{
    if (wpadc == NULL)
    {
        return -1;
    }

    if (navi_route_process(wpadc) != 0)
    {
        return -1;
    }

    NaviWaypointDc wpdc_out = { 0 };

    if (navi_route_wp_compressor_end(&s_navi_route_wp_compressor, &wpdc_out) != 0)
    {
        goto err_handler;
    }

    //必须保存最后一个有效路点（通常就是终点）
    if (tcnx_route_wp_compressed_data_write(&s_tcnx_writer, &wpdc_out) != 0)
    {
        goto err_handler;
    }

    s_navi_route_wp_list.len += 1;

    navi_route_segmentor_exec(&s_navi_route_segmentor, &wpdc_out);

    //完成线路分段
    navi_route_segmentor_end(&s_navi_route_segmentor);

    //获取线路各数据项，用于后续写入
    navi_route_data_calculator_data_get(&s_navi_route_data_calculator, &s_navi_route_data);
    navi_route_wp_sampler_data_get(&s_navi_route_wp_sampler, &s_navi_route_wp_sample);
    navi_route_segmentor_data_get(&s_navi_route_segmentor, &s_navi_route_segment_array);

    if (tcnx_route_data_write(&s_tcnx_writer, &s_navi_route_data) != 0)
    {
        goto err_handler;
    }
    if (tcnx_route_wp_sample_write(&s_tcnx_writer, &s_navi_route_wp_sample) != 0)
    {
        goto err_handler;
    }
    if (tcnx_route_segment_array_write(&s_tcnx_writer, &s_navi_route_segment_array) != 0)
    {
        goto err_handler;
    }
    if (tcnx_route_wp_compressed_num_write(&s_tcnx_writer, s_navi_route_wp_list.len) != 0)
    {
        goto err_handler;
    }
    if (tcnx_header_write(&s_tcnx_writer) != 0)
    {
        goto err_handler;
    }

    qw_f_close(s_tcnx_writer.fp);
    s_tcnx_writer.fp = NULL;

#ifdef NAVI_NEW
    rt_kprintf("[NAVI_NEW] tcnx end, writer.fp = %x, reader.fp = %x\n", s_tcnx_writer.fp, s_tcnx_reader.fp);
#endif

    if (qw_f_open(&s_tcnx_reader.fp, s_tcnx_path, QW_FA_READ) != QW_OK)
    {
        s_tcnx_reader.fp = NULL;
        return -1;
    }

#ifdef NAVI_NEW
    rt_kprintf("[NAVI_NEW] tcnx reopened, writer.fp = %x, reader.fp = %x\n", s_tcnx_writer.fp, s_tcnx_reader.fp);
#endif

    s_navi_route_wp_list.cache.tcnx_reader = &s_tcnx_reader;

    return 0;

err_handler:
    qw_f_close(s_tcnx_writer.fp);
    s_tcnx_writer.fp = NULL;
    qw_f_unlink(s_tcnx_path);
    return -1;
}

//中止导航线路处理
void navi_route_process_terminate(void)
{
    if (s_tcnx_writer.fp != NULL)
    {
        qw_f_close(s_tcnx_writer.fp);
        s_tcnx_writer.fp = NULL;
        qw_f_unlink(s_tcnx_path);
    }
}

//导航线路处理成功，但由于外部原因不能使用，退出
void navi_route_process_exit(void)
{
    if (s_tcnx_reader.fp != NULL)
    {
        qw_f_close(s_tcnx_reader.fp);
        s_tcnx_reader.fp = NULL;
        qw_f_unlink(s_tcnx_path);
    }
}

//导航线路匹配
int navi_route_match(double lng, double lat, float course, uint8_t is_reverse, NaviRouteProgress *progress)
{
    return navi_route_matcher_exec(&s_navi_route_matcher, lng, lat, course, is_reverse, progress);
}

//导航线路最近点查找
int navi_route_find(double lng, double lat, uint8_t is_reverse, NaviRouteProgress *progress)
{
    return navi_route_finder_exec(&s_navi_route_finder, lng, lat, is_reverse, progress);
}

//导航线路指定距离的路点查找
int navi_route_wp_find(float dist, uint8_t is_reverse, double *lng, double *lat)
{
    return navi_route_finder_exec2(&s_navi_route_finder, dist, is_reverse, lng, lat);
}

//导航线路附近路点加载
int navi_route_wp_load(uint8_t is_reverse, uint32_t idx)
{
    return navi_route_wp_loader_exec(&s_navi_route_wp_loader, is_reverse, idx);
}

//停止导航线路匹配
void navi_route_match_stop(void)
{
    if (s_tcnx_reader.fp != NULL)
    {
        qw_f_close(s_tcnx_reader.fp);
        s_tcnx_reader.fp = NULL;
    }
}

//反转线路数据
void navi_route_reverse(void)
{
    //反转线路总升总降
    const float tmp = s_navi_route_data.ascent;
    s_navi_route_data.ascent = s_navi_route_data.descent;
    s_navi_route_data.descent = tmp;

    NaviWaypointDc wpdc = { 0 };

    //反转线路采样点的距离和航向
    for (uint32_t i = 0; i < s_navi_route_wp_sample.len; i++)
    {
        s_navi_route_wp_sample.wpdc_buf[i].dist = s_navi_route_data.dist - s_navi_route_wp_sample.wpdc_buf[i].dist;
        s_navi_route_wp_sample.wpdc_buf[i].course = navi_util_course_reverse_calc(s_navi_route_wp_sample.wpdc_buf[i].course);
    }

    //重新排列线路采样点
    for (uint32_t i = 0; i < s_navi_route_wp_sample.len / 2; i++)
    {
        navi_waypoint_dc_copy(&wpdc, &s_navi_route_wp_sample.wpdc_buf[i]);
        navi_waypoint_dc_copy(&s_navi_route_wp_sample.wpdc_buf[i], &s_navi_route_wp_sample.wpdc_buf[s_navi_route_wp_sample.len-1-i]);
        navi_waypoint_dc_copy(&s_navi_route_wp_sample.wpdc_buf[s_navi_route_wp_sample.len-1-i], &wpdc);
    }
}

//获取导航线路数据
const NaviRouteData* navi_route_data_get_impl(void)
{
    return &s_navi_route_data;
}

//获取导航线路路点采样
const NaviRouteWpSample* navi_route_wp_sample_get_impl(void)
{
    return &s_navi_route_wp_sample;
}

//获取导航线路指定路点前后的路点
const NaviRouteWpNearby* navi_route_wp_nearby_get_impl(void)
{
    navi_route_wp_loader_data_get(&s_navi_route_wp_loader, &s_navi_route_wp_nearby);
    return &s_navi_route_wp_nearby;
}
