/************************************************************
* 
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   db_health_blood_oxygen.c
@Time    :   2025/01/15 17:51:37
* 
************************************************************/
#include <string.h>
#include "db_health_blood_oxygen.h"
#include "service_config.h"
#include "service_health.h"
#include "db_health_heartrate.h"
#include "service_gui_health.h"
#include "qw_fs.h"
#include "service_datetime.h"
#include "sp_crc16.h"
#include "subscribe_data_protocol.h"


static spo2_data_t g_spo2_last_point = {0};
static spo2_cache_data_t g_spo2_cache_data = {0};
static spo2_minimax_t g_spo2_minimax = {0};
static db_spo2_chart_t g_spo2_chart_data = {0};
static db_spo2_history_t g_history_spo2 = {0};
static spo2_sleep_period_t g_spo2_sleep_period = {0};



void db_health_spo2_sleep_data_update(spo2_data_t *data)
{
    if (NULL == data) {
        return;
    }
    if (g_spo2_sleep_period.num >= DB_SPO2_SLEEP_NUM_MAX) {
        HEALTH_LOG_D("sleep spo2 num is invaild:%d", g_spo2_sleep_period.num);
        return;
    }
    if (data->spo2 > g_spo2_sleep_period.max) {
        g_spo2_sleep_period.max = data->spo2;
    }
    if (data->spo2 < g_spo2_sleep_period.min) {
        g_spo2_sleep_period.min = data->spo2;
    }
    g_spo2_sleep_period.timestamp = data->timestamp;
    g_spo2_sleep_period.timezone = data->timezone;
    g_spo2_sleep_period.data[g_spo2_sleep_period.num] = data->spo2;
    g_spo2_sleep_period.num++;
}

void db_health_sleep_spo2_statistic(void)
{
    uint16_t sum_value = 0;

    for (uint8_t i = 0; i < g_spo2_sleep_period.num; i++) {
        if (g_spo2_sleep_period.data[i] != 0) {
            sum_value += g_spo2_sleep_period.data[i];
        }
    }
    if (g_spo2_sleep_period.num != 0) {
        g_spo2_sleep_period.avg = sum_value / g_spo2_sleep_period.num;
    }
}

static void db_health_spo2_sleep_gui_data_update(void)
{
    gui_slp_spo2_data_t gui_slp_spo2_data = {0};

    gui_slp_spo2_data.min = g_spo2_sleep_period.min;
    gui_slp_spo2_data.max = g_spo2_sleep_period.max;
    gui_slp_spo2_data.avg = g_spo2_sleep_period.avg;

    service_gui_set_slp_spo2(&gui_slp_spo2_data);
}

void db_health_spo2_sleep_save(void)
{
    uint8_t filepath[HEALTH_FILE_NAMEMAX] = {0};
    file_info_t file_info = {
        .file_version = SLP_SPO2_FILE_VERSION,
        .struct_size = sizeof(spo2_sleep_period_t)};
    qw_tm_t rtc_time = {0};
    UINT byte = 0;
    QW_FIL* fp = NULL;
    QW_FRESULT ret_head = 0;
    QW_FRESULT ret_data = 0;

    if (g_spo2_sleep_period.timestamp == 0) {
        g_spo2_sleep_period.timestamp = service_datetime_get_gmt_time();
        g_spo2_sleep_period.timezone = service_datetime_get_timezone();
    }
    service_datetime_gmt2datetime(g_spo2_sleep_period.timestamp, &rtc_time, g_spo2_sleep_period.timezone);
    snprintf((char *)filepath, HEALTH_FILE_NAMEMAX, "%s/%04d%02d%02d.slo",
             HLTH_DIR_STORE_HR, rtc_time.tm_year, rtc_time.tm_mon, rtc_time.tm_mday);
    ret_data = qw_f_open(&fp, (char *)filepath, QW_FA_OPEN_ALWAYS | QW_FA_WRITE);
    if (ret_data != FR_OK) {
        HEALTH_LOG_E("open report save %s failed %d", (const char *)filepath, ret_data);
        if (ret_data == FR_NO_PATH) {
            if (!find_dir(HLTH_DIR_STORE_HR)) {
                HEALTH_LOG_E("mkdir hr sub dir error!");
                return;
            } else {
                ret_data = qw_f_open(&fp, (char *)filepath, QW_FA_OPEN_ALWAYS | QW_FA_WRITE);
                if (ret_data != FR_OK) {
                    HEALTH_LOG_E("open report save1 %s failed %d", (const char *)filepath, ret_data);
                    return;
                }
            }
        } else {
            return;
        }
    }
    ret_head = qw_f_write(fp, &file_info, sizeof(file_info), &byte);
    byte = 0;
    ret_data = qw_f_write(fp, &g_spo2_sleep_period, sizeof(spo2_sleep_period_t), &byte);
    if (ret_head != 0 || byte != sizeof(spo2_sleep_period_t)) {
        HEALTH_LOG_E("[E] errType:%d%d, write %s failed", ret_head, byte, (const char *)filepath);
    }
    qw_f_close(fp);

    return;
}

static bool db_health_spo2_slp_data_read_from_file(void)
{
    file_info_t file_info = {0};
    static spo2_sleep_period_t spo2_slp = {0};
    uint8_t filename[HEALTH_FILE_NAMEMAX] = {0};
    QW_FIL* fp = NULL;
    QW_FRESULT ret = 0;
    UINT read_byte = 0;
    uint32_t slp_spo2_file_size = 0;

    qw_tm_t rtc_time = {0};
    uint32_t gmt = service_datetime_get_gmt_time();
    int16_t timezone = service_datetime_get_timezone();
    service_datetime_gmt2datetime(gmt, &rtc_time, timezone);
    snprintf((char *)filename, HEALTH_FILE_NAMEMAX, "%s/%04d%02d%02d.slo",
             HLTH_DIR_STORE_HR, rtc_time.tm_year, rtc_time.tm_mon, rtc_time.tm_mday);
    ret = qw_f_open(&fp, (char *)filename, QW_FA_OPEN_EXISTING | QW_FA_READ);
    //HEALTH_LOG_D("open report read %s", (const char *)filename);
    if (ret != 0) {
        HEALTH_LOG_E("open report read failed, return false");
        return false;
    } else {
        slp_spo2_file_size = qw_f_size(fp);
        if (slp_spo2_file_size != (sizeof(file_info_t) + sizeof(spo2_sleep_period_t))) {
            HEALTH_LOG_E("read spo2 slp file invaild: %d-%s", slp_spo2_file_size, (const char *)filename);
            qw_f_close(fp);
            qw_f_unlink((const char *)filename);
            return false;
        }
        ret = qw_f_read(fp, &file_info, sizeof(file_info_t), &read_byte);
        if (ret != 0) {
            HEALTH_LOG_E("fp db spo2 slp r header e");
        }
        ret = qw_f_read(fp, &spo2_slp, sizeof(spo2_sleep_period_t), &read_byte);
        if (ret != 0) {
            HEALTH_LOG_E("fp db spo2 slp r detail e");
        }
        qw_f_close(fp);
    }
    memcpy(&g_spo2_sleep_period, &spo2_slp, sizeof(spo2_sleep_period_t));

    return true;
}

bool db_health_spo2_slp_over(void)
{
    db_health_sleep_spo2_statistic();
    db_health_spo2_sleep_gui_data_update();
    db_health_spo2_sleep_save();
    memset(&g_spo2_sleep_period, 0x00, sizeof(spo2_sleep_period_t));
    g_spo2_sleep_period.min = 255;
    g_spo2_sleep_period.max = 0;
    return true;
}

static void db_health_spo2_minimax_update(const spo2_data_t* data)
{
    if(NULL == data) {
        return;
    }

    if(data->spo2 == 0 || data->timestamp == 0) {
        return;
    }

    if (data->spo2 > g_spo2_minimax.max.spo2) {
        memcpy(&g_spo2_minimax.max, data, sizeof(spo2_data_t));
        service_gui_set_spo2_minimax(&g_spo2_minimax);
    }
    if ((data->spo2 < g_spo2_minimax.min.spo2) || (0 == g_spo2_minimax.min.spo2)) {
        memcpy(&g_spo2_minimax.min, data, sizeof(spo2_data_t));
        service_gui_set_spo2_minimax(&g_spo2_minimax);
    }
}

static bool db_health_spo2_avg_insert_data(spo2_data_t* spo2_avg, uint8_t day_index)
{
    if (spo2_avg == NULL) {
        return false;
    }
    if (day_index > HEALTH_SPO2_WEEK_DAY) {
        return false;
    }

    spo2_data_t* p_spo2_avg = &g_history_spo2.data[day_index];
    p_spo2_avg->timestamp = spo2_avg->timestamp;
    p_spo2_avg->spo2 = spo2_avg->spo2;
    //HEALTH_LOG_D("read avg spo2:%d-%d", day_index, spo2_avg->spo2);

    return true;
}

static void db_health_spo2_historyinfo_gui_set(void)
{
    historyinfo_spo2_t historyinfo_spo2 = {0}; // GUI show
    uint8_t tmp_num = 0;
    spo2_data_t* p_spo2_avg = NULL;
    for (uint8_t i = 0; i < HEALTH_SPO2_WEEK_DAY; i++) {
        p_spo2_avg = &g_history_spo2.data[i];
        if (p_spo2_avg->spo2 != 0) {
            tmp_num++;
            historyinfo_spo2.spo2_avg[i] = p_spo2_avg->spo2;
            //HEALTH_LOG_D("db spo2 avg hstry ui:%u,%u-%u", i, p_spo2_avg->spo2, tmp_num);
        }
    }
    if (tmp_num != 0) {
        historyinfo_spo2.num = tmp_num;
    }
    service_gui_set_spo2_avg_historyinfo(&historyinfo_spo2);
}

uint16_t db_health_spo2_get_day_current_time_line_num(void)
{
    uint32_t gmt_time = service_datetime_get_gmt_time();
    uint32_t offset = service_datetime_get_curday_time_offset(gmt_time);
    uint16_t index = offset/ (60 * 60) + 1;

    return index;
}

static bool db_health_insert_one_spo2_data(spo2_data_t *data)
{
    if (data == NULL) {
        return false;
    }
    bool ret = false;
    db_spo2_period_t* spo2_period = NULL;
    int32_t offset = service_datetime_get_curday_time_offset(data->timestamp);
    uint16_t index = offset / (60 * 60);
    // HEALTH_LOG_D("db spo2 insert index:%u, offset:%d", index, offset);
    if ((data->spo2 >= SPO2_VALID_MIN) && (data->spo2 <= SPO2_VALID_MAX)) {
        if (index >= HEALTH_SPO2_GUI_LINE_NUM) {
            HEALTH_LOG_D("db spo2 insert out");
            return false;
        }
        spo2_period = &g_spo2_chart_data.spo2_data[index];
        // insert data
        if (spo2_period->data_len == 0) {
            spo2_period->data[0] = data->spo2;
            spo2_period->data_len++;
        } else if (spo2_period->data_len < DB_SPO2_VAL_NUM_MAX) {
            spo2_period->data[spo2_period->data_len] = data->spo2;
            spo2_period->data_len++;
        } else if (spo2_period->data_len >= DB_SPO2_VAL_NUM_MAX) {
            memmove(&spo2_period->data[0], &spo2_period->data[1], DB_SPO2_VAL_NUM_MAX - 1);
            spo2_period->data[DB_SPO2_VAL_NUM_MAX - 1] = data->spo2;
            spo2_period->data_len = DB_SPO2_VAL_NUM_MAX;
        }
        spo2_period->max = (data->spo2 > spo2_period->max) ? data->spo2 : spo2_period->max;
        if (spo2_period->min == 0) {
            spo2_period->min = data->spo2;
        } else {
            spo2_period->min = (data->spo2 < spo2_period->min) ? data->spo2 : spo2_period->min;
        }
        spo2_period->sum += data->spo2;
        spo2_period->num++;
        ret = true;
    }

    return ret;
}

static void db_health_spo2_chart_statistic(void)
{
    uint32_t sum_value = 0;
    uint16_t sum_num = 0;
    uint8_t minDB = 255;
    uint8_t maxDB = 0;
    uint16_t num = db_health_spo2_get_day_current_time_line_num();
    if (num > HEALTH_SPO2_GUI_LINE_NUM) {
        HEALTH_LOG_D("db spo2 chart statistic out of size");
        return;
    }
    for (int i = 0; i < num; i++) {
        if (g_spo2_chart_data.spo2_data[i].max != 0) {
            maxDB = (g_spo2_chart_data.spo2_data[i].max > maxDB) ? g_spo2_chart_data.spo2_data[i].max : maxDB;
            minDB = (g_spo2_chart_data.spo2_data[i].min < minDB) ? g_spo2_chart_data.spo2_data[i].min : minDB;
            sum_value += g_spo2_chart_data.spo2_data[i].sum;
            sum_num += g_spo2_chart_data.spo2_data[i].num;
        }
    }
    if (sum_num != 0) {
        g_spo2_chart_data.spo2_avg = (uint8_t)(sum_value / sum_num);
        g_spo2_chart_data.spo2_max = maxDB;
        g_spo2_chart_data.spo2_min = minDB;
    }

    return;
}

static void db_spo2_chart_line_set(gui_hlth_spo2_line_t* line_data, db_spo2_period_t* spo2_data)
{
    if (line_data == NULL || spo2_data == NULL) {
        HEALTH_LOG_E("db spo2 chart line calc NULL");
        return;
    }
    if (spo2_data->data_len == 0) {
        return;
    }
    if (spo2_data->data_len > DB_SPO2_VAL_NUM_MAX) {
        HEALTH_LOG_E("db spo2 chart line calc len out");
        return;
    }
    line_data->max = spo2_data->max;
    line_data->min = spo2_data->min;
    line_data->data = spo2_data->data[spo2_data->data_len - 1];
}

static void db_health_spo2_gui_data_notify(void)
{
    gui_hlth_spo2_24h_t gui_spo2_data = {0};
    spo2_data_t avg_spo2 = {0};
    uint16_t num = db_health_spo2_get_day_current_time_line_num();
    if (num > HEALTH_SPO2_GUI_LINE_NUM) {
        HEALTH_LOG_D("db spo2 gui notify index out");
        return;
    }
    for (uint16_t i = 0; i < num; i++) {
        if (g_spo2_chart_data.spo2_data[i].data_len != 0) {
            db_spo2_chart_line_set(&gui_spo2_data.data[i], &g_spo2_chart_data.spo2_data[i]);
        }
    }
    gui_spo2_data.max = g_spo2_chart_data.spo2_max;
    gui_spo2_data.min = g_spo2_chart_data.spo2_min;
    gui_spo2_data.num = num;
    gui_spo2_data.avg = g_spo2_chart_data.spo2_avg;
    avg_spo2.timestamp = g_spo2_last_point.timestamp;
    avg_spo2.timezone = service_datetime_get_timezone();
    avg_spo2.spo2 = g_spo2_chart_data.spo2_avg;
    db_health_spo2_avg_insert_data(&avg_spo2, 0);
    g_spo2_last_point.spo2_avg = avg_spo2.spo2;
    // set gui data and notify
    service_gui_set_spo2_bar_chart(&gui_spo2_data);
    db_health_spo2_historyinfo_gui_set();
}

static void db_health_spo2_point_recover_handle(spo2_data_t *data)
{
    if (data == NULL) {
        return;
    }
    // HEALTH_LOG_D("db spo2 p recover:%u-%u-%hu", data->spo2, data->timestamp, data->timezone);
    qw_tm_t data_rtc = {0};
    qw_tm_t tmp_rtc = {0};
    uint32_t gmt = service_datetime_get_gmt_time();
    int32_t timezone = service_datetime_get_timezone();
    service_datetime_gmt2datetime(data->timestamp, &data_rtc, data->timezone);
    for (uint8_t i = 0; i < HEALTH_SPO2_WEEK_DAY; i++) {
        service_datetime_gmt2datetime(gmt - HEALTH_ONE_DAY_SECOND * i , &tmp_rtc, timezone);
        if ((tmp_rtc.tm_mday == data_rtc.tm_mday) &&
            (tmp_rtc.tm_mon == data_rtc.tm_mon) &&
            (tmp_rtc.tm_year == data_rtc.tm_year)) {
            if (0 == i) {
                spo2_data_t period_data = {0};
                period_data.spo2 = data->spo2;
                period_data.timestamp = data->timestamp;
                period_data.timezone = data->timezone;
                period_data.spo2_avg = data->spo2_avg;
                period_data.mode = data->mode;
                service_gui_set_last_spo2(&period_data);
                db_health_spo2_minimax_update(&period_data);
                db_health_insert_one_spo2_data(&period_data);
                db_health_spo2_chart_statistic();
                //HEALTH_LOG_D("db spo2 p recover:%u,%u-%u-%u", data->spo2, data_rtc.tm_year, data_rtc.tm_mon, data_rtc.tm_mday);
            }
            db_health_spo2_avg_insert_data(data, i);
        }
    }
}

static void db_health_spo2_read_point_data_from_file(void)
{
    QW_FIL* fp = NULL;
    int ret = 0;

    ret = qw_f_open(&fp, HLTH_DIR_STORE_SPO2_POINT, QW_FA_OPEN_EXISTING | QW_FA_READ | QW_FA_WRITE);
    if (ret != 0) {
        HEALTH_LOG_E("db spo2 point r op E!");
        return;
    }
    uint32_t filesize = qw_f_size(fp);
    if(filesize == 0) {
        qw_f_close(fp);
        return;
    }

    UINT r_bytes = 0;
    spo2_data_t data = {0};
    uint8_t data_size = sizeof(spo2_data_t);
    hlth_store_header_t header_data = {0};
    qw_f_read(fp, &header_data, HEALTH_STORE_HEADER_SIZE, &r_bytes);
    if (r_bytes != HEALTH_STORE_HEADER_SIZE) {
        qw_f_close(fp);
        HEALTH_LOG_E("db spo2 p r header E");
        return;
    }
    uint16_t crc_val = sp_crc16((uint8_t *)&header_data, HEALTH_STORE_HEADER_SIZE - 2, CRC16_INITIAL);
    HEALTH_LOG_D("db spo2 p r CRC:%hu-%hu", header_data.crc, crc_val);
    if (header_data.crc != crc_val) {
        qw_f_close(fp);
        qw_f_unlink(HLTH_DIR_STORE_SPO2_POINT);
        HEALTH_LOG_E("db spo2 p r CRC E");
        return;
    }
    uint32_t r_offset = 0;
    if (filesize < HEALTH_SPO2_STORE_SIZE_MAX) {
        r_offset = HEALTH_STORE_HEADER_SIZE;
    } else {
        if (header_data.cur_offset == HEALTH_SPO2_STORE_SIZE_MAX) {
            r_offset = HEALTH_STORE_HEADER_SIZE;
        } else {
            r_offset = header_data.cur_offset;
        }
    }
    ret = qw_f_lseek(fp, r_offset);
    if (ret != 0) {
        qw_f_close(fp);
        HEALTH_LOG_E("db hr p r LSK E");
        return;
    }
    uint32_t max_while = (HEALTH_SPO2_STORE_SIZE_MAX - HEALTH_STORE_HEADER_SIZE) / data_size + 10;
    uint32_t count = 0;
    HEALTH_LOG_D("db spo2 p r offset:%u-%u-%u-%u", header_data.cur_offset, r_offset, filesize, max_while);
    do {
        // 防止异常死循环
        if (count > max_while) {
            HEALTH_LOG_E("db spo2 p r While E:%u", count);
            break;
        }
        count++;
        r_bytes = 0;
        qw_f_read(fp, &data, data_size, &r_bytes);
        if (r_bytes != data_size) {
            HEALTH_LOG_E("db spo2 p r dt size:%d", r_bytes);
            break;
        }
        db_health_spo2_point_recover_handle(&data);
        r_offset += data_size;
        if (r_offset == header_data.cur_offset) {
            HEALTH_LOG_E("db spo2 p r over");
            break;
        } else if (r_offset >= HEALTH_SPO2_STORE_SIZE_MAX) {
            r_offset = HEALTH_STORE_HEADER_SIZE;
            ret = qw_f_lseek(fp, HEALTH_STORE_HEADER_SIZE);
            if (ret == -1) {
                HEALTH_LOG_E("db spo2 p r LSK E");
                break;
            }
            HEALTH_LOG_E("db spo2 p r tail");
        }
    } while(r_bytes > 0);
    qw_f_close(fp);

    return;
}

bool db_health_spo2_point_cache_data_save(void)
{
    if(0 == g_spo2_cache_data.num){
        return true;
    }

    if (g_spo2_cache_data.num > SPO2_CACHE_NUM_MAX) {
        HEALTH_LOG_E("db spo2 p sv err:%u", g_spo2_cache_data.num);
        memset(&g_spo2_cache_data, 0x00, sizeof(spo2_cache_data_t));
        return false;
    }
    bool ret = db_health_cache_data_save(HLTH_DIR_STORE_SPO2_POINT, g_spo2_cache_data.data,
               g_spo2_cache_data.num * sizeof(spo2_data_t), HLTH_FILE_TYPE_SPO2);
    memset(&g_spo2_cache_data, 0x00, sizeof(spo2_cache_data_t));
    HEALTH_LOG_D("db spo2 p sv ret:%d", ret);
    return true;
}

static void db_health_spo2_gui_data_init(void)
{
    //HEALTH_LOG_D("db spo2 24h graph init");
    db_health_spo2_read_point_data_from_file();
    db_health_spo2_gui_data_notify();
}

bool db_health_spo2_point_update(spo2_data_t *spo2_data)
{
    if (spo2_data == NULL) {
        return false;
    }
    // HEALTH_LOG_D("db spo2 p:%u-%u", spo2_data->timestamp, spo2_data->spo2);
    memcpy(&g_spo2_last_point, spo2_data, sizeof(spo2_data_t));
    service_gui_set_realtime_spo2(spo2_data->spo2);
    service_gui_set_last_spo2(&g_spo2_last_point);

    return true;
}

bool db_health_spo2_point_over(void)
{
    HEALTH_LOG_D("db spo2 p over:%u-%u", g_spo2_last_point.spo2, g_spo2_last_point.timestamp);
    if (g_spo2_last_point.spo2 != 0) {
        spo2_data_t data = {0};
        uint8_t slp_sts = 0;
        uint8_t slp_type = 0;
        data.spo2 = g_spo2_last_point.spo2;
        data.timestamp = g_spo2_last_point.timestamp;
        data.timezone = g_spo2_last_point.timezone;
        db_health_spo2_minimax_update(&g_spo2_last_point);
        bool b_update = db_health_insert_one_spo2_data(&g_spo2_last_point);
        if (b_update) {
            db_health_spo2_chart_statistic();
            db_health_spo2_gui_data_notify();
        }
        data.spo2_avg = g_spo2_last_point.spo2_avg;
        data.mode = g_spo2_last_point.mode;
        slp_sts = service_gui_get_sleep_status();
        slp_type = service_gui_get_sleep_type();
        if (data.mode != SPO2_MODE_MANUAL) {
            if (slp_sts == SLEEP_ENTER_EVENT) {
                db_health_spo2_sleep_data_update(&data);
            }
        }
        if (g_spo2_cache_data.num == SPO2_CACHE_NUM_MAX) {
            db_health_spo2_point_cache_data_save();
        }
        if (g_spo2_cache_data.num < SPO2_CACHE_NUM_MAX) {
            memcpy(&g_spo2_cache_data.data[g_spo2_cache_data.num], &data, sizeof(spo2_data_t));
            g_spo2_cache_data.num++;
        }
    }

    return true;
}

void db_health_spo2_new_day_data_init(void)
{
    HEALTH_LOG_D("db spo2 0:00 init");
    memset(&g_spo2_chart_data, 0x00, sizeof(db_spo2_chart_t));
    memset(&g_history_spo2, 0x00, sizeof(db_spo2_history_t));
    db_health_spo2_point_cache_data_save();
    memset(&g_spo2_minimax, 0, sizeof(spo2_minimax_t));
    g_spo2_minimax.max.spo2 = 0;
    g_spo2_minimax.min.spo2 = 255;
    service_gui_set_spo2_minimax(&g_spo2_minimax);
    db_health_spo2_read_point_data_from_file();
    db_health_spo2_gui_data_notify();
    db_health_spo2_historyinfo_gui_set();
}


void db_health_spo2_data_init(void)
{
    memset(&g_spo2_last_point, 0x00, sizeof(spo2_data_t));
    memset(&g_spo2_cache_data, 0x00, sizeof(spo2_cache_data_t));
    memset(&g_spo2_minimax, 0x00, sizeof(spo2_minimax_t));
    memset(&g_spo2_chart_data, 0x00, sizeof(db_spo2_chart_t));
    memset(&g_history_spo2, 0x00, sizeof(db_spo2_history_t));
    memset(&g_spo2_sleep_period, 0x00, sizeof(spo2_sleep_period_t));

    g_spo2_minimax.max.spo2 = 0;
    g_spo2_minimax.min.spo2 = 255;
    g_spo2_sleep_period.max = 0;
    g_spo2_sleep_period.min = 255;
    db_health_spo2_gui_data_init();
    db_health_spo2_historyinfo_gui_set();
    db_health_spo2_slp_data_read_from_file();
    db_health_spo2_sleep_gui_data_update();
}

void db_health_spo2_data_deinit(void)
{
    HEALTH_LOG_D("db spo2 deinit");
    db_health_spo2_point_cache_data_save();
}

void db_health_spo2_data_reinit(void)
{
    HEALTH_LOG_D("db spo2 deinit");
    db_health_spo2_point_cache_data_save();
    memset(&g_spo2_last_point, 0x00, sizeof(spo2_data_t));
    memset(&g_spo2_cache_data, 0x00, sizeof(spo2_cache_data_t));
    memset(&g_spo2_minimax, 0x00, sizeof(spo2_minimax_t));
    memset(&g_spo2_chart_data, 0x00, sizeof(db_spo2_chart_t));
    memset(&g_history_spo2, 0x00, sizeof(db_spo2_history_t));
    memset(&g_spo2_sleep_period, 0x00, sizeof(spo2_sleep_period_t));

    g_spo2_minimax.max.spo2 = 0;
    g_spo2_minimax.min.spo2 = 255;
    g_spo2_sleep_period.max = 0;
    g_spo2_sleep_period.min = 255;
    db_health_spo2_gui_data_init();
    db_health_spo2_historyinfo_gui_set();
    db_health_spo2_slp_data_read_from_file();
    db_health_spo2_sleep_gui_data_update();
}


