/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   SelectDialView.cpp
@Brief   :   表盘选择视图实现文件
@Details :   实现表盘选择页面的UI布局和交互逻辑
* 
**************************************************************************/

#include "SelectDialView.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QwGUITheme.h"
#include "Image/images.h"
#include "GUI/QwGUIKey.h"
#include "qw_time_util.h"
#include "../touchgfx_js/touchgfx_js_api.h"
#include "../touchgfx_js/quickjs_user.h"
#include "../touchgfx_js/js_data_servers.h"
#include "../touchgfx_js/jsApp.h"
#include <touchgfx/containers/CacheableContainer.hpp>
#ifndef SIMULATOR
#include "qw_ff.h"
#include "qw_fs.h"
#endif
#include "../DialCommon.h"
#define IMG_OR_SNAPSHOT 1 // 1图片 2截表盘图
SelectDialView::SelectDialView(PageManager* manager) :
	PageView(manager),
    focus_index_(-1),
    dialCount_(0),
    tick_(0),
    indicator_tick_(0),
    inUseGoodsId_(0),
    updateItemCallback_(this, &SelectDialView::updateItemCallbackHandler)
{
    cashBuf[0] = nullptr;
    cashBuf[1] = nullptr;
    cashBuf[2] = nullptr;
}

void SelectDialView::quit()
{
    //The method is an intentionally-blank override.
}

SelectDialView::~SelectDialView()
{
    if(cashBuf[0])
    {
        HAL::freeScreenCachedBuffer(cashBuf[0]);
    }
    if(cashBuf[1])
    {
        HAL::freeScreenCachedBuffer(cashBuf[1]);
    }
    if(cashBuf[2])
    {
        HAL::freeScreenCachedBuffer(cashBuf[2]);
    }

    uint32_t inUseGoodsId = get_current_dial_goodsid();
    if(inUseGoodsId_ != inUseGoodsId)
    {
        DIAL_LOG_D("now goodsid %u prev goodsid:%u\n", inUseGoodsId, inUseGoodsId_);
        freeLauncherDial();
    }
    
}

void SelectDialView::setup()
{
    for(int i = 0; i < 3; i++)
    {
        if(cashBuf[i] == nullptr)
        {
            cashBuf[i] = HAL::getScreenCachedBuffer(LV_COLOR_DEPTH/8);
        }
    }

    // 获取当前使用的表盘索引和表盘总数
    uint32_t usingDialIndex = get_using_dial_index();
    dialCount_ = get_dial_counts();

	add(bg_);
	bg_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
	bg_.setColor(0x000000);

    add(scroll_list_);
    scroll_list_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    scroll_list_.setHorizontal(false);
    // scroll_list_.setCircular(true);// 循环滚动
    scroll_list_.setWindowSize(1);
    scroll_list_.setEasingEquation(EasingEquations::cubicEaseOut);
    scroll_list_.setSwipeAcceleration(10);
    scroll_list_.setDragAcceleration(10);
    scroll_list_.setDrawableSize(320,16);// 设置滚动列表的宽度
    scroll_list_.setPadding(57,0);// 设置内边距
    scroll_list_.setTouchable(true);
    scroll_list_.setKeyScrollEnable(true);
    scroll_list_.allowVerticalDrag(true);// 允许垂直拖拽
    scroll_list_.setNumberOfItems(dialCount_);// 设置列表的项数
    scroll_list_.setSnapping((ScrollList::ScrollSnap )true);// 设置列表滚动
    scroll_list_.setDrawables(scrollListListItems_, updateItemCallback_);// 设置创建滚动列表回调
    scroll_list_.updateKeyScrollFocusItem(usingDialIndex, true, 0);
    
    add(indicator_);
    indicator_.setAlign(ALIGN_IN_LM, 12);
    indicator_.setBitmaps(Bitmap(&normalPage), Bitmap(&highlightedPage));
    indicator_.setNumberOfPages(dialCount_);
    indicator_.setCurrentPage(usingDialIndex);
    indicator_tick_ = 2;

    add(fab_);
    fab_.setup(FABTN_START::OK);// FABTN_BACK::CANCEL,FABTN_POWER::SETTING);

    add(edit_dial_img_);
    edit_dial_img_.setBitmap(Bitmap(&edit_dial));
    edit_dial_img_.setAlign(ALIGN_IN_RT, 0,44);
    edit_dial_img_.setVisible(false);
    inUseGoodsId_ = get_current_dial_goodsid();

}

void SelectDialView::updateItemCallbackHandler(DrawableListItemsInterface* items, int16_t containerIndex, int16_t itemIndex)
{
    // rt_kprintf("itemIndex: %d containerIndex: %d\n", itemIndex, containerIndex);
    if (items == &scrollListListItems_)
    {
        Drawable* d = items->getDrawable(containerIndex);
        Container* container = static_cast<Container*>(d);

        container->add(img_bg_[containerIndex]);
        char snapPath[50] = {0};
        uint32_t dialGoodsId = get_cfg_dial_goodsid_by_index(itemIndex);

        snprintf(snapPath, 50, "%s/%u_dialSnapshot.bin", DIAL_CACHE_PATH, dialGoodsId);
        // rt_kprintf("snapPath:%s containerIndex:%d\n", snapPath, containerIndex);
        // rt_kprintf("1 bootSmec:%u\n", (uint32_t)get_boot_msec());
        if(!is_dial_snapshot_exist(snapPath))
        {
            rt_kprintf("snap not exit create new\n");
            freeLauncherDial();
            create_dial_snapshot(dialGoodsId, cashBuf[containerIndex], false, itemIndex);
        } 
        // rt_kprintf("21 bootSmec:%u\n", (uint32_t)get_boot_msec());
    #ifdef SIMULATOR
        getDialBuf(cashBuf[containerIndex], snapPath, containerIndex);
    #else
        getDialBuf(cashBuf[containerIndex], snapPath, containerIndex);
    #endif
        // rt_kprintf("2 bootSmec:%u\n", (uint32_t)get_boot_msec());
        img_bg_[containerIndex].setBitmap(Bitmap(&cache_img_dsc[containerIndex]));
        img_bg_[containerIndex].setZoom(176);
        img_bg_[containerIndex].setAlign(ALIGN_IN_CENTER, 0, 0);
        lv_img_cache_invalidate_src(img_bg_[containerIndex].getBitmap());
        container->add(circle_[containerIndex]);
        circle_[containerIndex].setLineWidth(2);
        circle_[containerIndex].setWidthHeight(320, 320);
        circle_[containerIndex].setCircle(160, 160, 160);
        circle_[containerIndex].setArc(-120, 240);
        circle_[containerIndex].setAlign(ALIGN_IN_CENTER, 0, 0);
    }
}  

void SelectDialView::handleTickEvent()
{
    if (indicator_tick_ > 0)
    {
        if (UI_TICK_TO_MS_AUTO(tick_, 1000))
        {
            indicator_tick_--;
            if (indicator_tick_ == 0)
            {
                indicator_.setVisible(false);
            }
        }
    }
    scroll_list_.handleTickEvent();

    setDialBtnAlpha();
    if(focus_index_ == scroll_list_.GetFocusListIndex()) return;
    focus_index_ = scroll_list_.GetFocusListIndex();
    setFocusCircleColor();
    showDialStyle();
}

void SelectDialView::handleKeyEvent(uint8_t c)
{
    indicator_.setVisible(true);
    indicator_tick_ = 2;
    scroll_list_.handleKeyEvent(c);
    scroll_list_.HandleKnobEvent(c);
    if (c == KEY_CLK_BACK)
    {
        if (get_enter_dial_edit_type() == EDIT_MODE_LONG_PRESS) {
            manager_->push("Launcher");
        } else if (get_enter_dial_edit_type() == EDIT_MODE_CLICK) {
            manager_->push("Personalization");
        }
    }
    else if (c == KEY_CLK_START) 
    {
        // 设置当前使用表盘索引
        set_inuse_dial_index(indicator_.getCurrentPage());
        manager_->push("Launcher");
    }else if (c == KEY_CLK_POWER)
    {
        if(!edit_dial_img_.isVisible()) return;
        
            // 设置当前使用表盘索引
            uint32_t currentPage = indicator_.getCurrentPage();
            uint32_t editType = get_dial_edit_type_by_index(currentPage);
            // 设置当前使用表盘索引
            set_inuse_dial_index(currentPage);
            if(editType == EDIT_TYPE_COLOR || editType == EDIT_TYPE_ALL)
            {
                manager_->push("EditDialTheme");
            }else if(editType == EDIT_TYPE_DATA)
            {
                // 初始化数据选择页面数据索引
                init_dial_edit_data_index();
                init_dial_prv_edit_data_index();
                init_edit_data_positon();
                manager_->push("EditDialDataComponent");
            }
    }
}

void SelectDialView::handleClickEvent(const ClickEvent& evt)
{
    scroll_list_.handleClickEvent(evt);

    if (fab_.get_fab_absoluteRect(FABTN_TYPE::START).intersect(evt.getX(), evt.getY()))
	{
        // 设置当前使用表盘索引
        set_inuse_dial_index(indicator_.getCurrentPage());
        manager_->push("Launcher");
	}
    if(edit_dial_img_.isVisible())
    {
        if (edit_dial_img_.getAbsoluteRect().intersect(evt.getX(), evt.getY()))
        {
            // 设置当前使用表盘索引
            uint32_t currentPage = indicator_.getCurrentPage();
            uint32_t editType = get_dial_edit_type_by_index(currentPage);
            // 设置当前使用表盘索引
            set_inuse_dial_index(currentPage);
            if(editType == EDIT_TYPE_COLOR || editType == EDIT_TYPE_ALL)
            {
                manager_->push("EditDialTheme");
            }else if(editType == EDIT_TYPE_DATA)
            {
                // 初始化数据选择页面数据索引
                init_dial_edit_data_index();
                init_dial_prv_edit_data_index();
                init_edit_data_positon();
                manager_->push("EditDialDataComponent");
            }
        }
    }
}

void SelectDialView::handleDragEvent(const DragEvent& evt)
{
    indicator_.setVisible(true);
    indicator_tick_ = 2;
    scroll_list_.handleDragEvent(evt); 
}

void SelectDialView::handleGestureEvent(const GestureEvent& evt)
{
    indicator_.setVisible(true);
    indicator_tick_ = 2;
    scroll_list_.handleGestureEvent(evt);
}

void SelectDialView::showDialStyle(void)
{
    if( dial_cfg_is_exist(indicator_.getCurrentPage()))
    {
        edit_dial_img_.setVisible(true);
    }else{
        edit_dial_img_.setVisible(false);
    }
}

// 设置当前聚焦的页面弧形颜色
void SelectDialView::setFocusCircleColor(void)
{

    indicator_.setCurrentPage(focus_index_);
    for(int i = 0; i < NUM_OF_DIAL; i++){
        if(i == indicator_.getCurrentPage()%NUM_OF_DIAL){
            circle_[i].setColor(0xffffff);
        }else{
            circle_[i].setColor(0x808080);
        }
    }
    bg_.invalidate();
}

// 设置fab键在最上和最下的位移透明度
void SelectDialView::setDialBtnAlpha(void)
{
    int16_t itemSize = scroll_list_.GetItemSize();
    int32_t offset = scroll_list_.getOffset();
    if(offset > 0 || abs(offset) > (dialCount_-1)*itemSize){
        uint8_t alpha = 0;
        if(offset > 0){
            alpha = 255-255/264.0*abs(offset);
        }else if(abs(offset) > (dialCount_-1)*itemSize){
            alpha = 255-255/264.0*(abs(offset)-(dialCount_-1)*itemSize);
        }
        edit_dial_img_.setAlpha(alpha);
        fab_.setFaBtnImgArea(FABTN_TYPE::START, alpha);
        bg_.invalidate();
    }else{
        edit_dial_img_.setAlpha(LV_OPA_COVER);
        fab_.setFaBtnImgArea(FABTN_TYPE::START, LV_OPA_COVER);
    }
}
#ifdef SIMULATOR
// 获取图片缓存到cashBuf
bool SelectDialView::getDialBuf(void* cashBuf, const char* imgPath, uint8_t index)
{
    lv_fs_file_t f;
    lv_img_header_t header;
    lv_fs_res_t res = lv_fs_open(&f, imgPath, LV_FS_MODE_RD);
    if (res == LV_FS_RES_OK) {
        uint32_t rn, size, data_size, br;

        res = lv_fs_read(&f, &header, sizeof(lv_img_header_t), &rn);
        if (res != LV_FS_RES_OK) {
            lv_fs_close(&f);
            rt_kprintf("read file failed\n");
            return false;
        }
        lv_fs_seek(&f, 0, LV_FS_SEEK_END);
        lv_fs_tell(&f, &size);
        data_size = size - sizeof(lv_img_header_t);

        lv_fs_seek(&f, sizeof(lv_img_header_t), LV_FS_SEEK_SET);
        lv_fs_read(&f, static_cast<uint8_t *>(cashBuf), data_size, &br);
        lv_fs_close(&f);

        lv_memcpy(&cache_img_dsc[index].header, &header, sizeof(lv_img_header_t));
        cache_img_dsc[index].data_size = data_size;
        cache_img_dsc[index].data = static_cast<uint8_t *>(cashBuf);
        return true;
    }
    return false;
}
#else
void SelectDialView::getDialBuf(void* cashBuf, const char* imgPath,uint8_t index)
{
    FIL fp;
    lv_img_header_t header;
    uint8_t ret = f_open(&fp, imgPath, QW_FA_READ);
    if (FR_OK == ret)
    {
        uint32_t data_size, br;
        ret = f_read(&fp, &header, sizeof(lv_img_header_t), &br);
        if(FR_OK == ret)
        {
            FILINFO fileInfo;
            f_stat(imgPath, &fileInfo);
            data_size = fileInfo.fsize - sizeof(lv_img_header_t);
            DIAL_LOG_D("fileInfo.size = %u dataSize:%u", fileInfo.fsize, data_size);

            f_lseek(&fp, sizeof(lv_img_header_t));
            f_read(&fp, static_cast<uint8_t *>(cashBuf), data_size, &br);
            f_close(&fp);

            lv_memcpy(&cache_img_dsc[index].header, &header, sizeof(lv_img_header_t));
            cache_img_dsc[index].data_size = data_size;
            cache_img_dsc[index].data = static_cast<uint8_t *>(cashBuf);
        }
        else 
        {
            DIAL_LOG_D("@@@read dial bin fail:%u", ret);
            f_close(&fp);
        }
        return;
    }
    DIAL_LOG_D("open %s fail:%u\n", imgPath, ret);
    return;
}
#endif
// Notification Callback function
// ObserverDrawable Callback function
// custom function
void SelectDialView::freeLauncherDial()
{
    // 释放Launcher的表盘资源
    js_destory_dial();
    bool get_dial_container(CacheableContainer **container);
    CacheableContainer* jsContainer;
    if(get_dial_container(&jsContainer))
    {
        remove(*jsContainer);
    }
}

