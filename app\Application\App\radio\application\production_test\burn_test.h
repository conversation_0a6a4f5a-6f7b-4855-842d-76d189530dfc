/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   burn_test.h
@Time    :   2025/06/13 10:10:20
*
**************************************************************************/

#ifdef __cplusplus
extern "C" {
#endif

#include "at_cmd_table.h"
#include "production_test.h"

// 老化测试项功能最多
#define ITEM_FACTORY_FUN_MAX 31

#define ITEM_REBOOT  ITEM_NULL        //老化测试重启

// 老化测试项配置
#define DEFAULT_BURN_TIMES_NULL     0
#define DEFAULT_BURN_PARM1_NULL     0
#define DEFAULT_BURN_PARM2_NULL     0

#define DEFAULT_BURN_TIMES_REBOOT   3 // 默认老化项次数: 复位老化测试
#define DEFAULT_BURN_TIMES_MOTOR    20// 默认老化项次数: 马达老化测试
#define DEFAULT_BURN_TIMES_BEEP     15// 默认老化项次数: 蜂鸣器老化测试
#define DEFAULT_BURN_TIMES_LCD      10// 默认老化项次数: 屏幕老化测试
#define DEFAULT_BURN_TIMES_EMMC     10// 默认老化项次数: EMMC老化测试
#define DEFAULT_BURN_TIMES_TP       0 // 默认老化项次数: 触摸老化测试
#define DEFAULT_BURN_TIMES_KEY      0 // 默认老化项次数: 按键老化测试
#define DEFAULT_BURN_TIMES_KNOB     0 // 默认老化项次数: 光旋钮老化测试
#define DEFAULT_BURN_TIMES_BAR      0 // 默认老化项次数: 气压计老化测试
#define DEFAULT_BURN_TIMES_MAG      0 // 默认老化项次数: 地磁老化测试
#define DEFAULT_BURN_TIMES_ATT      3 // 默认老化项次数: 姿态传感器老化测试
#define DEFAULT_BURN_TIMES_PPG      3 // 默认老化项次数: PPG老化测试
#define DEFAULT_BURN_TIMES_GPS      5 // 默认老化项次数: GPS老化测试
#define DEFAULT_BURN_TIMES_BLE      5 // 默认老化项次数: BLE老化测试
#define DEFAULT_BURN_TIMES_LIGHT    0 // 默认老化项次数: 光感老化测试


#define DEFAULT_BURN_PARM1_REBOOT   0 // 默认老化项次数: 复位老化测试，复位模式，默认正常复位
#define DEFAULT_BURN_PARM2_REBOOT   6 // 默认老化项次数: 复位老化测试，复位后延时关闭的时间，单位秒

#define DEFAULT_BURN_PARM1_MOTOR    1 // 默认老化项参数: 马达老化测试，马达振动时间，单位秒
#define DEFAULT_BURN_PARM2_MOTOR    2 // 默认老化项参数: 马达老化测试，马达停止时间，单位秒

#define DEFAULT_BURN_PARM1_BEEP     5 // 默认老化项参数: 蜂鸣器老化测试，蜂鸣器开启时间，单位秒
#define DEFAULT_BURN_PARM2_BEEP     2 // 默认老化项参数: 蜂鸣器老化测试，蜂鸣器停止时间，单位秒

#define DEFAULT_BURN_PARM1_LCD      6 // 默认老化项次数: 屏幕老化测试，屏幕切换颜色种类
#define DEFAULT_BURN_PARM2_LCD      2 // 默认老化项次数: 屏幕老化测试，屏幕切换时间间隔，单位秒

#define DEFAULT_BURN_PARM1_EMMC     0 // 默认老化项次数: EMMC老化测试，老化模式，默认正常擦写
#define DEFAULT_BURN_PARM2_EMMC     9 // 默认老化项次数: EMMC老化测试，单次读写大小：2的N次方字节，2^9=512,单次读写512B

#define DEFAULT_BURN_PARM1_TP       0 // 默认老化项次数: 触摸老化测试
#define DEFAULT_BURN_PARM2_TP       0 // 默认老化项次数: 触摸老化测试

#define DEFAULT_BURN_PARM1_KEY      0 // 默认老化项次数: 按键老化测试
#define DEFAULT_BURN_PARM2_KEY      0 // 默认老化项次数: 按键老化测试

#define DEFAULT_BURN_PARM1_KNOB     0 // 默认老化项次数: 光旋钮老化测试
#define DEFAULT_BURN_PARM2_KNOB     0 // 默认老化项次数: 光旋钮老化测试

#define DEFAULT_BURN_PARM1_BAR      0 // 默认老化项次数: 气压计老化测试
#define DEFAULT_BURN_PARM2_BAR      0 // 默认老化项次数: 气压计老化测试

#define DEFAULT_BURN_PARM1_MAG      0 // 默认老化项次数: 地磁老化测试
#define DEFAULT_BURN_PARM2_MAG      0 // 默认老化项次数: 地磁老化测试

#define DEFAULT_BURN_PARM1_ATT      5 // 默认老化项次数: 姿态传感器老化测试，传感器开启时间，单位秒
#define DEFAULT_BURN_PARM2_ATT      2 // 默认老化项次数: 姿态传感器老化测试，传感器关闭时间，单位秒

#define DEFAULT_BURN_PARM1_PPG      5 // 默认老化项次数: PPG老化测试，PPG开启时间，单位秒
#define DEFAULT_BURN_PARM2_PPG      2 // 默认老化项次数: PPG老化测试，PPG关闭时间，单位秒

#define DEFAULT_BURN_PARM1_GPS      5 // 默认老化项次数: GPS老化测试，GPS开启时间，单位秒
#define DEFAULT_BURN_PARM2_GPS      2 // 默认老化项次数: GPS老化测试，GPS关闭时间，单位秒

#define DEFAULT_BURN_PARM1_BLE      0 // 默认老化项次数: BLE老化测试，BLE开启时间，单位秒
#define DEFAULT_BURN_PARM2_BLE      0 // 默认老化项次数: BLE老化测试，BLE关闭时间，单位秒

#define DEFAULT_BURN_PARM1_LIGHT    0 // 默认老化项次数: 光感老化测试，光感开启时间，单位秒
#define DEFAULT_BURN_PARM2_LIGHT    0 // 默认老化项次数: 光感老化测试，光感关闭时间，单位秒


// 默认支持的老化测试项
#define DEFAULT_BURN_FUN          ((0x00000001 << ITEM_MOTOR)|(0x00000001 << ITEM_BEEP)|(0x00000001 << ITEM_LCD)| \
                                   (0x00000001 << ITEM_ACC)|(0x00000001 << ITEM_EMMC)| \
                                   (0x00000001 << ITEM_PPG)|(0x00000001 << ITEM_GPS)| \
                                   (0x00000001 << ITEM_REBOOT))
// 目标测试项老化测试次数
#define DEFAULT_BURN_COUNT_ARRAY   {    DEFAULT_BURN_TIMES_REBOOT, \
                                        DEFAULT_BURN_TIMES_MOTOR,  \
                                        DEFAULT_BURN_TIMES_KEY,    \
                                        DEFAULT_BURN_TIMES_TP,     \
                                        DEFAULT_BURN_TIMES_LCD,    \
                                        DEFAULT_BURN_TIMES_ATT,    \
                                        DEFAULT_BURN_TIMES_GPS,    \
                                        DEFAULT_BURN_TIMES_NULL,   \
                                        DEFAULT_BURN_TIMES_NULL,   \
                                        DEFAULT_BURN_TIMES_NULL,   \
                                        DEFAULT_BURN_TIMES_NULL,   \
                                        DEFAULT_BURN_TIMES_NULL,   \
                                        DEFAULT_BURN_TIMES_NULL,   \
                                        DEFAULT_BURN_TIMES_NULL,   \
                                        DEFAULT_BURN_TIMES_NULL,   \
                                        DEFAULT_BURN_TIMES_MAG,    \
                                        DEFAULT_BURN_TIMES_BAR,    \
                                        DEFAULT_BURN_TIMES_BEEP,   \
                                        DEFAULT_BURN_TIMES_NULL,   \
                                        DEFAULT_BURN_TIMES_EMMC,   \
                                        DEFAULT_BURN_TIMES_BLE,    \
                                        DEFAULT_BURN_TIMES_KNOB,   \
                                        DEFAULT_BURN_TIMES_LIGHT,  \
                                        DEFAULT_BURN_TIMES_PPG,    \
                                        DEFAULT_BURN_TIMES_NULL,   \
                                        DEFAULT_BURN_TIMES_NULL,   \
                                        DEFAULT_BURN_TIMES_NULL,   \
                                        DEFAULT_BURN_TIMES_NULL,   \
                                        DEFAULT_BURN_TIMES_NULL,   \
                                        DEFAULT_BURN_TIMES_NULL,   \
                                        DEFAULT_BURN_TIMES_NULL,   \
                                        DEFAULT_BURN_TIMES_NULL    \
                                   }
// 目标测试项老化测试参数一
#define DEFAULT_BURN_PARM1_ARRAY   {    DEFAULT_BURN_PARM1_REBOOT, \
                                        DEFAULT_BURN_PARM1_MOTOR,  \
                                        DEFAULT_BURN_PARM1_KEY,    \
                                        DEFAULT_BURN_PARM1_TP,     \
                                        DEFAULT_BURN_PARM1_LCD,    \
                                        DEFAULT_BURN_PARM1_ATT,    \
                                        DEFAULT_BURN_PARM1_GPS,    \
                                        DEFAULT_BURN_PARM1_NULL,   \
                                        DEFAULT_BURN_PARM1_NULL,   \
                                        DEFAULT_BURN_PARM1_NULL,   \
                                        DEFAULT_BURN_PARM1_NULL,   \
                                        DEFAULT_BURN_PARM1_NULL,   \
                                        DEFAULT_BURN_PARM1_NULL,   \
                                        DEFAULT_BURN_PARM1_NULL,   \
                                        DEFAULT_BURN_PARM1_NULL,   \
                                        DEFAULT_BURN_PARM1_MAG,    \
                                        DEFAULT_BURN_PARM1_BAR,    \
                                        DEFAULT_BURN_PARM1_BEEP,   \
                                        DEFAULT_BURN_PARM1_NULL,   \
                                        DEFAULT_BURN_PARM1_EMMC,   \
                                        DEFAULT_BURN_PARM1_BLE,    \
                                        DEFAULT_BURN_PARM1_KNOB,   \
                                        DEFAULT_BURN_PARM1_LIGHT,  \
                                        DEFAULT_BURN_PARM1_PPG,    \
                                        DEFAULT_BURN_PARM1_NULL,   \
                                        DEFAULT_BURN_PARM1_NULL,   \
                                        DEFAULT_BURN_PARM1_NULL,   \
                                        DEFAULT_BURN_PARM1_NULL,   \
                                        DEFAULT_BURN_PARM1_NULL,   \
                                        DEFAULT_BURN_PARM1_NULL,   \
                                        DEFAULT_BURN_PARM1_NULL,   \
                                        DEFAULT_BURN_PARM1_NULL    \
                                   }
// 目标测试项老化测试参数二
#define DEFAULT_BURN_PARM2_ARRAY   {    DEFAULT_BURN_PARM2_REBOOT, \
                                        DEFAULT_BURN_PARM2_MOTOR,  \
                                        DEFAULT_BURN_PARM2_KEY,    \
                                        DEFAULT_BURN_PARM2_TP,     \
                                        DEFAULT_BURN_PARM2_LCD,    \
                                        DEFAULT_BURN_PARM2_ATT,    \
                                        DEFAULT_BURN_PARM2_GPS,    \
                                        DEFAULT_BURN_PARM2_NULL,   \
                                        DEFAULT_BURN_PARM2_NULL,   \
                                        DEFAULT_BURN_PARM2_NULL,   \
                                        DEFAULT_BURN_PARM2_NULL,   \
                                        DEFAULT_BURN_PARM2_NULL,   \
                                        DEFAULT_BURN_PARM2_NULL,   \
                                        DEFAULT_BURN_PARM2_NULL,   \
                                        DEFAULT_BURN_PARM2_NULL,   \
                                        DEFAULT_BURN_PARM2_MAG,    \
                                        DEFAULT_BURN_PARM2_BAR,    \
                                        DEFAULT_BURN_PARM2_BEEP,   \
                                        DEFAULT_BURN_PARM2_NULL,   \
                                        DEFAULT_BURN_PARM2_EMMC,   \
                                        DEFAULT_BURN_PARM2_BLE,    \
                                        DEFAULT_BURN_PARM2_KNOB,   \
                                        DEFAULT_BURN_PARM2_LIGHT,  \
                                        DEFAULT_BURN_PARM2_PPG,    \
                                        DEFAULT_BURN_PARM2_NULL,   \
                                        DEFAULT_BURN_PARM2_NULL,   \
                                        DEFAULT_BURN_PARM2_NULL,   \
                                        DEFAULT_BURN_PARM2_NULL,   \
                                        DEFAULT_BURN_PARM2_NULL,   \
                                        DEFAULT_BURN_PARM2_NULL,   \
                                        DEFAULT_BURN_PARM2_NULL,   \
                                        DEFAULT_BURN_PARM2_NULL    \
                                   }


// 老化测试状态枚举
enum {
    BURN_TEST_NOT_BEGIN = 0, // 老化未开始
    BURN_TEST_RUNNING,       // 老化测试中
    BURN_TEST_END,           // 老化测试结束
};

void burn_test_entry(at_command_t *cmd, uint8_t *response, uint16_t *response_len);
// 获取老化测试状态
uint8_t get_burn_state(void);

// 获取老化重启进度
uint8_t get_burn_progress(void);

// 获取老化测试项状态
uint8_t get_burn_item_progress(int item);

// 获取老化测试项最慢进度信息:作为总进度
uint8_t get_burn_item_min_progress();

// 获取老化测试是否完成信息
bool get_burn_over_state(void);

#ifdef __cplusplus
}
#endif
