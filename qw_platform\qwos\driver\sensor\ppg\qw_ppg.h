/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   qw_ppg.h
@Time    :   2024/12/19 11:03:48
*
**************************************************************************/
#ifndef __QW_PPG_H__
#define __QW_PPG_H__

#include "basictype.h"
#include "qw_gsensor.h"
#include "qw_log.h"
#include "qw_sensor.h"
#include "qw_sensor_dbg.h"
#include "qw_time_util.h"
#include "rtconfig.h"
#include "sensor.h"
#include <rtthread.h>
#include <stdbool.h>
#include "gh_demo_config.h"
#include "subscribe_service.h"

#ifdef CONFIG_PPG_CAPTURE
#include "ble_lb55x_macros.h"
#endif

#define USING_DVT2_LEDV1
#undef USING_DVT2_LEDV2
#undef USING_DVT2_LEDV3
#undef USING_DVT1

#ifdef USING_DVT2_LEDV3
#define PPG_APPLICATION_INDEX_DAILY   0
#define PPG_APPLICATION_INDEX_SPORT   1
#endif

#define DRV_PPG_LVL LOG_LVL_INFO
#define DRV_PPG_TAG "drv_ppg"

#if (DRV_PPG_LVL >= LOG_LVL_DBG)
#define DRV_PPG_LOG_D(fmt, ...) QW_LOG_D(DRV_PPG_TAG, fmt, ##__VA_ARGS__)
#else
#define DRV_PPG_LOG_D(fmt, ...)
#endif

#if (DRV_PPG_LVL >= LOG_LVL_INFO)
#define DRV_PPG_LOG_I(fmt, ...) QW_LOG_I(DRV_PPG_TAG, fmt, ##__VA_ARGS__)
#else
#define DRV_PPG_LOG_I(fmt, ...)
#endif

#if (DRV_PPG_LVL >= LOG_LVL_WARNING)
#define DRV_PPG_LOG_W(fmt, ...) QW_LOG_W(DRV_PPG_TAG, fmt, ##__VA_ARGS__)
#else
#define DRV_PPG_LOG_W(fmt, ...)
#endif

#if (DRV_PPG_LVL >= LOG_LVL_ERROR)
#define DRV_PPG_LOG_E(fmt, ...) QW_LOG_E(DRV_PPG_TAG, fmt, ##__VA_ARGS__)
#else
#define DRV_PPG_LOG_E(fmt, ...)
#endif


/*ppg driver thread*/
#define DRV_PPG_THREAD_STACK_SIZE        (5 * 1024)
#define DRV_PPG_THREAD_PRIORITY          PPG_TASK_PRIORITY

#define PPG_MSG_DATA_SIZE                (300)

#define DRV_PPG_WEAR_NADT_USER_CONFID_EN 0   //活体检测判断使用可信度
//CONFIG_FORCE_ONBODY设置为强制佩戴便于挂测验证
//#define CONFIG_FORCE_ONBODY
#ifdef CONFIG_FORCE_ONBODY
#define DRV_PPG_WEAR_NADT_EN 0   //活体检测判断佩戴关闭
#else
#define DRV_PPG_WEAR_NADT_EN 1   //活体检测判断佩戴打开
#endif

#define DRV_PPG_WEAR_MOVE_EN 0   //活动/移动检测使能(会关软硬件的ADT)

//驱动自动根据未佩戴关闭HR，HRV，SPO2应用，但要记录当前应用的订阅，等佩戴上需恢复上报对应订阅数据。
#define DRV_PPG_WEAR_OFF_AUTO_CLOSE_APP 1

//可疑佩戴是否允许ppg测量
#define DRV_PPG_DUBIOUS_WEAR_EN_PPG_FUN 1

#undef DRV_PPG_ADT_MONITOR_EN   //静电、异常上下电、总线异常等偶现ADT功能异常，无法恢复检测逻辑。
#define DRV_PPG_ADT_LONGKEEP   //手表ADT常开


#if (__INTERRUPT_PROCESS_MODE__ == __NORMAL_INT_PROCESS_MODE__)
/*如果使用PPG中断，则禁用ACC中断,不创建守护线程*/
#undef DRV_PPG_USE_ACC_INT
#undef DRV_PPG_POLLING_INT_TIMER
#elif (__INTERRUPT_PROCESS_MODE__ == __MIX_INT_PROCESS_MODE__ \
       || __INTERRUPT_PROCESS_MODE__ == __POLLING_INT_PROCESS_MODE__)
/*需要polling时，可以用acc中断来做定时timer*/
#define DRV_PPG_USE_ACC_INT
#define DRV_PPG_POLLING_INT_TIMER 400
#endif   //__INTERRUPT_PROCESS_MODE__

#ifdef __GH3X2X_PROTOCOL_SEND_TIMER_PERIOD__
#define PPG_BLE_CAPTURE_USE_SYS_TIMER   //PPG采集数据上报使用系统定时器,开启此宏注意验证是否会丢采集数据.
#endif

#if (__DRIVER_LIB_MODE__ == __DRV_LIB_WITH_ALGO__)
//打开此宏，ppg驱动输出数据时会自动运行算法.
#define PPG_DRV_CONTAIN_ALGO    1
#endif

#define DRV_PPG_INT_SOURCE_ACC 1
#define DRV_PPG_INT_SOURCE_PPG 2

/*PPG_IRQ_EVENT_TRACE PPG中断事件调试*/
#undef PPG_IRQ_EVENT_TRACE

#ifdef __cplusplus
extern "C" {
#endif

#define ACC_DATA_FEED_SIZE   (5)                        // 单次最小喂数据组
#define ACC_DATA_BUFFER_SIZE (4 * ACC_DATA_FEED_SIZE)   // 缓存最大组
#define ACC_EVENT_WITH_TIMESTAMP
#undef DUMP_FEED_ACC

// PPG原始数据缓存大小定义
#define PPG_RAWDATA_CACHE_SIZE (100)  // PPG原始数据缓存大小

typedef enum {
    DRV_PPG_MSG_TYPE_SUBS = SUB_CMD_TYPE_SUBSCRIBE,
    DRV_PPG_MSG_TYPE_UNSUBS = SUB_CMD_TYPE_UNSUBSCRIBE,
    /*gh lib event*/
    GH_DRV_MSG_TYPE_HR_RAW_REPORT,   // HR ppg RAW上报
    GH_DRV_MSG_TYPE_HRV_RAW_REPORT,
    GH_DRV_MSG_TYPE_SPO2_RAW_REPORT,
    GH_DRV_MSG_TYPE_WEAR_STATE_CHANGE,      //汇顶算法库识别到佩戴状态发生变化
    GH_DRV_MSG_TYPE_DRV_INTERRUPT,
    /*user event*/
    USER_MSG_TYPE_CHARGE_STATE_CHANGE,   //充电状态发生变化.
    USER_MSG_TYPE_SPORT_STATE_CHANGED,  //用户运动状态发生变化
    USER_MSG_TYPE_SWITCH_APPLICATION,   //切换应用
    USER_MSG_TYPE_ALGO_HAI_STATE_CHANGE, //活动强度算法状态变化
    /*other event*/
    OPT_PPG_MSG_TYPE_TIMEOUT_CB,            // timer事件转任务处理消息
    OPT_PPG_MSG_TYPE_NADT_ONCE,             // 开启一次活体监测
} drv_ppg_msg_type_e;

typedef struct
{
    uint16_t type;                           // 消息类型
    uint16_t len;                            // 消息数据长度
    unsigned char data[PPG_MSG_DATA_SIZE];   // 消息数据指针
} drv_ppg_msg_t;

typedef struct
{
    rt_mq_t drv_ppg_mq;               //消息队列
    rt_thread_t drv_ppg_thread;       //线程
#ifdef DRV_PPG_POLLING_INT_TIMER
    rt_timer_t drv_ppg_guard_timer;   //守护定时
#endif
    rt_timer_t drv_ppg_wear_timer;    //佩戴逻辑处理定时

    uint32_t request_mask;            //请求的ppg功能function标记
    uint32_t online_mask;             //在线的ppg功能function标记

    uint8_t ppg_hr_subs_num;          //日常hr订阅量
    uint8_t ppg_hr_run_subs_num;       //运动hr订阅量
    uint8_t ppg_hr_rawdata_subs_num;  //hr原始数据订阅量
    uint8_t ppg_hrv_subs_num;         //hrv订阅量
    uint8_t ppg_spo2_subs_num;        //spo2订阅量
    uint8_t ppg_wear_subs_num;        //wear订阅量

    uint8_t ppg_wear_status;          //当前 qw_ppg_algo_wear_state_t 枚举状态
    uint8_t ppg_wear_status_old;      //旧的 qw_ppg_algo_wear_state_t 枚举状态

    uint8_t drv_ppg_mode;             //ppg当前状态模式，测试模式，app应用模式
    uint8_t charge_state;             //充电状态

    uint8_t live_retry_noresult;      // 活体判断重复无结果次数
    uint8_t live_retry_result;        // 活体判断重复结果次数

    bool nadt_start_flag;             //nadt起始标志
    bool thread_init_ok;
} drv_ppg_local_variable_t;

/**
 * @brief 获取活体佩戴检测起始状态
 *
 * @return bool 0 ：非起始状态 1 ：是起始状态
 */
bool get_nadt_start_flag(void);

/**
 * @brief 设置活体佩戴检测起始状态
 *
 * @param onoff 0 ：非起始状态 1 ：是起始状态
 */
void set_nadt_start_flag(bool onoff);

/**
 * @brief 给drv_ppg_task发送消息API
 *
 * @param type 消息类型
 * @param len  数据长度
 * @param data 数据
 * @return int32_t RT_ERROR ：失败 RT_EOK ：成功
 */
int32_t send_msg_to_drv_ppg_task(uint16_t type, uint16_t len, void *data);

/************************************************************************
 *@function:const char *get_wear_status_str(uint8_t status)
 *@brief:将佩戴状态转换为字符串
 *@param:佩戴状态,see qw_ppg_algo_wear_data_t
 *@return:返回佩戴状态字符串
*************************************************************************/
const char *get_wear_status_str(uint8_t status);

/************************************************************************
 *@function:int32_t algo_ppg_get_accRawData(uint64_t ppg_ts,STGsensorRawdata **p_feedAccRawBuff, uint8_t len);
 *@brief:goodix算法库获取加速度数据接口
 *@return:acc个数
*************************************************************************/
//int32_t algo_ppg_get_accRawData(uint64_t ppg_ts, STGsensorRawdata *out_accel, uint8_t len);

/************************************************************************
 *@function:void ppg_bottom_irq_request(void)
 *@brief:ppg中断处理用户函数
 *@return:None
*************************************************************************/
void ppg_irq_request(void);

/************************************************
*@function:void update_gh3x2x_irq_ts(uint64_t event_ts)
*@brief:设置ppg产生中断的时间戳，因为Gh3x2xDemoInterruptProcess不能在中断
上下文执行，所以需要通过这个函数将时间戳传递给Gh3x2xDemoInterruptProcess函数。
*@param:event_ts: 记录中断产生的时间戳.
************************************************/
void update_gh3x2x_irq_ts(uint64_t event_ts);

uint64_t get_gh3x2x_irq_ts(void); //获取中断时间戳

/************************************************
*@function:bool need_report_hr_rawdata(void)
*@brief:判断是否需要上报HR原始数据,因为心率算法需要PPG原始数据，
应用层也有单独使用ppg原始数据的,这里需要判断如果仅有心率算法需要则不上报始数据.
*@return:true: 需要上报HR原始数据，false:不需要上报HR原始数据.
************************************************/
bool need_report_hr_rawdata(void);

/************************************************
*@function:int get_ppg_application_config_index(void);
*@brief:PPG方案3支持日常/运动两种模式
*@return:0日常模式，1运动模式, ref hr_algo_run_mode_t.
************************************************/
int get_ppg_application_config_index(void);

/************************************************************************
 *@function: int32_t qw_ppg_force_enable_rawdata(bool enable)
 *@brief: 强制启用PPG原始数据采集，绕过佩戴检测限制
 *@param: enable - true:启用强制模式，false:关闭强制模式
 *@return: 0表示成功，其他值表示失败
 *************************************************************************/
int32_t qw_ppg_force_enable_rawdata(bool enable);

/************************************************************************
 *@function: int32_t qw_ppg_get_rawdata(struct sensor_ppg *ppg_data, uint32_t max_count)
 *@brief: 获取PPG原始数据
 *@param: ppg_data - 输出PPG数据的缓冲区
 *@param: max_count - 最大获取的数据个数
 *@return: 实际获取到的数据个数，负数表示错误
 *************************************************************************/
int32_t qw_ppg_get_rawdata(struct sensor_ppg *ppg_data, uint32_t max_count);

/************************************************************************
 *@function: uint32_t qw_ppg_get_rawdata_count(void)
 *@brief: 获取当前缓存中PPG原始数据的个数
 *@return: 缓存中数据的个数
 *************************************************************************/
uint32_t qw_ppg_get_rawdata_count(void);

#ifdef CONFIG_PPG_CAPTURE
/************************************************************************
 *@function:bool ghealth_protocal_is_ready(void)
 *@brief:ghealth app 是否已经连接
 *@return:false:未连接，true:已连接且可用
*************************************************************************/
bool ghealth_protocal_is_ready(void);

#endif



#ifdef __cplusplus
}
#endif

#endif /* __QW_PPG_H__ */
