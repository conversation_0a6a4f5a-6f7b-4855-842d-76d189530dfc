/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ble_service_data_sync.h
@Time    :   2025/04/10 11:00:35
*
**************************************************************************/

#ifndef __BLE_SERVICE_DATA_SYNC_H__
#define __BLE_SERVICE_DATA_SYNC_H__

#include "basictype.h"
#include "rtconfig.h"

#ifdef __cplusplus
extern "C" {
{
#endif

#define BLE_CONNECT_STATE_MASK_IDLE (0)
#define BLE_CONNECT_STATE_MASK_CONNECTING (1 << 0)
#define BLE_CONNECT_STATE_MASK_CONNECTED (1 << 1)
#define BLE_CONNECT_STATE_MASK_PB_PROTOCOL_READY (1 << 2)
#define BLE_CONNECT_STATE_MASK_GOODIX_READY (1 << 3)
#define BLE_CONNECT_STATE_MASK_DISCONNECTING (1 << 4)

uint8_t ble_connect_state_get(void);

#ifdef BF0_LCPU
/************************************************************************
 *@function:void ble_weather_update_sync_time(void)
 *@brief:同步天气数据更新时间
 *@param: null
 *@return:null
*************************************************************************/
void ble_weather_update_sync_time(void);

/************************************************************************
 *@function:void ble_weather_update_sync_request_time(void)
 *@brief:同步天气数据更新请求时间
 *@param: null
 *@return:null
*************************************************************************/
void ble_weather_update_sync_request_time(void);

/************************************************************************
 *@function:void ble_health_update_sync_minute(uint8_t minute_val)
 *@brief:同步健康数据同步分钟数
 *@param: null
 *@return:null
*************************************************************************/
void ble_health_update_sync_minute(uint8_t minute_val);

/************************************************************************
 *@function:void process_pb_connect_event(uint8_t state)
 *@brief:ble连接状态检查
 *@param: uint8_t state: 连接状态
 *@return:null
*************************************************************************/
void process_pb_connect_event(uint8_t state);

#elif defined(BF0_HCPU)

/************************************************************************
 *@function:int32_t health_data_sync_time_send(uint8_t minute_val);
 *@brief:向小核发送健康数据同步时间
 *@param: minute_val: 健康数据同步分钟数
 *@return:0: 成功，其他：失败
*************************************************************************/
int32_t health_data_sync_time_send(uint8_t minute_val);

/************************************************************************
 *@function:int32_t weather_data_sync_succ_send(void);
 *@brief:向小核发送天气数据同步成功
 *@param: null
 *@return:0: 成功，其他：失败
*************************************************************************/
int32_t weather_data_sync_succ_send(void);

/************************************************************************
 *@function:int32_t weather_data_sync_request_succ_send(void);
 *@brief:向小核发送天气数据已向app请求更新
 *@param: null
 *@return:0: 成功，其他：失败
*************************************************************************/
int32_t weather_data_sync_request_succ_send(void);

/************************************************************************
 *@function:int32_t ble_connect_state_notify(uint8_t state);
 *@brief:向小核发送ble连接状态更新
 *@param: null
 *@return:0: 成功，其他：失败
*************************************************************************/
int32_t ble_connect_state_notify(uint8_t state);

#endif

#ifdef __cplusplus
}
#endif

#endif // __BLE_SERVICE_DATA_SYNC_H__