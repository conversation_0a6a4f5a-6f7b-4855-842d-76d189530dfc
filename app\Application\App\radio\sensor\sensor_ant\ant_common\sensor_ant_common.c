/** 
 * @*************************************** Copyright (c) ***************************************
 * @                              <PERSON>han <PERSON>wu Technology Co., Ltd
 * @*********************************************************************************************
 * @Author: Jiang<PERSON>hen
 * @Date: 2021-10-25 20:26:05
 * @LastEditTime: 2022-01-25 13:46:22
 * @LastEditors: JiangZhen
 * @FilePath: \iGS630_App\Application\App\radio\sensor\sensor_ant\ant_common\sensor_ant_common.c
 * @Description: 
 * @*********************************************************************************************
 */
#include "nrf_log.h"
#include "nrf_sdh.h"
#include "nrf_sdh_ant.h"
#include "app_error.h"
#include "ant_parameters.h"
#include "ant_interface.h"
#include "ant_key_manager.h"
#include "ant_key_manager_config.h"
#include "basictype.h"

#include "sensor_ant_common.h"
#include "qw_sensor_config.h"
#include "ant_search_common.h"
#include "ant_hrm_rx.h"
#include "ant_spd_rx.h"
#include "ant_cad_rx.h"
#include "ant_bsc_rx.h"
#if ANT_SENSOR_BPWR_ENABLED
#include "ant_bpwr_rx.h"
#endif
#if ANT_SENSOR_FEC_ENABLED
#include "ant_fe_rx.h"
#endif
#if ANT_SENSOR_DI2_ENABLED
#include "ant_di2_rx.h"
#endif
#if ANT_SENSOR_SHFT_ENABLED
#include "ant_shft_rx.h"
#endif
#if ANT_SENSOR_RADAR_ENABLED
#include "ant_radar_rx.h"
#endif
#if ANT_SENSOR_LIGHT_ENABLED
#include "ant_light_rx.h"
#endif
#if ANT_SENSOR_LEV_ENABLED
#include "ant_lev_rx.h"
#endif
#if ANT_SENSOR_RD_ENABLED
#include "ant_rd_rx.h"
#endif

#include "ant_search_config.h"
#include "qw_sensor_data.h"
#include "ble_ant_module.h"
#include "qw_log.h"

#define SENSOR_ANT_LOG_TAG "SENSOR_ANT_COMMON"

#define SENSOR_ANT_LOG_LVL               LOG_LVL_DBG       //定义log等级

#if (SENSOR_ANT_LOG_LVL >= LOG_LVL_DBG)
    #define SENSOR_ANT_LOG_D(...)        QW_LOG_D(SENSOR_ANT_LOG_TAG, __VA_ARGS__)   //定义debug等级的log输出
#else
    #define SENSOR_ANT_LOG_D(...)
#endif

#if (SENSOR_ANT_LOG_LVL >= LOG_LVL_INFO)
    #define SENSOR_ANT_LOG_I(...)        QW_LOG_I(SENSOR_ANT_LOG_TAG, __VA_ARGS__)   //定义info等级的log输出
#else
    #define SENSOR_ANT_LOG_I(...)
#endif

#if (SENSOR_ANT_LOG_LVL >= LOG_LVL_WARNING)
    #define SENSOR_ANT_LOG_W(...)        QW_LOG_W(SENSOR_ANT_LOG_TAG, __VA_ARGS__)  //定义warning等级的log输出
#else
    #define SENSOR_ANT_LOG_W(...)
#endif

#if (SENSOR_ANT_LOG_LVL >= LOG_LVL_ERROR)
    #define SENSOR_ANT_LOG_E(...)        QW_LOG_E(SENSOR_ANT_LOG_TAG, __VA_ARGS__)  //定义error等级的log输出
#else
    #define SENSOR_ANT_LOG_E(...)
#endif

#if (BLE_APP_LOG_LVL >= LOG_LVL_DBG)
    #define BLE_APP_LOG_HEX(TAG, width, buf, size)      QW_LOG_HEX(TAG, width, buf, size)
#else
    #define BLE_APP_LOG_HEX(TAG, width, buf, size)
#endif

static sensor_ant_config_t sensor_ant_config[] = 
{
    {SENSOR_TYPE_HRM,    ant_hrm_rx_open,   ant_hrm_rx_profile_setup},
    {SENSOR_TYPE_CBSC,   ant_bsc_rx_open,   ant_bsc_rx_profile_setup},
    {SENSOR_TYPE_CAD,    ant_cad_rx_open,   ant_cad_rx_profile_setup},
    {SENSOR_TYPE_SPD,    ant_spd_rx_open,   ant_spd_rx_profile_setup},
#if ANT_SENSOR_BPWR_ENABLED
    { SENSOR_TYPE_BPWR,   ant_bpwr_rx_open,  ant_bpwr_rx_profile_setup},
#endif
#if ANT_SENSOR_SHFT_ENABLED
    {SENSOR_TYPE_SHFT,   ant_shft_rx_open,  ant_shft_rx_profile_setup},
#endif
#if ANT_SENSOR_DI2_ENABLED
    {SENSOR_TYPE_DI2,     ant_di2_rx_open,   ant_di2_rx_profile_setup},
#endif
#if ANT_SENSOR_FEC_ENABLED
    {SENSOR_TYPE_FEC,     ant_fe_rx_open,   ant_fe_rx_profile_setup},
#endif
#if ANT_SENSOR_LEV_ENABLED
    {SENSOR_TYPE_LEV,     ant_lev_rx_open,   ant_lev_rx_profile_setup},
#endif
#if ANT_SENSOR_RD_ENABLED
    {SENSOR_TYPE_RD,      ant_rd_rx_open,    ant_rd_rx_profile_setup},
#endif
#if ANT_SENSOR_RADAR_ENABLED
    {SENSOR_TYPE_RADAR,   ant_radar_rx_open, ant_radar_rx_profile_setup},
#endif
#if ANT_SENSOR_LIGHT_ENABLED
    {SENSOR_TYPE_LIGHT,   ant_light_rx_open, ant_light_rx_profile_setup},
#endif
    {SENSOR_TYPE_SEARCH1, ant_search_ch1_rx_open, ant_search_ch1_rx_profile_setup},
#if ANT_SENSOR_SEARCH2_ENABLED
    {SENSOR_TYPE_SEARCH2, ant_search_ch2_rx_open, ant_search_ch2_rx_profile_setup}
#endif
};

static uint8_t m_ant_di2_network_key[]   = DI2_PLUS_NETWORK_KEY;
static sensor_ant_chn_t ant_channel_cfg[ANT_CHANNEL_NUM_MAX];
/** 
 * @*********************************************************************************************
 * @description: 设置通道搜索参数
 * @param {uint8_t} channel_number
 * @return {*}
 * @*********************************************************************************************
 */
static void sensor_ant_profile_search_set(uint8_t channel_number)
{
    ret_code_t err_code = NRF_SUCCESS;                                                      
                                                                                            
    err_code = sd_ant_channel_rx_search_timeout_set(channel_number, 0);                 //HPS搜索关闭，   HPS搜索影响其它通道的通信，可能会产生丢包现象
    APP_ERROR_CHECK(err_code);                                                              
                                                                                                                                                                                  
    err_code = sd_ant_channel_low_priority_rx_search_timeout_set(channel_number, 4/*1*/);    //4 * 2.5   s = 10s
    APP_ERROR_CHECK(err_code);                                                                                                                                                     

    err_code = sd_ant_active_search_sharing_cycles_set(channel_number, 1);                             //搜索通道共享周期
    APP_ERROR_CHECK(err_code);

    err_code = sd_ant_search_waveform_set(channel_number, ANT_WAVEFORM_FAST);                                      //搜索波形
    APP_ERROR_CHECK(err_code);
}

/** 
 * @*********************************************************************************************
 * @description: 配置ANT通道
 * @param {uint8_t} channel_number
 * @param {uint8_t} device_type
 * @param {ant_id_t} *id
 * @return {*}
 * @*********************************************************************************************
 */
static void sensor_ant_profile_setup(sensor_type_t sensor_type, ant_id_t *id)
{
    uint8_t max_num = sizeof(sensor_ant_config) / sizeof(sensor_ant_config_t);

    for (uint8_t i = 0; i < max_num; i++) 
    {
        if (sensor_ant_config[i].sensor_type == sensor_type
            && NULL != sensor_ant_config[i].sensor_ant_profile_setup_handle)
        {
            sensor_ant_config[i].sensor_ant_profile_setup_handle(id);
            break; 
        }
    }
}

/** 
 * @*********************************************************************************************
 * @description: 打开指定通道
 * @param {uint8_t} channel_number
 * @param {uint8_t} device_type
 * @return {*}
 * @*********************************************************************************************
 */
static void sensor_ant_profile_open(sensor_type_t sensor_type)
{
    uint8_t i = 0;
    uint8_t max_num = sizeof(sensor_ant_config) / sizeof(sensor_ant_config_t);

    for (i = 0; i < max_num; i++) 
    {
        if (sensor_ant_config[i].sensor_type == sensor_type
            && NULL != sensor_ant_config[i].sensor_ant_open_handle)
        {
            sensor_ant_config[i].sensor_ant_open_handle();
            break;
        }
    }
}

/** 
 * @*********************************************************************************************
 * @description: 根据通道设备类型获取传感器类型
 * @param {uin8_t} device_type
 * @return {*}
 * @*********************************************************************************************
 */
sensor_type_t sensor_ant_sensor_type_get(uint8_t device_type)
{
    sensor_type_t sensor_type = SENSOR_TYPE_INVALID;
    
    switch (device_type)
    {
        case HRM_DEVICE_TYPE:
            sensor_type = SENSOR_TYPE_HRM;
            break;
        case CBSC_DEVICE_TYPE:
            sensor_type = SENSOR_TYPE_CBSC;
            break;
        case BPWR_DEVICE_TYPE:
            sensor_type = SENSOR_TYPE_BPWR;
            break;
        case CAD_DEVICE_TYPE:
            sensor_type = SENSOR_TYPE_CAD;
            break;
        case SPD_DEVICE_TYPE:
            sensor_type = SENSOR_TYPE_SPD;
            break;
        case SHFT_DEVICE_TYPE:
            sensor_type = SENSOR_TYPE_SHFT;
            break;
        case DI2_DEVICE_TYPE:
            sensor_type = SENSOR_TYPE_DI2;
            break; 
        case FEC_DEVICE_TYPE:
            sensor_type = SENSOR_TYPE_FEC;
            break;
        case LEV_DEVICE_TYPE:
            sensor_type = SENSOR_TYPE_LEV;
            break;
        case RD_DEVICE_TYPE:
            sensor_type = SENSOR_TYPE_RD;
            break;
        case RADAR_DEVICE_TYPE:
            sensor_type = SENSOR_TYPE_RADAR;
            break;
        case LIGHT_DEVICE_TYPE:
            sensor_type = SENSOR_TYPE_LIGHT;
            break;
        default:
            break;
    }

    return sensor_type;
}

/** 
 * @*********************************************************************************************
 * @description: 根据传感器类型，获取传感器通道设备配置类型
 * @param {sensor_type_t} sensor_type
 * @return {*}
 * @*********************************************************************************************
 */
uint8_t sensor_ant_device_type_get(sensor_type_t sensor_type)
{
    uint8_t device_type = INVALID_DEVICE_TYPE;
    
    switch (sensor_type)
    {
        case SENSOR_TYPE_HRM:
            device_type = HRM_DEVICE_TYPE;
            break;
        case SENSOR_TYPE_CBSC:
            device_type = CBSC_DEVICE_TYPE;
            break;
        case SENSOR_TYPE_BPWR:
            device_type = BPWR_DEVICE_TYPE;
            break;
        case SENSOR_TYPE_CAD:
            device_type = CAD_DEVICE_TYPE;
            break;
        case SENSOR_TYPE_SPD:
            device_type = SPD_DEVICE_TYPE;
            break;
        case SENSOR_TYPE_SHFT:
            device_type = SHFT_DEVICE_TYPE;
            break;
        case SENSOR_TYPE_DI2:
            device_type = DI2_DEVICE_TYPE;
            break; 
        case SENSOR_TYPE_FEC:
            device_type = FEC_DEVICE_TYPE;
            break;
        case SENSOR_TYPE_LEV:
            device_type = LEV_DEVICE_TYPE;
            break;
        case SENSOR_TYPE_RD:
            device_type = RD_DEVICE_TYPE;
            break;
        case SENSOR_TYPE_RADAR:
            device_type = RADAR_DEVICE_TYPE;
            break;
        case SENSOR_TYPE_LIGHT:
            device_type = LIGHT_DEVICE_TYPE;
            break;
        case SENSOR_TYPE_SEARCH1:
        case SENSOR_TYPE_SEARCH2:
            device_type = SEARCH_DEVICE_TYPE;
            break;
        default:
            break;
    }

    return device_type;
}

/** 
 * @*********************************************************************************************
 * @description: 根据传感器类型获取对应通道编号，调用者注意通道号0xff为无效
 * @param {sensor_type_t} device_type
 * @return {*}
 * @*********************************************************************************************
 */
uint8_t sensor_ant_channel_id_get(sensor_type_t sensor_type)
{
    uint8_t channel_num = INVALID_CHANNEL_NUMBER;
    // switch (sensor_type)
    // {
    //     case SENSOR_TYPE_HRM: 
    //         channel_num = HRM_CHANNEL_NUMBER;
    //         break;
    //     case SENSOR_TYPE_CBSC:
    //         channel_num = CBSC_CHANNEL_NUMBER;
    //         break;       
    //     case SENSOR_TYPE_BPWR:
    //         channel_num = BPWR_CHANNEL_NUMBER;
    //         break;       
    //     case SENSOR_TYPE_CAD:
    //         channel_num = CAD_CHANNEL_NUMBER;
    //         break;
    //     case SENSOR_TYPE_SPD:
    //         channel_num = SPD_CHANNEL_NUMBER;
    //         break;       
    //     case SENSOR_TYPE_SHFT:
    //         channel_num = SHFT_CHANNEL_NUMBER;
    //         break; 
    //     case SENSOR_TYPE_DI2:
    //         channel_num = DI2_CHANNEL_NUMBER;
    //         break; 
    //     case SENSOR_TYPE_FEC:
    //         channel_num = FEC_CHANNEL_NUMBER;
    //         break; 
    //     case SENSOR_TYPE_LEV:
    //         channel_num = LEV_CHANNEL_NUMBER;
    //         break; 
    //     case SENSOR_TYPE_RD:
    //         channel_num = RD_CHANNEL_NUMBER;
    //         break; 
    //     case SENSOR_TYPE_RADAR:
    //         channel_num = RADAR_CHANNEL_NUMBER;
    //         break; 
    //     case SENSOR_TYPE_LIGHT:
    //         channel_num = LIGHT_CHANNEL_NUMBER;
    //         break; 
    //     case SENSOR_TYPE_SEARCH1:
    //         channel_num = SEARCH1_CHANNEL_NUMBER;
    //         break; 
    //     case SENSOR_TYPE_SEARCH2:
    //         channel_num = SEARCH2_CHANNEL_NUMBER;
    //         break;
    //     default:
    //         break;
    // } 

    channel_num = sensor_ant_channel_num_get(sensor_type);

    return channel_num;
}

/** 
 * @*********************************************************************************************
 * @description: 关闭指定类型传感器
 * @param {sensor_type_t} sensor_type
 * @return {*}
 * @*********************************************************************************************
 */
extern bool system_is_enter_sleep(void);
void sensor_ant_close(sensor_type_t sensor_type)
{
    uint8_t    channel_number = sensor_ant_channel_id_get(sensor_type);
    uint8_t    channelstate   = 0;
    ret_code_t err_code       = NRF_SUCCESS;
    sensor_connect_infor_t    sensor_connect;
    bool found = sensor_connect_infor_get(sensor_type, &sensor_connect);
    // rt_kprintf("%s %s:%s\n", __func__, ant_ble_sensor_type_decription_str_get(sensor_type), ant_ble_sensor_type_states_str_get(sensor_connect.state));

    if (channel_number < 1 || channel_number >= NRF_SDH_ANT_TOTAL_CHANNELS_ALLOCATED)
    {
        return;
    }

    if (true != nrf_sdh_is_enabled())
    {
        return;
    }

    if (found && (sensor_connect.state == SENSOR_CONNECT_STATE_IDLE
        || sensor_connect.state == SENSOR_CONNECT_STATE_CLOSE_DONE))
    {
        return;
    }

    err_code = sd_ant_channel_status_get(channel_number, &channelstate);
    // rt_kprintf("@@@@@@@@@Warning:sd_ant_channel_status_get [%d,0x%x]\n", channel_number, err_code);
    if (err_code != NRF_SUCCESS)
    {
        //////back_trace_printf("Warning:sd_ant_channel_status_get [%x,0x%x]\n", channel_number, err_code);
        SENSOR_ANT_LOG_W("Warning:sd_ant_channel_status_get channel_number = %d, sensor_type = %d, err_code = %d\n", channel_number, sensor_type, err_code);
        // 重启52832?
        if((!system_is_enter_sleep()) || (!nrf_is_ready_stop(NRF_USED_TYPE_SENSOR)))
        {
    	    APP_ERROR_CHECK(err_code);
        }
    }
    

    if (STATUS_SEARCHING_CHANNEL == channelstate
        || STATUS_TRACKING_CHANNEL == channelstate)
    {
        if (found && (sensor_connect.state == SENSOR_CONNECT_STATE_IDLE))
        {
            return;//防止重复关闭
        }
        if (found)
        {
            if(sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED || sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)
            {
                sensor_connect.state = SENSOR_CONNECT_STATE_CLOSE_WAIT;
            }
            sensor_connect_infor_set(sensor_type, &sensor_connect);
            err_code = sd_ant_channel_close(channel_number);
            // 失败重启52832?
        }
        else if (sensor_type == SENSOR_TYPE_SEARCH1 || sensor_type == SENSOR_TYPE_SEARCH2)
        {
            err_code = sd_ant_channel_close(channel_number);
            // 失败重启52832?
        }
    }
}

/** 
 * @*********************************************************************************************
 * @description: 根据指定传感器类型、ID，连接指定传感器
 * @param {sensor_type_t} sensor_type
 * @param {uint32_t} *id
 * @return {*}
 * @*********************************************************************************************
 */
void sensor_ant_open(sensor_type_t sensor_type,  ant_id_t *id)
{   
    ret_code_t err_code     = NRF_SUCCESS;
    uint8_t    channelstate = 0;
    uint8_t channel_number  = sensor_ant_channel_num_assign(sensor_type);//sensor_ant_channel_id_get(sensor_type);
    sensor_connect_infor_t    sensor_connect = {0};
    bool found = sensor_connect_infor_get(sensor_type, &sensor_connect);

    if (channel_number < 1 || channel_number >= NRF_SDH_ANT_TOTAL_CHANNELS_ALLOCATED)
    {
        return;
    }
    
    if (found && (sensor_connect.state != SENSOR_CONNECT_STATE_IDLE))
    {
        return;
    }
    else if (found)
    {
        err_code = sd_ant_channel_status_get(channel_number, &channelstate);
        if (err_code != NRF_SUCCESS)
        {
            // 重启52832?
        }
        sensor_connect.state = SENSOR_CONNECT_STATE_CONNECTING;
        sensor_connect_infor_set(sensor_type, &sensor_connect);
        if (STATUS_SEARCHING_CHANNEL == channelstate || STATUS_TRACKING_CHANNEL == channelstate)
        {
            rt_kprintf("sensor_ant_open channelstate=%d\n",channelstate);
            return;
        }
    }
    

    sensor_ant_profile_setup(sensor_type, id);
    
    sensor_ant_profile_search_set(channel_number);           
    
    sensor_ant_profile_open(sensor_type);
}

/** 
 * @*********************************************************************************************
 * @description: 启动ANT传感器搜索
 * @param {*}
 * @return {*}
 * @*********************************************************************************************
 */
void sensor_ant_search_start(void)
{
   sensor_ant_open(SENSOR_TYPE_SEARCH1, 0x00);
#if ANT_SENSOR_SEARCH2_ENABLED
   sensor_ant_open(SENSOR_TYPE_SEARCH2, 0x00); 
#endif
}

/** 
 * @*********************************************************************************************
 * @description: ANT初始化
 * @param {*}
 * @return {*}
 * @*********************************************************************************************
 */
void sensor_ant_init(void)
{
    ret_code_t err_code = NRF_SUCCESS;

    if (true != nrf_sdh_is_enabled())
    {
        err_code = nrf_sdh_enable_request();
        APP_ERROR_CHECK(err_code);
    }

    ASSERT(nrf_sdh_is_enabled());

    err_code = nrf_sdh_ant_enable();
    APP_ERROR_CHECK(err_code);

    err_code = ant_plus_key_set(ANTPLUS_NETWORK_NUM);
    APP_ERROR_CHECK(err_code);

#if ANT_SENSOR_DI2_ENABLED
    err_code = ant_custom_key_set(DI2_ANTPLUS_NETWORK_NUM, m_ant_di2_network_key);
    APP_ERROR_CHECK(err_code);
#endif

     /* Set library config to report RSSI and Device ID */
    err_code = sd_ant_lib_config_set(ANT_LIB_CONFIG_MESG_OUT_INC_RSSI
                                            | ANT_LIB_CONFIG_MESG_OUT_INC_DEVICE_ID);
    APP_ERROR_CHECK(err_code);
}

/** 
 * @*********************************************************************************************
 * @description: 分配传感器通道号
 * @param {sensor_type_t} sensor_type 传感器类型
 * @return {uint8_t} 通道号
 * @*********************************************************************************************
 */
uint8_t sensor_ant_channel_num_assign(sensor_type_t sensor_type)
{
    if (sensor_type == SENSOR_TYPE_SEARCH1)
    {
        return SEARCH1_CHANNEL_NUMBER;
    }
    else if (sensor_type == SENSOR_TYPE_SEARCH2)
    {
        return SEARCH2_CHANNEL_NUMBER;
    }

    for (uint8_t i = 0; i < ANT_CHANNEL_NUM_MAX; i++)
    {
        if (ant_channel_cfg[i].sensor_type == sensor_type)
        {
            return ant_channel_cfg[i].channel_num;
        }
    }
    uint8_t j;
    for (j = 0; j < ANT_CHANNEL_NUM_MAX; j++)
    {
        if (ant_channel_cfg[j].sensor_type == SENSOR_TYPE_INVALID)
        {
            ant_channel_cfg[j].sensor_type = sensor_type;
            ant_channel_cfg[j].channel_num = j + 1;
            return ant_channel_cfg[j].channel_num;
        }
    }

    return -1;
}

/** 
 * @*********************************************************************************************
 * @description: 释放传感器通道号
 * @param {sensor_type_t} sensor_type 传感器类型
 * @return {uint8_t} 0:成功 -1:失败
 * @*********************************************************************************************
 */
uint8_t sensor_ant_channel_num_unassign(sensor_type_t sensor_type)
{
    if (sensor_type == SENSOR_TYPE_SEARCH1 || sensor_type == SENSOR_TYPE_SEARCH2)
    {
        return 0;
    }

    for (uint8_t i = 0; i < ANT_CHANNEL_NUM_MAX; i++)
    {
        if (ant_channel_cfg[i].sensor_type == sensor_type)
        {
            ant_channel_cfg[i].sensor_type = SENSOR_TYPE_INVALID;
            ant_channel_cfg[i].channel_num = INVALID_CHANNEL_NUMBER;
            return 0;
        }
    }

    return -1;
}

/** 
 * @*********************************************************************************************
 * @description: 获取传感器通道号
 * @param {sensor_type_t} sensor_type 传感器类型
 * @return {uint8_t} 通道号
 * @*********************************************************************************************
 */
uint8_t sensor_ant_channel_num_get(sensor_type_t sensor_type)
{
    if (sensor_type == SENSOR_TYPE_SEARCH1)
    {
        return SEARCH1_CHANNEL_NUMBER;
    }
    else if (sensor_type == SENSOR_TYPE_SEARCH2)
    {
        return SEARCH2_CHANNEL_NUMBER;
    }

    for (uint8_t i = 0; i < ANT_CHANNEL_NUM_MAX; i++)
    {
        if (ant_channel_cfg[i].sensor_type == sensor_type)
        {
            return ant_channel_cfg[i].channel_num;
        }
    }

    return -1;
}

/** 
 * @*********************************************************************************************
 * @description: 检测心率传感器是否支持跑步动态
 * @param null
 * @return null
 * @*********************************************************************************************
 */
void sensor_ant_hr_rd_check(void)
{
    ant_hr_rd_handle();
}
