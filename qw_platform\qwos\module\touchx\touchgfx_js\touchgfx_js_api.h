/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   tpuchgfx_js_api.h
* 
**************************************************************************/
#ifndef _TOUCHGFX_JS_API_H_
#define _TOUCHGFX_JS_API_H_

#include <stdint.h>
#include <stdbool.h>
#include "quickjs_user.h"
#include "touchgfx_js.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_DIALS           10      // 最大表盘数量
#define MAX_THEME_COLOR     7       // 最大主题颜色数量
#define MAX_EDIT_DATA_NUM   6       // 最大修改数据数量

#ifdef SIMULATOR
#define DIAL_CACHE_PATH "./Dial/Snapshot"
#else
#define DIAL_CACHE_PATH "0:/iGPSPORT/Dial/Snapshot"
#endif

typedef enum {
    EDIT_TYPE_COLOR = 1,// 修改主题颜色
    EDIT_TYPE_DATA,     // 修改数据
    EDIT_TYPE_ALL       // 修改主题色和数据
}EDIT_TYPE_E;

typedef enum{
    EDIT_MODE_LONG_PRESS = 0,    // 长按进入编辑模式
    EDIT_MODE_CLICK,             // 点击进入编辑模式
} EDIT_MODE_E;

// app操作类型
typedef enum{
    APP_DO_NOTHING = 0,      // 无操作
    APP_ADD_DIAL,            // 新增表盘
    APP_DEL_DIAL,            // 删除表盘
    APP_EDIT_DIAL,           // 编辑表盘(颜色或者数据)
    APP_USE_DIAL,            // 使用表盘
} APP_OPERATE_E;
// 定义操作类型枚举
typedef enum {
    OPERATION_MODIFY,       // 修改
    OPERATION_ADD,          // 添加
    OPERATION_DELETE        // 删除
} OPERATION_TYPE_E;

// 用于返回launcher前的一些判断，例如在表盘主题色页面设置颜色不保存，
// 黑屏后返回launcher，需要恢复表盘
typedef enum{
    BACK_LAUNCHER_STATUS_NONE = 0,      // 未使用
    BACK_LAUNCHER_STATUS_KEY,           // 按键使用
    BACK_LAUNCHER_STATUS_CLICK,         // 点击使用
} BACK_LAUNCHER_STATUS_E;
typedef struct edit_data_positon {
    int16_t x;      //数据x坐标
    int16_t y;      //数据y坐标
    int16_t width;  //数据宽度
    int16_t height; //数据高度
}edit_data_positon_t;
/************************************************************************
 * @function: set_is_from_launcher_flag
 * @brief: 设置是否从launcher进入标志
 * @param: flag
 * @return: 无
 * *************************************************************************/
void set_is_from_launcher_flag(bool flag);
/************************************************************************
 * @function: get_is_from_launcher_flag
 * @brief: 获取是否从launcher进入标志
 * @param: 无
 * @return: 当前标志
*************************************************************************/
bool get_is_from_launcher_flag();
/************************************************************************
 * @function: get_app_operate_type
 * @brief: 获取app操作类型
 * @param: 无
 * @return: APP_OPERATE_E - 操作类型
*************************************************************************/
APP_OPERATE_E get_app_operate_type(void);
/************************************************************************
 * @function: set_app_operate_type
 * @brief: 设置app操作类型
 * @param: type - 操作类型
 * @return: 无
*************************************************************************/
void set_app_operate_type(APP_OPERATE_E type);
/************************************************************************
 * @function: get_app_operate_goodsId
 * @brief: 获取app操作商品id
 * @param: 无
 * @return: uint32_t - 商品id
*************************************************************************/
uint32_t get_app_operate_goodsId();
/************************************************************************
 * @function: get_dial_them_color
 * @brief: 获取表盘主题颜色
 * @param: index - 表盘索引值
 * @return: uint32_t - 颜色值
*************************************************************************/
uint32_t get_dial_them_color(uint32_t index);
/************************************************************************
 *@function: set_current_dial_goodsid
 *@brief: 设置当前表盘的goodsId
 *@param: index - 表盘索引值
 *@return: void
*************************************************************************/
static void set_current_dial_goodsid(uint8_t index);
/************************************************************************
 *@function: get_current_dial_goodsid
 *@brief: 获取当前表盘的goodsId
 *@param: void
 *@return: 当前表盘的goodsId
*************************************************************************/
uint8_t get_current_dial_goodsid(void);
/************************************************************************
 *@function: get_using_dial_index
 *@brief: 获取当前正在使用的表盘索引
 *@param: void
 *@return: 当前使用的表盘索引值
*************************************************************************/
uint32_t get_using_dial_index(void);
/************************************************************************
 *@function: set_inuse_dial_index
 *@brief: 设置当前使用的表盘索引
 *@param: usingDialIndexValue - 要设置的表盘索引值
 *@return: void
*************************************************************************/
void set_inuse_dial_index(uint32_t usingDialIndexValue);
/************************************************************************
 *@function: get_dial_counts
 *@brief: 获取表盘总数量
 *@param: void
 *@return: 表盘总数量
*************************************************************************/
uint32_t get_dial_counts();
/************************************************************************
 *@function: get_cfg_current_using_dial_goodsid
 *@brief: 获取配置文件中当前使用的表盘goodsId
 *@param: void
 *@return: 当前使用表盘的goodsId
*************************************************************************/
uint32_t get_cfg_current_using_dial_goodsid(void);
/************************************************************************
 *@function: get_cfg_dial_goodsid
 *@brief: 根据索引获取表盘的goodsId
 *@param: index - 表盘索引
 *@return: 对应索引表盘的goodsId
*************************************************************************/
uint32_t get_cfg_dial_goodsid(int index);
/************************************************************************
 *@function: get_dial_path
 *@brief: 获取表盘的js文件路径
 *@param: goodsId - 表盘goodsId
 *@param: jsPath - 存储js路径的buffer
 *@return: void
*************************************************************************/
void get_dial_path(uint32_t goodsId, const char* dialType, char* jsPath);
/************************************************************************
 *@function: get_cfg_dial_type
 *@brief: 获取表盘类型，是相册表盘还是其它
 *@param: index - 表盘索引，第几个表盘
 *@return: 表盘的类型
*************************************************************************/
const char* get_cfg_dial_type(int index);
/************************************************************************
 *@function: dial_cfg_is_exist
 *@brief: 判断表盘配置文件是否存在
 *@param: usingDialIndex - 表盘索引
 *@return: 1-存在,0-不存在
*************************************************************************/
uint8_t dial_cfg_is_exist(uint32_t usingDialIndex);
/************************************************************************
 *@function: get_dial_edit_type
 *@brief: 获取表盘的编辑类型
 *@param: goodsId - 表盘goodsId
 *@return: 表盘编辑类型
*************************************************************************/
uint32_t get_dial_edit_type(uint32_t goodsId);
/************************************************************************
 *  @function: get_dial_cfg_theme_color_num
 * @brief: 获取表盘主题颜色数量
 * @param: goodsId - 表盘goodsId
 * @return: 表盘主题颜色数量
 * *************************************************************************/
uint32_t get_dial_cfg_theme_color_num(uint32_t goodsId);
/************************************************************************
 * @function: set_dial_cfg_color_inuse_index
 * @brief: 设置当前表盘正在使用第几个主题颜色
 * @param: goodsId - 表盘goodsId
 * @param: index - 第几个主题颜色
 * @return: void
 * *************************************************************************/
void set_dial_cfg_color_inuse_index(uint32_t goodsId, uint32_t index);
/************************************************************************
 *  @function: get_dial_cfg_color_inuse_index
 * @brief: 获取当前表盘正在使用几个主题颜色索引
 * @param: goodsId - 表盘goodsId
 * @return: 表盘主题颜色索引
 * *************************************************************************/
uint32_t get_dial_cfg_color_inuse_index(uint32_t goodsId);
/************************************************************************
 * @function: show_js_dial_theme_color_preview_view
 * @brief: 显示表盘主题颜色预览
 * @param: jsPath - 表盘js路径
 * @param: colorIndex - 颜色索引
 * @return: void
 * *************************************************************************/
void show_js_dial_theme_color_preview_view(const char* jsPath, uint32_t colorIndex);
/************************************************************************
 * @function: get_dial_edit_type_num
 * @brief: 获取表盘可编辑类型数量
 * @param: void
 * @return: 表盘可编辑类型数量
 * *************************************************************************/
uint32_t get_dial_edit_type_num(uint32_t *goodsid);
/************************************************************************
 * @function: get_edit_data_type
 * @brief: 获取可编辑数据类型
 * @param: dataType - 存储数据类型的buffer
 * @return: void
 * *************************************************************************/
void get_edit_data_type(uint32_t *dataType, uint32_t *goodsid);
/************************************************************************
 * @function: get_dial_data_num
 * @brief: 获取表盘可编辑数据数量
 * @param: void
 * @return: 表盘数据数量
 * *************************************************************************/
uint32_t get_dial_data_num(uint32_t *goodsid);
/************************************************************************
 * @function: get_dial_data_type
 * @brief: 获取表盘数据类型
 * @param: index - 数据索引
 * @return: 数据类型
 * *************************************************************************/
uint32_t get_dial_data_type(uint32_t index);
/************************************************************************
 * @function: set_dial_data_type
 * @brief: 设置表盘数据类型
 * @param: index - 数据索引
 * @param: dataType - 数据类型
 * @return: void
 * *************************************************************************/
void set_dial_data_type(uint32_t index, uint32_t dataType, uint32_t *goodsid);
/************************************************************************
 * @function: init_dial_edit_data_index
 * @brief: 初始化表盘数据索引
 * @param: void
 * @return: void
 * *************************************************************************/
void init_dial_edit_data_index(void);
/************************************************************************
 * @function: get_dial_edit_data_index
 * @brief: 获取表盘数据索引
 * @param: void
 * @return: 数据索引
 * *************************************************************************/
uint32_t get_dial_edit_data_index();
/************************************************************************
 * @function: set_dial_edit_data_index_increase
 * @brief: 设置表盘数据索引增加
 * @param: void
 * @return: void
 * *************************************************************************/
void set_dial_edit_data_index_increase(void);
/************************************************************************
 * @function: set_dial_edit_data_index_decrease
 * @brief: 设置表盘数据索引减少
 * @param: void
 * @return: void
 * *************************************************************************/
void set_dial_edit_data_index_decrease(void);
/************************************************************************
 * @function: init_dial_prv_edit_data_index
 * @brief: 初始化记录表盘上一个编剧数据的索引，例如现在是编辑第四个，上一个就是第三个
 * @param: void
 * @return: void
 * *************************************************************************/
void init_dial_prv_edit_data_index();
/************************************************************************
 * @function: get_dial_prv_edit_data_index
 * @brief: 获取记录表盘上一个编剧数据的索引，
 * @param: void
 * @return: 例如现在是编辑第四个，上一个就是第三个，放回第三个
 * *************************************************************************/
int8_t get_dial_prv_edit_data_index(void);
/************************************************************************
 * @function: set_dial_prv_edit_data_index
 * @brief: 获取记录表盘上一个编剧数据的索引，
 * @param: 例如现在是编辑第四个，上一个就是第三个，放回第三个
 * @return: void
 * *************************************************************************/
void set_dial_prv_edit_data_index(int8_t index);
/************************************************************************
 * @function: restore_all_js_edit_data_preview_view
 * @brief: 恢复表盘编辑界面，不带上面遮挡，截图时使用
 * @param: jsPath - js文件路径
 * @param: editDataIndex - 编辑数据索引
 * @return: void
 * *************************************************************************/
void restore_all_js_edit_data_preview_view(const char* jsPath, uint32_t editDataIndex);
/************************************************************************
 * @function: refresh_js_dial_pview
 * @brief: 立刻刷新表盘显示，防止跳秒
 * @param: jsPath - js文件路径
 * @return: void
 * *************************************************************************/
void refresh_js_dial_pview(const char* jsPath);
/************************************************************************
 * @function: set_js_edit_data_preview
 * @brief: 设置更新表盘编辑数据控件，编辑表盘数据时使用
 * @param: jsPath - js文件路径
 * @param: editDataIndex - 编辑数据索引
 * @return: void
 * *************************************************************************/
void set_js_edit_data_preview(const char* jsPath, uint32_t editDataIndex);
/************************************************************************
 * @function: set_js_aod_mode
 * @brief: 设置表盘脚本aod模式，显示aod表盘
 * @param: jsPath - js文件路径
 * @param: isAodMode - 是否显示aod标志标志
 * @return: void
 * *************************************************************************/
void set_js_aod_mode(const char* jsPath, bool isAodMode);
/************************************************************************
 * @function: get_js_support_aod_flag
 * @brief: 设置获取当前表盘是否支持aod表盘
 * @param: goodsId - 当前表盘goodsId
 * @return: 1:支持 0：不支持
 * *************************************************************************/
uint32_t get_js_support_aod_flag(uint32_t goodsId);
/************************************************************************
 *@function: get_dial_value_from_cfg
 *@brief: 获取表盘配置文件中的值
 *@param: file_path - 配置文件路径
 *@param: parse_key - 需要解析的关键字
 *@param: parse_value - 存储解析值的buffer
 *@param: str 获取配置文件中的字符串
 *@return: true-成功,false-失败
*************************************************************************/
static bool get_dial_value_from_cfg(const char* file_path, const char* parse_key, uint32_t* parse_value, const char **str);
/************************************************************************
 *@function: set_dial_value_in_cfg
 *@brief: 根据操作类型设置表盘配置文件中的值
 *@param: file_path - 配置文件路径
 *@param: parse_key - 需要设置的关键字
 *@param: new_value - 需要设置的新值
 *@param: operation - 操作类型
 *@return: true-成功,false-失败
*************************************************************************/
static bool set_dial_value_in_cfg(const char* file_path, const char* parse_key, uint32_t new_value, OPERATION_TYPE_E operation);

/************************************************************************
 *@function: add_dial
 *@brief: 添加表盘
 *@param: goodsId - 表盘goodsId buff  内存
 *@return: void
*************************************************************************/
void add_dial(uint32_t goodsId, void* buff);
/************************************************************************
 *@function: delete_dial
 *@brief: 删除指定表盘
 *@param: goodsId - 表盘goodsId
 *@return: void
*************************************************************************/
void delete_dial(uint32_t goodsId);

// 

/************************************************************************
 *@function: get_enter_dial_edit_type
 *@brief: 获取从何进入表盘编辑模式
 *@param: void
 *@return: EDIT_MODE_E
*************************************************************************/
EDIT_MODE_E get_enter_dial_edit_type(void);

// 设置进入表盘编辑页面ID
/************************************************************************
 *@function: set_dial_edit_type
 *@brief: 设置从何进入表盘编辑模式
 *@param: enter_type
 *@return: void
*************************************************************************/
void set_enter_dial_edit_type(EDIT_MODE_E enter_type);
/************************************************************************
 * @function: backup_cfg_file
 * @brief: 配置文件备份
 * @param: void
 * @return: bool
 * *************************************************************************/
bool backup_cfg_file();
/************************************************************************
 * @function: restore_backup_config_file
 * @brief: 配置文件恢复
 * @param: void
 * @return: bool
 * *************************************************************************/
bool restore_backup_config_file();
/************************************************************************
 * @function: delete_backup_config_file
 * @brief: 删除备份文件
 * @param: void
 * @return: bool
 * *************************************************************************/
bool delete_backup_config_file();
/************************************************************************
 * @function: create_dial_snapshot
 * @brief: 创建表盘快照
 * @param: goodsId buff isRecover(是否覆盖原来的缩略图)
 * @return: bool
 * *************************************************************************/
bool create_dial_snapshot(uint32_t goodsId, void *buf, bool isRecover);
/************************************************************************
 * @function: is_dial_snapshot_exist
 * @brief: 判断表盘快照是否存在
 * @param: path
 * @return: bool
 * *************************************************************************/
bool is_dial_snapshot_exist(const char *path);
/************************************************************************
 * @function: delete_dial_snapshot
 * @brief: 删除表盘快照
 * @param: goodsId
 * @return: bool
 * *************************************************************************/
bool delete_dial_snapshot(uint32_t goodsId);
/************************************************************************
 * @function: dial_path_clear
 * @brief: 清理文件夹内所有文件
 * @param: goodsId
 * @return: void
 * *************************************************************************/
static void dial_path_clear(uint32_t goodsId);
/************************************************************************
 * @function: send_msg_to_gui_thread
 * @brief: 发送消息到GUI线程
 * @param: void
 * @return: void
 * *************************************************************************/
void send_msg_to_gui_thread(uint32_t goodsId);

void get_edit_color_type(uint32_t *colorType, uint32_t *goodsid);
void get_using_data_type(uint32_t *dataType, uint32_t *goodsid);
#ifdef __cplusplus
}
#endif

#endif

