/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   MsgBoxService.h
@Time    :   2024/12/13 16:08:23
*
**************************************************************************/

#ifndef __MSG_BOX_SERVICE_H
#define __MSG_BOX_SERVICE_H

#include "MsgBoxDataDef.h"

#if defined(__cplusplus)
extern "C" {
#endif

// #define MSGBOX_TEST

/**
     * @brief 弹窗消息
     */
typedef enum {
    enumPOPUP_CLOSE,           // 关闭弹窗
    enumPOPUP_TIMEOUT_CLOSE,   // 超时关闭弹窗

#ifdef MSGBOX_TEST
    enumPOPUP_TEST1,
    enumPOPUP_TEST2,
    enumPOPUP_TEST3,
    enumPOPUP_TEST4,
#endif                                           // MSGBOX_TEST

    enumPOPUP_ALARM_CLOCK,                       // 闹钟时间到
    enumPOPUP_TIMER_CLOCK,                       // 计时器时间到
    enumPOPUP_HEALTH_GOAL,                       // 健康指标达标提醒
    enumPOPUP_HEALTH_SEDENTARY,                  // 久坐提醒
    enumPOPUP_SPORT_REMIND,                      // 运动状态提醒 半屏
    enumPOPUP_GIVE_UP_SPORT,                     // 放弃运动弹窗
    enumPOPUP_POWER_SAVE,                        // 省电模式弹窗
    enumPOPUP_POWER_SAVE_UNLOCK,                 // 省电模式解锁弹窗
    enumPOPUP_SPORTING_LAP,                      // 运动中记圈弹窗
    enumPOPUP_CHARGE_REMINDER,                   // 充电电量提醒 充满电、充电状态改变
    enumPOPUP_UNDER_VOLTAGE_TEN,                 // 亏电提醒 10电量
    enumPOPUP_UNDER_VOLTAGE_TWENTY,              // 亏电提醒 20电量
    enumPOPUP_UNDER_VOLTAGE_OFF,                 // 亏电关机倒计时提醒
    enumPOPUP_WATCH_UNLOCK,                      // 手表解锁
    enumPOPUP_SAVE_RECORD,                       // 保存活动记录并准备预览概要
    enumPOPUP_TOAST_TIP,                         // toast提示
    enumPOPUP_ALIPAY,                            // 支付宝系列弹窗(带2个或2个以上按钮)
    enumPOPUP_BPWR_CALIB,                        // 功率校准弹窗
    enumPOPUP_BPWR_CALIB_RESULT,                 // 功率校准结果弹窗
    enumPOPUP_SPORT_OUT_COUNT_DOWN,              // 运动准备页面退出倒计时弹窗
    enumPOPUP_SPORT_NO_GPS,                      // 运动准备页面未定位成功开启运动提醒弹窗
    enumPOPUP_REMOVE_SENSOR,                     // 移除传感器弹窗
    enumPOPUP_RECONNECT_SENSOR,                  // 传感器重连弹窗
    enumPOPUP_SENSOR_LOW_POWER,                  // 传感器电量低弹窗
    enumPOPUP_SENSOR_CONN_REACHED_LIMIT,         // 传感器连接达到上限弹窗
    enumPOPUP_ALTIMETER_ICON,                    // 高度计GPS校准结果弹窗
    enumPOPUP_ALTIMETER_ACL,                     // 高度计GPS校准数值弹窗
    enumPOPUP_HEART_RATE,                        // 心率佩戴监测提醒
    enumPOPUP_HEART_RATE_NOTICE,                 // 心率高低提醒
    enumPOPUP_AOD_TIP,                           // AOD提醒
    enumPOPUP_DECODE_ERR,                        // 文件解析失败提醒
    enumPOPUP_DND_TIP,                           // dnd提醒
    enumPOPUP_SLEEP_BED,                         // 就寝时间到达提醒
    enumPOPUP_RESET_TIP,                         // 还原设置、恢复出厂 提醒
    enumPOPUP_DELETE_FIT,                        // 删除活动文件提醒
    enumPOPUP_LOADING,                           // 加载中
    enumPOPUP_SPO2,                              // 血氧佩戴监测提醒
    enumPOPUP_PRESSURE,                          // 压力佩戴监测提醒
    enumPOPUP_PRESSURE_REMIND,                   // 压力高提醒
    enumPOPUP_POWER_CONSUME_REMIND,              // 电量消耗提醒
    enumPOPUP_ALARM_REMINDER,                    // 闹钟时间提醒
    enumPOPUP_VRB_BACKWARD,                      // 虚拟兔子落后提醒
    enumPOPUP_DELETE_TRAINING_COURSE,            // 删除训练课程提醒
    enumPOPUP_DELETE_NAVIGATION,                 // 删除导航路线提醒
    enumPOPUP_TRIATHLON_RESET,                   // 铁人三项重置运动项提醒
    enumPOPUP_TRIATHLON_CHANGE,                  // 铁人三项切换运动项提醒
    enumPOPUP_TRIATHLON_NEXT,                    // 铁人三项下一个运动项提醒
    enumPOPUP_TRIATHLON_COMPLETED,               // 铁人三项完成提醒
    enumPOPUP_MODIFY_WHEEL_PERIMETER,            // 更新轮径提醒
    enumPOPUP_FEC_NOT_CON,                       // FEC未连接
    enumPOPUP_INNER_TRAIN_TIP,                   // 间歇训练提示
    enumPOPUP_TRAIN_COMPLETED,                   // 训练完成提示
    enumPOPUP_SENSOR_CONNECT_NOT_IN_MOTION,      // 非运动中传感器连接成功
    enumPOPUP_SENSOR_DISCONNECT_NOT_IN_MOTION,   // 非运动中传感器连接失败
    enumPOPUP_SENSOR_CONNECT_IN_MOTION,          // 运动中传感器连接成功
    enumPOPUP_SENSOR_DISCONNECT_IN_MOTION,       // 运动中传感器连接失败
    enumPOPUP_FTP_TRAIN_COURSE,                  // FTP训练课程提醒
    enumPOPUP_SENSOR_CONNECT_FEC_POWER_TIP,      // 传感器连接提醒，骑行台和功率计
    enumPOPUP_FTP_TRAIN_COMPLETED,               // FTP训练完成，FTP更新提醒
    enumPOPUP_FTP_TRAIN_FAILED,                  // FTP训练失败
    enumPOPUP_BIND_WATCH_SUCC,                   // 绑定成功
    enumPOPUP_BIND_WATCH_FAILED,                 // 绑定失败
    enumPOPUP_UNBIND,                            // 解除绑定
    enumPOPUP_FACTORY_RESET_ID_CHANGE,           // ID变更恢复出厂
    enumPOPUP_SPORTING_GPS_LOCATE,               // 运动中GPS定位成功提醒
    enumPOPUP_SPORTING_GPS_LOSS,                 // 运动中GPS信号丢失提醒
    enumPOPUP_NAVI_START,                        // 导航开始
    enumPOPUP_NAVI_YAW,                          // 导航偏航
    enumPOPUP_NAVI_RESTORE_ROUTE,                // 导航路线恢复
    enumPOPUP_NAVI_END,                          // 导航结束
    enumPOPUP_NAVI_REMAINING_CLIMB,              // 导航剩余爬升
    enumPOPUP_NAVI_CHANGE_DIR,                   // 导航转向
    enumPOPUP_INTELLIGENT_NOTIFY,                // 智能通知
    enumPOPUP_INCOMING_CALL,                     // 来电
    enumPOPUP_ACH_RESET,                         // 成就重置
    enumPOPUP_ACH_UPDATE,                        // 成就更新
    enumPOPUP_MAX_HR_UPDATE,                     // 最大心率更新
    enumPOPUP_LT_HR_UPDATE,                      // 乳酸阈值心率更新
    enumPOPUP_FTP_UPDATE,                        // FTP更新
    enumPOPUP_UNBIND_WATCH_APP,                  // APP解绑
    enumPOPUP_RESET,                             // 还原设置
    enumPOPUP_FTP_TEST_REMINDER,                 // FTP测试提醒
    enumPOPUP_NAVI_BACK,                         // （导航）返航提醒
    enumPOPUP_SPORT_START_COUNT_DOWN,            // 开始运动倒计时
    enumPOPUP_TODAY_TRAIN_TIP,                   // 今日训练提醒
    enumPOPUP_RESTORE_FACTORY,                   // 恢复出厂
    enumPOPUP_OTA_FINISH_SUCCESS,                // OTA提示 成功
    enumPOPUP_OTA_FINISH_FAILED,                 // OTA提示 失败
    enumPOPUP_ALIPAY_STATUS,                     // 支付宝状态(不带按钮的弹窗)
    enumPOPUP_ALIPAY_CONFIRM,                    // 支付宝确认(只带一个按钮的弹窗)
    enumPOPUP_FIND_WATCH,                        // 查找手表
    enumPOPUP_DRINK_WATER,                       // 喝水提醒
    enumPOPUP_DATA_SYNC,                         // 数据同步提醒

     // TODO 预留枚举 未实现
    enumPOPUP_PAY_SUCCESS,                       // 支付成功
    enumPOPUP_PAY_FAILED,                        // 支付失败
    enumPOPUP_AGPS_EXPIRE,                       // AGPS过期 
    enumPOPUP_FAULT,                             // 故障弹窗
    enumPOPUP_DIAL_UPGRADE_FAILED,               // 表盘升级失败
    enumPOPUP_SENSOR_DISCONNECTION,              // 传感器断开连接   

    enumPOPUP_MAX,                               // 弹窗最大值

    enumGUI_POPUP_FORCE_FLAG = 0x8000,           // 强制刷新(setup)标志,需要用的时候与上
} GUI_MSGBOX;

#define LOADING_SUCCESS 0x23232323
#define LOADING_FAILED  0x24242424

/**
     * @brief 弹窗模块初始化, 输入控制结构
     * @param msg_list QwMsgList对象类型
     * @param evt_manager 传入的pagemanager对象
     */
void msg_msgbox_init(void *msg_list, void *evt_manager);

/**
     * @brief 弹窗消息
     * @param type GUI_MSGBOX类型, 与上enumGUI_POPUP_FORCE_FLAG代表强制刷新
     * @param data 用户传入参数
     * @param force 强制刷新,表示如果当前弹窗与传入类型一致时,强制重新调用setup而非update
     */
void msg_gui_popup_msgbox(uint16_t type, void *data);

/**
     * @brief 弹窗可用性, 初始化时为不可用
     * @param enable 可用性
     */
void msg_msgbox_enable(bool enable);

void msg_msgbox_set_p_msg_list_scope(uint16_t scope);

#if defined(__cplusplus)
}
#endif

#endif   //__MSG_BOX_SERVICE_H
