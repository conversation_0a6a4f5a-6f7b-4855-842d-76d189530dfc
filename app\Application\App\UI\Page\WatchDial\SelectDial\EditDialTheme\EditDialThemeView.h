/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   EditDialThemeView.h
* 
**************************************************************************/
#pragma once
#include "EditDialThemeViewModel.h"
#include "QwFaBtn/QwFaBtn.h"

class EditDialThemeView : public PageView
{
private:

	// Drawable
	Box bg_;
	TextArea title_text_;
	QwFaBtn fab_;
	Circle circle_;
	Circle focus_circle_;
	QwPageIndicator indicator_;     // 页码控件
	int indicator_tick_;          // 页码控件刷新时间

	// 截图
	char *jsAppObjectName_;
	char jsPath_[50];
	char dialCfgPath_[50];
	uint32_t index_;
	uint32_t usingColorIndex_;
	uint32_t colorNum_;
	uint32_t dialGoodsId_;
	CacheableContainer * jsContainer_;
	void* cashBuf_;
	bool isReleaseCashBuf_;
	uint32_t tick_;
	uint8_t backLauncherStatus_;
	bool isRefreshFlag_;
	Bitmap bitmapList[4];
	// Notification bak
	
	// Notification Callback
	
	// ObserverDrawable bak
	
	// Drawable Update
	
	// custom variables
	
protected:

public:
	EditDialThemeView(PageManager* manager);
	virtual ~EditDialThemeView();

	// PageView override
	void setup() override;
	void quit() override;

	// Screen override
	void handleTickEvent() override;
	void handleKeyEvent(uint8_t c) override;
	void handleClickEvent(const ClickEvent& evt) override;
	void handleDragEvent(const DragEvent& evt) override;
	void handleGestureEvent(const GestureEvent& evt) override;

	// Notification Callback function
	// ObserverDrawable Callback function
	// custom functions
	
};

