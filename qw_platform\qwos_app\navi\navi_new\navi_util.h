#ifndef NAVI_UTIL_H
#define NAVI_UTIL_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

uint8_t navi_util_is_coord_valid(double lng, double lat);

uint8_t navi_util_is_course_valid(float course);

uint8_t navi_util_is_coord_same(double lng1, double lat1, double lng2, double lat2);

float navi_util_course_calc(double lng1, double lat1, double lng2, double lat2);

float navi_util_course_reverse_calc(float course);

float navi_util_course_angle_calc(float course1, float course2);

float navi_util_course_diff_calc(float course1, float course2);

float navi_util_seg_dist_calc(double lng1, double lat1, double lng2, double lat2);

void navi_util_pos_dxdy_calc(double lng, double lat, float dist, double *dx, double *dy);

float navi_util_p2seg_dist_calc(double lng, double lat, double lng1, double lat1, double lng2, double lat2);

int navi_util_p2seg_coord_calc(double lng, double lat, double lng1, double lat1, double lng2, double lat2, double *lng_d, double *lat_d);

float navi_util_triangle_h_calc(float a, float b, float c);

#ifdef __cplusplus
}
#endif

#endif