﻿/*
 * *************************************** Copyright (c) ***************************************
 *                               <PERSON><PERSON> Technology Co., Ltd
 * *********************************************************************************************
 * @File name: alg_compass.h
 * @Author: dingjunjie
 * @Date: 2021-12-23 10:32:35
 * @Description: 电子罗盘实现头文件
 * *********************************************************************************************
 */
#ifndef _COMPASS_ALGORITHM_H
#define _COMPASS_ALGORITHM_H

#include "basic_app_module_log.h"
#include "stdbool.h"

#if defined(__cplusplus)
extern "C" {
#endif
/****************************************************************************
*Private  Functions Definitions
****************************************************************************/
/**
 * @brief 罗盘校准状态
 */
typedef enum
{
    enumEC_NO_CALIBRATE = 0,            // 未校准, 初始状态
    enumEC_IN_CALIBRATING = 1,          // 校准中
    enumEC_CALIBRATE_SUCCESS = 2,       // 校准成功
    enumEC_CALIBRATE_FAILED = 3,        // 校准失败
} ecompass_status_t;

/************************************************************************
* raw_data_listener
* 指南针算法需要使用的原始数据,如mag、acc原始数据.
**************************************************************************/
typedef void (*raw_data_listener)(int type, const void* data, int bytes);

/************************************************************************
* ecompass_result_cb
* 指南针算法输出结果回调
*************************************************************************/
typedef void (*ecompass_result_cb)(ecompass_status_t stat,int angel);

/****************************************************************************
*ecompass_listener_t
*指南针算法的输入输出监听接口.
****************************************************************************/
typedef struct ecompass_listener{
    raw_data_listener mag_acc_cb;
    ecompass_result_cb result_cb;
}ecompass_listener_t;

/**
 * @brief 开始校准
 */
void ecompass_calibrate_start(ecompass_listener_t *listener);

/**
 * @brief 取消校准
 */
void ecompass_calibrate_stop(void);

/**
 * @brief 获取罗盘校准状态
 * @return 罗盘校准状态
 */
ecompass_status_t ecompass_status_get(void);

// uint8_t ecompass_fail_get(void);
// uint32_t get_alg_compass_course_process_count();
/**
 * @brief 注册所有mag相关的原始数据
 */
void register_alg_compass_publisher();

/************************************************************************
 *@function:void reset_ecompass_status(void)
 *@brief:重置指南针算法校准状态,每次进入指南针页面都需要重新校准.
*************************************************************************/
void reset_ecompass_status(void);

/************************************************************************
 *@function:void ecompass_fusion_enable(bool enable)
 *@brief:启用或禁用传感器融合算法
 *@param:enable - true启用，false禁用
*************************************************************************/
void ecompass_fusion_enable(bool enable);

/************************************************************************
 *@function:bool ecompass_fusion_is_enabled(void)
 *@brief:获取传感器融合算法启用状态
 *@return:true已启用，false已禁用
*************************************************************************/
bool ecompass_fusion_is_enabled(void);

/************************************************************************
 *@function:void ecompass_fusion_reset(void)
 *@brief:重置传感器融合算法状态
*************************************************************************/
void ecompass_fusion_reset(void);

/************************************************************************
 *@function:void ecompass_register_gyro_callback(void)
 *@brief:注册陀螺仪数据回调
*************************************************************************/
void ecompass_register_gyro_callback(void);

#if defined(__cplusplus)
}
#endif

#endif // _COMPASS_ALGORITHM_H
