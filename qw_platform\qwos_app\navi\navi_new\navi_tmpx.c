#include <string.h>
#include <stddef.h>
#include <stdbool.h>
#include "navi_tmpx.h"

//读取tmp1文件头
int tmp1_header_read(Tmp1Reader *self, uint8_t *header)
{
    if (self == NULL || header == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 0) != QW_OK)
    {
        return -1;
    }

    UINT br = 0;

    if (qw_f_read(self->fp, header, 32, &br) != QW_OK || br != 32)
    {
        return -1;
    }

    return 0;
}

//读取指定的爬坡信息
int tmp1_climb_info_read(Tmp1Reader *self, uint32_t idx, NaviClimbInfo *info)
{
    if (self == NULL || info == NULL)
    {
        return -1;
    }

    if (idx >= self->num)
    {
        return -1;
    }

    const uint32_t offset = 32 + idx * 28;

    if (qw_f_lseek(self->fp, offset) != QW_OK)
    {
        return -1;
    }

    UINT br = 0;

    if (qw_f_read(self->fp, info, 28, &br) != QW_OK || br != 28)
    {
        return -1;
    }

    return 0;
}

//写入tmp1文件头
int tmp1_header_write(Tmp1Writer *self)
{
    if (self == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 0) != QW_OK)
    {
        return -1;
    }

    UINT bw = 0;

    const uint8_t buf[32] = {
        'T', 'M', 'P', '1', NAVI_FILE_MAJOR_VERSION, NAVI_FILE_MINOR_VERSION,
        'D', 'e', 's', 'i', 'g', 'n', 'e', 'd', ' ', 'b', 'y', ' ', 'J', 'u', 'n',
        'j', 'i', 'e', ' ', 'D', 'i', 'n', 'g', 0, 0, 0,
    };

    if (qw_f_write(self->fp, buf, 32, &bw) != QW_OK || bw != 32)
    {
        return -1;
    }

    return 0;
}

//写入一个爬坡信息
int tmp1_climb_info_write(Tmp1Writer *self, const NaviClimbInfo *info)
{
    if (self == NULL || info == NULL)
    {
        return -1;
    }

    UINT bw = 0;

    if (qw_f_write(self->fp, info, 28, &bw) != QW_OK || bw != 28)
    {
        return -1;
    }

    self->num += 1;

    return 0;
}

//读取tmp2文件头
int tmp2_header_read(Tmp2Reader *self, uint8_t *header)
{
    if (self == NULL || header == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 0) != QW_OK)
    {
        return -1;
    }

    UINT br = 0;

    if (qw_f_read(self->fp, header, 32, &br) != QW_OK || br != 32)
    {
        return -1;
    }

    return 0;
}

//读取指定范围的爬坡点
int tmp2_route_cp_read(Tmp2Reader *self, uint32_t start, uint32_t end, uint32_t cp_num, NaviClimbpoint *cp_buf)
{
    if (self == NULL || cp_buf == NULL)
    {
        return -1;
    }

    if (start > end || end > cp_num)
    {
        return -1;
    }

    const uint32_t ntr = end - start;

    if (ntr == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const uint32_t offset = 32 + start * 8;

    if (qw_f_lseek(self->fp, offset) != QW_OK)
    {
        return -1;
    }

    const UINT btr = ntr * 8;

    UINT br = 0;

    if (qw_f_read(self->fp, cp_buf, btr, &br) != QW_OK || br != btr)
    {
        return -1;
    }

    return 0;
}

//写入tmp2文件头
int tmp2_header_write(Tmp2Writer *self)
{
    if (self == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 0) != QW_OK)
    {
        return -1;
    }

    UINT bw = 0;

    const uint8_t buf[32] = {
        'T', 'M', 'P', '2', NAVI_FILE_MAJOR_VERSION, NAVI_FILE_MINOR_VERSION,
        'D', 'e', 's', 'i', 'g', 'n', 'e', 'd', ' ', 'b', 'y', ' ', 'J', 'u', 'n',
        'j', 'i', 'e', ' ', 'D', 'i', 'n', 'g', 0, 0, 0,
    };

    if (qw_f_write(self->fp, buf, 32, &bw) != QW_OK || bw != 32)
    {
        return -1;
    }

    return 0;
}

//写入一个爬坡点
int tmp2_route_cp_write(Tmp2Writer *self, const NaviClimbpoint *cp)
{
    if (self == NULL || cp == NULL)
    {
        return -1;
    }

    UINT bw = 0;

    if (qw_f_write(self->fp, cp, 8, &bw) != QW_OK || bw != 8)
    {
        return  -1;
    }

    self->num += 1;

    return 0;
}

//从导航线路爬坡点list（临时文件版本）中获取指定的爬坡点
int navi_tmp_route_cp_list_get(NaviTmpRouteCpList *self, uint32_t idx, NaviClimbpoint *output)
{
    if (self == NULL || output == NULL)
    {
        return -1;
    }

    if (idx >= self->len)
    {
        return -1;
    }

    //缓存命中
    const uint32_t start = self->cache.cp_buf.range.start;
    const uint32_t end = self->cache.cp_buf.range.end;

    if (idx >= start && idx < end)
    {
        for (uint32_t i = start; i < end; i++)
        {
            if (idx == i)
            {
                navi_route_cp_copy(output, &self->cache.cp_buf.buf[i-start]);
                return 0;
            }
        }
    }

    //缓存未命中，加载从指定爬坡点开始的若干爬坡点
    NaviRouteCpBuf *cp_buf = &self->cache.cp_buf;

    cp_buf->range.start = idx;
    cp_buf->range.end = cp_buf->range.start + cp_buf->capacity;

    if (cp_buf->range.end > self->len)
    {
        cp_buf->range.end = self->len;
    }

    if (tmp2_route_cp_read(self->cache.tmp2_reader, cp_buf->range.start, cp_buf->range.end, self->len, cp_buf->buf) != 0)
    {
        return -1;
    }

    navi_route_cp_copy(output, &cp_buf->buf[idx - cp_buf->range.start]);

    return 0;
}

//重置导航线路爬坡点list（临时文件版本）
void navi_tmp_route_cp_list_reset(NaviTmpRouteCpList *self)
{
    if (self != NULL)
    {
        navi_route_cp_buf_reset(&self->cache.cp_buf);
    }
}

//复制导航爬坡点信息
void navi_route_cp_info_copy(NaviRouteCpInfo *self, const NaviRouteCpInfo *info)
{
    if (self != NULL && info != NULL)
    {
        memcpy(self, info, sizeof(NaviRouteCpInfo));
    }
}

//更新导航爬坡点信息
void navi_route_cp_info_update(NaviRouteCpInfo *self, float dist, float alt, uint32_t idx)
{
    if (self != NULL)
    {
        self->dist = dist;
        self->alt = alt;
        self->idx = idx;
    }
}

//复制爬坡信息
void navi_climb_info_copy(NaviClimbInfo *self, const NaviClimbInfo *info)
{
    if (self != NULL && info != NULL)
    {
        memcpy(self, info, sizeof(NaviClimbInfo));
    }
}
