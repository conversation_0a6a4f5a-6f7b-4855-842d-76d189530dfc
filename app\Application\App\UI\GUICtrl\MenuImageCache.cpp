/****************************************Copyright (c)****************************************
* <PERSON><PERSON>wu Technology Co., Ltd
*
*---------------------------------------File Info--------------------------------------------
* File path :
* Created by : Lxin
* LastEditors: Lxin
* Descriptions :
*--------------------------------------------------------------------------------------------
* History :
* 2023-07-18 13:36:35: Lxin 原始版本
*
*********************************************************************************************/

#include <GUICtrl/MenuImageCache.h>
#include "qw_os_gui.h"
#include "../../qwos_app/GUI/PageBase.h"
#ifdef IGS_DEV
#include "lv_lcd.h"
#endif
#include "qw_fs.h"
#include "qw_general.h"
#include "MenuImageCacheDef.h"
// #include "view_data_srv_cb.h"

//#define MODULE_PRINTF rt_kprintf
#define MODULE_PRINTF(...)

using namespace touchgfx;

typedef struct {
    uint16_t storeIndex;
    uint16_t reserve;
    lv_img_dsc_t dsc;
    uint8_t userData[USER_DATA_MAX_SIZE];
}MenuImageCahceItem;

typedef struct {
    uint16_t storeSize;
    uint8_t  lastedStorePath[IMAGE_CACHE_STORE_PATH_SIZE];
    MenuImageCahceItem itemGroup[ONE_TYPE_IMAGE_CACHE_MAX_SIZE];
}MenuImageCahceManager;

static const char *imageTypeString[ENUM_IMAGE_CACHE_END] = {
    "navi","stage","history","training"
};

#define MANAGER_STORE_NAME       "cacheDsc"
#define MANAGER_STORE_VER_NAME   "cacheDsc.ini"
#define MANAGER_STORE_VER        111
#define INVALID_INDEX       0xFFFF

#ifdef IGS_DEV
#include "mem_map.h"
#include "mem_section.h"
L2_RET_BSS_SECT_BEGIN(menu_cache_mem)
ALIGN(4) static MenuImageCahceManager MenuImageCahceGroup[ENUM_IMAGE_CACHE_END];
L2_RET_BSS_SECT_END
#else
static MenuImageCahceManager MenuImageCahceGroup[ENUM_IMAGE_CACHE_END];
#endif

static bool enableCachedMemory = false;
static bool managerInfoUpdated = false;
static bool powerOnResourceIdle = false;

static void historyMenuCacheCheck(void);

static uint8_t* getBackgroundCahceAddr(void)
{
#ifdef IGS_DEV
    return lv_display_no_retention_mem_buf_huge(420 * 400 * 3);
#else
    static uint8_t bg_cache[450 * 450 * 3] = {0};
    return bg_cache;
#endif
}

static uint8_t* getBackgroundTxtDscCahceAddr(void)
{
#ifdef IGS_DEV
    return lv_display_no_retention_mem_buf_small(230 * 60 * 3);
#else
    static uint8_t bg_dsc_cache[180 * 44 * 3] = {0};
    return bg_dsc_cache;
#endif
}

void menuImageCacheInit(bool cachedMemory)
{
    //enableCachedMemory = cachedMemory;
    lv_memset_00(MenuImageCahceGroup, sizeof(MenuImageCahceGroup));
    for (uint16_t i = 0; i < ONE_TYPE_IMAGE_CACHE_MAX_SIZE; i++) {
        lv_memset_ff(MenuImageCahceGroup[i].itemGroup,
            sizeof(MenuImageCahceItem)* ONE_TYPE_IMAGE_CACHE_MAX_SIZE);
    }

    char managerStorePath[IMAGE_CACHE_STORE_PATH_SIZE];
    char ver_text[IMAGE_CACHE_STORE_PATH_SIZE];
    uint32_t bw = 0;
    lv_fs_file_t fp;

    //验证缓存版本
    int ver = 0;
    sprintf(managerStorePath, "%s/%s", IMAGE_CACHE_STORE_DIR, MANAGER_STORE_VER_NAME);
    if (LV_FS_RES_OK == lv_fs_open(&fp, managerStorePath, LV_FS_MODE_RD))
    {
        lv_fs_seek(&fp, 0, LV_FS_SEEK_SET);
        lv_fs_read(&fp, ver_text, IMAGE_CACHE_STORE_PATH_SIZE, &bw);
        lv_fs_close(&fp);

        if (bw > 6)
        {
            ver_text[IMAGE_CACHE_STORE_PATH_SIZE - 1] = '\0';
            char* tok = strstr(ver_text, "=");
            if (tok != NULL)
            {
                ver = atoi(tok + 1);
            }
        }
    }
    else
    {
        //无版本控制文件, 认为是旧版本
    }

    if (ver != MANAGER_STORE_VER)
    {
        for (int i = 0; i < ENUM_IMAGE_CACHE_END; i++)
        {
            menuImageCacheStoreReset((MenuImageCacheType)i);
        }

        if (LV_FS_RES_OK == lv_fs_open(&fp, managerStorePath, LV_FS_MODE_WR))
        {
            sprintf(ver_text, "VER = %d", MANAGER_STORE_VER);

            lv_fs_seek(&fp, 0, LV_FS_SEEK_SET);
            lv_fs_write(&fp, ver_text, strlen(ver_text) + 1, &bw);
            lv_fs_close(&fp);
        }
    }
    else
    {
        //读取缓存配置
        sprintf(managerStorePath, "%s/%s", IMAGE_CACHE_STORE_DIR, MANAGER_STORE_NAME);

        if (LV_FS_RES_OK == lv_fs_open(&fp, managerStorePath, LV_FS_MODE_RD)) {
            lv_fs_seek(&fp, 0, LV_FS_SEEK_SET);
            lv_fs_read(&fp, &MenuImageCahceGroup, sizeof(MenuImageCahceGroup), &bw);
            lv_fs_close(&fp);
        }

        if (enableCachedMemory) {

        }
    }

    historyMenuCacheCheck();
    historyMenuCacheSimpleCacheLasted();
}

void menuImageCacheStoreUpdate(void)
{
    if (managerInfoUpdated) {
        char managerStorePath[IMAGE_CACHE_STORE_PATH_SIZE];
        sprintf(managerStorePath, "%s/%s", IMAGE_CACHE_STORE_DIR, MANAGER_STORE_NAME);

        uint32_t bw = 0;
        // lv_fs_file_t fp;
        // lv_fs_open(&fp, managerStorePath, LV_FS_MODE_WR);
        // lv_fs_seek(&fp, 0, LV_FS_SEEK_SET);
        // lv_fs_write(&fp, &MenuImageCahceGroup, sizeof(MenuImageCahceGroup), &bw);
        // lv_fs_close(&fp);
        QW_FIL *fp = NULL;
        if (QW_OK == qw_f_open(&fp, managerStorePath, QW_FA_CREATE_ALWAYS | QW_FA_WRITE)){
            qw_f_lseek(fp, 0);
            qw_f_write(fp, &MenuImageCahceGroup, sizeof(MenuImageCahceGroup), &bw);
            qw_f_close(fp);
        }
    }
}

void menuImageCacheStoreUpdateCancel(void)
{
    managerInfoUpdated = false;
}

void menuImageCacheStoreReset(MenuImageCacheType type)
{
    if (type < ENUM_IMAGE_CACHE_END)
    {
        lv_memset_00(&MenuImageCahceGroup[type], sizeof(MenuImageCahceManager));
        lv_memset_ff(MenuImageCahceGroup[type].itemGroup,
            sizeof(MenuImageCahceItem) * ONE_TYPE_IMAGE_CACHE_MAX_SIZE);
    }   
    managerInfoUpdated = true;
}

bool getLastedMenuImageCache(MenuImageCacheType type, void** imageData, void** userData)
{
    if (!imageData || type >= ENUM_IMAGE_CACHE_END) {
        return false;
    }
    uint16_t storeSize = MenuImageCahceGroup[type].storeSize;
    if (storeSize != 0) {
        uint16_t Index = storeSize - 1;
        if (!enableCachedMemory) {
            *imageData = static_cast<void*>(MenuImageCahceGroup[type].lastedStorePath);
        }
        else {
            *imageData = static_cast<void*>(&MenuImageCahceGroup[type].itemGroup[Index].dsc);
        }
        if (userData) {
            *userData = static_cast<void*>(MenuImageCahceGroup[type].itemGroup[Index].userData);
        }
        return true;
    }
    return false;
}

static void deleteAndResortIndex(MenuImageCacheType type, uint16_t index)
{
    if (type < ENUM_IMAGE_CACHE_END)
    {
        uint16_t i = 0;
        uint16_t deleteIndex = INVALID_INDEX;
        uint16_t storeSize = MenuImageCahceGroup[type].storeSize;

        char storePath[IMAGE_CACHE_STORE_PATH_SIZE];
        char oldStorePath[IMAGE_CACHE_STORE_PATH_SIZE];

        for (i = 0; i < storeSize && i < ONE_TYPE_IMAGE_CACHE_MAX_SIZE; i++) {
            if (index == i) {
                MenuImageCahceGroup[type].itemGroup[i].storeIndex = INVALID_INDEX;
                deleteIndex = i;
                storeSize--;
                MenuImageCahceGroup[type].storeSize = storeSize;
                sprintf(oldStorePath, "%s/%s_%d.bin", IMAGE_CACHE_STORE_DIR, imageTypeString[type], i);
                // rt_kprintf("delete index %d:%s\n", i, oldStorePath);
                qw_f_unlink(oldStorePath);
                break;
            }
        }
        if ((deleteIndex != INVALID_INDEX) && MenuImageCahceGroup[type].storeSize) {
            for (i = deleteIndex; i < storeSize && i < ONE_TYPE_IMAGE_CACHE_MAX_SIZE - 1; i++) {
                lv_memcpy(&MenuImageCahceGroup[type].itemGroup[i], \
                    & MenuImageCahceGroup[type].itemGroup[i + 1], sizeof(MenuImageCahceItem));
                sprintf(oldStorePath, "%s/%s_%d.bin", IMAGE_CACHE_STORE_DIR, imageTypeString[type], i + 1);
                sprintf(storePath, "%s/%s_%d.bin", IMAGE_CACHE_STORE_DIR, imageTypeString[type], i);
                // rt_kprintf("%s qw_f_rename %s -> %s\n", __func__, oldStorePath, storePath);
                qw_f_rename(oldStorePath, storePath);
            }
        }
    }    
}

static uint16_t getNextStoreIndex(MenuImageCacheType type)
{
    return MenuImageCahceGroup[type].storeSize;
}

bool setLastedMenuImageCache(MenuImageCacheType type, void* container, void* userData, uint16_t userDataSize, bool externBuffer)
{
    char storePath[IMAGE_CACHE_STORE_PATH_SIZE];
    char oldStorePath[IMAGE_CACHE_STORE_PATH_SIZE];
    
    if (type >= ENUM_IMAGE_CACHE_END)
    {
        return false;
    }

    if (type != ENUM_IMAGE_CACHE_STAGE && ENUM_IMAGE_CACHE_HISTORY != type)
    {
        for (uint16_t i = 0; i < MenuImageCahceGroup[type].storeSize && i < ONE_TYPE_IMAGE_CACHE_MAX_SIZE; i++)
        {
            //此处保证存入cache时的userData都是以文件路径字符串开头, 后续修改不要改动
            if (strcmp(reinterpret_cast<const char*>(MenuImageCahceGroup[type].itemGroup[i].userData),
                    reinterpret_cast<const char*>(userData)) == 0)
            {
                uint8_t endIndex = MenuImageCahceGroup[type].storeSize - 1;
                if (i != endIndex)
                {
                    MenuImageCahceItem tmp;
                    memcpy(&tmp, &MenuImageCahceGroup[type].itemGroup[i], sizeof(MenuImageCahceItem));
                    sprintf(oldStorePath, "%s/%s_%d.bin", IMAGE_CACHE_STORE_DIR, imageTypeString[type], i);
                    sprintf(storePath, "%s/%s_%d.bintmp", IMAGE_CACHE_STORE_DIR, imageTypeString[type], i);
                    // rt_kprintf("qw_f_rename %s -> %s\n", oldStorePath, storePath);
                    qw_f_rename(oldStorePath, storePath);

                    for (int16_t j = i; j < endIndex && j < ONE_TYPE_IMAGE_CACHE_MAX_SIZE - 1; j++) {
                        memcpy(&MenuImageCahceGroup[type].itemGroup[j], &MenuImageCahceGroup[type].itemGroup[j + 1], sizeof(MenuImageCahceItem));
                        sprintf(oldStorePath, "%s/%s_%d.bin", IMAGE_CACHE_STORE_DIR, imageTypeString[type], j + 1);
                        sprintf(storePath, "%s/%s_%d.bin", IMAGE_CACHE_STORE_DIR, imageTypeString[type], j);
                        // rt_kprintf("qw_f_rename %s -> %s\n", oldStorePath, storePath);
                        qw_f_rename(oldStorePath, storePath);
                    }

                    if (endIndex < ONE_TYPE_IMAGE_CACHE_MAX_SIZE)
                    {
                        memcpy(&MenuImageCahceGroup[type].itemGroup[endIndex], &tmp, sizeof(MenuImageCahceItem));
                        sprintf(oldStorePath, "%s/%s_%d.bintmp", IMAGE_CACHE_STORE_DIR, imageTypeString[type], i);
                        sprintf(storePath, "%s/%s_%d.bin", IMAGE_CACHE_STORE_DIR, imageTypeString[type], endIndex);
                        // rt_kprintf("qw_f_rename %s -> %s\n", oldStorePath, storePath);
                        qw_f_rename(oldStorePath, storePath);
                    }
                }
                return true;
            }
        }
    }

    if (ENUM_IMAGE_CACHE_HISTORY == type)
    {
        uint8_t areadyCached = false;
        HISTORY_CACHE_INFO* chcheInfo = static_cast<HISTORY_CACHE_INFO*>(userData);
        if (chcheInfo && !checkHistoryMenuCacheIsNeedUpdate(chcheInfo->fit_time, &areadyCached))
        {
            return true;
        }
        if (areadyCached)
        {
            deleteAndResortIndex(ENUM_IMAGE_CACHE_HISTORY, MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].storeSize - 1);
        }
    }

    SnapshotWidget snap;
    snap.setPosition(*(static_cast<Container*>(container)));

    if (powerOnResourceIdle) {
        snap.makeSnapshot(*(static_cast<Container*>(container)), getBackgroundCahceAddr(), false);
    }
    else {
        snap.makeSnapshot(*(static_cast<Container*>(container)), HAL::getAnimationBuffer(), false);
    }

    void* imageData;
    lv_img_dsc_t resizeImgDsc;
    lv_memset_00(&resizeImgDsc, sizeof(lv_img_dsc_t));

    if (type == ENUM_IMAGE_CACHE_TRAINING)
    {
        imageData = lv_gx_pixelmap_resize(snap.getCachedDsc(), &resizeImgDsc, IMAGE_WORKOUT_CHCHE_WIDTH, IMAGE_WORKOUT_CHCHE_HEIGHT);
    }
    else
    {
        imageData = lv_gx_pixelmap_resize(snap.getCachedDsc(), &resizeImgDsc, IMAGE_CHCHE_WIDTH, IMAGE_CHCHE_HEIGHT);
    }

    uint16_t storeIndex = 0;
    uint16_t storeSize = MenuImageCahceGroup[type].storeSize;
    if (storeSize == ONE_TYPE_IMAGE_CACHE_MAX_SIZE) {
        deleteAndResortIndex(type, 0);
        storeSize = MenuImageCahceGroup[type].storeSize;
    }
    uint16_t nextStoreIndex = getNextStoreIndex(type);
    if (nextStoreIndex >= ONE_TYPE_IMAGE_CACHE_MAX_SIZE)
    {
        return false;
    }
    lv_memcpy(&MenuImageCahceGroup[type].itemGroup[nextStoreIndex].dsc, \
        &resizeImgDsc, sizeof(lv_img_dsc_t));
    if (userDataSize) {
        lv_memcpy(&MenuImageCahceGroup[type].itemGroup[nextStoreIndex].userData, \
            userData, userDataSize);
    }

    sprintf(storePath, "%s/%s_%d.bin", IMAGE_CACHE_STORE_DIR, imageTypeString[type], nextStoreIndex);
    lv_memcpy(MenuImageCahceGroup[type].lastedStorePath, storePath, strlen(storePath) + 1);

    if (storeSize < ONE_TYPE_IMAGE_CACHE_MAX_SIZE) {
        MenuImageCahceGroup[type].storeSize++;
        MenuImageCahceGroup[type].itemGroup[MenuImageCahceGroup[type].storeSize - 1].storeIndex = nextStoreIndex;
    }

    uint32_t bw = 0;
    lv_fs_file_t fp;
    lv_fs_open(&fp, storePath, LV_FS_MODE_WR);
    lv_fs_seek(&fp, 0, LV_FS_SEEK_SET);

    lv_fs_write(&fp, &resizeImgDsc.header, sizeof(lv_img_header_t), &bw);
    lv_fs_write(&fp, imageData, resizeImgDsc.data_size, &bw);

    lv_fs_close(&fp);

    managerInfoUpdated = true;

    if (enableCachedMemory) {

    }

    lv_mem_free(imageData);
#ifdef SIMULATOR
    menuImageCacheStoreUpdate();
#endif
    return false;
}

static void historyMenuCacheCheck(void)
{
    // uint32_t storeFitime[ONE_TYPE_IMAGE_CACHE_MAX_SIZE] = { 0 };
    // for (uint8_t i = 0; i < MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].storeSize; i++)
    // {
    //     HISTORY_CACHE_INFO chcheInfo = { 0 };
    //     lv_memcpy(&chcheInfo, &MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].itemGroup[i].userData, sizeof(chcheInfo));
    //     storeFitime[i] = chcheInfo.fit_time;
    // }
    // MODULE_PRINTF("Fit store [%d],[%d],[%d]\n", storeFitime[0], storeFitime[1], storeFitime[2]);
    // for (uint8_t i = 0; i < MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].storeSize; i++)
    // {
    //     if (storeFitime[i] != 0 && UINT16_MAX == view_api_activities_file_time_index_get(storeFitime[i])) {
    //         historyMenuCacheCheckAndDelete(storeFitime[i], false);
    //         MODULE_PRINTF("Fit %d is no exist\n", storeFitime[i]);
    //     }
    // }
}

bool historyMenuCacheSimpleCacheLasted(void)
{
//     uint32_t fitime;

//     if (view_api_activities_file_time_get(0, &fitime) && fitime > 0)
//     {
//         uint8_t areadyCached = false;
//         if (checkHistoryMenuCacheIsNeedUpdate(fitime, &areadyCached) && !areadyCached)
//         {
//             MODULE_PRINTF("%s add %d\n", __func__, fitime);
//             HISTORY_CACHE_INFO chcheInfo = { 0 };
//             chcheInfo.fit_time = fitime;
//             convert_fittime_to_path(fitime, chcheInfo.path);

//             uint16_t storeIndex = 0;
//             uint16_t storeSize = MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].storeSize;

//             if (storeSize == ONE_TYPE_IMAGE_CACHE_MAX_SIZE) {
//                 deleteAndResortIndex(ENUM_IMAGE_CACHE_HISTORY, 0);
//                 storeSize = MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].storeSize;
//             }

//             uint16_t nextStoreIndex = getNextStoreIndex(ENUM_IMAGE_CACHE_HISTORY);
//             lv_memset_00(MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].lastedStorePath, IMAGE_CACHE_STORE_PATH_SIZE);
//             lv_memcpy(&MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].itemGroup[nextStoreIndex].userData, &chcheInfo, sizeof(chcheInfo));

//             if (storeSize < ONE_TYPE_IMAGE_CACHE_MAX_SIZE)
//             {
//                 MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].storeSize++;
//                 MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].itemGroup[MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].storeSize - 1].storeIndex = nextStoreIndex;
//             }
//             MODULE_PRINTF("%s size:%d\n",__func__, MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].storeSize);
//             managerInfoUpdated = true;
// #ifdef SIMULATOR
//             menuImageCacheStoreUpdate();
// #endif
//             return true;
//         }
//     }
    return false;
}


void historyMenuCacheCheckAndDelete(uint32_t fitime, bool invalidCache)
{

    if (invalidCache)
    {
        lv_memset_00(&MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY], sizeof(MenuImageCahceManager));
        historyMenuCacheSimpleCacheLasted();
    }
    else if (fitime)
    {
        uint8_t i = 0;
        bool needDelete = false;
        HISTORY_CACHE_INFO chcheInfo = { 0 };
        MODULE_PRINTF("%s %d, %d\n", __func__, fitime, MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].storeSize);
        for (; i < MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].storeSize && i < ONE_TYPE_IMAGE_CACHE_MAX_SIZE; i++)
        {
            memcpy(&chcheInfo, &MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].itemGroup[i].userData, sizeof(HISTORY_CACHE_INFO));
            MODULE_PRINTF("%s Compare %d : %d\n", __func__, fitime, chcheInfo.fit_time);
            if (fitime == chcheInfo.fit_time)
            {
                needDelete = true;
                break;
            }
        }
        if (needDelete)
        {
            void* img_data;
            HISTORY_CACHE_INFO* pchcheInfo = NULL;
            deleteAndResortIndex(ENUM_IMAGE_CACHE_HISTORY, i);
            historyMenuCacheSimpleCacheLasted();
            if (getLastedMenuImageCache(ENUM_IMAGE_CACHE_HISTORY, reinterpret_cast<void**>(&img_data), reinterpret_cast<void**>(&pchcheInfo)))
            {
                if (pchcheInfo && pchcheInfo->distance == 0 && pchcheInfo->speed == 0 && pchcheInfo->time == 0)
                {
                    lv_memset_00(MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].lastedStorePath, IMAGE_CACHE_STORE_PATH_SIZE);
                }
                else if (pchcheInfo)
                {
                    char storePath[IMAGE_CACHE_STORE_PATH_SIZE];
                    sprintf(storePath, "%s/%s_%d.bin", IMAGE_CACHE_STORE_DIR, imageTypeString[ENUM_IMAGE_CACHE_HISTORY], MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].storeSize - 1);
                    lv_memcpy(MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].lastedStorePath, storePath, strlen(storePath) + 1);
                    MODULE_PRINTF("history lastedStorePath:%s\n", storePath);
                }
                managerInfoUpdated = true;
#ifdef SIMULATOR
                menuImageCacheStoreUpdate();
#endif
            }
            else
            {
                managerInfoUpdated = true;
#ifdef SIMULATOR
                menuImageCacheStoreUpdate();
#endif
            }
        }
    }
}

bool checkHistoryMenuCacheIsNeedUpdate(uint32_t fitime, uint8_t* isAreadyCached)
{
    HISTORY_CACHE_INFO chcheInfo = { 0 };
    int16_t lastedIndex = MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].storeSize - 1;
    if (lastedIndex >= 0)
    {
        memcpy(&chcheInfo, &MenuImageCahceGroup[ENUM_IMAGE_CACHE_HISTORY].itemGroup[lastedIndex].userData, sizeof(HISTORY_CACHE_INFO));
        if (fitime < chcheInfo.fit_time)
        {
            return false;
        }
        else if (fitime == chcheInfo.fit_time)
        {
            if (isAreadyCached)
            {
                *isAreadyCached = true;
            }
            if (chcheInfo.distance != 0 || chcheInfo.speed != 0 || chcheInfo.time != 0)
            {
                return false;
            }
        }
    }

    return true;
}



void deleteMenuImageCache(MenuImageCacheType type, const char* path)
{
    if (!path || ENUM_IMAGE_CACHE_END <= type) {
        return;
    }
    uint16_t storeSize = MenuImageCahceGroup[type].storeSize;

    for (uint16_t n = 0; n < ONE_TYPE_IMAGE_CACHE_MAX_SIZE; n++)
    {
        uint16_t deleteIndex = INVALID_INDEX;
        for (uint16_t i = 0; i < storeSize && i < ONE_TYPE_IMAGE_CACHE_MAX_SIZE; i++)
        {
            //此处保证存入cache时的userData都是以文件路径字符串开头, 后续修改不要改动
            if (strcmp(reinterpret_cast<const char*>(MenuImageCahceGroup[type].itemGroup[i].userData), path) == 0)
            {
                deleteIndex = i;
                break;
            }
        }
        if (deleteIndex != INVALID_INDEX)
        {
            deleteAndResortIndex(type, deleteIndex);
            storeSize = MenuImageCahceGroup[type].storeSize;
            if (storeSize) {
                char storePath[IMAGE_CACHE_STORE_PATH_SIZE];
                sprintf(storePath, "%s/%s_%d.bin", IMAGE_CACHE_STORE_DIR, imageTypeString[type], storeSize - 1);
                lv_memcpy(MenuImageCahceGroup[type].lastedStorePath, storePath, strlen(storePath) + 1);
            }
            managerInfoUpdated = true;
        }
        else
        {
            break;
        }
    }
}

/**********************************power on image cache**********************************/
static lv_img_dsc_t poweron_image_cahce[SYS_IMAGE_CACHE_END];

static const char *poweron_img_path[] = {
#ifdef IGS_DEV
    "1:/Img/im_logo_circle_bin.bin",
    "1:/Img/im_new_logo_icon_bin.bin",
    "1:/Img/im_logo_user.bin",
    "1:/Img/im_menu_user.bin"
#else
    "./Img/im_logo_circle_bin.bin",
    "./Img/im_new_logo_icon_bin.bin",
    "./Img/im_logo_user.bin",
    "./Img/im_menu_user.bin"
#endif
};

void loadResourceFromFileSystem(PowerOnImageCacheType type)
{
    uint32_t bw = 0;
    QW_FIL* fp;
    uint32_t src_size;
    lv_img_dsc_t* img_dsc = &poweron_image_cahce[type];

    if (LV_FS_RES_OK == qw_f_open(&fp, poweron_img_path[type], QW_FA_OPEN_EXISTING | QW_FA_READ)) {
        qw_f_read(fp, static_cast<void*>(&img_dsc->header), sizeof(lv_img_header_t), &bw);
        src_size = qw_f_size(fp) - sizeof(lv_img_header_t);
        img_dsc->data_size = src_size;

        void* img_data = NULL;
        if (POWERON_IMAGE_CACHE_BG_TXT_DSC == type) {
            img_data = getBackgroundTxtDscCahceAddr();
            img_dsc->data = static_cast<const uint8_t*>(img_data);
        }
        else {
            img_data = getBackgroundCahceAddr();
            img_dsc->data = static_cast<const uint8_t*>(img_data);
        }

        qw_f_read(fp, img_data, src_size, &bw);
        qw_f_close(fp);
    }
}

void* powerOnImageCacheGet(PowerOnImageCacheType type)
{
#ifndef UI_SUPPORT_MEMU_IMAGE
    if(MENU_IMAGE_BACKGROUND == type){
        return NULL;
    }
#endif
    if (LV_IMG_CF_UNKNOWN != poweron_image_cahce[type].header.cf) {
        return (void*)(&poweron_image_cahce[type]);
    }
    return NULL;
}

void powerOnImageCacheInit(void)
{
    QW_FIL* fp;
    bool has_user_img = false;
    lv_memset_00(&poweron_image_cahce, sizeof(poweron_image_cahce));
    if (QW_OK == qw_f_open(&fp, poweron_img_path[POWERON_IMAGE_CACHE_USER], QW_FA_OPEN_EXISTING | QW_FA_READ))
    {
        has_user_img = true;
        qw_f_close(fp);
    }
    if (has_user_img) {
        loadResourceFromFileSystem(POWERON_IMAGE_CACHE_USER);
    }
    else {
        loadResourceFromFileSystem(POWERON_IMAGE_CACHE_BG_DEFAULT);
        loadResourceFromFileSystem(POWERON_IMAGE_CACHE_BG_TXT_DSC);
    }
}

void powerOnResourceRelease(void)
{
    powerOnResourceIdle = true;
}

/**********************************preview cache**********************************/
// #include "navi_canvas.h"
#include "navi_route.h"
// #include "workout_interface.h"
// #include "workout_canvas.h"
#include <GUICtrl/MenuBarPreviewDef.h>
// #include "view_data_cfg_cb.h"

typedef struct {
    char* pathname;
    MenuImageCacheType type;
}previewParam;

static void navi_preview_draw_canvas(const void* map)
{
    // show_preview_track_thumbnail(0, 0, 100, 100, 3);
}

static void history_preview_draw_canvas(const void* map)
{
    // ride_summary_show_track_thumbnail(0, 0, 100, 100, 3);
}

static void training_preview_draw_canvas(const void* map)
{
    // hWorkout pWkt = workout_preview_get();
    // workout_preview_draw(0, 0, 230, 24, pWkt);
}

static void segment_preview_draw_canvas(const void* map)
{
    // segment_cache_t* segment_cache = segment_cache_get();
    // show_point_track_thumbnail(0, 0, 100, 100, 3, segment_cache->points.points_num, (record_mesg_simple_t *)segment_cache->points.points, THUMB_SEGMENT_COLOR);
}

void previewFileImageCacheCreate(void* userData)
{
    bool created = false;
    previewParam* param = static_cast<previewParam*>(userData);

    Container m_CanvasContainer;
    QwCanvasWidget Canvas;

    lv_gui_map_t* map_obj = static_cast<lv_gui_map_t*>(Canvas.getLvMapObj());
    lv_gui_map_init_user_data(map_obj, 0, 0, NULL);

    m_CanvasContainer.add(Canvas);

    switch (param->type)
    {
        case ENUM_IMAGE_CACHE_NAVI:
        {
            // DEFAULT_NAVI_CACHE_INFO(chcheInfo);
            // get_preview_thumbnail_info(&chcheInfo.distance, &chcheInfo.climb);
            // strcpy(chcheInfo.path, param->pathname);

            // lv_gui_map_set_draw(map_obj, navi_preview_draw_canvas);
            // m_CanvasContainer.setPosition(0, 0, 100, 100);
            // Canvas.setPosition(m_CanvasContainer);

            // setLastedMenuImageCache(param->type, &m_CanvasContainer, &chcheInfo, sizeof(NAVI_CACHE_INFO), true);
            // view_api_set_navi_cache_status(true);

            created = true;
            break;
        }
        case ENUM_IMAGE_CACHE_HISTORY:
        {
            break;
        }
        case ENUM_IMAGE_CACHE_TRAINING:
        {
            // hWorkout pWktHandle = workout_preview_get();
            // if (pWktHandle != nullptr && !workout_is_ftp_test(pWktHandle))
            // {
            //     DEFAULT_WORKOUT_CACHE_INFO(chcheInfo);
            //     strcpy(chcheInfo.path, param->pathname);

            //     lv_gui_map_set_draw(map_obj, training_preview_draw_canvas);
            //     m_CanvasContainer.setPosition(0, 0, 230, 24);
            //     Canvas.setPosition(m_CanvasContainer);

            //     setLastedMenuImageCache(ENUM_IMAGE_CACHE_TRAINING, &m_CanvasContainer,
            //         &chcheInfo, sizeof(WORKOUT_CACHE_INFO), true);

            //     created = true;
            // }
            break;
        }
        case ENUM_IMAGE_CACHE_STAGE:
        {
            // segment_cache_t* segment_cache = segment_cache_get();
            // lv_gui_map_set_draw(map_obj, segment_preview_draw_canvas);

            // m_CanvasContainer.setPosition(0, 0, 100, 100);
            // Canvas.setPosition(m_CanvasContainer);

            // setLastedMenuImageCache(ENUM_IMAGE_CACHE_STAGE, &m_CanvasContainer,
            //     &segment_cache->info, sizeof(segment_cache_info_t), true);

            // created = true;
            break;
        }
        default:
            break;
    }

    if (created) {
        if (ENUM_IMAGE_CACHE_NAVI != param->type){
            // gui_command_submit(enumGUICMD_RESET_SCREEN, NULL);
        }
    }

    if (param->pathname) {
        lv_mem_free(param->pathname);
    }
    lv_mem_free(param);
}

void previewFileImageCacheCreateWithInfo(MenuImageCacheType type, void* userData, uint16_t userDataSize)
{
    Container m_CanvasContainer;
    QwCanvasWidget Canvas;

    lv_gui_map_t* map_obj = static_cast<lv_gui_map_t*>(Canvas.getLvMapObj());
    lv_gui_map_init_user_data(map_obj, 0, 0, NULL);

    m_CanvasContainer.add(Canvas);

    void (*preview_draw)(const void*) = NULL;
    switch (type)
    {
    case ENUM_IMAGE_CACHE_NAVI:
    {
        // view_api_set_navi_cache_status(true);
        // preview_draw = navi_preview_draw_canvas;
        break;
    }
    case ENUM_IMAGE_CACHE_HISTORY:
    {
        // preview_draw = history_preview_draw_canvas;
        break;
    }
    case ENUM_IMAGE_CACHE_STAGE:
    {
        // preview_draw = segment_preview_draw_canvas;
        break;
    }
    default:
        break;
    }

    lv_gui_map_set_draw(map_obj, preview_draw);
    m_CanvasContainer.setPosition(0, 0, 100, 100);
    Canvas.setPosition(m_CanvasContainer);

    setLastedMenuImageCache(type, &m_CanvasContainer, userData, userDataSize, true);
}

void previewFileUpdate(const char* pathname, MenuImageCacheType type, bool pre_load)
{
    previewParam* param = NULL;
    param = static_cast<previewParam*>(lv_mem_alloc(sizeof(previewParam)));
    lv_memset_00(param, sizeof(previewParam));
    param->type = type;

    if (pathname != NULL) {
        uint32_t len = strlen(pathname) + 1;
        param->pathname = static_cast<char*>(lv_mem_alloc(len));
        strcpy(param->pathname, pathname);
    }

    switch (param->type)
    {
    case ENUM_IMAGE_CACHE_NAVI:
        // route_preview(param->pathname);
        break;
    case ENUM_IMAGE_CACHE_TRAINING:
    {
        // hWorkout pWktHandle = workout_preview(param->pathname);
        // if (pWktHandle == NULL) {
        //     return;
        // }
        // else
        {
            break;
        }
    }
    case ENUM_IMAGE_CACHE_STAGE:
        break;
    default:
        break;
    }

    if (pre_load){
        // gui_preload_submit(enumGUICMD_PREVIEW_UPDATA_PRELOAD, (void*)param);
    }
    else{
        // gui_command_submit(enumGUICMD_PREVIEW_UPDATA_FROM_APP, (void*)param);
    }
}
/**********************************schedule preview cache**********************************/

typedef struct {
    int weekindex;
    int dayindex;
    uint8_t  StoreMainPath[IMAGE_CACHE_STORE_PATH_SIZE];
    uint8_t  StorePath[IMAGE_CACHE_STORE_PATH_SIZE];
}ScheduleImageCahceItem;

typedef struct {
    uint16_t storeSize;
    ScheduleImageCahceItem itemGroup[SCHEDULE_IMAGE_CACHE_MAX_SIZE];
}ScheduleImageCahceManager;

static const char* imageScheduleString = "schedule";

#define SCHEDULE_MANAGER_STORE_NAME       "cacheDsc"
#define SCHEDULE_MANAGER_STORE_VER_NAME   "cacheDsc.ini"
#define SCHEDULE_MANAGER_STORE_VER        110
#define SCHEDULE_INVALID_INDEX       0xFFFF

// static ScheduleImageCahceManager ScheduleImageCahceGroup;
// static bool scheduleEnableCachedMemory = false;
// static bool scheduleManagerInfoUpdated = false;
// static hWorkout s_pWktHandle = nullptr;

void scheduleImageCacheInit(void)
{
    // lv_memset_00(&ScheduleImageCahceGroup, sizeof(ScheduleImageCahceGroup));

    // lv_memset_ff(ScheduleImageCahceGroup.itemGroup,
    //     sizeof(ScheduleImageCahceItem) * SCHEDULE_IMAGE_CACHE_MAX_SIZE);

    // char managerStorePath[IMAGE_CACHE_STORE_PATH_SIZE];
    // char ver_text[IMAGE_CACHE_STORE_PATH_SIZE];
    // uint32_t bw = 0;
    // lv_fs_file_t fp;

    // //验证缓存版本
    // int ver = 0;
    // sprintf(managerStorePath, "%s/%s", SCHEDULE_IMAGE_CACHE_STORE_DIR, SCHEDULE_MANAGER_STORE_VER_NAME);
    // if (LV_FS_RES_OK == lv_fs_open(&fp, managerStorePath, LV_FS_MODE_RD))
    // {
    //     lv_fs_seek(&fp, 0, LV_FS_SEEK_SET);
    //     lv_fs_read(&fp, ver_text, IMAGE_CACHE_STORE_PATH_SIZE, &bw);
    //     lv_fs_close(&fp);

    //     if (bw > 6)
    //     {
    //         ver_text[IMAGE_CACHE_STORE_PATH_SIZE - 1] = '\0';
    //         char* tok = strstr(ver_text, "=");
    //         if (tok != NULL)
    //         {
    //             ver = atoi(tok + 1);
    //         }
    //     }
    // }
    // else
    // {
    //     //无版本控制文件, 认为是旧版本
    // }

    // if (ver != SCHEDULE_MANAGER_STORE_VER)
    // {
    //     for (int i = 0; i < ENUM_IMAGE_CACHE_END; i++)
    //     {
    //         scheduleImageCacheStoreReset();
    //     }

    //     if (LV_FS_RES_OK == lv_fs_open(&fp, managerStorePath, LV_FS_MODE_WR))
    //     {
    //         sprintf(ver_text, "VER = %d", SCHEDULE_MANAGER_STORE_VER);

    //         lv_fs_seek(&fp, 0, LV_FS_SEEK_SET);
    //         lv_fs_write(&fp, ver_text, strlen(ver_text) + 1, &bw);
    //         lv_fs_close(&fp);
    //     }
    // }
    // else
    // {
    //     //读取缓存配置
    //     sprintf(managerStorePath, "%s/%s", SCHEDULE_IMAGE_CACHE_STORE_DIR, SCHEDULE_MANAGER_STORE_VER_NAME);

    //     if (LV_FS_RES_OK == lv_fs_open(&fp, managerStorePath, LV_FS_MODE_RD)) {
    //         lv_fs_seek(&fp, 0, LV_FS_SEEK_SET);
    //         lv_fs_read(&fp, &ScheduleImageCahceGroup, sizeof(ScheduleImageCahceGroup), &bw);
    //         lv_fs_close(&fp);
    //     }
    // }
}

void scheduleImageCacheStoreUpdate(void)
{
    // if (managerInfoUpdated) {
    //     char managerStorePath[IMAGE_CACHE_STORE_PATH_SIZE];
    //     sprintf(managerStorePath, "%s/%s", SCHEDULE_IMAGE_CACHE_STORE_DIR, SCHEDULE_MANAGER_STORE_NAME);

    //     uint32_t bw = 0;
    //     lv_fs_file_t fp;
    //     lv_fs_open(&fp, managerStorePath, LV_FS_MODE_WR);
    //     lv_fs_seek(&fp, 0, LV_FS_SEEK_SET);
    //     lv_fs_write(&fp, &ScheduleImageCahceGroup, sizeof(ScheduleImageCahceGroup), &bw);
    //     lv_fs_close(&fp);
    // }
}

void scheduleImageCacheStoreReset(void)
{
    // lv_memset_00(&ScheduleImageCahceGroup, sizeof(ScheduleImageCahceManager));
    // lv_memset_ff(ScheduleImageCahceGroup.itemGroup,
    //     sizeof(ScheduleImageCahceItem) * SCHEDULE_IMAGE_CACHE_MAX_SIZE);
    // scheduleManagerInfoUpdated = true;


}

bool getScheduleImageCache(void** imageData, int weekindex, int dayindex, bool mainflag)
{
    // if (!imageData) {
    //     return false;
    // }

    // uint16_t storeSize = ScheduleImageCahceGroup.storeSize;
    // if (storeSize != 0)
    // {
    //     for (int i = 0; i < storeSize; i++)
    //     {
    //         if ((ScheduleImageCahceGroup.itemGroup[i].weekindex == weekindex) &&
    //             (ScheduleImageCahceGroup.itemGroup[i].dayindex == dayindex))
    //         {
    //             if(mainflag)
    //             {
    //                 *imageData = (void*)ScheduleImageCahceGroup.itemGroup[i].StoreMainPath;
    //             }
    //             else
    //             {
    //                 *imageData = (void*)ScheduleImageCahceGroup.itemGroup[i].StorePath;
    //             }
    //         }
    //     }

    //     return true;
    // }
    return false;
}

bool setScheduleImageCacheMain(void* container, int weekindex, int dayindex)
{
    // SnapshotWidget snap;
    // snap.setPosition(*(Container*)container);

    // if (powerOnResourceIdle) {
    //     snap.makeSnapshot(*(Container*)container, getBackgroundCahceAddr(), true);
    // }
    // else {
    //     snap.makeSnapshot(*(Container*)container, HAL::getAnimationBuffer(), true);
    // }

    // void* imageData;
    // lv_img_dsc_t resizeImgDsc;
    // lv_memset_00(&resizeImgDsc, sizeof(lv_img_dsc_t));

    // imageData = lv_gx_pixelmap_resize(snap.getCachedDsc(), &resizeImgDsc, 100, 40);

    // uint16_t storeIndex = 0;
    // uint16_t storeSize = ScheduleImageCahceGroup.storeSize;

    // char storePath[IMAGE_CACHE_STORE_PATH_SIZE];
    // sprintf(storePath, "%s/%s_%d_%d_M.bin", SCHEDULE_IMAGE_CACHE_STORE_DIR, imageScheduleString, weekindex, dayindex);
    // qw_f_unlink(storePath);

    // if (storeSize < SCHEDULE_IMAGE_CACHE_MAX_SIZE)
    // {
    //     ScheduleImageCahceGroup.storeSize++;
    //     lv_memcpy(ScheduleImageCahceGroup.itemGroup[ScheduleImageCahceGroup.storeSize - 1].StoreMainPath, storePath, strlen(storePath) + 1);
    //     ScheduleImageCahceGroup.itemGroup[ScheduleImageCahceGroup.storeSize - 1].weekindex = weekindex;
    //     ScheduleImageCahceGroup.itemGroup[ScheduleImageCahceGroup.storeSize - 1].dayindex = dayindex;
    // }

    // uint32_t bw = 0;
    // lv_fs_file_t fp;
    // lv_fs_open(&fp, storePath, LV_FS_MODE_WR);
    // lv_fs_seek(&fp, 0, LV_FS_SEEK_SET);

    // lv_fs_write(&fp, &resizeImgDsc.header, sizeof(lv_img_header_t), &bw);
    // lv_fs_write(&fp, imageData, resizeImgDsc.data_size, &bw);

    // lv_fs_close(&fp);

    // scheduleManagerInfoUpdated = true;

    // lv_mem_free(imageData);

    // scheduleImageCacheStoreUpdate();

    return false;
}

bool setScheduleImageCache(void* container, int weekindex, int dayindex)
{
    // SnapshotWidget snap;
    // snap.setPosition(*(Container*)container);

    // if (powerOnResourceIdle) {
    //     snap.makeSnapshot(*(Container*)container, getBackgroundCahceAddr(), true);
    // }
    // else {
    //     snap.makeSnapshot(*(Container*)container, HAL::getAnimationBuffer(), true);
    // }

    // void* imageData;
    // lv_img_dsc_t resizeImgDsc;
    // lv_memset_00(&resizeImgDsc, sizeof(lv_img_dsc_t));

    // imageData = lv_gx_pixelmap_resize(snap.getCachedDsc(), &resizeImgDsc, 260, 65);

    // uint16_t storeIndex = 0;
    // uint16_t storeSize = ScheduleImageCahceGroup.storeSize;

    // char storePath[IMAGE_CACHE_STORE_PATH_SIZE];
    // sprintf(storePath, "%s/%s_%d_%d.bin", SCHEDULE_IMAGE_CACHE_STORE_DIR, imageScheduleString, weekindex, dayindex);
    // qw_f_unlink(storePath);

    // if (storeSize < SCHEDULE_IMAGE_CACHE_MAX_SIZE)
    // {
    //     ScheduleImageCahceGroup.storeSize++;
    //     lv_memcpy(ScheduleImageCahceGroup.itemGroup[ScheduleImageCahceGroup.storeSize - 1].StorePath, storePath, strlen(storePath) + 1);
    //     ScheduleImageCahceGroup.itemGroup[ScheduleImageCahceGroup.storeSize - 1].weekindex = weekindex;
    //     ScheduleImageCahceGroup.itemGroup[ScheduleImageCahceGroup.storeSize - 1].dayindex = dayindex;
    // }

    // uint32_t bw = 0;
    // lv_fs_file_t fp;
    // lv_fs_open(&fp, storePath, LV_FS_MODE_WR);
    // lv_fs_seek(&fp, 0, LV_FS_SEEK_SET);

    // lv_fs_write(&fp, &resizeImgDsc.header, sizeof(lv_img_header_t), &bw);
    // lv_fs_write(&fp, imageData, resizeImgDsc.data_size, &bw);

    // lv_fs_close(&fp);

    // scheduleManagerInfoUpdated = true;

    // lv_mem_free(imageData);

    // //for MainMenu
    // lv_memset_00(&resizeImgDsc, sizeof(lv_img_dsc_t));

    // imageData = lv_gx_pixelmap_resize(snap.getCachedDsc(), &resizeImgDsc, 100, 40);

    // char storePathMain[IMAGE_CACHE_STORE_PATH_SIZE];
    // sprintf(storePathMain, "%s/%s_%d_%d_M.bin", SCHEDULE_IMAGE_CACHE_STORE_DIR, imageScheduleString, weekindex, dayindex);
    // qw_f_unlink(storePathMain);

    // if (storeSize < SCHEDULE_IMAGE_CACHE_MAX_SIZE)
    // {
    //     lv_memcpy(ScheduleImageCahceGroup.itemGroup[ScheduleImageCahceGroup.storeSize - 1].StoreMainPath, storePathMain, strlen(storePathMain) + 1);
    // }

    // lv_fs_open(&fp, storePathMain, LV_FS_MODE_WR);
    // lv_fs_seek(&fp, 0, LV_FS_SEEK_SET);

    // lv_fs_write(&fp, &resizeImgDsc.header, sizeof(lv_img_header_t), &bw);
    // lv_fs_write(&fp, imageData, resizeImgDsc.data_size, &bw);

    // lv_fs_close(&fp);

    // scheduleManagerInfoUpdated = true;

    // lv_mem_free(imageData);
    // scheduleImageCacheStoreUpdate();

    return false;
}

void deleteScheduleImageCache()
{
    // The method is an intentionally-blank override.
}

static void schedule_preview_draw_canvas(const void* map)
{
    // workout_preview_draw(0, 0, 260, 65, s_pWktHandle);
}

void SchedulePreviewFileImageCacheCreate()
{
    // scheduleImageCacheStoreReset();

    // Container m_CanvasContainer;
    // QwCanvasWidget Canvas;

    // lv_gui_map_t* map_obj = (lv_gui_map_t*)Canvas.getLvMapObj();
    // lv_gui_map_init_user_data(map_obj, 0, 0, NULL);

    // m_CanvasContainer.add(Canvas);

    // for (int weekindex = 0; weekindex < 7; weekindex++)
    // {
    //     uint8_t curday_course_cnt = get_courses_cnt_by_index(weekindex);
    //     if(curday_course_cnt > 0)
    //     {
    //         for (int dayindex = 0; dayindex < curday_course_cnt; dayindex++)
    //         {
    //             s_pWktHandle = (hWorkout)get_schedule_course_info((uint8_t)weekindex, dayindex);
    //             if (s_pWktHandle != nullptr && !workout_is_ftp_test(s_pWktHandle))
    //             {
    //                 lv_gui_map_set_draw(map_obj, schedule_preview_draw_canvas);
    //                 m_CanvasContainer.setPosition(0, 0, 304, 65);
    //                 Canvas.setPosition(m_CanvasContainer);
    //                 setScheduleImageCache(&m_CanvasContainer, weekindex, dayindex);
    //             }
    //         }
    //     }
    // }

    // gui_command_submit(enumGUICMD_RESET_SCREEN, NULL);
}

void SchedulePreviewFileUpdate(void)
{
    // gui_preload_submit(enumGUICMD_SCHEDULE_PREVIEW_UPDATA_PRELOAD, nullptr);
}
