/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   activity_fit_app.c
@Time    :   2024/12/10 10:24:51
<AUTHOR>   lxin
*
**************************************************************************/
#include "activity_fit_app.h"
#include "activity_fit_lap.h"
#include "activity_fit_record.h"
#include "activity_fit_session.h"
#include "activity_fit_simulator.h"
#include "activity_fit_workout_analysis.h"
#include "auto_wheel_perimeter_srv.h"
#include "backlight_module/backlight_module.h"
#include "cfg_header_def.h"
#include "file_manage.h"
#include "gui_event_service.h"
#include "pm_manager.h"
#include "poweroff_ctrl.h"
#include "qw_data_type.h"
#include "qw_fit_api.h"
#include "qw_time_service.h"
#include "qw_timer.h"
#include "service_datetime.h"
#include "subscribe_data.h"
#include "subscribe_data_protocol.h"
#include "system_utils.h"
#include "thread_pool.h"
#ifndef SIMULATOR
#include "../sport_ability/service_sport_ability.h"
#include "../sport_ability/service_sport_ability_sync.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_task.h"
#include "dev_status_report.h"
#include "pm.h"
#include "qw_time_util.h"
#include "subscribe_service.h"
#include "qw_sys_trace.h"
#endif
#include "focus_mode_srv/focus_mode_srv.h"
#include "igs_dev_config.h"
#include "map_input.h"
#include "message_service.h"
#include "navi_canvas.h"
#include "navi_port.h"
#include "remind_response_app/metronome_remind_app.h"
#include "sport_end_msg_app/sport_end_msg_app.h"
#include "track_ride_port.h"
#include "workout_interface.h"
#include "power_save_srv/power_save_srv.h"

#define ACTIVITY_FRAME_TICK 1000     // tick刷新周期
#define FIT_MSG_EVENT       0x1000   // fit消息事件base
#define FIT_MSG_QUEUE_SIZE  5        // fit消息队列大小
#define FIT_BAK_RECORD_COUNT  60     // 每60个record备份一次

// #define ACTIVITY_SAVE_SIM_FIT_FRAME

/**
 * @brief 活动文件记录状态
 */
typedef enum {
    SAVE_FLAG_INIT = 0,                                 // 未开始记录
    SAVE_FLAG_FIT = 0x01,                               // 完成fit数据结算
    SAVE_FLAG_WKT = (SAVE_FLAG_FIT << 1),               // 完成Gomore结算

    SAVE_FLAG_DEAL = (SAVE_FLAG_WKT + SAVE_FLAG_FIT),   // 执行保存
} ACTIVITY_SAVE_FLAG;

/**
 * @brief 活动文件记录种类
 */
typedef enum {
    SAVE_TYPE_NORMAL,     // 正常 预览+保存
    SAVE_TYPE_DEL,        // 删除 预览+不保存
    SAVE_TYPE_POWEROFF,   // 断电 不预览+保存
} ACTIVITY_SAVE_TYPE;

static saving_status_e g_fit_app_status = enum_status_free;
static algo_sports_ctrl_t s_sports_ctrl = {0};
static algo_sports_ctrl_t s_sports_ctrl_null = {0};
static algo_auto_sports_ctrl_t s_auto_sports_ctrl = {0};
static FIT_SPORTS g_activity_sports = FIT_SPORTS_MAX;   // 运动类型
static bool g_lap_reset_trigger = false;
static uint8_t g_save_flag = SAVE_FLAG_INIT;
static ACTIVITY_SAVE_TYPE g_save_type = SAVE_TYPE_NORMAL;
static osThreadId_t fit_thread = NULL;
static os_QueueHandle fit_msg_queue = NULL;
static rt_sem_t s_fit_sync;
static rt_sem_t s_fit_evt_lock;
static uint32_t g_fit_record_count = 0;

static uint16_t s_last_num_laps = 0;
static uint16_t s_last_num_lengths = 0;
static uint16_t s_last_num_idle_lengths_fit = 0; // fit上次空闲length数目

static qw_timer timer_fit_sync = {0};   // 定时器

/// @brief 内部操作事件枚举
typedef enum {
    SAVE_EVT_INVALID,         // 初始值, 无效值
    SAVE_EVT_CREATE,          // 创建记录
    SAVE_EVT_STOP,            // 停止记录
    SAVE_EVT_STOP_DEL,        // 停止记录, 不保存文件
    SAVE_EVT_STOP_DIRECTLY,   // 停止记录, 不解析文件, 在异常状态下使用(例如直接关机或升级时)
    SAVE_EVT_AUTO_PAUSE,      // 自动暂停
    SAVE_EVT_PAUSE,           // 手动暂停
    SAVE_EVT_RESUME,          // 恢复记录
    SAVE_EVT_LAP,             // 记圈打点
    SAVE_EVT_WKT,             // Gomore结算完成 与STOP事件合并后正式存储文件
    SAVE_EVT_FIT_BAK,         // FIT备份

} ACTIVITY_SAVE_EVT;

/**
 * @brief 发布运动状态
 * @param[ctrl_type_e] type 事件类型
 * @param[saving_status_e] status 运动状态
 */
static void publish_sports_status(ctrl_type_e type, evt_lap_type_e lap_type, saving_status_e status)
{
#ifndef SIMULATOR
    ACTIVITY_LOG_D("[activity_fit_app] @%s@ type = %d status = %d", __FUNCTION__, type, status);
    algo_sports_ctrl_t *p_ctrl = NULL;

    if (enum_ctrl_null == type)
    {
        s_sports_ctrl_null.ctrl_type = type;
        s_sports_ctrl_null.lap_type = lap_type;
        s_sports_ctrl_null.saving_status = status;
        s_sports_ctrl_null.sports_type = g_activity_sports;
        p_ctrl = (algo_sports_ctrl_t *) &s_sports_ctrl_null;
    }
    else
    {
        s_sports_ctrl.ctrl_type = type;
        s_sports_ctrl.lap_type = lap_type;
        s_sports_ctrl.saving_status = status;
        s_sports_ctrl.sports_type = g_activity_sports;
        p_ctrl = (algo_sports_ctrl_t *) &s_sports_ctrl;
    }

    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_EVENT_SPORTS_CTRL, p_ctrl, sizeof(algo_sports_ctrl_t)) != ERRO_CODE_OK)
    {
        ACTIVITY_LOG_E("[activity_fit_app] @%s@ sports_status publish error", __FUNCTION__);
    }
#endif
}

static void fit_lap_length_deal()
{
    bool auto_lap_enable = get_auto_record_lap(get_current_sport_mode());                                      // 是否启用自动记圈

#ifdef ACTIVITY_SAVE_SIM_FIT_FRAME
    FIT_LAP_MESG *p_lap = get_simulator_lap();
#else
    FIT_LAP_MESG *p_lap = get_lap();
#endif
    FIT_LENGTH_MESG *p_length = get_length();
    FIT_LENGTH_MESG *p_idle_length = get_idle_length();
    FIT_LAP_MESG *p_idle_lap = get_idle_lap();
    FIT_SESSION_MESG *p_session = get_session();

    // 空闲趟：有新的空闲length需要写入
    if (s_last_num_idle_lengths_fit != get_last_num_idle_lengths())
    {
        // 写入空闲趟和空闲圈
        fit_save_length(p_idle_length); // 空闲趟
        s_last_num_idle_lengths_fit = get_last_num_idle_lengths(); // 更新上次空闲length数目

        if (auto_lap_enable)
        {
            fit_dyn_data_sync(DEF_LAP, (const FIT_UINT8 *)p_idle_lap);        //动态消息同步
            fit_save_lap(fit_dyn_data_get(DEF_LAP));                       //空闲圈
            s_last_num_laps = p_session->num_laps;
        }
    }

    // 活动趟：有新趟需要写入
    if (s_last_num_lengths != p_session->num_lengths)
    {
        fit_save_length(p_length);   //记录活动趟
        s_last_num_lengths = p_session->num_lengths;
    }

    //Lap
    if (s_last_num_laps != p_session->num_laps)
    {
        fit_dyn_data_sync(DEF_LAP, (const FIT_UINT8 *)p_lap);        //动态消息同步
        fit_save_lap(fit_dyn_data_get(DEF_LAP));                       // 记录lap
        s_last_num_laps = p_session->num_laps;
    }
}

/**
 * @brief 存储文件
 * @param type ACTIVITY_SAVE_TYPE
 */
static void fit_save_deal(ACTIVITY_SAVE_TYPE type)
{
    ACTIVITY_LOG_E("[BUG:15205] fit_save_deal start g_fit_app_status: %d", g_fit_app_status);

    if (g_fit_app_status == enum_status_free)
    {
        return;
    }

    char fit_path[100] = {0};
    char fit_name[30] = {0};
    fit_brief_t brief = {0};

#ifdef ACTIVITY_SAVE_SIM_FIT_FRAME
    fit_dyn_data_sync(DEF_SESSION, (const FIT_UINT8 *) get_simulator_session());
#else
    fit_dyn_data_sync(DEF_SESSION, (const FIT_UINT8 *) get_session());
#endif
    fit_dyn_data_sync(DEF_SESSION_EXTEND, (const FIT_UINT8 *) get_session_extend());
    fit_save_workout_analysis(get_workout_analysis());   // 保存运动分析
    fit_save_end_activity(fit_dyn_data_get(DEF_SESSION), fit_dyn_data_get(DEF_SESSION_EXTEND), &brief);
    fit_dyn_data_uninit();

#if __NAVI
    set_navi_last_position_update(); // 更新导航最后位置
#endif

    if (type == SAVE_TYPE_NORMAL)   // 正常存储 只有正常存储的时候才解析文件
    {
        // 解析当前文件到内存
        service_fitsec_2_filename(fit_save_start_time_get(), fit_name);
        sprintf(fit_path, "%s%s", ACTIVITY_PATH, fit_name);

        ACTIVITY_LOG_D("[activity_fit_app] @%s@ fit_load start = %s", __FUNCTION__, fit_path);
        fit_load(fit_path, NULL);
        ACTIVITY_LOG_D("[activity_fit_app] @%s@ fit_load over", __FUNCTION__);
    }

    if (type == SAVE_TYPE_DEL || is_fit_session_invalid())
    {
        fit_activity_delete_file(fit_save_start_time_get());
    }
    else
    {
#ifndef SIMULATOR
        service_sport_ability_save_event(); // 保存运动能力
#endif

        // 添加到文件夹
        fit_activity_add_file(&brief);

        if (type == SAVE_TYPE_NORMAL)
        {
            sport_end_msg_app();    // 上报运动结束弹窗内容
        }
#ifndef SIMULATOR
        sport_status_change_notice(enum_status_free);   // 上报运动结束且文件保存成功 只有文件添加才进行上报
#endif
    }

    fit_save_bak_delete();  // 删除备份文件

    g_fit_record_count = 0;
    g_fit_app_status = enum_status_free;
    ACTIVITY_LOG_I("[BUG:15205] fit_save_deal over g_fit_app_status: %d", g_fit_app_status);

    if (type == SAVE_TYPE_POWEROFF)
    {
        poweroff_ctrl_unlock(BLOCK_APP_SPORT);
    }
    else
    {
        // 存储完成之后激活一下亮屏 激活gui线程切换到结算页面
        backlight_open_app();
    }
}

/// @brief 长任务事件处理, 用于写入文件
/// @param task_info 长任务信息
/// @return 无意义
static bool activity_save_deal(ACTIVITY_SAVE_EVT evt, uint32_t arg)
{
    // 处理传入事件
    switch (evt)
    {
    case SAVE_EVT_CREATE:
        if (g_fit_app_status == enum_status_ready)
        {
            fit_dyn_data_init(g_activity_sports);

            DEV_INFO info;
            info.serial_number_h = get_serial_number_high();
            info.serial_number = get_serial_number_low();
            info.manufacturer = get_manufacturer_id();
            info.product = get_product_id();
            info.hardware_version = get_hardware_version();
            info.software_version = get_major_app_version();
            fit_save_create_activity(g_activity_sports, service_datetime_get_fit_time(), &info);
#if __NAVI
            track_ride_start();
#endif   //__NAVI
            g_fit_record_count = 0;
            g_fit_app_status = enum_status_saving;
        }
        break;
    case SAVE_EVT_STOP:
        ACTIVITY_LOG_I("[BUG:15205] activity_save_deal SAVE_EVT_STOP status:%d g_save_flag:%d", g_fit_app_status, g_save_flag);
        if (g_fit_app_status > enum_status_ready)
        {
            g_save_flag |= SAVE_FLAG_FIT;
            g_save_type = SAVE_TYPE_NORMAL;

            if (g_save_flag == SAVE_FLAG_DEAL)
            {
                fit_save_deal(g_save_type);
                g_save_flag = SAVE_FLAG_INIT;
            }
#if __NAVI
            track_ride_end();
#endif   //__NAVI
        }
        break;
    case SAVE_EVT_STOP_DEL:
        ACTIVITY_LOG_I("[BUG:15205] activity_save_deal SAVE_EVT_STOP_DEL status:%d g_save_flag:%d", g_fit_app_status, g_save_flag);
        if (g_fit_app_status > enum_status_ready)
        {
            // 不保存, 直接写结束信息等待删除
            g_save_type = SAVE_TYPE_DEL;
            fit_save_deal(g_save_type);
            g_save_flag = SAVE_FLAG_INIT;

#if __NAVI
            track_ride_end();
#endif   //__NAVI
        }
        break;
    case SAVE_EVT_STOP_DIRECTLY:
        ACTIVITY_LOG_I("[BUG:15205] activity_save_deal SAVE_EVT_STOP_DIRECTLY status:%d g_save_flag:%d", g_fit_app_status, g_save_flag);
        if (g_fit_app_status > enum_status_ready)
        {
            g_save_flag |= SAVE_FLAG_FIT;
            g_save_type = SAVE_TYPE_POWEROFF;

            if (g_save_flag == SAVE_FLAG_DEAL)
            {
                fit_save_deal(g_save_type);
                g_save_flag = SAVE_FLAG_INIT;
            }
#if __NAVI
            track_ride_end();
#endif   //__NAVI
        }
        break;
    case SAVE_EVT_PAUSE:
        if (g_fit_app_status == enum_status_saving || g_fit_app_status == enum_status_pause_auto)
        {
            if (g_fit_app_status == enum_status_saving)
            {
                // 仅在未暂停时写入fit事件, 自动暂停切过来的情况下不用重复写入
                fit_save_event(FIT_SAVE_PAUSE, service_datetime_get_fit_time());
            }
#if __NAVI
            track_ride_pause();
#endif   //__NAVI
            g_fit_app_status = enum_status_pause_manul;
        }
        break;
    case SAVE_EVT_AUTO_PAUSE:
        if (g_fit_app_status == enum_status_saving)
        {
            fit_save_event(FIT_SAVE_PAUSE, service_datetime_get_fit_time());
#if __NAVI
            track_ride_pause();
#endif   //__NAVI
            g_fit_app_status = enum_status_pause_auto;
        }
        break;
    case SAVE_EVT_RESUME:
        if (g_fit_app_status >= enum_status_pause_auto)
        {
            fit_save_event(FIT_SAVE_RESUME, service_datetime_get_fit_time());
#if __NAVI
            track_ride_resume();
#endif   //__NAVI
            g_fit_app_status = enum_status_saving;
        }
        break;
    case SAVE_EVT_LAP:
        if (g_fit_app_status >= enum_status_saving || ACTIVITY_HITLAP_STOP == arg)   //结束时最后一圈在暂停状态
        {
#ifdef ACTIVITY_SAVE_SIM_FIT_FRAME
            FIT_LAP_MESG *p_lap = get_simulator_lap();
#else
            FIT_LAP_MESG *p_lap = get_lap();
#endif

            if (FIT_SPORTS_POOL_SWIMMING != g_activity_sports)
            {
                switch (arg)
                {
                case ACTIVITY_HITLAP_TIME:
                    p_lap->lap_trigger = FIT_LAP_TRIGGER_TIME;
                    break;
                case ACTIVITY_HITLAP_DISTANCE:
                case ACTIVITY_HITLAP_COUNT:
                    p_lap->lap_trigger = FIT_LAP_TRIGGER_DISTANCE;
                    break;
                case ACTIVITY_HITLAP_POSITION:
                    p_lap->lap_trigger = FIT_LAP_TRIGGER_POSITION_MARKED;
                    break;
                case ACTIVITY_HITLAP_STOP:
                    p_lap->lap_trigger = FIT_LAP_TRIGGER_SESSION_END;
                    break;
                case ACTIVITY_HITLAP_CALORIES:
                    p_lap->lap_trigger = FIT_LAP_TRIGGER_CALORIES;
                    break;
                default:
                    p_lap->lap_trigger = FIT_LAP_TRIGGER_MANUAL;
                    break;
                }
                fit_dyn_data_sync(DEF_LAP, (const FIT_UINT8 *) p_lap);
                fit_save_lap(fit_dyn_data_get(DEF_LAP));

                sports_lap_data_save_one(g_activity_sports, (const FIT_UINT8 *) p_lap);
            }
            else //泳池游泳
            {
                fit_lap_length_deal();
            }
        }
        break;
    case SAVE_EVT_WKT:
        ACTIVITY_LOG_I("[BUG:15205] activity_save_deal SAVE_EVT_WKT status:%d g_save_flag:%d", g_fit_app_status, g_save_flag);
        if (g_fit_app_status > enum_status_ready)
        {
            g_save_flag |= SAVE_FLAG_WKT;

            if (g_save_flag == SAVE_FLAG_DEAL)
            {
                fit_save_deal(g_save_type);
                g_save_flag = SAVE_FLAG_INIT;
            }
        }
        break;

    case SAVE_EVT_FIT_BAK:
        ACTIVITY_LOG_I("activity_save_deal SAVE_EVT_FIT_BAK status:%d g_save_flag:%d", g_fit_app_status, g_save_flag);
        if (g_fit_app_status > enum_status_ready)
        {
#ifdef ACTIVITY_SAVE_SIM_FIT_FRAME
            fit_dyn_data_sync(DEF_SESSION, (const FIT_UINT8 *) get_simulator_session());
            fit_dyn_data_sync(DEF_LAP, (const FIT_UINT8 *) get_simulator_lap());
#else
            fit_dyn_data_sync(DEF_SESSION, (const FIT_UINT8 *) get_session());
            fit_dyn_data_sync(DEF_LAP, (const FIT_UINT8 *) get_fake_lap());
#endif
            fit_dyn_data_sync(DEF_SESSION_EXTEND, (const FIT_UINT8 *) get_session_extend());

            fit_save_bak_end(fit_dyn_data_get(DEF_SESSION), fit_dyn_data_get(DEF_SESSION_EXTEND), fit_dyn_data_get(DEF_LAP));
        }
        break;

    default:
        break;
    }

    rt_sem_release(s_fit_evt_lock);

    return true;
}

static void send_activity_msg(ACTIVITY_SAVE_EVT evt, int32_t data)
{
    EventData *eventData = message_allocEventData();
    message_eventDataInit(eventData, evt, data, NULL);

    system_send_event(fit_thread, FIT_MSG_EVENT, eventData);
}

#if __NAVI
static void navi_input(void)
{
    map_input_data_t *input_data = get_map_input();
    map_input_gps_t *p_navi_gps = get_navi_gps();
    FIT_RECORD_MESG *p_record = get_record();
    struct minmea_float longitude = {0x7fffffff, 1};
    struct minmea_float latitude = {0x7fffffff, 1};
    int32_t altitude = 0x7fffffff;
    static struct minmea_float last_latitude = {0x7fffffff, 1}, last_longitude = {0x7fffffff, 1};
    float d2, bearing;

    //获取经纬度和高度
    util_convert_semicircles_2_gps_pos(&p_record->position_lat, &p_record->position_long, &latitude, &longitude);
    altitude = UTIL_FitAlt2Altitude_100(p_record->altitude);

    const navi_calc_input_t c_navi_calc_input = {
        .longitude = {
            .value = longitude.value,
            .scale = longitude.scale,
        },
        .latitude = {
            .value = latitude.value,
            .scale = latitude.scale,
        },
    };

    const track_ride_input_t c_track_ride_input = {
        .longitude = {
            .value = longitude.value,
            .scale = longitude.scale,
        },
        .latitude = {
            .value = latitude.value,
            .scale = latitude.scale,
        },
        .altitude = altitude,
    };

    if (false == navi_route_sim_get())
    {
        navi_calc_input(&c_navi_calc_input);
        track_ride_input(&c_track_ride_input);

        //Calc course.
        if (position_invalid_check(&last_latitude) || position_invalid_check(&last_longitude))
        {
            memcpy(&last_latitude, &latitude, sizeof(struct minmea_float));
            memcpy(&last_longitude, &longitude, sizeof(struct minmea_float));
        }
        else if (!position_invalid_check(&latitude) && !position_invalid_check(&longitude)
                 && (latitude.value != last_latitude.value || longitude.value != last_longitude.value))
        {
            util_pos_simple_distance_get(&d2, &latitude, &longitude, &last_latitude, &last_longitude);

            if (WAYPOINT_MIN_INTERVAL_COURSE < d2)                                                        //Calc course if dist interval achieved.
            {
                util_pos_bearing_get(&bearing, &last_latitude, &last_longitude, &latitude, &longitude);   //两点的方位角
                input_data->course = ((int32_t) bearing + 720) % 360;
                p_navi_gps->course = ((int32_t) bearing + 720) % 360;
                memcpy(&last_latitude, &latitude, sizeof(struct minmea_float));
                memcpy(&last_longitude, &longitude, sizeof(struct minmea_float));
            }
        }

        // input_data->cur_page = get_cur_page_is_map();  //仅区分地图页和爬坡地图页
        input_data->status = point_valid_check(p_record->position_lat, p_record->position_long);
        // input_data->course = p_calc_data->algorithm.ride_course;
        input_data->gps_lat.value = latitude.value;
        input_data->gps_lat.scale = latitude.scale;
        input_data->gps_lon.value = longitude.value;
        input_data->gps_lon.scale = longitude.scale;
    }

    input_data->cur_page = 1;   //仅区分地图页和爬坡地图页
    input_data->saved_lat = get_navi_last_lat();
    input_data->saved_lon = get_navi_last_lon();
    input_data->runtime_ms = get_boot_msec();
    input_data->map_origin.x = 0;
    input_data->map_origin.y = 0;
    input_data->map_rang_x = 466;
    input_data->map_rang_y = 349;
    input_data->on_save = true;
    input_data->rotate = get_navi_map_dir();
    input_data->turning_indicator = get_navi_turn_tip();
    input_data->auto_zoom = get_navi_auto_zoom();
    input_data->meter_page = true;
    input_data->unit_style_dist = get_unit_distance();
    input_data->unit_style_alt = get_unit_height();
#ifdef IGS_DEV
    // input_data->has_radar = sensor_is_connect(SENSOR_TYPE_RADAR);
#else
    // input_data->has_radar = false;
#endif
    // if (input_data->touch_map)
    // {
    //     input_data->route_elevation = false;
    // }
    // else
    {
        input_data->route_elevation = get_navi_show_high();
    }
    // sprintf(input_data->map_dir, "%s", MAPS_DIR);
    sprintf(input_data->route_dir, "%s", g_fileDirName[6]);
}

#ifndef SIMULATOR
static void navi_pub(void)
{
    static algo_navi_pub_t navi_pub;
    navi_data_t *p_navi_data = get_navi_data();
    const navi_progress_t *progress = navi_progress_get();
    FIT_SESSION_MESG *p_session = get_session();

    if (false == p_navi_data->with_route || NULL == progress)
    {
        memset(&navi_pub, 0xff, sizeof(algo_navi_pub_t));
    }
    else
    {
        navi_pub.dest_dist = (uint32_t) (progress->dist2dest * 100);
        navi_pub.remain_ascent = (uint32_t) (progress->climb.h_residual * 100);
        navi_pub.dest_time = 0;   //TODO

        if ((enumNAVI_STATUS_NORMAL == progress->status || enumNAVI_STATUS_OFF_COURSE == progress->status) && 0xffffffff != p_session->enhanced_avg_speed
            && 0 != p_session->enhanced_avg_speed)
        {
            navi_pub.dest_time = navi_pub.dest_dist * 10 / p_session->enhanced_avg_speed;
        }
        else
        {
            navi_pub.dest_time = 0xffffffff;
        }
    }

    qw_dataserver_publish_id(DATA_ID_EVENT_NAVI, (const void *) &navi_pub, sizeof(navi_pub));
}
#endif   //SIMULATOR

#endif   //__NAVI

static void activity_fit_signal_sync(void)
{
    rt_sem_release(s_fit_sync);
}

static void activity_fit_wait_sync(void)
{
    rt_sem_take(s_fit_sync, RT_WAITING_FOREVER);
}

static void activity_fit_sport_tick(void)
{
    // 广播运动算法tick
    if (g_fit_app_status > enum_status_ready)
    {
        publish_sports_status(enum_ctrl_null, enum_lap_null, g_fit_app_status);
    }

    // fit record打点
    if (g_fit_app_status == enum_status_saving)
    {
        // 处理模拟器
        activity_simulator_deal();

        // 处理lap length
        fit_lap_length_deal();

        // 同步打点数据到动态帧
#ifdef ACTIVITY_SAVE_SIM_FIT_FRAME
        fit_dyn_data_sync(DEF_RECORD, (const FIT_UINT8 *) get_simulator_record());
#else
        fit_dyn_data_sync(DEF_RECORD, (const FIT_UINT8 *) get_record());
#endif
        // 保存打点帧
        if (fit_save_record(fit_dyn_data_get(DEF_RECORD)) == FIT_OK)
        {
            g_fit_record_count++;
            if (g_fit_record_count > FIT_BAK_RECORD_COUNT)
            {
                g_fit_record_count = 0;
                send_activity_msg(SAVE_EVT_FIT_BAK, 0); // 保存备份
            }
        }

        // 处理训练数据
        workout_input();                            // 训练数据输入
        workout_input_cp20m(get_session_cp20m());   // 训练数据cp20m输入

                                                    // 处理导航数据
#if __NAVI
        navi_input();          // 导航数据输入
        navi_gps_lock();       //GPS数据锁定
        navi_route_update();   // 导航路线更新
        track_ride_update();   // 轨迹更新
        navi_info_update();    // 导航信息更新
#ifndef SIMULATOR
        navi_pub();            // 导航数据发布
#endif
#endif

        // 处理Gomore训练分析数据
        activity_workout_analysis_feed();
    }
}

/**
 * @brief record tick
 * @param[void *] p_context 计时器上下文
 */
static void activity_fit_app_tick(void *p_context)
{
    activity_fit_signal_sync();   // 同步信号
}

static bool msg_fit_event_handle(EventData *eventData, void *data)
{
    activity_save_deal((ACTIVITY_SAVE_EVT) eventData->arg1, eventData->arg2);
    return true;
}

static void fit_task_process(void *parameter)
{
    fit_msg_queue = user_msg_queue_create(FIT_MSG_QUEUE_SIZE);
    message_registerTask(fit_thread, fit_msg_queue);
    system_bindlistener(fit_thread, FIT_MSG_EVENT, msg_fit_event_handle, NULL);

    while (1)
    {
        if (g_fit_app_status > enum_status_ready)
        {
            int64_t last_sync_tick_ms = get_boot_msec();

            activity_fit_sport_tick();
            submit_gui_event(GUI_EVT_SERVICE_AOD_TIMEOUT, 0, NULL);
            message_GetAndDispatchedEvent(fit_msg_queue, 0, 6);

            // 处理定时器 定到rtc时间准秒
            int64_t cur_tick_ms = get_boot_msec();
            if (cur_tick_ms >= last_sync_tick_ms && cur_tick_ms - last_sync_tick_ms < ACTIVITY_FRAME_TICK)
            {
                qw_tick_t timer_offset = (last_sync_tick_ms / ACTIVITY_FRAME_TICK + 1) * ACTIVITY_FRAME_TICK - last_sync_tick_ms;
                qw_timer_start(&timer_fit_sync, timer_offset, NULL, "act_fit_app");   // 启动定时器

                activity_fit_wait_sync();                                             // 等待同步
            }
            else
            {
                // >= 1000ms 或 tick_ms翻转了
                // 立刻开始下个循环
            }
        }
        else
        {
            qw_timer_stop(&timer_fit_sync);
            message_GetAndDispatchedEvent(fit_msg_queue, osWaitForever, 1);
        }
    }
}

// 自动运动事件回调
static void algo_auto_sports_ctrl_callback(const void *in, uint32_t len)
{
    algo_auto_sports_ctrl_t *auto_sports_ctrl = (algo_auto_sports_ctrl_t *) in;

    if (NULL == in)
    {
        return;
    }

    s_auto_sports_ctrl.ctrl_type = auto_sports_ctrl->ctrl_type;
    s_auto_sports_ctrl.lap_type = auto_sports_ctrl->lap_type;

    if (enum_ctrl_pause_auto == s_auto_sports_ctrl.ctrl_type)
    {
        activity_pause(true);
    }
    else if (enum_ctrl_resume == s_auto_sports_ctrl.ctrl_type)
    {
        activity_resume();
    }
    else if (enum_ctrl_lap == s_auto_sports_ctrl.ctrl_type)
    {
        if ((FIT_SPORTS) get_current_sport_mode() == FIT_SPORTS_JUMP_ROPE || (FIT_SPORTS) get_current_sport_mode() == FIT_SPORTS_STRENGTH_TRAINING
            || (FIT_SPORTS) get_current_sport_mode() == FIT_SPORTS_ROWING_MACHINE)
        {
            if (enum_lap_count == s_auto_sports_ctrl.lap_type)
            {
                activity_hitlap(ACTIVITY_HITLAP_COUNT);
                remind_trigger(enumPOPUP_SPORTING_LAP, true);
            }
            else
            {
                activity_hitlap(ACTIVITY_HITLAP_TIME);
                remind_trigger(enumPOPUP_SPORTING_LAP, true);
            }
        }
        else
        {
            if (enum_lap_distance == s_auto_sports_ctrl.lap_type)
            {
                activity_hitlap(ACTIVITY_HITLAP_DISTANCE);
                submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_SPORTING_LAP, NULL);
            }
            else if (enum_lap_time == s_auto_sports_ctrl.lap_type)
            {
                activity_hitlap(ACTIVITY_HITLAP_TIME);
                submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_SPORTING_LAP, NULL);
            }
            else if (enum_lap_position == s_auto_sports_ctrl.lap_type)
            {
                activity_hitlap(ACTIVITY_HITLAP_POSITION);
                submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_SPORTING_LAP, NULL);
            }
            else if (enum_lap_count == s_auto_sports_ctrl.lap_type)
            {
                activity_hitlap(ACTIVITY_HITLAP_COUNT);
                submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_SPORTING_LAP, NULL);
            }
        }
    }
}

static void activity_auto_sports_ctrl_begin(void)
{
#ifndef SIMULATOR
    int32_t ret = -1;
    optional_config_t config = {.sampling_rate = 0};

    if (0 != (ret = qw_dataserver_subscribe_id(DATA_ID_ALGO_AUTO_SPORTS_CTRL, algo_auto_sports_ctrl_callback, &config)))   //订阅运动控制数据
    {
        ACTIVITY_LOG_E("%s AUTO_SPORTS_CTRL erro ret:%d", __FUNCTION__, ret);
    }
#endif
}

static void activity_auto_sports_ctrl_end(void)
{
#ifndef SIMULATOR
    int32_t ret = -1;

    if (0 != (ret = qw_dataserver_unsubscribe_id(DATA_ID_ALGO_AUTO_SPORTS_CTRL, algo_auto_sports_ctrl_callback)))
    {
        ACTIVITY_LOG_E("%s AUTO_SPORTS_CTRL erro ret:%d", __FUNCTION__, ret);
    }
#endif
}

// Gomore运动算法结算回调
static void algo_workout_analysis_save_callback(const void *in, uint32_t len)
{
    ACTIVITY_LOG_I("[BUG:15205] algo_workout_analysis_save_callback status:%d g_save_flag:%d", g_fit_app_status, g_save_flag);

    rt_sem_take(s_fit_evt_lock, RT_WAITING_FOREVER);
    lib_gm_summary_prepare_to_save();
    send_activity_msg(SAVE_EVT_WKT, 0);   // Gomore运动算法结算回调
}

static void activity_workout_analysis_save_evt(void)
{
#ifndef SIMULATOR
    int32_t ret = -1;
    optional_config_t config = {.sampling_rate = 0};

    if (0 != (ret = qw_dataserver_subscribe_id(DATA_ID_EVENT_FITNESS_SAVE_END, algo_workout_analysis_save_callback, &config)))   //订阅Gomore运动算法结算
    {
        ACTIVITY_LOG_E("%s FITNESS_SAVE_END erro ret:%d", __FUNCTION__, ret);
    }
#endif
}

static void activity_app_poweroff_callback(void)
{
    if (g_fit_app_status > enum_status_ready)
    {
        poweroff_ctrl_lock(BLOCK_APP_SPORT, 0);
    }

    stop_activity_record_directly();   //停止运动记录
}

///////////////////////////////////////////////////////////////

static const block_app_info s_poweroff_info = {
    .info = {
        .app = BLOCK_APP_SPORT,
        .type = BLOCK_TYPE_SOFT_BLOCK,
        .source = BLOCK_SOURCE_HCPU,
        .priority = 1,
        .reason_id = 0,
    },
    .reason = NULL,
    .reason_max_num = 0,
    .name = "fit_close_save",
    .prepare_cb = activity_app_poweroff_callback,
};

static void on_workout_step_complete(wktstep_duration_type_e duration_type)
{
    // 根据持续类型决定如何记圈
    ACTIVITY_HITLAP lap_type = ACTIVITY_HITLAP_MANUAL;
    
    // 根据持续类型选择不同的记圈方式
    // 注意：时间类型使用手动记圈，避免算法模块的时间修正逻辑
    switch (duration_type) {
        case enumDurationOfTime:
            // 时间类型的步骤使用手动记圈，避免触发算法模块的时间修正
            // 如果使用ACTIVITY_HITLAP_TIME，算法模块会将lap时间强制设置为
            // 自动记圈的预设值，而不是实际的运动时间
            lap_type = ACTIVITY_HITLAP_MANUAL;
            break;
        case enumDurationOfDistance:
            // 距离类型的步骤
            lap_type = ACTIVITY_HITLAP_DISTANCE;
            break;
        case enumDurationOfCalories:
            // 卡路里类型的步骤
            lap_type = ACTIVITY_HITLAP_CALORIES;
            break;
        default:
            lap_type = ACTIVITY_HITLAP_MANUAL;
            break;
    }

    // 执行记圈操作
    activity_hitlap(lap_type);
}

void activity_app_module_init()
{
    qw_timer_init(&timer_fit_sync, QW_TIMER_FLAG_ONE_SHOT | QW_TIMER_FLAG_SOFT_TIMER, activity_fit_app_tick);
    s_fit_sync = rt_sem_create("fitsync", 1, RT_IPC_FLAG_FIFO);
    s_fit_evt_lock = rt_sem_create("fitevt", 1, RT_IPC_FLAG_FIFO);

    poweroff_ctrl_bolck_register(&s_poweroff_info);

    activity_workout_analysis_save_evt();

    // 注册训练完成步骤回调函数
    workout_register_step_complete_callback(on_workout_step_complete);

    osThreadAttr_t task_attr = {0};
    task_attr.name = "activity";
    task_attr.priority = osPriorityNormal;
    task_attr.stack_size = 4096;

    fit_thread = osThreadCreate(fit_task_process, NULL, &task_attr);
    if (fit_thread != NULL)
    {
        osThreadStartUp(fit_thread);
    }
}

void ready_activity_record(FIT_SPORTS sport_type)
{
#ifndef SIMULATOR
    saving_status_e cur_status = g_fit_app_status;
#endif
    if (g_fit_app_status == enum_status_free)
    {
        // 初始化
        g_activity_sports = sport_type;
        g_lap_reset_trigger = false;
#ifndef SIMULATOR
        g_save_flag = SAVE_FLAG_INIT;
        QW_TRACE_APP_EVENT(TRACE_APP_TYPE_SPORT, TRACE_MODULE_START);
#else
        g_save_flag = SAVE_FLAG_DEAL;
#endif   // !SIMULATOR

        // 订阅数据
        activity_record_begin();                              // 记录record
        activity_lap_begin();                                 // 记圈数据计算
        activity_workout_analysis_begin(g_activity_sports);   // 训练分析
        activity_session_begin();                             // 统计数据计算
        activity_simulator_begin();                           // 模拟数据, 在 ROUTER_FIT_SIMULATOR_ENABLE 开启时有效
        auto_wheel_perimeter_detect_begin();                  // 自动检测轮径

        // 订阅自动运动控制
        activity_auto_sports_ctrl_begin();
        sports_lap_data_clear();   // 清除圈数据

        s_last_num_laps = 0;
        s_last_num_lengths = 0;
        s_last_num_idle_lengths_fit = 0;

        // 广播运动状态变更
#ifdef SIMULATOR
        set_simulator_sport_status(ACTIVITY_FIT_READY);
#else
        publish_sports_status(enum_ctrl_null, enum_lap_null, enum_status_ready);
#endif   // !SIMULATOR

        g_fit_app_status = enum_status_ready;
    }

#ifndef SIMULATOR
    ACTIVITY_LOG_D("[activity_fit_app] @%s@ g_fit_app_status %d >> %d", __FUNCTION__, cur_status, g_fit_app_status);
#endif
}

void start_activity_record()
{
#ifndef SIMULATOR
    saving_status_e cur_status = g_fit_app_status;
#endif
    rt_sem_take(s_fit_evt_lock, RT_WAITING_FOREVER);
    if (g_fit_app_status == enum_status_ready)
    {
        // 创建文件 写文件头 必须出现在record之前
        send_activity_msg(SAVE_EVT_CREATE, 0);

        // 广播运动状态变更
#ifdef SIMULATOR
        set_simulator_sport_status(ACTIVITY_FIT_RECORD);

        if (g_activity_sports == FIT_SPORTS_JUMP_ROPE || g_activity_sports == FIT_SPORTS_STRENGTH_TRAINING || g_activity_sports == FIT_SPORTS_ROWING_MACHINE
            || g_activity_sports == FIT_SPORTS_POOL_SWIMMING)
        {
            if (get_auto_record_lap(g_activity_sports))
            {
                // 记圈模式 模拟器模拟算法中打开记组页面
                submit_gui_event(GUI_EVT_SCROLL_TO_PAGE, GRAPH_CONST_PAGE_OTHER, NULL);
            }
        }
#else
        publish_sports_status(enum_ctrl_start, enum_lap_null, enum_status_saving);
#endif   // !SIMULATOR

        activity_workout_analysis_start(g_activity_sports);
    }
    else
    {
        rt_sem_release(s_fit_evt_lock);
    }

#ifndef SIMULATOR
    ACTIVITY_LOG_D("[activity_fit_app] @%s@ g_fit_app_status %d >> %d", __FUNCTION__, cur_status, g_fit_app_status);
#endif
}

void stop_activity_record(bool save)
{
    ACTIVITY_LOG_I("[BUG:15205] stop_activity_record status:%d g_save_flag:%d", g_fit_app_status, g_save_flag);
#ifndef SIMULATOR
    saving_status_e cur_status = g_fit_app_status;
#endif
    rt_sem_take(s_fit_evt_lock, RT_WAITING_FOREVER);
    if (g_fit_app_status != enum_status_free)
    {
        // 最后一圈数据要处理 相当于手动记了一圈
        activity_save_stop_lap();

        // 广播运动状态变更
#ifdef SIMULATOR
        set_simulator_sport_status(ACTIVITY_FIT_IDLE);
#else
        QW_TRACE_APP_EVENT(TRACE_APP_TYPE_SPORT, TRACE_MODULE_END);   // 运动结束事件
        publish_sports_status(enum_ctrl_stop, enum_lap_null, enum_status_free);
#endif   // !SIMULATOR

        if (save)
        {
            check_session_for_record();   // 更新个人纪录
        }
        activity_workout_analysis_end();     // 停止响应workout分析
        activity_auto_sports_ctrl_end();     // 停止自动运动控制
        activity_record_end();               // 停止记录record
        activity_lap_end();                  // 停止响应记圈
        activity_session_end();              // 结算统计数据
        activity_simulator_end();            // 模拟数据, 在 ROUTER_FIT_SIMULATOR_ENABLE 开启时有效
        auto_wheel_perimeter_detect_end();   // 自动检测轮径

        sports_lap_data_clear();             // 清除圈数据

        if (g_fit_app_status > enum_status_ready)
        {
            send_activity_msg((save ? SAVE_EVT_STOP : SAVE_EVT_STOP_DEL), 0);   // 处理保存数据
        }
        else
        {
            g_fit_app_status = enum_status_free;
            rt_sem_release(s_fit_evt_lock);
        }

        g_lap_reset_trigger = false;
        g_activity_sports = FIT_SPORTS_MAX;
    }
    else
    {
        rt_sem_release(s_fit_evt_lock);
    }
#ifndef SIMULATOR
    ACTIVITY_LOG_D("[activity_fit_app] @%s@ g_fit_app_status %d >> %d", __FUNCTION__, cur_status, g_fit_app_status);
#endif

    service_datetime_calib(); // 校时。结束运动时调用
}

void stop_activity_record_directly()
{
    ACTIVITY_LOG_I("[BUG:15205] stop_activity_record_directly status:%d g_save_flag:%d", g_fit_app_status, g_save_flag);
#ifndef SIMULATOR
    saving_status_e cur_status = g_fit_app_status;
#endif
    rt_sem_take(s_fit_evt_lock, RT_WAITING_FOREVER);
    if (g_fit_app_status != enum_status_free)
    {
        // 最后一圈数据要处理 相当于手动记了一圈
        activity_save_stop_lap();

        // 广播运动状态变更
#ifdef SIMULATOR
        set_simulator_sport_status(ACTIVITY_FIT_IDLE);
#else
        QW_TRACE_APP_EVENT(TRACE_APP_TYPE_SPORT, TRACE_MODULE_END);   // 运动结束事件
        publish_sports_status(enum_ctrl_stop, enum_lap_null, enum_status_free);
#endif                                       // !SIMULATOR

        check_session_for_record();          // 更新个人纪录

        activity_workout_analysis_end();     // 停止响应workout分析
        activity_auto_sports_ctrl_end();     // 停止自动运动控制
        activity_record_end();               // 停止记录record
        activity_lap_end();                  // 停止响应记圈
        activity_session_end();              // 结算统计数据
        activity_simulator_end();            // 模拟数据, 在 ROUTER_FIT_SIMULATOR_ENABLE 开启时有效
        auto_wheel_perimeter_detect_end();   // 自动检测轮径

        sports_lap_data_clear();             // 清除圈数据

        if (g_fit_app_status > enum_status_ready)
        {
            send_activity_msg(SAVE_EVT_STOP_DIRECTLY, 0);   // 处理保存数据
        }
        else
        {
            g_fit_app_status = enum_status_free;
            rt_sem_release(s_fit_evt_lock);
        }

        g_lap_reset_trigger = false;
        g_activity_sports = FIT_SPORTS_MAX;
    }
    else
    {
        rt_sem_release(s_fit_evt_lock);
    }

#ifndef SIMULATOR
    ACTIVITY_LOG_D("[activity_fit_app] @%s@ g_fit_app_status %d >> %d", __FUNCTION__, cur_status, g_fit_app_status);
#endif

    service_datetime_calib(); // 校时。结束运动时调用
}

void activity_pause(bool auto_pause)
{
#ifndef SIMULATOR
    saving_status_e cur_status = g_fit_app_status;
#endif
    rt_sem_take(s_fit_evt_lock, RT_WAITING_FOREVER);
    if (g_fit_app_status == enum_status_saving || g_fit_app_status == enum_status_pause_auto)
    {
        if (g_fit_app_status == enum_status_saving && auto_pause)
        {
            // 广播运动状态变更
#ifdef SIMULATOR
            set_simulator_sport_status(ACTIVITY_FIT_AUTOPAUSE);
#else
            publish_sports_status(enum_ctrl_pause_auto, enum_lap_null, enum_status_pause_auto);
#endif   // !SIMULATOR

            // 处理保存fit段
            send_activity_msg(SAVE_EVT_AUTO_PAUSE, 0);

            // 通知gomore暂停
            notify_gomore_sport_pause();
        }
        else if (!auto_pause)
        {
            // 广播运动状态变更
#ifdef SIMULATOR
            set_simulator_sport_status(ACTIVITY_FIT_PAUSE);
#else
            publish_sports_status(enum_ctrl_pause, enum_lap_null, enum_status_pause_manul);
#endif   // !SIMULATOR

            // 处理保存fit段
            send_activity_msg(SAVE_EVT_PAUSE, 0);

            // 通知gomore暂停
            notify_gomore_sport_pause();
        }
        else
        {
            rt_sem_release(s_fit_evt_lock);
        }
    }
    else
    {
        rt_sem_release(s_fit_evt_lock);
    }

#ifndef SIMULATOR
    ACTIVITY_LOG_D("[activity_fit_app] @%s@ g_fit_app_status %d >> %d", __FUNCTION__, cur_status, g_fit_app_status);
#endif
}

void activity_resume()
{
#ifndef SIMULATOR
    saving_status_e cur_status = g_fit_app_status;
#endif
    rt_sem_take(s_fit_evt_lock, RT_WAITING_FOREVER);
    if (g_fit_app_status >= enum_status_pause_auto)
    {
        // 广播运动状态变更
#ifdef SIMULATOR
        set_simulator_sport_status(ACTIVITY_FIT_RECORD);
#else
        publish_sports_status(enum_ctrl_resume, enum_lap_null, enum_status_saving);

        // 通知gomore手动继续 自动暂停不通知
        // if (g_fit_app_status == enum_status_pause_manul)
        {
            notify_gomore_sport_continue();
        }
#endif   // !SIMULATOR

        // 处理保存fit段
        send_activity_msg(SAVE_EVT_RESUME, 0);
    }
    else
    {
        rt_sem_release(s_fit_evt_lock);
    }

#ifndef SIMULATOR
    ACTIVITY_LOG_D("[activity_fit_app] @%s@ g_fit_app_status %d >> %d", __FUNCTION__, cur_status, g_fit_app_status);
#endif
}

void activity_hitlap(ACTIVITY_HITLAP type)
{
    rt_sem_take(s_fit_evt_lock, RT_WAITING_FOREVER);
    if (g_fit_app_status > enum_status_ready)
    {
        if (g_activity_sports == FIT_SPORTS_JUMP_ROPE || g_activity_sports == FIT_SPORTS_STRENGTH_TRAINING || g_activity_sports == FIT_SPORTS_ROWING_MACHINE
            || g_activity_sports == FIT_SPORTS_POOL_SWIMMING)
        {
            // 需要组间休息控制的运动类别 记圈信号与组间休息轮流切换
            if (!g_lap_reset_trigger)
            {
                if (ACTIVITY_HITLAP_TIME == type)
                {
                    publish_sports_status(enum_ctrl_lap, enum_lap_time, g_fit_app_status);   // 广播记圈事件

                    g_lap_reset_trigger = true;
                }
                else if (ACTIVITY_HITLAP_COUNT == type)
                {
                    publish_sports_status(enum_ctrl_lap, enum_lap_count, g_fit_app_status);   // 广播记圈事件

                    g_lap_reset_trigger = true;
                }
                else if (ACTIVITY_HITLAP_MANUAL == type)
                {
                    publish_sports_status(enum_ctrl_lap, enum_lap_manual, g_fit_app_status);   // 广播记圈事件

                    g_lap_reset_trigger = true;
                }

                rt_sem_release(s_fit_evt_lock);
#ifdef SIMULATOR
                submit_gui_event(GUI_EVT_RESET_PAGE, true, NULL);
#endif   // !SIMULATOR
            }
            else
            {
                if (ACTIVITY_HITLAP_MANUAL == type)
                {
                    publish_sports_status(enum_ctrl_rest_resume, enum_lap_manual, g_fit_app_status);   // 广播记圈事件

                    send_activity_msg(SAVE_EVT_LAP, type);                                             // 处理保存事件

                    g_lap_reset_trigger = false;

#ifdef SIMULATOR
                    submit_gui_event(GUI_EVT_RESET_PAGE, false, NULL);
#endif   // !SIMULATOR
                }
                else
                {
                    rt_sem_release(s_fit_evt_lock);
                }
            }
        }
        else
        {
            if (ACTIVITY_HITLAP_TIME == type)
            {
                publish_sports_status(enum_ctrl_lap, enum_lap_time, g_fit_app_status);   // 广播记圈事件
            }
            else if (ACTIVITY_HITLAP_DISTANCE == type)
            {
                publish_sports_status(enum_ctrl_lap, enum_lap_distance, g_fit_app_status);   // 广播记圈事件
            }
            else if (ACTIVITY_HITLAP_POSITION == type)
            {
                publish_sports_status(enum_ctrl_lap, enum_lap_position, g_fit_app_status);   // 广播记圈事件
            }
            else if (ACTIVITY_HITLAP_COUNT == type)
            {
                publish_sports_status(enum_ctrl_lap, enum_lap_count, g_fit_app_status);   // 广播记圈事件
            }
            else if (ACTIVITY_HITLAP_CALORIES == type)
            {
                publish_sports_status(enum_ctrl_lap, enum_lap_calories, g_fit_app_status);   // 广播记圈事件
            }
            else
            {
                publish_sports_status(enum_ctrl_lap, enum_lap_manual, g_fit_app_status);   // 广播记圈事件
            }

            send_activity_msg(SAVE_EVT_LAP, type);   // 处理保存事件
        }
    }
    else
    {
        rt_sem_release(s_fit_evt_lock);
    }
}

void activity_save_stop_lap()
{
    if (g_fit_app_status > enum_status_ready)
    {
        if (g_activity_sports == FIT_SPORTS_JUMP_ROPE || g_activity_sports == FIT_SPORTS_STRENGTH_TRAINING || g_activity_sports == FIT_SPORTS_ROWING_MACHINE
            || g_activity_sports == FIT_SPORTS_POOL_SWIMMING)
        {
            publish_sports_status(enum_ctrl_rest_resume, enum_lap_stop, g_fit_app_status);   // 广播记圈事件

            g_lap_reset_trigger = false;
        }
        else
        {
            publish_sports_status(enum_ctrl_lap, enum_lap_stop, g_fit_app_status);   // 广播记圈事件
        }

        send_activity_msg(SAVE_EVT_LAP, ACTIVITY_HITLAP_STOP);  // 处理保存事件
    }
}

FIT_SPORTS get_activity_sport_type()
{
    return g_activity_sports;
}

bool get_activity_is_rest_in_group()
{
    return g_lap_reset_trigger;
}

bool get_activity_is_saving()
{
    return (g_fit_app_status != enum_status_free);
}

/**
 * @brief 设置训练感受 在stop_activity_record之前调用
 * @return TRAINING_FEEL_TYPE
 */
void set_activity_training_feel(TRAINING_FEEL_TYPE feel)
{
    if (feel < TRAINING_FEEL_MAX)
    {
        FIT_SESSION_EXTEND_MESG *p_session_ex = get_session_extend();
        p_session_ex->training_perception = (FIT_UINT8) feel;
    }
}

#ifdef SIMULATOR

static ACTIVITY_FIT_STATUS g_simulator_sport_status = ACTIVITY_FIT_IDLE;
static uint8_t state_save_flag = 0;

/**
 * @brief 获取模拟器临时使用的运动状态
 * @return 运动状态
 */
ACTIVITY_FIT_STATUS get_simulator_sport_status()
{
    return g_simulator_sport_status;
}

/**
 * @brief 设置模拟器临时使用的运动状态
 * @param 运动状态
 */
void set_simulator_sport_status(ACTIVITY_FIT_STATUS status)
{
    if (status >= ACTIVITY_FIT_RECORD && state_save_flag == 0)
    {
        //开运动
        sleep_sumit_work(SLEEP_OPS_SPORT, &status);
        dnd_sumit_work(DND_OPS_SPORT, &status);
        sport_state_change_notify_power_save(status);
        state_save_flag = 1;
    }
    else if (status == ACTIVITY_FIT_IDLE && state_save_flag == 1)
    {
        //关运动
        sleep_sumit_work(SLEEP_OPS_SPORT, &status);
        dnd_sumit_work(DND_OPS_SPORT, &status);
        sport_state_change_notify_power_save(status);
        state_save_flag = 0;
    }
    g_simulator_sport_status = status;
}

#endif   // !SIMULATOR

#ifndef SIMULATOR
#include "algo_service_sport_status.h"
static void avt_sta(int argc, char **argv)
{
    if (strcmp(argv[1], "status") == 0)
    {
        // rt_kprintf("g_fit_app_status:%d algo_status:%d\n", g_fit_app_status, get_sport_status());
    }
}

MSH_CMD_EXPORT(avt_sta, avt_sta);
#endif   // !SIMULATOR
