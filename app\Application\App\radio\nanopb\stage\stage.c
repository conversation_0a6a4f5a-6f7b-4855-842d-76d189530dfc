/************************************************************************
* 
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   stage.c
@Time    :   2025/03/25 15:33:14
<AUTHOR>   lxin
* 
**************************************************************************/
#include "stage.h"
#include "crc8.h"
#include "ble_cmd_response.h"
#include "ble_cmd_common.h"
#include "ble_nus_srv.h"
#include "pb.h"
#include "pb_encode.h"
#include "pb_decode.h"
#include "pb_decode_common.h"
#include "pb_encode_common.h"
#include "stage.pb.h"
#include "qw_mlist.h"
#include "qw_fs_api.h"
#include "app_timer.h"
#include "igs_dev_config.h"
#include "ble_data_inf.h"
#include "system_utils.h"
#include "ble_data_inf.h"
#include "segments_port.h"
#include "thread_pool.h"
#include "data_convert.h"

extern osThreadId_t gui_task_thread_get(void);

#define  STAGE_FILE_LIST_NUM_MAX_UPLOAD    10
#define  STAGE_CACHE_VALID_TIME            5000

/***************************************************************************************/
APP_TIMER_DEF(trace_timer_id_s);
static bool trace_timer_inited = false;

static uint16_t file_list_start_index = 0;
static uint16_t file_list_end_index = 0;
static qw_mlist_t mlist_file;
static bool view_request_sync_file = false;

static void stage_file_request_timeout(bool error);

static void trace_timer_handler(void *p)
{
    if(trace_timer_inited){
        stage_file_request_timeout(true);
    }
}

static void stage_sync_status_update(bool result)
{
    if (view_request_sync_file) {
        EventData* pEvent = message_allocEventData();
        message_eventDataInit(pEvent, result, 0, NULL);
        system_send_event(gui_task_thread_get(), enumGUI_STAGE_SYNC_EVENT, pEvent);
        view_request_sync_file = false;
    }
}

static void ble_connect_sta_changed(uint32_t sta)
{
	if(BLE_STA_CONNECT != sta){
		stage_sync_status_update(false);
	}
}
DECLARE_LITTLE_NOTIFY(ble_sta_listener,ble_connect_sta_changed);

static void trace_timer_restart(void)
{
    if(!trace_timer_inited){
        app_timer_create(&trace_timer_id_s,APP_TIMER_MODE_SINGLE_SHOT, trace_timer_handler);
        little_notify_register(&ble_connect_sta_notify_list,&ble_sta_listener);
        trace_timer_inited = true;
    }
    if(trace_timer_inited){
        app_timer_stop(trace_timer_id_s);
        app_timer_start(trace_timer_id_s, STAGE_CACHE_VALID_TIME ,NULL, "stage_cache");
    }
}

static void trace_timer_stop(void)
{
    if(trace_timer_inited){
        app_timer_stop(trace_timer_id_s);
    }
}

static void stage_file_request_timeout(bool error)
{
    if(error){
        stage_sync_status_update(false);
    }
}

static uint32_t stage_file_num_get(void)
{
    uint32_t num = 0;
    qw_mlist_t* mnode;
    qw_mlist_init(&mlist_file);
    fs_scan_files(&mlist_file, SEGMENTS_DIR, ".cnx", NULL, NULL, NULL, NULL);
    FOR_MLIST(mnode, &mlist_file) {
        num++;
    }
    qw_mlist_uninit(&mlist_file);
    return num;
}

static bool stage_file_update_srv(const thread_pool_task *task_info)
{
    segments_sync_end_reload_segments();
    gui_command_submit(enumGUICMD_RESET_SCREEN, NULL);
    segments_sync_end_restart_segments_function();
    stage_sync_status_update(true);
    return true;
}

static void stage_file_infomation_update(void)
{
    thread_pool_add_task(stage_file_update_srv, NULL, NULL, osPriorityBelowNormal2);
}

/***************************************************************************************/
static void stage_list_num_send(void)
{
    uint8_t pb_crc = 0;
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    stage_data_msg stage_message;

    memset(&stage_message, 0, sizeof(stage_data_msg));
    ble_data_var_tx_get(&data, &length, BLE_NUS_CH3_UUID);

    stage_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_STAGE;
    stage_message.statge_date_operate_type = STAGE_DATA_OPERATE_TYPE_enum_STAGE_DATA_OPERATE_TYPE_LIST_NUM_GET;

    stage_message.has_list_msg = true;
    stage_message.list_msg.has_file_num = true;
    stage_message.list_msg.file_num = stage_file_num_get();
    stage_message.list_msg.has_file_list_support_num_max = true;
    stage_message.list_msg.file_list_support_num_max = STAGE_FILE_LIST_NUM_MAX_UPLOAD;
    stage_message.list_msg.has_file_index_start = false;
    stage_message.list_msg.has_file_index_end = false;

    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX);
    pb_encode(&encode_stream, stage_data_msg_fields, &stage_message);

    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch2( );
    pb_crc = CRC_Calc8_Table_L(data, *length);
    ble_cmd_end_tx(stage_message.service_type, 0, stage_message.statge_date_operate_type, 0, encode_stream.bytes_written, pb_crc);
}

//1234567890_StageName.cnx
static bool stage_filename_to_stage_infor(uint8_t *stage_file_name, uint32_t *id, uint8_t *stage_name)
{
    char *str1, *str2;
    uint8_t stage_id[ROUTE_ID_STR_LENGTH_MAX] = {0};
    uint8_t stage_file_name_temp[ROUTE_FILE_NAME_LENGTH_MAX] = {0};

    memcpy (stage_file_name_temp, stage_file_name, ROUTE_FILE_NAME_LENGTH_MAX - 1);   

    if (0 == strchr(stage_file_name_temp, '_') || 0 == strrchr(stage_file_name_temp, '.') 
        || strchr(stage_file_name_temp, '_') > strrchr(stage_file_name_temp, '.'))
    {
        return false;
    }
    
    str1 = strchr(stage_file_name_temp, '_');
    *str1 = '\0';
    if (strlen(stage_file_name_temp) >= ROUTE_ID_STR_LENGTH_MAX)
    {
        memcpy (stage_id, stage_file_name_temp, ROUTE_ID_STR_LENGTH_MAX - 1);
    }
    else
    {
        memcpy (stage_id, stage_file_name_temp, strlen(stage_file_name_temp));
    }

    string_to_int(stage_id, id);
    if (*id == 0)
    {
        return false;
    }
    

    str2 = strrchr(str1 + 1, '.');
    *str2 = '\0';
    
    if (strlen(str1 + 1) >= ROUTE_NAME_LENGTH_MAX)
    {
        memcpy (stage_name, str1 + 1, ROUTE_NAME_LENGTH_MAX - 1);
    }
    else
    {
       memcpy (stage_name, str1 + 1, strlen(str1 + 1)); 
    }

    return true;
}

static bool stage_data_list_repeated_submsg_encode(pb_ostream_t *stream, const pb_field_t *field, void * const *arg)
{
#define STAGE_FILE_NAME_LENGTH  128
    uint16_t offset = 0;
    uint8_t status = false;
    uint16_t start_index = file_list_start_index;
    uint16_t end_index = file_list_end_index + 1;
    qw_mlist_t* mnode = NULL;
    stage_file_message stage_data_list = {0};
    uint8_t file_name[STAGE_FILE_NAME_LENGTH];

    uint8_t split_file_name[STAGE_FILE_NAME_LENGTH];
    uint32_t file_id = 0;

	stage_data_list.file_name.funcs.encode = encode_string;
    stage_data_list.has_file_size = false;
    stage_data_list.has_file_id = true;

    qw_mlist_init(&mlist_file);
    fs_scan_files(&mlist_file, SEGMENTS_DIR, ".cnx", NULL, NULL, NULL, NULL);
    FOR_MLIST(mnode, &mlist_file){
        memset(file_name, 0, STAGE_FILE_NAME_LENGTH);
        memset(split_file_name, 0, STAGE_FILE_NAME_LENGTH);

        if ((offset >= start_index) && (offset < end_index)){
            if(mnode->data_size >= STAGE_FILE_NAME_LENGTH){
                memcpy(file_name,mnode->mnode_data,STAGE_FILE_NAME_LENGTH - 1);
                file_name[STAGE_FILE_NAME_LENGTH - 1] = '\0';
            }else{
                memcpy(file_name,mnode->mnode_data,mnode->data_size);
                file_name[mnode->data_size] = '\0';
            }

            if(stage_filename_to_stage_infor(file_name, &file_id, split_file_name)){
                stage_data_list.file_id = file_id;
                stage_data_list.file_name.arg = split_file_name;
                status = pb_encode_tag_for_field(stream, field);
                status &= pb_encode_submessage(stream, stage_file_message_fields, &stage_data_list);
            }
        }
        offset ++;
    }
    qw_mlist_uninit(&mlist_file);

    return status;
}

static void stage_data_list_send(void)
{
	uint8_t pb_crc = 0;
	uint8_t *data = NULL;
	uint16_t *length = NULL;
	stage_data_msg stage_message;

	memset(&stage_message, 0, sizeof(stage_data_msg));
	ble_data_var_tx_get(&data, &length, BLE_NUS_CH3_UUID);

	//参数赋值
	stage_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_STAGE;
	stage_message.statge_date_operate_type = STAGE_DATA_OPERATE_TYPE_enum_STAGE_DATA_OPERATE_TYPE_LIST_GET;


	stage_message.stage_file_msg.arg = NULL;
	stage_message.stage_file_msg.funcs.encode = &stage_data_list_repeated_submsg_encode;

	pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX);
	pb_encode(&encode_stream, stage_data_msg_fields, &stage_message);

	*length = encode_stream.bytes_written;
    ble_nus_data_tx_ch2( );

	pb_crc = CRC_Calc8_Table_L(data, *length);
	ble_cmd_end_tx(stage_message.service_type, 0, stage_message.statge_date_operate_type, 0, encode_stream.bytes_written, pb_crc);
}

bool stage_del_file_message_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    uint8_t file_name[156] = {0};
    stage_file_message stage_data_list = {0};

    stage_data_list.file_name.arg = file_name;
    stage_data_list.file_name.funcs.decode = decode_string;
    if (!pb_decode(stream, stage_file_message_fields, &stage_data_list)){
        return false;
    }
    segments_delete_by_name((const char*)file_name);
    return true;
}

void stage_decode(uint8_t* pb_buffer, uint16_t buffer_length, END_TYPE end_type)
{
    uint8_t status = false;
    stage_data_msg stage_message;
    memset(&stage_message, 0x00, sizeof(stage_data_msg));

    stage_message.stage_file_msg.arg = NULL;
    stage_message.stage_file_msg.funcs.decode = stage_del_file_message_decode;

    pb_istream_t decode_stream = pb_istream_from_buffer(pb_buffer, buffer_length);
    status = pb_decode(&decode_stream, stage_data_msg_fields, &stage_message);

    if (true == status)
    {
        switch (stage_message.statge_date_operate_type)
        {
        case STAGE_DATA_OPERATE_TYPE_enum_STAGE_DATA_OPERATE_TYPE_LIST_NUM_GET:
            stage_list_num_send();
            break;
        case STAGE_DATA_OPERATE_TYPE_enum_STAGE_DATA_OPERATE_TYPE_LIST_GET:
            if(stage_message.has_list_msg){
                file_list_start_index = stage_message.list_msg.file_index_start;
                file_list_end_index = stage_message.list_msg.file_index_end;
                stage_data_list_send();
            }else{
                ble_cmd_err_status_tx(stage_message.service_type, 0, stage_message.statge_date_operate_type, 0);
            }
            break;
        case STAGE_DATA_OPERATE_TYPE_enum_STAGE_DATA_OPERATE_TYPE_FILE_DEL:
            ble_cmd_success_status_tx(stage_message.service_type, 0, stage_message.statge_date_operate_type, 0);
            break;
        case STAGE_DATA_OPERATE_TYPE_enum_STAGE_DATA_OPERATE_TYPE_FILE_SYNC_START:
            trace_timer_stop();
            segments_sync_start_disable_segments_function();
            ble_cmd_success_status_tx(stage_message.service_type, 0, stage_message.statge_date_operate_type, 0);
            break;
        case STAGE_DATA_OPERATE_TYPE_enum_STAGE_DATA_OPERATE_TYPE_FILE_SYNC_END:
            ble_cmd_success_status_tx(stage_message.service_type, 0, stage_message.statge_date_operate_type, 0);
            stage_file_infomation_update();
            break;
        default:
            ble_cmd_err_status_tx(stage_message.service_type, 0, stage_message.statge_date_operate_type, 0);
            break;
        }
    }
}

void stage_status_handle(uint8_t* buf)
{
    ble_status_cmd_st* ble_status_cmd_s = (ble_status_cmd_st*)buf;
    uint8_t status = ble_status_cmd_s->status;

    if (enmuDATA_ERR_STATUS == status)
    {
        switch (ble_status_cmd_s->op_type)
        {
        default:
            break;
        }
    }
}

bool device_request_stage_file_sync(void)
{
    if(g_device_get_ble_connect_status() && !view_request_sync_file)
    {
        ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_STAGE, 0, 0, 0xff, 0);
        trace_timer_restart();
        view_request_sync_file = true;
        return true;
    }
    return false;
}
