import * as touchgfx from "touchgfx"
import * as data_servers from "data_servers"

import * as touchx_event from "../touchx_event.js"
import * as dial_enum from "../dial_enum.js"

var g_sec_ = -100000;
var g_min_ = -100000;
var g_hour_ = -100000;

var g_color = data_servers.getThemeColor();

class wf1 extends touchgfx.touchx_app
{
	updateHour(refreshImmediately) {
		var hour = data_servers.getIntByString(data_servers.getSysTime(dial_enum.TIME_HOUR));
		if(!refreshImmediately)
		{
			if(g_hour_ == hour) return;
		}
		g_hour_ = hour;
		var hourUnit = hour%10;
		var hourTen =  Math.floor(hour/10);
		let tenImgPath = hourTen + "_lrg_num.bin";
		let unitImgPath = hourUnit + "_lrg_num.bin"; 
		// print("updateHour:", hour, hourTen, hourUnit, tenImgPath, unitImgPath);
		this.hourTenImg.setBitMap(tenImgPath);
		this.hourUnitImg.setBitMap(unitImgPath);
		this.hourTenImg.setAlign(dial_enum.ALIGN_IN_LT, 0, 0);
		this.hourUnitImg.setAlign(dial_enum.ALIGN_IN_LT, 62, 0);

        this.updateObject(this.hourTenImg.getObject());
        this.updateObject(this.hourUnitImg.getObject());
	}

	createHour() {
		// 时
		this.hourTenImg = new touchgfx.createImg();
		this.add(this.dataContainer.getObject());
		this.hourTenImg.setWidthHeight(62,86);

		this.hourUnitImg = new touchgfx.createImg();
		this.add(this.dataContainer.getObject());
		this.hourUnitImg.setWidthHeight(62,86);

		this.updateHour(false);
	}

	updateMinute(refreshImmediately) {
		// 分
		var min = data_servers.getIntByString(data_servers.getSysTime(dial_enum.TIME_MIN));
		if(!refreshImmediately){
			if(g_min_ == min) return;
		}
		g_min_ = min;
		var minUnit = min%10;
		var minTen =  Math.floor(min/10);
		let minTenImgPath = minTen + "_lrg_num.bin";
		let minUnitImgPath = minUnit + "_lrg_num.bin";
		// print("updateMinute:", min, minTen, minUnit, minTenImgPath, minUnitImgPath);
		this.minTenImg.setBitMap(minTenImgPath);
		this.minTenImg.setReColor(g_color);
		this.minUnitImg.setBitMap(minUnitImgPath);
		this.minUnitImg.setReColor(g_color);
		this.minTenImg.setAlign(dial_enum.ALIGN_IN_LT, 155, 0);
		this.minUnitImg.setAlign(dial_enum.ALIGN_IN_LT, 217, 0);

        this.updateObject(this.minTenImg.getObject());
        this.updateObject(this.minUnitImg.getObject());
	}

	createMinute() {
		this.minTenImg = new touchgfx.createImg();
		this.add(this.dataContainer.getObject());
		this.minTenImg.setWidthHeight(62,86);

		this.minUnitImg = new touchgfx.createImg();
		this.add(this.dataContainer.getObject());
		this.minUnitImg.setWidthHeight(62,86);

		this.updateMinute(false);
	}

	startUp() 
	{
		this.bg = new touchgfx.createImg();
		this.add(this.bg.getObject());
		this.bg.setWidthHeight(466,466);
		this.bg.setBitMap("bg.bin");
		this.bg.setAlign(dial_enum.ALIGN_IN_CENTER, 0, 0);
		
		this.dataContainer = new touchgfx.createContainer();
		this.add(this.dataContainer.getObject());
		this.dataContainer.setWidthHeight(279,126);
		this.dataContainer.setAlign(dial_enum.ALIGN_IN_CENTER, 0, 0);

		this.symble = new touchgfx.createImg();
		this.add(this.dataContainer.getObject());
		this.symble.setWidthHeight(30,86);
		this.symble.setBitMap("0_simble.bin");
		this.symble.setAlign(dial_enum.ALIGN_IN_LT, 124, 0);

		// 背景图
		this.createHour();
		this.createMinute();


		//刷新函数
		this.setUpdate(
			function() {
				this.update();
			}
			, 1
		);
	}

	refreshDialViewImmediately()
	{

		this.updateHour(true);
		this.updateMinute(true);
		// this.updateObject(this.bg.getObject());

	}

	update() 
	{
		var sec_str = data_servers.getSysTime(dial_enum.TIME_SEC);
		var min_str = data_servers.getSysTime(dial_enum.TIME_MIN);
		// //判断秒数是否有变化
		if(g_sec_ != sec_str || g_minute_ != min_str)
		{
			this.updateHour(false);
			this.updateMinute(false);

			g_sec_ = sec_str;
            g_minute_ = min_str;
		}
	}
	tearDown() {
		this.removeAll();
	}	
}
globalThis.wf1 = wf1;