﻿/*************************************************************************
 * @file algo_service_heart_rate_avg.c
 * <AUTHOR> (<EMAIL>)
 * @brief 平均心率算法组件实现
 * @version 0.1
 * @date 2024-12-03
 *
 * @copyright Copyright (c) 2024-2025, <PERSON>han Qiwu Technology Co., Ltd
 *
 ************************************************************************/
#include "algo_service_heart_rate_avg.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_task.h"
#include "cfg_header_def.h"
#include "max_min.h"
#include "qw_data_type.h"
#include "qw_time_util.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"
#include "qw_user_debug.h"
#include "qw_fit_api.h"

#define MIN_ALERT_TIME_DIFF 30   //最小的报警时间间隔,单位s
#define MIN_HR              30

// 输入数据
typedef struct
{
    uint32_t total_timer_time;       // 移动时间 1000 * s,
    uint8_t heart_rate;              //心率
    saving_status_e saving_status;   //数据记录的状态
    uint8_t sports_type;             // 运动类型
    bool in_rest;                    // 是否在休息
} algo_heart_rate_avg_sub_t;

static algo_heart_rate_avg_sub_t s_algo_in = {0};

// 发布数据
static algo_heart_rate_avg_pub_t s_algo_out = {0};

// 中间数据
static max_min_unsigned_t s_max_min = {0};
static max_min_unsigned_t s_max_min_lap = {0};
static uint32_t s_last_time_hr_deal = 0;
static uint32_t s_last_time_hr_update = 0;

// 本算法打开标记
static bool s_is_open = false;

//将输入的心率及心率区间设置转换成心率区间(*10)。区间有没有0区取决于区间配置数组的[0]值是否大于0。默认心率有0区，区间显示从0.1到5.9，与功率不同
static uint16_t get_hr_zone(const uint8_t *hr_zone, uint8_t zone_num, uint8_t hr)
{
    uint8_t *p_zone_8 = NULL;
    uint16_t zone_out_10 = 0xffff;

    //Calc digital zone.
    if (0xff == hr || NULL == hr_zone || 0 == zone_num || ALGO_TIME_IN_HR_ZONE_COUNT + 1 < zone_num)
    {
        zone_out_10 = 0xffff;
    }
    else   //心率区间储存了最大心率，所以不用MAX_HR计算
    {
        p_zone_8 = (uint8_t *) hr_zone;

        if (hr >= p_zone_8[zone_num - 1])            //达到最大心率
        {
            zone_out_10 = (zone_num - 1) * 10 + 9;   //区间5.9
        }
        else if (hr < p_zone_8[0])                   //处于第0个区间
        {
            zone_out_10 = hr * 10 / p_zone_8[0];
        }
        else
        {
            for (uint8_t i = 1; i < zone_num; i++)
            {
                if (hr < p_zone_8[i])
                {
                    //找到所在区间
                    zone_out_10 = i * 10 + (hr - p_zone_8[i - 1]) * 10 / (p_zone_8[i] - p_zone_8[i - 1]);
                    break;
                }
            }
        }
    }

    return zone_out_10;
}

//心率警示检测
static void auto_alert_hrm_check(sports_alert_t *p_alert, uint8_t heart_rate)
{
    uint8_t hrm = heart_rate;
    SPORTTYPE sport_type = get_current_sport_mode();
    int hrm_alert_used = get_sport_remind_en(sport_type, SPORT_REMIND_HRM, MAIN_EN);   //心率警示是否开启
    int high_alert_used = get_sport_remind_en(sport_type, SPORT_REMIND_HRM, HIGH_EN);
    int low_alert_used = get_sport_remind_en(sport_type, SPORT_REMIND_HRM, LOW_EN);
    static uint32_t time_hrm_alert_high = 0;
    static uint32_t time_hrm_alert_low = 0;
    uint16_t hrm_max_threshold = get_sport_remind_value(sport_type, SPORT_REMIND_HRM, true);    //警示阈值
    uint16_t hrm_min_threshold = get_sport_remind_value(sport_type, SPORT_REMIND_HRM, false);   //警示阈值
    sports_alert_t alert_msg = {0, 0, 0, 0};
    uint32_t runtime_ms = get_boot_msec();

    if (NULL == p_alert)
    {
        return;
    }

    if (hrm_alert_used)    //当前报警开启
    {
        if (0xff == hrm)   //心率无效
        {
            time_hrm_alert_high = 0;
            time_hrm_alert_low = 0;
        }
        else if (hrm > hrm_max_threshold)   //需要报警
        {
            alert_msg.high_alert_status = high_alert_used ? true : false;
            alert_msg.low_alert_status = false;
            alert_msg.low_alert_event = false;

            //时间间隔需要满足要求
            if (0 == time_hrm_alert_high || runtime_ms / 1000 - time_hrm_alert_high > MIN_ALERT_TIME_DIFF)
            {
                time_hrm_alert_high = runtime_ms / 1000;   //记录本次报警的时间
                alert_msg.high_alert_event = high_alert_used ? true : false;
            }
            else
            {
                alert_msg.high_alert_event = false;
            }
        }
        else if (hrm < hrm_min_threshold && MIN_HR < hrm)
        {
            alert_msg.high_alert_status = false;
            alert_msg.high_alert_event = false;
            alert_msg.low_alert_status = low_alert_used ? true : false;

            //时间间隔需要满足要求
            if (0 == time_hrm_alert_low || runtime_ms / 1000 - time_hrm_alert_low > MIN_ALERT_TIME_DIFF)
            {
                time_hrm_alert_low = runtime_ms / 1000;   //记录本次报警的时间
                alert_msg.low_alert_event = low_alert_used ? true : false;
            }
            else
            {
                alert_msg.low_alert_event = false;
            }
        }
        else   //正常范围内，不警示
        {
            alert_msg.high_alert_status = false;
            alert_msg.low_alert_status = false;
            alert_msg.high_alert_event = false;
            alert_msg.low_alert_event = false;
        }
    }

    memcpy(p_alert, &alert_msg, sizeof(sports_alert_t));
}

//更新心率区间时间
static void update_time_in_zone_hr(uint32_t *time_in_zone, uint8_t time_in_zone_num, uint16_t hr_zone, uint32_t total_timer_time)
{
    uint16_t zone, delta_time;

    if (total_timer_time <= s_last_time_hr_deal)
    {
        return;
    }

    delta_time = total_timer_time - s_last_time_hr_deal;
    s_last_time_hr_deal = total_timer_time;

    //Input check.
    if (NULL == time_in_zone || 0 == time_in_zone_num || ALGO_TIME_IN_HR_ZONE_COUNT < time_in_zone_num || hr_zone < 10 || hr_zone == UINT16_MAX)
    {
        return;
    }

    zone = hr_zone / 10 - 1;

    if (0 <= zone && time_in_zone_num > zone)
    {
        time_in_zone[zone] += delta_time;
    }
}

/**
 * @brief 算法处理
 *
 * @param algo_out 输出数据
 * @param algo_in 输入数据
 */
static void algo_heart_rate_avg_deal(algo_heart_rate_avg_pub_t *algo_out, const algo_heart_rate_avg_sub_t *algo_in)
{
    uint8_t hrm_zone[ALGO_TIME_IN_HR_ZONE_COUNT + 1] = {0};   //最后一个为最大心率
    uint8_t max_hr = 0;                                       // 最大心率
    uint8_t rest_hr = 0;                                      // 静息心率
    uint8_t heart_rate_reserve = 0;                           // 储备心率

    if (algo_in->sports_type < SPORTSTYPE_CYCLING)            //跑步
    {
        for (int i = 0; i < ALGO_TIME_IN_HR_ZONE_COUNT; i++)
        {
            hrm_zone[i] = get_user_info_run_hrm_zone(i);                      //跑步心率区间
        }
        hrm_zone[ALGO_TIME_IN_HR_ZONE_COUNT] = get_user_info_run_hrm_max();   //跑步最大心率
    }
    else if (algo_in->sports_type < SPORTSTYPE_POOL_SWIMMING)                 //骑行
    {
        for (int i = 0; i < ALGO_TIME_IN_HR_ZONE_COUNT; i++)
        {
            hrm_zone[i] = get_user_info_ride_hrm_zone(i);                      //骑行心率区间
        }
        hrm_zone[ALGO_TIME_IN_HR_ZONE_COUNT] = get_user_info_ride_hrm_max();   //骑行最大心率
    }
    else if (algo_in->sports_type < SPORTSTYPE_STRENGTH_TRAINING)              //游泳
    {
        for (int i = 0; i < ALGO_TIME_IN_HR_ZONE_COUNT; i++)
        {
            hrm_zone[i] = get_user_info_swim_hrm_zone(i);                      //游泳心率区间
        }
        hrm_zone[ALGO_TIME_IN_HR_ZONE_COUNT] = get_user_info_swim_hrm_max();   //游泳最大心率
    }
    else                                                                       //其他运动
    {
        for (int i = 0; i < ALGO_TIME_IN_HR_ZONE_COUNT; i++)
        {
            hrm_zone[i] = get_user_info_run_hrm_zone(i);                                                                //其他运动使用跑步心率区间
        }
        hrm_zone[ALGO_TIME_IN_HR_ZONE_COUNT] = get_user_info_run_hrm_max();                                             //其他运动使用跑步最大心率
    }
    if (algo_in->sports_type < SPORTSTYPE_CYCLING)                                                                      //跑步
    {
        max_hr = get_user_info_run_hrm_max();                                                                           //跑步最大心率
    }
    else if ((algo_in->sports_type > SPORTSTYPE_INDOOR_RUNNING) && (algo_in->sports_type < SPORTSTYPE_POOL_SWIMMING))   //骑行
    {
        max_hr = get_user_info_ride_hrm_max();
    }
    else if (algo_in->sports_type < SPORTSTYPE_STRENGTH_TRAINING)   //游泳
    {
        max_hr = get_user_info_swim_hrm_max();                      //游泳最大心率
    }
    else                                                            //其他运动
    {
        max_hr = get_user_info_run_hrm_max();                       //其他运动使用跑步最大心率
    }

    rest_hr = get_user_info_hrm_resting();
    heart_rate_reserve = max_hr - rest_hr;

    algo_out->heart_rate = algo_in->heart_rate;

    // 计算心率百分比 = 心率/最大心率*100%
    if (0xff != algo_out->heart_rate && max_hr > 0)
    {
        algo_out->heart_rate_prcent = (uint8_t) (algo_out->heart_rate * 100 / max_hr);
    }
    else
    {
        algo_out->heart_rate_prcent = 0xff;
    }
    // 计算储备心率 = 最大心率 - 静息心率
    algo_out->heart_rate_reserve = heart_rate_reserve;

    // 计算当前心率储备百分比 = (当前心率 - 静息心率) / 储备心率 * 100%
    if (0xff != algo_out->heart_rate && heart_rate_reserve > 0 && algo_out->heart_rate > rest_hr)
    {
        algo_out->heart_rate_reserve_prcent = (uint8_t) ((algo_out->heart_rate - rest_hr) * 100 / heart_rate_reserve);
    }
    else
    {
        algo_out->heart_rate_reserve_prcent = 0xff;
    }

    // 区间
    algo_out->heart_rate_zone = get_hr_zone(hrm_zone, ALGO_TIME_IN_HR_ZONE_COUNT + 1, algo_out->heart_rate);

    if (enum_status_saving == algo_in->saving_status)
    {
        // 运动警示
        auto_alert_hrm_check(&algo_out->alert, algo_out->heart_rate);

        if (0xff != algo_in->heart_rate && !algo_in->in_rest)
        {
            max_min_unsigned_update(&s_max_min, (uint32_t) algo_in->heart_rate);
            algo_out->avg_heart_rate = (uint8_t) s_max_min.avg;
            algo_out->min_heart_rate = (uint8_t) s_max_min.min;
            algo_out->max_heart_rate = (uint8_t) s_max_min.max;
            max_min_unsigned_update(&s_max_min_lap, (uint32_t) algo_in->heart_rate);
            algo_out->avg_heart_rate_lap = (uint8_t) s_max_min_lap.avg;
            algo_out->min_heart_rate_lap = (uint8_t) s_max_min_lap.min;
            algo_out->max_heart_rate_lap = (uint8_t) s_max_min_lap.max;
            // 计算平均心率百分比 = 平均心率/最大心率*100%

            if (max_hr > 0)
            {
                algo_out->avg_heart_rate_prcent = (uint8_t) (algo_out->avg_heart_rate * 100 / max_hr);
                algo_out->max_heart_rate_prcent = (uint8_t) (algo_out->max_heart_rate * 100 / max_hr);
                algo_out->avg_heart_rate_lap_prcent = (uint8_t) (algo_out->avg_heart_rate_lap * 100 / max_hr);
                algo_out->max_heart_rate_lap_prcent = (uint8_t) (algo_out->max_heart_rate_lap * 100 / max_hr);

                // 如果有前圈数据，计算前圈百分比
                if (0xff != algo_out->avg_heart_rate_pre_lap)
                {
                    algo_out->avg_heart_rate_pre_lap_prcent = (uint8_t) (algo_out->avg_heart_rate_pre_lap * 100 / max_hr);
                }
                else
                {
                    algo_out->avg_heart_rate_pre_lap_prcent = 0xff;
                }

                if (0xff != algo_out->max_heart_rate_pre_lap)
                {
                    algo_out->max_heart_rate_pre_lap_prcent = (uint8_t) (algo_out->max_heart_rate_pre_lap * 100 / max_hr);
                }
                else
                {
                    algo_out->max_heart_rate_pre_lap_prcent = 0xff;
                }
            }
            else
            {
                algo_out->avg_heart_rate_prcent = 0xff;
                algo_out->max_heart_rate_prcent = 0xff;
                algo_out->avg_heart_rate_lap_prcent = 0xff;
                algo_out->max_heart_rate_lap_prcent = 0xff;
                algo_out->avg_heart_rate_pre_lap_prcent = 0xff;
                algo_out->max_heart_rate_pre_lap_prcent = 0xff;
            }

            // 计算储备心率百分比
            if (heart_rate_reserve > 0)
            {
                // 本圈储备心率百分比
                if (algo_out->avg_heart_rate_lap > rest_hr)
                {
                    algo_out->heart_rate_reserve_lap_prcent = (uint8_t) ((algo_out->avg_heart_rate_lap - rest_hr) * 100 / heart_rate_reserve);
                }
                else
                {
                    algo_out->heart_rate_reserve_lap_prcent = 0xff;
                }
                // 前圈储备心率百分比
                if (0xff != algo_out->avg_heart_rate_pre_lap && algo_out->avg_heart_rate_pre_lap > rest_hr)
                {
                    algo_out->heart_rate_reserve_pre_lap_prcent = (uint8_t) ((algo_out->avg_heart_rate_pre_lap - rest_hr) * 100 / heart_rate_reserve);
                }
                else
                {
                    algo_out->heart_rate_reserve_pre_lap_prcent = 0xff;
                }
            }
            else
            {
                algo_out->heart_rate_reserve_lap_prcent = 0xff;
                algo_out->heart_rate_reserve_pre_lap_prcent = 0xff;
            }
            // 更新本趟心率(trip是指单趟泳道或其他单次活动，可暂时保持与当前心率相同)
            algo_out->trip_heart_rate = algo_out->heart_rate;
        }
        if (0xffff != algo_out->heart_rate_zone)
        {
            // 区间时间
            update_time_in_zone_hr(algo_out->time_in_heart_rate_zone, ALGO_TIME_IN_HR_ZONE_COUNT, algo_out->heart_rate_zone, algo_in->total_timer_time);
        }
    }
}

/**
 * @brief 算法控制
 *
 * @param algo_out 输出数据
 * @param ctrl_type 控制类型
 */
static void algo_heart_rate_avg_ctrl(algo_heart_rate_avg_pub_t *algo_out, const algo_sports_ctrl_t *ctrl)
{
    if (ctrl->sports_type == FIT_SPORTS_JUMP_ROPE || ctrl->sports_type == FIT_SPORTS_STRENGTH_TRAINING || ctrl->sports_type == FIT_SPORTS_ROWING_MACHINE)
    {
        if (enum_ctrl_start == ctrl->ctrl_type)
        {
            memset(algo_out, 0xff, sizeof(algo_heart_rate_avg_pub_t));
            memset(&algo_out->alert, 0, sizeof(sports_alert_t));
            memset(algo_out->time_in_heart_rate_zone, 0, sizeof(uint32_t) * ALGO_TIME_IN_HR_ZONE_COUNT);
            max_min_unsigned_init(&s_max_min);
            max_min_unsigned_init(&s_max_min_lap);
            s_last_time_hr_deal = 0;
            s_last_time_hr_update = 0;
            s_algo_in.in_rest = false;
        }
        else if (enum_ctrl_lap == ctrl->ctrl_type)
        {
            s_algo_in.in_rest = true;
        }
        else if (enum_ctrl_rest_resume == ctrl->ctrl_type)
        {
            // 保存前圈心率数据
            algo_out->avg_heart_rate_pre_lap = algo_out->avg_heart_rate_lap;
            algo_out->min_heart_rate_pre_lap = algo_out->min_heart_rate_lap;
            algo_out->max_heart_rate_pre_lap = algo_out->max_heart_rate_lap;
            algo_out->min_heart_rate_pre_lap = algo_out->min_heart_rate_lap;
            // 保存前圈心率百分比数据
            algo_out->avg_heart_rate_pre_lap_prcent = algo_out->avg_heart_rate_lap_prcent;
            algo_out->max_heart_rate_pre_lap_prcent = algo_out->max_heart_rate_lap_prcent;
            algo_out->heart_rate_reserve_pre_lap_prcent = algo_out->heart_rate_reserve_lap_prcent;

            // 保存前趟心率数据
            algo_out->last_trip_heart_rate = algo_out->trip_heart_rate;

            // 重置当前圈数据
            algo_out->avg_heart_rate_lap = 0xff;
            algo_out->min_heart_rate_lap = 0xff;
            algo_out->max_heart_rate_lap = 0xff;
            algo_out->min_heart_rate_lap = 0xff;
            algo_out->avg_heart_rate_lap_prcent = 0xff;
            algo_out->max_heart_rate_lap_prcent = 0xff;
            algo_out->heart_rate_reserve_lap_prcent = 0xff;
            algo_out->trip_heart_rate = 0xff;

            max_min_unsigned_init(&s_max_min_lap);

            s_algo_in.in_rest = false;
        }
    }
    else
    {
        if (enum_ctrl_start == ctrl->ctrl_type)
        {
            memset(algo_out, 0xff, sizeof(algo_heart_rate_avg_pub_t));
            memset(&algo_out->alert, 0, sizeof(sports_alert_t));
            memset(algo_out->time_in_heart_rate_zone, 0, sizeof(uint32_t) * ALGO_TIME_IN_HR_ZONE_COUNT);
            max_min_unsigned_init(&s_max_min);
            max_min_unsigned_init(&s_max_min_lap);
            s_last_time_hr_deal = 0;
            s_last_time_hr_update = 0;
            s_algo_in.in_rest = false;
        }
        else if (enum_ctrl_lap == ctrl->ctrl_type)
        {
            // 保存前圈心率数据
            algo_out->avg_heart_rate_pre_lap = algo_out->avg_heart_rate_lap;
            algo_out->min_heart_rate_pre_lap = algo_out->min_heart_rate_lap;
            algo_out->max_heart_rate_pre_lap = algo_out->max_heart_rate_lap;
            algo_out->min_heart_rate_pre_lap = algo_out->min_heart_rate_lap;
            // 保存前圈心率百分比数据
            algo_out->avg_heart_rate_pre_lap_prcent = algo_out->avg_heart_rate_lap_prcent;
            algo_out->max_heart_rate_pre_lap_prcent = algo_out->max_heart_rate_lap_prcent;
            algo_out->heart_rate_reserve_pre_lap_prcent = algo_out->heart_rate_reserve_lap_prcent;

            // 保存前趟心率数据
            algo_out->last_trip_heart_rate = algo_out->trip_heart_rate;

            // 重置当前圈数据
            algo_out->avg_heart_rate_lap = 0xff;
            algo_out->min_heart_rate_lap = 0xff;
            algo_out->max_heart_rate_lap = 0xff;
            algo_out->min_heart_rate_lap = 0xff;
            algo_out->avg_heart_rate_lap_prcent = 0xff;
            algo_out->max_heart_rate_lap_prcent = 0xff;
            algo_out->heart_rate_reserve_lap_prcent = 0xff;
            algo_out->trip_heart_rate = 0xff;

            max_min_unsigned_init(&s_max_min_lap);
        }
    }
}

/**
 * @brief 算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_heart_rate_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_HEART_RATE_AVG;
    head.input_type = DATA_ID_ALGO_HEART_RATE;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_timer_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_HEART_RATE_AVG;
    head.input_type = DATA_ID_ALGO_TIMER;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法控制订阅处理
 *
 * @param in 控制数据
 * @param len 数据长度
 */
static void algo_sports_ctrl_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_HEART_RATE_AVG;
    head.input_type = DATA_ID_EVENT_SPORTS_CTRL;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_heart_rate_avg_out_callback(const void *out, uint32_t len)
{
    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_ALGO_HEART_RATE_AVG, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("%s publish error", __FUNCTION__);
    }
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_heart_rate",
        .topic_id = DATA_ID_ALGO_HEART_RATE,
        .callback = algo_heart_rate_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_timer",
        .topic_id = DATA_ID_ALGO_TIMER,
        .callback = algo_timer_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
};

/**
 * @brief 算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_heart_rate_avg_init(void)
{
    return 0;
}

/**
 * @brief 算法open
 *
 * @return int32_t 结果
 */
static int32_t algo_heart_rate_avg_open(void)
{
    if (s_is_open)
    {
        ALGO_COMP_LOG_W("%s already open", __FUNCTION__);
        return 0;
    }

    ALGO_COMP_LOG_D("%s open", __FUNCTION__);

    s_is_open = true;
    memset(&s_algo_in, 0xff, sizeof(s_algo_in));
    s_algo_in.total_timer_time = 0;
    s_algo_in.saving_status = 0;
    memset(&s_algo_out, 0xff, sizeof(algo_heart_rate_avg_pub_t));
    memset(&s_algo_out.alert, 0, sizeof(sports_alert_t));
    memset(s_algo_out.time_in_heart_rate_zone, 0, sizeof(uint32_t) * ALGO_TIME_IN_HR_ZONE_COUNT);
    max_min_unsigned_init(&s_max_min);
    max_min_unsigned_init(&s_max_min_lap);
    s_last_time_hr_deal = 0;
    s_last_time_hr_update = 0;

    sports_data_chart_init(DATATYPE_CHART_HRM_GRAPH);

    return algo_topic_list_subscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
}

/**
 * @brief 算法feed
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_heart_rate_avg_feed(uint32_t input_type, void *data, uint32_t len)
{
    algo_heart_rate_avg_sub_t *algo_in = &s_algo_in;
    algo_heart_rate_avg_pub_t *algo_out = &s_algo_out;
    switch (input_type)
    {
    case DATA_ID_ALGO_HEART_RATE:
    {
        const uint8_t *heart_rate_data = (uint8_t *) data;
        algo_in->heart_rate = (*heart_rate_data);
        s_last_time_hr_update = algo_in->total_timer_time;   // 更新最后心率更新时间
    }
    break;
    case DATA_ID_ALGO_TIMER:
    {
        const algo_timer_pub_t *timer_data = (algo_timer_pub_t *) data;
        algo_in->total_timer_time = timer_data->timer_total.timer_time;

        if (s_last_time_hr_update + 10000 < algo_in->total_timer_time)
        {
            algo_in->heart_rate = 0xff;   // 超过10s没有心率数据，则认为心率无效
        }
    }
    break;
    case DATA_ID_EVENT_SPORTS_CTRL:
    {
        const algo_sports_ctrl_t *sports_ctrl = (algo_sports_ctrl_t *) data;
        algo_in->saving_status = sports_ctrl->saving_status;
        algo_in->sports_type = sports_ctrl->sports_type;

        //算法控制
        algo_heart_rate_avg_ctrl(algo_out, sports_ctrl);

        //数据发布
        if (sports_ctrl->ctrl_type == enum_ctrl_null)
        {
            //算法处理
            algo_heart_rate_avg_deal(algo_out, algo_in);
            //更新速度图表数据
            sports_data_chart_update(DATATYPE_CHART_HRM_GRAPH, algo_out->heart_rate);
            //数据发布
            algo_heart_rate_avg_out_callback(algo_out, sizeof(algo_heart_rate_avg_pub_t));
        }
        else
        {
            // 记录事件后马上数据发布
            algo_heart_rate_avg_out_callback(algo_out, sizeof(algo_heart_rate_avg_pub_t));
        }
    }
    break;
    default:
        return 0;
    }
    return 0;
}

/**
 * @brief 算法close
 *
 * @return int32_t 结果
 */
static int32_t algo_heart_rate_avg_close(void)
{
    if (!s_is_open)
    {
        ALGO_COMP_LOG_W("%s already close", __FUNCTION__);
        return 0;
    }

    algo_topic_list_unsubscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));

    s_is_open = false;
    ALGO_COMP_LOG_D("%s close", __FUNCTION__);
    return 0;
}

// 算法组件实现
static algo_compent_ops_t s_heart_rate_avg_algo = {
    .init = algo_heart_rate_avg_init,
    .open = algo_heart_rate_avg_open,
    .feed = algo_heart_rate_avg_feed,
    .close = algo_heart_rate_avg_close,
    .ioctl = NULL,
};

/**
 * @brief 组件注册
 *
 * @return int32_t 结果
 */
int32_t register_heart_rate_avg_algo(void)
{
    algo_compnent_register(ALGO_TYPE_HEART_RATE_AVG, &s_heart_rate_avg_algo);
    return 0;
}