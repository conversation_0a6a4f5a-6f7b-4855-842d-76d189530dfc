/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   power_save_srv.c
@Time    :   2025/08/12 09:18:09
*
**************************************************************************/
#include "power_save_srv.h"
#include "gps_api.h"
#include "health.h"
#include "hr_push.h"
#include "service_health_kv.h"
#include "view_page_model.h"

// 模块状态联合体
typedef union {
    bool as_hr_push;            // 心率推送
    HR_MODE_E as_hr_monitor;    // 心率监测
    SPO2_MODE_E as_ox;          // 血氧监测
    STRESS_MODE_E as_stress;    // 压力监测
    gps_prefer_mode_e as_gps;   // GPS模式
} ModuleStateValue;

static cfg_power_save_set_t power_manager = {0};

// 模块配置结构
typedef struct
{
    ModuleStateValue regular_value;   // 常规省电值
    ModuleStateValue extreme_value;   // 极致省电值
} ModuleConfig;

// 模块配置表
static const ModuleConfig module_configs[PS_MODULE_COUNT] = {
    [PS_MODULE_HR_PUSH] = {.regular_value.as_hr_push = false, .extreme_value.as_hr_push = false},
    [PS_MODULE_HR_MONITOR] = {.regular_value.as_hr_monitor = HR_MODE_10MIN, .extreme_value.as_hr_monitor = HR_MODE_10MIN},
    [PS_MODULE_BLOOD_OXYGEN] = {.regular_value.as_ox = SPO2_MODE_MANUAL, .extreme_value.as_ox = SPO2_MODE_MANUAL},
    [PS_MODULE_STRESS_MONITOR] = {.regular_value.as_stress = STRESS_MODE_MANUAL, .extreme_value.as_stress = STRESS_MODE_MANUAL},
    [PS_MODULE_GPS] = {.regular_value.as_gps = enum_gps_prefer_power, .extreme_value.as_gps = enum_gps_prefer_power},
};

/**
 * @brief 检查并恢复省电模式设置
 *
 * 在开机时调用，检查当前省电模式状态，如果开启了省电模式，
 * 则直接应用对应的省电设置，不进行备份操作
 */
static void check_and_restore_power_save_mode(void)
{
    POWER_SAVE_TYPE current_mode = get_power_save_setting();

    if (current_mode == POWER_SAVE_NORMAL || current_mode == POWER_SAVE_SUPPER)
    {
        // 直接应用省电设置
        if (current_mode == POWER_SAVE_NORMAL)
        {
            cfg_set_gps_prefer_mode(module_configs[PS_MODULE_GPS].regular_value.as_gps);
            set_hr_push_en(module_configs[PS_MODULE_HR_PUSH].regular_value.as_hr_push);
            hr_measure_mode_set(module_configs[PS_MODULE_HR_MONITOR].regular_value.as_hr_monitor);
            spo2_measure_mode_set(module_configs[PS_MODULE_BLOOD_OXYGEN].regular_value.as_ox);
            stress_measure_mode_set(module_configs[PS_MODULE_STRESS_MONITOR].regular_value.as_stress);
        }
        else if (current_mode == POWER_SAVE_SUPPER)
        {
            cfg_set_gps_prefer_mode(module_configs[PS_MODULE_GPS].extreme_value.as_gps);
            set_hr_push_en(module_configs[PS_MODULE_HR_PUSH].extreme_value.as_hr_push);
            hr_measure_mode_set(module_configs[PS_MODULE_HR_MONITOR].extreme_value.as_hr_monitor);
            spo2_measure_mode_set(module_configs[PS_MODULE_BLOOD_OXYGEN].extreme_value.as_ox);
            stress_measure_mode_set(module_configs[PS_MODULE_STRESS_MONITOR].extreme_value.as_stress);
        }
    }
    // 更新当前模式
    power_manager.power_save_type = current_mode;
}

// ===================================================================
// 省电模块公共接口
// ===================================================================
/**
 * @brief 初始化省电模块
 *
 * 从KV存储加载保存的状态，并初始化硬件模块状态
 */
void power_saving_init(void)
{
    memset(&power_manager, 0, sizeof(power_manager));
    power_manager.power_save_type = get_power_save_setting();
    for (int i = 0; i < PS_MODULE_COUNT; i++)
    {
        power_manager.modules[i].user_modified = get_power_save_module_user_modified(i);
        power_manager.modules[i].backup = get_power_save_module_back_value(i);
    }
    // check_and_restore_power_save_mode();
}

static void set_power_manamger_setting(void)
{
    set_power_save_setting(power_manager.power_save_type);
    for (int i = 0; i < PS_MODULE_COUNT; i++)
    {
        set_power_save_module_user_modified(i, power_manager.modules[i].user_modified);
        set_power_save_module_back_value(i, power_manager.modules[i].backup);
    }
}

static void clr_power_manamger_setting(void)
{
    power_manager.power_save_type = POWER_SAVE_NO;
    for (int i = 0; i < PS_MODULE_COUNT; i++)
    {
        power_manager.modules[i].user_modified = false;
        power_manager.modules[i].backup = 0;
    }
}

/**
 * @brief 进入省电模式
 *
 * @param new_mode 目标省电模式
 *
 * @return bool 模式切换是否成功
 */
bool enter_power_saving_mode(POWER_SAVE_TYPE new_mode)
{
    // 验证模式切换有效性
    if (power_manager.power_save_type == POWER_SAVE_NO)
    {
        // 正常模式可进入任何省电模式
    }
    else if (power_manager.power_save_type == POWER_SAVE_NORMAL && new_mode == POWER_SAVE_SUPPER)
    {
        // 常规能直接进极致
    }
    else if (power_manager.power_save_type == POWER_SAVE_SUPPER && new_mode == POWER_SAVE_NORMAL)
    {
        return false;   // 极致不能直接进常规
    }
    else if (power_manager.power_save_type == new_mode)
    {
        return true;   // 已是目标模式
    }

    // 备份当前状态（仅当从正常模式切换时）
    power_manager.modules[PS_MODULE_GPS].backup = cfg_get_gps_prefer_mode();
    power_manager.modules[PS_MODULE_HR_PUSH].backup = get_hr_push_en();
    power_manager.modules[PS_MODULE_HR_MONITOR].backup = hr_measure_mode_get();
    power_manager.modules[PS_MODULE_BLOOD_OXYGEN].backup = spo2_measure_mode_get();
    power_manager.modules[PS_MODULE_STRESS_MONITOR].backup = stress_measure_mode_get();

    // 应用新省电模式设置
    if (new_mode == POWER_SAVE_NORMAL)
    {
        if (!power_manager.modules[PS_MODULE_GPS].user_modified)
        {
            cfg_set_gps_prefer_mode(module_configs[PS_MODULE_GPS].regular_value.as_gps);
        }
        if (!power_manager.modules[PS_MODULE_HR_PUSH].user_modified)
        {
            set_hr_push_en(module_configs[PS_MODULE_HR_PUSH].regular_value.as_hr_push);
        }
        if (!power_manager.modules[PS_MODULE_HR_MONITOR].user_modified)
        {
            hr_measure_mode_set(module_configs[PS_MODULE_HR_MONITOR].regular_value.as_hr_monitor);
        }
        if (!power_manager.modules[PS_MODULE_BLOOD_OXYGEN].user_modified)
        {
            spo2_measure_mode_set(module_configs[PS_MODULE_BLOOD_OXYGEN].regular_value.as_ox);
        }
        if (!power_manager.modules[PS_MODULE_STRESS_MONITOR].user_modified)
        {
            stress_measure_mode_set(module_configs[PS_MODULE_STRESS_MONITOR].regular_value.as_stress);
        }
    }
    else if (new_mode == POWER_SAVE_SUPPER)
    {
        if (!power_manager.modules[PS_MODULE_GPS].user_modified)
        {
            cfg_set_gps_prefer_mode(module_configs[PS_MODULE_GPS].extreme_value.as_gps);
        }
        if (!power_manager.modules[PS_MODULE_HR_PUSH].user_modified)
        {
            set_hr_push_en(module_configs[PS_MODULE_HR_PUSH].extreme_value.as_hr_push);
        }
        if (!power_manager.modules[PS_MODULE_HR_MONITOR].user_modified)
        {
            hr_measure_mode_set(module_configs[PS_MODULE_HR_MONITOR].extreme_value.as_hr_monitor);
        }
        if (!power_manager.modules[PS_MODULE_BLOOD_OXYGEN].user_modified)
        {
            spo2_measure_mode_set(module_configs[PS_MODULE_BLOOD_OXYGEN].extreme_value.as_ox);
        }
        if (!power_manager.modules[PS_MODULE_STRESS_MONITOR].user_modified)
        {
            stress_measure_mode_set(module_configs[PS_MODULE_STRESS_MONITOR].extreme_value.as_stress);
        }
    }
    power_manager.power_save_type = new_mode;
    set_power_manamger_setting();

    return true;
}

/**
 * @brief 退出省电模式
 *
 * 恢复到正常模式，并根据需要恢复模块状态
 */
void exit_power_saving_mode(void)
{
    if (power_manager.power_save_type == POWER_SAVE_NO)
    {
        return;   // 已是正常模式
    }

    // 恢复模块状态
    if (!power_manager.modules[PS_MODULE_GPS].user_modified)
    {
        cfg_set_gps_prefer_mode(power_manager.modules[PS_MODULE_GPS].backup);
    }

    if (!power_manager.modules[PS_MODULE_HR_PUSH].user_modified)
    {
        set_hr_push_en(power_manager.modules[PS_MODULE_HR_PUSH].backup);
    }

    if (!power_manager.modules[PS_MODULE_HR_MONITOR].user_modified)
    {
        hr_measure_mode_set(power_manager.modules[PS_MODULE_HR_MONITOR].backup);
    }

    if (!power_manager.modules[PS_MODULE_BLOOD_OXYGEN].user_modified)
    {
        spo2_measure_mode_set(power_manager.modules[PS_MODULE_BLOOD_OXYGEN].backup);
    }

    if (!power_manager.modules[PS_MODULE_STRESS_MONITOR].user_modified)
    {
        stress_measure_mode_set(power_manager.modules[PS_MODULE_STRESS_MONITOR].backup);
    }

    // 重置修改标志
    for (int i = 0; i < PS_MODULE_COUNT; i++)
    {
        power_manager.modules[i].user_modified = false;
    }

    // 更新模式
    power_manager.power_save_type = POWER_SAVE_NO;
    set_power_save_setting(power_manager.power_save_type);
}

/**
 * @brief 用户设置模块状态
 *
 * @param module 模块ID
 * @param state 新状态值
 */
void power_save_user_change_module_state(POWER_SAVE_ModuleId id)
{
    if (id >= PS_MODULE_COUNT)
        return;

    // 在省电模式下修改时标记
    if (power_manager.power_save_type != POWER_SAVE_NO)
    {
        power_manager.modules[id].user_modified = true;
        set_power_save_module_user_modified(id, true);
    }
}

/**
 * @brief 获取当前省电模式
 */
POWER_SAVE_TYPE get_current_power_mode(void)
{
    return get_power_save_setting();
}
