/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   blt_inbike_pb.c
@Time    :   2024/12/25 18:11:54
* 
**************************************************************************/
#include "blt_inbike_pb.h"
#include "blt_inbike.pb.h"
#include "ble_cmd_response.h"
#include "peripheral_common.pb.h"
#include "pb.h"
#include "pb_encode.h"
#include "pb_decode.h"
#include "crc8.h"
#include "ble_nus_c_srv.h"
#include "qw_sensor_common.h"
#include "sensor_ble.h"
#include "ble_light_cfg.h"


static void pb_light_proto_ver_set(uint8_t channel, uint8_t ver)
{
    const uint8_t* ble_mac = ble_periph_nus_c_channel_get_mac_addr(channel);
    if (ble_mac){
        blt_proto_ver_set(ble_mac, ver);
    }   
}

static void pb_light_type_set(uint8_t channel, uint8_t type)
{
    const uint8_t* ble_mac = ble_periph_nus_c_channel_get_mac_addr(channel);
    if (ble_mac){
        ble_light_set_type(ble_mac, type);
    }   
}


static void pb_light_mode_sup_set(uint8_t channel, blt_mode_sup *mode_sup, uint16_t cnt)
{
    const uint8_t* ble_mac = ble_periph_nus_c_channel_get_mac_addr(channel);
    if (ble_mac){
        blt_light_mode_sup_set(ble_mac, mode_sup, cnt);
    }
}

static void pb_light_cfg_sup_set(uint8_t channel, blt_cfg_sup *cfg_sup, uint16_t cnt)
{
    const uint8_t* ble_mac = ble_periph_nus_c_channel_get_mac_addr(channel);
    if (ble_mac){
        blt_light_cfg_sup_set(ble_mac, cfg_sup, cnt);
        blt_smtcfg_sleep_set(ble_mac, 2);
    }
}


static void pb_light_cur_mode_set(uint8_t channel, uint8_t mode)
{
    const uint8_t* ble_mac = ble_periph_nus_c_channel_get_mac_addr(channel);
    if (ble_mac){
        ble_light_set_mode((uint8_t*)ble_mac, mode);
    }   
}

static void pb_light_left_time_set(uint8_t channel, uint32_t lefttime)
{
    const uint8_t* ble_mac = ble_periph_nus_c_channel_get_mac_addr(channel);
    if (ble_mac){
        ble_light_left_time_update(ble_mac, lefttime);
    }   
}

static void pb_light_bat_pct_set(uint8_t channel, uint8_t bat_pct)
{
    const uint8_t* ble_mac = ble_periph_nus_c_channel_get_mac_addr(channel);
    if (ble_mac){
        ble_light_battary_update(ble_mac, bat_pct);
    }   
}

//状态命令解析
static void pb_light_lefttime_update(uint8_t channel, uint32_t lefttime)
{
    const uint8_t* mac_addr = ble_periph_nus_c_channel_get_mac_addr(channel);
    if (mac_addr) {
        ble_light_left_time_update(mac_addr, lefttime); 
    }
}



static void pb_light_mode_cur_update(uint8_t channel, uint8_t mode)
{
    const uint8_t* mac_addr = ble_periph_nus_c_channel_get_mac_addr(channel);
    if (mac_addr) {
        sensor_module_evt_handler evt_handler = sensor_module_evt_handler_get();
        ble_light_set_mode((uint8_t*)mac_addr, mode);
        if(evt_handler){
            evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, SENSOR_TYPE_LIGHT, SENSOR_RADIO_TYPE_BLE, 0);
        }  
    }
}

void pb_blt_status_handle(uint8_t* buf, uint8_t channel_id)
{
    ble_status_cmd_st* ble_status_cmd_data = (ble_status_cmd_st*)buf;
    if (ble_status_cmd_data->cmd_type == enum_BLE_NOTICE_CMD) {
        if (ble_status_cmd_data->service_type == PERIPHERAL_SERVICE_TYPE_PST_BLE_LIGHT_INBIKE) {
            if (ble_status_cmd_data->sub_service_type == BLT_INBIKE_SERVICE_BIS_LEFT_TIME) {
                uint32_t    left_time = 0;
                left_time = (ble_status_cmd_data->reserved3[3]
                | (uint32_t)ble_status_cmd_data->reserved3[4] << 8
                | (uint32_t)ble_status_cmd_data->reserved3[5] << 16
                | (uint32_t)ble_status_cmd_data->reserved3[6] << 24);
                pb_light_lefttime_update(channel_id, left_time);
            }
            else if (ble_status_cmd_data->sub_service_type == BLT_INBIKE_SERVICE_BIS_MODE_CUR) {
                pb_light_mode_cur_update(channel_id, ble_status_cmd_data->status);
            }
            else if (ble_status_cmd_data->sub_service_type == BLT_INBIKE_SERVICE_BIS_MODE_SUP) {
                const uint8_t* mac_addr = ble_periph_nus_c_channel_get_mac_addr(channel_id);
                if (mac_addr) {
                    blt_mode_support_request(mac_addr);
                }
            }
            else if (ble_status_cmd_data->sub_service_type == BLT_INBIKE_SERVICE_BIS_SMT_CFG) {
                const uint8_t* mac_addr = ble_periph_nus_c_channel_get_mac_addr(channel_id);
                if (mac_addr) {
                    blt_cfg_support_request(mac_addr);
                }            
            }
            else if (ble_status_cmd_data->sub_service_type == BLT_INBIKE_SERVICE_BIS_BAT_PCT) {
                if(ble_status_cmd_data->status == 0xff)
                {
                    sensor_module_evt_handler evt_handler = sensor_module_evt_handler_get();
                    if (evt_handler)
                    {
                        evt_handler(EVENT_SENSOR_LOW_POWER, NULL, SENSOR_TYPE_LIGHT, SENSOR_RADIO_TYPE_BLE, 0);
                    }
                }
                else 
                {
                    pb_light_bat_pct_set(channel_id, ble_status_cmd_data->status);
                }
            }
        }
    }
    else if(ble_status_cmd_data->cmd_type == enum_BLE_STATUS_CMD) {
        ble_periph_contact_release_buffer(channel_id);
    }
}


static bool repeated_light_mode_decode(pb_istream_t* stream, const pb_field_t* field, void** arg)
{
    blt_mode_sup** ptr = (blt_mode_sup**)arg;
    blt_inbike_mode mode;
    if (!pb_decode(stream, blt_inbike_mode_fields, &mode))
        return false;
    (**ptr).mode = mode.mode;
    (**ptr).enable = mode.enable;
    (**ptr).serial_number = mode.serial_number;
    (*ptr)++;
    return true;
}

static bool repeated_light_cfg_decode(pb_istream_t* stream, const pb_field_t* field, void** arg)
{
    blt_cfg_sup** ptr = (blt_cfg_sup**)arg;
    blt_inbike_smtcfg cfg;
    if (!pb_decode(stream, blt_inbike_smtcfg_fields, &cfg))
        return false;
    (**ptr).cfg = cfg.smt_cfg;
    (**ptr).cfg_status = cfg.status;
    (*ptr)++;
    return true;
}


void pb_blt_decode_handle(uint8_t* pb_buffer, uint16_t buffer_length, END_TYPE end_type, uint8_t channel_id)
{
    uint8_t status = false;
    blt_mode_sup mode_sup[BLT_LIGHT_MODE_MAX];
    blt_cfg_sup cfg_sup[BLT_LIGHT_CFG_MAX];
    blt_inbike_message_format message_format;
    memset(&message_format, 0, sizeof(blt_inbike_message_format));
    memset(mode_sup, 0x00, sizeof(mode_sup));
    memset(cfg_sup, 0x00, sizeof(cfg_sup));
    message_format.message.mode_sup.mode.arg = mode_sup;
    message_format.message.mode_sup.mode.funcs.decode = repeated_light_mode_decode;
    message_format.message.cfg_sup.smtcfg.arg = cfg_sup;
    message_format.message.cfg_sup.smtcfg.funcs.decode = repeated_light_cfg_decode;

    pb_istream_t decode_stream = pb_istream_from_buffer(pb_buffer, buffer_length);
    status = pb_decode(&decode_stream, blt_inbike_message_format_fields, &message_format);
    if (status)
    {
        switch(message_format.operate_type)
        {
            case PERIPHERAL_OPERATE_TYPE_POT_SET:
                break;
            case PERIPHERAL_OPERATE_TYPE_POT_GET:
                if (message_format.blt_service == BLT_INBIKE_SERVICE_BIS_PROTO_VER)
                {
                    pb_light_proto_ver_set(channel_id, message_format.message.proto_ver);
                }
                else if (message_format.blt_service == BLT_INBIKE_SERVICE_BIS_LIGHT_TYPE)
                {
                    pb_light_type_set(channel_id, message_format.message.light_type);
                }
                else if (message_format.blt_service == BLT_INBIKE_SERVICE_BIS_MODE_SUP)
                {
                    pb_light_mode_sup_set(channel_id, mode_sup, BLT_LIGHT_MODE_MAX);
                }
                else if (message_format.blt_service == BLT_INBIKE_SERVICE_BIS_MODE_CUR) {
                    pb_light_cur_mode_set(channel_id, message_format.message.cur_mode);
                }
                else if (message_format.blt_service == BLT_INBIKE_SERVICE_BIS_SMT_CFG)
                {
                    pb_light_cfg_sup_set(channel_id, cfg_sup, BLT_LIGHT_CFG_MAX);
                }
                else if (message_format.blt_service == BLT_INBIKE_SERVICE_BIS_LEFT_TIME)
                {
                    pb_light_left_time_set(channel_id, message_format.message.left_time);
                }
                else if (message_format.blt_service == BLT_INBIKE_SERVICE_BIS_BAT_PCT)
                {
                    pb_light_bat_pct_set(channel_id, message_format.message.bat_pct);
                }
                else if (message_format.blt_service == BLT_INBIKE_SERVICE_BIS_ALL)
                {
                    pb_light_proto_ver_set(channel_id, message_format.message.proto_ver);
                    pb_light_type_set(channel_id, message_format.message.light_type);
                    pb_light_mode_sup_set(channel_id, mode_sup, BLT_LIGHT_MODE_MAX);
                    pb_light_cur_mode_set(channel_id, message_format.message.cur_mode);
                    pb_light_cfg_sup_set(channel_id, cfg_sup, BLT_LIGHT_CFG_MAX);
                    pb_light_left_time_set(channel_id, message_format.message.left_time);
                    pb_light_bat_pct_set(channel_id, message_format.message.bat_pct);
                }
                break;
            case PERIPHERAL_OPERATE_TYPE_POT_DEL:
                break;
            case PERIPHERAL_OPERATE_TYPE_POT_CON:
                break;
            default:
                break;
        }
        ble_periph_contact_release_buffer(channel_id);
    }
}


void blt_light_type_request(const uint8_t* ble_mac)
{
    uint8_t channel;
    if (ble_periph_nus_c_channel_get(ble_mac, &channel)) {
        ble_periph_contact item;
        if(ble_periph_contact_request_buffer(&item)){
            uint8_t pb_crc = 0;
            uint16_t length = 0;
            blt_inbike_message_format message_format;
            memset(&message_format, 0, sizeof(blt_inbike_message_format));

            message_format.service_type = PERIPHERAL_SERVICE_TYPE_PST_BLE_LIGHT_INBIKE;
            message_format.operate_type = PERIPHERAL_OPERATE_TYPE_POT_GET;

            message_format.has_blt_service = true;
            message_format.blt_service = BLT_INBIKE_SERVICE_BIS_LIGHT_TYPE;

            pb_ostream_t encode_stream = pb_ostream_from_buffer(&item.data[sizeof(ble_end_cmd_st)], NUS_C_PERIPH_TX_BUFF_SIZE);
            pb_encode(&encode_stream, blt_inbike_message_format_fields, &message_format);

            length = encode_stream.bytes_written;
            pb_crc = CRC_Calc8_Table_L(&item.data[sizeof(ble_end_cmd_st)], length);

            ble_periph_contact_end_cmd_construct(item.data,message_format.service_type, message_format.blt_service, \
                message_format.operate_type, 0, length, pb_crc);
            item.length = length + sizeof(ble_status_cmd_st);
            ble_periph_contact_mailbox_queue_data_push(&item, channel);
        }
    }
}

void blt_light_curmode_request(const uint8_t* ble_mac)
{
    uint8_t channel;
    if (ble_periph_nus_c_channel_get(ble_mac, &channel)) {
        ble_periph_contact item;
        if(ble_periph_contact_request_buffer(&item)){
            uint8_t pb_crc = 0;
            uint16_t length = 0;
            blt_inbike_message_format message_format;
            memset(&message_format, 0, sizeof(blt_inbike_message_format));

            message_format.service_type = PERIPHERAL_SERVICE_TYPE_PST_BLE_LIGHT_INBIKE;
            message_format.operate_type = PERIPHERAL_OPERATE_TYPE_POT_GET;

            message_format.has_blt_service = true;
            message_format.blt_service = BLT_INBIKE_SERVICE_BIS_MODE_CUR;

            pb_ostream_t encode_stream = pb_ostream_from_buffer(&item.data[sizeof(ble_end_cmd_st)], NUS_C_PERIPH_TX_BUFF_SIZE);
            pb_encode(&encode_stream, blt_inbike_message_format_fields, &message_format);

            length = encode_stream.bytes_written;
            pb_crc = CRC_Calc8_Table_L(&item.data[sizeof(ble_end_cmd_st)], length);

            ble_periph_contact_end_cmd_construct(item.data,message_format.service_type, message_format.blt_service, \
                message_format.operate_type, 0, length, pb_crc);
            item.length = length + sizeof(ble_status_cmd_st);
            ble_periph_contact_mailbox_queue_data_push(&item, channel);
        }
    }
}


void blt_mode_support_request(const uint8_t* ble_mac)
{
    uint8_t channel;
    if (ble_periph_nus_c_channel_get(ble_mac, &channel)) {
        ble_periph_contact item;
        if(ble_periph_contact_request_buffer(&item)){
            uint8_t pb_crc = 0;
            uint16_t length = 0;
            blt_inbike_message_format message_format;
            memset(&message_format, 0, sizeof(blt_inbike_message_format));

            message_format.service_type = PERIPHERAL_SERVICE_TYPE_PST_BLE_LIGHT_INBIKE;
            message_format.operate_type = PERIPHERAL_OPERATE_TYPE_POT_GET;

            message_format.has_blt_service = true;
            message_format.blt_service = BLT_INBIKE_SERVICE_BIS_MODE_SUP;

            pb_ostream_t encode_stream = pb_ostream_from_buffer(&item.data[sizeof(ble_end_cmd_st)], NUS_C_PERIPH_TX_BUFF_SIZE);
            pb_encode(&encode_stream, blt_inbike_message_format_fields, &message_format);

            length = encode_stream.bytes_written;
            pb_crc = CRC_Calc8_Table_L(&item.data[sizeof(ble_end_cmd_st)], length);

            ble_periph_contact_end_cmd_construct(item.data,message_format.service_type, message_format.blt_service, \
                message_format.operate_type, 0, length, pb_crc);
            item.length = length + sizeof(ble_status_cmd_st);
            ble_periph_contact_mailbox_queue_data_push(&item, channel);
        }
    }
}

void blt_cfg_support_request(const uint8_t* ble_mac)
{
    uint8_t channel;
    if (ble_periph_nus_c_channel_get(ble_mac, &channel)) {
        ble_periph_contact item;
        if(ble_periph_contact_request_buffer(&item)){
            uint8_t pb_crc = 0;
            uint16_t length = 0;
            blt_inbike_message_format message_format;
            memset(&message_format, 0, sizeof(blt_inbike_message_format));

            message_format.service_type = PERIPHERAL_SERVICE_TYPE_PST_BLE_LIGHT_INBIKE;
            message_format.operate_type = PERIPHERAL_OPERATE_TYPE_POT_GET;

            message_format.has_blt_service = true;
            message_format.blt_service = BLT_INBIKE_SERVICE_BIS_SMT_CFG;

            pb_ostream_t encode_stream = pb_ostream_from_buffer(&item.data[sizeof(ble_end_cmd_st)], NUS_C_PERIPH_TX_BUFF_SIZE);
            pb_encode(&encode_stream, blt_inbike_message_format_fields, &message_format);

            length = encode_stream.bytes_written;
            pb_crc = CRC_Calc8_Table_L(&item.data[sizeof(ble_end_cmd_st)], length);

            ble_periph_contact_end_cmd_construct(item.data,message_format.service_type, message_format.blt_service, \
                message_format.operate_type, 0, length, pb_crc);
            item.length = length + sizeof(ble_status_cmd_st);
            ble_periph_contact_mailbox_queue_data_push(&item, channel);
        }
    }
}

void blt_all_msg_request(const uint8_t* ble_mac)
{
    uint8_t channel;
    if (ble_periph_nus_c_channel_get(ble_mac, &channel)) {
        ble_periph_contact item;
        if(ble_periph_contact_request_buffer(&item)){
            uint8_t pb_crc = 0;
            uint16_t length = 0;
            blt_inbike_message_format message_format;
            memset(&message_format, 0, sizeof(blt_inbike_message_format));

            message_format.service_type = PERIPHERAL_SERVICE_TYPE_PST_BLE_LIGHT_INBIKE;
            message_format.operate_type = PERIPHERAL_OPERATE_TYPE_POT_GET;

            message_format.has_blt_service = true;
            message_format.blt_service = BLT_INBIKE_SERVICE_BIS_ALL;

            pb_ostream_t encode_stream = pb_ostream_from_buffer(&item.data[sizeof(ble_end_cmd_st)], NUS_C_PERIPH_TX_BUFF_SIZE);
            pb_encode(&encode_stream, blt_inbike_message_format_fields, &message_format);

            length = encode_stream.bytes_written;
            pb_crc = CRC_Calc8_Table_L(&item.data[sizeof(ble_end_cmd_st)], length);

            ble_periph_contact_end_cmd_construct(item.data,message_format.service_type, message_format.blt_service, \
                message_format.operate_type, 0, length, pb_crc);
            item.length = length + sizeof(ble_status_cmd_st);
            ble_periph_contact_mailbox_queue_data_push(&item, channel);
        }
    }
}


//智能功能联动数据发送
static bool smt_trigger_user_data_encode(pb_ostream_t* stream, const pb_field_t* field, void* const* arg)
{
    uint8_t status = false;
    user_data_trans_t *p_data = (user_data_trans_t*)(*arg);
    blt_smtcfg_trigger user_data_send;
    for (uint8_t i = 0; i < p_data->num; i++)
    {
        user_data_send.has_data_type = true;
        user_data_send.has_user_data = true;
        user_data_send.data_type = (*p_data).user_data[i].data_type;
        user_data_send.user_data = (*p_data).user_data[i].data;
        status = pb_encode_tag_for_field(stream, field);
        status &= pb_encode_submessage(stream, blt_smtcfg_trigger_fields, &user_data_send);
    }
    return status;
}


static void blt_smt_config_trigger_set(const uint8_t *mac_addr, user_data_trans_t *user_data_trans)
{
    uint8_t channel;
    if (ble_periph_nus_c_channel_get(mac_addr, &channel)) {
        ble_periph_contact item;
        if(ble_periph_contact_request_buffer(&item)){
            uint8_t pb_crc = 0;
            uint16_t length = 0;
            blt_inbike_message_format message_format;
            memset(&message_format, 0, sizeof(blt_inbike_message_format));

            message_format.service_type = PERIPHERAL_SERVICE_TYPE_PST_BLE_LIGHT_INBIKE;
            message_format.operate_type = PERIPHERAL_OPERATE_TYPE_POT_SET;

            message_format.has_blt_service = true;
            message_format.blt_service = BLT_INBIKE_SERVICE_BIS_SMT_CFG;
            message_format.has_message = true;
            message_format.message.has_inbike_data = true;
            message_format.message.inbike_data.inbike_data.arg= user_data_trans;
            message_format.message.inbike_data.inbike_data.funcs.encode = smt_trigger_user_data_encode;

            pb_ostream_t encode_stream = pb_ostream_from_buffer(&item.data[sizeof(ble_end_cmd_st)], NUS_C_PERIPH_TX_BUFF_SIZE);
            pb_encode(&encode_stream, blt_inbike_message_format_fields, &message_format);

            length = encode_stream.bytes_written;
            pb_crc = CRC_Calc8_Table_L(&item.data[sizeof(ble_end_cmd_st)], length);

            ble_periph_contact_end_cmd_construct(item.data,message_format.service_type, message_format.blt_service, \
                message_format.operate_type, 0, length, pb_crc);
            item.length = length + sizeof(ble_status_cmd_st);
            ble_periph_contact_mailbox_queue_data_push(&item, channel);
        }
    }
}


void blt_smtcfg_trigger_set(const uint8_t* mac_addr, user_data_trans_t *user_data_trans)
{
    if (mac_addr && user_data_trans)
    {
        blt_smt_config_trigger_set(mac_addr, user_data_trans);
    }
}


void blt_smtcfg_sleep_set(const uint8_t* mac_addr, uint8_t data)
{
    if (mac_addr)
    {
        if (blt_light_cfg_sup_get(mac_addr, BLT_INBIKE_CONFIG_TYPE_BICT_SYNC_SLEEP) == BLT_INBIKE_CFG_STATUS_BICS_CFG_FOL)
        {
            user_data_trans_t user_data_trans;
            user_data_trans.user_data[0].data_type = BLT_INBIKE_DATA_TYPE_SLEEP;
            user_data_trans.user_data[0].data = data;
            user_data_trans.num = 1;
            blt_smtcfg_trigger_set(mac_addr, &user_data_trans);
        }
    }
}

void blt_smtcfg_pwroff_set(const uint8_t* mac_addr, uint8_t data)
{
    if (mac_addr)
    {
        if (blt_light_cfg_sup_get(mac_addr, BLT_INBIKE_CONFIG_TYPE_BICT_SYNC_OFF))
        {
            user_data_trans_t user_data_trans;
            user_data_trans.user_data[0].data_type = BLT_INBIKE_DATA_TYPE_POWEROFF;
            user_data_trans.user_data[0].data = data;
            user_data_trans.num = 1;
            blt_smtcfg_trigger_set(mac_addr, &user_data_trans);
        }
    }
}

void blt_smtcfg_lightness_set(const uint8_t* mac_addr, uint8_t data)
{
    if (mac_addr)
    {
        if (blt_light_cfg_sup_get(mac_addr, BLT_INBIKE_CONFIG_TYPE_BICT_SYNC_LIGHT))
        {
            user_data_trans_t user_data_trans;
            user_data_trans.user_data[0].data_type = BLT_INBIKE_DATA_TYPE_LIGHTNESS;
            user_data_trans.user_data[0].data = data;
            user_data_trans.num = 1;
            blt_smtcfg_trigger_set(mac_addr, &user_data_trans);
        }
    }
}


void blt_light_mode_set(const uint8_t* mac_addr, uint8_t mode)
{
    uint8_t channel;
    if(ble_periph_nus_c_channel_get(mac_addr,&channel)){
        ble_periph_contact item;
        if(ble_periph_contact_request_buffer(&item)){
            uint8_t pb_crc = 0;
            uint16_t length = 0;
            blt_inbike_message_format message_format;
            memset(&message_format, 0, sizeof(blt_inbike_message_format));

            message_format.service_type = PERIPHERAL_SERVICE_TYPE_PST_BLE_LIGHT_INBIKE;
            message_format.operate_type = PERIPHERAL_OPERATE_TYPE_POT_SET;

            message_format.has_blt_service = true;
            message_format.blt_service = BLT_INBIKE_SERVICE_BIS_MODE_CUR;
            message_format.has_message = true;
            message_format.message.has_cur_mode = true;
            message_format.message.cur_mode = mode;

            pb_ostream_t encode_stream = pb_ostream_from_buffer(&item.data[sizeof(ble_end_cmd_st)], NUS_C_PERIPH_TX_BUFF_SIZE);
            pb_encode(&encode_stream, blt_inbike_message_format_fields, &message_format);

            length = encode_stream.bytes_written;
            pb_crc = CRC_Calc8_Table_L(&item.data[sizeof(ble_end_cmd_st)], length);

            ble_periph_contact_end_cmd_construct(item.data,message_format.service_type, message_format.blt_service, \
                message_format.operate_type, 0, length, pb_crc);
            item.length = length + sizeof(ble_status_cmd_st);

            ble_periph_contact_mailbox_queue_data_push(&item, channel);
        }
    }   
}





















