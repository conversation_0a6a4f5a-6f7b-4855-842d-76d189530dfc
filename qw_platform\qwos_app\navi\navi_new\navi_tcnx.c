/************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   navi_tcnx.c
@Time    :   2025/05/12 19:41:53
*
************************************************************/
#include <string.h>
#include <stddef.h>
#include <stdbool.h>
#include "navi_tcnx.h"

#define NAVIROUT_HEAD_PADDING      (sizeof(uint32_t) * 3)
#define NAVIROUT_DATA_SIZE         (52) //(sizeof(NaviRouteData)) it maybe a bug, not set to 56
#define NAVIROUT_FILELINE_BUF_SIZE (100)   //must be more than NAVIROUT_HEAD_PADDING+NAVIROUT_DATA_SIZE

//读取tcnx文件头，用于检查文件类型和版本信息
int tcnx_header_read(TcnxReader *self, uint8_t *header)
{
    if (self == NULL || header == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 0) != QW_OK)
    {
        return -1;
    }

    UINT br = 0;

    if (qw_f_read(self->fp, header, 32, &br) != QW_OK || br != 32)
    {
        return -1;
    }

    return 0;
}

//读取tcnx线路整体数据
int tcnx_route_data_read(TcnxReader *self, NaviRouteData *data)
{
    if (self == NULL || data == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 32) != QW_OK)
    {
        return -1;
    }

    uint8_t buf[NAVIROUT_FILELINE_BUF_SIZE] = { 0 };

    UINT br = 0;

    if (qw_f_read(self->fp, buf, NAVIROUT_HEAD_PADDING + NAVIROUT_DATA_SIZE, &br) != QW_OK ||
            br != NAVIROUT_HEAD_PADDING + NAVIROUT_DATA_SIZE)
    {
        return -1;
    }

    const uint32_t check_code = *((uint32_t *)buf);
    const uint32_t size = *((uint32_t *)buf + 1);
    const uint32_t num = *((uint32_t *)buf + 2);

    if (check_code != 0 || size != NAVIROUT_DATA_SIZE || num != 1)
    {
        return -1;
    }
    memcpy(data, buf + 12, NAVIROUT_DATA_SIZE);   //resure sizeof NaviRouteData is 52

    return 0;
}

//读取线路全局路点
int tcnx_route_wp_sample_read(TcnxReader *self, NaviRouteWpSample *sample)
{
    if (self == NULL || sample == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 96) != QW_OK)
    {
        return -1;
    }

    uint32_t buf[3] = { 0 };

    UINT br = 0;

    if (qw_f_read(self->fp, buf, 12, &br) != QW_OK || br != 12)
    {
        return -1;
    }

    const uint32_t check_code = buf[0];
    const uint32_t size = buf[1];
    const uint32_t num = buf[2];

    if (check_code != 1 || size != 24 || num > NAVI_ROUTE_WP_SAMPLES_NUM)
    {
        return -1;
    }

    sample->len = num;

    if (sample->len == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const UINT btr = sample->len * 24;

    if (qw_f_read(self->fp, sample->wpdc_buf, btr, &br) != QW_OK || br != btr)
    {
        return -1;
    }

    return 0;
}

//读取线路分段信息
int tcnx_route_segment_array_read(TcnxReader *self, NaviRouteSegmentArray *seg_array)
{
    if (self == NULL || seg_array == NULL)
    {
        return -1;
    }

    //32 + (12 + 52) + (12 + 300 * 24) = 7308
    if (qw_f_lseek(self->fp, 7308) != QW_OK)
    {
        return -1;
    }

    uint32_t buf[3] = { 0 };

    UINT br = 0;

    if (qw_f_read(self->fp, buf, 12, &br) != QW_OK || br != 12)
    {
        return -1;
    }

    const uint32_t check_code = buf[0];
    const uint32_t size = buf[1];
    const uint32_t num = buf[2];

    if (check_code != 2 || size != 48 || num > NAVI_ROUTE_SEGMENTS_NUM)
    {
        return -1;
    }

    seg_array->len = num;

    if (seg_array->len == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const UINT btr = seg_array->len * 48;

    if (qw_f_read(self->fp, seg_array->segments, btr, &br) != QW_OK || br != btr)
    {
        return -1;
    }

    return 0;
}

//读取线路路点（压缩后）数量
int tcnx_route_wp_compressed_num_read(TcnxReader *self, uint32_t *wp_num)
{
    if (self == NULL || wp_num == NULL)
    {
        return -1;
    }

    //32 + (12 + 52) + (12 + 300 * 24) + (12 + 48 * 100) = 12120
    if (qw_f_lseek(self->fp, 12120) != QW_OK)
    {
        return -1;
    }

    uint32_t buf[3] = { 0 };

    UINT br = 0;

    if (qw_f_read(self->fp, buf, 12, &br) != QW_OK || br != 12)
    {
        return -1;
    }

    const uint32_t check_code = buf[0];
    const uint32_t size = buf[1];
    const uint32_t num = buf[2];

    if (check_code != 3 || size != 24)
    {
        return -1;
    }

    *wp_num = num;

    return 0;
}

//读取指定范围的线路路点数据
int tcnx_route_wp_compressed_data_read(TcnxReader *self, uint32_t start, uint32_t end, uint32_t wp_num, NaviWaypointDc *wpdc_buf)
{
    if (self == NULL || wpdc_buf == NULL)
    {
        return -1;
    }

    if (start > end || end > wp_num)
    {
        return -1;
    }

    const uint32_t ntr = end - start;

    if (ntr == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    //32 + (12 + 52) + (12 + 300 * 24) + (12 + 48 * 100) + 12 = 12132
    const uint32_t offset = 12132 + start * 24;

    if (qw_f_lseek(self->fp, offset) != QW_OK)
    {
        return -1;
    }

    const UINT btr = ntr * 24;

    UINT br = 0;

    if (qw_f_read(self->fp, wpdc_buf, btr, &br) != QW_OK || br != btr)
    {
        return -1;
    }

    return 0;
}

//写入文件头占位符
int tcnx_header_placeholder_write(TcnxWriter *self)
{
    if (self == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 0) != QW_OK)
    {
        return -1;
    }

    const uint8_t buf[32] = {
        'S', 'h', 'i', 't', ' ', 'H', 'a', 'p', 'p', 'e', 'n', 's', '.',
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 32, &bw) != QW_OK || bw != 32)
    {
        return -1;
    }

    return 0;
}

//写入tcnx文件头
int tcnx_header_write(TcnxWriter *self)
{
    if (self == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 0) != QW_OK)
    {
        return -1;
    }

    const uint8_t buf[32] = {
        'T', 'C', 'N', 'X', NAVI_FILE_MAJOR_VERSION, NAVI_FILE_MINOR_VERSION,
        'D', 'e', 's', 'i', 'g', 'n', 'e', 'd', ' ', 'b', 'y', ' ', 'J', 'u', 'n',
        'j', 'i', 'e', ' ', 'D', 'i', 'n', 'g', 0, 0, 0,
    };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 32, &bw) != QW_OK || bw != 32)
    {
        return -1;
    }

    return 0;
}

//写入tcnx线路整体数据
int tcnx_route_data_write(TcnxWriter *self, const NaviRouteData *data)
{
    if (self == NULL || data == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 32) != QW_OK)
    {
        return -1;
    }

    const uint32_t buf[3] = { 0, NAVIROUT_DATA_SIZE, 1 };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, NAVIROUT_HEAD_PADDING, &bw) != QW_OK || bw != NAVIROUT_HEAD_PADDING)
    {
        return -1;
    }

    if (qw_f_write(self->fp, data, NAVIROUT_DATA_SIZE, &bw) != QW_OK || bw != NAVIROUT_DATA_SIZE)
    {
        return -1;
    }

    return 0;
}

//写入tcnx线路全局路点
int tcnx_route_wp_sample_write(TcnxWriter *self, const NaviRouteWpSample *sample)
{
    if (self == NULL || sample == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 96) != QW_OK)
    {
        return -1;
    }

    const uint32_t buf[3] = { 1, 24, sample->len };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    if (sample->len == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const UINT btw = sample->len * 24;

    if (qw_f_write(self->fp, sample->wpdc_buf, btw, &bw) != QW_OK || bw != btw)
    {
        return -1;
    }

    return 0;
}

//写入tcnx线路分段信息
int tcnx_route_segment_array_write(TcnxWriter *self, const NaviRouteSegmentArray *seg_array)
{
    if (self == NULL || seg_array == NULL)
    {
        return -1;
    }

    //32 + (12 + 52) + (12 + 300 * 24) = 7308
    if (qw_f_lseek(self->fp, 7308) != QW_OK)
    {
        return -1;
    }

    const uint32_t buf[3] = { 2, 48, seg_array->len };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    if (seg_array->len == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const UINT btw = seg_array->len * 48;

    if (qw_f_write(self->fp, seg_array->segments, btw, &bw) != QW_OK || bw != btw)
    {
        return -1;
    }

    return 0;
}

//写入tcnx线路路点（压缩后）数量
int tcnx_route_wp_compressed_num_write(TcnxWriter *self, uint32_t num)
{
    if (self == NULL)
    {
        return -1;
    }

    //32 + (12 + 52) + (12 + 300 * 24) + (12 + 48 * 100) = 12120
    if (qw_f_lseek(self->fp, 12120) != QW_OK)
    {
        return -1;
    }

    const uint32_t buf[3] = { 3, 24, num };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    return 0;
}

//写入tcnx一个路点（压缩后）数据
int tcnx_route_wp_compressed_data_write(TcnxWriter *self, const NaviWaypointDc *wpdc)
{
    if (self == NULL || wpdc == NULL)
    {
        return -1;
    }

    UINT bw = 0;

    if (qw_f_write(self->fp, wpdc, 24, &bw) != QW_OK || bw != 24)
    {
        return -1;
    }

    return 0;
}

//根据index从路点list中获取指定路点数据
int navi_route_wp_list_get(NaviRouteWpList *self, uint32_t idx, NaviWaypointDc *output)
{
    if (self == NULL || output == NULL)
    {
        return -1;
    }

    if (idx >= self->len)
    {
        return -1;
    }

    //缓存命中
    for (uint32_t i = 0; i < self->cache.len; i++)
    {
        const uint32_t start = self->cache.wpdc_buf[i].range.start;
        const uint32_t end = self->cache.wpdc_buf[i].range.end;
        if (idx >= start && idx < end)
        {
            for (uint32_t j = start; j < end; j++)
            {
                if (idx == j)
                {
                    //注意索引的计算，匹配后需要确定是第几个元素再去取，不能使用原索引
                    navi_waypoint_dc_copy(output, &self->cache.wpdc_buf[i].buf[j-start]);
                    return 0;
                }
            }
        }
    }

    //缓存未命中，则加载和缓存
    NaviWaypointDcBuf *wpdc_buf = &self->cache.wpdc_buf[self->cache.next];

    //通常也会使用前一个元素，故一起加载
    wpdc_buf->range.start = idx > 0 ? idx - 1 : idx;
    wpdc_buf->range.end = wpdc_buf->range.start + wpdc_buf->capacity;
    if (wpdc_buf->range.end > self->len)
    {
        wpdc_buf->range.end = self->len;
    }

    if (tcnx_route_wp_compressed_data_read(self->cache.tcnx_reader, wpdc_buf->range.start, wpdc_buf->range.end, self->len, wpdc_buf->buf) != 0)
    {
        return -1;
    }

    //同样地，注意索引的计算
    navi_waypoint_dc_copy(output, &wpdc_buf->buf[idx - wpdc_buf->range.start]);

    //更新启用的路点缓冲区数量
    if (self->cache.len < self->cache.capacity)
    {
        self->cache.len += 1;
    }

    //更新下一个使用的路点缓冲区
    self->cache.next += 1;
    if (self->cache.next >= self->cache.capacity)
    {
        self->cache.next = 0;
    }

    return 0;
}

//重置路点缓冲区
void navi_waypoint_dc_buf_reset(NaviWaypointDcBuf *self)
{
    if (self != NULL)
    {
        self->range.start = 0;
        self->range.end = 0;
    }
}

//重置路点缓存
void navi_waypoint_dc_cache_reset(NaviWaypointDcCache *self)
{
    if (self != NULL)
    {
        self->len = 0;
        self->next = 0;

        for (uint32_t i = 0; i < self->capacity; i++)
        {
            navi_waypoint_dc_buf_reset(&self->wpdc_buf[i]);
        }
    }
}

//重置路点list
void navi_route_wp_list_reset(NaviRouteWpList *self)
{
    if (self != NULL)
    {
        navi_waypoint_dc_cache_reset(&self->cache);
    }
}
