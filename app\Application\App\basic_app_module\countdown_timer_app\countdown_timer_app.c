/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   countdown_timer_app.c
@Time    :   2024/12/28 15:04:47
* 
**************************************************************************/

#include "countdown_timer_app.h"
#include "gui_thread.h"
#include "qw_time_util.h"
#include "qw_timer.h"
#include "view_page_model.h"
#include "gui_event_service.h"
#include "cfg_header_def.h"

static COUNTDOWN_TIMER_TYPE countdown_timer_type = COUNTDOWN_TIMER__MAX;
static qw_timer countdown_timer_id_s;                  // 倒计时定时器ID
static bool countdown_timer_app_inited = false;    // 倒计时模块是否初始化
static bool countdown_timer_app_started = false;    // 倒计时模块是否运行
static gui_evt_service_page_command_t countdown_timer_page_command;

static void countdown_timer_handler(void* parameter);   // 倒计时定时器回调函数

void countdown_timer_app_init(void)
{
    if(!countdown_timer_app_inited)
    {
        qw_timer_init(&countdown_timer_id_s, QW_TIMER_FLAG_ONE_SHOT | QW_TIMER_FLAG_SOFT_TIMER,
                      countdown_timer_handler);

        countdown_timer_app_inited = true;
    }
}

void countdown_timer_start(COUNTDOWN_TIMER_TYPE type)
{
    if(!countdown_timer_app_inited)
    {
        countdown_timer_app_init();
    }

    countdown_timer_type = type;

    if(countdown_timer_app_inited)
    {
        if(countdown_timer_app_started)
        {
            qw_timer_stop(&countdown_timer_id_s);
            countdown_timer_app_started = false;
        }

        int time_tick = 0;
        if(countdown_timer_type == COUNTDOWN_TIMER_GPS_WAIT)
        {
            time_tick = COUNTDOWN_TIMER_GPS_WAIT_NUM;
        }
        else if(countdown_timer_type == COUNTDOWN_TIMER_START_WAIT)
        {
            time_tick = COUNTDOWN_TIMER_START_WAIT_NUM;
        }

        if(time_tick > 0)
        {
            qw_timer_start(&countdown_timer_id_s, time_tick, (void *)countdown_timer_type, "countdown_timer");
            countdown_timer_app_started = true;
        }
        else
        {
            countdown_timer_type = COUNTDOWN_TIMER__MAX;
        }
    }
}

void countdown_timer_stop()
{
    if(countdown_timer_app_started && countdown_timer_app_inited)
    {
        countdown_timer_app_started = false;
        countdown_timer_type = COUNTDOWN_TIMER__MAX;
        qw_timer_stop(&countdown_timer_id_s);
    }
}

static void countdown_timer_handler(void* parameter)
{
    COUNTDOWN_TIMER_TYPE tmp = (COUNTDOWN_TIMER_TYPE)parameter;

    countdown_timer_page_command.page = "SportStart";
    countdown_timer_page_command.cmd = 5; //SportStart_CMD::SET_COUNTDOWN_TIME
    countdown_timer_page_command.user_data = (void *)COUNTDOWN_TIMER_EVENT_OUT_SPORT;
    submit_gui_event(GUI_EVT_SERVICE_PAGE_COMMAND, 0, &countdown_timer_page_command);
}







