/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   MvcApp.cpp
@Time    :   2024/12/10 10:58:31
*
**************************************************************************/


#include "MvcApp.h"
#include "rtconfig.h"
#include "system_utils.h"
#include <new>
#ifndef SIMULATOR
#include "boot_switch.h"
#include "../altitude/service_altitude.h"
#include "../barometer/service_barometer.h"
#include "algo_service_sport_status.h"
#include "beep.h"
//ota
#include "ota.h"
#include "qw_system_params.h"
#include "power_ctl.h"
#include "service_daily_activity.h"
#include "service_health.h"
#endif   // IGS_DEV
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QwGUIKey.h"
#include "../weather/service_weather.h"
#include "Page/Launcher/AodScreen.h"
#include "activity_record/activity_fit_app.h"
#include "backlight_module/backlight_module.h"
#include "battery_srv_module/battery_srv.h"
#include "cfg_header_def.h"
#include "countdown_timer_app/countdown_timer_app.h"
#include "focus_mode_srv/focus_mode_srv.h"
#include "global_button_srv/global_button.h"
#include "gui_event_service.h"
#include "qw_fit_api.h"
#include "qw_ota.h"
#include "qw_time_util.h"
#include "radio/application/ble_test_module/button_debug_cmd.h"
#include "remind_response_app/remind_response_app.h"
#include "watch_lock_srv/watch_lock_srv.h"
#include "workout_lib.h"
#include "GUICtrl/WatchLockService/WatchLockService.h"
#include "hydration_remind/hydration_remind.h"
#include "data_sync_remind/data_sync_remind.h"
#include "../qwos/module/gui/Translate/language_srv.h"
#include "touchgfx_js/touchgfx_js_api.h"
#include "touchgfx_js/jsApp.h"
#include "power_save_srv/power_save_srv.h"
#include "music_control/music_control.h"
#if !defined(SIMULATOR) && defined(BUTTON_DEBUG_MODE) && (BUTTON_DEBUG_MODE == 1)
#include "radio/application/ble_test_module/ui_debug_cmd.h"
#endif

#define WAKE_UP_AUTO_LOCK_TIME 60          // 休眠时间120s唤醒后台屏幕锁定

static int64_t g_cfg_save_last_time = 0;

static uint8_t g_sim_cli_key = UINT8_MAX;   // 模拟按键
static uint8_t g_draw_test_flag = 0;        // draw时间测试
static int64_t g_draw_test_begin_tick = 0;
static int64_t g_draw_test_begin_tick_2 = 0;

static MvcApp factory;
static AodScreen g_aod_screen;

MvcApp::MvcApp()
    : last_sleep_time_(0)
    , is_page_click_deal(false)
#if USE_SLEEP_SCREEN_BUF
    , sleep_awake_(false)
    , snapshot_buffer_(nullptr)
#endif
{
    instance = this;
}

MvcApp *MvcApp::get_mvc_app()
{
    return &factory;
}

#ifdef SIMULATOR
void MvcApp::draw_simulator_circle()
{
    simulator_circle_.getRootContainer().setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    simulator_circle_.getRootContainer().add(simulator_circle_box_);

    simulator_circle_box_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    simulator_circle_box_.setContentAlpha(LV_OPA_TRANSP);
    simulator_circle_box_.setBorderAlpha(LV_OPA_COVER);
    simulator_circle_box_.setBorderSize(1);
    simulator_circle_box_.setBorderColor(LV_COLOR_RED);
    simulator_circle_box_.setRadius(LV_RADIUS_CIRCLE);
    simulator_circle_box_.invalidate();
}
#endif

void MvcApp::getRefreshArea(Rect &area)
{
    area = Rect(0, 0, 0, 0);
    if (cachedDirtyAreas.size())
    {
        area = cachedDirtyAreas[0];
        for (uint16_t i = 1; i < cachedDirtyAreas.size(); i++)
        {
            area.expandToFit(cachedDirtyAreas[i]);
        }
    }
}

void MvcApp::handlePendingScreenTransition()
{
    if (g_draw_test_flag)
    {
        g_draw_test_begin_tick = get_boot_msec();
    }

    switch (manager_.get_switch_event())
    {
    case PAGE_EVENT::INIT:
    {
        manager_.init();

        setdrawCacheEnabled(true);

        appAddaodScreen(g_aod_screen);                             //添加aod层
        appAddScreen((int) MVCLayer::MSG, msg_list_);              //添加弹窗层
        appAddScreen((int) MVCLayer::LOCK, watch_lock_);           //添加锁窗层
#ifdef SIMULATOR
        appAddScreen((int) MVCLayer::CIRCLE, simulator_circle_);   //添加模拟器专用划线层 一个圆圈
        draw_simulator_circle();

        transition_.init_kv();
#endif

        g_aod_screen.init();
        msg_msgbox_init(&msg_list_, &manager_);
        watch_lock_init(&watch_lock_, &manager_);

#ifndef SIMULATOR
        register_sport_status_algo();    //运动状态订阅
        register_altitude_subscribe();   // 高度计订阅
#endif
        activity_app_module_init();

#ifdef IGS_DEV
        service_daily_activity_subscribe();
        service_health_hr_subscribe();
        service_health_remind_hr_subscribe();
        service_health_rest_hr_subscribe();
        service_health_sleep_summary_subscribe();
        service_health_gm_previous_data_subscribe();
        service_health_blood_oxygen_subscribe();   //血氧订阅
        service_health_stress_subscribe();
        service_health_remind_stress_subscribe();
        service_health_hrv_subscribe();
        register_barometer_subscribe();         // 气压计订阅
        hydration_remind_subscribe();          // 喝水提醒
        data_sync_remind_init();              // 数据同步提醒

#endif                                          // IGS_DEV
        init_language_system();               // 语言系统初始化
        MusicCtrl_Init();                      // 音乐控制初始化
        dnd_srv_init();                         //勿扰模式初始化
        sleep_srv_init();                       //睡眠模式初始化
        power_saving_init();                   //省电模式初始化
        backlight_init_app();                   // 初始化背光模块
        remind_response_init();
        weather_init(); // 天气初始化
        crossing_day_check(); // 日程初始化
        init_font_module();
        sync_cfg_version(); // 同步配置版本
#ifndef SIMULATOR
        if (ota_need_upgrade(OTA_FILE_ADD))
        {
            manager_.push(OTA_PAGE); // 跳转到主页面
        }
        else
#else
        if (check_character_buffer())
        {
            init_font_characters(); //升级字库
            manager_.push(OTA_PAGE); // 跳转到升级页面显示进度
        }
        else
#endif   // 模拟器
        {
            manager_.push(START_PAGE); // 跳转到主页面
        }

        g_cfg_save_last_time = get_boot_msec(); // 记录上一次保存配置的时间
        last_sleep_time_ = (uint32_t)get_boot_sec(); // 初始化上一次进入睡眠模式的时间
        break;
    }
    case PAGE_EVENT::UNLOAD:
    {
        suspendEventDispatch();

        // 对旧页面截图, 计算动效
        bool need_transition = true;
        if(msg_list_.msg_is_activate())
        {
            PAGE_TYPE tmp = msg_list_.get_cur_msg_page_type();
            if(tmp == PAGE_TYPE::MSG_TYPE_BOTTOM_TOAST || tmp == PAGE_TYPE::MSG_TYPE_TOAST ||
                tmp == PAGE_TYPE::MSG_TYPE_HALF_MESSAGE || tmp == PAGE_TYPE::MSG_TYPE_HALF_TIP)
            {
                if(msg_list_.get_slider_busy())
                {
                    msg_list_.stop_slider();
                }
            }
            else
            {
                need_transition = false;
            }
        }
        if(need_transition)
        {
            transition_.push(manager_.get_next_page());   // 跟随页面一起推送, 计算动效逻辑
            if (transition_.get_transition_type() != TRANSITION_TYPE::NONE)
            {
                manager_.unload_prepare();
            #if USE_SLEEP_SCREEN_BUF
                if (snapshot_buffer_ == nullptr)
            #endif
                {
                    msg_list_.set_page_transition_flag(true);
                    transition_.create_snapshot(manager_.get_cur_screen());
                }
            }
        }

        if (strcmp(manager_.get_cur_page(), START_PAGE) == 0)
        {
            backlight_wrist_init_app();                 // 初始化抬腕亮屏
            set_backlight_force_on(BK_FORCE_DISABLE);   // 调整强制背光
            backlight_open_app();                       // 激活背光

            msg_msgbox_enable(true);                    // 退出logo页时启用弹窗
            fit_manager_scan_sync();                    // 活动文件摘要同步
        }

        // 各图层退出指令
        // msg_layer.exit_page();

        // 操作解绑
        evt_handle_.unbind_page();

        // 页面析构
        manager_.unload_page();
        // break; // continue to load page
    }
    case PAGE_EVENT::LOAD:
    {
        // 加载新页面, 申请内存
        manager_.load_page();

        // 绑定Screen到GUI平台
        switchScreen(manager_.get_cur_screen());

        // 创建并绑定切换动效
        switchTransition(transition_.create_transition(manager_.get_cur_screen()));

        // 结束页面切换
        manager_.load_over();

#if !defined(SIMULATOR) && defined(BUTTON_DEBUG_MODE) && (BUTTON_DEBUG_MODE == 1)
        ui_report(UI_BLE_DEBUG_REPORT_TYPE_PAGE, manager_.get_cur_page(), strlen(manager_.get_cur_page()) + 1);
#endif
        break;
    }
    default:
    {
        if (!transition_.is_transition_busy())
        {
            manager_.notify();
        }

        int64_t cur_time = get_boot_msec();
        if ((cur_time < g_cfg_save_last_time || cur_time > g_cfg_save_last_time + 1000) &&
                cfg_mark_update_get(enum_cfg_check_all))
        {
            g_cfg_save_last_time = cur_time;
            submit_gui_event(GUI_EVT_SAVE_CFG, 0, NULL);
        }
        break;
    }
    }

    if (g_draw_test_flag)
    {
        rt_kprintf("[MvcApp] handlePendingScreenTransition test:%dms\n", (int) (get_boot_msec() - g_draw_test_begin_tick));
    }
}

void MvcApp::handleTickEvent()
{
    if (g_draw_test_flag)
    {
        g_draw_test_begin_tick = get_boot_msec();
    }

    if (g_aod_screen.is_screen_visable())
    {
        Application::handleTickEvent();
        g_aod_screen.handleTickEvent();
        return;
    }

#if USE_SLEEP_SCREEN_BUF
    if (!sleep_awake_)
#endif
    {
        // watch_lock_.handleTickEvent();
        g_aod_screen.handleTickEvent();
        Application::handleTickEvent();

        evt_handle_.handleTickEvent();

        if (g_sim_cli_key != UINT8_MAX)
        {
            evt_handle_.handleKeyEvent(g_sim_cli_key);
            g_sim_cli_key = UINT8_MAX;
        }
    }

    if (g_draw_test_flag)
    {
        rt_kprintf("[MvcApp] handleTickEvent test:%dms\n", (int) (get_boot_msec() - g_draw_test_begin_tick));
    }
}

void MvcApp::handleKeyEvent(uint8_t c)
{
#ifdef IGS_DEV
    if (strcmp(manager_.get_cur_page(), START_PAGE) != 0
        && strcmp(manager_.get_cur_page(), "ShutdownAnimation") != 0)   //开关机动画页面和OTA、表盘升级页面不处理按键提醒
    {
        if (c == KEY_KNOB_UP || c == KEY_KNOB_DOWN)
        {
            remind_trigger(RRT_B_KNOB, true);
        }
        else if (c == KEY_POWER_PRESS || c == KEY_START_PRESS || c == KEY_BACK_PRESS)
        {
            remind_trigger(RRT_B_BTN, true);
        }
    }
#endif
#ifndef FACTORY_MODE_ENABLE // 工厂模式不执行屏幕锁
    // IGS_DEV
    if(get_watch_lock_state() || watch_lock_.watch_lock_is_activate())
    {
        watch_lock_.handleKeyEvent(c);
        return;
    }
#endif
    // 保存操作行为
    if (c == KEY_CLK_POWER || c == KEY_HOLD_POWER)
    {
        transition_.update_user_move(LAST_USER_MOVE::POWER);
    }
    else if (c == KEY_CLK_BACK)
    {
        transition_.update_user_move(LAST_USER_MOVE::BACK);
    }
    else if (c == KEY_HOLD_BACK)
    {
        transition_.update_user_move(LAST_USER_MOVE::LBACK);
    }
    else if (c == KEY_CLK_START || c == KEY_HOLD_START || c == KEY_LONG_POWER_AND_BACK)
    {
        transition_.update_user_move(LAST_USER_MOVE::START);
    }
    else if (c == KEY_KNOB_UP)
    {
        transition_.update_user_move(LAST_USER_MOVE::UP);
    }
    else if (c == KEY_KNOB_DOWN)
    {
        transition_.update_user_move(LAST_USER_MOVE::DOWN);
    }

    if (msg_list_.msg_is_activate())
    {
        msg_list_.handleKeyEvent(c);
    }
    else
    {
#if ENABLE_GLOABL_BUTTON_EVENT
        user_key_event_submit_page(&c, manager_.get_cur_page(), getPageManager());
#endif
        evt_handle_.handleKeyEvent(c);
    }
}

void MvcApp::handleClickEvent(const ClickEvent &evt)
{
#ifndef FACTORY_MODE_ENABLE // 工厂模式不执行屏幕锁
    if (get_touch_lock() || get_swim_touch_lock())
    {
        return;
    }
    else if(get_watch_lock_state())
    {
        watch_lock_.handleClickEvent(evt);
        return;
    }
#endif
    // 保存操作行为
    if (evt.getType() == ClickEvent::PRESSED)
    {
        transition_.update_user_move(LAST_USER_MOVE::TOUCH);
    }

    if (msg_list_.msg_is_activate())
    {
        ClickEvent evt_tmp(ClickEvent::CANCEL, 0, 0);
        evt_handle_.handleClickEvent(evt_tmp);
        msg_list_.handleClickEvent(evt);
    }
    else
    {
        evt_handle_.handleClickEvent(evt);
    }
}

void MvcApp::handleDragEvent(const DragEvent &evt)
{
#ifndef FACTORY_MODE_ENABLE // 工厂模式不执行屏幕锁
    if (get_touch_lock_state())
    {
        return;
    }
#endif
    backlight_open_app();   // 激活背光

    // 保存操作行为
    transition_.update_user_move(LAST_USER_MOVE::TOUCH);

    if (msg_list_.msg_is_activate())
    {
        msg_list_.handleDragEvent(evt);
    }
    else
    {
        evt_handle_.handleDragEvent(evt);
    }
}

void MvcApp::handleGestureEvent(const GestureEvent &evt)
{
#ifndef FACTORY_MODE_ENABLE // 工厂模式不执行屏幕锁
    if (get_touch_lock_state())
    {
        return;
    }
#endif
    backlight_open_app();   // 激活背光

    // 保存操作行为
    if (evt.getType() == GestureEvent::GestureEventType::SWIPE_VERTICAL)
    {
        if (evt.getVelocity() > GESTURE_EXIT_ACCURACY)
        {
            transition_.update_user_move(LAST_USER_MOVE::DOWN);
        }
        else if (evt.getVelocity() < -GESTURE_EXIT_ACCURACY)
        {
            transition_.update_user_move(LAST_USER_MOVE::UP);
        }
    }
    else if (evt.getType() == GestureEvent::GestureEventType::SWIPE_HORIZONTAL)
    {
        if (evt.getVelocity() > GESTURE_EXIT_ACCURACY)
        {
            transition_.update_user_move(LAST_USER_MOVE::RIGHT);
        }
        else if (evt.getVelocity() < -GESTURE_EXIT_ACCURACY)
        {
            transition_.update_user_move(LAST_USER_MOVE::LEFT);
        }
    }

    if (msg_list_.msg_is_activate())
    {
        msg_list_.handleGestureEvent(evt);
    }
    else
    {
        evt_handle_.handleGestureEvent(evt);
    }
}

void MvcApp::handlePlamEvent()
{
    backlight_close_app();   // 关闭背光
}

void MvcApp::transitionDone()
{
    transition_.free_animation_buff();

    //根据页面设置当前的弹窗场景参数
    msg_msgbox_set_p_msg_list_scope(global_set_msglist_cur_scope(getPageManager()));

    evt_handle_.bind_page(manager_.get_cur_screen());

    msg_list_.set_page_transition_flag(false);

    // 页面绑定完成，通知 PageManager 释放 busy 标志
    manager_.switch_finish();
}

PageManager *MvcApp::getPageManager()
{
    return &manager_;
}

void MvcApp::draw()
{
    if (g_draw_test_flag)
    {
        g_draw_test_begin_tick = get_boot_msec();
        invalidateArea(Rect(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT));
        Application::draw();
        g_draw_test_flag = 0;
        //rt_kprintf("[MvcApp] draw test:%dms\n", (int) (get_boot_msec() - g_draw_test_begin_tick));
    }
    else
    {
#if USE_SLEEP_SCREEN_BUF
        if (sleep_awake_)
        {
            //rt_kprintf("MvcApp::draw test:%dms 1\n", (int) (get_boot_msec() - g_draw_test_begin_tick_2));
        }
#endif
        Application::draw();
#if USE_SLEEP_SCREEN_BUF
        if (sleep_awake_)
        {
            // print_memory_status();
            if(snapshot_buffer_ != nullptr)
            {
                HAL::freeScreenCachedBuffer(snapshot_buffer_);
                snapshot_buffer_ = nullptr;
            }
            sleep_awake_ = false;
            switchScreen(manager_.get_cur_screen());

            // 刷新当前页面
            invalidateArea(Rect(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT));
            //rt_kprintf("MvcApp::draw test:%dms 2\n", (int) (get_boot_msec() - g_draw_test_begin_tick_2));
        }
#endif
    }
}

void MvcApp::finishPendingScreen()
{
    stop_transition();   // 停止动画

    if (manager_.get_switch_event() != PAGE_EVENT::IDLE)
    {
        manager_.push(manager_.get_next_page());

        // 后台切换页面 无动画
        // 操作解绑
        evt_handle_.unbind_page();
        // 页面析构
        manager_.unload_page();
        // 加载新页面, 申请内存
        manager_.load_page();
        // 绑定Screen到GUI平台
        switchScreen(manager_.get_cur_screen());
        // 结束页面切换
        manager_.load_over();
        // 绑定evt_handle_和其他事件对象
    }

    transitionDone();
    resumeEventDispatch();
    resumeKeyEventDispatch();
}

/**
 * @brief 进入休眠通知
 */
void MvcApp::enter_gui_sleep()
{
    evt_handle_.handleKeyEvent(KEY_OFF);

    stop_transition();   // 停止动画

    if (msg_list_.get_slider_busy())
    {
        msg_list_.stop_slider();
    }

    // 截取当前界面
#if USE_SLEEP_SCREEN_BUF
    if(snapshot_buffer_ == nullptr)
    {
        snapshot_buffer_ = HAL::getScreenCachedBuffer(LV_COLOR_DEPTH/8);
    }
    snapshot_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    snapshot_.makeSnapshot(manager_.get_cur_screen()->getRootContainer(), snapshot_buffer_, false);
#endif

    last_sleep_time_ = (uint32_t) get_boot_sec();   // 初始化上一次进入睡眠模式的时间
}

/**
 * @brief 退出休眠通知
 */
void MvcApp::leave_gui_sleep()
{
    g_draw_test_begin_tick_2 = get_boot_msec();
    if (get_boot_sec() - last_sleep_time_ >= WAKE_UP_AUTO_LOCK_TIME)
    {
        //如果自动锁定按键功能开启，且息屏超过1分钟，则自动手表锁定生效
        if (get_access_auto_lock())
        {
            GUI_VIEW_LOG_I("[%s] auto lock", __FUNCTION__);
            bool state = true;
            watch_lock_submit_work(LOCK_OPS_WATCH, &state);
        }
    }

    //rt_kprintf("MvcApp::leave_gui_sleep test:%dms 1\n", (int) (get_boot_msec() - g_draw_test_begin_tick_2));
#if USE_SLEEP_SCREEN_BUF
    // 休眠起来的时候仅刷新截图 第二帧换成当前界面
    sleep_screenshot();

    //rt_kprintf("MvcApp::leave_gui_sleep test:%dms 2\n", (int) (get_boot_msec() - g_draw_test_begin_tick_2));

    bak_sleep_base_.getRootContainer().setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    bak_sleep_base_.getRootContainer().removeAll();
    if(strcmp(manager_.get_cur_page(), "Launcher") != 0)
    {
        bak_sleep_base_.getRootContainer().add(snapshot_);
    }
    else
    {
        bool get_dial_container(CacheableContainer * *container);
        CacheableContainer *jsContainer_ = nullptr;
        get_dial_container(&jsContainer_);
        if(jsContainer_)
        {
            bak_sleep_base_.getRootContainer().add(*jsContainer_);
        }
        else
        {
            bak_sleep_base_.getRootContainer().add(snapshot_);
        }

    }

    switchScreen(&bak_sleep_base_);
#endif

    // if(msg_list_.get_prepare_msg() && !msg_list_.get_slider_busy() && !transition_.is_transition_busy())
    // {
    //     msg_list_.check_pop_msgbox();
    // }

#if USE_SLEEP_SCREEN_BUF
    sleep_awake_ = true;
#endif

    // 刷新当前页面
    invalidateArea(Rect(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT));
    MvcApp::draw();

    //rt_kprintf("MvcApp::leave_gui_sleep test:%dms 3\n", (int) (get_boot_msec() - g_draw_test_begin_tick_2));
}

void MvcApp::sleep_push_page()
{
    if (!manager_.get_sleep_push_lock())    // 部分锁定页面在休眠中不跳转
    {
        // 关闭弹窗
        if (msg_list_.msg_is_activate())
        {
            msg_list_.sleep_close();
        }

        const char *next_page = nullptr;

#ifndef SIMULATOR
        if (get_sport_status() > enum_status_ready)
#else
        if (get_simulator_sport_status() > ACTIVITY_FIT_READY)
#endif
        {
                next_page = manager_.find_page_in_pool("WebGrid");
        }
#ifndef SIMULATOR
        else if (get_sport_status() == enum_status_ready)
#else
        else if (get_simulator_sport_status() == ACTIVITY_FIT_READY)
#endif
        {
            next_page = manager_.find_page_in_pool("SportStart");
            //  manager_.page_command(next_page, (int)SportStart_CMD::SET_COUNTDOWN_TIME, (void*)COUNTDOWN_TIMER_EVENT_RESET);
        }
        else
        {
            if (get_power_save_setting() == POWER_SAVE_SUPPER)
            {
                next_page = manager_.find_page_in_pool("PowerSaveDesk");

                if (get_power_save_setting() == POWER_SAVE_SUPPER)
            {
                    manager_.page_command(next_page, 0, NULL);
            }
                // else if(get_watch_lock_state())
                // {
                //     manager_.page_command(next_page, 1, NULL);
                // }
            }
            else
            {
                #ifdef SIMULATOR
                next_page = manager_.find_page_in_pool("Launcher");
                #else
                boot_app_type_t boot_app_type = boot_switch_get_app_type();
                if (boot_app_type == APP_TYPE_NORMAL)
                {
                    if(get_backlight_status() == BK_STATUS_AOD)
                    {
                        if(strcmp(manager_.get_cur_page(), "EditDialTheme") == 0 || strcmp(manager_.get_cur_page(), "EditDialDataComponent") == 0)
                        {
                            GUI_VIEW_LOG_E("edit dial now, dont back to launcher\n");
                            return;
                        }
                    }
                    next_page = manager_.find_page_in_pool("Launcher");
                }
                else if (boot_app_type == APP_TYPE_FACTORY)
                {
                    next_page = manager_.find_page_in_pool("FactoryMenu");
                }
                #endif
            }
        }

        // 确定要翻页
        if (next_page != nullptr && strcmp(manager_.get_cur_page(), next_page) != 0)
        {
            stop_transition();   // 停止动画

            manager_.push(next_page);

            // 后台切换页面 无动画
            // 操作解绑
            evt_handle_.unbind_page();
            // 页面析构
            manager_.page_jump();
            // 加载新页面, 申请内存
            manager_.load_page();
            // 绑定Screen到GUI平台
            switchScreen(manager_.get_cur_screen());
            // 结束页面切换
            manager_.load_over();
            // 绑定evt_handle_和其他事件对象
            transitionDone();
            resumeEventDispatch();
            resumeKeyEventDispatch();

#if USE_SLEEP_SCREEN_BUF
            if(snapshot_buffer_ == nullptr)
            {
                snapshot_buffer_ = HAL::getScreenCachedBuffer(LV_COLOR_DEPTH/8);
            }
            snapshot_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
            snapshot_.makeSnapshot(manager_.get_cur_screen()->getRootContainer(), snapshot_buffer_, false);
            cachedDirtyAreas.clear();
#endif

            invalidateArea(Rect(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT));

#if !defined(SIMULATOR) && defined(BUTTON_DEBUG_MODE) && (BUTTON_DEBUG_MODE == 1)
            ui_report(UI_BLE_DEBUG_REPORT_TYPE_PAGE, manager_.get_cur_page(), strlen(manager_.get_cur_page()) + 1);
#endif
        }
    }
}

/**
 * @brief 进入aod模式时推送页面
 */
void MvcApp::enter_gui_aod()
{
    evt_handle_.handleKeyEvent(KEY_OFF);

    stop_transition();   // 停止动画

    if (msg_list_.get_slider_busy())
    {
        msg_list_.stop_slider();
    }

    // 截取当前界面
#if USE_SLEEP_SCREEN_BUF
    if(snapshot_buffer_ == nullptr)
    {
        snapshot_buffer_ = HAL::getScreenCachedBuffer(LV_COLOR_DEPTH/8);
    }
    snapshot_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    snapshot_.makeSnapshot(manager_.get_cur_screen()->getRootContainer(), snapshot_buffer_, false);
#endif

#ifndef SIMULATOR
    if (get_sport_status() <= enum_status_ready)
#else
    if (get_simulator_sport_status() <= ACTIVITY_FIT_READY)
#endif
    {
        g_aod_screen.show();
    }

    // 刷新当前页面
    invalidateArea(Rect(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT));
    Application::draw();

    last_sleep_time_ = (uint32_t) get_boot_sec();   // 初始化上一次进入睡眠模式的时间
}

/**
 * @brief 退出aod模式
 */
void MvcApp::leave_gui_aod()
{
    if (get_boot_sec() - last_sleep_time_ >= WAKE_UP_AUTO_LOCK_TIME)
    {
        //如果自动锁定按键功能开启，且息屏超过1分钟，则自动手表锁定生效
        if (get_access_auto_lock())
        {
            GUI_VIEW_LOG_I("[%s] auto unlock", __FUNCTION__);
            bool state = true;
            watch_lock_submit_work(LOCK_OPS_WATCH, &state);
        }
    }

    g_aod_screen.hide();

#if USE_SLEEP_SCREEN_BUF
    // 休眠起来的时候仅刷新截图 第二帧换成当前界面
    sleep_screenshot();

    bak_sleep_base_.getRootContainer().setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    bak_sleep_base_.getRootContainer().removeAll();
    bak_sleep_base_.getRootContainer().add(snapshot_);
    switchScreen(&bak_sleep_base_);
#endif

    // if(msg_list_.get_prepare_msg() && !msg_list_.get_slider_busy() && !transition_.is_transition_busy())
    // {
    //     msg_list_.check_pop_msgbox();
    // }

#if USE_SLEEP_SCREEN_BUF
    sleep_awake_ = true;
#endif

    // 刷新当前页面
    invalidateArea(Rect(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT));
    MvcApp::draw();
}

/**
 * @brief 休眠中截图
 */
void MvcApp::sleep_screenshot()
{
#if USE_SLEEP_SCREEN_BUF
    // 走一次事件处理逻辑 若页面有变更时会更新 例如主界面时间变更
    evt_handle_.handleTickEvent();
    Application::handleTickEvent();
    if (!transition_.is_transition_busy())
    {
        manager_.notify();
    }
    if(strcmp(manager_.get_cur_page(), "Launcher") == 0)
    {

        bool get_dial_container(CacheableContainer * *container);
        CacheableContainer *jsContainer_ = nullptr;
        get_dial_container(&jsContainer_);
        if(jsContainer_ != nullptr)
        {
            // rt_kprintf("js container not null\n");
            return;
        }
    }
    Vector<Rect, MAX_SNAPSHOT_DIRTY_AREA_SIZE> dirty_list;
    if (cachedDirtyAreas.size())
    {
        for (uint16_t i = 0; i < cachedDirtyAreas.size(); i++)
        {
            //rt_kprintf("cachedDirtyAreas %d %d %d %d\n", cachedDirtyAreas[i].x, cachedDirtyAreas[i].y, cachedDirtyAreas[i].width, cachedDirtyAreas[i].height);
            dirty_list.add(cachedDirtyAreas[i]);
        }
    }

    snapshot_.updateSnapshot(manager_.get_cur_screen()->getRootContainer(), dirty_list);
    cachedDirtyAreas.clear();
#endif
}

//弹窗动画时停止转场动画
void MvcApp::stop_transition()
{
    if (transition_.is_transition_busy())
    {
        // 当前正在切换页面 需要清除状态
        transition_.stop_transition();

        // 绑定evt_handle_和其他事件对象
        transitionDone();
        resumeEventDispatch();
        resumeKeyEventDispatch();
    }
}

QwMsgList *MvcApp::getMsgList()
{
    return &msg_list_;
}

PageTransition *MvcApp::getPageTransition()
{
    return &transition_;
}


/////////////////////////////////////////////////

static int sim_key(int argc, char **argv)
{
    if (g_sim_cli_key == UINT8_MAX && argc == 3 && rt_strcmp(argv[1], "key") == 0)
    {
        g_sim_cli_key = keysTransforType(argv[2][0]);
    }
    return RT_EOK;
}

static int monkey_key(int argc, char **argv)
{
    return RT_EOK;
}

static int show_popmsg(int argc, char **argv)
{
    if (g_sim_cli_key == UINT8_MAX && argc == 2)
    {
        int pop_id = atoi(argv[1]);
        if (pop_id > 0 && pop_id < enumPOPUP_MAX)
        {
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, pop_id, NULL);
        }
    }
    return RT_EOK;
}

static int mvc_drawtest(int argc, char **argv)
{
    if (g_draw_test_flag == 0)
    {
        g_draw_test_flag = 1;
    }
    return RT_EOK;
}

#ifdef FINSH_USING_MSH
#include <finsh.h>
MSH_CMD_EXPORT(sim_key, sim_key);
MSH_CMD_EXPORT(monkey_key, monkey_key);
MSH_CMD_EXPORT(show_popmsg, show_popmsg);
MSH_CMD_EXPORT(mvc_drawtest, mvc_drawtest);
#endif /* FINSH_USING_MSH */
