#include "lvgl.h" 

#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP ALIGN(4)
#endif

#ifndef eZIP_RGBARGB888A
#define eZIP_RGBARGB888A
#endif


LV_ATTRIBUTE_MEM_ALIGN_EZIP const  uint8_t sys_set_voice_map[] SECTION(".ROM3_IMG_EZIP.sys_set_voice") = { 
    0x00,0x00,0x06,0xcd,0x46,0x08,0x20,0x00,0x00,0x48,0x00,0x48,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0x03,0x00,0x00,0x00,0x90,0x00,0x00,0x03,0x38,0x00,0x00,0x05,0xdc,
    0x3c,0xab,0x89,0x8d,0xa2,0x7c,0xe3,0xcf,0x33,0x33,0x4b,0x0b,0x24,0xff,0x0b,0xf9,0xc3,0xda,0x54,0x68,0x67,0x1b,0x0f,0xe5,0x80,0x89,0x51,0x77,0x83,0x17,0x41,0xb9,
    0x68,0x30,0x91,0x40,0x95,0xc8,0xc1,0x28,0xa8,0x24,0xd0,0x2e,0x04,0x63,0x94,0x8f,0x04,0x51,0x2e,0xb2,0xcb,0x62,0x44,0xac,0x89,0x21,0xe1,0x23,0xa1,0x78,0x91,0x83,
    0x07,0x29,0x09,0x97,0xda,0x36,0xd2,0x8b,0x98,0x4a,0x42,0xbb,0xb4,0xa5,0x21,0x50,0x4c,0x38,0x49,0x4b,0x3b,0x33,0x8f,0xcf,0xbb,0xcb,0xc2,0x76,0x3a,0xfb,0x39,0xef,
    0x7e,0xcc,0xee,0x2f,0xec,0xce,0x0c,0xd3,0x79,0xdf,0xd9,0xdf,0xfb,0x7b,0x3e,0x5f,0x44,0xa8,0x22,0xec,0x9b,0x68,0x08,0x58,0x96,0xb1,0xc6,0xb2,0xb0,0x1d,0x00,0xdb,
    0x80,0x68,0x25,0x28,0xe0,0x07,0x82,0x65,0x7c,0x7b,0xb9,0xf8,0x1b,0xe2,0x7f,0x0a,0xc0,0x23,0x02,0xbc,0xcb,0xf7,0xef,0x00,0xe2,0x28,0x1f,0x87,0x2d,0x84,0xa1,0xe9,
    0x46,0xa3,0xaf,0xbb,0x09,0x1e,0xca,0x7c,0xa7,0x8a,0x12,0x14,0x1e,0x69,0x68,0x03,0xcd,0xda,0x00,0x16,0xbd,0xca,0x97,0xaf,0xf0,0xc7,0x2f,0x61,0xd8,0x5e,0x26,0xef,
    0x12,0xc1,0x5c,0x4f,0x4c,0x87,0x7b,0x9e,0x23,0xa8,0xeb,0x16,0xb4,0x28,0xa0,0x75,0xb0,0x14,0x36,0xb1,0x1a,0x5e,0x2c,0xed,0x6c,0x78,0x06,0x11,0x4e,0x46,0x5a,0xe7,
    0x06,0xaa,0x9e,0xa0,0xae,0x51,0xf5,0x0d,0x44,0xdc,0xce,0xa7,0x6f,0x95,0x7b,0x51,0x58,0x51,0x17,0x54,0x15,0x0f,0x1f,0x5b,0x35,0x3b,0x5c,0x75,0x04,0xed,0xbd,0xa5,
    0xbe,0x6b,0x11,0x76,0xf1,0xe9,0x4b,0x55,0xe0,0xe6,0x0e,0x44,0x75,0xe3,0x48,0x55,0x10,0xb4,0x67,0x54,0x7d,0x93,0x14,0xfc,0x9c,0x97,0x2f,0x54,0x4d,0x81,0x80,0xdf,
    0xe7,0x0a,0x98,0xda,0x8e,0xe8,0x73,0x33,0xf1,0x8a,0x10,0x14,0x1e,0x6f,0xd4,0xc9,0x34,0xbf,0x46,0xa0,0x0e,0xa8,0x5e,0x4c,0x59,0x00,0xef,0xc5,0x74,0xe3,0x72,0x59,
    0x09,0xea,0x1a,0xf1,0xed,0x42,0x85,0xbe,0xe1,0xd3,0x45,0xe0,0x09,0x50,0x47,0x54,0x37,0x7b,0x4a,0x4e,0xd0,0xae,0x9b,0xf0,0x7f,0x4d,0xd5,0xbe,0xe7,0xd3,0x4d,0xe0,
    0x39,0x64,0x27,0xc9,0x35,0x41,0x5d,0x63,0xda,0x3a,0xb4,0xf0,0x27,0x9e,0x68,0x15,0x78,0x14,0x6c,0x6e,0x1b,0x32,0x99,0x9b,0x2b,0x82,0xf6,0xc6,0x7d,0x1f,0x5a,0x40,
    0x3f,0x82,0xf7,0x31,0x05,0x86,0x16,0x72,0x72,0xdc,0x4a,0xd1,0x51,0x2a,0xae,0x7d,0x51,0x23,0xe4,0x08,0x2c,0x07,0xd5,0xe8,0x76,0xba,0x51,0x14,0x41,0xe1,0x51,0xed,
    0x28,0x67,0xc1,0x47,0x6a,0x84,0x9c,0x94,0x2d,0xad,0x0f,0xc7,0xb5,0xfd,0xae,0x4d,0x4c,0x90,0xc3,0x4f,0x7d,0x06,0x35,0x0a,0x45,0x55,0x56,0xa7,0x67,0xdc,0x4a,0xa1,
    0x66,0x55,0xcb,0xe4,0x08,0x98,0x26,0x1d,0x2c,0x4a,0x41,0x35,0xe4,0x90,0x73,0x5b,0x1b,0x62,0x28,0x55,0xe0,0x2a,0xf9,0x86,0xf2,0x7a,0x21,0x27,0x91,0x19,0x11,0xec,
    0xcc,0x5b,0x41,0xc9,0x24,0xd0,0xf7,0x87,0x97,0xf3,0x9c,0xe2,0x72,0x23,0xc3,0x2f,0xfa,0x49,0x39,0x15,0x94,0xcc,0x90,0xeb,0x8b,0x9c,0xa4,0x72,0x7c,0x5b,0x72,0x9a,
    0x98,0xa8,0xad,0xbc,0x59,0x3e,0xc8,0x20,0x88,0x36,0x66,0x35,0x31,0x51,0x95,0x83,0x69,0xfc,0xed,0x9d,0xc2,0x53,0x3e,0xfe,0x6d,0x34,0x96,0x66,0x54,0x90,0x68,0x59,
    0xd4,0x33,0x39,0x02,0x8b,0x67,0xb4,0xb5,0x6a,0xa6,0x66,0x17,0x6b,0xeb,0xab,0x72,0xbf,0x50,0x7f,0xcc,0x82,0xc9,0x41,0x4a,0xc8,0xfa,0x7f,0xcd,0x95,0xdf,0x70,0x41,
    0x82,0x61,0xcd,0x51,0x3d,0xc9,0x4e,0x60,0x59,0x71,0x71,0xab,0x09,0x93,0x03,0xc9,0x49,0x45,0x02,0x12,0xec,0x54,0x20,0xd4,0x59,0x74,0xa9,0x28,0x2b,0x21,0x6a,0xc7,
    0x0c,0x3d,0xe4,0xf3,0xe5,0x54,0xcd,0x00,0x7f,0x1c,0xfd,0x60,0x5c,0xab,0x74,0x7b,0xf6,0xf7,0x05,0x4b,0xf4,0xb8,0xc1,0x5e,0x71,0x72,0x0a,0x81,0x50,0x5e,0x4a,0x7d,
    0x92,0x15,0xd4,0xa4,0xd9,0xb7,0x66,0xca,0xb5,0xfb,0x20,0x83,0x1c,0x41,0x8a,0x30,0xcd,0x14,0x36,0x9f,0x57,0xa1,0x39,0x88,0x32,0x43,0xbd,0x5f,0xb1,0xd5,0x20,0xdb,
    0x4b,0x4d,0x8c,0xf8,0x51,0x51,0xdd,0x90,0xa2,0x9c,0x6c,0x7e,0x4c,0x52,0xa7,0xb1,0x41,0x49,0xdf,0xf1,0x2c,0xf5,0xa6,0x9e,0x50,0x4d,0xfa,0x8a,0xbb,0x85,0x93,0x5a,
    0x64,0x92,0xc4,0xa3,0xe3,0x13,0x82,0xd0,0xd2,0xde,0xf1,0x82,0xbf,0xb1,0x23,0xe8,0x10,0xe9,0xfa,0x4f,0xc8,0x9b,0xe7,0x89,0x0f,0x42,0x84,0xb7,0x49,0x92,0x09,0xa5,
    0xe3,0x36,0xe7,0x35,0x22,0xb7,0x29,0x89,0x13,0x65,0xa4,0x52,0x81,0x74,0xf2,0xc5,0x5c,0x62,0x41,0x64,0xa4,0x09,0x09,0x8d,0x86,0x47,0x1a,0xda,0x40,0x31,0x6f,0x56,
    0x9b,0x42,0xec,0x61,0x5e,0xfc,0xf0,0x14,0xe1,0xa1,0xdd,0xca,0x3c,0x13,0x73,0x9a,0x5f,0x46,0x9a,0xf0,0x1f,0x00,0x00,0x00,0x44,0x60,0x84,0xc7,0x7c,0x3b,0xc1,0xa2,
    0xef,0xa0,0x48,0x4c,0x0e,0x10,0x5c,0xdc,0x6a,0x82,0x6c,0x84,0xe3,0xda,0xbc,0x6b,0x31,0x87,0x98,0x2b,0xd3,0xfd,0xa8,0x6e,0xcc,0xbb,0x0e,0x76,0x2a,0x10,0xe2,0x8f,
    0x1b,0x24,0x9f,0x36,0x69,0x1d,0x78,0x00,0xcd,0x2f,0xe3,0x02,0xc2,0xec,0x84,0xc8,0x46,0x72,0x44,0x84,0xb5,0xe0,0x41,0x08,0x35,0xf5,0xc7,0xac,0x27,0xd7,0x76,0xb5,
    0x0c,0xa4,0xdd,0x2b,0x9a,0xa0,0x7d,0x13,0x0d,0x01,0x3e,0xfa,0xbd,0x40,0x88,0x20,0xc0,0xae,0x92,0xc9,0x41,0xca,0xaa,0xa2,0x74,0x93,0x2c,0x8a,0x20,0xcb,0x32,0xd6,
    0x78,0x49,0x35,0x82,0xa4,0xe6,0x20,0xe6,0x4d,0xc0,0xed,0x41,0x57,0x04,0x4d,0x33,0x41,0xd8,0xee,0x35,0xd3,0xb2,0xfb,0xa2,0x7e,0x09,0xa6,0xe4,0x04,0xa6,0xf6,0x01,
    0xeb,0x11,0xdb,0xa0,0x86,0xf0,0xac,0x8d,0x3c,0x37,0x40,0x82,0x31,0x05,0x88,0x56,0x7a,0xce,0x39,0xdb,0xcc,0x26,0x9d,0x14,0x97,0x26,0x65,0x53,0x10,0x8e,0x2b,0x1c,
    0xc7,0xfc,0x5e,0x8c,0x5e,0xe5,0x50,0x94,0x82,0x34,0xc2,0x0a,0x82,0x65,0x5e,0x22,0xc7,0xee,0x6f,0x84,0xc3,0x9e,0xe7,0xb4,0x65,0x2a,0x88,0xe8,0x86,0x88,0x89,0xcb,
    0xbd,0x44,0x8e,0x3d,0xb7,0x49,0x77,0xd8,0x42,0x59,0x76,0x75,0xa5,0x93,0x57,0x38,0xd4,0xeb,0x8a,0x97,0xd4,0x33,0xe0,0xa0,0x9e,0xf4,0xe4,0xd0,0xee,0x7f,0xdc,0x91,
    0x03,0xd3,0xd1,0xc0,0xec,0x75,0x85,0x12,0xd1,0xcc,0x65,0xd8,0x0d,0xca,0x8b,0x1c,0xf9,0xce,0x23,0xce,0x37,0x9f,0x57,0xe7,0xa9,0xc7,0x4e,0x60,0x68,0xb7,0xab,0xf5,
    0x1f,0x14,0x5f,0x1a,0x0f,0xf1,0x88,0x19,0x6a,0x74,0xfb,0xf2,0x22,0x83,0x15,0xf6,0xef,0x36,0x73,0xcd,0x06,0x41,0x48,0xca,0x07,0xd9,0xcb,0x8a,0xfe,0x13,0xd9,0x7d,
    0x53,0x11,0x39,0x50,0x5f,0x82,0x20,0x0e,0x65,0x77,0xf9,0xb2,0x45,0x46,0x86,0x5b,0x88,0xef,0x90,0x39,0x8f,0x18,0xdf,0xbe,0x30,0x2e,0xd5,0x23,0x72,0xa0,0xab,0xc9,
    0x62,0x95,0xe8,0x4e,0x25,0x6a,0xa8,0x92,0x3a,0x6e,0x97,0xea,0x49,0xb4,0x4e,0x02,0x46,0x6f,0x92,0x20,0xc4,0xd1,0x72,0xd5,0x50,0xc2,0x44,0x64,0xfa,0x2b,0x27,0xbf,
    0x63,0xf7,0x4d,0x45,0xea,0xa7,0xe7,0x69,0xbb,0x83,0x68,0xb8,0x6c,0x35,0xd4,0xe3,0x97,0x97,0x45,0x92,0x53,0xd6,0xec,0xd6,0xb4,0x92,0xf9,0x8f,0x75,0xe9,0x69,0x35,
    0x8f,0x30,0x54,0xee,0x70,0x2d,0x48,0x92,0x61,0x72,0xf6,0xca,0x5e,0x0e,0xf9,0x38,0x71,0x3c,0x60,0x9e,0x4b,0x5d,0x69,0xd3,0x8d,0x46,0xdf,0xd2,0x19,0xad,0x22,0x6d,
    0x0b,0x51,0x06,0xb8,0x6d,0xd5,0xba,0x37,0xa7,0x05,0xe1,0xeb,0xe8,0xbc,0x72,0xa3,0xbb,0x09,0x1e,0xf2,0xb1,0xb7,0x22,0x6d,0x0b,0x5e,0x6d,0xd1,0x57,0x76,0x5a,0xf5,
    0x72,0xe5,0x56,0x36,0x5c,0x8b,0x06,0xe6,0x4e,0x2d,0x68,0xb9,0x72,0xa8,0xbf,0x54,0xc9,0x0c,0xd9,0xc9,0xe4,0x9a,0x25,0xb6,0x2d,0xf2,0x36,0x2e,0x85,0x0e,0x2d,0xf8,
    0x3f,0xf1,0xd5,0x19,0x87,0x15,0x0a,0x68,0x77,0xab,0xa5,0x52,0xaf,0x84,0x7a,0xb8,0x30,0x3d,0xcd,0xbe,0xe7,0x7d,0xc7,0xa6,0x7d,0x4c,0x87,0x7b,0xcc,0xd5,0x99,0xaa,
    0xe8,0x16,0x56,0xc6,0xb4,0xee,0x9b,0x4b,0xcc,0x4f,0x33,0xef,0x6a,0x08,0x29,0x21,0x9c,0x84,0x3a,0x05,0x02,0xed,0xf9,0xf6,0x19,0xb8,0x9f,0x95,0xa0,0x48,0xeb,0x1c,
    0x8b,0x1b,0x2f,0xd4,0x1d,0x39,0x48,0x3f,0x44,0x74,0xf3,0x6c,0xf6,0x7d,0xb1,0x54,0xf7,0x43,0xc5,0xc3,0xf5,0x44,0x0e,0xa7,0x99,0x43,0x91,0x16,0xf3,0x93,0xdc,0x1b,
    0x87,0x8f,0x71,0x6c,0xd5,0xac,0xc8,0xaa,0x0f,0xd4,0x0b,0x41,0x5c,0x67,0x7d,0x20,0xec,0x2b,0xbb,0xf9,0x39,0xed,0x89,0x8f,0x6a,0xbd,0x7c,0x67,0x7d,0x6d,0xab,0x87,
    0x36,0x1f,0xd7,0xcd,0x9f,0xf3,0xdb,0x7a,0xb6,0xc3,0xd4,0x76,0xf0,0xf7,0x54,0xed,0xfa,0x1d,0x0c,0xe7,0x43,0x4e,0x46,0x05,0x25,0x73,0x23,0xed,0x75,0x66,0xef,0xb7,
    0x7a,0x55,0x4e,0x76,0x05,0x25,0x72,0x23,0xe3,0x32,0x0f,0xd7,0x51,0x3b,0xa1,0x1c,0x86,0x10,0xf0,0xf9,0x42,0xc8,0x11,0xf8,0x0f,0x00,0x00,0x00,0x45,0xc8,0x81,0x70,
    0x5c,0xdd,0x02,0x80,0x17,0xc0,0xd3,0xa0,0xee,0x48,0xab,0xf9,0x31,0x22,0x50,0xa1,0x4f,0x2a,0xb9,0xfe,0x20,0xaa,0x9b,0x3d,0x16,0xc0,0x06,0x3e,0x9d,0xf2,0x1e,0x2f,
    0xf0,0x0f,0x73,0xb2,0x8d,0x7f,0xc3,0x47,0xc5,0x90,0x93,0x17,0x41,0x02,0x31,0xdd,0xb8,0x0c,0x86,0x16,0xe2,0x29,0xae,0x78,0x86,0x1b,0xa2,0xd3,0x2a,0x19,0xab,0x23,
    0xba,0x79,0xd6,0xcd,0x38,0x58,0xe8,0x03,0xe1,0xb8,0xb6,0x9f,0x0f,0x5f,0x56,0x31,0x37,0xd7,0x50,0xa1,0x43,0x91,0x16,0xf3,0x57,0x19,0x83,0x61,0x31,0x0f,0xed,0x1d,
    0x5f,0xd4,0x6e,0x9a,0x74,0x90,0x55,0xdb,0x51,0x3d,0xbc,0xe0,0x04,0xff,0x9a,0xa3,0xd1,0xd6,0xb9,0x53,0x52,0x47,0x75,0xf3,0xf0,0x9e,0x5b,0xbe,0x20,0x11,0xec,0x64,
    0x41,0x6f,0xab,0x9c,0x9b,0xc1,0x8b,0x40,0xd6,0x2f,0xc7,0x03,0xe6,0xb9,0x92,0xd0,0x2e,0x63,0x90,0xce,0x38,0xac,0x40,0xf0,0x6d,0x61,0x45,0x6d,0xe4,0xcb,0xd7,0x4a,
    0xc8,0xc7,0x34,0x7f,0x06,0xd9,0xdb,0xf6,0x21,0xc1,0xd5,0x68,0xc0,0xe8,0x2d,0xb9,0x2e,0x65,0x0f,0xb8,0xe3,0x0e,0x2c,0x59,0x3c,0xa3,0xad,0x55,0x08,0x5e,0x00,0xc4,
    0x76,0xf6,0x96,0x01,0x3e,0x36,0x31,0x79,0x7e,0x8e,0x86,0x0d,0x98,0x7b,0xce,0x69,0x26,0xe0,0x01,0x13,0x30,0x86,0x80,0xe3,0x80,0x34,0x42,0x0a,0xdd,0xb0,0x2c,0xf5,
    0xaf,0x98,0x3e,0xfb,0x67,0xb9,0x15,0xfa,0x1f,0x7b,0x52,0xaa,0xde,
};


#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER ALIGN(4)
#endif
LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER const eZIP_RGBARGB888A  lv_img_dsc_t sys_set_voice SECTION(".ROM3_IMG_EZIP_HEADER.sys_set_voice") = { 
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .header.always_zero = 0,
  .header.w = 72,
  .header.h = 72,
  .data_size  = 1741,
  .data = sys_set_voice_map
};
