/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   hr_push.h
@Time    :   2025/01/07 11:19:49
*
**************************************************************************/

#ifndef __HR_PUSH_H_
#define __HR_PUSH_H_
#include <stdbool.h>
#include "cfg_header_def.h"
#if defined(__cplusplus)
extern "C" {
#endif
#define HEART_RATE_PUSH_DELAY_MS (30*60*1000) //30分钟,每30分钟弹窗心率推送
// 心率推送相关
bool get_hr_push_en();
void set_hr_push_en(bool state);
void set_hr_push_devide(char* devide);
char* get_hr_push_devide();
void clear_hr_push_devide();
void notify_hr_push_value(uint8_t value);
uint8_t get_hr_push_value();
void heart_rate_push_callback(void *parameter);
#ifndef SIMULATOR
rt_err_t update_heart_rate_push_timer(void);
#endif
void delete_heart_rate_push_timer(void);
#ifdef __cplusplus
}
#endif

#endif /* __HR_PUSH_H_ */