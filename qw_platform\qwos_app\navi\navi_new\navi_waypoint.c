#include <string.h>
#include <stddef.h>
#include "navi_waypoint.h"

void navi_waypoint_ad_copy(NaviWaypointAd *self, const NaviWaypointAd *wpad)
{
    if (self != NULL && wpad != NULL)
    {
        memcpy(self, wpad, sizeof(NaviWaypointAd));
    }
}

void navi_waypoint_ad_reset(NaviWaypointAd *self)
{
    if (self != NULL)
    {
        self->lng = 0.0;
        self->lat = 0.0;
        self->alt = 0.0f;
        self->dist = 0.0f;
    }
}

void navi_waypoint_adc_copy(NaviWaypointAdc *self, const NaviWaypointAdc *wpadc)
{
    if (self != NULL && wpadc != NULL)
    {
        memcpy(self, wpadc, sizeof(NaviWaypointAdc));
    }
}

void navi_waypoint_adc_reset(NaviWaypointAdc *self)
{
    if (self != NULL)
    {
        self->lng = 0.0;
        self->lat = 0.0;
        self->alt = 0.0f;
        self->dist = 0.0f;
        self->course = 0.0f;
    }
}

void navi_waypoint_dc_copy(NaviWaypointDc *self, const NaviWaypointDc *wpdc)
{
    if (self != NULL && wpdc != NULL)
    {
        memcpy(self, wpdc, sizeof(NaviWaypointDc));
    }
}