/***************************************Copyright (c)****************************************/
//                              <PERSON>han <PERSON> Technology Co., Ltd
//
//---------------------------------------File Info--------------------------------------------
// File name         : peripheral_light_pb.c
// Created by        : Noxstella
// Descriptions      : 灯PB协议接口
//--------------------------------------------------------------------------------------------
// History          :
// 2023-4-17        : Original version
// 2023-8-11        ：Edited by Noxstella
/*********************************************************************************************/
#include <stdint.h>
#include "peripheral_light_pb.h"
#include "ble_cmd_response.h"
#include "peripheral_common.pb.h"
#include "pb.h"
#include "pb_encode.h"
#include "pb_decode.h"
#include "crc8.h"
#include "ble_nus_c_srv.h"
#include <rtthread.h>
#include "qw_sensor_common.h"
#include "sensor_ble.h"
#include "ble_light_cfg.h"

static const uint8_t light_mode_trans[] = {
    BLE_LIGHT_MODE_INVAILD,

    BLE_LIGHT_MODE_100,               //高
    BLE_LIGHT_MODE_60,              //中常量
    BLE_LIGHT_MODE_40,              //低
    BLE_LIGHT_MODE_FLASH_FAST,        //高亮闪
    BLE_LIGHT_MODE_FLASH_SLOW,          //低亮闪
    BLE_LIGHT_MODE_INVAILD,

    BLE_LIGHT_MODE_CUSTOM_START,            //自定义
    BLE_LIGHT_MODE_INVAILD,
    BLE_LIGHT_MODE_INVAILD,
    BLE_LIGHT_MODE_INVAILD,

    BLE_LIGHT_MODE_INVAILD,
    BLE_LIGHT_MODE_INVAILD,
    BLE_LIGHT_MODE_INVAILD,
    BLE_LIGHT_MODE_INVAILD,
    BLE_LIGHT_MODE_INVAILD,

    BLE_LIGHT_MODE_INVAILD,
    BLE_LIGHT_MODE_INVAILD,
    BLE_LIGHT_MODE_INVAILD,
    BLE_LIGHT_MODE_INVAILD,
    BLE_LIGHT_MODE_CLOSE,
};

static const uint8_t light_cfg_trans[] = {

    BLE_LIGHT_CAP_END,                             //INVALID
    BLE_LIGHT_CAP_ROUTE,                         //路由功能, 可作为主灯进行组网
    BLE_LIGHT_CAP_RIDE_SYNC,                 //与骑行记录同步开关
    BLE_LIGHT_CAP_BREAK_LIGHT,             //刹车灯
    BLE_LIGHT_CAP_LIGHT_SENSOR,         //环境光感应跟随码表
    BLE_LIGHT_CAP_RIDE_SLEEP,                //休眠跟随码表
    BLE_LIGHT_CAP_RIDE_POWEROFF,       //关机跟随码表
    BLE_LIGHT_CAP_TEAM_SYNC,               //团队同频
    BLE_LIGHT_CAP_END,
};

static sensor_id_t ble_light_sensor_id = { 0 };

static void periph_light_get_support_capability(uint8_t channel, uint32_t sup_abilitity)
{
    const uint8_t* ble_mac = ble_periph_nus_c_channel_get_mac_addr(channel);
    if(ble_mac){
        ble_light_set_supported_capability(ble_mac, (uint16_t)sup_abilitity);
    }
}

static void periph_light_get_type(uint8_t channel, uint8_t type)
{
    const uint8_t* ble_mac = ble_periph_nus_c_channel_get_mac_addr(channel);
    if (ble_mac)
    {
        ble_light_set_type(ble_mac, type);
    }
}


static void periph_light_mode_updated(uint8_t channel, uint8_t mode)
{
    const uint8_t* ble_mac = ble_periph_nus_c_channel_get_mac_addr(channel);
    if(ble_mac){
        ble_light_info_t info;
        if(ble_light_get_info_by_mac(ble_mac, &info)){
            sensor_module_evt_handler evt_handler = sensor_module_evt_handler_get();
            ble_light_set_mode((uint8_t*)ble_mac, light_mode_trans[mode]);
            // rt_kprintf("current_mode = %x  %d\n",light_mode_trans[mode], mode);
            if(evt_handler){
                evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, SENSOR_TYPE_LIGHT, SENSOR_RADIO_TYPE_BLE, 0);
            }
        }
    }
}
//获取所有的自动配置的开关（光感，刹车，同频， 休眠, 休眠倒计时，同步关机）
static void periph_light_get_all_cfg(uint8_t channel, peripheral_auto_config_message cfg_msg)
{
    const uint8_t* ble_mac = ble_periph_nus_c_channel_get_mac_addr(channel);
    ble_all_cfg_t all_cfg = { 0 };
    if (ble_mac)
    {
        //rt_kprintf("auto cfg get %d %d %d %d %d", cfg_msg.auto_light, cfg_msg.brake_swtich, cfg_msg.team_ride, cfg_msg.auto_sleep, cfg_msg.sync_off);
        all_cfg.auto_light = (BLE_MULTI_CFG)cfg_msg.auto_light;
        all_cfg.brake_swtich = cfg_msg.brake_swtich;
        all_cfg.team_ride = cfg_msg.team_ride;
        all_cfg.auto_sleep = (BLE_MULTI_CFG)cfg_msg.auto_sleep;
        all_cfg.sync_off = cfg_msg.sync_off;

        ble_light_all_config_set(ble_mac, &all_cfg);
    }
}

static void periph_light_get_bat_left_time(uint8_t channel, uint32_t left_time)
{
    const uint8_t* ble_mac = ble_periph_nus_c_channel_get_mac_addr(channel);
    if (ble_mac)
    {
        ble_light_left_time_update(ble_mac, left_time);
    }
}

static void periph_light_get_bat_pct(uint8_t channel, uint8_t bat_pct)
{
    const uint8_t* ble_mac = ble_periph_nus_c_channel_get_mac_addr(channel);
    if (ble_mac)
    {
        ble_light_battary_update(ble_mac, bat_pct > 100 ? 100 : bat_pct);
    }
}

static bool repeated_light_mode_info_enum_decode(pb_istream_t* stream, const pb_field_t* field, void** arg)
{
    uint32_t value = 0;
    uint32_t **ptr = (uint32_t **)arg;
	if (!pb_decode_varint32(stream, &value))    //需要用int32解
        return false;
    **ptr |= (uint32_t)(1 << light_mode_trans[value]);
    return true;
}

static bool repeated_light_cfg_info_enum_decode(pb_istream_t* stream, const pb_field_t* field, void** arg)
{
    uint32_t value = 0;
    uint32_t** ptr = (uint32_t**)arg;
    if (!pb_decode_varint32(stream, &value))
        return false;
    **ptr |= (uint32_t)(1 << light_cfg_trans[value]);
    return true;
}

void periph_light_type_request(const uint8_t* ble_mac)
{
    uint8_t channel;
    if(ble_periph_nus_c_channel_get(ble_mac,&channel)){
        ble_periph_contact item;
        if(ble_periph_contact_request_buffer(&item)){
            uint8_t pb_crc = 0;
            uint16_t length = 0;
            peripheral_light_format message_format;
            memset(&message_format, 0, sizeof(peripheral_light_format));

            message_format.service_type = PERIPHERAL_SERVICE_TYPE_PST_LIGHT;
            message_format.operate_type = PERIPHERAL_OPERATE_TYPE_POT_GET;

            message_format.has_sub_service_type = true;
            message_format.sub_service_type = PERIPHERAL_LIGHT_SERVICE_PLS_LIGHT_TYPE;

            pb_ostream_t encode_stream = pb_ostream_from_buffer(&item.data[sizeof(ble_end_cmd_st)], NUS_C_PERIPH_TX_BUFF_SIZE);
            pb_encode(&encode_stream, peripheral_light_format_fields, &message_format);

            length = encode_stream.bytes_written;
            pb_crc = CRC_Calc8_Table_L(&item.data[sizeof(ble_end_cmd_st)], length);

            ble_periph_contact_end_cmd_construct(item.data,message_format.service_type, message_format.sub_service_type, \
                message_format.operate_type, 0, length, pb_crc);
            item.length = length + sizeof(ble_status_cmd_st);

            ble_periph_contact_mailbox_queue_data_push(&item, channel);
        }
    }
}

void periph_light_cur_mode_request(const uint8_t* ble_mac)
{
    uint8_t channel;
    if(ble_periph_nus_c_channel_get(ble_mac,&channel)){
        ble_periph_contact item;
        if(ble_periph_contact_request_buffer(&item)){
            uint8_t pb_crc = 0;
            uint16_t length = 0;
            peripheral_light_format message_format;
            memset(&message_format, 0, sizeof(peripheral_light_format));

            message_format.service_type = PERIPHERAL_SERVICE_TYPE_PST_LIGHT;
            message_format.operate_type = PERIPHERAL_OPERATE_TYPE_POT_GET;

            message_format.has_sub_service_type = true;
            message_format.sub_service_type = PERIPHERAL_LIGHT_SERVICE_PLS_LIGHT_CURRENT;

            pb_ostream_t encode_stream = pb_ostream_from_buffer(&item.data[sizeof(ble_end_cmd_st)], NUS_C_PERIPH_TX_BUFF_SIZE);
            pb_encode(&encode_stream, peripheral_light_format_fields, &message_format);

            length = encode_stream.bytes_written;
            pb_crc = CRC_Calc8_Table_L(&item.data[sizeof(ble_end_cmd_st)], length);

            ble_periph_contact_end_cmd_construct(item.data,message_format.service_type, message_format.sub_service_type, \
                message_format.operate_type, 0, length, pb_crc);
            item.length = length + sizeof(ble_status_cmd_st);

            ble_periph_contact_mailbox_queue_data_push(&item, channel);
        }
    }
}

void periph_light_support_mode_request(const uint8_t* ble_mac)
{
    uint8_t channel;
    if(ble_periph_nus_c_channel_get(ble_mac,&channel)){
        ble_periph_contact item;
        if(ble_periph_contact_request_buffer(&item)){
            uint8_t pb_crc = 0;
            uint16_t length = 0;
            peripheral_light_format message_format;
            memset(&message_format, 0, sizeof(peripheral_light_format));

            message_format.service_type = PERIPHERAL_SERVICE_TYPE_PST_LIGHT;
            message_format.operate_type = PERIPHERAL_OPERATE_TYPE_POT_GET;

            message_format.has_sub_service_type = true;
            message_format.sub_service_type = PERIPHERAL_LIGHT_SERVICE_PLS_LIGHT_MODE_SUP;

            pb_ostream_t encode_stream = pb_ostream_from_buffer(&item.data[sizeof(ble_end_cmd_st)], NUS_C_PERIPH_TX_BUFF_SIZE);
            pb_encode(&encode_stream, peripheral_light_format_fields, &message_format);

            length = encode_stream.bytes_written;
            pb_crc = CRC_Calc8_Table_L(&item.data[sizeof(ble_end_cmd_st)], length);

            ble_periph_contact_end_cmd_construct(item.data,message_format.service_type, message_format.sub_service_type, \
                message_format.operate_type, 0, length, pb_crc);
            item.length = length + sizeof(ble_status_cmd_st);

            ble_periph_contact_mailbox_queue_data_push(&item, channel);
        }
    }
}
//请求所有能支持的配置
void periph_light_auto_config_request(const uint8_t* ble_mac)
{
    uint8_t channel;
    if(ble_periph_nus_c_channel_get(ble_mac,&channel)){
        ble_periph_contact item;
        if(ble_periph_contact_request_buffer(&item)){
            uint8_t pb_crc = 0;
            uint16_t length = 0;
            peripheral_light_format message_format;
            memset(&message_format, 0, sizeof(peripheral_light_format));

            message_format.service_type = PERIPHERAL_SERVICE_TYPE_PST_LIGHT;
            message_format.operate_type = PERIPHERAL_OPERATE_TYPE_POT_GET;

            message_format.has_sub_service_type = true;
            message_format.sub_service_type = PERIPHERAL_LIGHT_SERVICE_PLS_LIGHT_CFG_SUP;

            pb_ostream_t encode_stream = pb_ostream_from_buffer(&item.data[sizeof(ble_end_cmd_st)], NUS_C_PERIPH_TX_BUFF_SIZE);
            pb_encode(&encode_stream, peripheral_light_format_fields, &message_format);

            length = encode_stream.bytes_written;
            pb_crc = CRC_Calc8_Table_L(&item.data[sizeof(ble_end_cmd_st)], length);

            ble_periph_contact_end_cmd_construct(item.data,message_format.service_type, message_format.sub_service_type, \
                message_format.operate_type, 0, length, pb_crc);
            item.length = length + sizeof(ble_status_cmd_st);

            ble_periph_contact_mailbox_queue_data_push(&item, channel);
        }
    }
}

//请求所有配置的开关
void periph_light_all_config_request(const uint8_t* ble_mac)
{
    uint8_t channel;
    if(ble_periph_nus_c_channel_get(ble_mac,&channel)){
        ble_periph_contact item;
        if(ble_periph_contact_request_buffer(&item)){
            uint8_t pb_crc = 0;
            uint16_t length = 0;
            peripheral_light_format message_format;
            memset(&message_format, 0, sizeof(peripheral_light_format));

            message_format.service_type = PERIPHERAL_SERVICE_TYPE_PST_LIGHT;
            message_format.operate_type = PERIPHERAL_OPERATE_TYPE_POT_GET;

            message_format.has_sub_service_type = true;
            message_format.sub_service_type = PERIPHERAL_LIGHT_SERVICE_PLS_LIGHT_AUTO_CONFIG;

            pb_ostream_t encode_stream = pb_ostream_from_buffer(&item.data[sizeof(ble_end_cmd_st)], NUS_C_PERIPH_TX_BUFF_SIZE);
            pb_encode(&encode_stream, peripheral_light_format_fields, &message_format);

            length = encode_stream.bytes_written;
            pb_crc = CRC_Calc8_Table_L(&item.data[sizeof(ble_end_cmd_st)], length);

            ble_periph_contact_end_cmd_construct(item.data,message_format.service_type, message_format.sub_service_type, \
                message_format.operate_type, 0, length, pb_crc);
            item.length = length + sizeof(ble_status_cmd_st);
            ble_periph_contact_mailbox_queue_data_push(&item, channel);
        }
    }
}

void periph_pb_light_auto_config_set(const uint8_t* ble_mac, uint8_t auto_sleep, uint8_t auto_light, uint8_t brake_swtich, uint8_t team_ride)
{
    uint8_t channel;
    if(ble_periph_nus_c_channel_get(ble_mac,&channel)){
        ble_periph_contact item;
        if(ble_periph_contact_request_buffer(&item)){
            uint8_t pb_crc = 0;
            uint16_t length = 0;
            peripheral_light_format message_format;
            memset(&message_format, 0, sizeof(peripheral_light_format));

            message_format.service_type = PERIPHERAL_SERVICE_TYPE_PST_LIGHT;
            message_format.operate_type = PERIPHERAL_OPERATE_TYPE_POT_SET;

            message_format.has_sub_service_type = true;
            message_format.sub_service_type = PERIPHERAL_LIGHT_SERVICE_PLS_LIGHT_AUTO_CONFIG;

            message_format.has_message = true;
            message_format.message.has_auto_config = true;
            message_format.message.auto_config.has_auto_sleep = true;
            message_format.message.auto_config.auto_sleep = auto_sleep;
            message_format.message.auto_config.has_auto_light = true;
            message_format.message.auto_config.auto_light = auto_light;
            message_format.message.auto_config.has_brake_swtich = true;
            message_format.message.auto_config.brake_swtich = brake_swtich;
            message_format.message.auto_config.has_team_ride = true;
            message_format.message.auto_config.team_ride = team_ride;

            pb_ostream_t encode_stream = pb_ostream_from_buffer(&item.data[sizeof(ble_end_cmd_st)], NUS_C_PERIPH_TX_BUFF_SIZE);
            pb_encode(&encode_stream, peripheral_light_format_fields, &message_format);

            length = encode_stream.bytes_written;
            pb_crc = CRC_Calc8_Table_L(&item.data[sizeof(ble_end_cmd_st)], length);

            ble_periph_contact_end_cmd_construct(item.data,message_format.service_type, message_format.sub_service_type, \
                message_format.operate_type, 0, length, pb_crc);
            item.length = length + sizeof(ble_status_cmd_st);

            ble_periph_contact_mailbox_queue_data_push(&item, channel);

            // periph_light_mode_updated(channel, mode);
        }
    }
}

//跟随码表功能设置,可以通过enable设置灯是否要关机或者休眠或者触发光感关灯
void periph_pb_follow_cfg_set(const uint8_t* ble_mac, uint8_t sub_service_type, uint8_t flag)
{
    uint8_t channel;
    if(ble_periph_nus_c_channel_get(ble_mac,&channel)){
        ble_periph_contact item;
        if(ble_periph_contact_request_buffer(&item)){
            uint8_t pb_crc = 0;
            uint16_t length = 0;
            peripheral_light_format message_format;
            memset(&message_format, 0, sizeof(peripheral_light_format));

            message_format.service_type = PERIPHERAL_SERVICE_TYPE_PST_LIGHT;
            message_format.operate_type = PERIPHERAL_OPERATE_TYPE_POT_SET;

            message_format.has_sub_service_type = true;
            message_format.sub_service_type = sub_service_type;
            message_format.has_message = true;

            if (sub_service_type == PERIPHERAL_LIGHT_SERVICE_PLS_SLEEP_FOLLOW)
            {
                message_format.message.has_sleep_flag = true;
                message_format.message.sleep_flag = flag;
            }
            else if (sub_service_type == PERIPHERAL_LIGHT_SERVICE_PLS_AUTOLIGHT_FOLLOW)
            {
                message_format.message.has_autolight_flag = true;
                message_format.message.autolight_flag = flag;
            }
            else if (sub_service_type == PERIPHERAL_LIGHT_SERVICE_PLS_SYC_OFF)
            {
                message_format.message.has_off_flag = true;
                message_format.message.off_flag = flag;
            }
            else
            {
                //todo other sync settings
            }

            pb_ostream_t encode_stream = pb_ostream_from_buffer(&item.data[sizeof(ble_end_cmd_st)], NUS_C_PERIPH_TX_BUFF_SIZE);
            pb_encode(&encode_stream, peripheral_light_format_fields, &message_format);

            length = encode_stream.bytes_written;
            pb_crc = CRC_Calc8_Table_L(&item.data[sizeof(ble_end_cmd_st)], length);

            ble_periph_contact_end_cmd_construct(item.data,message_format.service_type, message_format.sub_service_type, \
                message_format.operate_type, 0, length, pb_crc);
            item.length = length + sizeof(ble_status_cmd_st);

            ble_periph_contact_mailbox_queue_data_push(&item, channel);
        }
    }
}
#if 0
void periph_pb_light_autolight_follow_set(const uint8_t* ble_mac, uint8_t enable)
{
    uint8_t channel;
    if(ble_periph_nus_c_channel_get(ble_mac,&channel)){
        ble_periph_contact item;
        if(ble_periph_contact_request_buffer(&item)){
            uint8_t pb_crc = 0;
            uint16_t length = 0;
            peripheral_light_format message_format;
            memset(&message_format, 0, sizeof(peripheral_light_format));

            message_format.service_type = PERIPHERAL_SERVICE_TYPE_PST_LIGHT;
            message_format.operate_type = PERIPHERAL_OPERATE_TYPE_POT_SET;

            message_format.has_sub_service_type = true;
            message_format.sub_service_type = PERIPHERAL_LIGHT_SERVICE_PLS_AUTOLIGHT_FOLLOW;

            message_format.has_message = true;
            message_format.message.has_autolight_flag = true;
            message_format.message.autolight_flag = enable;

            pb_ostream_t encode_stream = pb_ostream_from_buffer(&item.data[sizeof(ble_end_cmd_st)], NUS_C_PERIPH_TX_BUFF_SIZE);
            pb_encode(&encode_stream, peripheral_light_format_fields, &message_format);

            length = encode_stream.bytes_written;
            pb_crc = CRC_Calc8_Table_L(&item.data[sizeof(ble_end_cmd_st)], length);

            ble_periph_contact_end_cmd_construct(item.data,message_format.service_type, message_format.sub_service_type, \
                message_format.operate_type, 0, length, pb_crc);
            item.length = length + sizeof(ble_status_cmd_st);

            ble_periph_contact_mailbox_queue_data_push(&item, channel);
        }
    }
}
#endif

void periph_pb_light_mode_set(const uint8_t* ble_mac, PERIPHERAL_LIGHT_MODE mode)
{
    uint8_t channel;
    if (mode && ble_periph_nus_c_channel_get(ble_mac, &channel)) {
        ble_periph_contact item;
        if(ble_periph_contact_request_buffer(&item)){
            uint8_t pb_crc = 0;
            uint16_t length = 0;
            peripheral_light_format message_format;
            memset(&message_format, 0, sizeof(peripheral_light_format));

            message_format.service_type = PERIPHERAL_SERVICE_TYPE_PST_LIGHT;
            message_format.operate_type = PERIPHERAL_OPERATE_TYPE_POT_SET;

            message_format.has_sub_service_type = true;
            message_format.sub_service_type = PERIPHERAL_LIGHT_SERVICE_PLS_LIGHT_CURRENT;

            message_format.has_message = true;
            message_format.message.has_cur_mode = true;
            message_format.message.cur_mode = mode;
            pb_ostream_t encode_stream = pb_ostream_from_buffer(&item.data[sizeof(ble_end_cmd_st)], NUS_C_PERIPH_TX_BUFF_SIZE);
            pb_encode(&encode_stream, peripheral_light_format_fields, &message_format);

            length = encode_stream.bytes_written;
            pb_crc = CRC_Calc8_Table_L(&item.data[sizeof(ble_end_cmd_st)], length);

            ble_periph_contact_end_cmd_construct(item.data,message_format.service_type, message_format.sub_service_type, \
                message_format.operate_type, 0, length, pb_crc);
            item.length = length + sizeof(ble_status_cmd_st);

            ble_periph_contact_mailbox_queue_data_push(&item, channel);

            // periph_light_mode_updated(channel, mode);
        }
    }
}

void periph_pb_light_status_handle(uint8_t* buf, uint8_t channel_id)
{
    if (buf[0] == enum_BLE_NOTICE_CMD) {
        if(buf[1] == PERIPHERAL_SERVICE_TYPE_PST_LIGHT)
        {
            if (buf[2] == PERIPHERAL_LIGHT_SERVICE_PLS_LIGHT_CURRENT){                 //模式改变的时候同时也要更新剩余使用时间,否则会有延迟显示
                ble_status_cmd_st* ble_status_cmd_s = (ble_status_cmd_st*)buf;
                uint32_t    left_time = 0;
                uint8_t bytes[4];
                memcpy(bytes, &ble_status_cmd_s->reserved3[3], 4);                   //reserved3的4~7用来存储剩余时间
                
                for (int i = 3; i >= 0; i--) {
                    left_time <<= 8;  
                    left_time |= bytes[i];  
                }  
                periph_light_get_bat_left_time(channel_id, left_time);                                      //更新电池剩余可用时间
                periph_light_mode_updated(channel_id, ble_status_cmd_s->status);
            }
            else if (buf[2] == PERIPHERAL_LIGHT_SERVICE_PLS_REMAINING_TIME) {
                ble_status_cmd_st* ble_status_cmd_s = (ble_status_cmd_st*)buf;
                uint32_t    left_time = 0;
                uint8_t bytes[4];
                memcpy(bytes, &ble_status_cmd_s->reserved3[3], 4);                   //reserved3的4~7用来存储剩余时间
                
                for (int i = 3; i >= 0; i--) {
                    left_time <<= 8;  
                    left_time |= bytes[i];  
                }
                periph_light_get_bat_left_time(channel_id, left_time);                                      //更新电池剩余可用时间
            }
            else if (buf[2] == PERIPHERAL_LIGHT_SERVICE_PLS_LIGHT_AUTO_CONFIG) {                            //APP端对灯的智能功能配置修改会通过灯转发到码表，此时码表请求一次所有配置选项
                const uint8_t* ble_mac = ble_periph_nus_c_channel_get_mac_addr(channel_id);
                if (ble_mac)
                {
                    periph_light_all_config_request(ble_mac);
                }
            }
        }
        else if (buf[1] == PERIPHERAL_SERVICE_TYPE_PST_INFO) {                                  //注: INFO的PB目前
            ble_status_cmd_st* ble_status_cmd_s = (ble_status_cmd_st*)buf;
            sensor_module_evt_handler evt_handler = sensor_module_evt_handler_get();
            if(ble_status_cmd_s->status == enmuPOWER_ERR_STATUS)
            {
                if (evt_handler)
                {
                    const uint8_t* ble_mac = ble_periph_nus_c_channel_get_mac_addr(channel_id);
                    memset(ble_light_sensor_id.ble_mac_addr, 0, sizeof(ble_light_sensor_id.ble_mac_addr));
                    memcpy(ble_light_sensor_id.ble_mac_addr, ble_mac, BLE_GAP_ADDR_LEN);
                    evt_handler(EVENT_SENSOR_LOW_POWER, &ble_light_sensor_id, SENSOR_TYPE_LIGHT, SENSOR_RADIO_TYPE_BLE, 0);
                }
            }
            if(ble_status_cmd_s->sub_service_type == PERIPHERAL_LIGHT_SERVICE_PLS_LIGHT_CURRENT)
            {
                periph_light_get_bat_pct(channel_id, ble_status_cmd_s->reserved3[7]);
            }                                 //
        }
        
    }
    else if (buf[0] == enum_BLE_STATUS_CMD) {
        ble_periph_contact_release_buffer(channel_id);
    }
}


static const uint8_t light_type_trans[] = {

    BLE_LIGHT_UNDEFINE,                        //未定义类型
    BLE_LIGHT_TAIL,                                 //尾灯
    BLE_LIGHT_HEAD,                                 //前灯
    BLE_LIGHT_UNDEFINE,                         //未定义类型
    BLE_LIGHT_SIGNAL_LEFT,                      //左转向
    BLE_LIGHT_SIGNAL_RIGHT,                     //右转向
    BLE_LIGHT_SIGNAL_ALL,                       //信号灯, 可配置
    BLE_LIGHT_UNDEFINE,                         //未定义类型
    BLE_LIGHT_UNDEFINE,                         //未定义类型
    BLE_LIGHT_UNDEFINE,                         //未定义类型
    BLE_LIGHT_UNDEFINE,                         //未定义类型
    BLE_LIGHT_UNDEFINE,                         //未定义类型
    BLE_LIGHT_UNDEFINE,                         //未定义类型
    BLE_LIGHT_UNDEFINE,                         //未定义类型
    BLE_LIGHT_UNDEFINE,                         //未定义类型
    BLE_LIGHT_UNDEFINE,                         //未定义类型
};


void periph_pb_light_decode_handler(uint8_t* pb_buffer, uint16_t buffer_length, END_TYPE end_type, uint8_t channel_id)
{
    uint8_t status = false;
    uint32_t support_mode = 0;
    uint32_t support_ability = 0;
    peripheral_light_format message_format;
    memset(&message_format, 0, sizeof(peripheral_light_format));
    //rt_kprintf("periph_pb_light_decode_handler\n");
    message_format.message.light_modes_support.arg = &support_mode;
    message_format.message.light_modes_support.funcs.decode = repeated_light_mode_info_enum_decode;

    message_format.message.light_cfg_sup.arg = &support_ability;
    message_format.message.light_cfg_sup.funcs.decode = repeated_light_cfg_info_enum_decode;
    pb_istream_t decode_stream = pb_istream_from_buffer(pb_buffer, buffer_length);
    status = pb_decode(&decode_stream, peripheral_light_format_fields, &message_format);
    if(status)
    {
        switch(message_format.operate_type)
        {
            case PERIPHERAL_OPERATE_TYPE_POT_SET:

                break;
            case PERIPHERAL_OPERATE_TYPE_POT_GET:
                if(message_format.sub_service_type == PERIPHERAL_LIGHT_SERVICE_PLS_LIGHT_CURRENT){
                    periph_light_mode_updated(channel_id, message_format.message.cur_mode);
                    //periph_light_get_support_capability(channel_id);
                }else if(message_format.sub_service_type == PERIPHERAL_LIGHT_SERVICE_PLS_LIGHT_MODE_SUP){
                    const uint8_t* ble_mac = ble_periph_nus_c_channel_get_mac_addr(channel_id);
                    if (ble_mac) {
                        ble_light_set_supported_mode(ble_mac, support_mode);
                    }
                }else if(message_format.sub_service_type == PERIPHERAL_LIGHT_SERVICE_PLS_LIGHT_CFG_SUP){
                    periph_light_get_support_capability(channel_id, support_ability);
                }
                else if (message_format.sub_service_type == PERIPHERAL_LIGHT_SERVICE_PLS_LIGHT_TYPE)
                {
                    periph_light_get_type(channel_id, light_type_trans[message_format.message.light_type]);                                                                                     //TODO 解灯的模式
                }
                else if (message_format.sub_service_type == PERIPHERAL_LIGHT_SERVICE_PLS_LIGHT_AUTO_CONFIG)
                {
                    periph_light_get_all_cfg(channel_id, message_format.message.auto_config);
                }
                else if (message_format.sub_service_type == PERIPHERAL_LIGHT_SERVICE_PLS_REMAINING_TIME)
                {
                    periph_light_get_bat_left_time(channel_id, message_format.message.remain_time);
                }
                break;
            case PERIPHERAL_OPERATE_TYPE_POT_DEL:
                break;
            case PERIPHERAL_OPERATE_TYPE_POT_CON:
                break;
            default:
                break;
        }
        ble_periph_contact_release_buffer(channel_id);
    }
}

uint8_t ble_light_supported_custom_mode_get(void)
{
    return 8;
}

uint8_t ble_light_pb_mode_get(uint32_t mode)
{
    for(uint8_t i = 0; i < sizeof(light_mode_trans)/sizeof(light_mode_trans[0]); i++){
        if(light_mode_trans[i] == mode){
            // rt_kprintf("ble_light_pb_mode_get = %x  %d\n",light_mode_trans[i], i);
            return i;
        }
    }
    return PERIPHERAL_LIGHT_MODE_PLM_LIGHT_OFF;
}

#include <stdlib.h>
static rt_err_t light_ctl(int argc, char **argv)
{
    if (argc < 3){
        return 1;
    }
    int index = atoi(argv[1]);
    int mode = atoi(argv[2]);
    const uint8_t *mac_addr = sensor_ble_channel_mac_addr_get_by_index(index);
    if(mac_addr && sensor_ble_channel_status_get(mac_addr,SENSOR_BLE_CHANNEL_DISCOVERY)){
        rt_kprintf("light_ctl mode : %x:%x:%x:%x:%x:%x->%d\n",mac_addr[0],mac_addr[1],mac_addr[2],mac_addr[3],mac_addr[4],mac_addr[5], mode);
        periph_pb_light_mode_set(mac_addr, mode);
    }
    return 0;
}

MSH_CMD_EXPORT(light_ctl, usage: light_ctl 1);
