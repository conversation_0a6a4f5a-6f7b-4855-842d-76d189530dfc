<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <remote name="origin" fetch="." review="http://10.0.0.3:8081"/>
  
  <default remote="origin" revision="develop"/>
  
  <project name="app" revision="567fd74a7b5563e64509345bcd6541a49cd0d278" upstream="develop" dest-branch="develop"/>
  <project name="qw_algo/navigation" revision="d930065bbee6d6242f8fb714e6c15b2de1ebb4a6" upstream="cm_develop" dest-branch="cm_develop"/>
  <project name="qw_platform" revision="6a48872819ea2fd32f1a26c020577932bb9f656d" upstream="develop" dest-branch="develop"/>
  <project name="sifli" revision="af4ddca7240a6f49899dc8eb0c91dbb20006ecd4" upstream="develop" dest-branch="develop"/>
  <project name="tools" revision="0481c1e7d514aaa7eab72f37be760e7694f78ef3" upstream="develop" dest-branch="develop"/>
  <project name="vendor" revision="49aa2937fd4577cf36cf466b5c8768a5fc425c74" upstream="develop" dest-branch="develop"/>
</manifest>
