/***************************************Copyright (c)****************************************/
//                              <PERSON>han <PERSON> Technology Co., Ltd
//
//---------------------------------------File Info--------------------------------------------
// File name         : ble_pb.h
// Created by        : jiangzhen
// Descriptions      : BLE pb通信.h文件
//--------------------------------------------------------------------------------------------
// History           :
// 2019-06-08        :原始版本
/*********************************************************************************************/
#if !defined(__BLE_STATUS_H_)
#define __BLE_STATUS_H_
#if defined(__cplusplus)
extern "C"
{
#endif

#include "ble_cmd_common.h"
#include "qw_log.h"
#define BLE_PB_LOG_TAG "BLE_PB"

#define BLE_PB_LOG_LVL               LOG_LVL_DBG       //定义log等级

#if (BLE_PB_LOG_LVL >= LOG_LVL_DBG)
    #define BLE_PB_LOG_D(...)        QW_LOG_D(BLE_PB_LOG_TAG, __VA_ARGS__)   //定义debug等级的log输出
#else
    #define BLE_PB_LOG_D(...)
#endif

#if (BLE_PB_LOG_LVL >= LOG_LVL_INFO)
    #define BLE_PB_LOG_I(...)        QW_LOG_I(BLE_PB_LOG_TAG, __VA_ARGS__)   //定义info等级的log输出
#else
    #define BLE_PB_LOG_I(...)
#endif

#if (BLE_PB_LOG_LVL >= LOG_LVL_WARNING)
    #define BLE_PB_LOG_W(...)        QW_LOG_W(BLE_PB_LOG_TAG, __VA_ARGS__)  //定义warning等级的log输出
#else
    #define BLE_PB_LOG_W(...)
#endif

#if (BLE_PB_LOG_LVL >= LOG_LVL_ERROR)
    #define BLE_PB_LOG_E(...)        QW_LOG_E(BLE_PB_LOG_TAG, __VA_ARGS__)  //定义debug等级的log输出
#else
    #define BLE_PB_LOG_E(...)
#endif

#if (BLE_PB_LOG_LVL >= LOG_LVL_DBG)
    #define BLE_PB_LOG_HEX(TAG, width, buf, size)      QW_LOG_HEX(TAG, width, buf, size)
#else
    #define BLE_PB_LOG_HEX(TAG, width, buf, size)
#endif

typedef enum
{
    BLE_BOND_STATUS_INVALID = 0,
    BLE_BOND_STATUS_RESET_CONFIRM,  //鉴权用户点击确认恢复出厂设置
    BLE_BOND_STATUS_RESET_CANCEL,   //鉴权用户点击取消恢复出厂设置
} BLE_BOND_STATUS_ENUM;

typedef void (*ble_unbond_report_callback_t)(void);
  
//-------------------------------------------------------------------------------------------
// Function Name : ble_status_status_handle
// Purpose       : BLE通信状态处理函数
// Param[in]     : uint8_t *buf  
// Param[out]    : None
// Return type   : 
// Comment       : 2019-06-10
//-------------------------------------------------------------------------------------------
void ble_status_status_handle(uint8_t *buf);

//-------------------------------------------------------------------------------------------
// Function Name : ble_status_decode
// Purpose       : BLE PB解码接口函数
// Param[in]     : uint8_t * pb_buffer     
//                 uint16_t buffer_length  
//                 END_TYPE end_type       
// Param[out]    : None
// Return type   : 
// Comment       : 2019-06-08
//-------------------------------------------------------------------------------------------
void ble_status_decode(uint8_t * pb_buffer, uint16_t buffer_length, END_TYPE end_type);

/************************************************************************
 *@function:void ble_unbond_report_callback_register(ble_unbond_report_callback_t callback);
 *@brief:注册BLE解绑通知回调函数
 *@param: callback - 解绑通知回调函数指针
 *@return:null
*************************************************************************/
void ble_unbond_report_callback_register(ble_unbond_report_callback_t callback);

/************************************************************************
 *@function:void ble_unbond_report_callback_unregister(void);
 *@brief:注销BLE解绑通知回调函数
 *@param: null
 *@return:null
*************************************************************************/
void ble_unbond_report_callback_unregister(void);

/************************************************************************
 *@function:void ble_unbond_report_callback_notify(void);
 *@brief:BLE解绑通知上报
 *@param: null
 *@return:null
*************************************************************************/
void ble_unbond_report_callback_notify(void);

/************************************************************************
 *@function:void ble_bond_status_update(BLE_BOND_STATUS_ENUM status);
 *@brief:BLE绑定流程状态主动上报更新
 *@param: status - 绑定状态(BLE_BOND_STATUS_ENUM)
 *@return:null
*************************************************************************/
void ble_bond_status_update(BLE_BOND_STATUS_ENUM status);

/************************************************************************
 *@function:void ble_unbond_status_update(void);
 *@brief: BLE解绑状态主动上报更新
 *@param: null
 *@return:null
*************************************************************************/
void ble_unbond_status_update(void);

#ifdef __cplusplus
}
#endif
#endif	//__BLE_STATUS_H_


