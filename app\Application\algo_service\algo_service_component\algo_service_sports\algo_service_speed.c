﻿/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   algo_service_speed.c
@Time    :   2024/12/17 14:10:14
*
**************************************************************************/

/**
 * @file algo_service_speed.c
 * <AUTHOR> (<EMAIL>)
 * @brief 速度算法组件实现
 * @version 0.1
 * @date 2024-11-26
 *
 * @copyright Copyright (c) 2024-2025, <PERSON>han Qiwu Technology Co., Ltd
 *
 */
#include "algo_service_speed.h"
#include "activity_record/activity_fit_app.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_task.h"
#include "cfg_header_def.h"
#include "data_check.h"
#include "gps_bearing.h"
#include "params_default.h"
#include "qw_data_type.h"
#include "qw_gps.h"
#include "qw_sensor_common.h"   //TODO: ant输入数据结构放在哪？
#include "qw_time_util.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"
#include "utility.h"
#include "view_page_model_sports.h"

#define MAX_DIST_DELTA      1000000   // 2个点之间的最大距离  单位100*m
#define MIN_ALERT_TIME_DIFF 60        //最小的报警时间间隔,单位s
#define MIN_SPD             833
#define SPEED_FILTER_ALPHA  10        //速度滤波个数
#define INPUT_TIMEOUT       10        //输入数据超时时间，单位s

// 输入数据
typedef struct
{
    gps_pub_t gps;                      //GPS参数（暂定）
    uint16_t wheel_speed_spd;           //圈速, 1000*圈/s，来自速度计
    uint16_t wheel_delta_spd;           //使用ANT的数据前后两个点之间的距离差 ，圈，来自速度计
    uint16_t wheel_speed_cbsc;          //圈速, 1000*圈/s，来自速度踏频二合一
    uint16_t wheel_delta_cbsc;          //使用ANT的数据前后两个点之间的距离差 ，圈，来自速度踏频二合一
    uint16_t wheel_speed_pwr;           //圈速, 1000*圈/s，来自功率计
    uint16_t wheel_delta_pwr;           //使用ANT的数据前后两个点之间的距离差 ，圈，来自功率计
    uint16_t speed_fe;                  //mm/s，速度，来自骑行台
    uint16_t speed_lev;                 //0.1km/h，速度，来自ebike
    uint16_t lev_wheel_circumference;   //lev轮径

    //跑步
    uint16_t step_length;            //mm
    uint16_t cadence;                //spm
    uint8_t step_count;              //127 rollover. 增量值

    saving_status_e saving_status;   //数据记录的状态
} algo_speed_sub_t;

static algo_speed_sub_t s_algo_in = {0};

// 发布数据
static algo_speed_pub_t s_algo_out = {0};

static uint8_t s_last_step_count = 0;

// 输入超时计时器
static uint8_t s_timeout_gps;    //GPS超时计时器
static uint8_t s_timeout_spd;    //速度计超时计时器
static uint8_t s_timeout_cbsc;   //速度踏频二合一超时计时器
static uint8_t s_timeout_pwr;    //功率计超时计时器
static uint8_t s_timeout_fe;     //骑行台超时计时器
static uint8_t s_timeout_lev;    //ebike超时计时器
static uint8_t s_timeout_rd;     //跑步动态超时计时器

// 本算法打开标记
static bool s_is_open = false;

// 输入超时计时器初始化
static void timeout_timer_init(void)
{
    s_timeout_gps = 0;
    s_timeout_spd = 0;
    s_timeout_cbsc = 0;
    s_timeout_pwr = 0;
    s_timeout_fe = 0;
    s_timeout_lev = 0;
    s_timeout_rd = 0;
}

// 输入超时处理
static void input_timeout_handler(algo_speed_sub_t *algo_in)
{
    if (INPUT_TIMEOUT <= s_timeout_gps)
    {
        algo_in->gps.latitude.value = 0x7fffffff;
        algo_in->gps.latitude.scale = 1;
        algo_in->gps.longitude.value = 0x7fffffff;
        algo_in->gps.longitude.scale = 1;
        algo_in->gps.speed = 0;
    }
    else
    {
        s_timeout_gps++;
    }

    if (INPUT_TIMEOUT <= s_timeout_spd)
    {
        algo_in->wheel_speed_spd = 0xffff;
        algo_in->wheel_delta_spd = 0;
    }
    else
    {
        s_timeout_spd++;
    }

    if (INPUT_TIMEOUT <= s_timeout_cbsc)
    {
        algo_in->wheel_speed_cbsc = 0xffff;
        algo_in->wheel_delta_cbsc = 0;
    }
    else
    {
        s_timeout_cbsc++;
    }

    if (INPUT_TIMEOUT <= s_timeout_pwr)
    {
        algo_in->wheel_speed_pwr = 0xffff;
        algo_in->wheel_delta_pwr = 0;
    }
    else
    {
        s_timeout_pwr++;
    }

    if (INPUT_TIMEOUT <= s_timeout_fe)
    {
        algo_in->speed_fe = 0xffff;
    }
    else
    {
        s_timeout_fe++;
    }

    if (INPUT_TIMEOUT <= s_timeout_lev)
    {
        algo_in->speed_lev = 0xffff;
        algo_in->lev_wheel_circumference = 0xffff;
    }
    else
    {
        s_timeout_lev++;
    }

    if (INPUT_TIMEOUT <= s_timeout_rd)
    {
        algo_in->step_length = 0xffff;
        algo_in->cadence = 0xffff;
        algo_in->step_count = 0xff;
    }
    else
    {
        s_timeout_rd++;
    }
}

static bool check_wheel_size(uint16_t wheel_size)
{
    return (wheel_size > 0 && wheel_size <= 4095);
}

//将输入的速度及速度区间设置转换成速度区间(*10)。区间有没有0区取决于区间配置数组的[0]值是否大于0。默认心率有0区，区间显示从0.1到5.9，与功率不同
static uint16_t get_spd_zone(const uint32_t *spd_zone, uint8_t zone_num, uint32_t spd)
{
    uint32_t *p_zone_8 = NULL;
    uint16_t zone_out_10 = 0xffff;

    //Calc digital zone.
    if (UINT32_MAX == spd || NULL == spd_zone || 0 == zone_num || ALGO_TIME_IN_SPEED_ZONE_COUNT + 1 < zone_num)
    {
        zone_out_10 = 0xffff;
    }
    else   //心率区间储存了最大心率，所以不用MAX_HR计算
    {
        p_zone_8 = (uint32_t *) spd_zone;

        if (spd >= p_zone_8[zone_num - 1])           //达到最大心率
        {
            zone_out_10 = (zone_num - 1) * 10 + 9;   //区间5.9
        }
        else if (spd < p_zone_8[0])                  //处于第0个区间
        {
            zone_out_10 = spd * 10 / p_zone_8[0];
        }
        else
        {
            for (uint8_t i = 1; i < zone_num; i++)
            {
                if (spd < p_zone_8[i])
                {
                    //找到所在区间
                    zone_out_10 = i * 10 + (spd - p_zone_8[i - 1]) * 10 / (p_zone_8[i] - p_zone_8[i - 1]);
                    break;
                }
            }
        }
    }

    return zone_out_10;
}

static uint16_t get_pace_zone(const uint32_t *pace_zone, uint8_t zone_num, uint16_t pace)
{
    uint32_t *p_zone_8 = NULL;
    uint16_t zone_out_10 = 0xffff;

    //Calc digital zone.
    if (UINT16_MAX == pace || NULL == pace_zone || 0 == zone_num || ALGO_TIME_IN_SPEED_ZONE_COUNT + 1 < zone_num)
    {
        zone_out_10 = 0xffff;
    }
    else   //心率区间储存了最大心率，所以不用MAX_HR计算
    {
        p_zone_8 = (uint32_t *) pace_zone;

        if (pace <= p_zone_8[zone_num - 1])          //达到最低配速(速度最快)
        {
            zone_out_10 = (zone_num - 1) * 10 + 9;   //区间5.9
        }
        else if (pace > p_zone_8[0])                 //处于第0个区间
        {
            zone_out_10 = pace * 10 / p_zone_8[0];
        }
        else
        {
            for (uint8_t i = 1; i < zone_num; i++)
            {
                if (pace > p_zone_8[i])
                {
                    //找到所在区间
                    zone_out_10 = i * 10 + (p_zone_8[i - 1] - pace) * 10 / (p_zone_8[i - 1] - p_zone_8[i]);
                    break;
                }
            }
        }
    }

    return zone_out_10;
}

//速度警示检测
static void auto_alert_spd_check(sports_alert_t *p_alert, uint16_t speed)
{
    uint16_t spd = speed;
    SPORTTYPE sport_type = get_current_sport_mode();
    int spd_alert_used = get_sport_remind_en(sport_type, SPORT_REMIND_SPEED, MAIN_EN);
    int high_alert_used = get_sport_remind_en(sport_type, SPORT_REMIND_SPEED, HIGH_EN);
    int low_alert_used = get_sport_remind_en(sport_type, SPORT_REMIND_SPEED, LOW_EN);
    static uint32_t time_spd_alert_high = 0;
    static uint32_t time_spd_alert_low = 0;
    uint16_t spd_max_threshold = get_sport_remind_value(sport_type, SPORT_REMIND_SPEED, true);    //警示阈值
    uint16_t spd_min_threshold = get_sport_remind_value(sport_type, SPORT_REMIND_SPEED, false);   //警示阈值
    sports_alert_t alert_msg = {0, 0, 0, 0};
    uint32_t runtime_ms = get_boot_msec();

    //TEST
    // spd_alert_used = true;
    // spd_max_threshold = 10000;

    if (NULL == p_alert)
    {
        return;
    }

    if (spd_alert_used)      //当前报警开启
    {
        if (0xffff == spd)   //速度无效
        {
            time_spd_alert_high = 0;
            time_spd_alert_low = 0;
        }
        else if (spd > spd_max_threshold)   //需要报警
        {
            alert_msg.high_alert_status = high_alert_used ? true : false;
            alert_msg.low_alert_status = false;
            alert_msg.low_alert_event = false;

            //时间间隔需要满足要求
            if (0 == time_spd_alert_high || runtime_ms / 1000 - time_spd_alert_high > MIN_ALERT_TIME_DIFF)
            {
                time_spd_alert_high = runtime_ms / 1000;   //记录本次报警的时间
                alert_msg.high_alert_event = high_alert_used ? true : false;
            }
            else
            {
                alert_msg.high_alert_event = false;
            }
        }
        else if (spd < spd_min_threshold && MIN_SPD < spd)
        {
            alert_msg.high_alert_status = false;
            alert_msg.high_alert_event = false;
            alert_msg.low_alert_status = low_alert_used ? true : false;

            //时间间隔需要满足要求
            if (0 == time_spd_alert_low || runtime_ms / 1000 - time_spd_alert_low > MIN_ALERT_TIME_DIFF)
            {
                time_spd_alert_low = runtime_ms / 1000;   //记录本次报警的时间
                alert_msg.low_alert_event = low_alert_used ? true : false;
            }
            else
            {
                alert_msg.low_alert_event = false;
            }
        }
        else   //正常范围内，不警示
        {
            alert_msg.high_alert_status = false;
            alert_msg.low_alert_status = false;
            alert_msg.high_alert_event = false;
            alert_msg.low_alert_event = false;
        }
    }

    memcpy(p_alert, &alert_msg, sizeof(sports_alert_t));
}

//配速警示检测
static void auto_alert_pace_check(sports_alert_t *p_alert, uint16_t pace)
{
    uint16_t pce = pace;
    SPORTTYPE sport_type = get_current_sport_mode();
    int pace_alert_used = get_sport_remind_en(sport_type, SPORT_REMIND_PACE, MAIN_EN);
    int high_alert_used = get_sport_remind_en(sport_type, SPORT_REMIND_PACE, HIGH_EN);
    int low_alert_used = get_sport_remind_en(sport_type, SPORT_REMIND_PACE, LOW_EN);
    static uint32_t time_pace_alert_high = 0;
    static uint32_t time_pace_alert_low = 0;
    uint16_t pace_max_threshold = get_sport_remind_value(sport_type, SPORT_REMIND_PACE, true);    //警示阈值
    uint16_t pace_min_threshold = get_sport_remind_value(sport_type, SPORT_REMIND_PACE, false);   //警示阈值
    sports_alert_t alert_msg = {0, 0, 0, 0};
    uint32_t runtime_ms = get_boot_msec();

    //TEST
    // pace_alert_used = true;
    // pace_max_threshold = 300;

    if (NULL == p_alert)
    {
        return;
    }

    if (pace_alert_used)                 //当前报警开启
    {
        if (0xffff == pce || 0 == pce)   //速度无效
        {
            time_pace_alert_high = 0;
            time_pace_alert_low = 0;
        }
        else if (pce < pace_max_threshold)   //需要报警
        {
            alert_msg.high_alert_status = high_alert_used ? true : false;
            alert_msg.low_alert_status = false;
            alert_msg.low_alert_event = false;

            //时间间隔需要满足要求
            if (0 == time_pace_alert_high || runtime_ms / 1000 - time_pace_alert_high > MIN_ALERT_TIME_DIFF)
            {
                time_pace_alert_high = runtime_ms / 1000;   //记录本次报警的时间
                alert_msg.high_alert_event = high_alert_used ? true : false;
            }
            else
            {
                alert_msg.high_alert_event = false;
            }
        }
        else if (pce > pace_min_threshold)
        {
            alert_msg.high_alert_status = false;
            alert_msg.high_alert_event = false;
            alert_msg.low_alert_status = low_alert_used ? true : false;

            //时间间隔需要满足要求
            if (0 == time_pace_alert_low || runtime_ms / 1000 - time_pace_alert_low > MIN_ALERT_TIME_DIFF)
            {
                time_pace_alert_low = runtime_ms / 1000;   //记录本次报警的时间
                alert_msg.low_alert_event = low_alert_used ? true : false;
            }
            else
            {
                alert_msg.low_alert_event = false;
            }
        }
        else   //正常范围内，不警示
        {
            alert_msg.high_alert_status = false;
            alert_msg.low_alert_status = false;
            alert_msg.high_alert_event = false;
            alert_msg.low_alert_event = false;
        }
    }

    memcpy(p_alert, &alert_msg, sizeof(sports_alert_t));
}

//数据来源
typedef enum {
    enum_data_invalid,
    enum_data_from_fe,
    enum_data_from_pwr,
    enum_data_from_spd,
    enum_data_from_cbsc,
    enum_data_from_cad,
    enum_data_from_gps,
    enum_data_from_lev,
    enum_data_from_gm,
} data_from_e;

#define DATA_SOURCE_NUM 9   //数据来源个数

// typedef struct
// {
//     data_from_e spd[DATA_SOURCE_NUM];
//     data_from_e cad[DATA_SOURCE_NUM];
//     data_from_e pwr[DATA_SOURCE_NUM];
// } cfg_data_priority_t; //数据来源优先级

/**
 * @brief 速度滤波
 *
 * @param algo_out 输出数据
 */
static void algo_speed_filter(algo_speed_pub_t *algo_out)
{
    SPORTTYPE sport_type = get_current_sport_mode();

    if ((SPORTSTYPE_CYCLING > sport_type || SPORTSTYPE_TRIP_CYCLING < sport_type) && 0xffffffff != algo_out->enhanced_speed)   //非骑行
    {
        // util_lp_filter((int32_t *) &algo_out->enhanced_speed, SPEED_FILTER_ALPHA, false);
    }
}

/**
 * @brief 算法处理
 *
 * @param algo_out 输出数据
 * @param algo_in 输入数据
 */
static void algo_speed_deal(algo_speed_pub_t *algo_out, algo_speed_sub_t *algo_in)
{
    static uint16_t wheel_delta_fraction_1000 = 0;
    const data_from_e priority[DATA_SOURCE_NUM] = {enum_data_from_fe,  enum_data_from_lev,  enum_data_from_pwr,
                                                   enum_data_from_spd, enum_data_from_cbsc, enum_data_from_gps};   //速度来源优先级
    int8_t speed_is_ant = (priority[5] == enum_data_from_gps);                                                     //是否ANT速度优先
    uint8_t i = 0;
    float f_dist = 0.0;

    // 跑步无GPS时计算速度与距离
    SPORTTYPE sport_type = get_current_sport_mode();
    uint32_t step_count_delta = 0;

    if (enum_status_saving > algo_in->saving_status)
    {
        return;
    }

    //计算gps距离差
    gps_pub_t gps_in = {0};
    memcpy(&gps_in, &algo_in->gps, sizeof(gps_pub_t));

    algo_out->course = gps_in.course;
    algo_out->accuracy = gps_in.accuracy;

    util_pos_simple_distance_get(&f_dist, &gps_in.latitude, &gps_in.longitude, &algo_out->last_latitude, &algo_out->last_longitude);
    algo_out->gps_distance_delta = (uint32_t) (f_dist + 0.5);

    if (MAX_DIST_DELTA < algo_out->gps_distance_delta)
    {
        algo_out->gps_distance_delta = 0;
    }

    //更新上一次坐标
    if (!position_invalid_check(&gps_in.latitude) && !position_invalid_check(&gps_in.longitude))
    {
        memcpy(&algo_out->last_latitude, &algo_in->gps.latitude, sizeof(struct minmea_float));
        memcpy(&algo_out->last_longitude, &algo_in->gps.longitude, sizeof(struct minmea_float));
    }

    algo_out->wheel_speed = 0xffff;
    algo_out->wheel_delta = 0xffff;
    algo_out->spd_source = enum_spd_source_gps;

    //骑行速度才使用ANT优先级
    if (SPORTSTYPE_CYCLING <= sport_type && SPORTSTYPE_TRIP_CYCLING >= sport_type)   //骑行
    {
        // 数据优先级
        for (i = 0; i < DATA_SOURCE_NUM; i++)
        {
            if (enum_data_from_fe == priority[i] && 0xffff != algo_in->speed_fe)
            {
                algo_out->wheel_size = BIKE_WHEEL_DEFAULT;
                algo_out->wheel_speed = (uint32_t) (algo_in->speed_fe) * 1000 / BIKE_WHEEL_DEFAULT;
                wheel_delta_fraction_1000 += algo_out->wheel_speed;
                algo_out->wheel_delta = wheel_delta_fraction_1000 / 1000;
                wheel_delta_fraction_1000 -= algo_out->wheel_delta * 1000;

                algo_out->spd_source = enum_spd_source_fe;
                break;
            }

            if (enum_data_from_lev == priority[i] && 0xffff != algo_in->speed_lev)
            {
                algo_out->wheel_size = sensor_connect_wheel_size_get(SENSOR_TYPE_LEV);
                if (check_wheel_size(algo_out->wheel_size))
                {
                    algo_out->wheel_speed = (uint32_t) (algo_in->speed_lev) * 1000 * 1000 / (algo_out->wheel_size * 36);
                    wheel_delta_fraction_1000 += algo_out->wheel_speed;
                    algo_out->wheel_delta = wheel_delta_fraction_1000 / 1000;
                    wheel_delta_fraction_1000 -= algo_out->wheel_delta * 1000;

                    algo_out->spd_source = enum_spd_source_lev;
                    break;
                }
            }

            if (enum_data_from_pwr == priority[i] && 0xffff != algo_in->wheel_speed_pwr)
            {
                algo_out->wheel_speed = algo_in->wheel_speed_pwr;
                algo_out->wheel_delta = algo_in->wheel_delta_pwr;
                algo_out->wheel_size = BIKE_WHEEL_DEFAULT;
                algo_out->spd_source = enum_spd_source_pwr;
                break;
            }

            if (enum_data_from_spd == priority[i] && 0xffff != algo_in->wheel_speed_spd)
            {
                algo_out->wheel_size = sensor_connect_wheel_size_get(SENSOR_TYPE_SPD);
                if (check_wheel_size(algo_out->wheel_size))
                {
                    algo_out->wheel_speed = algo_in->wheel_speed_spd;
                    algo_out->wheel_delta = algo_in->wheel_delta_spd;

                    algo_out->spd_source = enum_spd_source_spd;
                    break;
                }
            }

            if (enum_data_from_cbsc == priority[i] && 0xffff != algo_in->wheel_speed_cbsc)
            {
                algo_out->wheel_size = sensor_connect_wheel_size_get(SENSOR_TYPE_CBSC);
                if (check_wheel_size(algo_out->wheel_size))
                {
                    algo_out->wheel_speed = algo_in->wheel_speed_cbsc;
                    algo_out->wheel_delta = algo_in->wheel_delta_cbsc;

                    algo_out->spd_source = enum_spd_source_cbsc;
                    break;
                }
            }
        }
    }

    if (0xffff != algo_out->wheel_delta && false != speed_is_ant)   // ANT优先
    {
        algo_out->distance_delta = (uint32_t) algo_out->wheel_delta * algo_out->wheel_size / 10;
        algo_out->enhanced_speed = (uint32_t) ((uint64_t) algo_out->wheel_speed * algo_out->wheel_size / 1000);
    }
    else if (0 != algo_in->gps.signal && false != speed_check(gps_in.speed, false))   //ANT无效则使用GPS距离//速度为0时不增加距离，解决GPS漂移导致距离增加
    {
        algo_out->distance_delta = algo_out->gps_distance_delta;
        algo_out->enhanced_speed = algo_in->gps.speed;
    }
    else   //两者都无效时为0
    {
        algo_out->distance_delta = 0;
        algo_out->enhanced_speed = 0;
    }

    if ((SPORTSTYPE_TREADMILL == sport_type || SPORTSTYPE_INDOOR_RUNNING == sport_type)   //室内跑步
        || ((SPORTSTYPE_RUNNING == sport_type || SPORTSTYPE_PLAYGROUND == sport_type || SPORTSTYPE_TRAIL_RUNNING == sport_type)
            && 0 == algo_in->gps.signal))                                                 //户外跑步且GPS无效
    {
        algo_out->spd_source = enum_spd_source_gm;
        if (0xffff != algo_in->step_length && 0xffff != algo_in->cadence)
        {
            algo_out->enhanced_speed = (uint32_t) algo_in->step_length * algo_in->cadence / 60;
            if (0xff != algo_in->step_count)
            {
                step_count_delta = ((uint32_t) algo_in->step_count + 128 - s_last_step_count) % 128;
                s_last_step_count = algo_in->step_count;
                algo_out->distance_delta = step_count_delta * algo_in->step_length / 10;
            }
        }
    }

    //速度滤波
    algo_speed_filter(algo_out);

    // 配速
    algo_out->pace = (0 == algo_out->enhanced_speed) ? UINT16_MAX : (uint16_t) (1000000 / algo_out->enhanced_speed);

    // 运动警示
    if (enum_status_saving == algo_in->saving_status)
    {
        auto_alert_spd_check(&algo_out->alert, (uint16_t) algo_out->enhanced_speed);
        auto_alert_pace_check(&algo_out->pace_alert, algo_out->pace);
    }

    // 区间时间
    uint32_t zone[ALGO_TIME_IN_SPEED_ZONE_COUNT + 1] = {0};

    if (SPORTSTYPE_RUNNING == sport_type || SPORTSTYPE_TREADMILL == sport_type || SPORTSTYPE_PLAYGROUND == sport_type || SPORTSTYPE_TRAIL_RUNNING == sport_type
        || SPORTSTYPE_WALKING == sport_type || SPORTSTYPE_INDOOR_RUNNING == sport_type || SPORTSTYPE_HIKING == sport_type)
    {
        // 配速区间
        for (i = 0; i < ALGO_TIME_IN_SPEED_ZONE_COUNT; i++)
        {
            zone[i] = get_user_info_pace_zone(i);
        }
        zone[ALGO_TIME_IN_SPEED_ZONE_COUNT] = 0;
        algo_out->speed_zone = get_pace_zone(zone, ALGO_TIME_IN_SPEED_ZONE_COUNT + 1, algo_out->pace);
    }
    else
    {
        // 速度区间
        zone[0] = 0;   //第一个是0
        for (i = 0; i < ALGO_TIME_IN_SPEED_ZONE_COUNT; i++)
        {
            zone[i + 1] = get_user_info_spd_ride_zone(i);
        }
        algo_out->speed_zone = get_spd_zone(zone, ALGO_TIME_IN_SPEED_ZONE_COUNT + 1, algo_out->enhanced_speed);
    }
}

/**
 * @brief 速度数据算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_gps_data_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_SPEED;
    head.input_type = DATA_ID_ALGO_GPS_DATA;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void ble_fe_sensor_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_SPEED;
    head.input_type = DATA_ID_RAW_FE_SENSOR;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void ble_lev_sensor_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_SPEED;
    head.input_type = DATA_ID_RAW_LEV_SENSOR;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void ble_pwr_sensor_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_SPEED;
    head.input_type = DATA_ID_RAW_PWR_SENSOR;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void ble_spd_sensor_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_SPEED;
    head.input_type = DATA_ID_RAW_SPD_SENSOR;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void ble_cbsc_sensor_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_SPEED;
    head.input_type = DATA_ID_RAW_CBSC_SENSOR;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_running_dynamics_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_SPEED;
    head.input_type = DATA_ID_ALGO_RUNNING_DYNAMICS;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_sports_ctrl_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_SPEED;
    head.input_type = DATA_ID_EVENT_SPORTS_CTRL;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 速度数据算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_speed_out_callback(const void *out, uint32_t len)
{
    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_ALGO_SPEED, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("%s publish error", __FUNCTION__);
    }
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node_gps[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_gps_data",
        .topic_id = DATA_ID_ALGO_GPS_DATA,
        .callback = algo_gps_data_in_callback,
    },
};
/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node_run[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_ALG_NAME_RUNNING_DYNAMICS,
        .topic_id = DATA_ID_ALGO_RUNNING_DYNAMICS,
        .callback = algo_running_dynamics_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
};
/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node_ride[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "ble_fe_sensor",
        .topic_id = DATA_ID_RAW_FE_SENSOR,
        .callback = ble_fe_sensor_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "ble_lev_sensor",
        .topic_id = DATA_ID_RAW_LEV_SENSOR,
        .callback = ble_lev_sensor_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "ble_pwr_sensor",
        .topic_id = DATA_ID_RAW_PWR_SENSOR,
        .callback = ble_pwr_sensor_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "ble_spd_sensor",
        .topic_id = DATA_ID_RAW_SPD_SENSOR,
        .callback = ble_spd_sensor_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "ble_cbsc_sensor",
        .topic_id = DATA_ID_RAW_CBSC_SENSOR,
        .callback = ble_cbsc_sensor_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
};

/**
 * @brief 速度数据算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_speed_init(void)
{
    return 0;
}

/**
 * @brief 打开速度数据算法
 *
 * @return int32_t 结果
 */
static int32_t algo_speed_open(void)
{
    if (s_is_open)
    {
        ALGO_COMP_LOG_W("%s already open", __FUNCTION__);
        return 0;
    }

    ALGO_COMP_LOG_D("%s open", __FUNCTION__);

    int32_t ret = 0;
    algo_speed_sub_t *algo_in = &s_algo_in;
    algo_speed_pub_t *algo_out = &s_algo_out;
    SPORTTYPE sport_type = get_current_sport_mode();

    s_is_open = true;

    //输入数据初始化
    algo_in->wheel_speed_spd = 0xffff;
    algo_in->wheel_delta_spd = 0;
    algo_in->wheel_speed_cbsc = 0xffff;
    algo_in->wheel_delta_cbsc = 0;
    algo_in->wheel_speed_pwr = 0xffff;
    algo_in->wheel_delta_pwr = 0;
    algo_in->speed_fe = 0xffff;
    algo_in->speed_lev = 0xffff;
    algo_in->lev_wheel_circumference = 0xffff;
    algo_in->step_length = 0xffff;
    algo_in->cadence = 0xffff;
    algo_in->step_count = 0;
    algo_in->saving_status = 0;
    algo_in->gps.latitude.value = 0x7fffffff;
    algo_in->gps.latitude.scale = 1;
    algo_in->gps.longitude.value = 0x7fffffff;
    algo_in->gps.longitude.scale = 1;
    algo_in->gps.speed = 0;

    //输出数据初始化
    memset(algo_out, 0, sizeof(algo_speed_pub_t));
    algo_out->last_latitude.value = 0x7fffffff;
    algo_out->last_latitude.scale = 1;
    algo_out->last_longitude.value = 0x7fffffff;
    algo_out->last_longitude.scale = 1;

    s_last_step_count = 0;

    sports_data_chart_init(DATATYPE_CHART_SPD_GRAPH);
    sports_data_chart_init(DATATYPE_CHART_PACE_GRAPH);
    timeout_timer_init();

    // 室内运动不订阅GPS
    if (get_sport_type_is_outdoor((SPORTTYPE) get_activity_sport_type()))
    {
        ret = algo_topic_list_subscribe(s_algo_topic_node_gps, sizeof(s_algo_topic_node_gps) / sizeof(s_algo_topic_node_gps[0]));
    }

    if (SPORTSTYPE_CYCLING <= sport_type && SPORTSTYPE_TRIP_CYCLING >= sport_type)   //骑行
    {
        return ((ret == 0) && algo_topic_list_subscribe(s_algo_topic_node_ride, sizeof(s_algo_topic_node_ride) / sizeof(s_algo_topic_node_ride[0])) == 0) ? 0
                                                                                                                                                          : -1;
    }
    else if ((SPORTSTYPE_RUNNING <= sport_type && SPORTSTYPE_INDOOR_RUNNING >= sport_type) ||           //跑步
            (sport_type >= SPORTSTYPE_MOUNTAINEERING && sport_type <= SPORTSTYPE_OUTDOOR_AEROBIC))      //所有户外类型
    {
        return ((ret == 0) && algo_topic_list_subscribe(s_algo_topic_node_run, sizeof(s_algo_topic_node_run) / sizeof(s_algo_topic_node_run[0])) == 0) ? 0 : -1;
    }
    else
    {
        return ret;
    }
}

/**
 * @brief feed速度数据算法
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_speed_feed(uint32_t input_type, void *data, uint32_t len)
{
    algo_speed_sub_t *algo_in = &s_algo_in;
    algo_speed_pub_t *algo_out = &s_algo_out;
    int32_t ret = -1;

    if (NULL == data || 0 == len)
    {
        ALGO_COMP_LOG_E("%s data is NULL or len is 0", __FUNCTION__);
        return -1;
    }

    switch (input_type)
    {
    case DATA_ID_ALGO_GPS_DATA:   //TODO: algo_gps_data结构还未对应
    {
        if (sizeof(gps_pub_t) == len)
        {
            const gps_pub_t *gps_data = (gps_pub_t *) data;
            memcpy(&algo_in->gps, gps_data, sizeof(gps_pub_t));
            s_timeout_gps = 0;
            ret = 0;
        }
    }
    break;
    case DATA_ID_RAW_FE_SENSOR:
    {
        if (sizeof(fe_data_t) == len)
        {
            const fe_data_t *fe_data = (fe_data_t *) data;
            algo_in->speed_fe = fe_data->speed;
            s_timeout_fe = 0;
            ret = 0;
        }
    }
    break;
#if ANT_SENSOR_LEV_ENABLED
    case DATA_ID_RAW_LEV_SENSOR:
    {
        if (sizeof(lev_data_t) == len)
        {
            const lev_data_t *lev_data = (lev_data_t *) data;
            algo_in->speed_lev = lev_data->lev_speed;
            algo_in->lev_wheel_circumference = lev_data->capability.wheel_circumference;
            s_timeout_lev = 0;
            ret = 0;
        }
    }
    break;
#endif  // ANT_SENSOR_LEV_ENABLED
    case DATA_ID_RAW_PWR_SENSOR:
    {
        if (sizeof(pwr_data_t) == len)
        {
            const pwr_data_t *pwr_data = (pwr_data_t *) data;
            algo_in->wheel_speed_pwr = pwr_data->wheel_speed;
            algo_in->wheel_delta_pwr += pwr_data->wheel_delta;   //如果传感器发布的频率较快，这里就要累加。下同。
            s_timeout_pwr = 0;
            ret = 0;
        }
    }
    break;
    case DATA_ID_RAW_SPD_SENSOR:
    {
        if (sizeof(spd_data_t) == len)
        {
            const spd_data_t *spd_data = (spd_data_t *) data;
            algo_in->wheel_speed_spd = spd_data->wheel_speed;
            algo_in->wheel_delta_spd += spd_data->wheel_delta;
            s_timeout_spd = 0;
            ret = 0;
        }
    }
    break;
    case DATA_ID_RAW_CBSC_SENSOR:
    {
        if (sizeof(cbsc_data_t) == len)
        {
            const cbsc_data_t *cbsc_data = (cbsc_data_t *) data;
            algo_in->wheel_speed_cbsc = cbsc_data->wheel_speed;
            algo_in->wheel_delta_cbsc += cbsc_data->wheel_delta;
            s_timeout_cbsc = 0;
            ret = 0;
        }
    }
    break;
    case DATA_ID_ALGO_RUNNING_DYNAMICS:
    {
        if (sizeof(algo_running_dynamics_pub_t) == len)
        {
            const algo_running_dynamics_pub_t *rd_data = (algo_running_dynamics_pub_t *) data;
            algo_in->step_length = rd_data->step_length;
            algo_in->cadence = rd_data->cadence;
            algo_in->step_count = rd_data->step_count;
            s_timeout_rd = 0;
            ret = 0;
        }
    }
    break;
    case DATA_ID_EVENT_SPORTS_CTRL:
    {
        if (sizeof(algo_sports_ctrl_t) == len)
        {
            const algo_sports_ctrl_t *sports_ctrl = (algo_sports_ctrl_t *) data;
            algo_in->saving_status = sports_ctrl->saving_status;

            //数据发布
            if (sports_ctrl->ctrl_type == enum_ctrl_null)
            {
                //算法处理
                algo_speed_deal(algo_out, algo_in);

                //数据发布
                algo_speed_out_callback(algo_out, sizeof(algo_speed_pub_t));

                //更新速度图表数据
                sports_data_chart_update(DATATYPE_CHART_SPD_GRAPH, algo_out->enhanced_speed);
                sports_data_chart_update(DATATYPE_CHART_PACE_GRAPH, algo_out->pace);

                //输入超时处理
                input_timeout_handler(algo_in);

                //计算后距离增量重置为0
                algo_in->wheel_delta_pwr = 0;
                algo_in->wheel_delta_spd = 0;
                algo_in->wheel_delta_cbsc = 0;
                algo_out->wheel_delta = 0;
                algo_out->distance_delta = 0;
                algo_out->gps_distance_delta = 0;
            }
            else if (sports_ctrl->ctrl_type == enum_ctrl_start) //开始运动时距离增量重置为0
            {
                algo_in->wheel_delta_pwr = 0;
                algo_in->wheel_delta_spd = 0;
                algo_in->wheel_delta_cbsc = 0;
                algo_out->wheel_delta = 0;
                algo_out->distance_delta = 0;
                algo_out->gps_distance_delta = 0;
            }

            ret = 0;
        }
    }
    break;
    default:
        break;
    }

    if (0 != ret)
    {
        ALGO_COMP_LOG_E("algo_speed_feed error, input_type=%d, len=%d", input_type, len);
    }

    return ret;
}

/**
 * @brief 关闭速度数据算法
 *
 * @return int32_t 结果
 */
static int32_t algo_speed_close(void)
{
    if (!s_is_open)
    {
        ALGO_COMP_LOG_W("%s already close", __FUNCTION__);
        return 0;
    }

    SPORTTYPE sport_type = get_current_sport_mode();

    // 室内运动不订阅GPS
    if (get_sport_type_is_outdoor((SPORTTYPE) get_activity_sport_type()))
    {
        algo_topic_list_unsubscribe(s_algo_topic_node_gps, sizeof(s_algo_topic_node_gps) / sizeof(s_algo_topic_node_gps[0]));
    }

    if (SPORTSTYPE_CYCLING <= sport_type && SPORTSTYPE_TRIP_CYCLING >= sport_type)   //骑行
    {
        algo_topic_list_unsubscribe(s_algo_topic_node_ride, sizeof(s_algo_topic_node_ride) / sizeof(s_algo_topic_node_ride[0]));
    }
    else if ((SPORTSTYPE_RUNNING <= sport_type && SPORTSTYPE_INDOOR_RUNNING >= sport_type) ||           //跑步
            (sport_type >= SPORTSTYPE_MOUNTAINEERING && sport_type <= SPORTSTYPE_OUTDOOR_AEROBIC))      //所有户外类型
    {
        algo_topic_list_unsubscribe(s_algo_topic_node_run, sizeof(s_algo_topic_node_run) / sizeof(s_algo_topic_node_run[0]));
    }

    s_is_open = false;
    ALGO_COMP_LOG_D("%s close", __FUNCTION__);
    return 0;
}

// 速度数据算法组件实现
static algo_compent_ops_t s_speed_algo = {
    .init = algo_speed_init,
    .open = algo_speed_open,
    .feed = algo_speed_feed,
    .close = algo_speed_close,
    .ioctl = NULL,
};

/**
 * @brief 速度组件注册
 *
 * @return int32_t 结果
 */
int32_t register_speed_algo(void)
{
    algo_compnent_register(ALGO_TYPE_SPEED, &s_speed_algo);
    return 0;
}
