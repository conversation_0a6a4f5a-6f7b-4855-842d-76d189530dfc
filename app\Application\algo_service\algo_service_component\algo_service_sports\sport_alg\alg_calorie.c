/************************************************************************​
*Copyright(c) 2024, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#include <stddef.h>
#include "alg_calorie.h"

//卡路里功率计算方案输入
typedef struct _CalorieByPowerCalcInput
{
    uint32_t timestamp;
    uint16_t power;                     //功率（w），0xFFFF表示无效
} CalorieByPowerCalcInput;

//卡路里心率计算方案初始化所需数据
typedef struct _CalorieByHrCalcInit
{
    float weight;                       //体重（kg）
    uint8_t gender;                     //性别，0-女性 1-男性
    uint8_t age;                        //年龄（岁）
} CalorieByHrCalcInit;

//卡路里心率方案输入
typedef struct _CalorieByHrCalcInput
{
    uint32_t timestamp;
    uint8_t hr;                         //心率（bpm），0xFF表示无效，0值无效
} CalorieByHrCalcInput;

//卡路里估计功率方案输入
typedef struct _CalorieByPwrEstCalcInput
{
    uint32_t timestamp;
    float pwr_est;                      //预估功率（w）
} CalorieByPwrEstCalcInput;

static void calorie_by_power_calculator_reset(CalorieByPowerCalculator *self);
static void calorie_by_hr_calculator_reset(CalorieByHrCalculator *self);
static void calorie_by_pwr_est_calculator_reset(CalorieByPwrEstCalculator *self);

//卡路里计算功率方案初始化
static void calorie_by_power_calculator_init(CalorieByPowerCalculator *self)
{
    calorie_by_power_calculator_reset(self);
}

//卡路里功率方案计算
//返回值：本次输入计算出的卡路里（Cal）
static float calorie_by_power_calculator_exec(CalorieByPowerCalculator *self, const CalorieByPowerCalcInput *input)
{
    if (self == NULL || input == NULL)
    {
        return 0.0f;
    }

    //首次初始化
    if (self->cnt == 0)
    {
        self->timestamp = input->timestamp;
        self->cnt += 1;
        return 0.0f;
    }

    //时间戳检查
    if (input->timestamp < self->timestamp || input->timestamp - self->timestamp > 30)
    {
        calorie_by_power_calculator_reset(self);
        return 0.0f;
    }

    //输入功率检查
    if (input->power == 0xFFFF)
    {
        calorie_by_power_calculator_reset(self);
        return 0.0f;
    }

    //时间变化量
    const uint32_t dt = input->timestamp - self->timestamp;
    //做功
    const float work = (float)(input->power * dt);

    self->timestamp = input->timestamp;
    self->cnt += 1;

    //做功为0，返回0即可
    if (work < 0.001f)
    {
        return 0.0f;
    }

    const float power = (float)input->power;
    const float power_squared = (float)((uint32_t)input->power * (uint32_t)input->power);

    //功率（做功）与卡路里之间的转换比
    float conversion_ratio = 0.0f;

    if (input->power < 40)
    {
        conversion_ratio = 0.0102f * power_squared - 0.7434f * power + 20.199f;
    }
    else if (input->power < 100)
    {
        conversion_ratio = 0.0004f * power_squared - 0.0753f * power + 8.9698f;
    }
    else if (input->power < 200)
    {
        conversion_ratio = 5.5907f - 0.0047f * power;
    }
    else if (input->power < 400)
    {
        conversion_ratio = 4.837f - 0.001f * power;
    }
    else
    {
        conversion_ratio = 4.5468f - 0.0003f * power;
    }

    //做功乘以转换比得到热量消耗（J），除以4.186得到卡路里（Cal）
    const float calorie = work * conversion_ratio / 4.186f;

    return calorie;
}

//重置卡路里功率计算方案
static void calorie_by_power_calculator_reset(CalorieByPowerCalculator *self)
{
    if (self != NULL)
    {
        self->cnt = 0;
        self->timestamp = 0;
    }
}

//卡路里心率计算方案初始化
static void calorie_by_hr_calculator_init(CalorieByHrCalculator *self, const CalorieByHrCalcInit *init)
{
    if (self != NULL && init != NULL)
    {
        self->weight = init->weight;
        self->gender = init->gender;
        self->age = init->age;

        //估算最大心率
        self->hr_max = 220.0f - init->age;
        //计算心率区间
        self->hr_zones[0] = self->hr_max * 0.5f;
        self->hr_zones[1] = self->hr_max * 0.6f;
        self->hr_zones[2] = self->hr_max * 0.7f;
        self->hr_zones[3] = self->hr_max * 0.8f;
        self->hr_zones[4] = self->hr_max * 0.9f;

        //指定缩放系数区间
        self->coeff_zones[0] = 0.9f;
        self->coeff_zones[1] = 0.5f;
        self->coeff_zones[2] = 0.6f;
        self->coeff_zones[3] = 0.8f;
        self->coeff_zones[4] = 1.0f;

        calorie_by_hr_calculator_reset(self);
    }
}

//卡路里心率方案计算
static float calorie_by_hr_calculator_exec(CalorieByHrCalculator *self, const CalorieByHrCalcInput *input)
{
    if (self == NULL || input == NULL)
    {
        return 0.0f;
    }

    //首次初始化
    if (self->cnt == 0)
    {
        self->timestamp = input->timestamp;
        self->cnt += 1;
        return 0.0f;
    }

    //时间戳检查
    if (input->timestamp < self->timestamp || input->timestamp - self->timestamp > 30)
    {
        calorie_by_hr_calculator_reset(self);
        return 0.0f;
    }

    //心率有效性检查
    if (input->hr == 0xFF || input->hr == 0)
    {
        calorie_by_hr_calculator_reset(self);
        return 0.0f;
    }

    //时间变化量
    const uint32_t dt = input->timestamp - self->timestamp;

    self->timestamp = input->timestamp;
    self->cnt += 1;

    //热量消耗（J）
    float H = 0.0f;

    if (self->gender == 0)
    {
        H = -340.0367f + 7.4534f * input->hr - 2.105f * self->weight + 1.234f * self->age;
    }
    else
    {
        H = -918.2817f + 10.515f * input->hr + 3.3134f * self->weight + 3.3617f * self->age;
    }

    //热量消耗系数，用于缩减计算出的热量消耗
    float coeff = 0.0f;

    if (input->hr <= self->hr_zones[0])
    {
        coeff = self->coeff_zones[0];
    }
    else if (input->hr >= self->hr_zones[4])
    {
        coeff = self->coeff_zones[4];
    }
    else
    {
        uint8_t idx = 0;
        for (uint8_t i = 0; i < 4; i++)
        {
            if (input->hr >= self->hr_zones[i] && input->hr <= self->hr_zones[i+1])
            {
                idx = i;
                break;
            }
        }

        //线性计算
        const float k = (self->coeff_zones[idx+1] - self->coeff_zones[idx]) / (self->hr_zones[idx+1] - self->hr_zones[idx]);
        coeff = self->coeff_zones[idx] + k * (input->hr - self->hr_zones[idx]);
    }

    H *= coeff;

    if (H < 0.001f)
    {
        H = 0.0f;
    }

    H *= dt;

    //将热量消耗转换为卡路里（Cal）
    const float calorie = H / 4.186f;

    return calorie;
}

//重置卡路里心率计算方案
static void calorie_by_hr_calculator_reset(CalorieByHrCalculator *self)
{
    if (self != NULL)
    {
        self->cnt = 0;
        self->timestamp = 0;
    }
}

//初始化卡路里估计功率方案
static void calorie_by_pwr_est_calculator_init(CalorieByPwrEstCalculator *self)
{
    calorie_by_pwr_est_calculator_reset(self);
}

//卡路里估计功率方案计算
static float calorie_by_pwr_est_calculator_exec(CalorieByPwrEstCalculator *self, const CalorieByPwrEstCalcInput *input)
{
    if (self == NULL || input == NULL)
    {
        return 0.0f;
    }

    //首次初始化
    if (self->cnt == 0)
    {
        self->timestamp = input->timestamp;
        self->cnt += 1;
        return 0.0f;
    }

    //时间戳检查
    if (input->timestamp < self->timestamp || input->timestamp - self->timestamp > 30)
    {
        calorie_by_pwr_est_calculator_reset(self);
        return 0.0f;
    }

    //时间变化量
    const uint32_t dt = input->timestamp - self->timestamp;

    self->timestamp = input->timestamp;
    self->cnt += 1;

    //有效做功约热量消耗的20%~25%（即需要乘以4~5），热量消耗（J）转换为卡路里（Cal）需要除以4.186，因此直接令做功等于卡路里
    const float calorie = input->pwr_est * dt;

    return calorie;
}

//重置卡路里估计功率计算方案
static void calorie_by_pwr_est_calculator_reset(CalorieByPwrEstCalculator *self)
{
    if (self != NULL)
    {
        self->cnt = 0;
        self->timestamp = 0;
    }
}

//初始化卡路里计算
void calorie_calculator_init(CalorieCalculator *self, const CalorieCalcInit *init)
{
    if (self != NULL && init != NULL)
    {
        const CalorieByHrCalcInit hr_init = {
            .weight = init->weight,
            .gender = init->gender,
            .age = init->age,
        };

        calorie_by_power_calculator_init(&self->cal_power_calculator);
        calorie_by_hr_calculator_init(&self->cal_hr_calculator, &hr_init);
        calorie_by_pwr_est_calculator_init(&self->cal_pwr_est_calculator);
    }
}

//卡路里计算
float calorie_calculator_exec(CalorieCalculator *self, const CalorieCalcInput *input)
{
    if (self == NULL || input == NULL)
    {
        return 0.0f;
    }

    float calorie = 0.0f;

    //准确程度：功率方案>心率方案>估计功率方案

    //功率有效，则使用功率方案计算出的卡路里
    if (input->power != 0xFFFF)
    {
        const CalorieByPowerCalcInput power_input = {
            .timestamp = input->timestamp,
            .power = input->power,
        };

        calorie = calorie_by_power_calculator_exec(&self->cal_power_calculator, &power_input);
        calorie_by_hr_calculator_reset(&self->cal_hr_calculator);
        calorie_by_pwr_est_calculator_reset(&self->cal_pwr_est_calculator);
    }
    else if (input->hr != 0xFF && input->hr != 0)
    {
        //心率有效，使用心率方案计算出的卡路里

        const CalorieByHrCalcInput hr_input = {
            .timestamp = input->timestamp,
            .hr = input->hr,
        };

        calorie = calorie_by_hr_calculator_exec(&self->cal_hr_calculator, &hr_input);
        calorie_by_power_calculator_reset(&self->cal_power_calculator);
        calorie_by_pwr_est_calculator_reset(&self->cal_pwr_est_calculator);
    }
    else
    {
        //使用估计功率方案计算出的卡路里

        const CalorieByPwrEstCalcInput pwr_est_input = {
            .timestamp = input->timestamp,
            .pwr_est = input->pwr_est,
        };

        calorie = calorie_by_pwr_est_calculator_exec(&self->cal_pwr_est_calculator, &pwr_est_input);
        calorie_by_power_calculator_reset(&self->cal_power_calculator);
        calorie_by_hr_calculator_reset(&self->cal_hr_calculator);
    }

    return calorie;
}
