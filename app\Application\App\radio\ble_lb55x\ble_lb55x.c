/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ble_lb55x.c
@Time    :   2024/12/25 18:11:54
*
**************************************************************************/
#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include <string.h>
#include <stdlib.h>
#include <stdbool.h>

#include "ble_lb55x.h"
#include "bf0_ble_gap.h"
#include "bf0_sibles.h"
#include "bf0_sibles_internal.h"
#include "bf0_sibles_advertising.h"
#include "ble_connection_manager.h"

#include "drv_flash.h"
#include "app_task.h"
#include "ser_user_data_def.h"
#include "ble_data_inf.h"
#include "bf0_sibles_nvds.h"
#include "bf0_ble_ancs.h"

#include "ble_lb55x_api.h"
#include "ins_service.h"
#include "ppg_api.h"
#include "alipay_bind.h"
#include "ble_lb55x_macros.h"
#include "ble_test_cmd.h"
#include "ble_lb55x_hr_s.h"
#include "ant_ble_hr_send.h"
#include "ble_lb55x_generic_access_c.h"
#include "qw_log.h"                  //包含log定义头文件
#include "qw_system_params.h"
#include "qw_user_debug.h"
#include "ble_status_data_inf.h"
#include "algo_service_gps_data.h"
#include "gui_event_service.h"
#include "ble_status.h"
#include "ble_csi_layer.h"
#include "ble_media_srv.h"

#define BLE_APP_LOG_TAG "BLE_APP"

#define BLE_APP_LOG_LVL               LOG_LVL_DBG       //定义log等级

#if (BLE_APP_LOG_LVL >= LOG_LVL_DBG)
    #define BLE_APP_LOG_D(...)        QW_LOG_D(BLE_APP_LOG_TAG, __VA_ARGS__)   //定义debug等级的log输出
#else
    #define BLE_APP_LOG_D(...)
#endif

#if (BLE_APP_LOG_LVL >= LOG_LVL_INFO)
    #define BLE_APP_LOG_I(...)        QW_LOG_I(BLE_APP_LOG_TAG, __VA_ARGS__)   //定义info等级的log输出
#else
    #define BLE_APP_LOG_I(...)
#endif

#if (BLE_APP_LOG_LVL >= LOG_LVL_WARNING)
    #define BLE_APP_LOG_W(...)        QW_LOG_W(BLE_APP_LOG_TAG, __VA_ARGS__)  //定义warning等级的log输出
#else
    #define BLE_APP_LOG_W(...)
#endif

#if (BLE_APP_LOG_LVL >= LOG_LVL_ERROR)
    #define BLE_APP_LOG_E(...)        QW_LOG_E(BLE_APP_LOG_TAG, __VA_ARGS__)  //定义debug等级的log输出
#else
    #define BLE_APP_LOG_E(...)
#endif

#if (BLE_APP_LOG_LVL >= LOG_LVL_DBG)
    #define BLE_APP_LOG_HEX(TAG, width, buf, size)      QW_LOG_HEX(TAG, width, buf, size)
#else
    #define BLE_APP_LOG_HEX(TAG, width, buf, size)
#endif


#define ANCS_FILTER_CATEGORY_ID_OTHER                BLE_ANCS_CATEGORY_ID_OTHER
#define ANCS_FILTER_CATEGORY_ID_INCOMING_CALL        BLE_ANCS_CATEGORY_ID_INCOMING_CALL
#define ANCS_FILTER_CATEGORY_ID_MISSED_CALL          BLE_ANCS_CATEGORY_ID_MISSED_CALL
#define ANCS_FILTER_CATEGORY_ID_VOICE_MAIL           BLE_ANCS_CATEGORY_ID_VOICE_MAIL
#define ANCS_FILTER_CATEGORY_ID_SOCIAL               BLE_ANCS_CATEGORY_ID_SOCIAL
#define ANCS_FILTER_CATEGORY_ID_SCHEDULE             BLE_ANCS_CATEGORY_ID_SCHEDULE
#define ANCS_FILTER_CATEGORY_ID_EMAIL                BLE_ANCS_CATEGORY_ID_EMAIL
#define ANCS_FILTER_CATEGORY_ID_NEWS                 BLE_ANCS_CATEGORY_ID_NEWS
#define ANCS_FILTER_CATEGORY_ID_HEALTH_AND_FITNESS   BLE_ANCS_CATEGORY_ID_HEALTH_AND_FITNESS
#define ANCS_FILTER_CATEGORY_ID_BUSINESS_AND_FINANCE BLE_ANCS_CATEGORY_ID_BUSINESS_AND_FINANCE
#define ANCS_FILTER_CATEGORY_ID_LOCATION             BLE_ANCS_CATEGORY_ID_LOCATION
#define ANCS_FILTER_CATEGORY_ID_ENTERTAINMENT        BLE_ANCS_CATEGORY_ID_ENTERTAINMENT

#define ANCS_FILTER_CATEGORY_ID_NUM					 11  //暂不支持 BLE_ANCS_CATEGORY_ID_MISSED_CALL


#define DEFAULT_LOCAL_NAME "WR02"
#define CYCLING_COMPUTER_APPERANCE (1153)
#define SPORT_WATCH_APPERANCE (193)


enum ble_app_att_list
{
    BLE_APP_SVC,
    BLE_APP_CHAR_RX,
    BLE_APP_CHAR_RX_VALUE,
    BLE_APP_CHAR_TX,
    BLE_APP_CHAR_TX_VALUE,
    BLE_APP_CLIENT_CHAR_CONFIG_DESCRIPTOR,
    BLE_APP_ATT_NB
};

enum ble_app_alipay_att_list
{
    BLE_APP_ALIPAY_SVC,
    BLE_APP_ALIPAY_CHAR,
    BLE_APP_ALIPAY_CHAR_VALUE,
    BLE_APP_ALIPAY_CLIENT_CHAR_CONFIG_DESCRIPTOR,
    BLE_APP_ALIPAY_ATT_NB
};

#ifdef BLE_HID

enum ble_app_hid_att_list
{
    BLE_APP_HID_SVC,

    BLE_APP_HID_INFO_CHAR,
    BLE_APP_HID_INFO_VAL,

    BLE_APP_HID_REPORT_MAP,
    BLE_APP_HID_REPORT_MAP_VAL,

    BLE_APP_HID_REPORT,
    BLE_APP_HID_REPORT_VAL,
    BLE_APP_HID_REPORT_REF,
    BLE_APP_HID_REPORT_NTF_CFG,

    BLE_APP_HID_CTRL,
    BLE_APP_HID_CTRL_VAL,
    BLE_APP_HID_ATT_NB
};

#endif

typedef enum
{
    BLE_APP_UUID_TYPE_SERVICE,
    BLE_APP_UUID_TYPE_CHARATER,
    BLE_APP_UUID_TYPE_DESCRIPTOR,
    BLE_APP_UUID_TYPE_TOTAL
} ble_app_uuid_display_type_t;

// #define BLE_NUS_SERVICE_CHANNEL_NUM 7//6//5
typedef enum
{
    BLE_NUS_SERVICE_CHANNEL_0 = 0,
    BLE_NUS_SERVICE_CHANNEL_1,
    BLE_NUS_SERVICE_CHANNEL_2,
    BLE_NUS_SERVICE_CHANNEL_3,
    BLE_NUS_SERVICE_CHANNEL_GOODIX,
    BLE_NUS_SERVICE_CHANNEL_ALIPAY,
    BLE_NUS_SERVICE_CHANNEL_DEBUG,
    BLE_NUS_SERVICE_CHANNEL_HID,
    BLE_NUS_SERVICE_CHANNEL_SECURITY,

    BLE_NUS_SERVICE_CHANNEL_NUM
}BLE_NUS_SERVICE_CHANNEL_ENUM;

typedef enum
{
    BLE_USER_EVENT_BOND_CANCEL = BLE_USER_TYPE, //取消配对
    BLE_USER_EVENT_WAIT_ID_TIMEOUT,             //配对等待userid超时
    BLE_USER_EVENT_UNBOND,                      //解除配对
}BLE_USER_EVENT_ENUM;

#define BLE_UUID_NUS_TX_CHARACTERISTIC 0x03, 0x00//0x0003               /**< The UUID of the TX Characteristic. */
#define BLE_UUID_NUS_RX_CHARACTERISTIC 0x02, 0x00//0x0002               /**< The UUID of the RX Characteristic. */
#define BLE_SERVICE_GENERIC_UUID 0xca, 0xdc, 0x24, 0x0e, 0xe5, 0xa9, 0xe0, 0x93, 0xf3, 0xa3, 0xb5

#define BLE_SERVICE_STANDARD_UUID 0xfb, 0x34, 0x9b, 0x5f, 0x80, 0x00, 0x00, 0x80, 0x00, 0x10, 0x00, 0x00
#define BLE_UUID_NUS_TX_GOODIX_CHARACTERISTIC 0x03, 0x00
#define BLE_UUID_NUS_RX_GOODIX_CHARACTERISTIC 0x04, 0x00

#define BLE_UUID_NUS_ALIPAY_SERVICE_UUID 0x02, 0x38
#define BLE_UUID_NUS_ALIPAY_CHARACTERISTIC 0x02, 0x4a

#define app_svc_uuid_ch0 { \
    BLE_NUS_UUID_CH0,           \
    BLE_SERVICE_GENERIC_UUID,   \
    0x01, 0x00, 0x40, 0x6e,     \
};

#define app_chara_uuid_ch0_rx { \
    BLE_NUS_UUID_CH0,           \
    BLE_SERVICE_GENERIC_UUID,   \
    BLE_UUID_NUS_RX_CHARACTERISTIC, \
    0x40, 0x6e, \
}

#define app_chara_uuid_ch0_tx { \
    BLE_NUS_UUID_CH0,           \
    BLE_SERVICE_GENERIC_UUID,   \
    BLE_UUID_NUS_TX_CHARACTERISTIC, \
    0x40, 0x6e, \
}

#define app_svc_uuid_ch1 { \
    BLE_NUS_UUID_CH1,           \
    BLE_SERVICE_GENERIC_UUID,   \
    0x01, 0x00, 0x40, 0x6e,     \
};

#define app_chara_uuid_ch1_rx { \
    BLE_NUS_UUID_CH1,           \
    BLE_SERVICE_GENERIC_UUID,   \
    BLE_UUID_NUS_RX_CHARACTERISTIC, \
    0x40, 0x6e, \
}

#define app_chara_uuid_ch1_tx { \
    BLE_NUS_UUID_CH1,           \
    BLE_SERVICE_GENERIC_UUID,   \
    BLE_UUID_NUS_TX_CHARACTERISTIC, \
    0x40, 0x6e, \
}

#define app_svc_uuid_ch2 { \
    BLE_NUS_UUID_CH2,           \
    BLE_SERVICE_GENERIC_UUID,   \
    0x01, 0x00, 0x40, 0x6e,     \
};

#define app_chara_uuid_ch2_rx { \
    BLE_NUS_UUID_CH2,           \
    BLE_SERVICE_GENERIC_UUID,   \
    BLE_UUID_NUS_RX_CHARACTERISTIC, \
    0x40, 0x6e, \
}

#define app_chara_uuid_ch2_tx { \
    BLE_NUS_UUID_CH2,           \
    BLE_SERVICE_GENERIC_UUID,   \
    BLE_UUID_NUS_TX_CHARACTERISTIC, \
    0x40, 0x6e, \
}

#define app_svc_uuid_ch3 { \
    BLE_NUS_UUID_CH3,           \
    BLE_SERVICE_GENERIC_UUID,   \
    0x01, 0x00, 0x40, 0x6e,     \
};

#define app_chara_uuid_ch3_rx { \
    BLE_NUS_UUID_CH3,           \
    BLE_SERVICE_GENERIC_UUID,   \
    BLE_UUID_NUS_RX_CHARACTERISTIC, \
    0x40, 0x6e, \
}

#define app_chara_uuid_ch3_tx { \
    BLE_NUS_UUID_CH3,           \
    BLE_SERVICE_GENERIC_UUID,   \
    BLE_UUID_NUS_TX_CHARACTERISTIC, \
    0x40, 0x6e, \
}

#define app_svc_uuid_security { \
    BLE_NUS_UUID_SECURITY,           \
    BLE_SERVICE_GENERIC_UUID,        \
    0x01, 0x00, 0x40, 0x6e,          \
}

#define app_chara_uuid_security_rx { \
    BLE_NUS_UUID_SECURITY,           \
    BLE_SERVICE_GENERIC_UUID,        \
    BLE_UUID_NUS_RX_CHARACTERISTIC,  \
    0x40, 0x6e, \
}

#define app_chara_uuid_security_tx { \
    BLE_NUS_UUID_SECURITY,           \
    BLE_SERVICE_GENERIC_UUID,        \
    BLE_UUID_NUS_TX_CHARACTERISTIC,  \
    0x40, 0x6e, \
}

#define app_chara_uuid_goodix { \
    BLE_SERVICE_STANDARD_UUID,    \
    0x0e, 0x19, 0x00, 0x00,     \
};

#define app_chara_uuid_goodix_rx { \
    BLE_SERVICE_STANDARD_UUID,    \
    BLE_UUID_NUS_RX_GOODIX_CHARACTERISTIC, \
    0x00, 0x00, \
}

#define app_chara_uuid_goodix_tx { \
    BLE_SERVICE_STANDARD_UUID,    \
    BLE_UUID_NUS_TX_GOODIX_CHARACTERISTIC, \
    0x00, 0x00, \
}

#define app_chara_uuid_alipay { \
    BLE_SERVICE_STANDARD_UUID,    \
    BLE_UUID_NUS_ALIPAY_SERVICE_UUID, \
    0x00, 0x00, \
}

#define app_chara_uuid_alipay_chara { \
    BLE_SERVICE_STANDARD_UUID,    \
    BLE_UUID_NUS_ALIPAY_CHARACTERISTIC, \
    0x00, 0x00, \
}

#define app_chara_uuid_debugch { \
    BLE_NUS_UUID_DEBUG,       \
    BLE_SERVICE_GENERIC_UUID, \
    0x01, 0x00, 0x40, 0x6e,   \
}

#define app_chara_uuid_debugch_rx { \
    BLE_NUS_UUID_DEBUG,       \
    BLE_SERVICE_GENERIC_UUID, \
    BLE_UUID_NUS_RX_CHARACTERISTIC, \
    0x40, 0x6e, \
}

#define app_chara_uuid_debugch_tx { \
    BLE_NUS_UUID_DEBUG,       \
    BLE_SERVICE_GENERIC_UUID, \
    BLE_UUID_NUS_TX_CHARACTERISTIC, \
    0x40, 0x6e, \
}

#define SERIAL_UUID_16(x) {((uint8_t)(x&0xff)),((uint8_t)(x>>8))}
/* 24 * 1.25 = 30ms */
// #define BLE_APP_HIGH_PERFORMANCE_INTERVAL (24)
#define BLE_APP_BOND_STATUS_DELAY (1000)
#define BLE_APP_USER_ID_WAIT_TIME (30000)
#define BLE_APP_MAX_ADV_COUNT 20
#define BLE_APP_MAX_CONN_COUNT 4
#define APP_MAX_DESC 2
#define BLE_APP_BIT_CONVERT_DIGIT_INC(n, m) (((n & (1 << m)) != 0) * (m+1))
typedef struct
{
    uint8_t is_power_on;
    uint8_t conn_idx[BLE_APP_CONN_NUM_MAX];
    uint8_t is_bg_adv_on;
    uint8_t adv_count;
    uint8_t conn_count;
    ble_gap_addr_t adv_addr[BLE_APP_MAX_ADV_COUNT];
    uint8_t adv_info[BLE_APP_MAX_ADV_COUNT];
    int8_t scan_rssi;
    ble_gap_connection_create_param_t last_init_conn;
    struct
    {
        bd_addr_t peer_addr;
        uint16_t mtu;
        uint16_t conn_interval;
        uint8_t peer_addr_type;
    } conn_para[BLE_APP_CONN_NUM_MAX];
    struct
    {
        sibles_hdl srv_handle;
        uint32_t data;
        uint8_t is_config_on;
    } data[BLE_NUS_SERVICE_CHANNEL_NUM];
    struct
    {
        uint8_t conn_idx;
        uint8_t role;
        uint8_t peer_addr_type;
        uint16_t mtu;
        uint16_t conn_interval;
        bd_addr_t peer_addr;
        struct
        {
            uint32_t data;
            uint8_t is_config_on;
        } data;
        sibles_svc_remote_svc_t svc;
        uint16_t rmt_svc_hdl;
    } conn[BLE_APP_MAX_CONN_COUNT];
    rt_timer_t time_handle;
    rt_timer_t write_time_handle;
    rt_mailbox_t mb_handle;
} app_env_t;

uint64_t device_info_num = 0;
uint32_t device_adv_info = 0;
static sibles_advertising_para_t adv_para = {0};
static bool is_hrm_tx = false;          //心率推送广播标志
static bool is_bond_allow = false;      //是否允许配对广播标志
static bool adv_change_delay = false;   //广播内容延迟更新标志（连接期间无法更新广播内容）
static bool is_pairing_complete = false; //配对完成标志
static app_env_t g_app_env = {
    .conn_idx[0] = BLE_APP_CONN_IDX_INVALID,
    .conn_idx[1] = BLE_APP_CONN_IDX_INVALID,
};
static ble_app_user_id_set_callback_t g_ble_app_user_id_set_callback = NULL;
static ble_app_peer_data_callback_t g_ble_app_peer_data_callback = NULL;
static uint8_t ble_app_peer_device_name[BLE_DEVICE_NAME_MAX_LEN] = {0};
static uint8_t ble_app_peer_conn_idx = 0xff;
static char local_name[31] = {0};
static ble_app_peer_state_callback_t g_ble_app_peer_state_callback = NULL;
static gui_evt_service_page_command_t request_pairing_page_command;
static char* p_push_to_page = NULL;
static uint8_t g_ble_app_bonded_addr[BD_ADDR_LEN] = {0};
static uint16_t g_ble_app_mb_op_type = 0;
// static rt_mailbox_t g_app_mb;

static uint8_t g_app_svc_ch0[ATT_UUID_128_LEN] = app_svc_uuid_ch0;
static uint8_t g_app_svc_ch1[ATT_UUID_128_LEN] = app_svc_uuid_ch1;
static uint8_t g_app_svc_ch2[ATT_UUID_128_LEN] = app_svc_uuid_ch2;
static uint8_t g_app_svc_ch3[ATT_UUID_128_LEN] = app_svc_uuid_ch3;
static uint8_t g_app_svc_sec[ATT_UUID_128_LEN] = app_svc_uuid_security;
static uint8_t g_app_svc_goodix[ATT_UUID_128_LEN] = app_chara_uuid_goodix;
static uint8_t g_app_svc_alipay[ATT_UUID_128_LEN] = app_chara_uuid_alipay;
static uint8_t g_app_svc_debug[ATT_UUID_128_LEN] = app_chara_uuid_debugch;

static uint8_t g_attr_disp_name[BLE_ANCS_ATTR_DATA_MAX];                                    /**< Buffer to store attribute data. */
static ble_ancs_attr_value_t g_notif_attr_app_id_latest;                     /**< Local copy of the newest app attribute. */
static uint8_t  g_ancs_filter_category_id[ANCS_FILTER_CATEGORY_ID_NUM] =
	{ANCS_FILTER_CATEGORY_ID_OTHER,
    ANCS_FILTER_CATEGORY_ID_INCOMING_CALL,
	// ANCS_FILTER_CATEGORY_ID_MISSED_CALL,
	ANCS_FILTER_CATEGORY_ID_VOICE_MAIL,
	ANCS_FILTER_CATEGORY_ID_SOCIAL,
	ANCS_FILTER_CATEGORY_ID_SCHEDULE,
	ANCS_FILTER_CATEGORY_ID_EMAIL,
	ANCS_FILTER_CATEGORY_ID_NEWS,
	ANCS_FILTER_CATEGORY_ID_HEALTH_AND_FITNESS,
	ANCS_FILTER_CATEGORY_ID_BUSINESS_AND_FINANCE,
	ANCS_FILTER_CATEGORY_ID_LOCATION,
	ANCS_FILTER_CATEGORY_ID_ENTERTAINMENT};


struct attm_desc_128 app_att_db_ch0[] =
{
    // Service declaration
    [BLE_APP_SVC] = {SERIAL_UUID_16(ATT_DECL_PRIMARY_SERVICE), PERM(RD, ENABLE), 0, 0},
    // Characteristic  declaration
    [BLE_APP_CHAR_RX] = {SERIAL_UUID_16(ATT_DECL_CHARACTERISTIC), PERM(RD, ENABLE), 0, 0},
    // Characteristic value config
    [BLE_APP_CHAR_RX_VALUE] = {
        /* The permissions are for: 1.Allowed read, write req, write command and notification.
                                    2.Write requires Unauthenticated link
           The ext_perm are for: 1. Support 128bit UUID. 2. Read will involve callback. */
        app_chara_uuid_ch0_rx,/* PERM(RD, ENABLE) |*/ PERM(WRITE_REQ, ENABLE) | PERM(WRITE_COMMAND, ENABLE) |/* PERM(NTF, ENABLE) |*/
        PERM(WP, NO_AUTH/*UNAUTH*/), PERM(UUID_LEN, UUID_128) | PERM(RI, ENABLE), 1024
    },
    // Characteristic  declaration
    [BLE_APP_CHAR_TX] = {SERIAL_UUID_16(ATT_DECL_CHARACTERISTIC), PERM(RD, ENABLE), 0, 0},
    // Characteristic value config
    [BLE_APP_CHAR_TX_VALUE] = {
        /* The permissions are for: 1.Allowed read, write req, write command and notification.
                                    2.Write requires Unauthenticated link
           The ext_perm are for: 1. Support 128bit UUID. 2. Read will involve callback. */
        app_chara_uuid_ch0_tx,/* PERM(RD, ENABLE) | PERM(WRITE_REQ, ENABLE) | PERM(WRITE_COMMAND, ENABLE) |*/ PERM(NTF, ENABLE) |
        PERM(WP, NO_AUTH/*UNAUTH*/), PERM(UUID_LEN, UUID_128) | PERM(RI, ENABLE), 1024
    },
    // Descriptor config
    [BLE_APP_CLIENT_CHAR_CONFIG_DESCRIPTOR] = {
        SERIAL_UUID_16(ATT_DESC_CLIENT_CHAR_CFG), PERM(RD, ENABLE) | PERM(WRITE_REQ,
                ENABLE) | PERM(WP, NO_AUTH/*UNAUTH*/), PERM(RI, ENABLE), 2
    },
};

struct attm_desc_128 app_att_db_ch1[] =
{
    // Service declaration
    [BLE_APP_SVC] = {SERIAL_UUID_16(ATT_DECL_PRIMARY_SERVICE), PERM(RD, ENABLE), 0, 0},
    // Characteristic  declaration
    [BLE_APP_CHAR_RX] = {SERIAL_UUID_16(ATT_DECL_CHARACTERISTIC), PERM(RD, ENABLE), 0, 0},
    // Characteristic value config
    [BLE_APP_CHAR_RX_VALUE] = {
        /* The permissions are for: 1.Allowed read, write req, write command and notification.
                                    2.Write requires Unauthenticated link
           The ext_perm are for: 1. Support 128bit UUID. 2. Read will involve callback. */
        app_chara_uuid_ch1_rx,/* PERM(RD, ENABLE) |*/ PERM(WRITE_REQ, ENABLE) | PERM(WRITE_COMMAND, ENABLE) |/* PERM(NTF, ENABLE) |*/
        PERM(WP, NO_AUTH/*UNAUTH*/), PERM(UUID_LEN, UUID_128) | PERM(RI, ENABLE), 1024
    },
    // Characteristic  declaration
    [BLE_APP_CHAR_TX] = {SERIAL_UUID_16(ATT_DECL_CHARACTERISTIC), PERM(RD, ENABLE), 0, 0},
    // Characteristic value config
    [BLE_APP_CHAR_TX_VALUE] = {
        /* The permissions are for: 1.Allowed read, write req, write command and notification.
                                    2.Write requires Unauthenticated link
           The ext_perm are for: 1. Support 128bit UUID. 2. Read will involve callback. */
        app_chara_uuid_ch1_tx,/* PERM(RD, ENABLE) | PERM(WRITE_REQ, ENABLE) | PERM(WRITE_COMMAND, ENABLE) |*/ PERM(NTF, ENABLE) |
        PERM(WP, NO_AUTH/*UNAUTH*/), PERM(UUID_LEN, UUID_128) | PERM(RI, ENABLE), 1024
    },
    // Descriptor config
    [BLE_APP_CLIENT_CHAR_CONFIG_DESCRIPTOR] = {
        SERIAL_UUID_16(ATT_DESC_CLIENT_CHAR_CFG), PERM(RD, ENABLE) | PERM(WRITE_REQ,
                ENABLE) | PERM(WP, NO_AUTH/*UNAUTH*/), PERM(RI, ENABLE), 2
    },
};

struct attm_desc_128 app_att_db_ch2[] =
{
    // Service declaration
    [BLE_APP_SVC] = {SERIAL_UUID_16(ATT_DECL_PRIMARY_SERVICE), PERM(RD, ENABLE), 0, 0},
    // Characteristic  declaration
    [BLE_APP_CHAR_RX] = {SERIAL_UUID_16(ATT_DECL_CHARACTERISTIC), PERM(RD, ENABLE), 0, 0},
    // Characteristic value config
    [BLE_APP_CHAR_RX_VALUE] = {
        /* The permissions are for: 1.Allowed read, write req, write command and notification.
                                    2.Write requires Unauthenticated link
           The ext_perm are for: 1. Support 128bit UUID. 2. Read will involve callback. */
        app_chara_uuid_ch2_rx,/* PERM(RD, ENABLE) |*/ PERM(WRITE_REQ, ENABLE) | PERM(WRITE_COMMAND, ENABLE) |/* PERM(NTF, ENABLE) |*/
        PERM(WP, NO_AUTH/*UNAUTH*/), PERM(UUID_LEN, UUID_128) | PERM(RI, ENABLE), 1024
    },
    // Characteristic  declaration
    [BLE_APP_CHAR_TX] = {SERIAL_UUID_16(ATT_DECL_CHARACTERISTIC), PERM(RD, ENABLE), 0, 0},
    // Characteristic value config
    [BLE_APP_CHAR_TX_VALUE] = {
        /* The permissions are for: 1.Allowed read, write req, write command and notification.
                                    2.Write requires Unauthenticated link
           The ext_perm are for: 1. Support 128bit UUID. 2. Read will involve callback. */
        app_chara_uuid_ch2_tx, /*PERM(RD, ENABLE) | PERM(WRITE_REQ, ENABLE) | PERM(WRITE_COMMAND, ENABLE) |*/ PERM(NTF, ENABLE) |
        PERM(WP, NO_AUTH/*UNAUTH*/), PERM(UUID_LEN, UUID_128) | PERM(RI, ENABLE), 1024
    },
    // Descriptor config
    [BLE_APP_CLIENT_CHAR_CONFIG_DESCRIPTOR] = {
        SERIAL_UUID_16(ATT_DESC_CLIENT_CHAR_CFG), PERM(RD, ENABLE) | PERM(WRITE_REQ,
                ENABLE) | PERM(WP, NO_AUTH/*UNAUTH*/), PERM(RI, ENABLE), 2
    },


};

struct attm_desc_128 app_att_db_ch3[] =
{
    // Service declaration
    [BLE_APP_SVC] = {SERIAL_UUID_16(ATT_DECL_PRIMARY_SERVICE), PERM(RD, ENABLE), 0, 0},
    // Characteristic  declaration
    [BLE_APP_CHAR_RX] = {SERIAL_UUID_16(ATT_DECL_CHARACTERISTIC), PERM(RD, ENABLE), 0, 0},
    // Characteristic value config
    [BLE_APP_CHAR_RX_VALUE] = {
        /* The permissions are for: 1.Allowed read, write req, write command and notification.
                                    2.Write requires Unauthenticated link
           The ext_perm are for: 1. Support 128bit UUID. 2. Read will involve callback. */
        app_chara_uuid_ch3_rx,/* PERM(RD, ENABLE) |*/ PERM(WRITE_REQ, ENABLE) | PERM(WRITE_COMMAND, ENABLE) |/* PERM(NTF, ENABLE) |*/
        PERM(WP, NO_AUTH/*UNAUTH*/), PERM(UUID_LEN, UUID_128) | PERM(RI, ENABLE), 1024
    },
    // Characteristic  declaration
    [BLE_APP_CHAR_TX] = {SERIAL_UUID_16(ATT_DECL_CHARACTERISTIC), PERM(RD, ENABLE), 0, 0},
    // Characteristic value config
    [BLE_APP_CHAR_TX_VALUE] = {
        /* The permissions are for: 1.Allowed read, write req, write command and notification.
                                    2.Write requires Unauthenticated link
           The ext_perm are for: 1. Support 128bit UUID. 2. Read will involve callback. */
        app_chara_uuid_ch3_tx,/* PERM(RD, ENABLE) | PERM(WRITE_REQ, ENABLE) | PERM(WRITE_COMMAND, ENABLE) |*/ PERM(NTF, ENABLE) |
        PERM(WP, NO_AUTH/*UNAUTH*/), PERM(UUID_LEN, UUID_128) | PERM(RI, ENABLE), 1024
    },
    // Descriptor config
    [BLE_APP_CLIENT_CHAR_CONFIG_DESCRIPTOR] = {
        SERIAL_UUID_16(ATT_DESC_CLIENT_CHAR_CFG), PERM(RD, ENABLE) | PERM(WRITE_REQ,
                ENABLE) | PERM(WP, NO_AUTH/*UNAUTH*/), PERM(RI, ENABLE), 2
    },
};

struct attm_desc_128 app_att_db_security[] =
{
    // Service declaration
    [BLE_APP_SVC] = {SERIAL_UUID_16(ATT_DECL_PRIMARY_SERVICE), PERM(RD, ENABLE), 0, 0},
    // Characteristic  declaration
    [BLE_APP_CHAR_RX] = {SERIAL_UUID_16(ATT_DECL_CHARACTERISTIC), PERM(RD, ENABLE), 0, 0},
    // Characteristic value config
    [BLE_APP_CHAR_RX_VALUE] = {
        /* The permissions are for: 1.Allowed read, write req, write command and notification.
                                    2.Write requires Unauthenticated link
           The ext_perm are for: 1. Support 128bit UUID. 2. Read will involve callback. */
        app_chara_uuid_security_rx,/* PERM(RD, ENABLE) |*/ PERM(WRITE_REQ, ENABLE) | PERM(WRITE_COMMAND, ENABLE) |/* PERM(NTF, ENABLE) |*/
        PERM(WP, SEC_CON/*NO_AUTH*//*UNAUTH*/), PERM(UUID_LEN, UUID_128) | PERM(RI, ENABLE), 1024
    },
    // Characteristic  declaration
    [BLE_APP_CHAR_TX] = {SERIAL_UUID_16(ATT_DECL_CHARACTERISTIC), PERM(RD, ENABLE), 0, 0},
    // Characteristic value config
    [BLE_APP_CHAR_TX_VALUE] = {
        /* The permissions are for: 1.Allowed read, write req, write command and notification.
                                    2.Write requires Unauthenticated link
           The ext_perm are for: 1. Support 128bit UUID. 2. Read will involve callback. */
        app_chara_uuid_security_tx,/* PERM(RD, ENABLE) | PERM(WRITE_REQ, ENABLE) | PERM(WRITE_COMMAND, ENABLE) |*/ PERM(NTF, ENABLE) |
        PERM(WP, SEC_CON/*NO_AUTH*//*UNAUTH*/), PERM(UUID_LEN, UUID_128) | PERM(RI, ENABLE), 1024
    },
    // Descriptor config
    [BLE_APP_CLIENT_CHAR_CONFIG_DESCRIPTOR] = {
        SERIAL_UUID_16(ATT_DESC_CLIENT_CHAR_CFG), PERM(RD, ENABLE) | PERM(WRITE_REQ,
                ENABLE) | PERM(WP, SEC_CON/*NO_AUTH*//*UNAUTH*/), PERM(RI, ENABLE), 2
    },
};

struct attm_desc_128 app_att_db_goodix[] =
{
    // Service declaration
    [BLE_APP_SVC] = {SERIAL_UUID_16(ATT_DECL_PRIMARY_SERVICE), PERM(RD, ENABLE), 0, 0},
    // Characteristic  declaration
    [BLE_APP_CHAR_RX] = {SERIAL_UUID_16(ATT_DECL_CHARACTERISTIC), PERM(RD, ENABLE), 0, 0},
    // Characteristic value config
    [BLE_APP_CHAR_RX_VALUE] = {
        /* The permissions are for: 1.Allowed read, write req, write command and notification.
                                    2.Write requires Unauthenticated link
           The ext_perm are for: 1. Support 128bit UUID. 2. Read will involve callback. */
        app_chara_uuid_goodix_rx,/* PERM(RD, ENABLE) |*/ PERM(WRITE_REQ, ENABLE) | PERM(WRITE_COMMAND, ENABLE) |/* PERM(NTF, ENABLE) |*/
        PERM(WP, NO_AUTH/*UNAUTH*/), PERM(UUID_LEN, UUID_128) | PERM(RI, ENABLE), 1024
    },
    // Characteristic  declaration
    [BLE_APP_CHAR_TX] = {SERIAL_UUID_16(ATT_DECL_CHARACTERISTIC), PERM(RD, ENABLE), 0, 0},
    // Characteristic value config
    [BLE_APP_CHAR_TX_VALUE] = {
        /* The permissions are for: 1.Allowed read, write req, write command and notification.
                                    2.Write requires Unauthenticated link
           The ext_perm are for: 1. Support 128bit UUID. 2. Read will involve callback. */
        app_chara_uuid_goodix_tx,/* PERM(RD, ENABLE) | PERM(WRITE_REQ, ENABLE) | PERM(WRITE_COMMAND, ENABLE) |*/ PERM(NTF, ENABLE) |
        PERM(WP, NO_AUTH/*UNAUTH*/), PERM(UUID_LEN, UUID_128) | PERM(RI, ENABLE), 1024
    },
    // Descriptor config
    [BLE_APP_CLIENT_CHAR_CONFIG_DESCRIPTOR] = {
        SERIAL_UUID_16(ATT_DESC_CLIENT_CHAR_CFG), PERM(RD, ENABLE) | PERM(WRITE_REQ,
                ENABLE) | PERM(WP, NO_AUTH/*UNAUTH*/), PERM(RI, ENABLE), 2
    },
};

struct attm_desc_128 app_att_db_alipay[] =
{
    // Service declaration
    [BLE_APP_ALIPAY_SVC] = {SERIAL_UUID_16(ATT_DECL_PRIMARY_SERVICE), PERM(RD, ENABLE), 0, 0},
    // Characteristic  declaration
    [BLE_APP_ALIPAY_CHAR] = {SERIAL_UUID_16(ATT_DECL_CHARACTERISTIC), PERM(RD, ENABLE), 0, 0},
    // Characteristic value config
    [BLE_APP_ALIPAY_CHAR_VALUE] = {
        /* The permissions are for: 1.Allowed read, write req, write command and notification.
                                    2.Write requires Unauthenticated link
           The ext_perm are for: 1. Support 128bit UUID. 2. Read will involve callback. */
        app_chara_uuid_alipay_chara, PERM(RD, ENABLE) | PERM(WRITE_REQ, ENABLE) /*| PERM(WRITE_COMMAND, ENABLE) */| PERM(NTF, ENABLE) |
        PERM(WP, NO_AUTH/*UNAUTH*/), PERM(UUID_LEN, UUID_128) | PERM(RI, ENABLE), 1024
    },
    // Descriptor config
    [BLE_APP_ALIPAY_CLIENT_CHAR_CONFIG_DESCRIPTOR] = {
        SERIAL_UUID_16(ATT_DESC_CLIENT_CHAR_CFG), PERM(RD, ENABLE) | PERM(WRITE_REQ,
                ENABLE) | PERM(WP, NO_AUTH/*UNAUTH*/), /*PERM(UUID_LEN, UUID_128) |*/ PERM(RI, ENABLE), 2
    },
};

struct attm_desc_128 app_att_db_debug[] =
{
    // Service declaration
    [BLE_APP_SVC] = {SERIAL_UUID_16(ATT_DECL_PRIMARY_SERVICE), PERM(RD, ENABLE), 0, 0},
    // Characteristic  declaration
    [BLE_APP_CHAR_RX] = {SERIAL_UUID_16(ATT_DECL_CHARACTERISTIC), PERM(RD, ENABLE), 0, 0},
    // Characteristic value config
    [BLE_APP_CHAR_RX_VALUE] = {
        /* The permissions are for: 1.Allowed read, write req, write command and notification.
                                    2.Write requires Unauthenticated link
           The ext_perm are for: 1. Support 128bit UUID. 2. Read will involve callback. */
        app_chara_uuid_debugch_rx,/* PERM(RD, ENABLE) |*/ PERM(WRITE_REQ, ENABLE) | PERM(WRITE_COMMAND, ENABLE) |/* PERM(NTF, ENABLE) |*/
        PERM(WP, NO_AUTH/*UNAUTH*/), PERM(UUID_LEN, UUID_128) | PERM(RI, ENABLE), 1024
    },
    // Characteristic  declaration
    [BLE_APP_CHAR_TX] = {SERIAL_UUID_16(ATT_DECL_CHARACTERISTIC), PERM(RD, ENABLE), 0, 0},
    // Characteristic value config
    [BLE_APP_CHAR_TX_VALUE] = {
        /* The permissions are for: 1.Allowed read, write req, write command and notification.
                                    2.Write requires Unauthenticated link
           The ext_perm are for: 1. Support 128bit UUID. 2. Read will involve callback. */
        app_chara_uuid_debugch_tx,/* PERM(RD, ENABLE) | PERM(WRITE_REQ, ENABLE) | PERM(WRITE_COMMAND, ENABLE) |*/ PERM(NTF, ENABLE) |
        PERM(WP, NO_AUTH/*UNAUTH*/), PERM(UUID_LEN, UUID_128) | PERM(RI, ENABLE), 1024
    },
    // Descriptor config
    [BLE_APP_CLIENT_CHAR_CONFIG_DESCRIPTOR] = {
        SERIAL_UUID_16(ATT_DESC_CLIENT_CHAR_CFG), PERM(RD, ENABLE) | PERM(WRITE_REQ,
                ENABLE) | PERM(WP, NO_AUTH/*UNAUTH*/), PERM(RI, ENABLE), 2
    },
};

#ifdef BLE_HID

enum
{
    BLE_APP_HID_INPUT = 0x01,
    BLE_APP_HID_OUTPUT = 0x02,
    BLE_APP_HID_FEATURE = 0x03,
};

static ble_app_hid_info_t ble_app_hid_info =
{
    .version = BASE_USB_HID_SPEC_VERSION,
    .code = 0x00,
    .flags = HIDS_NORMALLY_CONNECTABLE,
};

static ble_app_hid_report_t ble_app_report =
{
    .id = 0x00,
    .type = BLE_APP_HID_INPUT,
};

static ble_app_hid_key_state_t ble_app_hid_consume_state =
{
    .key_state = 0,
};

static uint8_t ctrl_point = 0;

typedef enum
{
    HIDS_CTRL_PLAY,
    HIDS_CTRL_CONFG,
    HIDS_CTRL_SCAN_NEX,
    HIDS_CTRL_SCAN_PREV,
    HIDS_CTRL_VOL_DOWN,
    HIDS_CTRL_VOL_UP,
    HIDS_CTRL_FWD,
    HIDS_CTRL_BACK,
} HID_CONTROL_ENUM;

static const uint8_t ble_app_hid_report_map[] =
{
    0x05, 0x0C,       // Usage Page (Consumer)
    0x09, 0x01,       // Usage (Consumer Control)
    0xA1, 0x01,       // Collection (Application)
    0x15, 0x00,       // Logical minimum (0)
    0x25, 0x01,       // Logical maximum (1)
    0x75, 0x01,       // Report Size (1)
    0x95, 0x01,       // Report Count (1)

    0x09, 0xCD,       // Usage (Play/Pause)
    0x81, 0x06,       // Input (Data,Value,Relative,Bit Field)
    0x0A, 0x83, 0x01, // Usage (AL Consumer Control Configuration)
    0x81, 0x06,       // Input (Data,Value,Relative,Bit Field)
    0x09, 0xB5,       // Usage (Scan Next Track)
    0x81, 0x06,       // Input (Data,Value,Relative,Bit Field)
    0x09, 0xB6,       // Usage (Scan Previous Track)
    0x81, 0x06,       // Input (Data,Value,Relative,Bit Field)

    0x09, 0xEA,       // Usage (Volume Down)
    0x81, 0x06,       // Input (Data,Value,Relative,Bit Field)
    0x09, 0xE9,       // Usage (Volume Up)
    0x81, 0x06,       // Input (Data,Value,Relative,Bit Field)
    0x0A, 0x25, 0x02, // Usage (AC Forward)
    0x81, 0x06,       // Input (Data,Value,Relative,Bit Field)
    0x0A, 0x24, 0x02, // Usage (AC Back)
    0x81, 0x06,       // Input (Data,Value,Relative,Bit Field)
    0xC0              // End Collection

};

struct attm_desc app_att_db_hid[] =
{
    // HID service
    [BLE_APP_HID_SVC] = {ATT_DECL_PRIMARY_SERVICE,  PERM(RD, ENABLE), 0, 0},

    // HID Info
    [BLE_APP_HID_INFO_CHAR] = {ATT_DECL_CHARACTERISTIC,   PERM(RD, ENABLE), 0, 0},
    [BLE_APP_HID_INFO_VAL] = {ATT_CHAR_HID_INFO,         PERM(RD, ENABLE), PERM(RI, ENABLE), sizeof(ble_app_hid_info)},

    // HID Report map
    [BLE_APP_HID_REPORT_MAP] = {ATT_DECL_CHARACTERISTIC,   PERM(RD, ENABLE), 0, 0},
    [BLE_APP_HID_REPORT_MAP_VAL] = {ATT_CHAR_REPORT_MAP,       PERM(RD, ENABLE), PERM(RI, ENABLE), sizeof(ble_app_hid_report_map)},

    // HID Report
    [BLE_APP_HID_REPORT] = {ATT_DECL_CHARACTERISTIC,   PERM(RD, ENABLE), 0, 0},
    [BLE_APP_HID_REPORT_VAL] = {
        ATT_CHAR_REPORT,
        PERM(RD, ENABLE) | PERM(WRITE_REQ, ENABLE) | PERM(WRITE_COMMAND, ENABLE) | PERM(NTF, ENABLE) |
        PERM(WP, UNAUTH), PERM(UUID_LEN, UUID_16) | PERM(RI, ENABLE),
        8
    },
    [BLE_APP_HID_REPORT_REF] = {ATT_DESC_REPORT_REF,  PERM(RD, ENABLE), PERM(RI, ENABLE), 2},
    [BLE_APP_HID_REPORT_NTF_CFG] = {ATT_DESC_CLIENT_CHAR_CFG,  PERM(RD, ENABLE) | PERM(WRITE_REQ, ENABLE) | PERM(WP, UNAUTH), PERM(RI, ENABLE), 2},

    // HID Control
    [BLE_APP_HID_CTRL] = {ATT_DECL_CHARACTERISTIC,   PERM(RD, ENABLE), 0, 0},
    [BLE_APP_HID_CTRL_VAL] = {ATT_CHAR_HID_CTNL_PT,      PERM(RD, ENABLE) | PERM(WRITE_REQ, ENABLE), 0, 1},
};

#endif

static const char *const s_ble_app_addr_type[] =
{
    "public",
    "random",
    "public identity",
    "random identity",
    "not resovled",
    "anonymous"
};

static const char *const s_ble_app_report_type[] =
{
    "ext adv",
    "legacy adv",
    "ext scan response",
    "legacy scan response",
    "periodic adv",
};

static const char *const s_ble_app_info_type[] =
{
    "connectable",
    "scannable",
    "direct",
    "",
};


static const char *const s_ble_app_uuid_type[BLE_APP_UUID_TYPE_TOTAL] =
{
    "Service",
    "    Charateristic",
    "        Descriptor",
};

static const char *const s_ble_app_chara_prop[] =
{
    "",
    "Broadcast",
    "Read",
    "Write_wo_rsp",
    "Write",
    "Notify",
    "Indication",
    "Authticated_signed_writes",
    "Extended_properties",
};

#define WAIT_USER_ID_TIMER_NAME "WAIT_USER_ID_TIMER"
static rt_timer_t wait_user_id_timer = RT_NULL;

#define BOND_STATUS_UPDATA_DELAY_TIMER_NAME "BOND_STATUS_TIMER"
static rt_timer_t bond_status_updata_delay_timer = RT_NULL;

// 每次被绑定后，等待接收user id，若超时则视为绑定失败
static void ble_app_wait_user_id_timer_callback(void *p_context);
// 鉴权不一致，用户点击恢复出厂或取消后，先上报给手机状态，延迟进行恢复出厂或断连
static void ble_app_bond_status_updata_delay_timer_callback(void *p_context);
int32_t ble_connect_state_notify(uint8_t state);

static app_env_t *ble_app_get_env(void)
{
    return &g_app_env;
}

SIBLES_ADVERTISING_CONTEXT_DECLAR(g_app_advertising_context);

static void ble_app_advertising_init(void);
static void ble_app_advertising_update(void);

static uint8_t ble_app_advertising_event(uint8_t event, void *context, void *data)
{
    app_env_t *env = ble_app_get_env();

    switch (event)
    {
    case SIBLES_ADV_EVT_ADV_STARTED:
    {
        sibles_adv_evt_startted_t *evt = (sibles_adv_evt_startted_t *)data;
        if (!env->is_bg_adv_on)
        {
            env->is_bg_adv_on = 1;
            if (adv_change_delay)
            {
                ble_app_advertising_update();
                adv_change_delay = false;
            }
        }
        BLE_APP_LOG_I("ADV start result %d, mode %d\r\n", evt->status, evt->adv_mode);
        break;
    }
    case SIBLES_ADV_EVT_ADV_STOPPED:
    {
        sibles_adv_evt_stopped_t *evt = (sibles_adv_evt_stopped_t *)data;
        if (env->is_bg_adv_on)
        {
            env->is_bg_adv_on = 0;
        }
        BLE_APP_LOG_I("ADV stopped reason %d, mode %d\r\n", evt->reason, evt->adv_mode);
        break;
    }
    default:
        break;
    }
    return 0;
}

static void inttoa(uint64_t value, uint8_t *str, uint8_t length)
{
    uint8_t i = 0;
    uint64_t temp = value;

    for(i = 0; i< length; i++)
    {
        str[i] = temp % 10 + '0';
        temp /= 10;
    }
}

/************************************************************************
 *@function:void g_device_info_num_set(void);
 *@brief 从52832获取uuid作为设备信息，并缓存到全局变量device_info_num
 *@param: null
 *@return:null
*************************************************************************/
void g_device_info_num_set(void)
{
    device_info_num = ser_user_settings_read_uuid();
}

/************************************************************************
 *@function:uint64_t g_device_info_num_get(void);
 *@brief 从全局变量device_info_num获取设备信息uuid
 *@param: null
 *@return:uint64_t 设备信息
*************************************************************************/
uint64_t g_device_info_num_get(void)
{
    return device_info_num;
}

// 广播包中manufacturer_data字段使用格式
// |--------芯片UID--------| |reserved(BSC100用于区分国内与国际版本)| |产品标识符('W')| |--reserved--| | bit7 是否允许被配对 bit6-0 vesion MSB | |version LSB|
// |xx xx xx xx xx xx xx xx| |                 00                 | |     57       | |00 00 00 00|  |                00(10)               |  |    53    |
/* Enable advertise via advertising service. */
static void ble_app_advertising_init(void)
{

    uint8_t ret;

    uint8_t manu_additnal_data[] = {0x20, 0xC4, 0x00, 0x91};
    uint16_t manu_company_id = 0x73;
    uint16_t manu_appearance = SPORT_WATCH_APPERANCE;//1153;
    bd_addr_t addr;
    uint8_t ble_mac[BD_ADDR_LEN] = {0};
    uint8_t data[MANUF_SPECIFIC_DATA_SIZE] = {0};

    ret = ble_get_public_address(&addr);
    BLE_APP_LOG_I("lb55x ble mac:\r\n");
    for(uint8_t i = 0; i < BD_ADDR_LEN; i++)
    {
        BLE_APP_LOG_I("0x%02x ", addr.addr[i]);
    }
    BLE_APP_LOG_I("\r\n");

    rt_snprintf(local_name, 8, DEFAULT_LOCAL_NAME);

    g_device_get_ble_mac_addr(ble_mac, BD_ADDR_LEN);
    BLE_APP_LOG_I("device ble mac:\r\n");
    for(uint8_t i = 0; i < BD_ADDR_LEN; i++)
    {
        BLE_APP_LOG_I("0x%02x ", ble_mac[i]);
    }
    BLE_APP_LOG_I("\r\n");

    /* Add the last two byte of MAC address to local_name */
    char* p_local_name_add = local_name + strlen(DEFAULT_LOCAL_NAME);
    rt_snprintf(p_local_name_add, 8, "(%02x%02x)",ble_mac[1],ble_mac[0]);
    BLE_APP_LOG_I("local_name:%s\r\n",local_name);

    // ble_mac[5] -= 2;
    // if(memcmp(addr.addr, ble_mac, BD_ADDR_LEN))
    // {

    //     // memcpy(addr.addr, ble_mac, BD_ADDR_LEN);
    //     sifli_nvds_write_tag_t *update_tag = malloc(sizeof(sifli_nvds_write_tag_t) + BD_ADDR_LEN);

    //     memcpy(update_tag->value.value, ble_mac, BD_ADDR_LEN);
    //     update_tag->is_flush = 1;
    //     update_tag->type = BLE_UPDATE_ALWAYS;
    //     update_tag->value.tag = NVDS_STACK_TAG_BD_ADDRESS;
    //     update_tag->value.len = NVDS_STACK_LEN_BD_ADDRESS;

    //     sifli_nvds_write_tag_value(update_tag);
    //     free(update_tag);
    // }

    ble_gap_dev_name_t *dev_name = rt_malloc(sizeof(ble_gap_dev_name_t) + strlen(local_name));
    dev_name->len = strlen(local_name);
    memcpy(dev_name->name, local_name, dev_name->len);
    ble_gap_set_dev_name(dev_name);                     //设置广播名称
    rt_free(dev_name);

    adv_para.own_addr_type = GAPM_STATIC_ADDR;
    adv_para.config.adv_mode = SIBLES_ADV_CONNECT_MODE;
    /* Keep advertising till disable it or connected. */
    adv_para.config.mode_config.conn_config.duration = 0x0;
#ifdef CONFIG_PPG_BLE_FACTORY_TEST
    adv_para.config.mode_config.conn_config.interval = 0x30;  //adv interval 0x30 * 0.625ms = 30ms
    adv_para.config.max_tx_pwr = 4;
#else
    adv_para.config.mode_config.conn_config.interval = 0x320;//adv interval 800 * 0.625ms = 500ms
    adv_para.config.max_tx_pwr = 0x7F;
#endif
    /* Advertising will re-start after disconnected. */
    adv_para.config.is_auto_restart = 1;
    /* Scan rsp data is same as advertising data. */
    //adv_para.config.is_rsp_data_duplicate = 1;

    /* Prepare name filed. Due to name is too long to put adv data, put it to rsp data.*/
    adv_para.rsp_data.completed_name = rt_malloc(rt_strlen(local_name) + sizeof(sibles_adv_type_name_t));
    adv_para.rsp_data.completed_name->name_len = rt_strlen(local_name);
    rt_memcpy(adv_para.rsp_data.completed_name->name, local_name, adv_para.rsp_data.completed_name->name_len);

    adv_para.rsp_data.srv_data = rt_malloc(BD_ADDR_LEN + sizeof(sibles_adv_type_srv_data_t));
    adv_para.rsp_data.srv_data->uuid.uuid_len = 2;
    adv_para.rsp_data.srv_data->uuid.uuid.uuid_16[0] = 0x02;
    adv_para.rsp_data.srv_data->uuid.uuid.uuid_16[1] = 0x38;
    adv_para.rsp_data.srv_data->data_len = BD_ADDR_LEN;
    // memcpy(adv_para.rsp_data.srv_data->uuid.uuid.uuid_128, g_app_svc_alipay, adv_para.rsp_data.srv_data->uuid.uuid_len);
    for(uint8_t i = 0; i < BD_ADDR_LEN; i++)
    {
        adv_para.rsp_data.srv_data->additional_data[i] = addr.addr[BD_ADDR_LEN - i - 1];
    }
    /* Prepare manufacturer filed .*/

    // device_info_num = ser_user_settings_read_uuid();
    if (device_info_num == 0)
    {
        g_device_info_num_set();
    }

    device_adv_info = device_info_num % 100000000;
    // device_info_num %= 100000000;
    inttoa(device_adv_info, data, MANUF_SPECIFIC_DATA_SIZE);
    g_device_set_ble_manufacturer(data, MANUF_SPECIFIC_DATA_SIZE);
    adv_para.adv_data.manufacturer_data = rt_malloc(sizeof(sibles_adv_type_manufacturer_data_t) + sizeof(data) + /*BD_ADDR_LEN +*/
                                                BLE_ADV_REGION_CODE_LEN + BLE_ADV_DEVICE_TYPE_LEN + BLE_ADV_RESERVED_LEN +
                                                BLE_ADV_BOND_ALLOW_STATE_LEN + BLE_ADV_VERSION_LEN);
    adv_para.adv_data.manufacturer_data->company_id = manu_company_id;
    adv_para.adv_data.manufacturer_data->data_len = sizeof(data) + /*BD_ADDR_LEN +*/ 
                                                    BLE_ADV_REGION_CODE_LEN + BLE_ADV_DEVICE_TYPE_LEN + BLE_ADV_RESERVED_LEN +
                                                    BLE_ADV_BOND_ALLOW_STATE_LEN + BLE_ADV_VERSION_LEN;
    rt_memcpy(adv_para.adv_data.manufacturer_data->additional_data, data, sizeof(data));
    // rt_memcpy(&adv_para.adv_data.manufacturer_data->additional_data[sizeof(data)], addr.addr, BD_ADDR_LEN);
    // for(uint8_t i=0; i<BD_ADDR_LEN; i++)
    // {
    //     adv_para.adv_data.manufacturer_data->additional_data[sizeof(data) + i] = addr.addr[BD_ADDR_LEN - i - 1];
    // }
    adv_para.adv_data.manufacturer_data->additional_data[sizeof(data)] = 0x00;
    adv_para.adv_data.manufacturer_data->additional_data[sizeof(data) + BLE_ADV_REGION_CODE_LEN] = BLE_DEVICE_TYPE;
    for (uint8_t i = 0; i < BLE_ADV_RESERVED_LEN; i++)
    {
        adv_para.adv_data.manufacturer_data->additional_data[sizeof(data) + BLE_ADV_REGION_CODE_LEN + BLE_ADV_DEVICE_TYPE_LEN + i] = 0x00;
    }
    extern uint16_t get_major_app_version(void);
    uint16_t soft_version = get_major_app_version();
    adv_para.adv_data.manufacturer_data->additional_data[sizeof(data) + BLE_ADV_REGION_CODE_LEN + BLE_ADV_DEVICE_TYPE_LEN + BLE_ADV_RESERVED_LEN] = (is_bond_allow & 0x01) << 7;
    adv_para.adv_data.manufacturer_data->additional_data[sizeof(data) + BLE_ADV_REGION_CODE_LEN + BLE_ADV_DEVICE_TYPE_LEN + BLE_ADV_RESERVED_LEN] |= (soft_version >> 8) & 0x7f;
    adv_para.adv_data.manufacturer_data->additional_data[sizeof(data) + BLE_ADV_REGION_CODE_LEN + BLE_ADV_DEVICE_TYPE_LEN + BLE_ADV_RESERVED_LEN + 1] = soft_version & 0xff;
    // adv_para.adv_data.manufacturer_data->additional_data[sizeof(data) + BD_ADDR_LEN + 2] = is_bond_allow;
    // uint16_t uuid_arr[2] = {ATT_SVC_DEVICE_INFO, ATT_SVC_HEART_RATE};
#ifdef ADV_UUID
    uint16_t uuid_arr[1] = {ATT_SVC_HID};//{ATT_SVC_DEVICE_INFO};
    uint8_t uuid_num = sizeof(uuid_arr) / sizeof(uuid_arr[0]);
    adv_para.adv_data.completed_uuid = rt_malloc(sizeof(sibles_adv_type_srv_uuid_t) + (sizeof(sibles_adv_uuid_t) * uuid_num));
    adv_para.adv_data.completed_uuid->count = uuid_num;
    // adv_para.adv_data.completed_uuid->uuid_list[0].uuid_len = 2;
    for(uint8_t i = 0; i < uuid_num; i++)
    {
        adv_para.adv_data.completed_uuid->uuid_list[i].uuid_len = 2;
        adv_para.adv_data.completed_uuid->uuid_list[i].uuid.uuid_16[0] = uuid_arr[i] & 0xff;
        adv_para.adv_data.completed_uuid->uuid_list[i].uuid.uuid_16[1] = (uuid_arr[i] >> 8) & 0xff;
    }
#endif
    // uint16_t uuid = ATT_SVC_DEVICE_INFO;
    // adv_para.adv_data.completed_uuid->uuid_list[0].uuid.uuid_16[0] = uuid & 0xff;
    // adv_para.adv_data.completed_uuid->uuid_list[0].uuid.uuid_16[1] = (uuid >> 8) & 0xff;
    // adv_para.adv_data.completed_uuid->uuid_list[1].uuid_len = 2;
    // uuid = ATT_SVC_HEART_RATE;
    // adv_para.adv_data.completed_uuid->uuid_list[1].uuid.uuid_16[0] = uuid & 0xff;
    // adv_para.adv_data.completed_uuid->uuid_list[1].uuid.uuid_16[1] = (uuid >> 8) & 0xff;


    adv_para.adv_data.appearance = &manu_appearance;

    adv_para.evt_handler = ble_app_advertising_event;

    ret = sibles_advertising_init(g_app_advertising_context, &adv_para);
    // if (ret == SIBLES_ADV_NO_ERR)
    // {
    //     sibles_advertising_start(g_app_advertising_context);
    // }

    rt_free(adv_para.rsp_data.completed_name);
    rt_free(adv_para.adv_data.manufacturer_data);
    rt_free(adv_para.rsp_data.srv_data);
#ifdef ADV_UUID
    rt_free(adv_para.adv_data.completed_uuid);
#endif
}

void ble_app_advertising_start(void)
{
    uint8_t ret = 0;
    ret = sibles_advertising_start(g_app_advertising_context);
    BLE_APP_LOG_I("adv start ret:%d\r\n", ret);
}


void ble_app_advertising_stop(void)
{
    uint8_t ret = 0;
    ret = sibles_advertising_stop(g_app_advertising_context);
    BLE_APP_LOG_I("adv stop ret:%d\r\n", ret);
}

static void ble_app_advertising_update(void)
{
    uint8_t data[MANUF_SPECIFIC_DATA_SIZE] = {0};
    uint16_t manu_company_id = 0x73;
    uint16_t manu_appearance = SPORT_WATCH_APPERANCE;//1153;
    bd_addr_t addr;

    ble_get_public_address(&addr);

    // if (is_hrm_tx != hrm_tx_flag)
    {
        uint8_t  uuid_num = 0;
        uint16_t uuid_arr[2] = {0};
        if (is_hrm_tx)
        {
            uuid_arr[uuid_num++] = ATT_SVC_HEART_RATE;
            // uuid_arr[uuid_num++] = ATT_SVC_DEVICE_INFO;
        }
        // else
        // {
        //     uuid_arr[uuid_num++] = ATT_SVC_DEVICE_INFO;
        // }
        adv_para.adv_data.completed_uuid = rt_malloc(sizeof(sibles_adv_type_srv_uuid_t) + (sizeof(sibles_adv_uuid_t) * uuid_num));
        adv_para.adv_data.completed_uuid->count = uuid_num;
        for (uint8_t i = 0; i < uuid_num; i++)
        {
            adv_para.adv_data.completed_uuid->uuid_list[i].uuid_len = 2;
            uint16_t uuid = uuid_arr[i];
            adv_para.adv_data.completed_uuid->uuid_list[i].uuid.uuid_16[0] = uuid & 0xff;
            adv_para.adv_data.completed_uuid->uuid_list[i].uuid.uuid_16[1] = (uuid >> 8) & 0xff;
        }

        /* Prepare name filed. Due to name is too long to put adv data, put it to rsp data.*/
        adv_para.rsp_data.completed_name = rt_malloc(rt_strlen(local_name) + sizeof(sibles_adv_type_name_t));
        adv_para.rsp_data.completed_name->name_len = rt_strlen(local_name);
        rt_memcpy(adv_para.rsp_data.completed_name->name, local_name, adv_para.rsp_data.completed_name->name_len);

        adv_para.rsp_data.srv_data = rt_malloc(BD_ADDR_LEN + sizeof(sibles_adv_type_srv_data_t));
        adv_para.rsp_data.srv_data->uuid.uuid_len = 2;
        adv_para.rsp_data.srv_data->uuid.uuid.uuid_16[0] = 0x02;
        adv_para.rsp_data.srv_data->uuid.uuid.uuid_16[1] = 0x38;
        adv_para.rsp_data.srv_data->data_len = BD_ADDR_LEN;
        // memcpy(adv_para.rsp_data.srv_data->uuid.uuid.uuid_128, g_app_svc_alipay, adv_para.rsp_data.srv_data->uuid.uuid_len);
        for(uint8_t i = 0; i < BD_ADDR_LEN; i++)
        {
            adv_para.rsp_data.srv_data->additional_data[i] = addr.addr[BD_ADDR_LEN - i - 1];
        }

        // static uint32_t device_adv_info = 0;
        device_adv_info = device_info_num % 100000000;
        // device_info_num %= 100000000;

        inttoa(device_adv_info, data, MANUF_SPECIFIC_DATA_SIZE);

        g_device_set_ble_manufacturer(data, MANUF_SPECIFIC_DATA_SIZE);
        adv_para.adv_data.manufacturer_data = rt_malloc(sizeof(sibles_adv_type_manufacturer_data_t) + sizeof(data) + /*BD_ADDR_LEN +*/
                                                BLE_ADV_REGION_CODE_LEN + BLE_ADV_DEVICE_TYPE_LEN + BLE_ADV_RESERVED_LEN +
                                                BLE_ADV_BOND_ALLOW_STATE_LEN + BLE_ADV_VERSION_LEN);
        adv_para.adv_data.manufacturer_data->company_id = manu_company_id;
        adv_para.adv_data.manufacturer_data->data_len = sizeof(data) + /*BD_ADDR_LEN +*/ 
                                                    BLE_ADV_REGION_CODE_LEN + BLE_ADV_DEVICE_TYPE_LEN + BLE_ADV_RESERVED_LEN +
                                                    BLE_ADV_BOND_ALLOW_STATE_LEN + BLE_ADV_VERSION_LEN;
        rt_memcpy(adv_para.adv_data.manufacturer_data->additional_data, data, sizeof(data));
        // rt_memcpy(&adv_para.adv_data.manufacturer_data->additional_data[sizeof(data)], addr.addr, BD_ADDR_LEN);
        // for(uint8_t i=0; i<BD_ADDR_LEN; i++)
        // {
        //     adv_para.adv_data.manufacturer_data->additional_data[sizeof(data) + i] = addr.addr[BD_ADDR_LEN - i - 1];
        // }
        adv_para.adv_data.manufacturer_data->additional_data[sizeof(data)] = 0x00;
        adv_para.adv_data.manufacturer_data->additional_data[sizeof(data) + BLE_ADV_REGION_CODE_LEN] = BLE_DEVICE_TYPE;
        for (uint8_t i = 0; i < BLE_ADV_RESERVED_LEN; i++)
        {
            adv_para.adv_data.manufacturer_data->additional_data[sizeof(data) + BLE_ADV_REGION_CODE_LEN + BLE_ADV_DEVICE_TYPE_LEN + i] = 0x00;
        }
        extern uint16_t get_major_app_version(void);
        uint16_t soft_version = get_major_app_version();
        adv_para.adv_data.manufacturer_data->additional_data[sizeof(data) + BLE_ADV_REGION_CODE_LEN + BLE_ADV_DEVICE_TYPE_LEN + BLE_ADV_RESERVED_LEN] = (is_bond_allow & 0x01) << 7;
        adv_para.adv_data.manufacturer_data->additional_data[sizeof(data) + BLE_ADV_REGION_CODE_LEN + BLE_ADV_DEVICE_TYPE_LEN + BLE_ADV_RESERVED_LEN] |= (soft_version >> 8) & 0x7f;
        adv_para.adv_data.manufacturer_data->additional_data[sizeof(data) + BLE_ADV_REGION_CODE_LEN + BLE_ADV_DEVICE_TYPE_LEN + BLE_ADV_RESERVED_LEN + 1] = soft_version & 0xff;
        // sibles_advertising_stop(g_app_advertising_context);
        // sibles_advertising_reconfig(g_app_advertising_context, &adv_para);
        // sibles_advertising_start(g_app_advertising_context);
        sibles_advertising_update_adv_and_scan_rsp_data(g_app_advertising_context, &adv_para.adv_data, &adv_para.rsp_data);

        // is_hrm_tx = hrm_tx_flag;

        rt_free(adv_para.rsp_data.completed_name);
        rt_free(adv_para.adv_data.manufacturer_data);
        rt_free(adv_para.adv_data.completed_uuid);
        rt_free(adv_para.rsp_data.srv_data);
    }
}

/************************************************************************
 *@function:void ble_app_adv_hrs_change(bool hrm_tx_flag);
 *@brief 开启心率推送时更新广播信息
 *@param: bool hrm_tx_flag 心率推送标志
 *@return:null
*************************************************************************/
void ble_app_adv_hrs_change(bool hrm_tx_flag)
{
    app_env_t *env = ble_app_get_env();
    is_hrm_tx = hrm_tx_flag;
    uint8_t conn_num = 0;

    // 查询当前连接数
    for (uint8_t i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
    {
        if (env->conn_idx[i] != BLE_APP_CONN_IDX_INVALID)
        {
            conn_num++;
        }
    }

    // 开启心率推送
    if (hrm_tx_flag)
    {

        // 连接数为0时，直接更新广播
        if (conn_num == 0)
        {
            if (env->is_bg_adv_on)
            {
                ble_app_advertising_update();
            }
            else
            {
                adv_change_delay = true;
                ble_app_advertising_start();
            }
        }

        // 连接数为1时，重新打开广播并更新广播
        else if (conn_num == 1)
        {
            if (!env->is_bg_adv_on)
            {
                adv_change_delay = true;
                ble_app_advertising_start();
            }
            else
            {
                ble_app_advertising_update();
            }
        }

        // 连接数为2时（不应存在该情况，但为了保险起见，仍然做识别处理），断连非配对设备，重新打开广播并更新广播
        else
        {
            for (uint8_t i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
            {
                if (env->conn_idx[i] != BLE_APP_CONN_IDX_INVALID)
                {
                    if (!ble_app_bond_device_check(env->conn_para[i].peer_addr.addr))
                    {
                        adv_change_delay = true;
                        ble_gap_disconnect_t dis_conn;
                        dis_conn.conn_idx = env->conn_idx[i];
                        dis_conn.reason = CO_ERROR_REMOTE_USER_TERM_CON;
                        ble_gap_disconnect(&dis_conn);
                    }
                }
            }

            if (!env->is_bg_adv_on)
            {
                ble_app_advertising_start();
            }
        }
    }

    // 关闭心率推送
    else
    {

        // 连接数为0时，直接更新广播
        if (conn_num == 0)
        {
            if (env->is_bg_adv_on)
            {
                ble_app_advertising_update();
            }
            else
            {
                adv_change_delay = true;
                ble_app_advertising_start();
            }
        }

        // 连接数为1时（此时应处于开启广播状态）, 判断是否为配对设备
        else if (conn_num == 1)
        {
            for (uint8_t i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
            {
                if (env->conn_idx[i] != BLE_APP_CONN_IDX_INVALID)
                {
                    // 如果是配对设备，则不断开连接，更新广播并停止广播
                    if (ble_app_bond_device_check(env->conn_para[i].peer_addr.addr))
                    {
                        adv_change_delay = true;
                        if (env->is_bg_adv_on)
                        {
                            ble_app_advertising_stop();
                        }
                    }
                    // 如果是非配对设备，则断开连接，重新打开广播并更新广播
                    else
                    {
                        ble_gap_disconnect_t dis_conn;
                        dis_conn.conn_idx = env->conn_idx[i];
                        dis_conn.reason = CO_ERROR_REMOTE_USER_TERM_CON;
                        ble_gap_disconnect(&dis_conn);

                        if (env->is_bg_adv_on)
                        {
                            ble_app_advertising_update();
                        }
                        else
                        {
                            adv_change_delay = true;
                        }
                    }
                }
            }
        }

        // 连接数为2时（此时应处于关闭广播状态）,判断是否有配对设备
        else
        {
            for (uint8_t i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
            {
                if (env->conn_idx[i] != BLE_APP_CONN_IDX_INVALID)
                {
                    // 断开非配对设备
                    if (!ble_app_bond_device_check(env->conn_para[i].peer_addr.addr))
                    {
                        ble_gap_disconnect_t dis_conn;
                        dis_conn.conn_idx = env->conn_idx[i];
                        dis_conn.reason = CO_ERROR_REMOTE_USER_TERM_CON;
                        ble_gap_disconnect(&dis_conn);
                    }
                }
            }
            adv_change_delay = true;
        }
    }
}

/************************************************************************
 *@function:ble_app_adv_bond_state_change(bool bond_allow, bool disconn_force);
 *@brief 开关配对二维码时更新广播信息
 *@param: bool bond_allow 配对二维码状态
 *@param: bool disconn_force 强制断连标志
 *@return:null
*************************************************************************/
void ble_app_adv_bond_state_change(bool bond_allow, bool disconn_force)
{
    app_env_t *env = ble_app_get_env();
    static bool last_bond_allow = false;
    is_bond_allow = bond_allow;

    if (env->is_bg_adv_on)
    {
        ble_app_advertising_update();
    }
    else
    {
        if (last_bond_allow != bond_allow)
        {
            adv_change_delay = true;
        }

        if (disconn_force)
        {
            if ((bond_allow == true) && (last_bond_allow != bond_allow))
            {
                ble_unbond_status_update();
                g_ble_app_mb_op_type = BLE_USER_EVENT_UNBOND;
                rt_timer_start(bond_status_updata_delay_timer);
            }
            else
            {
                for (uint8_t i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
                {
                    if (env->conn_idx[i] != BLE_APP_CONN_IDX_INVALID)
                    {
                        ble_gap_disconnect_t dis_conn;
                        dis_conn.conn_idx = env->conn_idx[i];
                        dis_conn.reason = CO_ERROR_REMOTE_USER_TERM_CON;
                        ble_gap_disconnect(&dis_conn);
                    }
                }
            }
        }
    }
    last_bond_allow = is_bond_allow;
}

/************************************************************************
 *@function:bool ble_app_bond_allow_get(void);
 *@brief 获取是否允许配对状态
 *@param: null
 *@return: bool 是否允许配对
*************************************************************************/
bool ble_app_bond_allow_get(void)
{
    return is_bond_allow;
}

void ble_app_disconnect_all(void)
{
    app_env_t *env = ble_app_get_env();

    if(ble_is_connected())
    {
        for (uint8_t i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
        {
            if (env->conn_idx[i] != BLE_APP_CONN_IDX_INVALID)
            {
                ble_gap_disconnect_t dis_conn;
                dis_conn.conn_idx = env->conn_idx[i];
                dis_conn.reason = CO_ERROR_REMOTE_USER_TERM_CON;
                ble_gap_disconnect(&dis_conn);
            }
        }
    }
}

// HandLe read operation
uint8_t *ble_app_gatts_get_cbk_ch0(uint8_t conn_idx, uint8_t idx, uint16_t *len)
{
    uint8_t *ret_val = NULL;
    app_env_t *env = ble_app_get_env();
    *len = 0;
    // BLE_APP_LOG_I("[ble_app_gatts_get_cbk] idx %d\r\n", idx);
    switch (idx)
    {
    case BLE_APP_CHAR_RX_VALUE:
    {
        // Prepare data to remote device
        BLE_APP_LOG_I("Read app value %d\r\n", env->data[0].data);
        ret_val = (uint8_t *)&env->data[0].data;
        *len = 4;
        break;
    }
    default:
        break;
    }
    return ret_val;
}

// Hanlde read operation
uint8_t *ble_app_gatts_get_cbk_ch1(uint8_t conn_idx, uint8_t idx, uint16_t *len)
{
    uint8_t *ret_val = NULL;
    app_env_t *env = ble_app_get_env();
    *len = 0;
    // BLE_APP_LOG_I("[ble_app_gatts_get_cbk] idx %d\r\n", idx);
    switch (idx)
    {
    case BLE_APP_CHAR_RX_VALUE:
    {
        // Prepare data to remote device
        BLE_APP_LOG_I("Read app value %d\r\n", env->data[1].data);
        ret_val = (uint8_t *)&env->data[1].data;
        *len = 4;
        break;
    }
    default:
        break;
    }
    return ret_val;
}

// Hanlde read operation
uint8_t *ble_app_gatts_get_cbk_ch2(uint8_t conn_idx, uint8_t idx, uint16_t *len)
{
    uint8_t *ret_val = NULL;
    app_env_t *env = ble_app_get_env();
    *len = 0;
    // BLE_APP_LOG_I("[ble_app_gatts_get_cbk] idx %d\r\n", idx);
    switch (idx)
    {
    case BLE_APP_CHAR_RX_VALUE:
    {
        // Prepare data to remote device
        BLE_APP_LOG_I("Read app value %d\r\n", env->data[2].data);
        ret_val = (uint8_t *)&env->data[2].data;
        *len = 4;
        break;
    }
    default:
        break;
    }
    return ret_val;
}

// Hanlde read operation
uint8_t *ble_app_gatts_get_cbk_ch3(uint8_t conn_idx, uint8_t idx, uint16_t *len)
{
    uint8_t *ret_val = NULL;
    app_env_t *env = ble_app_get_env();
    *len = 0;
    // BLE_APP_LOG_I("[ble_app_gatts_get_cbk] idx %d\r\n", idx);
    switch (idx)
    {
    case BLE_APP_CHAR_RX_VALUE:
    {
        // Prepare data to remote device
        BLE_APP_LOG_I("Read app value %d\r\n", env->data[3].data);
        ret_val = (uint8_t *)&env->data[3].data;
        *len = 4;
        break;
    }
    default:
        break;
    }
    return ret_val;
}

// Hanlde read operation
uint8_t *ble_app_gatts_get_cbk_goodix(uint8_t conn_idx, uint8_t idx, uint16_t *len)
{
    uint8_t *ret_val = NULL;
    app_env_t *env = ble_app_get_env();
    *len = 0;
    // BLE_APP_LOG_I("[ble_app_gatts_get_cbk] idx %d\r\n", idx);
    switch (idx)
    {
    case BLE_APP_CHAR_RX_VALUE:
    {
        // Prepare data to remote device
        BLE_APP_LOG_I("Read app value %d\r\n", env->data[4].data);
        ret_val = (uint8_t *)&env->data[4].data;
        *len = 4;
        break;
    }
    default:
        break;
    }
    return ret_val;
}

// Hanlde read operation
uint8_t *ble_app_gatts_get_cbk_alipay(uint8_t conn_idx, uint8_t idx, uint16_t *len)
{
    uint8_t *ret_val = NULL;
    app_env_t *env = ble_app_get_env();
    *len = 0;
    BLE_APP_LOG_I("[ble_app_gatts_get_cbk_alipay] idx %d\r\n", idx);
    switch (idx)
    {
    case BLE_APP_ALIPAY_CHAR_VALUE:
    {
        // Prepare data to remote device
        BLE_APP_LOG_I("Read app value %d\r\n", env->data[5].data);
        ret_val = (uint8_t *)&env->data[5].data;
        *len = 4;
        break;
    }
    case BLE_APP_ALIPAY_CLIENT_CHAR_CONFIG_DESCRIPTOR:
    {
        BLE_APP_LOG_I("cccd value %d\r\n", env->data[5].is_config_on);
        // ret_val = (uint8_t *)&env->data[5].is_config_on;
        // *len = 1;
        break;
    }
    default:
        break;
    }
    return ret_val;
}

// HandLe read operation
uint8_t *ble_app_gatts_get_cbk_debug(uint8_t conn_idx, uint8_t idx, uint16_t *len)
{
    uint8_t *ret_val = NULL;
    app_env_t *env = ble_app_get_env();
    *len = 0;
    // BLE_APP_LOG_I("[ble_app_gatts_get_cbk] idx %d\r\n", idx);
    switch (idx)
    {
    case BLE_APP_CHAR_RX_VALUE:
    {
        // Prepare data to remote device
        BLE_APP_LOG_I("Read app value %d\r\n", env->data[6].data);
        ret_val = (uint8_t *)&env->data[6].data;
        *len = 4;
        break;
    }
    default:
        break;
    }
    return ret_val;
}

#ifdef BLE_HID


uint8_t *ble_app_gatts_get_cbk_hid(uint8_t conn_idx, uint8_t idx, uint16_t *len)
{
    uint8_t *ret_val = NULL;
    *len = 0;

    BLE_APP_LOG_I("HID get: idx = %d", idx);

    switch (idx)
    {
        case BLE_APP_HID_INFO_VAL:
        {
            ret_val = (uint8_t *)&ble_app_hid_info;
            *len = sizeof(ble_app_hid_info);
            break;
        }

        case BLE_APP_HID_REPORT_MAP_VAL:
        {
            ret_val = (uint8_t *)&ble_app_hid_report_map;
            *len = sizeof(ble_app_hid_report_map);
            break;
        }

        case BLE_APP_HID_REPORT_VAL:
        {
            break;
        }

        case BLE_APP_HID_CTRL_VAL:
        {
            ret_val = (uint8_t *)&ctrl_point;
            *len = sizeof(ctrl_point);
            BLE_APP_LOG_I("HID %s", ctrl_point ? "Exit Suspend" : "Suspend");
            break;
        }

        case BLE_APP_HID_REPORT_REF:
        {
            ret_val = (uint8_t *)&ble_app_report;
            *len = sizeof(ble_app_report);
            break;
        }

        default:
            break;
    }
    return ret_val;
}

#endif

// HandLe write operation
uint8_t *ble_app_gatts_get_cbk_sec(uint8_t conn_idx, uint8_t idx, uint16_t *len)
{
    uint8_t *ret_val = NULL;
    app_env_t *env = ble_app_get_env();
    *len = 0;
    // BLE_APP_LOG_I("[ble_app_gatts_get_cbk] idx %d\r\n", idx);
    switch (idx)
    {
    case BLE_APP_CHAR_RX_VALUE:
    {
        // Prepare data to remote device
        BLE_APP_LOG_I("Read app value %d", env->data[8].data);
        ret_val = (uint8_t *)&env->data[8].data;
        *len = 4;
        break;
    }
    default:
        break;
    }
    return ret_val;
}

extern void ble_data_deal_notify(void);

// Handle read operation
uint8_t ble_app_gatts_set_cbk_ch0(uint8_t conn_idx, sibles_set_cbk_t *para)
{
    app_env_t *env = ble_app_get_env();
    // BLE_APP_LOG_I("[ble_app_gatts_set_cbk0] idx %d\r\n", para->idx);
    switch (para->idx)
    {
        case BLE_APP_CHAR_RX_VALUE:
        {
            BLE_APP_LOG_HEX("channel 0 get: ", 16, para->value, para->len);
            lb55x_ble_data_continuous_idletime_update();
            lb55x_ble_nus_data_rx_ch0(para->value, para->len);
            break;
        }
        case BLE_APP_CLIENT_CHAR_CONFIG_DESCRIPTOR:
        {
            env->data[0].is_config_on = *(para->value);
            BLE_APP_LOG_I("[ble_app_gatts_set_cbk0] CCCD %d\r\n", env->data[0].is_config_on);
            // Enable notification via timer
            if (env->data[0].is_config_on)
            {
                ;// rt_timer_start(env->time_handle);
                lb55x_ble_data_continuous_idletime_update();
                lb55x_ble_nus_channel_status_set(ENUM_CHANNEL0);
            }
            else
            {
                // rt_timer_stop(env->time_handle);
                lb55x_ble_nus_channel_status_clear(ENUM_CHANNEL0);
            }
            break;
        }
        default:
            break;
    }
    return 0;
}

// Hanlde read operation
uint8_t ble_app_gatts_set_cbk_ch1(uint8_t conn_idx, sibles_set_cbk_t *para)
{
    app_env_t *env = ble_app_get_env();
    // BLE_APP_LOG_I("[ble_app_gatts_set_cbk1] idx %d\r\n", para->idx);
    switch (para->idx)
    {
        case BLE_APP_CHAR_RX_VALUE:
        {
            BLE_APP_LOG_HEX("channel 1 get: ", 16, para->value, para->len);
            lb55x_ble_data_continuous_idletime_update();
            lb55x_ble_nus_data_rx_ch1(para->value, para->len);
            break;
        }
        case BLE_APP_CLIENT_CHAR_CONFIG_DESCRIPTOR:
        {
            env->data[1].is_config_on = *(para->value);
            BLE_APP_LOG_I("[ble_app_gatts_set_cbk1] CCCD %d\r\n", env->data[1].is_config_on);
            // Enable notification via timer
            if (env->data[1].is_config_on)
            {
                ;// rt_timer_start(env->time_handle);
                lb55x_ble_data_continuous_idletime_update();
                lb55x_ble_nus_channel_status_set(ENUM_CHANNEL1);
            }
            else
            {
                // rt_timer_stop(env->time_handle);
                lb55x_ble_nus_channel_status_clear(ENUM_CHANNEL1);
            }
            break;
        }
        default:
            break;
    }
    return 0;
}

// Hanlde read operation
uint8_t ble_app_gatts_set_cbk_ch2(uint8_t conn_idx, sibles_set_cbk_t *para)
{
    app_env_t *env = ble_app_get_env();
    // BLE_APP_LOG_I("[ble_app_gatts_set_cbk2] idx %d\r\n", para->idx);
    switch (para->idx)
    {
        case BLE_APP_CHAR_RX_VALUE:
        {
            BLE_APP_LOG_HEX("channel 2 get: ", 16, para->value, para->len);
            lb55x_ble_data_continuous_idletime_update();
            lb55x_ble_nus_data_rx_ch2(para->value, para->len);
            break;
        }
        case BLE_APP_CLIENT_CHAR_CONFIG_DESCRIPTOR:
        {
            env->data[2].is_config_on = *(para->value);
            BLE_APP_LOG_I("[ble_app_gatts_set_cbk2] CCCD %d\r\n", env->data[2].is_config_on);
            // Enable notification via timer
            if (env->data[2].is_config_on)
            {
                ;// rt_timer_start(env->time_handle);
                lb55x_ble_data_continuous_idletime_update();
                lb55x_ble_nus_channel_status_set(ENUM_CHANNEL2);
            }
            else
            {
                // rt_timer_stop(env->time_handle);
                lb55x_ble_nus_channel_status_clear(ENUM_CHANNEL2);
            }
            break;
        }
        default:
            break;
    }
    return 0;
}

// Hanlde read operation
uint8_t ble_app_gatts_set_cbk_ch3(uint8_t conn_idx, sibles_set_cbk_t *para)
{
    app_env_t *env = ble_app_get_env();
    // BLE_APP_LOG_I("[ble_app_gatts_set_cbk3] idx %d\r\n", para->idx);
    switch (para->idx)
    {
        case BLE_APP_CHAR_RX_VALUE:
        {
            // rt_hexdump("channel 3 get: ", 16, para->value, para->len);
            lb55x_ble_data_continuous_idletime_update();
            lb55x_ble_nus_data_rx_ch3(para->value, para->len);
            break;
        }
        case BLE_APP_CLIENT_CHAR_CONFIG_DESCRIPTOR:
        {
            env->data[3].is_config_on = *(para->value);
            BLE_APP_LOG_I("[ble_app_gatts_set_cbk3] CCCD %d\r\n", env->data[3].is_config_on);
            // Enable notification via timer
            if (env->data[3].is_config_on)
            {
                ;// rt_timer_start(env->time_handle);
                lb55x_ble_data_continuous_idletime_update();
                lb55x_ble_nus_channel_status_set(ENUM_CHANNEL3);
            }
            else
            {
                // rt_timer_stop(env->time_handle);
                lb55x_ble_nus_channel_status_clear(ENUM_CHANNEL3);
            }
            break;
        }
        default:
            break;
    }
    return 0;
}

// Hanlde read operation
uint8_t ble_app_gatts_set_cbk_goodix(uint8_t conn_idx, sibles_set_cbk_t *para)
{
    app_env_t *env = ble_app_get_env();
    // BLE_APP_LOG_I("[ble_app_gatts_set_cbk_goodix] idx %d\r\n", para->idx);
    switch (para->idx)
    {
        case BLE_APP_CHAR_RX_VALUE:
        {
            BLE_APP_LOG_HEX("goodix :", 20, para->value, para->len);
            t_recv_ble_data ble_data;
            memset(&ble_data, 0, sizeof(ble_data));
            if(para->len <= BLE_ROTOCOL_PACKET_LEN_MAX)
            {
                memcpy(&ble_data.a_ble_data[0], para->value, para->len);
                ble_data.ble_data_len = para->len;
            }
            else
            {
                memcpy(&ble_data.a_ble_data[0], para->value, BLE_ROTOCOL_PACKET_LEN_MAX);
                ble_data.ble_data_len = BLE_ROTOCOL_PACKET_LEN_MAX;
            }

            // Gh3x2xDemoProtocolProcess(para->value, para->len);
            Gh3x2xDemoProtocolProcessArr(&ble_data);
            lb55x_ble_data_continuous_idletime_update();
            lb55x_ble_nus_data_rx_goodix(para->value, para->len);
            break;
        }
        case BLE_APP_CLIENT_CHAR_CONFIG_DESCRIPTOR:
        {
            env->data[4].is_config_on = *(para->value);
            BLE_APP_LOG_I("[ble_app_gatts_set_cbk_goodix] CCCD %d\r\n", env->data[3].is_config_on);
            // Enable notification via timer
            if (env->data[4].is_config_on)
            {
                ;// rt_timer_start(env->time_handle);
                lb55x_ble_data_continuous_idletime_update();
                // lb55x_ble_nus_channel_status_set(ENUM_CHANNEL3);
            }
            else
            {
                // rt_timer_stop(env->time_handle);
                // lb55x_ble_nus_channel_status_clear(ENUM_CHANNEL3);
            }
            ble_connect_state_notify(BLE_CONNECT_STATE_GOODIX_READY);
            break;
        }
        default:
            break;
    }
    return 0;
}

ble_alipay_data_t g_alipay_data;
rt_mq_t g_alipay_message;

// Hanlde read operation
uint8_t ble_app_gatts_set_cbk_alipay(uint8_t conn_idx, sibles_set_cbk_t *para)
{
    app_env_t *env = ble_app_get_env();
    // BLE_APP_LOG_I("[ble_app_gatts_set_cbk_alipay] idx %d\r\n", para->idx);
    switch (para->idx)
    {
        case BLE_APP_ALIPAY_CHAR_VALUE:
        {
            BLE_APP_LOG_HEX("alipay :", 16, para->value, para->len);
            // alipay_ble_recv_data_handle(para->value, para->len);
            // memcpy(g_alipay_data.a_ble_send_data, para->value, para->len);
            // g_alipay_data.p_ble_alipay_data = para->value;
#if 0
            g_alipay_data.ble_alipay_data_len = para->len;
            if(g_alipay_data.ble_alipay_data_len <= 250)
            {
                memcpy(g_alipay_data.p_ble_alipay_data, para->value, g_alipay_data.ble_alipay_data_len);
            }
            else
            {
                memcpy(g_alipay_data.p_ble_alipay_data, para->value, 250);
            }
            rt_mq_send(g_alipay_message, &g_alipay_data, sizeof(g_alipay_data));
#else
            ble_csi_data_deal(para->value, para->len);
#endif
            break;
        }

        case BLE_APP_ALIPAY_CLIENT_CHAR_CONFIG_DESCRIPTOR:
        {
            env->data[BLE_NUS_SERVICE_CHANNEL_ALIPAY].is_config_on = *(para->value);
            // BLE_APP_LOG_I("[ble_app_gatts_set_cbk3] CCCD %d\r\n", env->data[3].is_config_on);
            // Enable notification via timer
            if (env->data[BLE_NUS_SERVICE_CHANNEL_ALIPAY].is_config_on)
            {
                ;// rt_timer_start(env->time_handle);
                // lb55x_ble_data_continuous_idletime_update();
                // lb55x_ble_nus_channel_status_set(ENUM_CHANNEL3);
                ble_csi_channel_status_set();
            }
            else
            {
                // rt_timer_stop(env->time_handle);
                // lb55x_ble_nus_channel_status_clear(ENUM_CHANNEL3);
                ble_csi_channel_status_clear();
            }
            break;
        }
        default:
            break;
    }
    return 0;
}

// Handle read operation
uint8_t ble_app_gatts_set_cbk_debug(uint8_t conn_idx, sibles_set_cbk_t *para)
{
    app_env_t *env = ble_app_get_env();
    // BLE_APP_LOG_I("[ble_app_gatts_set_debug] idx %d\r\n", para->idx);
    switch (para->idx)
    {
        case BLE_APP_CHAR_RX_VALUE:
        {
            BLE_APP_LOG_HEX("channel debug get: ", 16, para->value, para->len);
            // lb55x_ble_data_continuous_idletime_update();
            // lb55x_ble_nus_data_rx_ch0(para->value, para->len);
            ble_debug_data_deal(para->value, para->len);
            break;
        }
        case BLE_APP_CLIENT_CHAR_CONFIG_DESCRIPTOR:
        {
            env->data[BLE_NUS_SERVICE_CHANNEL_DEBUG].is_config_on = *(para->value);
            BLE_APP_LOG_I("[ble_app_gatts_set_debug] CCCD %d\r\n", env->data[BLE_NUS_SERVICE_CHANNEL_DEBUG].is_config_on);
            // Enable notification via timer
            if (env->data[BLE_NUS_SERVICE_CHANNEL_DEBUG].is_config_on)
            {
                // ;// rt_timer_start(env->time_handle);
                // lb55x_ble_data_continuous_idletime_update();
                // lb55x_ble_nus_channel_status_set(ENUM_CHANNEL0);
                ble_debug_channel_status_set();
            }
            else
            {
                // // rt_timer_stop(env->time_handle);
                // lb55x_ble_nus_channel_status_clear(ENUM_CHANNEL0);
                ble_debug_channel_status_clear();
            }
            break;
        }
        default:
            break;
    }
    return 0;
}

#ifdef BLE_HID

uint8_t ble_app_gatts_set_cbk_hid(uint8_t conn_idx, sibles_set_cbk_t *para)
{
    app_env_t *env = ble_app_get_env();

    // BLE_APP_LOG_I("[ble_app_gatts_set_cbk_hid] idx %d\r\n", para->idx);
    switch (para->idx)
    {
        case BLE_APP_HID_REPORT_NTF_CFG:
        {
            env->data[BLE_NUS_SERVICE_CHANNEL_HID].is_config_on = *(para->value);
            BLE_APP_LOG_I("[ble_app_gatts_set_cbk_hid] CCCD %d", env->data[BLE_NUS_SERVICE_CHANNEL_HID].is_config_on);
            break;
        }

        case BLE_APP_HID_CTRL_VAL:
        {
            RT_ASSERT(para->len <= sizeof(ctrl_point));
            memcpy(&ctrl_point, para->value, sizeof(uint8_t));
            BLE_APP_LOG_I("[ble_app_gatts_set_cbk_hid] updated app value to:%x", ctrl_point);
            break;
        }

        default:
            break;
    }
    return 0;
}

#endif

uint8_t ble_app_gatts_set_cbk_sec(uint8_t conn_idx, sibles_set_cbk_t *para)
{
    app_env_t *env = ble_app_get_env();
    // BLE_APP_LOG_I("[ble_app_gatts_set_cbk_sec] idx %d\r\n", para->idx);
    switch (para->idx)
    {
        case BLE_APP_CHAR_RX_VALUE:
        {
            ;
            break;
        }
        case BLE_APP_CLIENT_CHAR_CONFIG_DESCRIPTOR:
        {
            env->data[BLE_NUS_SERVICE_CHANNEL_SECURITY].is_config_on = *(para->value);
            BLE_APP_LOG_I("[ble_app_gatts_set_cbk_sec] CCCD %d\r\n", env->data[BLE_NUS_SERVICE_CHANNEL_SECURITY].is_config_on);
            // Enable notification via timer
            break;
        }
        default:
            break;
    }
    return 0;
}

int32_t lb55x_ble_nus_data_send(uint8_t channel, uint8_t *data, uint16_t *length)
{
    app_env_t *env = ble_app_get_env();
    sibles_value_t value;
    int ret;
    // rt_hexdump("nus_send", 16, data, *length);//调试时打开
    if(channel >= BLE_NUS_SERVICE_CHANNEL_NUM)
    {
        return -1;
    }

    value.hdl = env->data[channel].srv_handle;
    value.idx = BLE_APP_CHAR_TX_VALUE;
    value.len = *length;
    value.value = data;

    for (uint8_t i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
    {
        if (env->conn_idx[i] != BLE_APP_CONN_IDX_INVALID)
        {
            ret = sibles_write_value(env->conn_idx[i], &value);
        }
    }
    if (ret == *length)
    {
        return 0;
    }
    else
    {
        return -1;
    }
}

uint32_t ble_app_send_nus_data(uint8_t const *data, uint16_t * length,  BLE_APP_NUS_CHANNLE_UUID nus_channel_uuid)
{
    uint32_t err_code = 0;
    extern bool ble_nus_channel_all_is_start(void);
    // rt_hexdump("ble_app_send_nus_data", 16, (rt_uint8_t *)data, *length);
    if(ble_nus_channel_all_is_start())
    {
        switch (nus_channel_uuid)
		{
            case BLE_NUS_UUID_CH0:
                err_code = lb55x_ble_nus_data_send(0, (uint8_t *)data, length);
                break;
            case BLE_NUS_UUID_CH1:
                err_code = lb55x_ble_nus_data_send(1, (uint8_t *)data, length);
                break;
            case BLE_NUS_UUID_CH2:
                err_code = lb55x_ble_nus_data_send(2, (uint8_t *)data, length);
                break;
            case BLE_NUS_UUID_CH3:
                err_code = lb55x_ble_nus_data_send(3, (uint8_t *)data, length);
                break;
            default:
                break;
        }
    }

    return err_code;
}

void lb55x_ble_nus_ppg_data_send(uint8_t *ppg_data, uint16_t *ppg_length)
{
    app_env_t *env = ble_app_get_env();
    sibles_value_t value;


    value.hdl = env->data[4].srv_handle;
    value.idx = BLE_APP_CHAR_TX_VALUE;
    value.len = *ppg_length;
    value.value = ppg_data;

    for (uint8_t i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
    {
        if (env->conn_idx[i] != BLE_APP_CONN_IDX_INVALID)
        {
            sibles_write_value(env->conn_idx[i], &value);
        }
    }
    //BLE_APP_LOG_I("[ble_ppg_data_send] send_len %d\r\n", value.len);
}

void ble_ppg_data_send(ble_send_data_t* t_ppg_data)
{
    lb55x_ble_nus_ppg_data_send(t_ppg_data->a_ble_send_data, &(t_ppg_data->ble_send_data_len));
}

void lb55x_ble_nus_csi_data_send(uint8_t *data, uint16_t *length)
{
    app_env_t *env = ble_app_get_env();
    sibles_value_t value;

    value.hdl = env->data[5].srv_handle;
    value.idx = BLE_APP_ALIPAY_CHAR_VALUE;
    value.len = *length;
    value.value = data;

    for (uint8_t i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
    {
        if (env->conn_idx[i] != BLE_APP_CONN_IDX_INVALID)
        {
            sibles_write_value(env->conn_idx[i], &value);
        }
    }
}

void lb55x_ble_nus_debug_data_send(uint8_t *data, uint16_t *length)
{
    app_env_t *env = ble_app_get_env();
    sibles_value_t value;

    value.hdl = env->data[6].srv_handle;
    value.idx = BLE_APP_CHAR_TX_VALUE;
    value.len = *length;
    value.value = data;

    for (uint8_t i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
    {
        if (env->conn_idx[i] != BLE_APP_CONN_IDX_INVALID)
        {
            sibles_write_value(env->conn_idx[i], &value);
        }
    }
}

static void ble_app_service_ch0_init(void)
{
    app_env_t *env = ble_app_get_env();
    sibles_register_svc_128_t svc;

    svc.att_db = (struct attm_desc_128 *)&app_att_db_ch0;   //设置特征uuid
    svc.num_entry = BLE_APP_ATT_NB;
    /* Service security level to control all characteristic. */
    svc.sec_lvl = PERM(SVC_AUTH, NO_AUTH) | PERM(SVC_UUID_LEN, UUID_128) | PERM(SVC_MI, ENABLE);
    svc.uuid = g_app_svc_ch0;   //设置服务uuid
    /* Reigster GATT service and related callback for next response. */
    env->data[0].srv_handle = sibles_register_svc_128(&svc);
    if (env->data[0].srv_handle)
        sibles_register_cbk(env->data[0].srv_handle, ble_app_gatts_get_cbk_ch0, ble_app_gatts_set_cbk_ch0);
}

static void ble_app_service_ch1_init(void)
{
    app_env_t *env = ble_app_get_env();
    sibles_register_svc_128_t svc;

    svc.att_db = (struct attm_desc_128 *)&app_att_db_ch1;   //设置特征uuid
    svc.num_entry = BLE_APP_ATT_NB;
    /* Service security level to control all characteristic. */
    svc.sec_lvl = PERM(SVC_AUTH, NO_AUTH) | PERM(SVC_UUID_LEN, UUID_128) | PERM(SVC_MI, ENABLE);
    svc.uuid = g_app_svc_ch1;   //设置服务uuid
    /* Reigster GATT service and related callback for next response. */
    env->data[1].srv_handle = sibles_register_svc_128(&svc);
    if (env->data[1].srv_handle)
        sibles_register_cbk(env->data[1].srv_handle, ble_app_gatts_get_cbk_ch1, ble_app_gatts_set_cbk_ch1);
}

static void ble_app_service_ch2_init(void)
{
    app_env_t *env = ble_app_get_env();
    sibles_register_svc_128_t svc;

    svc.att_db = (struct attm_desc_128 *)&app_att_db_ch2;   //设置特征uuid
    svc.num_entry = BLE_APP_ATT_NB;
    /* Service security level to control all characteristic. */
    svc.sec_lvl = PERM(SVC_AUTH, NO_AUTH) | PERM(SVC_UUID_LEN, UUID_128) | PERM(SVC_MI, ENABLE);
    svc.uuid = g_app_svc_ch2;   //设置服务uuid
    /* Reigster GATT service and related callback for next response. */
    env->data[2].srv_handle = sibles_register_svc_128(&svc);
    if (env->data[2].srv_handle)
        sibles_register_cbk(env->data[2].srv_handle, ble_app_gatts_get_cbk_ch2, ble_app_gatts_set_cbk_ch2);
}

static void ble_app_service_ch3_init(void)
{
    app_env_t *env = ble_app_get_env();
    sibles_register_svc_128_t svc;

    svc.att_db = (struct attm_desc_128 *)&app_att_db_ch3;   //设置特征uuid
    svc.num_entry = BLE_APP_ATT_NB;
    /* Service security level to control all characteristic. */
    svc.sec_lvl = PERM(SVC_AUTH, NO_AUTH) | PERM(SVC_UUID_LEN, UUID_128) | PERM(SVC_MI, ENABLE);
    svc.uuid = g_app_svc_ch3;   //设置服务uuid
    /* Reigster GATT service and related callback for next response. */
    env->data[3].srv_handle = sibles_register_svc_128(&svc);
    if (env->data[3].srv_handle)
        sibles_register_cbk(env->data[3].srv_handle, ble_app_gatts_get_cbk_ch3, ble_app_gatts_set_cbk_ch3);
}

static void ble_app_service_goodix_init(void)
{
    app_env_t *env = ble_app_get_env();
    sibles_register_svc_128_t svc;

    svc.att_db = (struct attm_desc_128 *)&app_att_db_goodix;   //设置特征uuid
    svc.num_entry = BLE_APP_ATT_NB;
    /* Service security level to control all characteristic. */
    svc.sec_lvl = PERM(SVC_AUTH, NO_AUTH) | PERM(SVC_UUID_LEN, UUID_128) | PERM(SVC_MI, ENABLE);
    svc.uuid = g_app_svc_goodix;   //设置服务uuid
    /* Reigster GATT service and related callback for next response. */
    env->data[4].srv_handle = sibles_register_svc_128(&svc);
    if (env->data[4].srv_handle)
        sibles_register_cbk(env->data[4].srv_handle, ble_app_gatts_get_cbk_goodix, ble_app_gatts_set_cbk_goodix);
}

static void ble_app_service_alipay_init(void)
{
    app_env_t *env = ble_app_get_env();
    sibles_register_svc_128_t svc;

    svc.att_db = (struct attm_desc_128 *)&app_att_db_alipay;
    svc.num_entry = BLE_APP_ALIPAY_ATT_NB;
    /* Service security level to control all characteristic. */
    svc.sec_lvl = PERM(SVC_AUTH, NO_AUTH) | PERM(SVC_UUID_LEN, UUID_128) | PERM(SVC_MI, ENABLE);
    svc.uuid = g_app_svc_alipay;   //设置服务uuid
    /* Reigster GATT service and related callback for next response. */
    env->data[5].srv_handle = sibles_register_svc_128(&svc);
    if (env->data[5].srv_handle)
        sibles_register_cbk(env->data[5].srv_handle, ble_app_gatts_get_cbk_alipay, ble_app_gatts_set_cbk_alipay);
}

static void ble_app_service_debug_init(void)
{
    app_env_t *env = ble_app_get_env();
    sibles_register_svc_128_t svc;

    svc.att_db = (struct attm_desc_128 *)&app_att_db_debug;   //设置特征uuid
    svc.num_entry = BLE_APP_ATT_NB;
    /* Service security level to control all characteristic. */
    svc.sec_lvl = PERM(SVC_AUTH, NO_AUTH) | PERM(SVC_UUID_LEN, UUID_128) | PERM(SVC_MI, ENABLE);
    svc.uuid = g_app_svc_debug;   //设置服务uuid
    /* Reigster GATT service and related callback for next response. */
    env->data[6].srv_handle = sibles_register_svc_128(&svc);
    if (env->data[6].srv_handle)
        sibles_register_cbk(env->data[6].srv_handle, ble_app_gatts_get_cbk_debug, ble_app_gatts_set_cbk_debug);
}

#ifdef BLE_HID

static void ble_app_service_hid_init(void)
{
    app_env_t *env = ble_app_get_env();
    sibles_register_svc_t svc;

    svc.att_db = (struct attm_desc *)&app_att_db_hid;   //设置特征uuid
    svc.num_entry = BLE_APP_HID_ATT_NB;
    /* Service security level to control all characteristic. */
    svc.sec_lvl = PERM(SVC_AUTH, NO_AUTH) | PERM(SVC_UUID_LEN, UUID_16) | PERM(SVC_MI, ENABLE);
    svc.uuid = ATT_SVC_HID;
    /* Reigster GATT service and related callback for next response. */
    env->data[7].srv_handle = sibles_register_svc(&svc);
    if (env->data[7].srv_handle)
    {
        sibles_register_cbk(env->data[7].srv_handle, ble_app_gatts_get_cbk_hid, ble_app_gatts_set_cbk_hid);
    }
}

#endif

static void ble_app_service_security_init(void)
{
    app_env_t *env = ble_app_get_env();
    sibles_register_svc_128_t svc;

    svc.att_db = (struct attm_desc_128 *)&app_att_db_security;   //设置特征uuid
    svc.num_entry = BLE_APP_ATT_NB;
    /* Service security level to control all characteristic. */
    svc.sec_lvl = PERM(SVC_AUTH, NO_AUTH) | PERM(SVC_UUID_LEN, UUID_128) | PERM(SVC_MI, ENABLE);
    svc.uuid = g_app_svc_sec;   //设置服务uuid
    /* Reigster GATT service and related callback for next response. */
    env->data[8].srv_handle = sibles_register_svc_128(&svc);
    if (env->data[8].srv_handle)
        sibles_register_cbk(env->data[8].srv_handle, ble_app_gatts_get_cbk_sec, ble_app_gatts_set_cbk_sec);
}

static void ble_app_service_init(void)
{
#ifndef CONFIG_PPG_BLE_FACTORY_TEST //TODO qw_env判断是否为厂测模式
    ble_app_service_ch0_init();
    ble_app_service_ch1_init();
    ble_app_service_ch2_init();
    ble_app_service_ch3_init();
    ble_app_service_alipay_init();
#endif
    ble_app_service_goodix_init();
    ble_app_service_debug_init();
    ble_app_service_security_init();
#ifdef BLE_HID
    ble_app_service_hid_init();
#endif
}


// void app_timeout_handler(void *parameter)
// {
//     app_env_t *env = ble_app_get_env();
//     // if (env->data.is_config_on)
//     // {
//         // sibles_value_t value;
//         // value.hdl = env->data.srv_handle;
//         // value.idx = BLE_APP_CHAR_RX_VALUE;
//         // value.len = 4;
//         // value.value = (uint8_t *)&env->data.data;
//         // sibles_write_value(env->conn_idx, &value);
//         // rt_timer_start(env->time_handle);
//     // }
// }

#ifndef NVDS_AUTO_UPDATE_MAC_ADDRESS_ENABLE
ble_common_update_type_t ble_request_public_address(bd_addr_t *addr)
{
    // int ret = bt_mac_addr_generate_via_uid(addr);

    // if (ret != 0)
    // {
    //     BLE_APP_LOG_I("generate mac addres failed %d", ret);
    //     return BLE_UPDATE_NO_UPDATE;
    // }

    uint8_t ble_mac[BD_ADDR_LEN] = {0};
    g_device_get_ble_mac_addr(ble_mac, BD_ADDR_LEN);
    memcpy(addr->addr, ble_mac, BD_ADDR_LEN);
    // BLE_APP_LOG_I("ble mac address is %02x:%02x:%02x:%02x:%02x:%02x\n", addr->addr[0], addr->addr[1], addr->addr[2], addr->addr[3], addr->addr[4], addr->addr[5]);

    return BLE_UPDATE_ALWAYS;
}
#endif // NVDS_AUTO_UPDATE_MAC_ADDRESS_ENABLE

void ble_app_entry(void *param)
{
    app_env_t *env = ble_app_get_env();
    env->mb_handle = rt_mb_create("app", 8, RT_IPC_FLAG_FIFO);
    sifli_ble_enable();
    ble_app_gapc_init(true);
    ble_app_ams_init();
    if (wait_user_id_timer == NULL)
    {
        wait_user_id_timer = rt_timer_create(WAIT_USER_ID_TIMER_NAME, ble_app_wait_user_id_timer_callback, NULL,
                                            rt_tick_from_millisecond(BLE_APP_USER_ID_WAIT_TIME), RT_TIMER_FLAG_ONE_SHOT);
    }
    if (bond_status_updata_delay_timer == NULL)
    {
        bond_status_updata_delay_timer = rt_timer_create(BOND_STATUS_UPDATA_DELAY_TIMER_NAME, ble_app_bond_status_updata_delay_timer_callback, &g_ble_app_mb_op_type,
                                                        rt_tick_from_millisecond(BLE_APP_BOND_STATUS_DELAY), RT_TIMER_FLAG_ONE_SHOT);
    }
    // env->time_handle  = rt_timer_create("app", app_timeout_handler,  NULL,
    //                                     rt_tick_from_millisecond(BLE_APP_TIMEOUT_INTERVAL), RT_TIMER_FLAG_SOFT_TIMER);
    g_alipay_message = rt_mq_create("g_alipay_message", sizeof(g_alipay_data), 20, RT_IPC_FLAG_FIFO);
    extern int ble_csi_init(void);
    ble_csi_init();
    while (1)
    {
        uint32_t value;
        int ret;
        rt_mb_recv(env->mb_handle, (rt_uint32_t *)&value, RT_WAITING_FOREVER);
        if (value == BLE_POWER_ON_IND)
        {
            env->is_power_on = 1;
            env->conn_para[0].mtu = BLE_MTU_MAX; /* Default value. */
            env->conn_para[1].mtu = BLE_MTU_MAX; /* Default value. */
            ble_app_service_init();
            ble_lb55x_hr_s_init();
            // ble_app_update_conn_param(env->conn_idx, BLE_INTV_MAX, BLE_INTV_MIN, BLE_CONNECTION_TIMEOUT);
            /* First enable connectable adv then enable non-connectable. */
            connection_manager_set_bond_cnf_iocap(GAP_IO_CAP_DISPLAY_YES_NO);
            connection_manager_set_bond_ack(BOND_PENDING);
            ble_app_bond_device_get(g_ble_app_bonded_addr, sizeof(g_ble_app_bonded_addr));
            ble_app_advertising_init();
            ble_app_advertising_start();
            BLE_APP_LOG_I("receive BLE power on!\r\n");
        }
        else if (value == BLE_USER_EVENT_BOND_CANCEL)
        {
            uint8_t l_addr[BD_ADDR_LEN] = {0};
            if (!memcmp(g_ble_app_bonded_addr, l_addr, BD_ADDR_LEN))
            {
                // connection_manager_delete_bond_highest_priority();
            }
            else
            {
                ble_app_bond_resume();
            }
            ble_app_disconnect_all();
        }
        else if (value == BLE_USER_EVENT_WAIT_ID_TIMEOUT)
        {
            if (g_device_get_app_request_bind_type() == BLE_APP_REQUEST_BIND_QR_CODE)
            {
                static char *p_push_to_page = BLE_APP_PAGE_QRCODE_PAIRING;
                //添加模式下接受配对但未收到user id ，断连并恢复绑定信息
                connection_manager_delete_bond_highest_priority();
                ble_app_bond_resume();
                ble_app_disconnect_all();

                g_device_set_app_request_bind_type(BLE_APP_REQUEST_BIND_TYPE_NONE);
                submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_BIND_WATCH_FAILED, (void *)p_push_to_page);//绑定失败
            }
            else if (g_device_get_app_request_bind_type() == BLE_APP_REQUEST_BIND_OTHER)
            {
                //正常工作模式下接受配对但未收到user id ，仅恢复绑定信息
                ble_app_bond_resume();
            }
            g_device_set_app_request_bind_type(BLE_APP_REQUEST_BIND_TYPE_NONE);
        }
        else if (value == BLE_USER_EVENT_UNBOND)
        {
            // connection_manager_delete_bond_highest_priority();
            connection_manager_delete_all_bond();
            memset(g_ble_app_bonded_addr, 0, sizeof(g_ble_app_bonded_addr));
            ble_app_disconnect_all();
            if (is_pairing_complete)
            {
                is_pairing_complete = false;
            }
            // set_bond_infor_to_flash();
        }
        // rt_thread_mdelay(1000*10);
    }
}

int ble_app_init(void)
{
    rt_thread_t tid;
    // app_env_t *env = ble_app_get_env();
    // sifli_ble_enable();
    // env->mb_handle = rt_mb_create("ble_app", 8, RT_IPC_FLAG_FIFO);
    tid = rt_thread_create("ble_app", ble_app_entry, NULL, 2048, RT_THREAD_PRIORITY_MIDDLE, RT_THREAD_TICK_DEFAULT);
    rt_thread_startup(tid);
    return RT_EOK;
}
// INIT_APP_EXPORT(ble_app_init);

static bool ble_app_adv_filter(app_env_t *env, ble_gap_ext_adv_report_ind_t *ind)
{
    uint8_t ret = false;
    uint32_t i;
    for (i = 0; i < env->adv_count; i++)
    {
        if (memcmp(&env->adv_addr[i].addr.addr, &ind->addr.addr.addr, sizeof(uint8_t) * BD_ADDR_LEN) == 0 &&
                env->adv_addr[i].addr_type == ind->addr.addr_type && env->adv_info[i] == ind->info)
        {
            ret = true;
            break;
        }
    }
    return ret;
}

static void ble_app_adv_add(app_env_t *env, ble_gap_ext_adv_report_ind_t *ind)
{
    memcpy(&env->adv_addr[env->adv_count], &ind->addr, sizeof(ble_gap_addr_t));
    env->adv_info[env->adv_count] = ind->info;
    env->adv_count++;
}

static void ble_app_display_adv_context(ble_gap_ext_adv_report_ind_t *ind, uint8_t adv_count)
{
    uint8_t addr_str_type = ind->addr.addr_type;
    // char *adv_info_str = malloc(40);
    char adv_info_str[40] = {0};
    if (addr_str_type == 0xFE)
        addr_str_type = 0x04;
    else if (addr_str_type == 0xFF)
        addr_str_type = 0x05;

    BLE_APP_LOG_I("advertising device %2d: addr type: %s, addr: %x-%x-%x-%x-%x-%x", adv_count, s_ble_app_addr_type[addr_str_type], ind->addr.addr.addr[0],
          ind->addr.addr.addr[1], ind->addr.addr.addr[2], ind->addr.addr.addr[3], ind->addr.addr.addr[4],
          ind->addr.addr.addr[5]);
    {
        uint8_t is_conn = ((ind->info & GAPM_REPORT_INFO_CONN_ADV_BIT) != 0) ? 0 : 3;
        uint8_t is_scan = ((ind->info & GAPM_REPORT_INFO_SCAN_ADV_BIT) != 0) ? 1 : 3;
        uint8_t is_dir = ((ind->info & GAPM_REPORT_INFO_DIR_ADV_BIT) != 0) ? 2 : 3;
        rt_snprintf(adv_info_str, 40, "%s %s %s adv", s_ble_app_info_type[is_conn],
                    s_ble_app_info_type[is_scan],
                    s_ble_app_info_type[is_dir]);
    }
    BLE_APP_LOG_I("adv type: %s, adv info: %s", s_ble_app_report_type[ind->info & GAPM_REPORT_INFO_REPORT_TYPE_MASK], adv_info_str);

    BLE_APP_LOG_I("adv tx pwr: %d, rssi: %d", ind->tx_pwr, ind->rssi);
    rt_hexdump("adv_data", 16, ind->data, ind->length);

    BLE_APP_LOG_I("\r\n");
}

static void ble_app_display_connected_device(app_env_t *env)
{
    BLE_APP_LOG_I("Total connected %d devices", env->conn_count);
    for (uint32_t i = 0; i < BLE_APP_MAX_CONN_COUNT; i++)
    {
        if (env->conn[i].conn_idx == 0xFF)
            continue;

        BLE_APP_LOG_I("Device %d: role:%d, address(type %d): %x-%x-%x-%x-%x-%x", i, env->conn[i].role, env->conn[i].peer_addr_type,
              env->conn[i].peer_addr.addr[0],
              env->conn[i].peer_addr.addr[1],
              env->conn[i].peer_addr.addr[2],
              env->conn[i].peer_addr.addr[3],
              env->conn[i].peer_addr.addr[4],
              env->conn[i].peer_addr.addr[5]);
        BLE_APP_LOG_I("MTU: %d, connection interval %d", env->conn[i].mtu, env->conn[i].conn_interval * 5 / 4);
        BLE_APP_LOG_I("");
    }
}

static void ble_app_device_connected(app_env_t *env, connection_manager_connect_ind_t *ind)
{
    uint32_t i;
    for (i = 0; i < BLE_APP_MAX_CONN_COUNT; i++)
        if (env->conn[i].conn_idx == 0xFF)
            break;

    if (i == BLE_APP_MAX_CONN_COUNT)
        RT_ASSERT(0);

    env->conn_count++;
    env->conn[i].conn_idx = ind->conn_idx;
    env->conn[i].conn_interval = ind->con_interval;
    env->conn[i].peer_addr_type = ind->peer_addr_type;
    env->conn[i].peer_addr = ind->peer_addr;
    env->conn[i].role = ind->role;
    env->conn[i].mtu = 23;

    BLE_APP_LOG_I("Peer device(role:%d) (%x-%x-%x-%x-%x-%x) connected as deivce %d", ind->role, env->conn[i].peer_addr.addr[5],
          env->conn[i].peer_addr.addr[4],
          env->conn[i].peer_addr.addr[3],
          env->conn[i].peer_addr.addr[2],
          env->conn[i].peer_addr.addr[1],
          env->conn[i].peer_addr.addr[0],
          i);
}

static void ble_app_deivce_disconnected(app_env_t *env, uint8_t idx, uint8_t reason)
{
    RT_ASSERT((idx < BLE_APP_MAX_CONN_COUNT) && (env->conn[idx].conn_idx != 0xFF));
    if (idx < BLE_APP_MAX_CONN_COUNT)
    {
        env->conn[idx].conn_idx = 0xFF;
        env->conn_count--;

        BLE_APP_LOG_I("Device %d (%x-%x-%x-%x-%x-%x) disconnected(reason %d)", idx, env->conn[idx].peer_addr.addr[5],
          env->conn[idx].peer_addr.addr[4],
          env->conn[idx].peer_addr.addr[3],
          env->conn[idx].peer_addr.addr[2],
          env->conn[idx].peer_addr.addr[1],
          env->conn[idx].peer_addr.addr[0],
          reason);
    }
}

static uint8_t ble_app_get_dev_by_idx(app_env_t *env, uint8_t conn_idx)
{
    uint32_t i;
    for (i = 0; i < BLE_APP_MAX_CONN_COUNT; i++)
        if (env->conn[i].conn_idx == conn_idx)
            break;

    return i == BLE_APP_MAX_CONN_COUNT ? 0xFF : (uint8_t)i;
}

static void ble_app_display_uuid(ble_app_uuid_display_type_t type, uint8_t uuid_len, uint8_t *uuid)
{
    if (type >= BLE_APP_UUID_TYPE_TOTAL)
        return;

    BLE_APP_LOG_I("%s UUID(%d bit):", s_ble_app_uuid_type[type], uuid_len * 8);
    switch (uuid_len)
    {
    case 2:
    {
        BLE_APP_LOG_I("%s UUID: 0x%02x%02x", s_ble_app_uuid_type[type], uuid[1], uuid[0]);
    }
    break;
    case 4:
    {
        BLE_APP_LOG_I("%s UUID: 0x%02x%02x", s_ble_app_uuid_type[type], uuid[3], uuid[2], uuid[1], uuid[0]);
    }
    break;
    case 16:
    {
        BLE_APP_LOG_I("%s UUID: 0x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x", s_ble_app_uuid_type[type], uuid[15], uuid[14], uuid[13], uuid[12], uuid[11],
              uuid[10], uuid[9], uuid[8], uuid[7], uuid[6],
              uuid[5], uuid[4], uuid[3], uuid[2], uuid[1],
              uuid[0]);
    }
    break;
    default:
    {
        ASSERT(0 && "Invalid service uuid length");
    }
    break;
    }
}


static void ble_app_display_service(sibles_svc_remote_svc_t *svc)
{
    if (svc == NULL || svc->att_db == NULL)
    {
        BLE_APP_LOG_W("Displayed wrongly service.");
        return;
    }

    ble_app_display_uuid(BLE_APP_UUID_TYPE_SERVICE, svc->uuid_len, svc->uuid);

    uint32_t i, t;
    uint16_t offset = 0;
    uint8_t desc_num;
    sibles_svc_search_char_t *chara = (sibles_svc_search_char_t *)svc->att_db;
    for (i = 0; i < svc->char_count; i++)
    {
        BLE_APP_LOG_I("    Charateristic(%d) handle %d, value handle %d", i, chara->attr_hdl, chara->pointer_hdl, chara->prop);
        ble_app_display_uuid(BLE_APP_UUID_TYPE_CHARATER, chara->uuid_len, chara->uuid);
        BLE_APP_LOG_I("    Properties: %s %s %s %s %s %s %s %s", s_ble_app_chara_prop[BLE_APP_BIT_CONVERT_DIGIT_INC(chara->prop, 0)],
              s_ble_app_chara_prop[BLE_APP_BIT_CONVERT_DIGIT_INC(chara->prop, 1)],
              s_ble_app_chara_prop[BLE_APP_BIT_CONVERT_DIGIT_INC(chara->prop, 2)],
              s_ble_app_chara_prop[BLE_APP_BIT_CONVERT_DIGIT_INC(chara->prop, 3)],
              s_ble_app_chara_prop[BLE_APP_BIT_CONVERT_DIGIT_INC(chara->prop, 4)],
              s_ble_app_chara_prop[BLE_APP_BIT_CONVERT_DIGIT_INC(chara->prop, 5)],
              s_ble_app_chara_prop[BLE_APP_BIT_CONVERT_DIGIT_INC(chara->prop, 6)],
              s_ble_app_chara_prop[BLE_APP_BIT_CONVERT_DIGIT_INC(chara->prop, 7)]);

        for (t = 0; t < chara->desc_count; t++)
        {
            BLE_APP_LOG_I("        Descriptor(%d) handle %d", t, chara->desc[t].attr_hdl);
            ble_app_display_uuid(BLE_APP_UUID_TYPE_DESCRIPTOR, chara->desc[t].uuid_len, chara->desc[t].uuid);
        }
        offset = sizeof(sibles_svc_search_char_t) + chara->desc_count * sizeof(struct sibles_disc_char_desc_ind);
        chara = (sibles_svc_search_char_t *)((uint8_t *)chara + offset);
    }

}


int ble_app_gattc_event_handler(uint16_t event_id, uint8_t *data, uint16_t len)
{
    app_env_t *env = ble_app_get_env();
    BLE_APP_LOG_I("app gattc event handler %d\r\n", event_id);
    switch (event_id)
    {
    case SIBLES_REGISTER_REMOTE_SVC_RSP:
    {
        sibles_register_remote_svc_rsp_t *rsp = (sibles_register_remote_svc_rsp_t *)data;
        BLE_APP_LOG_I("client register ret %d\r\n", rsp->status);
        break;
    }
    case SIBLES_REMOTE_EVENT_IND:
    {
        sibles_remote_event_ind_t *ind = (sibles_remote_event_ind_t *)data;
        BLE_APP_LOG_I("Notify(handle %d) received", ind->handle);
        rt_hexdump("Notify content", 16, ind->value, ind->length);
        // Notify upper layer
        break;
    }
    case SIBLES_READ_REMOTE_VALUE_RSP:
    {
        sibles_read_remote_value_rsp_t *rsp = (sibles_read_remote_value_rsp_t *)data;
        BLE_APP_LOG_I("Read(handle %d) received", rsp->handle);
        rt_hexdump("Read content", 16, rsp->value, rsp->length);
        break;
    }
    default:
        break;
    }
    return 0;
}


void ble_app_update_conn_param(uint8_t conn_idx, uint16_t inv_max, uint16_t inv_min, uint16_t timeout)
{
    ble_gap_update_conn_param_t conn_para;
    conn_para.conn_idx = conn_idx;
    conn_para.intv_max = inv_max;
    conn_para.intv_min = inv_min;
    /* value = argv * 1.25 */
    conn_para.ce_len_max = 48;//0x100;
    conn_para.ce_len_min = 0;//0x1;
    conn_para.latency = 0;
    conn_para.time_out = timeout;
    ble_gap_update_conn_param(&conn_para);
}

int ble_app_event_handler(uint16_t event_id, uint8_t *data, uint16_t len, uint32_t context)
{
    app_env_t *env = ble_app_get_env();
    // BLE_APP_LOG_I("[ble_app_event_handler] event_id %d!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\r\n", event_id);
    static uint16_t attr_mask = 0x00;
    switch (event_id)
    {
    case BLE_POWER_ON_IND:
    {
        /* Handle in own thread to avoid conflict */
        if (env->mb_handle)
            rt_mb_send(env->mb_handle, BLE_POWER_ON_IND);
        break;
    }
    case BLE_GAP_CONNECTED_IND:
    {
        ble_gap_connect_ind_t *ind = (ble_gap_connect_ind_t *)data;
        uint8_t idx = ble_app_conn_idx_set(ind->conn_idx);
        if (idx >= BLE_APP_CONN_NUM_MAX)
        {
            BLE_APP_LOG_E("[BLE_GAP_CONNECTED_IND] num = %d, Exceed maximum link number(%d)!", idx, BLE_APP_CONN_NUM_MAX);
            break;
        }
        else
        {
            ble_hrs_conn_idx_set(env->conn_idx[idx]);
        }
        env->conn_para[idx].conn_interval = ind->con_interval;
        env->conn_para[idx].peer_addr_type = ind->peer_addr_type;
        env->conn_para[idx].peer_addr = ind->peer_addr;
#ifndef CONFIG_PPG_CAPTURE
        if (ind->role == DEVICE_ROLE_SLAVER)
        {
            BLE_APP_LOG_I("Peripheral should be slave!!!");
            sibles_exchange_mtu(ind->conn_idx);
        }
#else   //兼容汇顶采集APK.
        if (ind->role == DEVICE_ROLE_MASTER)
        {
            BLE_APP_LOG_I("Peripheral should be slave In Goodix!!!");
        }
#endif  /*CONFIG_PPG_CAPTURE*/

        BLE_APP_LOG_I("Peer device(%x-%x-%x-%x-%x-%x) connected", env->conn_para[idx].peer_addr.addr[5],
              env->conn_para[idx].peer_addr.addr[4],
              env->conn_para[idx].peer_addr.addr[3],
              env->conn_para[idx].peer_addr.addr[2],
              env->conn_para[idx].peer_addr.addr[1],
              env->conn_para[idx].peer_addr.addr[0]);

        g_device_set_ble_connect_status(BLE_STA_CONNECT);
        ble_connect_state_notify(BLE_CONNECT_STATE_CONNECTED);

        BLE_APP_LOG_I("is_hrm_tx:%d\r\n", is_hrm_tx);
        // 心率推送期间做的额外处理
        if (is_hrm_tx)
        {
            // 判断连接数量，决定广播策略
            uint8_t conn_num = 0;
            for (uint8_t i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
            {

                if (env->conn_idx[i] != BLE_APP_CONN_IDX_INVALID)
                {
                    conn_num++;
                }
            }

            BLE_APP_LOG_I("conn_num:%d\r\n", conn_num);
            if (conn_num == 1)
            {
                // 只有一个连接，重新开启广播
                ble_app_advertising_start();
            }
        }
        break;
    }
    // case BLE_GAP_CREATE_CONNECTION_CNF:
    // {
    //     ble_gap_create_connection_cnf_t *cnf = (ble_gap_create_connection_cnf_t *)data;
    //     if (cnf->status != HL_ERR_NO_ERROR)
    //         BLE_APP_LOG_E("Create connection failed %d!", cnf->status);
    //     break;
    // }
    // case BLE_GAP_CANCEL_CREATE_CONNECTION_CNF:
    // {
    //     ble_gap_create_connection_cnf_t *cnf = (ble_gap_create_connection_cnf_t *)data;
    //     BLE_APP_LOG_I("Create connection cancel status: %d", cnf->status);
    //     break;
    // }
    // case CONNECTION_MANAGER_CONNCTED_IND:
    // {
    //     connection_manager_connect_ind_t *ind = (connection_manager_connect_ind_t *)data;
    //     if (env->conn_count == BLE_APP_MAX_CONN_COUNT)
    //     {
    //         BLE_APP_LOG_E("Exceed maximum link number(%d)!", BLE_APP_MAX_CONN_COUNT);
    //         ble_gap_disconnect_t dis_conn;
    //         dis_conn.conn_idx = ind->conn_idx;
    //         dis_conn.reason = CO_ERROR_REMOTE_USER_TERM_CON;
    //         ble_gap_disconnect(&dis_conn);
    //         break;
    //     }

    //     ble_app_device_connected(env, ind);
    //     sibles_exchange_mtu(ind->conn_idx);
    //     // if (ind->role == 0)
    //     //   connection_manager_create_bond(ind->conn_idx);
    //     break;
    // }
    case BLE_GAP_UPDATE_CONN_PARAM_IND:
    {
        ble_gap_update_conn_param_ind_t *ind = (ble_gap_update_conn_param_ind_t *)data;
        uint8_t idx = ble_app_conn_idx_check(ind->conn_idx);
        if (idx >= BLE_APP_CONN_NUM_MAX)
        {
            BLE_APP_LOG_E("[BLE_GAP_UPDATE_CONN_PARAM_IND] num = %d, Exceed maximum link number(%d)!", idx, BLE_APP_CONN_NUM_MAX);
        }
        else
        {
            env->conn_para[idx].conn_interval = ind->con_interval;
            BLE_APP_LOG_I("Updated connection interval :%d", ind->con_interval);
#ifdef CONFIG_PPG_BLE_FACTORY_TEST
            if (env->conn_para[idx].conn_interval > BLE_INTV_MIN)
            {
                ble_app_update_conn_param(idx, BLE_INTV_MIN, BLE_INTV_MIN, BLE_CONNECTION_TIMEOUT);
            }
#endif
        }
        break;
    }
    case SIBLES_MTU_EXCHANGE_IND:
    {
        /* Negotiated MTU. */
        sibles_mtu_exchange_ind_t *ind = (sibles_mtu_exchange_ind_t *)data;
        uint8_t idx = ble_app_conn_idx_check(ind->conn_idx);
        if (idx >= BLE_APP_CONN_NUM_MAX)
        {
            BLE_APP_LOG_E("[SIBLES_MTU_EXCHANGE_IND] num = %d, Exceed maximum link number(%d)!", idx, BLE_APP_CONN_NUM_MAX);
        }
        else
        {
            env->conn_para[idx].mtu = ind->mtu;
        }
        extern uint8_t dynamic_mtu_size;
        dynamic_mtu_size = ind->mtu - 3;
        BLE_APP_LOG_I("Exchanged MTU size: %d", ind->mtu);
        break;
    }
    case BLE_GAP_DISCONNECTED_IND:
    {
        ble_gap_disconnected_ind_t *ind = (ble_gap_disconnected_ind_t *)data;
        bool conn_state = false;
        ble_hrs_conn_idx_clear(ind->conn_idx);
        ble_app_conn_idx_clear(ind->conn_idx);
        ble_app_device_name_update(false, ind->conn_idx);
        BLE_APP_LOG_I("BLE_GAP_DISCONNECTED_IND(%d)", ind->reason);
        ble_connect_state_notify(BLE_CONNECT_STATE_DISCONNECTED);
        lb55x_ble_nus_channel_status_clear_all();
        if (wait_user_id_timer)
        {
            if (wait_user_id_timer->parent.flag & RT_TIMER_FLAG_ACTIVATED)
            {
                ble_app_wait_user_id_timer_stop(false);
            }
        }
        for (uint8_t i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
        {
            if (env->conn_idx[i] != BLE_APP_CONN_IDX_INVALID)
            {
                conn_state = true;
                break;
            }
        }
        if (conn_state == false)
        {
            if (g_device_get_ble_bondstatus())
            {
                g_device_set_ble_connect_status(BLE_STA_SEARCH);
            }
            else
            {
                g_device_set_ble_connect_status(BLE_STA_CLOSE);
            }
            // if (is_pairing_complete)
            // {
            //     is_pairing_complete = false;
            // }
        }

        // 心率推送期间做的额外处理
        if (is_hrm_tx)
        {
            // 判断连接数量，决定广播策略
            uint8_t conn_num = 0;
            for (uint8_t i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
            {
                if (env->conn_idx[i] != BLE_APP_CONN_IDX_INVALID)
                {
                    conn_num++;
                }
            }

            if (conn_num == 1)
            {
                // 只有一个连接，重新开启广播
                ble_app_advertising_start();
            }
        }
        break;
    }

    case CONNECTION_MANAGER_BOND_AUTH_INFOR:
    {
        connection_manager_bond_ack_infor_t *info = (connection_manager_bond_ack_infor_t *)data;
        if (info->request == GAPC_PAIRING_REQ)
        {
            // 对端发起配对的事件， 可以做弹窗或者其它处理，最后要调用下面的ack_reply
            connection_manager_bond_ack_reply(info->conn_idx, GAPC_PAIRING_REQ, true);
        }
        else if (info->request == GAPC_TK_EXCH)
        {
            // 显示pin_code
            BLE_APP_LOG_I("pin_code: %d\r\n", info->confirm_data);
            connection_manager_bond_ack_reply(info->conn_idx, GAPC_TK_EXCH, true);
        }
        else if (info->request == GAPC_NC_EXCH)
        {
            uint32_t nc_data = info->confirm_data;
            BLE_APP_LOG_I("NC_EXCH: %d", nc_data);
            // 显示NC_EXCH
            ble_app_peer_conn_idx = info->conn_idx;
            // ble_app_display_peer_data(nc_data);
            //设置绑定类型,如果绑定类型未设置，则设置为其他配对绑定
            BLE_APP_LOG_I("%s %d: bind type: %d", __func__, __LINE__, g_device_get_app_request_bind_type());
            ble_app_pairing_start(nc_data);//发起蓝牙配对
            // connection_manager_bond_ack_reply(info->conn_idx, GAPC_NC_EXCH, true);
        }
        break;
    }

    case CONNECTION_MANAGER_PAIRING_FAILED:
    {
        if(g_device_get_app_request_bind_type() != BLE_APP_REQUEST_BIND_TYPE_NONE)
        {
            static char *p_push_to_page = BLE_APP_PAGE_QRCODE_PAIRING;
            g_device_set_app_request_bind_type(BLE_APP_REQUEST_BIND_TYPE_NONE);
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_BIND_WATCH_FAILED, (void *)p_push_to_page);//绑定失败
        }
        else
        {
            static char *p_push_to_page = "Launcher";
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_BIND_WATCH_FAILED, (void *)p_push_to_page);//配对失败
        }
        break;
    }

    case CONNECTION_MANAGER_PAIRING_SUCCEED:
    {
        static char *p_push_to_page = "Launcher";
        is_pairing_complete = true;
        if (g_device_get_app_request_bind_type() == BLE_APP_REQUEST_BIND_OTHER)
        {
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_BIND_WATCH_SUCC, (void *)p_push_to_page);//绑定成功
        }
        if (wait_user_id_timer)
        {
            rt_timer_start(wait_user_id_timer);
        }
        break;
    }

    case BLE_GAP_BOND_IND:
    {
        // lb55x_ble_periph_peer_success_evt_handler();
        // if(!g_device_get_ble_bondstatus())
        // {
        //     g_device_set_ble_bondstatus(true);
        // }
        // set_connect_on_bond_flag(true);
        ble_gap_bond_ind_t *evt = (ble_gap_bond_ind_t *)data;
        if (evt->info == GAPC_PAIRING_SUCCEED)
        {
            lb55x_ble_periph_peer_success_evt_handler();
            if(!g_device_get_ble_bondstatus())
            {
                g_device_set_ble_bondstatus(true);
            }
            set_connect_on_bond_flag(true);

            ble_ancs_attr_enable(BLE_ANCS_NOTIFICATION_ATTR_ID_APP_ID, 1, BLE_ANCS_ATTR_DATA_MAX);
            ble_ancs_app_enable(BLE_ANCS_APP_ATTR_ID_DISPLAY_NAME, 1, sizeof(g_attr_disp_name));
            ble_ancs_attr_enable(BLE_ANCS_NOTIFICATION_ATTR_ID_TITLE, 1, BLE_ANCS_ATTR_DATA_MAX);
            ble_ancs_attr_enable(BLE_ANCS_NOTIFICATION_ATTR_ID_MESSAGE, 1, BLE_ANCS_ATTR_MESSAGE_MAX);
            ble_ancs_attr_enable(BLE_ANCS_NOTIFICATION_ATTR_ID_DATE, 1, BLE_ANCS_ATTR_DATA_MAX);

            ble_ancs_category_mask_set(BLE_ANCS_CATEGORY_ID_MASK_ALL);
            ble_ancs_cccd_enable(1);
            ble_ancs_enable(evt->conn_idx);
        }
        else if (evt->info == GAPC_PAIRING_FAILED)
        {
            BLE_APP_LOG_W("Pairing failed");
            ble_gap_disconnect_t dis_conn;
            dis_conn.conn_idx = evt->conn_idx;
            dis_conn.reason = CO_ERROR_REMOTE_USER_TERM_CON;
            ble_gap_disconnect(&dis_conn);
        }
        break;
    }

    case BLE_GAP_ENCRYPT_IND:
    {
        // BLE_APP_LOG_I("[ble_app_event_handler] BLE_GAP_ENCRYPT_IND \r\n");
        ble_gap_encrypt_ind_t *ind = (ble_gap_encrypt_ind_t *)data;

        BLE_APP_LOG_I("encryt ind %d", ind->conn_idx);

        ble_ancs_attr_enable(BLE_ANCS_NOTIFICATION_ATTR_ID_APP_ID, 1, BLE_ANCS_ATTR_DATA_MAX);
        ble_ancs_app_enable(BLE_ANCS_APP_ATTR_ID_DISPLAY_NAME, 1, sizeof(g_attr_disp_name));
        ble_ancs_attr_enable(BLE_ANCS_NOTIFICATION_ATTR_ID_TITLE, 1, BLE_ANCS_ATTR_DATA_MAX);
        ble_ancs_attr_enable(BLE_ANCS_NOTIFICATION_ATTR_ID_MESSAGE, 1, BLE_ANCS_ATTR_MESSAGE_MAX);
        ble_ancs_attr_enable(BLE_ANCS_NOTIFICATION_ATTR_ID_DATE, 1, BLE_ANCS_ATTR_DATA_MAX);

        ble_ancs_category_mask_set(BLE_ANCS_CATEGORY_ID_MASK_ALL);
        ble_ancs_cccd_enable(1);
        ble_ancs_enable(ind->conn_idx);
        break;
    }

    case BLE_ANCS_NOTIFICATION_IND:
    {
        // BLE_APP_LOG_I("[ble_app_event_handler] BLE_ANCS_NOTIFICATION_IND !!!!!!!!!!!!!!!!\r\n");
        ble_ancs_noti_attr_t *notify = (ble_ancs_noti_attr_t *)data;
        ble_ancs_attr_value_t *ancs_value = notify->value;
        BLE_APP_LOG_I("received_notify %d, attr_count %d", notify->evt_id, notify->attr_count);
        BLE_APP_LOG_I("[BLE_ANCS_NOTIFICATION_IND]  notify->cate_id %d, notify->evt_id %d, notify->noti_uid %d\r\n", notify->cate_id, notify->evt_id, notify->noti_uid);
        BLE_APP_LOG_I("[BLE_ANCS_NOTIFICATION_IND]  notify->value->attr_id %d, notify->value->data %s, notify->value->len %d\r\n", notify->value->attr_id, notify->value->data, notify->value->len);
        // for(uint8_t i = 0; i < notify->attr_count; i++)
        // {
        //     if(ancs_value->len == 0)
        //     {
        //         ancs_value = (ble_ancs_attr_value_t *)((uint8_t *)ancs_value + sizeof(ble_ancs_attr_value_t));
        //         continue;
        //     }
        //     BLE_APP_LOG_I("[ancs_value] idx = %d, attr_id = %d\r\n", i, ancs_value->attr_id);
        //     rt_hexdump("ancs_value->data", 20, ancs_value->data, ancs_value->len);
        //     BLE_APP_LOG_I("\r\n");
        //     ancs_value = (ble_ancs_attr_value_t *)((uint8_t *)ancs_value + sizeof(ble_ancs_attr_value_t) + ancs_value->len);
        // }
        // ancs_value = notify->value;

        if(notify->evt_flag & BLE_ANCS_EVENT_FLAG_PRE_EXISTING)
        {
            BLE_APP_LOG_I("pre-existing notification, ignore it!!!\r\n");
            break;
        }

        for (uint8_t i = 0; i < ANCS_FILTER_CATEGORY_ID_NUM; i++)
        {
            if (g_ancs_filter_category_id[i] == notify->cate_id)
            {
                if(ancs_catagory_is_filter((ANCS_CATAGORY_ID)g_ancs_filter_category_id[i]))
                {
                    uint16_t ins_index = lb55x_ins_data_index_get();

                    if(BLE_ANCS_EVENT_ID_NOTIFICATION_REMOVED == notify->evt_id)
                    {
                        if(BLE_ANCS_CATEGORY_ID_INCOMING_CALL == notify->cate_id)
                        {
                            lb55x_ins_data_update(ins_index, notify->cate_id, notify->evt_id, notify->noti_uid);
                            attr_mask = 0;

                            lb55x_ancs_deal_interface(ancs_value->data, ancs_value->len);
                        }
                        break;
                    }
                    else
                    {
                        lb55x_ins_data_update(ins_index, notify->cate_id, notify->evt_id, notify->noti_uid);
                        attr_mask = 0;

                        // TODO: ...
                        // lb55x_ancs_deal_interface(notify->value->data);
                    }

                    if (ancs_value->attr_id == BLE_ANCS_NOTIFICATION_ATTR_ID_APP_ID)
                    {
                        #if ANCS_FILTER_APP_ENABLED

                        #endif
                        // rt_kprintf("strcmp : data: %s, len: %d\r\n", ancs_value->data, ancs_value->len);
                        if(strstr((char *)(ancs_value->data), ANCS_FILTER_APP_IDENTIFIER_SMS))
                        {
                            lb55x_ins_data_msg_type_set(ENUM_MSG_NOTE_TYPE);
                        }
                        else if(strstr((char *)(ancs_value->data), ANCS_FILTER_APP_IDENTIFIER_MOBILEPHONE))
                        {
                            lb55x_ins_data_msg_type_set(ENUM_MSG_INCOMING_TYPE);
                        }
                        else
                        {
                            lb55x_ins_data_msg_type_set(ENUM_MSG_APP_TYPE);
                        }

                        g_notif_attr_app_id_latest = *(ble_ancs_attr_value_t *)(ancs_value);
                    }

                    for(uint8_t i = 0; i < notify->attr_count; i++)
                    {
                        if(ancs_value->len == 0)
                        {
                            ancs_value = (ble_ancs_attr_value_t *)((uint8_t *)ancs_value + sizeof(ble_ancs_attr_value_t));
                            continue;
                        }
                        switch (ancs_value->attr_id)
                        {
                            case BLE_ANCS_NOTIFICATION_ATTR_ID_APP_ID:
                                attr_mask |= BLE_ANCS_NOTIF_ATTR_ID_APP_IDENTIFIER_MASK;
                                break;

                            case BLE_ANCS_NOTIFICATION_ATTR_ID_TITLE:
                                attr_mask |= BLE_ANCS_NOTIF_ATTR_ID_TITLE_MASK;
                                lb55x_ins_data_attr_update(BLE_ANCS_NOTIF_ATTR_ID_TITLE_MASK, ancs_value->data, ancs_value->len);
                                break;

                            case BLE_ANCS_NOTIFICATION_ATTR_ID_MESSAGE:
                                attr_mask |= BLE_ANCS_NOTIF_ATTR_ID_MESSAGE_MASK;
                                lb55x_ins_data_attr_update(BLE_ANCS_NOTIF_ATTR_ID_MESSAGE_MASK, ancs_value->data, ancs_value->len);
                                break;

                            case BLE_ANCS_NOTIFICATION_ATTR_ID_DATE:
                                attr_mask |= BLE_ANCS_NOTIF_ATTR_ID_DATE_MASK;
                                lb55x_ins_data_attr_update(BLE_ANCS_NOTIF_ATTR_ID_DATE_MASK, ancs_value->data, ancs_value->len);
                                break;

                            default:
                                break;
                        }

                        // lb55x_ancs_deal_interface(notify->value->data, notify->value->len);

                        ancs_value = (ble_ancs_attr_value_t *)((uint8_t *)ancs_value + sizeof(ble_ancs_attr_value_t) + ancs_value->len);
                    }
                    if(g_notif_attr_app_id_latest.len != 0 && BLE_ANCS_NOTIF_ATTR_MASK == attr_mask)
                    {
                        //TODO: g_timer_tick = 0;
                    }
                    lb55x_ancs_deal_interface(notify->value->data, notify->value->len);
                }
            }
        }
        // if(BLE_ANCS_EVENT_ID_NOTIFICATION_REMOVED != notify->evt_id)
        // {
        //     lb55x_ancs_deal_interface(notify->value->data, notify->value->len);
        // }
        break;
    }

    case BLE_ANCS_GET_APP_ATTR_RSP:
    {
        BLE_APP_LOG_I("[ble_app_event_handler] BLE_ANCS_GET_APP_ATTR_RSP\r\n");
        break;
    }

    case SIBLES_WRITE_VALUE_RSP:
    {
        sibles_write_value_rsp_t *rsp = (sibles_write_value_rsp_t *)data;
        //BLE_APP_LOG_I("SIBLES_WRITE_VALUE_RSP %d\r\n", rsp->result);

        lb55x_ble_data_continuous_idletime_update();
        ble_data_deal_notify();
        break;
    }
    // case BLE_GAP_SCAN_START_CNF:
    // {
    //     ble_gap_start_scan_cnf_t *cnf = (ble_gap_start_scan_cnf_t *)data;
    //     BLE_APP_LOG_I("Scan start status %d", cnf->status);
    //     env->adv_count = 0;
    //     break;
    // }
    // case BLE_GAP_SCAN_STOPPED_IND:
    // {
    //     ble_gap_scan_stopped_ind_t *ind = (ble_gap_scan_stopped_ind_t *)data;
    //     BLE_APP_LOG_I("Scan stopped %d", ind->reason);
    //     break;
    // }
    // case BLE_GAP_EXT_ADV_REPORT_IND:
    // {
    //     ble_gap_ext_adv_report_ind_t *ind = (ble_gap_ext_adv_report_ind_t *)data;
    //     if (ble_app_adv_filter(env, ind) == true)
    //         break;

    //     if (ind->rssi < env->scan_rssi)
    //         break;

    //     // if (env->adv_count == BLE_APP_MAX_ADV_COUNT)
    //     // {
    //     //     break;
    //     // }
    //     // ble_app_adv_add(env, ind);
    //     ble_app_display_adv_context(ind, env->adv_count);
    //     break;
    // }
    // case SIBLES_SEARCH_SVC_RSP:
    // {
    //     sibles_svc_search_rsp_t *rsp = (sibles_svc_search_rsp_t *)data;
    //     if (rsp->result != HL_ERR_NO_ERROR)
    //         break; // Do nothing

    //     uint8_t dev_idx = ble_app_get_dev_by_idx(env, rsp->conn_idx);
    //     if (dev_idx == 0xFF)
    //         break;

    //     memcpy(&env->conn[dev_idx].svc, rsp->svc, sizeof(sibles_svc_remote_svc_t));
    //     env->conn[dev_idx].svc.att_db = malloc(env->conn[dev_idx].svc.char_count *
    //                                            (sizeof(sibles_svc_search_char_t) + APP_MAX_DESC * sizeof(struct sibles_disc_char_desc_ind)));

    //     if (env->conn[dev_idx].svc.att_db == NULL)
    //         break;

    //     BLE_APP_LOG_I("Service searched");

    //     uint32_t i;
    //     uint16_t offset = 0;
    //     uint8_t desc_num;
    //     sibles_svc_search_char_t *chara = (sibles_svc_search_char_t *)rsp->svc->att_db;
    //     sibles_svc_search_char_t *curr_chara = (sibles_svc_search_char_t *)env->conn[dev_idx].svc.att_db;
    //     for (i = 0; i < rsp->svc->char_count; i++)
    //     {
    //         desc_num = chara->desc_count > APP_MAX_DESC ? APP_MAX_DESC : chara->desc_count;
    //         memcpy(curr_chara, chara, sizeof(sibles_svc_search_char_t) + desc_num * sizeof(struct sibles_disc_char_desc_ind));

    //         offset = sizeof(sibles_svc_search_char_t) + chara->desc_count * sizeof(struct sibles_disc_char_desc_ind);
    //         chara = (sibles_svc_search_char_t *)((uint8_t *)chara + offset);
    //         offset = sizeof(sibles_svc_search_char_t) + desc_num * sizeof(struct sibles_disc_char_desc_ind);
    //         curr_chara = (sibles_svc_search_char_t *)((uint8_t *)curr_chara + offset);
    //     }

    //     ble_app_display_service(&env->conn[dev_idx].svc);
    //     //register first
    //     env->conn[dev_idx].rmt_svc_hdl = sibles_register_remote_svc(rsp->conn_idx, rsp->svc->hdl_start, rsp->svc->hdl_end, ble_app_gattc_event_handler);
    //     // subscribe data src. then subscribe notfi src.
    //     break;
    // }
    default:
        break;
    }
    return 0;
}
BLE_EVENT_REGISTER(ble_app_event_handler, NULL);

/************************************************************************
 *@function:void ble_app_user_id_set_callback_register(ble_app_user_id_set_callback_t callback)
 *@brief:注册user id信息回调函数
 *@param: callback - 配对信息回调函数
 *@return:null
*************************************************************************/
void ble_app_user_id_set_callback_register(ble_app_user_id_set_callback_t callback)
{
    g_ble_app_user_id_set_callback = callback;
}

/************************************************************************
 *@function:void ble_app_user_id_set_callback_register(ble_app_user_id_set_callback_t callback)
 *@brief:注销user id信息回调函数
 *@param: null
 *@return:null
*************************************************************************/
void ble_app_user_id_set_callback_unregister(void)
{
    g_ble_app_user_id_set_callback = NULL;
}

/************************************************************************
 *@function:void ble_app_user_id_set(uint8_t *user_id, uint8_t len)
 *@brief:给应用层提供app下发的user id信息
 *@param: uint8_t *user_id - user id信息
 *@param: uint8_t len - user id信息长度
 *@return:null
*************************************************************************/
void ble_app_user_id_set(uint8_t *user_id, uint8_t len)
{
    if (g_ble_app_user_id_set_callback)
    {
        g_ble_app_user_id_set_callback(user_id, len);
    }
}

/************************************************************************
 *@function:void ble_app_peer_data_callback_register(ble_app_peer_data_callback_t callback)
 *@brief:注册配对信息回调函数
 *@param: callback - 配对信息回调函数
 *@return:null
*************************************************************************/
void ble_app_peer_data_callback_register(ble_app_peer_data_callback_t callback)
{
    g_ble_app_peer_data_callback = callback;
}

/************************************************************************
 *@function:void ble_app_peer_data_callback_unregister(void)
 *@brief:注销配对信息回调函数
 *@param: null
 *@return:null
*************************************************************************/
void ble_app_peer_data_callback_unregister(void)
{
    g_ble_app_peer_data_callback = NULL;
}

/************************************************************************
 *@function:void ble_app_display_peer_data(uint32_t passkey);
 *@brief:显示配对信息
 *@param: uint32_t passkey - 配对码
 *@return:null
*************************************************************************/
void ble_app_display_peer_data(uint32_t passkey)
{
    // memset(ble_app_peer_device_name, 0, sizeof(ble_app_peer_device_name));
    // uint8_t peer_device_name_len = ble_app_device_name_get(ble_app_peer_device_name);
    if (g_ble_app_peer_data_callback)
    {
        // if (peer_device_name_len > 0)
        // {
        //     g_ble_app_peer_data_callback(passkey, ble_app_peer_device_name, peer_device_name_len, UINT32_MAX);
        // }
        // else
        // {
        //     g_ble_app_peer_data_callback(passkey, NULL, 0, UINT32_MAX);
        // }
        g_ble_app_peer_data_callback(passkey);
    }
}

void ble_app_pairing_start(uint32_t pair_value)
{
    static uint32_t pair_value_static;
    pair_value_static = pair_value;
    submit_gui_event(GUI_EVT_BLE_PAIRING, 0, (void *)&pair_value_static);
}

/************************************************************************
 *@function:void ble_app_bond_ack_reply_set(bool reply)
 *@brief:设置是否接受配对
 *@param: bool reply true - 接受配对，false - 拒绝配对
 *@return:null
*************************************************************************/
void ble_app_bond_ack_reply_set(bool reply)
{
    connection_manager_bond_ack_reply(ble_app_peer_conn_idx, GAPC_NC_EXCH, reply);
}

/************************************************************************
 *@function:void ble_app_peer_state_callback_register(ble_app_peer_state_callback_t callback);
 *@brief:注册配对状态回调函数
 *@param: callback - 配对状态回调函数
 *@return:null
*************************************************************************/
void ble_app_peer_state_callback_register(ble_app_peer_state_callback_t callback)
{
    g_ble_app_peer_state_callback = callback;
}

/************************************************************************
 *@function:void void ble_app_peer_state_callback_unregister(void);
 *@brief:注销配对状态回调函数
 *@param: null
 *@return:null
*************************************************************************/
void ble_app_peer_state_callback_unregister(void)
{
    g_ble_app_peer_state_callback = NULL;
}

/************************************************************************
 *@function:void ble_app_display_peer_state(bool state);
 *@brief:显示配对状态
 *@param: bool state - 配对状态
 *@return:null
*************************************************************************/
void ble_app_display_peer_state(bool state)
{
    if (g_ble_app_peer_state_callback)
    {
        g_ble_app_peer_state_callback(state);
    }
}

/************************************************************************
 *@function:void ble_lb55x_peripheral_bond(void);
 *@brief:设置配对状态
 *@param: null
 *@return:null
*************************************************************************/
void ble_lb55x_peripheral_bond(void)
{
    app_env_t *env = ble_app_get_env();

    for (uint8_t i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
    {
        if (env->conn_idx[i] != BLE_APP_CONN_IDX_INVALID)
        {
            connection_manager_set_link_security(env->conn_idx[i], LE_SECURITY_LEVEL_SEC_CON_MITM_BOND);
        }
    }
}

/************************************************************************
 *@function:void ble_app_bonds_delete_reset_device(void);
 *@brief:解除配对删除配对信息
 *@param: null
 *@return:null
*************************************************************************/
void ble_app_bonds_delete_reset_device(void)
{
    BLE_APP_LOG_I("Erase bonds!");
    connection_manager_delete_all_bond();
}

#ifdef BLE_HID

static int ble_app_hid_consume_state_key_set_bit(uint8_t key)
{
    if ((ble_app_hid_consume_state.key_state & (1  << key)) == 0)
    {
        ble_app_hid_consume_state.key_state |= 1  << key;
        BLE_APP_LOG_I("key_state:0x%x, key:%d", ble_app_hid_consume_state.key_state, key);
        return 0;
    }
    return -EBUSY;
}

static int ble_app_hid_consume_state_key_clear_bit(uint8_t key)
{
    if ((ble_app_hid_consume_state.key_state & (1  << key)) != 0)
    {
        ble_app_hid_consume_state.key_state &= ~(1  << key);
        return 0;
    }
    /* All slots busy */
    return -EBUSY;
}

void key_report_send(uint8_t *key_val, uint16_t key_val_len)
{
    app_env_t *env = ble_app_get_env();
    if (env->data[7].is_config_on)
    {
        sibles_value_t value;
        value.hdl = env->data[7].srv_handle;
        value.idx = BLE_APP_HID_REPORT_VAL;
        value.len = key_val_len;
        value.value = key_val;
        BLE_APP_LOG_I("send key report srv_handle:0x%x, value:0x%x, value: %d", value.hdl, *(uint32_t *)value.value, *(uint32_t *)value.value);

        for (uint8_t i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
        {
            if (env->conn_idx[i] != BLE_APP_CONN_IDX_INVALID)
            {
                sibles_write_value(env->conn_idx[i], &value);
            }
        }
    }
}

/************************************************************************
 *@function:void ble_app_take_photo(void);
 *@brief: 拍照
 *@param: null
 *@return:null
*************************************************************************/
void ble_app_take_photo(void)
{
    app_env_t *env = ble_app_get_env();
    static bool control_state = false;

    if (control_state == false)
    {
        ble_app_hid_consume_state_key_set_bit(HIDS_CTRL_VOL_UP);
        key_report_send((uint8_t *)&ble_app_hid_consume_state, sizeof(ble_app_hid_consume_state));
        ble_app_hid_consume_state_key_clear_bit(HIDS_CTRL_VOL_UP);
        key_report_send((uint8_t *)&ble_app_hid_consume_state, sizeof(ble_app_hid_consume_state));
        control_state = true;
    }
    else
    {
        ble_app_hid_consume_state_key_set_bit(HIDS_CTRL_VOL_DOWN);
        key_report_send((uint8_t *)&ble_app_hid_consume_state, sizeof(ble_app_hid_consume_state));
        ble_app_hid_consume_state_key_clear_bit(HIDS_CTRL_VOL_DOWN);
        key_report_send((uint8_t *)&ble_app_hid_consume_state, sizeof(ble_app_hid_consume_state));
        control_state = false;
    }
}

#define HID_KEY_SET(key) ble_app_hid_consume_state_key_set_bit(key)
#define HID_KEY_CLEAR(key) ble_app_hid_consume_state_key_clear_bit(key)
#define HID_KEY_SEND() key_report_send((uint8_t *)&ble_app_hid_consume_state, sizeof(ble_app_hid_consume_state))


static rt_err_t test_hids(int argc, char **argv)
{
    if (argc < 3)
    {
        if (!strcmp(argv[1], "take_photo"))
        {
            ble_app_take_photo();
        }
        else
        {
            BLE_APP_LOG_I("usage: test_hids key [p|r] \n");
        }
    }
    else
    {
        uint8_t key = (uint8_t)atoi(&argv[1][0]);
        if (argv[2][0] == 'p')
        {
            BLE_APP_LOG_I("press key:%d", key);
            HID_KEY_SET(key);
        }
        else
        {
            BLE_APP_LOG_I("release key:%d, argv[2][0]:%c", key, argv[2][0]);
            HID_KEY_CLEAR(key);
        }
        HID_KEY_SEND();
    }
    return 0;
}
FINSH_FUNCTION_EXPORT(test_hids, Test HIDS);
MSH_CMD_EXPORT(test_hids, Test HIDS);

#endif

/************************************************************************
 *@function:uint8_t ble_app_conn_idx_set(uint8_t conn_idx);
 *@brief: 设置连接索引
 *@param: uint8_t conn_idx - 连接索引
 *@return:连接索引编号，BLE_APP_CONN_IDX_INVALID - 已满
*************************************************************************/
uint8_t ble_app_conn_idx_set(uint8_t conn_idx)
{
    app_env_t *env = ble_app_get_env();
    for (int i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
    {
        if (env->conn_idx[i] == conn_idx)
        {
            return i;
        }

        if (env->conn_idx[i] == BLE_APP_CONN_IDX_INVALID)
        {
            env->conn_idx[i] = conn_idx;
            return i;
        }
    }
    return BLE_APP_CONN_IDX_INVALID;
}

/************************************************************************
 *@function:void ble_app_conn_idx_clear(uint8_t conn_idx);
 *@brief: 清除连接索引
 *@param: uint8_t conn_idx - 连接索引
 *@return:null
*************************************************************************/
void ble_app_conn_idx_clear(uint8_t conn_idx)
{
    app_env_t *env = ble_app_get_env();
    uint8_t idx = ble_app_conn_idx_check(conn_idx);
    if (idx < BLE_APP_CONN_NUM_MAX)
    {
        env->conn_idx[idx] = BLE_APP_CONN_IDX_INVALID;
    }
}

/************************************************************************
 *@function:uint8_t ble_app_conn_idx_check(uint8_t conn_idx);
 *@brief: 检查连接索引是否存在
 *@param: uint8_t conn_idx - 连接索引
 *@return:连接索引，BLE_APP_CONN_IDX_INVALID - 未找到
*************************************************************************/
uint8_t ble_app_conn_idx_check(uint8_t conn_idx)
{
    app_env_t *env = ble_app_get_env();
    for (int i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
    {
        if (env->conn_idx[i] == conn_idx)
        {
            return i;
        }
    }
    return BLE_APP_CONN_IDX_INVALID;
}

/************************************************************************
 *@function:bool ble_app_bond_device_check(uint8_t *peer_addr);
 *@brief: 检查对端addr是否为配对addr
 *@param: uint8_t *peer_addr - 对端地址
 *@return:true - 配对，false - 未配对
*************************************************************************/
bool ble_app_bond_device_check(uint8_t *peer_addr)
{
    uint8_t bonded_num = 0;
    uint8_t bond_priority = 0xff;
    uint8_t bond_addr[BD_ADDR_LEN] = {0};
    conn_manager_get_bonded_dev_t get_bonded_dev;

    bonded_num = connection_manager_get_bonded_devices((uint8_t *)&get_bonded_dev);
    BLE_APP_LOG_I("bonded_num:%d", bonded_num);
    if (bonded_num > 0)
    {
        for (int i = 0; i < bonded_num && i < MAX_PAIR_DEV; i++)
        {
            BLE_APP_LOG_I("idx %d, priority %d", i, get_bonded_dev.priority[i]);
            BLE_APP_LOG_I("addr %02x:%02x:%02x:%02x:%02x:%02x", get_bonded_dev.peer_addr[i].addr.addr[5],
                get_bonded_dev.peer_addr[i].addr.addr[4], get_bonded_dev.peer_addr[i].addr.addr[3],
                get_bonded_dev.peer_addr[i].addr.addr[2], get_bonded_dev.peer_addr[i].addr.addr[1],
                get_bonded_dev.peer_addr[i].addr.addr[0]);

            if (get_bonded_dev.priority[i] < bond_priority)
            {
                bond_priority = get_bonded_dev.priority[i];
                memcpy(bond_addr, get_bonded_dev.peer_addr[i].addr.addr, BD_ADDR_LEN);
            }
        }

        if (memcmp(peer_addr, bond_addr, BD_ADDR_LEN) == 0)
        {
            return true;
        }
        else
        {
            return false;
        }
    }
    else
    {
        return false;
    }
}

/************************************************************************
 *@function:void ble_app_bond_device_get(uint8_t *peer_addr, uint8_t len);
 *@brief: 获取当前配对设备的addr
 *@param: uint8_t *peer_addr - 对端地址, uint8_t len - 地址长度 应为6
 *@return:null
*************************************************************************/
void ble_app_bond_device_get(uint8_t *peer_addr, uint8_t len)
{
    uint8_t bonded_num = 0;
    uint8_t bond_priority = 0xff;
    uint8_t bond_addr[BD_ADDR_LEN] = {0};
    conn_manager_get_bonded_dev_t get_bonded_dev;

    bonded_num = connection_manager_get_bonded_devices((uint8_t *)&get_bonded_dev);
    if (bonded_num > 0)
    {
        for (int i = 0; i < bonded_num && i < MAX_PAIR_DEV; i++)
        {
            if (get_bonded_dev.priority[i] < bond_priority)
            {
                bond_priority = get_bonded_dev.priority[i];
                memcpy(bond_addr, get_bonded_dev.peer_addr[i].addr.addr, BD_ADDR_LEN);
            }
        }
    }

    if (len <= BD_ADDR_LEN)
    {
        memcpy(peer_addr, bond_addr, len);
    }
    else
    {
        memcpy(peer_addr, bond_addr, BD_ADDR_LEN);
    }
}

/************************************************************************
 *@function:bool ble_app_conn_bond_check(void);
 *@brief: 检查当前正在连接的对端addr是否为配对addr
 *@param: null
 *@return:true - 配对，false - 未配对
*************************************************************************/
bool ble_app_conn_bond_check(void)
{
    app_env_t *env = ble_app_get_env();

    for (int i = 0; i < BLE_APP_CONN_NUM_MAX; i++)
    {
        if (env->conn_idx[i] != BLE_APP_CONN_IDX_INVALID)
        {
            if (ble_app_bond_device_check(env->conn_para[i].peer_addr.addr))
            {
                return true;
            }
        }
    }

    return false;
}

/************************************************************************
 *@function:void ble_app_bond_save(void);
 *@brief: 主动将当前所缓存的绑定信息保存到flash
 *@return:null
 *@return:null
*************************************************************************/
void ble_app_bond_save(void)
{
    uint8_t bonded_num = 0;
    conn_manager_get_bonded_dev_t get_bonded_dev;

    bonded_num = connection_manager_get_bonded_devices((uint8_t *)&get_bonded_dev);
    if (bonded_num > 0)
    {
        ble_app_bond_device_get(g_ble_app_bonded_addr, BD_ADDR_LEN);
        set_bond_infor_to_flash();
    }
    else
    {
        BLE_APP_LOG_W("No need bond device found.");
    }

}

/************************************************************************
 *@function:void ble_app_bond_resume(void);
 *@brief: 将最高优先级的配对信息恢复至无效绑定之前的状态
 *@return:null
 *@return:null
*************************************************************************/
void ble_app_bond_resume(void)
{
    bd_addr_t l_peer_addr;
    // connection_manager_delete_bond_highest_priority();
    uint8_t l_addr[BD_ADDR_LEN] = {0};
    if (memcmp(g_ble_app_bonded_addr, l_addr, BD_ADDR_LEN))
    {
        memcpy(l_peer_addr.addr, g_ble_app_bonded_addr, BD_ADDR_LEN);
        pair_priority_update(l_peer_addr);
    }
    set_bond_infor_to_flash();
}

static void ble_app_wait_user_id_timeout(void)
{
    app_env_t *env = ble_app_get_env();
    if (env->mb_handle)
    {
        rt_mb_send(env->mb_handle, BLE_USER_EVENT_WAIT_ID_TIMEOUT);
    }
}

static void ble_app_wait_user_id_timer_callback(void *p_context)
{
    // 收到绑定信息后，开启该定时器
    // 若此期间收到user id则关闭该定时器
    // 若此期间未收到user id则判断是否是二维码配对模式
    // 若是二维码配对模式，则主动断连，并且恢复绑定信息
    // 若不是二维码配对模式，则仅恢复绑定信息，保持连接
    BLE_APP_LOG_W("user id get timeout.");
    ble_app_wait_user_id_timeout();
}

/************************************************************************
 *@function:void ble_app_wait_user_id_timer_stop(bool reason_flag);
 *@brief: 已获得下发的user id，停止等待user id的定时器，并根据reason_flag判断是否需要断开连接并恢复绑定信息
 *@param: bool reason_flag - true - 配对成功，false - 配对异常
 *@return:null
*************************************************************************/
void ble_app_wait_user_id_timer_stop(bool reason_flag)
{
    if (wait_user_id_timer)
    {
        rt_timer_stop(wait_user_id_timer);
    }

    if (!reason_flag)
    {
        ble_app_wait_user_id_timeout();
    }
}

static void ble_app_bond_status_updata_delay_timer_callback(void *p_context)
{
    app_env_t *env = ble_app_get_env();
    uint16_t type = *(uint16_t *)p_context;
    if ((type == 0) || (type == BLE_USER_EVENT_BOND_CANCEL))
    {
        if (env->mb_handle)
        {
            rt_mb_send(env->mb_handle, BLE_USER_EVENT_BOND_CANCEL);
        }
    }
    else if (type == BLE_USER_EVENT_UNBOND)
    {
        if (env->mb_handle)
        {
            rt_mb_send(env->mb_handle, BLE_USER_EVENT_UNBOND);
        }
    }

    if (g_ble_app_mb_op_type != 0)
    {
        g_ble_app_mb_op_type = 0;
    }
}

/************************************************************************
 *@function:void ble_app_factory_reset_set(bool state);
 *@brief: 根据用户选择执行是否恢复出厂设置
 *@param: bool state - true - 恢复出厂设置，false - 不恢复出厂设置
 *@return:null
*************************************************************************/
void ble_app_factory_reset_set(bool state)
{
    if (state)
    {
        ble_bond_status_update(BLE_BOND_STATUS_RESET_CONFIRM);
    }
    else
    {
        g_ble_app_mb_op_type = BLE_USER_EVENT_BOND_CANCEL;
        ble_bond_status_update(BLE_BOND_STATUS_RESET_CANCEL);
        rt_timer_start(bond_status_updata_delay_timer);
    }
}

/************************************************************************
 *@function:bool ble_app_is_pair_complete(void);
 *@brief: 返回是否配对完成
 *@param: null
 *@return:true - 配对完成，false - 未配对完成或未执行配对
*************************************************************************/
bool ble_app_is_pair_complete(void)
{
    return is_pairing_complete;
}

#include "pm_manager.h"
bool ble_app_suspend_prepare(const void *task_info)
{
    ble_app_advertising_stop();
    return true;
}

bool ble_app_resume_prepare(const void *task_info)
{
    ble_app_advertising_start();
    return true;
}

static void pm_status_notify(uint32_t sta, void *data)
{
    if (PM_SLEEP_ENTER == sta)
    {
        ble_app_suspend_prepare(NULL);
    }
    else if (PM_SLEEP_EXIT == sta)
    {
        ble_app_resume_prepare(NULL);
    }
}
DECLARE_PM_EVENT_LISTENER(pm_lb55x_ble_module, pm_status_notify, PM_LEVEL_DEVICE);

int pm_ble_app_init(void)
{
    app_register_pm_event_listener(&pm_lb55x_ble_module);

    return 0;
}
// INIT_COMPONENT_EXPORT(pm_ble_app_init);

// ble central command api
static uint8_t ble_app_create_connection(ble_gap_addr_t *peer_addr, uint8_t own_addr_type, uint16_t super_timeout,
        uint16_t conn_itv, uint16_t scan_itv, uint16_t scan_wd)
{
    app_env_t *env = ble_app_get_env();
    ble_gap_connection_create_param_t *conn_param = &env->last_init_conn;
    conn_param->own_addr_type = own_addr_type;
    conn_param->conn_to = super_timeout;
    conn_param->type = GAPM_INIT_TYPE_DIRECT_CONN_EST;
    conn_param->conn_param_1m.scan_intv = scan_itv;
    conn_param->conn_param_1m.scan_wd = scan_wd;
    conn_param->conn_param_1m.conn_intv_max = conn_itv;
    conn_param->conn_param_1m.conn_intv_min = conn_itv == 12 ? conn_itv : (conn_itv - 16);
    conn_param->conn_param_1m.conn_latency = 0;
    conn_param->conn_param_1m.supervision_to = super_timeout;
    conn_param->conn_param_1m.ce_len_max = 48;//100;
    conn_param->conn_param_1m.ce_len_min = 0;//60;
    memcpy(&conn_param->peer_addr, peer_addr, sizeof(ble_gap_addr_t));
    return ble_gap_create_connection(conn_param);
}

static uint8_t ble_app_reconnect(void)
{
    app_env_t *env = ble_app_get_env();
    return ble_gap_create_connection(&env->last_init_conn);
}

static void write_cccd(uint8_t current_conn_idx, uint16_t write_handle)
{
    app_env_t *env = ble_app_get_env();

    sibles_write_remote_value_t value;
    value.handle = write_handle;
    value.write_type = SIBLES_WRITE;//SIBLES_WRITE_WITHOUT_RSP;
    value.len = 2;

    // uint8_t *write_data = malloc(2);
    // *write_data = 0x01;
    // *(write_data + 1) = 0x00;

    uint8_t write_data[2] = {0x01, 0x00};

    value.value = (uint8_t *)write_data;

    sibles_write_remote_value(env->conn[current_conn_idx].rmt_svc_hdl, env->conn[current_conn_idx].conn_idx, &value);
    BLE_APP_LOG_I("current_conn_idx %d, conn_idx %d", current_conn_idx, env->conn[current_conn_idx].conn_idx);
}

static void ble_app_display_command(void)
{
    BLE_APP_LOG_I("Command list:");
    BLE_APP_LOG_I("diss conn_idx. Connected with advertising index device. Details see: diss conn_idx help.");
    BLE_APP_LOG_I("diss reconn. Reconnected last initial connected device.");
    BLE_APP_LOG_I("diss scan. Scan advertising device. Details see: diss scan help.");
    BLE_APP_LOG_I("diss search_svc [dev_idx] [len] [uuid]. Search GATT service with speicifed uuid for peer device.");
    BLE_APP_LOG_I("diss up_conn [dev_idx] [interval max in ms] [interval min in ms] [latency] [supervision timeout in ms]. Update connection parameter.");
    BLE_APP_LOG_I("diss up_phy [dev_idx] [phy, 0:1M, 1:2M, 2:coded]. Update physical.");
    BLE_APP_LOG_I("diss up_len [dev_idx] [tx_octets] [tx_time]. Update data length.");
    BLE_APP_LOG_I("diss exch_mtu [dev_idx]. Trigger exchange MTU.");
    BLE_APP_LOG_I("diss show_dev. Show all connected device with device index.");
    BLE_APP_LOG_I("diss bond [dev_idx]. Bonded peer device.");
    BLE_APP_LOG_I("diss delete all_bond. Delete all bonded devices.");
    BLE_APP_LOG_I("diss show_rmt_service [dev_idx]. Show last searched service.");
    BLE_APP_LOG_I("diss read_val [dev_idx] [handle]. Read data via dedicated gatt handle.");
    BLE_APP_LOG_I("diss write_val [dev_idx] [handle] [data_len] [data]. Write data via dedicated gatt handle.");
    BLE_APP_LOG_I("diss adv [start|stop]. Start/stop advertising.");
}

int cmd_diss(int argc, char *argv[])
{
    app_env_t *env = ble_app_get_env();
    if (argc > 1)
    {
        if (strcmp(argv[1], "conn_idx") == 0)
        {
            if (strcmp(argv[2], "start") == 0)
            {
                uint8_t idx = atoi(argv[3]);
                if (idx > env->adv_count || idx == 0)
                {
                    BLE_APP_LOG_I("Wrongly idx(%d), Shall not equal 0 or exceed maximum idx(%d)", idx, env->adv_count);
                    return 0;
                }
                uint8_t own_addr_type = atoi(argv[4]);
                uint16_t timeout = atoi(argv[5]) / 10;
                uint16_t conn_itv = atoi(argv[6]) * 4 / 5;
                uint16_t scan_itv = atoi(argv[7]) * 8 / 5;
                uint8_t scan_wd = atoi(argv[8]) * 8 / 5;
                uint8_t ret = ble_app_create_connection(&env->adv_addr[idx - 1], own_addr_type, timeout, conn_itv, scan_itv, scan_wd);
                if (ret != HL_ERR_NO_ERROR)
                    BLE_APP_LOG_I("Create connection failed %d", ret);
            }
            else if (strcmp(argv[2], "cancel") == 0)
            {
                ble_gap_cancel_create_connection();
            }
            else if (strcmp(argv[2], "addr") == 0)
            {
                int i;
                ble_gap_connection_create_param_t conn_param;
                conn_param.own_addr_type = GAPM_STATIC_ADDR;
                conn_param.conn_to = 500;
                conn_param.type = GAPM_INIT_TYPE_DIRECT_CONN_EST;
                conn_param.conn_param_1m.scan_intv = 0x30;
                conn_param.conn_param_1m.scan_wd = 0x30;
                conn_param.conn_param_1m.conn_intv_max = 0x80;
                conn_param.conn_param_1m.conn_intv_min = 0x60;
                conn_param.conn_param_1m.conn_latency = 0;
                conn_param.conn_param_1m.supervision_to = 500;
                conn_param.conn_param_1m.ce_len_max = 48;//100;
                conn_param.conn_param_1m.ce_len_min = 0;//60;
                conn_param.peer_addr.addr_type = atoi(argv[3]);

                hex2data(argv[4], conn_param.peer_addr.addr.addr, BD_ADDR_LEN);

                ble_gap_create_connection(&conn_param);
            }
            else
            {
                BLE_APP_LOG_I("Create connection: diss conn_idx start [idx, select from adv idx] [own_addr_type, 0(public/randam)/1(resolve)]");
                BLE_APP_LOG_I("[super_timeout(ms)] [conn_itv(ms)] [scan_itv(ms)] [scan_wd(ms)]");
                BLE_APP_LOG_I("Create connection via addres: diss conn_idx addr [own_addr_type] [peer_addr, aabbccddeeff]");
                BLE_APP_LOG_I("Cancel connection: diss conn_idx cancel");
            }

        }
        else if (strcmp(argv[1], "reconn") == 0)
        {
            uint8_t ret = ble_app_reconnect();
            if (ret != HL_ERR_NO_ERROR)
                BLE_APP_LOG_I("Reconnection failed %d", ret);
        }
        else if (strcmp(argv[1], "scan") == 0)
        {
            if (strcmp(argv[2], "start") == 0)
            {
                ble_gap_scan_start_t scan_param;
                scan_param.own_addr_type = GAPM_STATIC_ADDR;
                scan_param.type = GAPM_SCAN_TYPE_OBSERVER;
                scan_param.dup_filt_pol = atoi(argv[3]);
                scan_param.scan_param_1m.scan_intv = atoi(argv[4]) * 8 / 5;
                scan_param.scan_param_1m.scan_wd = atoi(argv[5]) * 8 / 5;
                scan_param.duration = atoi(argv[6]) / 10;
                scan_param.period = 0;
                env->scan_rssi = atoi(argv[7]);
                ble_gap_scan_start(&scan_param);
            }
            else if (strcmp(argv[2], "stop") == 0)
            {
                ble_gap_scan_stop();
            }
            else
            {
                BLE_APP_LOG_I("Scan start: diss scan start [dup, 0/1] [interval, ms] [window, ms] [duration, ms] [received_rssi]");
                BLE_APP_LOG_I("Scan stop: diss scan stop");
            }
        }
        else if (strcmp(argv[1], "search_svc") == 0)
        {
            int i, len;
            uint8_t uuid[128] = {0};
            uint8_t idx = (uint16_t)atoi(argv[2]);
            if (idx < BLE_APP_MAX_CONN_COUNT)
            {
                len = atoi(argv[3]);

                hex2data(argv[4], uuid, len);
                //sibles_attm_convert_to128(uuid, uuid, len);
                sibles_search_service(env->conn[idx].conn_idx, len, uuid);
            }
            else
            {
                BLE_APP_LOG_I("Wrongly device index(%d)", idx);
            }
        }
        else if (strcmp(argv[1], "up_conn") == 0)
        {
            ble_gap_update_conn_param_t conn_para;
            uint8_t idx = (uint16_t)atoi(argv[2]);
            if (idx < BLE_APP_MAX_CONN_COUNT)
            {
                conn_para.conn_idx =  env->conn[idx].conn_idx;
                conn_para.intv_max = (uint16_t)atoi(argv[3]) * 4 / 5;
                conn_para.intv_min = (uint16_t)atoi(argv[4]) * 4 / 5;
                // value = argv * 1.25
                conn_para.ce_len_max = 48;//0x100;
                conn_para.ce_len_min = 0;//0x1;
                conn_para.latency = (uint16_t)atoi(argv[5]);
                conn_para.time_out = (uint16_t)atoi(argv[6]) / 10;
                ble_gap_update_conn_param(&conn_para);
            }
            else
            {
                BLE_APP_LOG_I("Wrongly device index(%d)", idx);
            }
        }
        else if (strcmp(argv[1], "up_phy") == 0)
        {
            ble_gap_update_phy_t phy;
            uint8_t idx = (uint16_t)atoi(argv[2]);
            if (idx < BLE_APP_MAX_CONN_COUNT)
            {
                phy.conn_idx = env->conn[idx].conn_idx;
                phy.rx_phy = 1 << atoi(argv[3]);
                phy.tx_phy = 1 << atoi(argv[3]);
                phy.phy_opt = 0;
                ble_gap_update_phy(&phy);
            }
            else
            {
                BLE_APP_LOG_I("Wrongly device index(%d)", idx);
            }
        }
        else if (strcmp(argv[1], "up_len") == 0)
        {
            ble_gap_update_data_len_t data_len;
            uint8_t idx = (uint16_t)atoi(argv[2]);
            if (idx < BLE_APP_MAX_CONN_COUNT)
            {
                data_len.conn_idx = env->conn[idx].conn_idx;
                data_len.tx_octets = (uint16_t)atoi(argv[3]);
                data_len.tx_time = (uint16_t)atoi(argv[4]);
                ble_gap_update_data_len(&data_len);
            }
            else
            {
                BLE_APP_LOG_I("Wrongly device index(%d)", idx);
            }
        }
        else if (strcmp(argv[1], "exch_mtu") == 0)
        {
            uint8_t idx = (uint16_t)atoi(argv[2]);
            if (idx < BLE_APP_MAX_CONN_COUNT)
            {
                sibles_exchange_mtu(env->conn[idx].conn_idx);
            }
            else
            {
                BLE_APP_LOG_I("Wrongly device index(%d)", idx);
            }
        }
        else if (strcmp(argv[1], "show_dev") == 0)
        {
            ble_app_display_connected_device(env);
        }
        else if (strcmp(argv[1], "show_rmt_service") == 0)
        {
            uint8_t idx = (uint16_t)atoi(argv[2]);
            if (idx < BLE_APP_MAX_CONN_COUNT)
            {
                ble_app_display_service(&env->conn[idx].svc);
            }
            else
            {
                BLE_APP_LOG_I("Wrongly device index(%d)", idx);
            }
        }
        else if (strcmp(argv[1], "delete") == 0)
        {
            if (strcmp(argv[2], "all_bond") == 0)
            {
                connection_manager_delete_all_bond();
            }
        }
        else if (strcmp(argv[1], "read_val") == 0)
        {
            uint8_t idx = (uint16_t)atoi(argv[2]);
            if (idx < BLE_APP_MAX_CONN_COUNT)
            {
                sibles_read_remote_value_req_t value;
                value.read_type = SIBLES_READ;
                value.handle = (uint16_t)atoi(argv[3]);
                value.length = 0;
                value.offset = 0;
                int8_t ret = sibles_read_remote_value(env->conn[idx].rmt_svc_hdl, env->conn[idx].conn_idx, &value);
                if (ret != 0)
                    BLE_APP_LOG_I("Read remote value ");
            }
            else
            {
                BLE_APP_LOG_I("Wrongly device index(%d)", idx);
            }
        }
        else if (strcmp(argv[1], "write_val") == 0)
        {
            uint8_t idx = (uint16_t)atoi(argv[2]);
            if (idx < BLE_APP_MAX_CONN_COUNT)
            {
                sibles_write_remote_value_t value;
                value.handle = (uint16_t)atoi(argv[3]);
                value.write_type = SIBLES_WRITE;
                value.len = (uint16_t)atoi(argv[4]);
                uint8_t *w_data = rt_malloc(value.len);
                RT_ASSERT(w_data);
                hex2data(argv[5], w_data, value.len);
                rt_hexdump("write context", 16, w_data, value.len);
                value.value = (uint8_t *)w_data;
                int8_t ret = sibles_write_remote_value(env->conn[idx].rmt_svc_hdl, env->conn[idx].conn_idx, &value);
                if (ret != 0)
                    BLE_APP_LOG_I("Write remote value ");
                rt_free(w_data);
            }
            else
            {
                BLE_APP_LOG_I("Wrongly device index(%d)", idx);
            }
        }
        else if (strcmp(argv[1], "bond") == 0)
        {
            uint8_t idx = (uint16_t)atoi(argv[2]);
            if (idx < BLE_APP_MAX_CONN_COUNT)
            {
                connection_manager_create_bond(env->conn[idx].conn_idx);
            }
            else
            {
                BLE_APP_LOG_I("Wrongly device index(%d)", idx);
            }
        }
        else if (strcmp(argv[1], "write_cccd") == 0)
        {
            uint8_t conn_idx = (uint8_t)atoi(argv[2]);
            uint16_t write_handle = (uint16_t)atoi(argv[3]);

            write_cccd(conn_idx, write_handle);
        }
        else if (strcmp(argv[1], "bond_reply") == 0)
        {
            if (strcmp(argv[2], "1") == 0)
            {
                ble_app_bond_ack_reply_set(true);
            }
            else if (strcmp(argv[2], "0") == 0)
            {
                ble_app_bond_ack_reply_set(false);
            }
            else
            {
                BLE_APP_LOG_I("Wrongly bond_reply value");
            }
        }
        else if (strcmp(argv[1], "adv") == 0)
        {
            if (strcmp(argv[2], "start") == 0)
            {
                ble_app_advertising_start();
            }
            else if (strcmp(argv[2], "stop") == 0)
            {
                ble_app_advertising_stop();
            }
            else
            {
                BLE_APP_LOG_I("Wrongly adv_start value");
            }
        }
        else
        {
            ble_app_display_command();
        }
    }

    return 0;
}

FINSH_FUNCTION_EXPORT_ALIAS(cmd_diss, __cmd_diss, My device information service.);
