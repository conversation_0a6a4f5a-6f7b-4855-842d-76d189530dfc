GUI: 表盘框架适配新字段，优化表盘流畅度

Change-Id: I561c05004f110943b5451153fca685058f522a2f
Signed-off-by: ya<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>

# Please enter the commit message for your changes. Lines starting
# with '#' will be ignored, and an empty message aborts the commit.
#
# Date:      Tue Sep 9 15:49:37 2025 +0800
#
# On branch develop
# Your branch is ahead of 'origin/develop' by 1 commit.
#   (use "git push" to publish your local commits)
#
# Changes to be committed:
#	modified:   Application/App/UI/Page/Launcher/LauncherView.cpp
#	modified:   Application/App/UI/Page/WatchDial/SelectDial/EditDialDataComponent/EditDialDataComponentView.cpp
#	modified:   Application/App/UI/Page/WatchDial/SelectDial/EditDialDataComponent/EditDialDataComponentView.h
#	modified:   Application/App/UI/Page/WatchDial/SelectDial/EditDialTheme/EditDialThemeView.cpp
#	modified:   Application/App/UI/Page/WatchDial/SelectDial/EditDialTheme/EditDialThemeView.h
#	modified:   Application/App/UI/Page/WatchDial/SelectDial/SelectDialView.cpp
#	modified:   Application/App/UI/Page/WatchDial/SelectDial/SelectDialView.h
#	modified:   project/WR02_Win32/project/simulator/WR02_Disk/Dial/dialCommonCfg.cfg
#
