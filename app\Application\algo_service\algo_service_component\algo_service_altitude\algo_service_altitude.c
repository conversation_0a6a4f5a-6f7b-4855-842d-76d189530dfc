/**
 * @file algo_service_altitude.c
 * <AUTHOR> (yanx<PERSON><PERSON><PERSON>@igpsport.com)
 * @brief 高度算法组件实现
 * @version 0.1
 * @date 2024-11-29
 *
 * @copyright Copyright (c) 2024-2025, <PERSON>han Qiwu Technology Co., Ltd
 *
 */
#include "algo_service_altitude.h"
#include "../../qwos/driver/sensor/baro/lps28dfw/lps28dfw.h"   //气压计
#include "../../qwos/driver/sensor/baro/qw_barosensor.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_task.h"
#include "qw_sensor.h"
#include "qw_time_util.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"
#include "utility.h"

//GPS定位后1~2分钟完成校准：
//1、GPS定位后30秒数据不使用，等待稳定；
//2、31~60秒这30秒的GPS高度与气压高度的差值的平均值为偏移值，并计算方差；
//3、如果方差小于阈值，使用偏移值作为气压高度的校准值。此时校准时间为定位后一分钟；
//4、如果方差大于阈值，继续计算GPS高度与气压高度的差值的平均值，直到30秒方差小于阈值，或定位达到2分钟，使用此时的偏移值为校准值。校准时间为定位后1~2分钟。
#define GPS_STEADY_TIME         15        //GPS第一次有信号后等待稳定时间
#define ALTITUDE_CALIB_MIN_TIME 15        //最小计算时间
#define ALTITUDE_CALIB_MAX_TIME 90        //最大计算时间
#define VARIANCE_THRESHOLD      250000    //30秒GPS高度稳定判决方差阈值
#define GPS_ERROR_RANGE         40000     //GPS和气压高度差异阈值，高于这个值认为是错误的
#define NEGATIVE_FILTER_VALUE   1000      //小于10m增加一个随机偏移至10~30m
#define MAX_ALTITUDE_100        700000    //7000米
#define MIN_ALTITUDE_100        -100000   //-1000米
//气压转高度 //press单位为Pa,  100pa=1mbar
#define PRESS_2_ALT(press) (443307.6923 * (10.0 - pow(((double) (press)) / 101325.0, 0.1905) * 10.0))   //100*米

// 输入数据
typedef struct
{
    int32_t altitude_sensor;         // 气压计高度，单位cm
    int32_t altitude_gps;            // GPS高度，单位cm
    uint8_t gps_signal;              // 0:无信号 1:信号一般 2:信号好 3:信号特别好
    uint8_t sports_type;             // 运动类型
    saving_status_e saving_status;   // 记录状态
} algo_altitude_sub_t;

static algo_altitude_sub_t s_algo_in;

// 发布数据
static algo_altitude_pub_t s_algo_out = {0x7fffffff, 0x7fffffff};

// 中间数据
static int s_gps_subscribed = false;   //是否订阅了GPS

// 本算法打开标记
static bool s_is_open = false;

#ifdef SOC_BF0_LCPU

/**
 * @brief 全部运动类型(模式)
 */
typedef enum {
    //跑步类型
    SPORTSTYPE_RUNNING,          //跑步
    SPORTSTYPE_TREADMILL,        //跑步机
    SPORTSTYPE_PLAYGROUND,       //操场跑步
    SPORTSTYPE_TRAIL_RUNNING,    //越野跑
    SPORTSTYPE_WALKING,          //步行
    SPORTSTYPE_INDOOR_RUNNING,   //室内步行

    //骑行类型
    SPORTSTYPE_CYCLING,            //骑行
    SPORTSTYPE_INDOOR_CYCLING,     //室内骑行
    SPORTSTYPE_ROAD_CYCLING,       //公路骑行
    SPORTSTYPE_MOUNTAIN_CYCLING,   //山地骑行
    SPORTSTYPE_COMMUTING,          //骑车通勤
    SPORTSTYPE_TRIP_CYCLING,       //长途骑行

    //游泳
    SPORTSTYPE_POOL_SWIMMING,         //泳池游泳
    SPORTSTYPE_OPEN_WATER_SWIMMING,   //公共水域游泳

    //室内健身
    SPORTSTYPE_STRENGTH_TRAINING,    //力量训练
    SPORTSTYPE_INDOOR_AEROBIC,       //室内有氧
    SPORTSTYPE_ELLIPTICAL_MACHINE,   //椭圆机
    SPORTSTYPE_ROWING_MACHINE,       //划船机

    //户外
    SPORTSTYPE_MOUNTAINEERING,    //登山
    SPORTSTYPE_HIKING,            //徒步
    SPORTSTYPE_SKIING,            //滑雪
    SPORTSTYPE_OUTDOOR_AEROBIC,   //户外有氧

    //其他
    SPORTSTYPE_JUMP_ROPE,         //跳绳
    SPORTSTYPE_TRIATHLON,         //铁人三项
    SPORTSTYPE_COMPOUND_MOTION,   //复合运动
    SPORTSTYPE_EXERCISE,          //锻炼

    //以下不做 v1.00
    SPORTSTYPE_HIIT,                   //高强度间歇运动
    SPORTSTYPE_ROWING_BOAT,            //赛艇
    SPORTSTYPE_PULP_BOARD,             //浆板
    SPORTSTYPE_FITNESS,                //健身
    SPORTSTYPE_SNOWBOARDING,           //单板滑雪
    SPORTSTYPE_SNOWBOARDING_2,         //双板滑雪
    SPORTSTYPE_CROSS_COUNTRY_SKIING,   //越野滑雪
    SPORTSTYPE_YOGA,                   //瑜伽
    SPORTSTYPE_PILATES,                //普拉提
    SPORTSTYPE_OUTDOOR_FRISBEE,        //户外飞盘

    SPORTSTYPE__MAX,
} SPORTTYPE;

/************************************************************************
 *@function:获取运动类型是否为户外
 *@brief:
 *@param: sport_type 运动类型
 *@return: 1:户外 0:室内
*************************************************************************/
static int get_sport_type_is_outdoor(SPORTTYPE sport_type)
{
    int ret = 0;
    switch (sport_type)
    {
    case SPORTSTYPE_RUNNING:
    case SPORTSTYPE_PLAYGROUND:
    case SPORTSTYPE_TRAIL_RUNNING:
    case SPORTSTYPE_WALKING:
    case SPORTSTYPE_CYCLING:
    case SPORTSTYPE_ROAD_CYCLING:
    case SPORTSTYPE_MOUNTAIN_CYCLING:
    case SPORTSTYPE_COMMUTING:
    case SPORTSTYPE_TRIP_CYCLING:
    case SPORTSTYPE_OPEN_WATER_SWIMMING:
    case SPORTSTYPE_MOUNTAINEERING:
    case SPORTSTYPE_HIKING:
    case SPORTSTYPE_SKIING:
    case SPORTSTYPE_OUTDOOR_AEROBIC:
    case SPORTSTYPE_TRIATHLON:
    case SPORTSTYPE_COMPOUND_MOTION:
        ret = 1;
        break;
    case SPORTSTYPE_TREADMILL:
    case SPORTSTYPE_INDOOR_RUNNING:
    case SPORTSTYPE_INDOOR_CYCLING:
    case SPORTSTYPE_POOL_SWIMMING:
    case SPORTSTYPE_STRENGTH_TRAINING:
    case SPORTSTYPE_INDOOR_AEROBIC:
    case SPORTSTYPE_ELLIPTICAL_MACHINE:
    case SPORTSTYPE_ROWING_MACHINE:
    case SPORTSTYPE_JUMP_ROPE:
    case SPORTSTYPE_EXERCISE:
        ret = 0;
        break;
    default:
        ret = 1;
        break;
    }

    return ret;
}
#else
#include "view_page_model_sports.h"
#endif

// 高度检查
static bool altitude_check(int32_t altitude_100)
{
    //检测采集的错误
    if (0xffff == altitude_100)
    {
        return false;
    }
    if (altitude_100 < MIN_ALTITUDE_100 || altitude_100 > MAX_ALTITUDE_100)   //高度在-1000~6000米之间
    {
        return false;
    }
    else
    {
        return true;
    }
}

//输入:气压高度原始值,GPS高度,GPS信号,是否立即更新(若为否,输出正在使用的高度,只在后台修正偏移值),是否手动校准
//输出:校准后的高度(气压高度+偏移值)
//返回:是否校准成功(true or false)
static bool altitude_calib(int32_t *calibrated_altitude, int32_t sensor_altitude, int32_t gps_altitude, uint8_t gps_signal, bool update_immediately,
                           bool manual_set, bool force_calib)
{
    int i;
    static int32_t offset[ALTITUDE_CALIB_MIN_TIME] = {0};   //最近30秒偏移值
    int32_t offset_avg = 0;                                 //最近30秒平均偏移值
    static int32_t used_avg = 0;                            //使用的平均偏移值
    static int64_t total_sum = 0;                           //所有点的偏移值之和
    int32_t total_avg = 0;                                  //所有点的平均偏移值
    static int good_seconds = 0;                            //良好输入的秒数
    static int count = 0;                                   //计算的次数
    static bool calibrated = false;                         //是否已校准
    uint32_t variance = 0;                                  //30秒偏移值的方差
    static int manual_settled = false;
    uint32_t rtc_time = 0;
    // rtc_time = g_p_rtc_time->value;

    //输入高度有效性检测
    if (!altitude_check(sensor_altitude))
    {
        return calibrated;
    }

    //手动设置
    if (true == manual_set)
    {
        used_avg = *calibrated_altitude - sensor_altitude;
        manual_settled = true;
        calibrated = true;
        return calibrated;
    }

    //强制重新开始校准
    if (false != force_calib)
    {
        calibrated = false;
        good_seconds = 0;
        manual_settled = false;
    }

    //已手动设置，输出手动校准值后直接返回，不再进行GPS校准
    if (true == manual_settled)
    {
        *calibrated_altitude = sensor_altitude + used_avg;
        return calibrated;
    }

    //已校准后不再进行后台校准，因为日常高度订阅GPS不能常开
    if (calibrated)
    {
        *calibrated_altitude = sensor_altitude + used_avg;
        return calibrated;
    }

    //GPS高度无效
    if (0 == gps_signal || abs(gps_altitude - sensor_altitude) > GPS_ERROR_RANGE)
    {
        if (true == calibrated)
        {
            *calibrated_altitude = sensor_altitude + used_avg;
        }

        return calibrated;
    }

    if (0 < gps_signal)
    {
        good_seconds++;

        //定位30秒稳定之后开始存偏移值
        if (GPS_STEADY_TIME < good_seconds)
        {
            total_sum += (gps_altitude - sensor_altitude);
            count++;
            offset[(count - 1) % ALTITUDE_CALIB_MIN_TIME] = (gps_altitude - sensor_altitude);
        }

        //定位1分钟后开始尝试校准
        if (GPS_STEADY_TIME + ALTITUDE_CALIB_MIN_TIME <= good_seconds)
        {
            //所有点的平均偏移值
            total_avg = total_sum / count;

            //最近30秒平均偏移值
            for (i = 0; i < ALTITUDE_CALIB_MIN_TIME; i++)
            {
                offset_avg += offset[i];
            }

            offset_avg /= ALTITUDE_CALIB_MIN_TIME;

            //计算30秒方差
            for (i = 0; i < ALTITUDE_CALIB_MIN_TIME; i++)
            {
                variance += ((int) (offset[i] - offset_avg) * (int) (offset[i] - offset_avg));
            }

            variance /= ALTITUDE_CALIB_MIN_TIME;

            //已校准且正在记录，只更新平均偏移值，输出正在使用的高度
            if (true == calibrated && false == update_immediately)
            {
                *calibrated_altitude = sensor_altitude + used_avg;
            }
            //已校准且未记录，实时尝试更新
            else if (true == calibrated && true == update_immediately)
            {
                //方差小于阈值，趋于稳定，更新校准  //同时要求校准时的高度大于10m
                if (VARIANCE_THRESHOLD > variance && NEGATIVE_FILTER_VALUE < sensor_altitude + total_avg)
                {
                    *calibrated_altitude = sensor_altitude + total_avg;
                    used_avg = total_avg;
                    calibrated = true;
                }
                //方差不小于阈值，不稳定，使用正在使用的高度校准
                else
                {
                    *calibrated_altitude = sensor_altitude + used_avg;
                }
            }
            //达到2分钟且还未校准则直接完成校准
            else if (GPS_STEADY_TIME + ALTITUDE_CALIB_MAX_TIME <= good_seconds && false == calibrated)
            {
                *calibrated_altitude = sensor_altitude + total_avg;
                used_avg = total_avg;
                calibrated = true;

                //负值过滤
                if (NEGATIVE_FILTER_VALUE > *calibrated_altitude)
                {
                    *calibrated_altitude = ((random_num_get(rtc_time) % 20) + 10) * 100;
                    used_avg = *calibrated_altitude - sensor_altitude;
                }
            }
            //1到2分钟之间，未校准
            else
            {
                //方差小于阈值，趋于稳定，完成校准  //同时要求校准时的高度大于10m
                if (VARIANCE_THRESHOLD > variance && NEGATIVE_FILTER_VALUE < sensor_altitude + total_avg)
                {
                    *calibrated_altitude = sensor_altitude + total_avg;
                    used_avg = total_avg;
                    calibrated = true;
                }
            }
        }
    }
    return calibrated;
}

/**
 * @brief 计算气压计睡眠偏移
 * @param altitude 休眠唤醒后第一次输入的气压值
 * @param systime 时间戳
 */
static void altitude_sleep_offset(int32_t *altitude, uint32_t systime)
{
    static int32_t last_altitude;
    static uint32_t last_systime = 0;
    static int32_t sleep_offset = 0;

    if (0 != last_systime && 1200 < systime - last_systime)
    {
        sleep_offset = last_altitude - *altitude;
    }

    *altitude += sleep_offset;
    last_altitude = *altitude;
    last_systime = systime;
}

/**
 * @brief 一阶低通滤波，用于对原始气压高度数据进行滤波
 * @param alt sensor输入数据
 */
static void altitude_lp_filter(int32_t *alt)
{
    static uint8_t s_is_inited = false;
    static int32_t s_last_alt = 0;

    if (altitude_check(*alt) == true)
    {
        if (s_is_inited == false)
        {
            s_last_alt = *alt;
            s_is_inited = true;
        }

        *alt = s_last_alt + (int32_t) ((float) (*alt - s_last_alt) * 0.25f);
        s_last_alt = *alt;
    }
}

static void altitude_data_process(int32_t *sensor_data, uint32_t systime)
{
    //休眠偏移
    altitude_sleep_offset(sensor_data, systime);

    //低通滤波
    altitude_lp_filter(sensor_data);
}

/**
 * @brief 算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void sns_baro_in_callback(const void *in, uint32_t len)
{
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_ALTITUDE;
    head.input_type = DATA_ID_BROMETER;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_gps_data_in_callback(const void *in, uint32_t len)
{
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_ALTITUDE;
    head.input_type = DATA_ID_ALGO_GPS_DATA;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_click_alt_cal_in_callback(const void *in, uint32_t len)
{
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_ALTITUDE;
    head.input_type = DATA_ID_EVENT_ALTITUDE_CAL;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_sports_ctrl_in_callback(const void *in, uint32_t len)
{
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_ALTITUDE;
    head.input_type = DATA_ID_EVENT_SPORTS_CTRL;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_altitude_out_callback(const void *out, uint32_t len)
{
    // 算法输出后发布或者2次处理
    if (qw_dataserver_publish_id(DATA_ID_ALGO_ALTITUDE, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("%s publish error", __FUNCTION__);
    }
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_SENSOR_NAME_BARO,
        .topic_id = DATA_ID_BROMETER,
        .callback = sns_baro_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_CLICK_ALT,
        .topic_id = DATA_ID_EVENT_ALTITUDE_CAL,
        .callback = algo_click_alt_cal_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
};
/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node_gps[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_gps_data",
        .topic_id = DATA_ID_ALGO_GPS_DATA,
        .callback = algo_gps_data_in_callback,
    },
};

/**
 * @brief 算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_altitude_init(void)
{
    return 0;
}

/**
 * @brief 打开算法
 *
 * @return int32_t 结果
 */
static int32_t algo_altitude_open(void)
{
    if (s_is_open)
    {
        ALGO_COMP_LOG_W("%s already open", __FUNCTION__);
        return 0;
    }

    ALGO_COMP_LOG_D("%s open", __FUNCTION__);

    s_is_open = true;

    return algo_topic_list_subscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
}

/**
 * @brief feed算法
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_altitude_feed(uint32_t input_type, void *data, uint32_t len)
{
    int32_t ret = 0;
    algo_altitude_sub_t *algo_in = &s_algo_in;
    algo_altitude_pub_t *algo_out = &s_algo_out;
    uint32_t systime = (uint32_t) get_timestamp_sec();   //当前的时间戳 单位S
    static uint32_t last_systime = 0;

    if (DATA_ID_BROMETER == input_type && data != NULL)
    {
        struct sensor_baro *barometer_data = (struct sensor_baro *) data;
        algo_out->pressure = barometer_data->pressure;
        algo_in->altitude_sensor = PRESS_2_ALT(barometer_data->pressure * 100.0);
        altitude_data_process(&algo_in->altitude_sensor, systime);
        algo_out->altitude_sensor = algo_in->altitude_sensor;
        ALGO_COMP_LOG_D("algo_altitude_feed altitude_sensor:%fpa, %d\n", ((struct sensor_baro *) data)->pressure, algo_in->altitude_sensor);
    }
    else if (DATA_ID_ALGO_GPS_DATA == input_type && data != NULL)
    {
        gps_pub_t *gps_data = (gps_pub_t *) data;
        algo_in->altitude_gps = (gps_data->altitude.value / gps_data->altitude.scale) * 100;
        algo_in->gps_signal = gps_data->signal;
        ALGO_COMP_LOG_D("algo_altitude_feed value:%d scale:%d\n", gps_data->altitude.value, gps_data->altitude.scale);
    }
    else if (DATA_ID_EVENT_SPORTS_CTRL == input_type && data != NULL)
    {
        algo_sports_ctrl_t *sports_ctrl = (algo_sports_ctrl_t *) data;
        if ((sports_ctrl->ctrl_type == enum_ctrl_start && sports_ctrl->saving_status == enum_status_saving) ||
            (sports_ctrl->ctrl_type == enum_ctrl_stop && sports_ctrl->saving_status == enum_status_free))
        {
            algo_in->saving_status = sports_ctrl->saving_status;
            algo_in->sports_type = sports_ctrl->sports_type;
        }

        if (get_sport_type_is_outdoor((SPORTTYPE) algo_in->sports_type))
        {
            // 开启运动时订阅GPS
            if (algo_in->saving_status == enum_status_saving && false == s_gps_subscribed)
            {
                ret = algo_topic_list_subscribe(s_algo_topic_node_gps, sizeof(s_algo_topic_node_gps) / sizeof(s_algo_topic_node_gps[0]));
                if (ret != 0)
                {
                    return ret;
                }
                s_gps_subscribed = true;
            }

            // 关闭运动时取消订阅GPS
            if (algo_in->saving_status == enum_status_free && false != s_gps_subscribed)
            {
                algo_topic_list_unsubscribe(s_algo_topic_node_gps, sizeof(s_algo_topic_node_gps) / sizeof(s_algo_topic_node_gps[0]));
                s_gps_subscribed = false;
            }         
        }
    }

    // 强制校准
    if (DATA_ID_EVENT_ALTITUDE_CAL == input_type && data != NULL)
    {
        gui_altitude_calibration_t *click_alt = (gui_altitude_calibration_t *) data;
        // 手动或APP高度输入
        if (ALTITUDE_CALIBRATION_USER == click_alt->event_type || ALTITUDE_CALIBRATION_PHONE == click_alt->event_type)
        {
            altitude_calib(&click_alt->altitude_value, algo_in->altitude_sensor, algo_in->altitude_gps, 0 < algo_in->gps_signal && s_gps_subscribed,
                           enum_status_free == algo_in->saving_status, true, false);
        }
        else if (ALTITUDE_CALIBRATION_GPS == click_alt->event_type)   //强制重新开始GPS校准
        {
            // 不在运动中或在户外运动中才开启GPS校准
            if (enum_status_free == algo_in->saving_status
                || (enum_status_free != algo_in->saving_status && get_sport_type_is_outdoor((SPORTTYPE) algo_in->sports_type)))
            {
                if (false == s_gps_subscribed)
                {
                    ret = algo_topic_list_subscribe(s_algo_topic_node_gps, sizeof(s_algo_topic_node_gps) / sizeof(s_algo_topic_node_gps[0]));
                    if (ret != 0)
                    {
                        return ret;
                    }
                    s_gps_subscribed = true;
                }
            }

            altitude_calib(&algo_out->altitude, algo_in->altitude_sensor, algo_in->altitude_gps, 0 < algo_in->gps_signal && s_gps_subscribed,
                           enum_status_free == algo_in->saving_status, false, true);
        }
        else if (ALTITUDE_CALIBRATION_CANCEL == click_alt->event_type)   //取消校准
        {
            if (false != s_gps_subscribed)                               //取消订阅GPS
            {
                algo_topic_list_unsubscribe(s_algo_topic_node_gps, sizeof(s_algo_topic_node_gps) / sizeof(s_algo_topic_node_gps[0]));
                s_gps_subscribed = false;
            }
        }
    }
    else if (last_systime < systime)
    {
        last_systime = systime;

        if (!altitude_calib(&algo_out->altitude, algo_in->altitude_sensor, algo_in->altitude_gps, 0 < algo_in->gps_signal && s_gps_subscribed,
                            enum_status_free == algo_in->saving_status, false, false))
        {
            // 校准失败，则使用气压计高度
            algo_out->altitude_show = algo_in->altitude_sensor;
            algo_out->calibrated = false;
        }
        else
        {
            // 校准成功，则使用校准后的高度
            algo_out->altitude_show = algo_out->altitude;
            algo_out->calibrated = true;
            algo_out->calib_time = systime;
        }

        //SA TEST GRADE
        // static int32_t s_alt = 0;
        // static int s_up = true;
        // algo_out->altitude = s_alt;
        // algo_out->altitude_show = s_alt;

        // if (false != s_up && 10000 < s_alt)
        // {
        //     s_up = false;
        // }
        // else if (false == s_up && 0 > s_alt)
        // {
        //     s_up = true;
        // }


        // if (false != s_up)
        // {
        //     s_alt += 100;
        // }
        // else
        // {
        //     s_alt -= 100;
        // }

        //数据发布
        algo_altitude_out_callback(algo_out, sizeof(algo_altitude_pub_t));
    }

    return ret;
}

/**
 * @brief 关闭算法
 *
 * @return int32_t 结果
 */
static int32_t algo_altitude_close(void)
{
    if (!s_is_open)
    {
        ALGO_COMP_LOG_W("%s already close", __FUNCTION__);
        return 0;
    }

    if (false != s_gps_subscribed)
    {
        algo_topic_list_unsubscribe(s_algo_topic_node_gps, sizeof(s_algo_topic_node_gps) / sizeof(s_algo_topic_node_gps[0]));
        s_gps_subscribed = false;
    }

    algo_topic_list_unsubscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));

    s_is_open = false;
    ALGO_COMP_LOG_D("%s close", __FUNCTION__);
    return 0;
}

// 算法组件实现
static algo_compent_ops_t s_altitude_algo = {
    .init = algo_altitude_init,
    .open = algo_altitude_open,
    .feed = algo_altitude_feed,
    .close = algo_altitude_close,
    .ioctl = NULL,
};

/**
 * @brief 组件注册
 *
 * @return int32_t 结果
 */
int32_t register_altitude_algo(void)
{
    algo_compnent_register(ALGO_TYPE_ALTITUDE, &s_altitude_algo);
    return 0;
}
