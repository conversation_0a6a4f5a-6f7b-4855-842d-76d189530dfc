0x20, 0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x27, 0x28, 0x29, 0x2a, 0x2b, 0x2c, 0x2d, 0x2e, 0x2f, 0x30,
0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x3c, 0x3d, 0x3e, 0x3f, 0x40, 0x41,
0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48, 0x49, 0x4a, 0x4b, 0x4c, 0x4d, 0x4e, 0x4f, 0x50, 0x51, 0x52,
0x53, 0x54, 0x55, 0x56, 0x57, 0x58, 0x59, 0x5a, 0x5b, 0x5c, 0x5d, 0x5e, 0x5f, 0x60, 0x61, 0x62, 0x63,
0x64, 0x65, 0x66, 0x67, 0x68, 0x69, 0x6a, 0x6b, 0x6c, 0x6d, 0x6e, 0x6f, 0x70, 0x71, 0x72, 0x73, 0x74,
0x75, 0x76, 0x77, 0x78, 0x79, 0x7a, 0x7b, 0x7c, 0x7d, 0x7e, 0xa0, 0xa1, 0xa9, 0xab, 0xae, 0xb0, 0xbb,
0xbf, 0xc0, 0xc1, 0xc2, 0xc3, 0xc4, 0xc7, 0xc8, 0xc9, 0xca, 0xcb, 0xcc, 0xcd, 0xce, 0xcf, 0xd1, 0xd2,
0xd3, 0xd4, 0xd5, 0xd6, 0xd8, 0xd9, 0xda, 0xdb, 0xdc, 0xdd, 0xdf, 0xe0, 0xe1, 0xe2, 0xe3, 0xe4, 0xe7,
0xe8, 0xe9, 0xea, 0xeb, 0xec, 0xed, 0xee, 0xef, 0xf1, 0xf2, 0xf3, 0xf4, 0xf5, 0xf6, 0xf9, 0xfa, 0xfb,
0xfc, 0xfd, 0xff, 0x101, 0x102, 0x103, 0x104, 0x105, 0x106, 0x107, 0x10c, 0x10d, 0x10e, 0x10f, 0x110, 0x111, 0x118,
0x119, 0x11a, 0x11b, 0x128, 0x129, 0x139, 0x13a, 0x13d, 0x13e, 0x141, 0x142, 0x143, 0x144, 0x147, 0x148, 0x150, 0x151,
0x154, 0x155, 0x158, 0x159, 0x15a, 0x15b, 0x160, 0x161, 0x164, 0x165, 0x168, 0x169, 0x16e, 0x16f, 0x170, 0x171, 0x178,
0x179, 0x17a, 0x17b, 0x17c, 0x17d, 0x17e, 0x1a0, 0x1a1, 0x1af, 0x1b0, 0x300, 0x301, 0x302, 0x303, 0x309, 0x323, 0x401,
0x410, 0x411, 0x412, 0x413, 0x414, 0x415, 0x416, 0x417, 0x418, 0x419, 0x41a, 0x41b, 0x41c, 0x41d, 0x41e, 0x41f, 0x420,
0x421, 0x422, 0x423, 0x424, 0x425, 0x426, 0x427, 0x428, 0x429, 0x42b, 0x42d, 0x42e, 0x42f, 0x430, 0x431, 0x432, 0x433,
0x434, 0x435, 0x436, 0x437, 0x438, 0x439, 0x43a, 0x43b, 0x43c, 0x43d, 0x43e, 0x43f, 0x440, 0x441, 0x442, 0x443, 0x444,
0x445, 0x446, 0x447, 0x448, 0x449, 0x44a, 0x44b, 0x44c, 0x44d, 0x44e, 0x44f, 0x451, 0xe01, 0xe02, 0xe03, 0xe04, 0xe05,
0xe06, 0xe07, 0xe08, 0xe09, 0xe0a, 0xe0b, 0xe0c, 0xe0d, 0xe0e, 0xe0f, 0xe10, 0xe11, 0xe12, 0xe13, 0xe14, 0xe15, 0xe16,
0xe17, 0xe18, 0xe19, 0xe1a, 0xe1b, 0xe1c, 0xe1d, 0xe1e, 0xe1f, 0xe20, 0xe21, 0xe22, 0xe23, 0xe24, 0xe25, 0xe26, 0xe27,
0xe28, 0xe29, 0xe2a, 0xe2b, 0xe2c, 0xe2d, 0xe2e, 0xe2f, 0xe30, 0xe31, 0xe32, 0xe33, 0xe34, 0xe35, 0xe36, 0xe37, 0xe38,
0xe39, 0xe3a, 0xe3f, 0xe40, 0xe41, 0xe42, 0xe43, 0xe44, 0xe45, 0xe46, 0xe47, 0xe48, 0xe49, 0xe4a, 0xe4b, 0xe4c, 0xe4d,
0xe4e, 0xe4f, 0xe50, 0xe51, 0xe52, 0xe53, 0xe54, 0xe55, 0xe56, 0xe57, 0xe58, 0xe59, 0xe5a, 0xe5b, 0x1ea0, 0x1ea1, 0x1ea2,
0x1ea3, 0x1ea4, 0x1ea5, 0x1ea6, 0x1ea7, 0x1ea8, 0x1ea9, 0x1eaa, 0x1eab, 0x1eac, 0x1ead, 0x1eae, 0x1eaf, 0x1eb0, 0x1eb1, 0x1eb2, 0x1eb3,
0x1eb4, 0x1eb5, 0x1eb6, 0x1eb7, 0x1eb8, 0x1eb9, 0x1eba, 0x1ebb, 0x1ebc, 0x1ebd, 0x1ebe, 0x1ebf, 0x1ec0, 0x1ec1, 0x1ec2, 0x1ec3, 0x1ec4,
0x1ec5, 0x1ec6, 0x1ec7, 0x1ec8, 0x1ec9, 0x1eca, 0x1ecb, 0x1ecc, 0x1ecd, 0x1ece, 0x1ecf, 0x1ed0, 0x1ed1, 0x1ed2, 0x1ed3, 0x1ed4, 0x1ed5,
0x1ed6, 0x1ed7, 0x1ed8, 0x1ed9, 0x1eda, 0x1edb, 0x1edc, 0x1edd, 0x1ede, 0x1edf, 0x1ee0, 0x1ee1, 0x1ee2, 0x1ee3, 0x1ee4, 0x1ee5, 0x1ee6,
0x1ee7, 0x1ee8, 0x1ee9, 0x1eea, 0x1eeb, 0x1eec, 0x1eed, 0x1eee, 0x1eef, 0x1ef0, 0x1ef1, 0x1ef2, 0x1ef3, 0x1ef4, 0x1ef5, 0x1ef6, 0x1ef7,
0x1ef8, 0x1ef9, 0x2013, 0x2014, 0x2018, 0x2019, 0x201c, 0x201d, 0x201e, 0x2026, 0x2082, 0x20ac, 0x2103, 0x2109, 0x2122, 0x2192, 0x3001,
0x3002, 0x300c, 0x300d, 0x3042, 0x3044, 0x3046, 0x304a, 0x304b, 0x304c, 0x304d, 0x304e, 0x304f, 0x3051, 0x3052, 0x3053, 0x3054, 0x3055,
0x3055, 0x3055, 0x3057, 0x3058, 0x3059, 0x3059, 0x305a, 0x305b, 0x305d, 0x305e, 0x305f, 0x3060, 0x3061, 0x3063, 0x3065, 0x3066, 0x3067,
0x3068, 0x306a, 0x306a, 0x306b, 0x306b, 0x306e, 0x306f, 0x3070, 0x3073, 0x3079, 0x307e, 0x307f, 0x3081, 0x3082, 0x3084, 0x3086, 0x3087,
0x3088, 0x3089, 0x308a, 0x308b, 0x308c, 0x308c, 0x308c, 0x308d, 0x308f, 0x3092, 0x3093, 0x30a1, 0x30a2, 0x30a3, 0x30a3, 0x30a4, 0x30a6,
0x30a7, 0x30a8, 0x30a9, 0x30aa, 0x30ab, 0x30ad, 0x30ae, 0x30af, 0x30b0, 0x30b1, 0x30b2, 0x30b3, 0x30b4, 0x30b5, 0x30b6, 0x30b7, 0x30b8,
0x30b9, 0x30ba, 0x30bb, 0x30bd, 0x30be, 0x30bf, 0x30c0, 0x30c1, 0x30c3, 0x30c4, 0x30c6, 0x30c7, 0x30c8, 0x30c9, 0x30ca, 0x30cb, 0x30cd,
0x30ce, 0x30cf, 0x30d0, 0x30d1, 0x30d3, 0x30d4, 0x30d5, 0x30d6, 0x30d7, 0x30d8, 0x30d9, 0x30da, 0x30db, 0x30dc, 0x30dc, 0x30dd, 0x30de,
0x30df, 0x30e0, 0x30e1, 0x30e2, 0x30e3, 0x30e5, 0x30e6, 0x30e7, 0x30e9, 0x30ea, 0x30eb, 0x30ec, 0x30ed, 0x30ef, 0x30f3, 0x30fb, 0x30fc,
0x4e00, 0x4e09, 0x4e0a, 0x4e0b, 0x4e0d, 0x4e0e, 0x4e13, 0x4e1c, 0x4e22, 0x4e25, 0x4e2a, 0x4e2d, 0x4e3a, 0x4e3b, 0x4e45, 0x4e49, 0x4e50,
0x4e57, 0x4e58, 0x4e73, 0x4e86, 0x4e88, 0x4e8c, 0x4e8e, 0x4e91, 0x4e94, 0x4e9b, 0x4ea4, 0x4eae, 0x4eba, 0x4ec5, 0x4eca, 0x4ece, 0x4ed6,
0x4ed8, 0x4ee5, 0x4ef6, 0x4ef7, 0x4efb, 0x4f11, 0x4f17, 0x4f18, 0x4f1a, 0x4f20, 0x4f30, 0x4f34, 0x4f4d, 0x4f4e, 0x4f53, 0x4f55, 0x4f59,
0x4f5c, 0x4f69, 0x4f73, 0x4f7f, 0x4f9d, 0x4fa1, 0x4fa6, 0x4fa7, 0x4fa7, 0x4fbf, 0x4fdd, 0x4fe1, 0x500b, 0x5012, 0x5024, 0x503c, 0x504f,
0x505c, 0x5065, 0x5074, 0x5074, 0x5099, 0x50a8, 0x5143, 0x5145, 0x5148, 0x5149, 0x5154, 0x5165, 0x5168, 0x516c, 0x516d, 0x5173, 0x5176,
0x5177, 0x5185, 0x518d, 0x51b3, 0x51b7, 0x51bb, 0x51c6, 0x51fa, 0x5206, 0x5206, 0x5206, 0x5206, 0x5206, 0x5207, 0x5212, 0x5217, 0x5219,
0x521a, 0x521a, 0x521d, 0x5220, 0x5225, 0x522b, 0x5230, 0x5236, 0x5237, 0x523b, 0x524a, 0x524d, 0x5269, 0x5272, 0x529b, 0x529f, 0x52a0,
0x52a8, 0x52a9, 0x52b9, 0x52d5, 0x52e4, 0x52fe, 0x52ff, 0x5316, 0x5317, 0x533a, 0x5343, 0x5347, 0x534a, 0x5355, 0x5357, 0x5358, 0x5360,
0x5361, 0x5373, 0x5382, 0x5386, 0x538b, 0x539f, 0x53bb, 0x53c2, 0x53cd, 0x53d1, 0x53d6, 0x53d7, 0x53d8, 0x53ea, 0x53ef, 0x53f0, 0x53f2,
0x53f3, 0x53f7, 0x5404, 0x5408, 0x540c, 0x540d, 0x540e, 0x5410, 0x5411, 0x5417, 0x5426, 0x5427, 0x542f, 0x5438, 0x5439, 0x544a, 0x5468,
0x547c, 0x548c, 0x54cd, 0x5546, 0x554f, 0x5584, 0x559d, 0x5668, 0x56db, 0x56de, 0x56e0, 0x56f2, 0x56f3, 0x56f4, 0x56fd, 0x56fe, 0x5706,
0x5708, 0x571f, 0x5727, 0x5728, 0x5730, 0x573a, 0x5740, 0x5747, 0x575a, 0x5761, 0x5782, 0x57df, 0x57fa, 0x5831, 0x5834, 0x5883, 0x5897,
0x589e, 0x58f0, 0x5907, 0x5909, 0x590d, 0x5916, 0x591a, 0x5927, 0x5929, 0x592a, 0x5931, 0x5939, 0x594f, 0x5973, 0x597d, 0x59cb, 0x59ff,
0x5b50, 0x5b57, 0x5b58, 0x5b66, 0x5b89, 0x5b89, 0x5b8c, 0x5b9a, 0x5b9d, 0x5b9f, 0x5ba4, 0x5bb6, 0x5bc6, 0x5bdd, 0x5bf9, 0x5bfc, 0x5bfe,
0x5c02, 0x5c06, 0x5c0f, 0x5c11, 0x5c16, 0x5c1a, 0x5c31, 0x5c3e, 0x5c4b, 0x5c4f, 0x5c65, 0x5c71, 0x5de5, 0x5de6, 0x5dee, 0x5df1, 0x5df2,
0x5e03, 0x5e26, 0x5e2e, 0x5e30, 0x5e38, 0x5e45, 0x5e55, 0x5e73, 0x5e74, 0x5e76, 0x5e8a, 0x5e8f, 0x5e94, 0x5e97, 0x5ea6, 0x5eb7, 0x5efa,
0x5f00, 0x5f02, 0x5f03, 0x5f0f, 0x5f15, 0x5f37, 0x5f3a, 0x5f53, 0x5f55, 0x5f62, 0x5f71, 0x5f80, 0x5f84, 0x5f85, 0x5f88, 0x5f8c, 0x5f92,
0x5f93, 0x5f97, 0x5fa9, 0x5faa, 0x5fc3, 0x5fc5, 0x5fdc, 0x5feb, 0x6001, 0x6027, 0x603b, 0x6062, 0x606f, 0x60a8, 0x60aa, 0x60c5, 0x60f3,
0x610f, 0x611b, 0x611f, 0x614b, 0x6162, 0x61a9, 0x61d0, 0x6210, 0x6216, 0x6234, 0x6237, 0x623b, 0x6240, 0x624b, 0x6253, 0x6255, 0x626b,
0x626d, 0x6270, 0x627e, 0x6297, 0x62ac, 0x62b5, 0x62bc, 0x62cd, 0x62d4, 0x62df, 0x6301, 0x6307, 0x6309, 0x6309, 0x632f, 0x6362, 0x636e,
0x6377, 0x6392, 0x639b, 0x63a2, 0x63a5, 0x63a5, 0x63a7, 0x63a8, 0x63d0, 0x641c, 0x6442, 0x6444, 0x6478, 0x64ad, 0x64ae, 0x64cd, 0x652f,
0x6539, 0x653e, 0x6548, 0x6557, 0x6570, 0x6574, 0x6587, 0x6599, 0x65ad, 0x65b0, 0x65b9, 0x65cb, 0x65e0, 0x65e2, 0x65e5, 0x65f6, 0x6607,
0x660e, 0x661f, 0x6620, 0x6628, 0x662f, 0x663c, 0x663e, 0x6642, 0x666e, 0x6674, 0x667a, 0x6682, 0x66b4, 0x66c7, 0x66dc, 0x66f2, 0x66f4,
0x66ff, 0x6700, 0x6708, 0x6709, 0x671f, 0x6728, 0x672a, 0x672c, 0x673a, 0x673a, 0x675f, 0x6761, 0x6765, 0x6771, 0x677e, 0x6781, 0x6790,
0x679c, 0x67c4, 0x67d3, 0x67e5, 0x6807, 0x6821, 0x6839, 0x683c, 0x6868, 0x68c0, 0x68c4, 0x691c, 0x692d, 0x6975, 0x697d, 0x6a19, 0x6a21,
0x6a5f, 0x6b20, 0x6b21, 0x6b3e, 0x6b47, 0x6b62, 0x6b63, 0x6b64, 0x6b65, 0x6b69, 0x6b74, 0x6b8b, 0x6bb5, 0x6bce, 0x6bcf, 0x6bd4, 0x6c14,
0x6c17, 0x6c27, 0x6c34, 0x6c42, 0x6c5a, 0x6c60, 0x6c61, 0x6cd5, 0x6ce2, 0x6ce8, 0x6cf3, 0x6d3b, 0x6d41, 0x6d45, 0x6d4b, 0x6d77, 0x6d88,
0x6df1, 0x6dfb, 0x6e05, 0x6e08, 0x6e10, 0x6e29, 0x6e2c, 0x6e38, 0x6e7f, 0x6e90, 0x6e96, 0x6ed1, 0x6f15, 0x706b, 0x706f, 0x70ad, 0x70b9,
0x70bc, 0x70ed, 0x7121, 0x7126, 0x7167, 0x7206, 0x722c, 0x7231, 0x7247, 0x7248, 0x7259, 0x7269, 0x72b6, 0x7372, 0x7387, 0x73af, 0x73b0,
0x73fe, 0x7406, 0x74b0, 0x751f, 0x7528, 0x7531, 0x7535, 0x7537, 0x753b, 0x754c, 0x7570, 0x75b2, 0x767a, 0x767b, 0x7684, 0x76ca, 0x76d1,
0x76d8, 0x76db, 0x76e4, 0x76ee, 0x76f4, 0x7701, 0x770b, 0x7720, 0x773c, 0x7740, 0x7761, 0x77ac, 0x77e5, 0x77e9, 0x77ed, 0x7801, 0x7834,
0x786c, 0x786e, 0x78b3, 0x78ba, 0x793a, 0x7981, 0x79bb, 0x79d2, 0x79f0, 0x79fb, 0x7a0b, 0x7a0d, 0x7a33, 0x7a4d, 0x7b2c, 0x7b49, 0x7b4b,
0x7b52, 0x7ba1, 0x7ba1, 0x7bc0, 0x7bc4, 0x7cbe, 0x7cfb, 0x7d1a, 0x7d20, 0x7d22, 0x7d2f, 0x7d30, 0x7d42, 0x7d44, 0x7d4c, 0x7d50, 0x7d66,
0x7d9a, 0x7dad, 0x7dcf, 0x7dd1, 0x7dda, 0x7de8, 0x7de9, 0x7e04, 0x7e3e, 0x7e70, 0x7ea2, 0x7ea7, 0x7ebf, 0x7ec3, 0x7ec4, 0x7ec8, 0x7eca,
0x7ecf, 0x7ed1, 0x7ed1, 0x7ed3, 0x7ed9, 0x7edc, 0x7edf, 0x7ee7, 0x7ee9, 0x7eed, 0x7ef3, 0x7ef4, 0x7eff, 0x7f13, 0x7f16, 0x7f29, 0x7f51,
0x7f57, 0x7f6e, 0x7ffb, 0x8001, 0x8005, 0x8010, 0x8017, 0x8054, 0x80aa, 0x80cc, 0x80fd, 0x8102, 0x8155, 0x81ea, 0x81f3, 0x81f4, 0x8212,
0x8217, 0x822a, 0x822c, 0x8239, 0x826f, 0x8272, 0x8282, 0x82e5, 0x82f1, 0x8303, 0x8377, 0x83b7, 0x843d, 0x84dd, 0x865a, 0x8702, 0x8840,
0x884c, 0x885b, 0x8861, 0x8865, 0x8868, 0x88c5, 0x88dc, 0x897f, 0x8981, 0x899a, 0x89c4, 0x89c9, 0x89d2, 0x89e3, 0x89e3, 0x89e6, 0x8a00,
0x8a08, 0x8a18, 0x8a2d, 0x8a3a, 0x8a55, 0x8a66, 0x8a73, 0x8a8d, 0x8a9e, 0x8aad, 0x8abf, 0x8b66, 0x8ba1, 0x8ba4, 0x8bad, 0x8bae, 0x8bb0,
0x8bbe, 0x8bc4, 0x8bd5, 0x8be5, 0x8be6, 0x8bed, 0x8bef, 0x8bf7, 0x8bfe, 0x8c03, 0x8c6a, 0x8ca0, 0x8cbb, 0x8cea, 0x8d1f, 0x8d25, 0x8d28,
0x8d44, 0x8d64, 0x8d70, 0x8d77, 0x8d85, 0x8d8a, 0x8d9f, 0x8db3, 0x8dc3, 0x8dd1, 0x8ddd, 0x8ddf, 0x8def, 0x8df3, 0x8e0f, 0x8e29, 0x8eab,
0x8eca, 0x8ee2, 0x8ef8, 0x8efd, 0x8f38, 0x8f66, 0x8f6c, 0x8f6e, 0x8f6f, 0x8f74, 0x8f7b, 0x8f7d, 0x8f83, 0x8f85, 0x8f91, 0x8f93, 0x8fbc,
0x8fbe, 0x8fc5, 0x8fc7, 0x8fd0, 0x8fd1, 0x8fd4, 0x8fd8, 0x8fdb, 0x8fdc, 0x8fde, 0x8fde, 0x8ffd, 0x9000, 0x9001, 0x9002, 0x9009, 0x9014,
0x901a, 0x901f, 0x9023, 0x9032, 0x9045, 0x904b, 0x904e, 0x9053, 0x9054, 0x9055, 0x9065, 0x9069, 0x9078, 0x90e8, 0x914d, 0x9178, 0x9192,
0x91cc, 0x91cd, 0x91ce, 0x91cf, 0x91d1, 0x9332, 0x949f, 0x94ae, 0x94c1, 0x94c3, 0x9501, 0x9519, 0x952e, 0x952e, 0x953b, 0x9577, 0x957f,
0x958b, 0x9593, 0x95be, 0x95ed, 0x95f4, 0x95f9, 0x9608, 0x9614, 0x9634, 0x9635, 0x9636, 0x963b, 0x9645, 0x964d, 0x9650, 0x9664, 0x9669,
0x968e, 0x968f, 0x969b, 0x96c6, 0x96e2, 0x96e8, 0x96ea, 0x96f6, 0x96f7, 0x96fb, 0x96fe, 0x9700, 0x9727, 0x973e, 0x9759, 0x975e, 0x9762,
0x97f3, 0x9805, 0x9806, 0x983b, 0x9875, 0x9876, 0x9879, 0x987a, 0x9884, 0x9886, 0x9891, 0x9898, 0x989c, 0x98a8, 0x98ce, 0x98fd, 0x990a,
0x9971, 0x9996, 0x99c6, 0x9a6c, 0x9a91, 0x9aa4, 0x9ad8, 0x9cf4, 0x9e23, 0x9ec4, 0x9ed8, 0xac00, 0xac01, 0xac04, 0xac10, 0xac12, 0xac15,
0xac1c, 0xac70, 0xac74, 0xac77, 0xac78, 0xac80, 0xac8c, 0xaca0, 0xaca9, 0xacb0, 0xacbd, 0xacc4, 0xace0, 0xace1, 0xace7, 0xacf5, 0xacfc,
0xad00, 0xad11, 0xad50, 0xad6c, 0xadc0, 0xade0, 0xadf8, 0xadfc, 0xae00, 0xae08, 0xae09, 0xae30, 0xae38, 0xae40, 0xae4a, 0xae4c, 0xae68,
0xaebc, 0xb044, 0xb04a, 0xb098, 0xb0a0, 0xb0a8, 0xb0ae, 0xb0b4, 0xb108, 0xb110, 0xb118, 0xb124, 0xb144, 0xb178, 0xb188, 0xb192, 0xb204,
0xb208, 0xb20c, 0xb290, 0xb294, 0xb298, 0xb2a5, 0xb2c8, 0xb2c8, 0xb2d8, 0xb2dd, 0xb2e4, 0xb2e8, 0xb2ec, 0xb2f9, 0xb300, 0xb354, 0xb358,
0xb370, 0xb3c4, 0xb3cc, 0xb3d9, 0xb418, 0xb41c, 0xb428, 0xb429, 0xb450, 0xb465, 0xb4a4, 0xb4dc, 0xb4e0, 0xb4e4, 0xb4f1, 0xb514, 0xb529,
0xb530, 0xb54c, 0xb77c, 0xb78c, 0xb791, 0xb799, 0xb7a9, 0xb7ad, 0xb7c9, 0xb7ec, 0xb7f0, 0xb808, 0xb824, 0xb825, 0xb828, 0xb85c, 0xb85d,
0xb860, 0xb864, 0xb86d, 0xb8cc, 0xb8e8, 0xb8f9, 0xb958, 0xb974, 0xb978, 0xb97c, 0xb984, 0xb9ac, 0xb9b4, 0xb9bc, 0xb9bd, 0xb9c1, 0xb9c8,
0xb9c9, 0xb9cc, 0xb9ce, 0xb9d0, 0xb9d1, 0xb9e4, 0xba38, 0xba40, 0xba54, 0xba74, 0xba85, 0xbaa8, 0xbaa9, 0xbab0, 0xbab8, 0xbb34, 0xbb38,
0xbb3c, 0xbbf8, 0xbbf9, 0xbc00, 0xbc0d, 0xbc0f, 0xbc14, 0xbc15, 0xbc18, 0xbc1b, 0xbc1c, 0xbc1d, 0xbc29, 0xbc30, 0xbc34, 0xbc38, 0xbc84,
0xbc88, 0xbc8c, 0xbc94, 0xbca8, 0xbcc0, 0xbcc4, 0xbcf4, 0xbcf5, 0xbcf8, 0xbd80, 0xbd81, 0xbd84, 0xbd88, 0xbe0c, 0xbe14, 0xbe44, 0xbe60,
0xbe68, 0xc068, 0xc0ac, 0xc0ad, 0xc0b0, 0xc0c1, 0xc0c8, 0xc0c9, 0xc0dd, 0xc11c, 0xc11d, 0xc120, 0xc124, 0xc12d, 0xc131, 0xc138, 0xc13c,
0xc158, 0xc18c, 0xc18d, 0xc190, 0xc1a1, 0xc218, 0xc26c, 0xc2a4, 0xc2ac, 0xc2b5, 0xc2b9, 0xc2dc, 0xc2dd, 0xc2e0, 0xc2e4, 0xc2ec, 0xc528,
0xc544, 0xc545, 0xc548, 0xc54a, 0xc54c, 0xc555, 0xc55e, 0xc560, 0xc571, 0xc57c, 0xc57d, 0xc595, 0xc5b4, 0xc5b8, 0xc5bc, 0xc5c5, 0xc5c6,
0xc5c8, 0xc5d0, 0xc5d8, 0xc5ec, 0xc5ed, 0xc5f0, 0xc5f4, 0xc5fc, 0xc600, 0xc601, 0xc608, 0xc624, 0xc628, 0xc62c, 0xc640, 0xc644, 0xc678,
0xc67c, 0xc694, 0xc6a9, 0xc6b0, 0xc6b4, 0xc6b8, 0xc6c0, 0xc6cc, 0xc6d0, 0xc6d4, 0xc6e8, 0xc704, 0xc720, 0xc728, 0xc73c, 0xc740, 0xc744,
0xc744, 0xc74c, 0xc758, 0xc774, 0xc778, 0xc77c, 0xc784, 0xc785, 0xc788, 0xc790, 0xc791, 0xc794, 0xc7a0, 0xc7a5, 0xc7ac, 0xc800, 0xc801,
0xc804, 0xc808, 0xc810, 0xc811, 0xc815, 0xc81c, 0xc82f, 0xc838, 0xc870, 0xc871, 0xc874, 0xc885, 0xc88b, 0xc88c, 0xc8fc, 0xc900, 0xc904,
0xc911, 0xc988, 0xc99d, 0xc9c0, 0xc9c1, 0xc9c4, 0xc9c8, 0xc9d0, 0xc9d1, 0xc9dc, 0xc9e7, 0xcabd, 0xcc28, 0xcc29, 0xcc3e, 0xcc98, 0xcc9c,
0xccab, 0xccb4, 0xcd08, 0xcd09, 0xcd1d, 0xcd5c, 0xcd94, 0xcd95, 0xcd9c, 0xcda9, 0xcde8, 0xce20, 0xce21, 0xce58, 0xce68, 0xce74, 0xce7c,
0xce94, 0xceec, 0xcf00, 0xcf1c, 0xcf54, 0xcfe8, 0xd06c, 0xd074, 0xd0a4, 0xd0b9, 0xd0c0, 0xd0c4, 0xd0c8, 0xd0dc, 0xd130, 0xd14c, 0xd15d,
0xd1a0, 0xd1a4, 0xd1b1, 0xd1b5, 0xd1f4, 0xd22c, 0xd2b8, 0xd2bc, 0xd2f0, 0xd30c, 0xd310, 0xd328, 0xd37c, 0xd398, 0xd3b8, 0xd3c9, 0xd3ec,
0xd3ed, 0xd3f0, 0xd45c, 0xd480, 0xd504, 0xd508, 0xd50c, 0xd544, 0xd558, 0xd559, 0xd55c, 0xd560, 0xd568, 0xd569, 0xd56d, 0xd574, 0xd588,
0xd589, 0xd5a5, 0xd5d8, 0xd604, 0xd608, 0xd615, 0xd638, 0xd648, 0xd654, 0xd655, 0xd658, 0xd65c, 0xd68c, 0xd68d, 0xd69f, 0xd6a8, 0xd6c4,
0xd6c8, 0xd720, 0xd734, 0xd750, 0xd761, 0xd788, 0xff01, 0xff05, 0xff06, 0xff08, 0xff09, 0xff0c, 0xff1a, 0xff1b, 0xff1b, 0xff1f, 