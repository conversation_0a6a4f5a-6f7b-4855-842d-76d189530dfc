/************************************************************************​
*Copyright(c) 2025, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#ifndef ALG_GRADE_H
#define ALG_GRADE_H

#ifdef __cplusplus
extern "C" {
#endif

#include "maf.h"

//坡度方向计算（1-上坡，0-平路，-1-下坡）
typedef struct _GradeDirCalculator
{
    maf_f32_t dalt_maf;                     //海拔变化量MAF
    float *buf;                             //缓冲区，保存平滑后的海拔变化量
    uint32_t capacity;                      //缓冲区容量
    uint32_t len;                           //缓冲区中数据数量
} GradeDirCalculator;

//坡度计算输入数据
typedef struct _GradeCalcInput
{
    uint32_t timestamp;                     //时间戳（s）
    float dist;                             //距离（m）
    float alt;                              //海拔（m），规定小于-999.0m为无效值
} GradeCalcInput;

//坡度计算输出数据
typedef struct _GradeCalcOutput
{
    float grade;                            //坡度（原始数值，比如0.043，即4.3%）
    float dalt;                             //海拔变化量（m）
} GradeCalcOutput;

//坡度计算差分数据
typedef struct _GradeDiffData
{
    float dd;                               //距离变化量（m）
    float dalt;                             //海拔变化量（m）
} GradeDiffData;

//坡度计算
typedef struct _GradeCalculator
{
    GradeDirCalculator dir_calculator;      //坡度方向计算
    GradeCalcInput last;                    //上一次的输入
    GradeDiffData *buf;                     //缓冲区，保存坡度计算用的差分数据
    uint32_t capacity;                      //缓冲区容量
    uint32_t len;                           //缓冲区中保存的数据数量
    float grade;                            //上一次计算出的坡度
    uint32_t timeout;                       //距离不变等异常场景超时时间（s）
    uint32_t timecnt;                       //超时计数
    uint32_t cnt;                           //执行计算
} GradeCalculator;

int grade_dir_calculator_exec(GradeDirCalculator *self, float dalt);

void grade_dir_calculator_reset(GradeDirCalculator *self);

int grade_calculator_exec(GradeCalculator *self, const GradeCalcInput *input, GradeCalcOutput *output);

void grade_calculator_reset(GradeCalculator *self);

#ifdef __cplusplus
}
#endif

#endif