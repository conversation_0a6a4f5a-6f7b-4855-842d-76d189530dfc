/************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   db_health_heartrate.c
@Time    :   2024/12/04 13:51:31
* 
************************************************************/
#include <stdio.h>
#include "db_health_heartrate.h"
#include "service_health.h"
#include "service_gui_health.h"
#include "service_config.h"
#include "qw_fs.h"
#include "sp_crc16.h"
#include "service_datetime.h"
#include "service_health_kv.h"
#include "gui_event_service.h"

static db_hr_cache_t g_hr_cache_data = {0};
static db_hr_resting_cache_t g_hr_resting_cache = {0};
static heartrate_minimax_t g_hr_minimax = {0};
static heartrate_minimax_t g_hr_minimax_invalid = {0};
static db_resting_history_t g_resting_history = {0};
static heartrate_t g_hr_last_manual = {0};
static heartrate_t g_hr_point = {0};
static db_hr_chart_t g_hr_char_data = {0};
static uint32_t g_last_hr_high_remind_ts = 0;
static uint32_t g_last_hr_low_remind_ts = 0;
static db_hr_remind_list_t g_hr_remind_data = {0};

static void db_health_heartrate_chart_statistic(void);

static void db_health_store_filepath_get(uint8_t *filepath, HLTH_FILE_TYPE_E data_type)
{
    if (HLTH_FILE_TYPE_HEATRATE == data_type) {
        snprintf((char *)filepath, HEALTH_FILE_NAMEMAX, "%s", HLTH_DIR_STORE_HR_POINT);
    } else if (HLTH_FILE_TYPE_HR_RESTING == data_type) {
        snprintf((char *)filepath, HEALTH_FILE_NAMEMAX, "%s", HLTH_DIR_STORE_HR_REST);
    } else if (HLTH_FILE_TYPE_SPO2 == data_type) {
        snprintf((char *)filepath, HEALTH_FILE_NAMEMAX, "%s", HLTH_DIR_STORE_SPO2_POINT);
    } else if (HLTH_FILE_TYPE_STRESS == data_type) {
        snprintf((char *)filepath, HEALTH_FILE_NAMEMAX, "%s", HLTH_DIR_STORE_STRESS_POINT);
    }
}

static uint32_t db_health_store_get_data_size(HLTH_FILE_TYPE_E data_type)
{
    uint32_t data_size = 0;

    if (HLTH_FILE_TYPE_HEATRATE == data_type) {
        data_size = sizeof(heartrate_t);
    } else if (HLTH_FILE_TYPE_HR_RESTING == data_type) {
        data_size = sizeof(hr_resting_t);
    } else if (HLTH_FILE_TYPE_SPO2 == data_type) {
        data_size = sizeof(spo2_data_t);
    } else if (HLTH_FILE_TYPE_STRESS == data_type) {
        data_size = sizeof(stress_t);
    }

    return data_size;
}

static uint32_t db_health_store_file_max_size(HLTH_FILE_TYPE_E data_type)
{
    uint32_t file_max_size = 0;

    if (HLTH_FILE_TYPE_HEATRATE == data_type) {
        file_max_size = HEALTH_HR_STORE_SIZE_MAX;
    } else if (HLTH_FILE_TYPE_HR_RESTING == data_type) {
        file_max_size = HEALTH_HR_RESTING_STORE_SIZE_MAX;
    } else if (HLTH_FILE_TYPE_SPO2 == data_type) {
        file_max_size = HEALTH_SPO2_STORE_SIZE_MAX;
    } else if (HLTH_FILE_TYPE_STRESS == data_type) {
        file_max_size = HEALTH_STRESS_STORE_SIZE_MAX;
    }

    return file_max_size;
}

uint16_t db_health_get_day_current_time_line_num(void)
{
    uint32_t gmt_time = service_datetime_get_gmt_time();
    uint32_t offset = service_datetime_get_curday_time_offset(gmt_time);
    uint16_t index = offset/ (HEALTH_ONE_MINUTE_SEC * 5) + 1;

    return index;
}

static void db_health_hr_minimax_update(const heartrate_period_t* data)
{
    if(NULL == data) return ;

    if(data->heartRate == 0 || data->timestamp == 0) {
        return;
    }

    if (data->heartRate > g_hr_minimax.max.heartRate) {
        memcpy(&g_hr_minimax.max, data, sizeof(heartrate_period_t));
        service_gui_set_hr_minimax(&g_hr_minimax);
    }
    if ((data->heartRate < g_hr_minimax.min.heartRate) || (0 == g_hr_minimax.min.heartRate)) {
        memcpy(&g_hr_minimax.min, data, sizeof(heartrate_period_t));
        service_gui_set_hr_minimax(&g_hr_minimax);
    }
}

bool db_health_cache_data_save(const char * filepath, void* data, uint16_t size, uint8_t type)
{
    if (size == 0 || data == NULL) {
        HEALTH_LOG_E("db sv no data");
        return true;
    }
    uint8_t* data_context = (uint8_t*)data;
    HEALTH_LOG_D("db hlth cache sv:%hu-%d", size, type);
    QW_FIL* fp = NULL;
    bool b_new_file = false;
    int ret = 0;
    ret = qw_f_open(&fp, filepath, QW_FA_OPEN_EXISTING | QW_FA_READ | QW_FA_WRITE);
    if (FR_OK != ret) {
        HEALTH_LOG_E("db hlth no file");
        if (ret == FR_NO_PATH) {
            if (!find_dir(HLTH_DIR_STORE_HR)) {
                HEALTH_LOG_E("mkdir hr sub dir error!");
                return false;
            } else {
                ret = qw_f_open(&fp, filepath, QW_FA_CREATE_NEW | QW_FA_READ | QW_FA_WRITE);
                if (FR_OK != ret) {
                    HEALTH_LOG_E("file %s open err failed %d", (const char *)filepath, ret);
                    return false;
                }
            }
        } else {
            return false;
        }
        b_new_file = true;
    }
    hlth_store_header_t header_data = {0};
    UINT w_bytes = 0;
    UINT r_bytes = 0;
    uint32_t store_offset;
    uint16_t crc_val = 0;
    if (!b_new_file) {
        qw_f_read(fp, &header_data, HEALTH_STORE_HEADER_SIZE, &r_bytes);
        if (r_bytes != HEALTH_STORE_HEADER_SIZE) {
            qw_f_close(fp);
            HEALTH_LOG_E("db hlth cache sv w failed");
            return false;
        }
        crc_val = sp_crc16((uint8_t *)&header_data, HEALTH_STORE_HEADER_SIZE - 2, CRC16_INITIAL);
        if (header_data.crc == crc_val) {
            store_offset = header_data.cur_offset;
        } else {
            store_offset = HEALTH_STORE_HEADER_SIZE;
            qw_f_close(fp);
            uint8_t filepath_tmp[HEALTH_FILE_NAMEMAX] = {0};
            db_health_store_filepath_get(filepath_tmp, type);
            qw_f_unlink((char *)filepath_tmp);
            ret = qw_f_open(&fp, (char *)filepath_tmp, QW_FA_CREATE_NEW | QW_FA_READ | QW_FA_WRITE);
            if (FR_OK != ret) {
                HEALTH_LOG_E("file error");
                return false;
            }
            HEALTH_LOG_E("db hlth cache sv op crc error");
        }
    } else {
        store_offset = HEALTH_STORE_HEADER_SIZE;
    }
    uint32_t data_size = db_health_store_get_data_size(type);
    uint32_t file_max = db_health_store_file_max_size(type);
    HEALTH_LOG_D("db hlth cache sv, offset:%u-%u-%u crc:%hu-%hu-%u", store_offset, data_size,
        file_max, header_data.crc, crc_val, b_new_file);
    if (data_size == 0) {
        qw_f_close(fp);
        HEALTH_LOG_D("db hlth cache sv data size 0");
        return false;
    }
    ret = qw_f_lseek(fp, store_offset);
    if (FR_OK != ret) {
        qw_f_close(fp);
        HEALTH_LOG_E("db hlth cache sv lseek error");
        return false;
    }
    if ((store_offset + size) <= file_max) {
        ret = qw_f_write(fp, data_context, size, &w_bytes);
        if (FR_OK != ret || w_bytes != size) {
            qw_f_close(fp);
            HEALTH_LOG_E("db hlth cache sv w error");
            return false;
        }
        store_offset += size;
    } else {
        uint16_t write_len = (uint16_t)(file_max - store_offset);
        if (write_len != 0) {
            ret = qw_f_write(fp, data_context, write_len, &w_bytes);
            if (FR_OK != ret || w_bytes != write_len) {
                qw_f_close(fp);
                HEALTH_LOG_E("db hlth cache sv w1 error");
                return false;
            }
        }
        w_bytes = 0;
        ret = qw_f_lseek(fp, HEALTH_STORE_HEADER_SIZE);
        if (FR_OK != ret) {
            qw_f_close(fp);
            HEALTH_LOG_E("db hlth cache sv w2 lseek error");
            return false;
        }
        uint16_t write_len2 = size - write_len;
        ret = qw_f_write(fp, &data_context[write_len / data_size], write_len2, &w_bytes);
        if (FR_OK != ret || write_len2 != w_bytes) {
            qw_f_close(fp);
            HEALTH_LOG_E("db hlth cache sv w2 failed");
            return false;
        }
        store_offset = write_len2 + HEALTH_STORE_HEADER_SIZE;
    }
    w_bytes = 0;
    ret = qw_f_lseek(fp, 0);
    if (FR_OK != ret) {
        qw_f_close(fp);
        HEALTH_LOG_E("db hlth cache sv lseek fail");
        return false;
    }
    header_data.cur_offset = store_offset;
    crc_val = sp_crc16((uint8_t *)&header_data, HEALTH_STORE_HEADER_SIZE - 2, CRC16_INITIAL);
    header_data.crc = crc_val;
    ret = qw_f_write(fp, &header_data, HEALTH_STORE_HEADER_SIZE, &w_bytes);
    if (FR_OK != ret || w_bytes != HEALTH_STORE_HEADER_SIZE) {
        qw_f_close(fp);
        HEALTH_LOG_E("db hlth cache sv w failed");
        return false;
    }
    qw_f_close(fp);
    HEALTH_LOG_D("db hlth cache sv OK:%u", store_offset);
    return true;
}

static bool db_health_hr_rest_insert_data(hr_resting_t* hr_resting, uint8_t day_index)
{
    if (hr_resting == NULL) {
        return false;
    }
    if (day_index > HEALTH_HR_RESTING_HISTORY_DAYS_MAX) {
        return false;
    }

    db_resting_day_t* p_day_rest = &g_resting_history.data[day_index];
    p_day_rest->timestamp = hr_resting->timestamp;
    p_day_rest->data = hr_resting->hr_resting;
    //HEALTH_LOG_D("read rest hr:%d-%d", day_index, hr_resting->hr_resting);

    return true;
}

static void db_health_hr_resting_historyinfo_gui_set(void)
{
    historyinfo_hr_resting_t historyinfo_hr_resting = {0}; // GUI show
    uint8_t tmp_avg;
    uint16_t tmp_sum = 0;
    uint8_t tmp_num = 0;
    db_resting_day_t* p_rest_day = NULL;
    for (uint8_t i = 0; i < HEALTH_HR_RESTING_HISTORY_DAYS_MAX; i++) {
        p_rest_day = &g_resting_history.data[i];
        if (p_rest_day->data != 0) {
            tmp_sum += p_rest_day->data;
            tmp_num++;
            historyinfo_hr_resting.hr_resting[i] = p_rest_day->data;
            //HEALTH_LOG_D("db hr rest hstry ui:%u,%u-%hu-%u", i, p_rest_day->data, tmp_sum, tmp_num);
        }
    }
    if (tmp_num != 0) {
        tmp_avg = tmp_sum / tmp_num;
        historyinfo_hr_resting.hr_resting_avg = tmp_avg;
        historyinfo_hr_resting.num = tmp_num;
    }
    service_gui_set_hr_resting_historyinfo(&historyinfo_hr_resting);
}

static void db_health_read_hr_resting_data_handle(hr_resting_t *data)
{
    if (data == NULL) {
        return;
    }
    qw_tm_t tmp_rtc = {0};
    qw_tm_t data_rtc = {0};
    uint32_t gmt = service_datetime_get_gmt_time();
    int32_t timezone = service_datetime_get_timezone();
    service_datetime_gmt2datetime(data->timestamp, &data_rtc, data->timezone);
    for (uint8_t i = 0; i < HEALTH_STORE_DAY_MAX; i++) {
        service_datetime_gmt2datetime(gmt - HEALTH_ONE_DAY_SECOND * i , &tmp_rtc, timezone);
        if ((tmp_rtc.tm_mday == data_rtc.tm_mday) &&
            (tmp_rtc.tm_mon == data_rtc.tm_mon) &&
            (tmp_rtc.tm_year == data_rtc.tm_year)) {
            db_health_hr_rest_insert_data(data, i);
        }
    }
}

static bool db_health_read_rest_data_from_file(void)
{
    QW_FIL* fp = NULL;
    int ret = 0;

    ret = qw_f_open(&fp, HLTH_DIR_STORE_HR_REST, QW_FA_OPEN_EXISTING | QW_FA_READ | QW_FA_WRITE);
    if (FR_OK != ret) {
        HEALTH_LOG_E("db hr rest r op E!");
        return false;
    }
    uint32_t file_size = qw_f_size(fp);
    if (file_size == 0) {
        HEALTH_LOG_E("db hr rest filesize:%d", file_size);
        qw_f_close(fp);
        return false;
    }

    UINT r_bytes = 0;
    hr_resting_t data = {0};
    uint8_t data_size = sizeof(hr_resting_t);
    hlth_store_header_t header_data = {0};
    qw_f_read(fp, &header_data, HEALTH_STORE_HEADER_SIZE, &r_bytes);
    if (r_bytes != HEALTH_STORE_HEADER_SIZE) {
        HEALTH_LOG_E("db hr rest r header E");
        qw_f_close(fp);
        return false;
    }
    uint16_t crc_val = sp_crc16((uint8_t *)&header_data, HEALTH_STORE_HEADER_SIZE - 2, CRC16_INITIAL);
    if (header_data.crc != crc_val) {
        qw_f_close(fp);
        qw_f_unlink(HLTH_DIR_STORE_HR_REST);
        HEALTH_LOG_E("db hr rest r CRC E");
        return false;
    }
    uint32_t r_offset = 0;
    if (file_size < HEALTH_HR_RESTING_STORE_SIZE_MAX) {
        r_offset = HEALTH_STORE_HEADER_SIZE;
    } else {
        if (header_data.cur_offset == HEALTH_HR_RESTING_STORE_SIZE_MAX) {
            r_offset = HEALTH_STORE_HEADER_SIZE;
        } else {
            r_offset = header_data.cur_offset;
        }
    }
    ret = qw_f_lseek(fp, r_offset);
    if (FR_OK != ret) {
        HEALTH_LOG_E("db hr rest r LSK E");
        qw_f_close(fp);
        return false;
    }
    uint32_t max_while = (HEALTH_HR_RESTING_STORE_SIZE_MAX - HEALTH_STORE_HEADER_SIZE) / data_size + 10;
    uint32_t count = 0;
    HEALTH_LOG_D("db hr rest r offset:%u-%u-%u", header_data.cur_offset, r_offset, max_while);
    do {
        if (count > max_while) {
            HEALTH_LOG_E("db hr rest r While E:%u", count);
            break;
        }
        count++;
        r_bytes = 0;
        qw_f_read(fp, &data, data_size, &r_bytes);
        if (r_bytes != data_size) {
            HEALTH_LOG_D("db hr rest r dt size:%d", r_bytes);
            break;
        }
        db_health_read_hr_resting_data_handle(&data);
        r_offset += data_size;
        if (r_offset == header_data.cur_offset) {
            HEALTH_LOG_D("db hr rest r over");
            break;
        } else if (r_offset >= HEALTH_HR_RESTING_STORE_SIZE_MAX) {
            r_offset = HEALTH_STORE_HEADER_SIZE;
            ret = qw_f_lseek(fp, HEALTH_STORE_HEADER_SIZE);
            if (FR_OK != ret) {
                HEALTH_LOG_E("db hr rest r LSK E");
                break;
            }
            HEALTH_LOG_D("db hr rest r tail");
        }
    } while(r_bytes > 0);
    qw_f_close(fp);

    return true;
}

bool db_health_hr_resting_cache_data_save(void)
{
    if (g_hr_resting_cache.num > HEALTH_HR_RESTING_CACHE_NUM_MAX) {
        memset(&g_hr_resting_cache, 0x00, sizeof(db_hr_resting_cache_t));
        HEALTH_LOG_E("db hr rest sv e:%u ", g_hr_resting_cache.num);
        return false;
    }
    bool ret = db_health_cache_data_save(HLTH_DIR_STORE_HR_REST, g_hr_resting_cache.data,
        g_hr_resting_cache.num * sizeof(hr_resting_t), HLTH_FILE_TYPE_HR_RESTING);
    memset(&g_hr_resting_cache, 0x00, sizeof(db_hr_resting_cache_t));
    HEALTH_LOG_D("db hr rest sv ret:%u-%u", ret, g_hr_resting_cache.num);
    return true;
}

void db_health_hr_resting_data_update(hr_resting_t* hr_resting)
{
    if (hr_resting == NULL) {
        return;
    }
    if (hr_resting->hr_resting < g_resting_history.data[0].data ||
        g_resting_history.data[0].data == 0) {
        HEALTH_LOG_D("db hr resting hr:%u ts:%d", hr_resting->hr_resting, hr_resting->timestamp);
        memcpy(&g_hr_resting_cache.data[0], hr_resting, sizeof(hr_resting_t));
        g_hr_resting_cache.num = 1;
        db_health_hr_rest_insert_data(hr_resting, 0);
        db_health_hr_resting_historyinfo_gui_set();
        service_gui_set_heartrate_resting(hr_resting->hr_resting);
    }
}

static void db_health_hr_rest_last_init(void)
{
    service_gui_set_heartrate_resting(0);
    db_resting_day_t* p_rest_day = &g_resting_history.data[0];
    if ((p_rest_day->data > HEALTH_HR_MIN) && (p_rest_day->data < HEALTH_HR_MAX)) {
        service_gui_set_heartrate_resting(p_rest_day->data);
    }
}

static void db_health_heartrate_resting_init(void)
{
    db_health_read_rest_data_from_file();
    db_health_hr_resting_historyinfo_gui_set();
    db_health_hr_rest_last_init();
}

static bool db_health_insert_one_hr_data(heartrate_period_t *data)
{
    if (data == NULL) {
        return false;
    }
    bool ret = false;
    db_hr_period_t* hr_period = NULL;
    int32_t offset = service_datetime_get_curday_time_offset(data->timestamp);
    uint16_t index = offset / (HEALTH_ONE_MINUTE_SEC * 5);
    // HEALTH_LOG_D("db hr insert hr index:%u, base:%d", index, offset);
    if ((data->heartRate >= HEALTH_HR_MIN) && (data->heartRate <= HEALTH_HR_MAX)) {
        if (index >= HEALTH_HR_GUI_LINE_NUM) {
            HEALTH_LOG_D("db hr insert out");
            return false;
        }
        hr_period = &g_hr_char_data.data[index];
        if (hr_period->data_len > DB_HR_VAL_NUM_MAX) {
            HEALTH_LOG_D("db hr insert len out");
            //return false;
        }
        if (hr_period->data_len == 0) {
            hr_period->min = 0xFF;
            hr_period->data[0].hr_data = data->heartRate;
            hr_period->data[0].mode = data->mode;
            hr_period->data_len++;
            ret = true;
        } else if (hr_period->data_len < DB_HR_VAL_NUM_MAX) {
            hr_period->data[hr_period->data_len].hr_data = data->heartRate;
            hr_period->data[hr_period->data_len].mode = data->mode;
            hr_period->data_len++;
            ret = true;
        } else if (hr_period->data_len >= DB_HR_VAL_NUM_MAX) {
            memmove(&hr_period->data[0], &hr_period->data[1], (DB_HR_VAL_NUM_MAX - 1)*sizeof(db_hr_minute_t));
            hr_period->data[DB_HR_VAL_NUM_MAX - 1].hr_data = data->heartRate;
            hr_period->data[DB_HR_VAL_NUM_MAX - 1].mode = data->mode;
            hr_period->data_len = DB_HR_VAL_NUM_MAX;
            ret = true;
        }
        hr_period->max = (data->heartRate > hr_period->max) ? data->heartRate : hr_period->max;
        hr_period->min = (data->heartRate < hr_period->min) ? data->heartRate : hr_period->min;
        hr_period->sum += data->heartRate;
        hr_period->num++;
        // Debug
        // for (uint8_t i = 0; i < hr_period->data_len; i++ ) {
        //     HEALTH_LOG_D("db_hr_data:%d", hr_period->data[i]);
        // }
        // HEALTH_LOG_D("db hr insert 1 hr num%d sum:%d, max:%d min:%d len:%d\n", hr_period->num, hr_period->sum,
            //hr_period->max, hr_period->min, hr_period->data_len);
    }

    return ret;
}

static void db_health_hr_point_recover_handle(heartrate_t *data)
{
    if (data == NULL) {
        return;
    }
    // HEALTH_LOG_D("db hr p recover:%u-%u-%hu", data->heartRate, data->timestamp, data->timezone);
    qw_tm_t data_rtc = {0};
    qw_tm_t cur_rtc = {0};
    uint32_t gmt = service_datetime_get_gmt_time();
    int32_t timezone = service_datetime_get_timezone();
    service_datetime_gmt2datetime(data->timestamp, &data_rtc, data->timezone);
    service_datetime_gmt2datetime(gmt, &cur_rtc, timezone);
    if ((cur_rtc.tm_mday == data_rtc.tm_mday) &&
        (cur_rtc.tm_mon == data_rtc.tm_mon) &&
        (cur_rtc.tm_year == data_rtc.tm_year)) {
        heartrate_period_t period_data = {0};
        period_data.heartRate = data->heartRate;
        period_data.timestamp = data->timestamp;
        period_data.timezone = data->timezone;
        period_data.mode = data->mode;
        db_health_hr_minimax_update(&period_data);
        db_health_insert_one_hr_data(&period_data);
        db_health_heartrate_chart_statistic();
        // HEALTH_LOG_D("db hr p recover:%u,%u-%u-%u-%u-%u\n", data->heartRate, data_rtc.tm_year, data_rtc.tm_mon, data_rtc.tm_mday, data_rtc.tm_hour, data_rtc.tm_min);
    }
}

static void db_health_read_point_data_from_file(void)
{
    QW_FIL* fp = NULL;
    int ret = 0;

    ret = qw_f_open(&fp, HLTH_DIR_STORE_HR_POINT, QW_FA_OPEN_EXISTING | QW_FA_READ | QW_FA_WRITE);
    if (FR_OK != ret) {
        HEALTH_LOG_E("db hr point r op E!");
        return;
    }
    uint32_t filesize = qw_f_size(fp);
    HEALTH_LOG_D("db hr p filesize:%d", filesize);
    if(filesize == 0) {
        qw_f_close(fp);
        return;
    }

    UINT r_bytes = 0;
    heartrate_t data = {0};
    uint8_t data_size = sizeof(heartrate_t);
    hlth_store_header_t header_data = {0};
    qw_f_read(fp, &header_data, HEALTH_STORE_HEADER_SIZE, &r_bytes);
    if (r_bytes != HEALTH_STORE_HEADER_SIZE) {
        qw_f_close(fp);
        HEALTH_LOG_E("db hr p r header E");
        return;
    }
    uint16_t crc_val = sp_crc16((uint8_t *)&header_data, HEALTH_STORE_HEADER_SIZE - 2, CRC16_INITIAL);
    if (header_data.crc != crc_val) {
        qw_f_close(fp);
        qw_f_unlink(HLTH_DIR_STORE_HR_POINT);
        HEALTH_LOG_E("db hr p r CRC E");
        return;
    }
    uint32_t r_offset = 0;
    if (filesize < HEALTH_HR_STORE_SIZE_MAX) {
        r_offset = HEALTH_STORE_HEADER_SIZE;
    } else {
        if (header_data.cur_offset == HEALTH_HR_STORE_SIZE_MAX) {
            r_offset = HEALTH_STORE_HEADER_SIZE;
        } else {
            r_offset = header_data.cur_offset;
        }
    }
    ret = qw_f_lseek(fp, r_offset);
    if (FR_OK != ret) {
        qw_f_close(fp);
        HEALTH_LOG_E("db hr p r LSK E");
        return;
    }
    uint32_t max_while = (HEALTH_HR_STORE_SIZE_MAX - HEALTH_STORE_HEADER_SIZE) / data_size + 10;
    uint32_t count = 0;
    HEALTH_LOG_D("db hr p r offset:%u-%u-%u-%u", header_data.cur_offset, r_offset, filesize, max_while);
    do {
        if (count > max_while) {
            HEALTH_LOG_E("db hr p r While E:%u", count);
            break;
        }
        count++;
        r_bytes = 0;
        qw_f_read(fp, &data, data_size, &r_bytes);
        if (r_bytes != data_size) {
            HEALTH_LOG_E("db hr p r dt size:%d", r_bytes);
            break;
        }
        db_health_hr_point_recover_handle(&data);
        r_offset += data_size;
        if (r_offset == header_data.cur_offset) {
            HEALTH_LOG_E("db hr p r over");
            break;
        } else if (r_offset >= HEALTH_HR_STORE_SIZE_MAX) {
            r_offset = HEALTH_STORE_HEADER_SIZE;
            ret = qw_f_lseek(fp, HEALTH_STORE_HEADER_SIZE);
            if (FR_OK != ret) {
                HEALTH_LOG_E("db hr p r LSK E");
                break;
            }
            HEALTH_LOG_E("db hr p r tail");
        }
    } while(r_bytes > 0);
    qw_f_close(fp);

    return;
}

bool db_health_heartrate_cache_data_update(hr_sensorhub_buf_t *hr_data)
{
    if (hr_data == NULL) {
        return false;
    }

    if (hr_data->num == 0) {
        HEALTH_LOG_E("db hr cache num = 0");
        return false;
    }

    heartrate_t hr = {0};
    heartrate_period_t period_hr = {0};
    hr.timezone = hr_data->timezone;
    period_hr.timezone = hr_data->timezone;
    uint32_t stime = hr_data->timestamp - ((hr_data->num - 1) * HEALTH_ONE_MINUTE_SEC);
    HEALTH_LOG_I("db hr cache update time:%u,buf num:%d",hr_data->timestamp, hr_data->num);
    for(uint8_t i = 0; i < hr_data->num; i++) {
        if(hr_data->data[i].hr == 0){
            continue;
        }
        period_hr.timestamp = stime + (i * HEALTH_ONE_MINUTE_SEC);
        period_hr.heartRate = hr_data->data[i].hr_max;
        db_health_hr_minimax_update(&period_hr);
        period_hr.heartRate = hr_data->data[i].hr_min;
        db_health_hr_minimax_update(&period_hr);
        hr.timestamp = period_hr.timestamp;
        hr.heartRate = period_hr.heartRate;
        hr.hr_max = hr_data->data[i].hr_max;
        hr.hr_min = hr_data->data[i].hr_min;
        if (g_hr_cache_data.num < HEARTRATE_CACHE_NUM_MAX) {
            memcpy(&g_hr_cache_data.hr_data[g_hr_cache_data.num], &hr, sizeof(heartrate_t));
            g_hr_cache_data.num++;
        }
        if (g_hr_cache_data.num >= HEARTRATE_CACHE_NUM_MAX) {
            db_health_hr_point_cache_save();
        }
    }

    return true;
}

static void db_health_heartrate_chart_statistic(void)
{
    uint32_t sum_value = 0;
    uint32_t sum_num = 0;
    uint8_t minDB = 255;
    uint8_t maxDB = 0;
    uint16_t num = db_health_get_day_current_time_line_num();
    if (num > HEALTH_HR_GUI_LINE_NUM) {
        HEALTH_LOG_D("db hr chart statistic out of size");
        return;
    }
    for (int i = 0; i < num; i++) {
        if (g_hr_char_data.data[i].max != 0) {
            maxDB = (g_hr_char_data.data[i].max > maxDB) ? g_hr_char_data.data[i].max : maxDB;
            minDB = (g_hr_char_data.data[i].min < minDB) ? g_hr_char_data.data[i].min : minDB;
            sum_value += g_hr_char_data.data[i].sum;
            sum_num += g_hr_char_data.data[i].num;
        }
    }
    if (sum_num != 0) {
        g_hr_char_data.hr_avg = (uint8_t)(sum_value / sum_num);
        g_hr_char_data.hr_max = maxDB;
        g_hr_char_data.hr_min = minDB;
    }

    return;
}

static void db_hr_chart_line_set(gui_hlth_hr_line_t* line_data, db_hr_period_t* hr_data)
{
    if (line_data == NULL || hr_data == NULL) {
        HEALTH_LOG_E("db chart line calc NULL");
        return;
    }
    if (hr_data->data_len == 0) {
        HEALTH_LOG_E("db hr char line len is 0");
        return;
    }
    if (hr_data->data_len > DB_HR_VAL_NUM_MAX) {
        HEALTH_LOG_E("db hr char line calc len out");
        return;
    }
    line_data->max = hr_data->max;
    line_data->min = hr_data->min;
    line_data->data = hr_data->hr_data;
}

static void db_health_heartrate_gui_data_notify(void)
{
#if 0
    gui_hlth_hr_24h_t gui_hr_data = {0};
    uint16_t num = db_health_get_day_current_time_line_num();
    // copy db gui data to gui data
    if (num > HEALTH_HR_GUI_LINE_NUM) {
        HEALTH_LOG_D("db hr gui notify index out");
        return;
    }
    for (uint16_t i = 0; i < num; i++) {
        if (g_hr_char_data.data[i].data_len != 0) {
            db_hr_chart_line_set(&gui_hr_data.data[i], &g_hr_char_data.data[i]);
        }
    }
    gui_hr_data.max = g_hr_char_data.hr_max;
    gui_hr_data.min = g_hr_char_data.hr_min;
    gui_hr_data.num = num;
    gui_hr_data.avg = g_hr_char_data.hr_avg;

    // set gui data and notify
    service_gui_set_heartrate_bar_chart(&gui_hr_data);
#endif
}

static void db_health_heartrate_gui_statistic()
{
    gui_hlth_hr_24h_t gui_hr_data = {0};
    uint16_t num = db_health_get_day_current_time_line_num();
    // copy db gui data to gui data
    if (num > HEALTH_HR_GUI_LINE_NUM) {
        HEALTH_LOG_D("db hr gui notify index out");
        return;
    }
    // 记录GUI288个心率数据及其模式
    for (uint16_t i = 0; i < num; i++) {
        if (g_hr_char_data.data[i].data_len != 0) {
            for(uint8_t j = 0; j < DB_HR_VAL_NUM_MAX; j++) {
                if(g_hr_char_data.data[i].data[j].hr_data == g_hr_minimax.max.heartRate) {
                    g_hr_char_data.data[i].mode = g_hr_minimax.max.mode;
                    g_hr_char_data.data[i].hr_data = g_hr_minimax.max.heartRate;
                    break;
                } else if(g_hr_char_data.data[i].data[j].hr_data == g_hr_minimax.min.heartRate) {
                    g_hr_char_data.data[i].mode = g_hr_minimax.min.mode;
                    g_hr_char_data.data[i].hr_data = g_hr_minimax.min.heartRate;
                    break;
                } else {
                    g_hr_char_data.data[i].mode = g_hr_char_data.data[i].data[g_hr_char_data.data[i].data_len-1].mode;
                    g_hr_char_data.data[i].hr_data =g_hr_char_data.data[i].data[g_hr_char_data.data[i].data_len-1].hr_data;
                }
            }
        }
    }
    // 插入心率平均数据，当模式为10分钟模式，且前面无值，则需要给前面一个值赋平均心率
    for (uint16_t i = 2; i < num; i++) {
        if (g_hr_char_data.data[i].hr_data != 0 && g_hr_char_data.data[i].mode == HR_MODE_10MIN) {
            if (g_hr_char_data.data[i-1].hr_data == 0 && g_hr_char_data.data[i-2].hr_data != 0) {
                g_hr_char_data.data[i-1].hr_data = (g_hr_char_data.data[i-2].hr_data+g_hr_char_data.data[i].hr_data)/2;
                if (g_hr_char_data.data[i-1].data_len == 0) {
                    g_hr_char_data.data[i-1].data_len++;
                }
                // HEALTH_LOG_D("data_len_%d %d", g_hr_char_data.data[i-1].data_len, i-1);
            }
        }
    }
    for (uint16_t i = 0; i < num; i++) {
        if (g_hr_char_data.data[i].data_len != 0) {
            db_hr_chart_line_set(&gui_hr_data.data[i], &g_hr_char_data.data[i]);
        }
        // HEALTH_LOG_D("g_hr_char_data.data[%d].hr_data:%d", i, g_hr_char_data.data[i].hr_data);
    }
    gui_hr_data.max = g_hr_char_data.hr_max;
    gui_hr_data.min = g_hr_char_data.hr_min;
    gui_hr_data.num = num;
    gui_hr_data.avg = g_hr_char_data.hr_avg;
    service_gui_set_heartrate_bar_chart(&gui_hr_data);
}

bool db_health_hr_point_update(heartrate_t *hr_data)
{
    if (hr_data == NULL) {
        return false;
    }
    //HEALTH_LOG_D("db hr p:%d-%u-%u", hr_data->heartRate, hr_data->timestamp, hr_data->timezone);
    if(hr_data->mode == 0) {
        service_gui_set_hr_manual(hr_data);
    }
    service_gui_set_heartrate(hr_data->heartRate);
    service_gui_set_last_heartrate(hr_data);
    memcpy(&g_hr_last_manual, hr_data, sizeof(heartrate_t));
    memcpy(&g_hr_point, hr_data, sizeof(heartrate_t));

    return true;
}

bool db_health_hr_point_measure_over(uint32_t param)
{
    if (g_hr_point.heartRate != 0) {
        heartrate_period_t hr_period_data = {0};
        hr_period_data.heartRate = g_hr_point.heartRate;
        hr_period_data.timestamp = g_hr_point.timestamp;
        hr_period_data.timezone = g_hr_point.timezone;
        hr_period_data.mode = g_hr_point.mode;
        db_health_hr_minimax_update(&hr_period_data);
        if (param == 1) {
            return true;
        }
        bool b_update = db_health_insert_one_hr_data(&hr_period_data);
        if (b_update) {
            db_health_heartrate_chart_statistic();
            db_health_heartrate_gui_statistic();
            // db_health_heartrate_gui_data_notify();
        }
        if(g_hr_cache_data.num == HEARTRATE_CACHE_NUM_MAX) 
        {
            bool ret = db_health_hr_point_cache_save();
            HEALTH_LOG_D("hr save ret:%d", ret);
        }
        if (g_hr_cache_data.num < HEARTRATE_CACHE_NUM_MAX) {
            memcpy(&g_hr_cache_data.hr_data[g_hr_cache_data.num], &g_hr_point, sizeof(heartrate_t));
            g_hr_cache_data.num++;
        }
        HEALTH_LOG_D("mea over:%u-%u-%hu",hr_period_data.heartRate, hr_period_data.timestamp, hr_period_data.timezone);
    }
    return true;
}

bool db_health_hr_point_cache_save(void)
{
    if(0 == g_hr_cache_data.num){
        return true;
    }

    if (g_hr_cache_data.num > HEARTRATE_CACHE_NUM_MAX) {
        memset(&g_hr_cache_data, 0x00, sizeof(db_hr_cache_t));
        HEALTH_LOG_E("db hr p sv e:%u", g_hr_cache_data.num);
        return false;
    }
    bool ret = db_health_cache_data_save(HLTH_DIR_STORE_HR_POINT, g_hr_cache_data.hr_data,
                                         g_hr_cache_data.num * sizeof(heartrate_t), HLTH_FILE_TYPE_HEATRATE);
    HEALTH_LOG_D("db hr p sv ret:%u-%u", ret, g_hr_cache_data.num);
    memset(&g_hr_cache_data, 0x00, sizeof(db_hr_cache_t));

    return true;
}

static void db_health_hr_last_save(void)
{
    db_health_data_write(HLTH_DIR_STORE_HR_LAST, &g_hr_last_manual, sizeof(heartrate_t));
}

static void db_health_hr_last_read(void)
{
    memset(&g_hr_last_manual, 0x00, sizeof(heartrate_t));
    db_health_data_read(HLTH_DIR_STORE_HR_LAST, &g_hr_last_manual, sizeof(heartrate_t));
    service_gui_set_hr_manual(&g_hr_last_manual);
}

static bool db_health_hr_remind_data_save(void)
{
    QW_FIL* fp = NULL;
    int ret = 0;
    UINT w_bytes = 0;
    ret = qw_f_open(&fp, HLTH_DIR_STORE_HR_REMIND, QW_FA_OPEN_ALWAYS | QW_FA_READ | QW_FA_WRITE);
    if(ret != 0) {
        memset(&g_hr_remind_data, 0x00, sizeof(db_hr_remind_list_t));
        HEALTH_LOG_E("db hr remind open file fail");
        return false;
    }
    ret = qw_f_write(fp, &g_hr_remind_data, sizeof(db_hr_remind_list_t), &w_bytes);
    if (ret != 0 || w_bytes != sizeof(db_hr_remind_list_t)) {
        qw_f_close(fp);
        memset(&g_hr_remind_data, 0x00, sizeof(db_hr_remind_list_t));
        HEALTH_LOG_E("db hr cahce data save w failed");
        return false;
    }
    qw_f_close(fp);
    HEALTH_LOG_D("db hr remind data save OK");
    return true;
}

static bool db_health_heartrate_remind_read_from_file(void)
{
    QW_FIL* fp = NULL;
    int ret = 0;

    ret = qw_f_open(&fp, HLTH_DIR_STORE_HR_REMIND, QW_FA_OPEN_EXISTING | QW_FA_READ);
    if(ret != 0) {
        HEALTH_LOG_E("db hr remind r file path open fail");
        return false;
    }
    uint32_t filesize = qw_f_size(fp);
    if(filesize == 0) {
        qw_f_close(fp);
        HEALTH_LOG_E("db hr remind r filesize fail");
        return false;
    }
    UINT r_bytes = 0;
    ret = qw_f_read(fp, &g_hr_remind_data, sizeof(db_hr_remind_list_t), &r_bytes);
    if (ret !=0 || r_bytes != sizeof(db_hr_remind_list_t)) {
        qw_f_close(fp);
        HEALTH_LOG_E("db hr remind data r_bytes < 0");
        return false;
    }
    qw_f_close(fp);
    HEALTH_LOG_D("db hr remind data r num:%d", g_hr_remind_data.num);

    return true;
}

static void db_health_hr_reminder_nofity(heartrate_remind_t* remind)
{
    if (remind == NULL) {
        HEALTH_LOG_E("db hr remind NULL");
        return;
    }
    remind->heartrate_threshold_max = hr_remind_high_value_get();
    remind->heartrate_threshold_min = hr_remind_low_value_get();
    static uint8_t type = 0;
    type = remind->heartrate_remind_type;
    HEALTH_LOG_D("gui remind type:%d", type);
    submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_HEART_RATE_NOTICE, (void*)&type);
}

void db_health_heartrate_remind(heartrate_remind_t* heartrate_remind)
{
    if(heartrate_remind == NULL) {
        HEALTH_LOG_D("db hr remind NULL");
        return;
    }
    heartrate_remind_t remind_data = {0};
    bool b_remind = false;
    uint32_t notice_time = service_datetime_get_gmt_time();
    memcpy(&remind_data, heartrate_remind, sizeof(heartrate_remind_t));
    remind_data.timestamp = notice_time;
    if (remind_data.heartrate_remind_type == HEARTRATE_REMIND_HIGH) {
        b_remind = ((notice_time - g_last_hr_high_remind_ts) > HEALTH_ONE_HOUR_SECOND) ? true : false;
        if (b_remind && (remind_data.heartrate_monitor_type == HEARTRATE_MONITOR_DAILY)) {
            g_last_hr_high_remind_ts = notice_time;
            if(g_hr_remind_data.num == HEALTH_HR_REMIND_NUM_MAX) {
                for(uint8_t i = 0; i < (HEALTH_HR_REMIND_NUM_MAX -1); i++) {
                    memcpy(&g_hr_remind_data.data[i], &g_hr_remind_data.data[i + 1], sizeof(heartrate_remind_t));
                }
            } else {
                g_hr_remind_data.num++;
            }
            memcpy(&g_hr_remind_data.data[g_hr_remind_data.num -1], heartrate_remind, sizeof(heartrate_remind_t));
            db_health_hr_remind_data_save();
            db_health_hr_reminder_nofity(&remind_data);
        }
    } else {
        b_remind = ((notice_time - g_last_hr_low_remind_ts) > HEALTH_ONE_HOUR_SECOND) ? true : false;
        if (b_remind && (remind_data.heartrate_monitor_type == HEARTRATE_MONITOR_DAILY)) {
            g_last_hr_low_remind_ts = notice_time;
            if(g_hr_remind_data.num == HEALTH_HR_REMIND_NUM_MAX) {
                for(uint8_t i = 0; i < (HEALTH_HR_REMIND_NUM_MAX -1); i++) {
                    memcpy(&g_hr_remind_data.data[i], &g_hr_remind_data.data[i + 1], sizeof(heartrate_remind_t));
                }
            } else {
                g_hr_remind_data.num++;
            }
            memcpy(&g_hr_remind_data.data[g_hr_remind_data.num -1], heartrate_remind, sizeof(heartrate_remind_t));
            db_health_hr_remind_data_save();
            db_health_hr_reminder_nofity(&remind_data);
        }
    }
    HEALTH_LOG_D("db hlth_hr remind cur time:%d, is remind:%d, remind type:%d", notice_time,
                 b_remind, remind_data.heartrate_remind_type);
}

void db_health_heartrate_remind_data_init(void)
{
    HEALTH_LOG_D("db_health_heartrate_remind_data_init");
    memset(&g_hr_remind_data, 0x00, sizeof(db_hr_remind_list_t));
    db_health_heartrate_remind_read_from_file();
    if (g_hr_remind_data.num != 0) {
        for (uint8_t i = 0; i < g_hr_remind_data.num; i++) {
            if (g_hr_remind_data.data[i].heartrate_remind_type == HEARTRATE_REMIND_HIGH) {
                g_last_hr_high_remind_ts = g_hr_remind_data.data[i].timestamp;
            } else if (g_hr_remind_data.data[i].heartrate_remind_type == HEARTRATE_REMIND_LOW) {
                g_last_hr_low_remind_ts = g_hr_remind_data.data[i].timestamp;
            }
        }
    }
}

static void db_health_heartrate_gui_data_init(void)
{
    HEALTH_LOG_D("db hr 24h graph init");
    db_health_read_point_data_from_file();
    db_health_heartrate_gui_statistic();
    // db_health_heartrate_gui_data_notify();
}

void db_health_hr_new_day_data_init(void)
{
    HEALTH_LOG_D("db hr 0:00 init");
    memset(&g_hr_char_data, 0x00, sizeof(db_hr_chart_t));
    db_health_hr_point_cache_save();
    db_health_hr_resting_cache_data_save();
    db_health_heartrate_chart_statistic();
    memset(&g_hr_minimax, 0, sizeof(heartrate_minimax_t));
    g_hr_minimax.max.heartRate = 0;
    g_hr_minimax.min.heartRate = 255;
    g_hr_minimax_invalid.max.heartRate = 0;
    g_hr_minimax_invalid.min.heartRate = 255;
    service_gui_set_hr_minimax(&g_hr_minimax);
    // db_health_heartrate_gui_data_notify();
    db_health_heartrate_gui_statistic();

    memset(&g_resting_history,0x00,sizeof(g_resting_history));
    db_health_heartrate_resting_init();
    service_gui_set_heartrate_resting(0);
}

void db_health_heartrate_data_init(void)
{
    //HEALTH_LOG_D("db hr init");
    memset(&g_hr_cache_data,0x00,sizeof(db_hr_cache_t));
    memset(&g_hr_resting_cache,0x00,sizeof(g_hr_resting_cache));
    memset(&g_resting_history,0x00,sizeof(g_resting_history));
    memset(&g_hr_minimax, 0, sizeof(heartrate_minimax_t));
    memset(&g_hr_char_data, 0x00, sizeof(db_hr_chart_t));
    g_hr_minimax.max.heartRate = 0;
    g_hr_minimax.min.heartRate = 255;
    g_hr_minimax_invalid.max.heartRate = 0;
    g_hr_minimax_invalid.min.heartRate = 255;

    db_health_heartrate_gui_data_init();
    db_health_heartrate_resting_init();
    db_health_heartrate_remind_data_init();
    db_health_hr_last_read();
}

void db_health_heartrate_data_deinit(void)
{
    HEALTH_LOG_D("db hr deinit");
    db_health_hr_point_cache_save();
    db_health_hr_resting_cache_data_save();
    db_health_hr_last_save();
}

void db_health_heartrate_data_reinit(void)
{
    HEALTH_LOG_D("db hr reinit");
    db_health_hr_point_cache_save();
    db_health_hr_resting_cache_data_save();
    db_health_hr_last_save();
    memset(&g_hr_char_data, 0x00, sizeof(db_hr_chart_t));
    memset(&g_hr_resting_cache,0x00,sizeof(g_hr_resting_cache));
    memset(&g_resting_history,0x00,sizeof(g_resting_history));
    memset(&g_hr_minimax, 0, sizeof(heartrate_minimax_t));
    g_hr_minimax.max.heartRate = 0;
    g_hr_minimax.min.heartRate = 255;
    g_hr_minimax_invalid.max.heartRate = 0;
    g_hr_minimax_invalid.min.heartRate = 255;
    service_gui_set_hr_minimax(&g_hr_minimax);
    db_health_heartrate_gui_data_init();
    db_health_heartrate_resting_init();
    db_health_hr_last_read();
}


