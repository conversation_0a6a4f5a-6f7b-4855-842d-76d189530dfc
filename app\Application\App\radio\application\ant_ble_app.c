/** 
 * @*************************************** Copyright (c) ***************************************
 * @                              <PERSON>han <PERSON>wu Technology Co., Ltd
 * @*********************************************************************************************
 * @Author: Jiang<PERSON>hen
 * @Date: 2021-11-11 10:01:20
 * @LastEditTime: 2021-12-24 11:23:12
 * @LastEditors: Mars
 * @FilePath: \iGS630_App\Application\App\radio\application\ant_ble_app.c
 * @Description: 
 * @*********************************************************************************************
 */
#include "ant_ble_app.h"
#include "ins_common.h"
#include "igs_global.h"
#include "cmsis_os2.h"
#include "system_utils.h"
#include "basic_app_module/ant_ble_sensor_ctl.h"
#include "qw_sensor_common.h"
#include "gui_event_service.h"

// extern osThreadId_t gui_task_thread_get(void);

// #define USING_SENSOR_CHECK

#ifdef USING_SENSOR_CHECK
uint32_t app_ble_update_tick = 0;
uint32_t sensor_update_timer[SENSOR_TYPE_LIGHT + 1];
static void ant_ble_sensor_status_check(sensor_type_t sensor_type);
#endif

static uint8_t sensor_disconnect_by_self_group[_SENSOR_TYPE_END];

void sensor_disconnect_by_self_info_reset(void)
{
	memset(sensor_disconnect_by_self_group, 0, sizeof(sensor_disconnect_by_self_group));
}

void sensor_disconnect_by_self_info_clear(sensor_type_t sensor_type)
{
	sensor_disconnect_by_self_group[sensor_type] = false;
}

void sensor_disconnect_by_self_info_set(sensor_type_t sensor_type)
{
	sensor_disconnect_by_self_group[sensor_type] = true;
}

bool sensor_disconnect_by_self_info_get(sensor_type_t sensor_type)
{
	return sensor_disconnect_by_self_group[sensor_type];
}

// //-------------------------------------------------------------------------------------------
// // Function Name : sensor_evt_handler
// // Purpose       : ANT事件回调函数
// // Param[in]     : uint16_t p_event          
// //                 dev_id_st *dev_id         
// //                 dev_type_st dev_type      
// //                 radio_type_st radio_type  
// //                 int16_t data              
// // Param[out]    : None
// // Return type   : static
// // Comment       : 2021-01-21
// //-------------------------------------------------------------------------------------------
// void sensor_evt_handler(uint16_t p_event, sensor_id_t *sensor_id, sensor_type_t sensor_type, sensor_radio_type_t sensor_radio_type, int16_t data)
// {
// 	if(sensor_search_mode_get() & SENSOR_SEARCH_MODE_FACTORY)
// 	{
// 		return;
// 	}
//     if(!Display_InitedOver())
//     {
//         return;
//     }
//     switch(p_event)   
//     {
// 		case  EVENT_SENSOR_CONNECT_STATUS_CHANGE:
// 		#ifdef USING_SENSOR_CHECK
// 			ant_ble_sensor_status_check(sensor_type);
// 		#endif
// 			if(data)
// 			{
// 				sensor_disconnect_by_self_info_clear(sensor_type);
// 			}
// 			else
// 			{
// 				if (sensor_close_reason_is_sync_file())
// 				{
// 					sensor_disconnect_by_self_info_set(sensor_type);
// 				}
// 			}
// 			if (gui_task_thread_get())
// 			{
// 				//蓝牙传感器连接失败则从搜索列表删除等待再次搜索到
// 				if (!data && sensor_id)// && sensor_radio_type == SENSOR_RADIO_TYPE_BLE)
// 				{
// 					sensor_search_infor_t fail_info;
// 					fail_info.radio_type = sensor_radio_type;
// 					fail_info.sensor_type = sensor_type;
// 					memcpy(&fail_info.sensor_id, sensor_id, sizeof(sensor_id_t));
// 					sensor_search_infor_del(&fail_info);
// 					gui_event_submit(enumGUI_SENSOR_CHANGED_EVENT);
// 				}
// 			}
// 			break;
//         case EVENT_SENSOR_STATE_CHANGE:
//         	if(gui_task_thread_get()){
// //        		printf_ant_ble_search_information();
// //        		printf_ant_ble_saved_information();
//                 //应用使用system_bindlistener监听该事件，当手动模式或app模式时，保存的列表和搜索列表变化时会像gui线程发出该事件
//                 gui_event_submit(enumGUI_SENSOR_CHANGED_EVENT);
//         	}
//         	break;
//         case EVNET_SENOSR_DI2_CTRL:
//         	if(gui_task_thread_get()){
// 				//应用使用system_bindlistener监听该事件，当手动模式或app模式时，di2按键触发时会像gui线程发出该事件
//         		gui_command_submit(enumGUICMD_DI2_CMD, (void*)((uint32_t)data));
// 			}
//         	break;
//         case EVENT_SENSOR_BPWR_CALIB: // 校准超时
//         {
//             // EventData *e_data = message_allocEventData();
//             // message_eventDataInit(e_data,data,0,NULL);
//             // system_send_event(gui_task_thread_get(),enumPOPUP_SENSOR_BPWR_CALIB,e_data);
//             // gui_popup_calib_submit(enumPOPUP_CALIBRATION_FAIL, data); //底层超时机制时间不可控，这里不再处理
//         }
//         break;
//         case EVENT_SENSOR_BPWR_CALIB_SUCCESS:
// 			{
//                 gui_popup_calib_submit(enumPOPUP_CALIBRATION_SUCC, data);
// 			}
//         	break;
//         case EVENT_SENSOR_BPWR_CALIB_FAILED:
// 			{
//                 gui_popup_calib_submit(enumPOPUP_CALIBRATION_FAIL, data);
// 			}
//         	break;
//         case EVENT_SENSOR_RADAR_WARNING:
// 			{
//         		gui_command_submit(enumGUICMD_RADAR_SHOW, (void*)((uint32_t)data));
// 			}
//         	break;
//         case EVENT_SENSOR_LOW_POWER:
// 			{
// 			    EventData* pEvent = message_allocEventData();
// 			    message_eventDataInit(pEvent, enumPOPUP_BATTERY_LOW_SENSOR, sensor_type, NULL);
// 			    system_send_event(gui_task_thread_get(), enumGUI_POPUP_EVENT, pEvent);

// 			    gui_popup_sound_submit(enumPOPUP_BATTERY_LOW_SENSOR);
// 			}
//         	break;
//         case EVENT_SENSOR_DATA_UPDATE:
// 		#ifdef USING_SENSOR_CHECK
//         	sensor_update_timer[sensor_type] = app_ble_update_tick;
// 		#endif
//         	break;
// 		case EVENT_SENSOR_MANUAL_CONNECT_STATUS:
// 			if(!sensor_disconnect_by_self_info_get(sensor_type))
// 			{
// 				gui_msg_data_submit(enumPOPUP_SENSOR_CONNECT_RESULT, (uint32_t)sensor_type + (data > 0 ? 0x8000 : 0));
// 			}
// 			break;
//     }     
// }

//-------------------------------------------------------------------------------------------
// Function Name : ble_peripheral_evt_abserve
// Purpose       : 蓝牙从设备事件响应函数
// Param[in]     : ble_periph_evt_st p_event  
//                 void *p_context            
// Param[out]    : None
// Return type   : static
// Comment       : 2021-01-26
//-------------------------------------------------------------------------------------------
void ble_peripheral_evt_abserve(ble_periph_evt_st p_event, void *p_context)
{
	uint8_t *title = NULL;
	//TODO 建议把这些事件，通过参数直接发送过去
		
    switch (p_event)
    {
        case BLE_PERIPH_ANDROID_INCOMING_ADD_EVT:    //安卓来电
		case BLE_PERIPH_IOS_INCOMING_ADD_EVT:     //IOS来电
			submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_INCOMING_CALL, p_context);
			break;
        case BLE_PERIPH_ANDROID_INCOMING_REMOVE_EVT:
        case BLE_PERIPH_IOS_INCOMING_REMOVE_EVT:   
			submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_CLOSE, (void *)enumPOPUP_INCOMING_CALL);
        	break;
		case BLE_PERIPH_ANDROID_SOCIAL_EVT_ADD_EVT:   //安卓手机短信或者APP
		case BLE_PERIPH_IOS_SOCIAL_EVT_ADD_EVT:       //IOS手机短信或者APP
			submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_INTELLIGENT_NOTIFY, p_context);
        	break;
		case BLE_PERIPH_PAIR_CODE_EVT:
			break;
        default:
            break;
    }
}

void ant_ble_sensor_disconnect_check(void)
{
#ifdef USING_SENSOR_CHECK
	app_ble_update_tick ++;
	for(uint16_t i = 0; i <= SENSOR_TYPE_LIGHT; i++)
	{
		if(sensor_update_timer[i] != 0xffffffff){
			if((app_ble_update_tick - sensor_update_timer[i]) >= 120){
				// sensor_close_direct(i);
				sensor_update_timer[i] = 0xffffffff;
			}
		}
	}
#endif
}

#ifdef USING_SENSOR_CHECK
static void ant_ble_sensor_status_check(sensor_type_t sensor_type)
{
	if(sensor_type == SENSOR_TYPE_HRM || \
		sensor_type == SENSOR_TYPE_CBSC || \
		sensor_type == SENSOR_TYPE_BPWR || \
		sensor_type == SENSOR_TYPE_CAD || \
		sensor_type == SENSOR_TYPE_SPD)
	{
	    sensor_connect_infor_t sensor_connect;
		bool found = sensor_connect_infor_get(sensor_type, &sensor_connect);
		if(found){
			if(sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED){
				sensor_update_timer[sensor_type] = app_ble_update_tick;
			}else if(SENSOR_CONNECT_STATE_IDLE == sensor_connect.state || \
					sensor_connect.state == SENSOR_CONNECT_STATE_IDLE_UN){
				sensor_update_timer[sensor_type] = 0xffffffff;
			}
		}
	}
}

int ant_ble_app_data_init(void)
{
	for(uint16_t i = 0; i <= SENSOR_TYPE_LIGHT; i++)
	{
		sensor_update_timer[i] = 0xffffffff;
	}
	return 0;
}
INIT_PRE_APP_EXPORT(ant_ble_app_data_init);
#endif
