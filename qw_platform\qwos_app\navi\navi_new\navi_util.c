#include <math.h>
#include <stddef.h>
#include <stdbool.h>
#include "navi_util.h"

#define M_PI       3.14159265358979323846
#define ONE_DEGREE_DIST 111000.0                //赤道上1°对应的距离

//角度转弧度
static inline double radians(double degree)
{
    return degree * M_PI / 180.0;
}

//弧度转角度
static inline double degrees(double radian)
{
    return radian * 180.0 / M_PI;
}

//检查输入的经纬度是否有效
//true - 有效
//false - 无效
uint8_t navi_util_is_coord_valid(double lng, double lat)
{
    if (lng < -180.0 || lng > 180.0 || lat < -90.0 || lat > 90.0)
    {
        return false;
    }
    else
    {
        return true;
    }
}

//检查输入的航向是否有效
//航向定义为：以正北为基准，顺时针为正，逆时针为负，故范围为[-180°,+180°]
//true - 有效
//false - 无效
uint8_t navi_util_is_course_valid(float course)
{
    if (course < -180.0f || course > 180.0f)
    {
        return false;
    }
    else
    {
        return true;
    }
}

//检查两个经纬度坐标是否相同
//相同定义为：两个经度和纬度相差均小于1.0e-7，从而距离相差不超过一二十厘米
//true - 相同
//false - 不相同
uint8_t navi_util_is_coord_same(double lng1, double lat1, double lng2, double lat2)
{
    if (fabs(lng1 - lng2) < 1.0e-7 && fabs(lat1 - lat2) < 1.0e-7)
    {
        return true;
    }
    else
    {
        return false;
    }
}

//计算两个经纬度坐标构成的直线的航向
//航向定义为：以第一个经纬度坐标为原点，以正北为基准，顺时针为正，逆时针为负，从正北旋转经过第二个经纬度坐标时的旋转角度
//返回值：航向角度（°）
float navi_util_course_calc(double lng1, double lat1, double lng2, double lat2)
{
    float course = 0.0f;

    if (navi_util_is_coord_valid(lng1, lat1) == false || navi_util_is_coord_valid(lng2, lat2) == false)
    {
        return course;
    }

    if (navi_util_is_coord_same(lng1, lat1, lng2, lat2) == true)
    {
        return course;
    }

    const double rad_lng1 = radians(lng1);
    const double rad_lat1 = radians(lat1);
    const double rad_lng2 = radians(lng2);
    const double rad_lat2 = radians(lat2);

    course = (float)(degrees(atan2((rad_lng2 - rad_lng1) * cos(rad_lat2), rad_lat2 - rad_lat1)));

    return course;
}

//计算给定航向反转后的航向
//反转定义为：原航向是从a点到b点的航向，则反转后的航向是b点到a点的航向
//返回值：航向角度（°）
float navi_util_course_reverse_calc(float course)
{
    float course_reverse = 0.0f;

    if (course >= 0.0f)
    {
        course_reverse = course - 180.0f;
    }
    else
    {
        course_reverse = course + 180.0f;
    }

    return course_reverse;
}

//计算两个航向之间的角度
//角度定义为：从第一个航向以较小角度旋转到第二个航向时的旋转角度，顺时针为正，逆时针为负
//返回值：角度（°）
float navi_util_course_angle_calc(float course1, float course2)
{
    if (navi_util_is_course_valid(course1) == false || navi_util_is_course_valid(course2) == false)
    {
        return 0.0f;
    }

    float angle = fmodf(course2 - course1 + 360.0f, 360.0f);

    if (angle >= 180.0f)
    {
        angle -= 360.0f;
    }

    return angle;
}

//计算两个航向之间的角度差
//角度差定义为：从第一个航向以较小角度旋转到第二个航向时，旋转角度的变化量，根据定义，变化量一定为正值
//返回值：角度差（°）
float navi_util_course_diff_calc(float course1, float course2)
{
    float angle = navi_util_course_angle_calc(course1, course2);

    return fabsf(angle);
}

//计算两个经纬度坐标构成的线段的近似距离
//返回值：距离（m）
float navi_util_seg_dist_calc(double lng1, double lat1, double lng2, double lat2)
{
    float dist = 0.0f;

    if (navi_util_is_coord_valid(lng1, lat1) == false || navi_util_is_coord_valid(lng2, lat2) == false)
    {
        return dist;
    }

    if (navi_util_is_coord_same(lng1, lat1, lng2, lat2) == true)
    {
        return dist;
    }

    const double rad_lat2 = radians(lat2);

    const double dx = (lng2 - lng1) * cos(rad_lat2) * ONE_DEGREE_DIST;
    const double dy = (lat2 - lat1) * ONE_DEGREE_DIST;

    dist = (float)sqrt(dx * dx + dy * dy);

    return dist;
}

//计算到给定经纬度坐标的距离等于给定距离时，经度和纬度的变化量
void navi_util_pos_dxdy_calc(double lng, double lat, float dist, double *dx, double *dy)
{
    if (navi_util_is_coord_valid(lng, lat) == false || dist < 1.0f)
    {
        *dx = 0.0;
        *dy = 0.0;
        return;
    }

    const double rad_lat = radians(lat);
    const double dist_d = (double)dist;

    *dy = dist_d / ONE_DEGREE_DIST;
    *dx = dist_d / (cos(rad_lat) * ONE_DEGREE_DIST);
}

//计算一点到另外两点构成的线段的最小距离
//如果该点向线段的投影点在线段上，则最小距离为垂直距离，否则直接计算该点到线段起点的距离
//返回值：距离（m）
float navi_util_p2seg_dist_calc(double lng, double lat, double lng1, double lat1, double lng2, double lat2)
{
    float dist = 0.0f;

    if (navi_util_is_coord_valid(lng, lat) == false || navi_util_is_coord_valid(lng1, lat1) == false || navi_util_is_coord_valid(lng2, lat2) == false)
    {
        return dist;
    }

    if (navi_util_is_coord_same(lng1, lat1, lng2, lat2) == true)
    {
        //TODO return dist from (lng, lat) to (lng1, lat1)?
        return dist;
    }

    //投影点
    double lng_d = 0.0;
    double lat_d = 0.0;

    //线段位于经度线或纬度线，则投影点必定位于经度线或纬度线上
    if (fabs(lng1 - lng2) < 1.0e-7)
    {
        lng_d = lng1;
        lat_d = lat;
    }
    else if (fabs(lat1 - lat2) < 1.0e-7)
    {
        lng_d = lng;
        lat_d = lat1;
    }
    else
    {
        //TODO use another fomula: intersection of two line?
        const double A = lat2 - lat1;
        const double B = lng1 - lng2;
        const double C = lng2 * lat1 - lng1 * lat2;
        const double A2 = A * A;
        const double B2 = B * B;
        const double AB = A * B;
        const double AC = A * C;
        const double BC = B * C;
        lng_d = (B2 * lng - AB * lat - AC) / (A2 + B2);
        lat_d = (A2 * lat - AB * lng - BC) / (A2 + B2);
    }

    //检查投影点是否位于线段上，如果位于的话，则投影点必定位于线段的bbox中
    double lng_min = 0.0;
    double lng_max = 0.0;
    double lat_min = 0.0;
    double lat_max = 0.0;

    if (lng1 > lng2)
    {
        lng_max = lng1;
        lng_min = lng2;
    }
    else
    {
        lng_max = lng2;
        lng_min = lng1;
    }

    if (lat1 > lat2)
    {
        lat_max = lat1;
        lat_min = lat2;
    }
    else
    {
        lat_max = lat2;
        lat_min = lat1;
    }

    if (lng_d >= lng_min && lng_d <= lng_max && lat_d >= lat_min && lat_d <= lat_max)
    {
        dist = navi_util_seg_dist_calc(lng, lat, lng_d, lat_d);
    }
    else
    {
        dist = navi_util_seg_dist_calc(lng, lat, lng1, lat1);
    }

    return dist;
}

//计算一点到两外两点构成的直线的投影点
int navi_util_p2seg_coord_calc(double lng, double lat, double lng1, double lat1, double lng2, double lat2, double *lng_d, double *lat_d)
{
    if (lng_d == NULL || lat_d == NULL)
    {
        return -1;
    }

    if (navi_util_is_coord_valid(lng, lat) == false || navi_util_is_coord_valid(lng1, lat1) == false || navi_util_is_coord_valid(lng2, lat2) == false)
    {
        *lng_d = lng1;
        *lat_d = lat1;
    }

    if (navi_util_is_coord_same(lng1, lat1, lng2, lat2) == true)
    {
        *lng_d = lng1;
        *lat_d = lat1;
    }

    //线段位于经度线或纬度线，则投影点必定位于经度线或纬度线上
    if (fabs(lng1 - lng2) < 1.0e-7)
    {
        *lng_d = lng1;
        *lat_d = lat;
    }
    else if (fabs(lat1 - lat2) < 1.0e-7)
    {
        *lng_d = lng;
        *lat_d = lat1;
    }
    else
    {
        //TODO use another fomula: intersection of two line?
        const double A = lat2 - lat1;
        const double B = lng1 - lng2;
        const double C = lng2 * lat1 - lng1 * lat2;
        const double A2 = A * A;
        const double B2 = B * B;
        const double AB = A * B;
        const double AC = A * C;
        const double BC = B * C;
        *lng_d = (B2 * lng - AB * lat - AC) / (A2 + B2);
        *lat_d = (A2 * lat - AB * lng - BC) / (A2 + B2);
    }

    return 0;
}

//给定三角形的三条边的边长，计算底边的高
//主要用于计算点到线段的垂直距离，其中投影点可以位于线段外
//返回值：距离（m）
float navi_util_triangle_h_calc(float a, float b, float c)
{
    //a为底边
    float h = 0.0f;
    if (a > 0.0f && b > 0.0f && c > 0.0f)
    {
        const float s = (a + b + c) * (a + b - c) * (a + c - b) * (b + c - a);
        if (s > 0.0f)
        {
            h = sqrtf(s) / (a * 2.0f);
        }
    }

    return h;
}
