.\" DO NOT MODIFY THIS FILE!  It was generated by help2man.
.TH REPO "1" "April 2025" "repo manifest" "Repo Manual"
.SH NAME
repo \- repo manifest - manual page for repo manifest
.SH SYNOPSIS
.B repo
\fI\,manifest \/\fR[\fI\,-o {-|NAME.xml}\/\fR] [\fI\,-m MANIFEST.xml\/\fR] [\fI\,-r\/\fR]
.SH DESCRIPTION
Summary
.PP
Manifest inspection utility
.SH OPTIONS
.TP
\fB\-h\fR, \fB\-\-help\fR
show this help message and exit
.TP
\fB\-r\fR, \fB\-\-revision\-as\-HEAD\fR
save revisions as current HEAD
.TP
\fB\-m\fR NAME.xml, \fB\-\-manifest\-name\fR=\fI\,NAME\/\fR.xml
temporary manifest to use for this sync
.TP
\fB\-\-suppress\-upstream\-revision\fR
if in \fB\-r\fR mode, do not write the upstream field (only
of use if the branch names for a sha1 manifest are
sensitive)
.TP
\fB\-\-suppress\-dest\-branch\fR
if in \fB\-r\fR mode, do not write the dest\-branch field
(only of use if the branch names for a sha1 manifest
are sensitive)
.TP
\fB\-\-format\fR=\fI\,FORMAT\/\fR
output format: xml, json (default: xml)
.TP
\fB\-\-pretty\fR
format output for humans to read
.TP
\fB\-\-no\-local\-manifests\fR
ignore local manifests
.TP
\fB\-o\fR \-|NAME.xml, \fB\-\-output\-file\fR=\fI\,\-\/\fR|NAME.xml
file to save the manifest to. (Filename prefix for
multi\-tree.)
.SS Logging options:
.TP
\fB\-v\fR, \fB\-\-verbose\fR
show all output
.TP
\fB\-q\fR, \fB\-\-quiet\fR
only show errors
.SS Multi\-manifest options:
.TP
\fB\-\-outer\-manifest\fR
operate starting at the outermost manifest
.TP
\fB\-\-no\-outer\-manifest\fR
do not operate on outer manifests
.TP
\fB\-\-this\-manifest\-only\fR
only operate on this (sub)manifest
.TP
\fB\-\-no\-this\-manifest\-only\fR, \fB\-\-all\-manifests\fR
operate on this manifest and its submanifests
.PP
Run `repo help manifest` to view the detailed manual.
.SH DETAILS
.PP
With the \fB\-o\fR option, exports the current manifest for inspection. The manifest
and (if present) local_manifests/ are combined together to produce a single
manifest file. This file can be stored in a Git repository for use during future
\&'repo init' invocations.
.PP
The \fB\-r\fR option can be used to generate a manifest file with project revisions set
to the current commit hash. These are known as "revision locked manifests", as
they don't follow a particular branch. In this case, the 'upstream' attribute is
set to the ref we were on when the manifest was generated. The 'dest\-branch'
attribute is set to indicate the remote ref to push changes to via 'repo
upload'.
.PP
Multiple output formats are supported via \fB\-\-format\fR. The default output is XML,
and formats are generally "condensed". Use \fB\-\-pretty\fR for more human\-readable
variations.
.PP
repo Manifest Format
.PP
A repo manifest describes the structure of a repo client; that is the
directories that are visible and where they should be obtained from with git.
.PP
The basic structure of a manifest is a bare Git repository holding a single
`default.xml` XML file in the top level directory.
.PP
Manifests are inherently version controlled, since they are kept within a Git
repository. Updates to manifests are automatically obtained by clients during
`repo sync`.
.PP
[TOC]
.PP
XML File Format
.PP
A manifest XML file (e.g. `default.xml`) roughly conforms to the following DTD:
.PP
```xml <!DOCTYPE manifest [
.TP
<!ELEMENT manifest (notice?,
remote*,
default?,
manifest\-server?,
submanifest*?,
remove\-project*,
project*,
extend\-project*,
repo\-hooks?,
superproject?,
contactinfo?,
include*)>
.IP
<!ELEMENT notice (#PCDATA)>
.IP
<!ELEMENT remote (annotation*)>
<!ATTLIST remote name         ID    #REQUIRED>
<!ATTLIST remote alias        CDATA #IMPLIED>
<!ATTLIST remote fetch        CDATA #REQUIRED>
<!ATTLIST remote pushurl      CDATA #IMPLIED>
<!ATTLIST remote review       CDATA #IMPLIED>
<!ATTLIST remote revision     CDATA #IMPLIED>
.IP
<!ELEMENT default EMPTY>
<!ATTLIST default remote      IDREF #IMPLIED>
<!ATTLIST default revision    CDATA #IMPLIED>
<!ATTLIST default dest\-branch CDATA #IMPLIED>
<!ATTLIST default upstream    CDATA #IMPLIED>
<!ATTLIST default sync\-j      CDATA #IMPLIED>
<!ATTLIST default sync\-c      CDATA #IMPLIED>
<!ATTLIST default sync\-s      CDATA #IMPLIED>
<!ATTLIST default sync\-tags   CDATA #IMPLIED>
.IP
<!ELEMENT manifest\-server EMPTY>
<!ATTLIST manifest\-server url CDATA #REQUIRED>
.IP
<!ELEMENT submanifest EMPTY>
<!ATTLIST submanifest name           ID #REQUIRED>
<!ATTLIST submanifest remote         IDREF #IMPLIED>
<!ATTLIST submanifest project        CDATA #IMPLIED>
<!ATTLIST submanifest manifest\-name  CDATA #IMPLIED>
<!ATTLIST submanifest revision       CDATA #IMPLIED>
<!ATTLIST submanifest path           CDATA #IMPLIED>
<!ATTLIST submanifest groups         CDATA #IMPLIED>
<!ATTLIST submanifest default\-groups CDATA #IMPLIED>
.TP
<!ELEMENT project (annotation*,
project*,
copyfile*,
linkfile*)>
.TP
<!ATTLIST project name
CDATA #REQUIRED>
.TP
<!ATTLIST project path
CDATA #IMPLIED>
.TP
<!ATTLIST project remote
IDREF #IMPLIED>
.TP
<!ATTLIST project revision
CDATA #IMPLIED>
.IP
<!ATTLIST project dest\-branch CDATA #IMPLIED>
<!ATTLIST project groups      CDATA #IMPLIED>
<!ATTLIST project sync\-c      CDATA #IMPLIED>
<!ATTLIST project sync\-s      CDATA #IMPLIED>
<!ATTLIST project sync\-tags   CDATA #IMPLIED>
<!ATTLIST project upstream CDATA #IMPLIED>
<!ATTLIST project clone\-depth CDATA #IMPLIED>
<!ATTLIST project force\-path CDATA #IMPLIED>
.IP
<!ELEMENT annotation EMPTY>
<!ATTLIST annotation name  CDATA #REQUIRED>
<!ATTLIST annotation value CDATA #REQUIRED>
<!ATTLIST annotation keep  CDATA "true">
.IP
<!ELEMENT copyfile EMPTY>
<!ATTLIST copyfile src  CDATA #REQUIRED>
<!ATTLIST copyfile dest CDATA #REQUIRED>
.IP
<!ELEMENT linkfile EMPTY>
<!ATTLIST linkfile src CDATA #REQUIRED>
<!ATTLIST linkfile dest CDATA #REQUIRED>
.IP
<!ELEMENT extend\-project EMPTY>
<!ATTLIST extend\-project name CDATA #REQUIRED>
<!ATTLIST extend\-project path CDATA #IMPLIED>
<!ATTLIST extend\-project dest\-path CDATA #IMPLIED>
<!ATTLIST extend\-project groups CDATA #IMPLIED>
<!ATTLIST extend\-project revision CDATA #IMPLIED>
<!ATTLIST extend\-project remote CDATA #IMPLIED>
<!ATTLIST extend\-project dest\-branch CDATA #IMPLIED>
<!ATTLIST extend\-project upstream CDATA #IMPLIED>
<!ATTLIST extend\-project base\-rev CDATA #IMPLIED>
.IP
<!ELEMENT remove\-project EMPTY>
<!ATTLIST remove\-project name     CDATA #IMPLIED>
<!ATTLIST remove\-project path     CDATA #IMPLIED>
<!ATTLIST remove\-project optional CDATA #IMPLIED>
<!ATTLIST remove\-project base\-rev CDATA #IMPLIED>
.IP
<!ELEMENT repo\-hooks EMPTY>
<!ATTLIST repo\-hooks in\-project CDATA #REQUIRED>
<!ATTLIST repo\-hooks enabled\-list CDATA #REQUIRED>
.IP
<!ELEMENT superproject EMPTY>
<!ATTLIST superproject name     CDATA #REQUIRED>
<!ATTLIST superproject remote   IDREF #IMPLIED>
<!ATTLIST superproject revision CDATA #IMPLIED>
.IP
<!ELEMENT contactinfo EMPTY>
<!ATTLIST contactinfo bugurl  CDATA #REQUIRED>
.IP
<!ELEMENT include EMPTY>
<!ATTLIST include name     CDATA #REQUIRED>
<!ATTLIST include groups   CDATA #IMPLIED>
<!ATTLIST include revision CDATA #IMPLIED>
.PP
]>
```
.PP
For compatibility purposes across repo releases, all unknown elements are
silently ignored. However, repo reserves all possible names for itself for
future use. If you want to use custom elements, the `x\-*` namespace is reserved
for that purpose, and repo guarantees to never allocate any corresponding names.
.PP
A description of the elements and their attributes follows.
.PP
Element manifest
.PP
The root element of the file.
.PP
Element notice
.PP
Arbitrary text that is displayed to users whenever `repo sync` finishes. The
content is simply passed through as it exists in the manifest.
.PP
Element remote
.PP
One or more remote elements may be specified. Each remote element specifies a
Git URL shared by one or more projects and (optionally) the Gerrit review server
those projects upload changes through.
.PP
Attribute `name`: A short name unique to this manifest file. The name specified
here is used as the remote name in each project's .git/config, and is therefore
automatically available to commands like `git fetch`, `git remote`, `git pull`
and `git push`.
.PP
Attribute `alias`: The alias, if specified, is used to override `name` to be set
as the remote name in each project's .git/config. Its value can be duplicated
while attribute `name` has to be unique in the manifest file. This helps each
project to be able to have same remote name which actually points to different
remote url.
.PP
Attribute `fetch`: The Git URL prefix for all projects which use this remote.
Each project's name is appended to this prefix to form the actual URL used to
clone the project.
.PP
Attribute `pushurl`: The Git "push" URL prefix for all projects which use this
remote. Each project's name is appended to this prefix to form the actual URL
used to "git push" the project. This attribute is optional; if not specified
then "git push" will use the same URL as the `fetch` attribute.
.PP
Attribute `review`: Hostname of the Gerrit server where reviews are uploaded to
by `repo upload`. This attribute is optional; if not specified then `repo
upload` will not function.
.PP
Attribute `revision`: Name of a Git branch (e.g. `main` or `refs/heads/main`).
Remotes with their own revision will override the default revision.
.PP
Element default
.PP
At most one default element may be specified. Its remote and revision attributes
are used when a project element does not specify its own remote or revision
attribute.
.PP
Attribute `remote`: Name of a previously defined remote element. Project
elements lacking a remote attribute of their own will use this remote.
.PP
Attribute `revision`: Name of a Git branch (e.g. `main` or `refs/heads/main`).
Project elements lacking their own revision attribute will use this revision.
.PP
Attribute `dest\-branch`: Name of a Git branch (e.g. `main`). Project elements
not setting their own `dest\-branch` will inherit this value. If this value is
not set, projects will use `revision` by default instead.
.PP
Attribute `upstream`: Name of the Git ref in which a sha1 can be found. Used
when syncing a revision locked manifest in \fB\-c\fR mode to avoid having to sync the
entire ref space. Project elements not setting their own `upstream` will inherit
this value.
.PP
Attribute `sync\-j`: Number of parallel jobs to use when synching.
.PP
Attribute `sync\-c`: Set to true to only sync the given Git branch (specified in
the `revision` attribute) rather than the whole ref space. Project elements
lacking a sync\-c element of their own will use this value.
.PP
Attribute `sync\-s`: Set to true to also sync sub\-projects.
.PP
Attribute `sync\-tags`: Set to false to only sync the given Git branch (specified
in the `revision` attribute) rather than the other ref tags.
.PP
Element manifest\-server
.PP
At most one manifest\-server may be specified. The url attribute is used to
specify the URL of a manifest server, which is an XML RPC service.
.PP
See the [smart sync documentation](./smart\-sync.md) for more details.
.PP
Element submanifest
.PP
One or more submanifest elements may be specified. Each element describes a
single manifest to be checked out as a child.
.PP
Attribute `name`: A unique name (within the current (sub)manifest) for this
submanifest. It acts as a default for `revision` below. The same name can be
used for submanifests with different parent (sub)manifests.
.PP
Attribute `remote`: Name of a previously defined remote element. If not supplied
the remote given by the default element is used.
.PP
Attribute `project`: The manifest project name. The project's name is appended
onto its remote's fetch URL to generate the actual URL to configure the Git
remote with. The URL gets formed as:
.IP
${remote_fetch}/${project_name}.git
.PP
where ${remote_fetch} is the remote's fetch attribute and ${project_name} is the
project's name attribute. The suffix ".git" is always appended as repo assumes
the upstream is a forest of bare Git repositories. If the project has a parent
element, its name will be prefixed by the parent's.
.PP
The project name must match the name Gerrit knows, if Gerrit is being used for
code reviews.
.PP
`project` must not be empty, and may not be an absolute path or use "." or ".."
path components. It is always interpreted relative to the remote's fetch
settings, so if a different base path is needed, declare a different remote with
the new settings needed.
.PP
If not supplied the remote and project for this manifest will be used: `remote`
cannot be supplied.
.PP
Projects from a submanifest and its submanifests are added to the
submanifest::path:<path_prefix> group.
.PP
Attribute `manifest\-name`: The manifest filename in the manifest project. If not
supplied, `default.xml` is used.
.PP
Attribute `revision`: Name of a Git branch (e.g. "main" or "refs/heads/main"),
tag (e.g. "refs/tags/stable"), or a commit hash. If not supplied, `name` is
used.
.PP
Attribute `path`: An optional path relative to the top directory of the repo
client where the submanifest repo client top directory should be placed. If not
supplied, `revision` is used.
.PP
`path` may not be an absolute path or use "." or ".." path components.
.PP
Attribute `groups`: List of additional groups to which all projects in the
included submanifest belong. This appends and recurses, meaning all projects in
submanifests carry all parent submanifest groups. Same syntax as the
corresponding element of `project`.
.PP
Attribute `default\-groups`: The list of manifest groups to sync if no
`\-\-groups=` parameter was specified at init. When that list is empty, use this
list instead of "default" as the list of groups to sync.
.PP
Element project
.PP
One or more project elements may be specified. Each element describes a single
Git repository to be cloned into the repo client workspace. You may specify
Git\-submodules by creating a nested project. Git\-submodules will be
automatically recognized and inherit their parent's attributes, but those may be
overridden by an explicitly specified project element.
.PP
Attribute `name`: A unique name for this project. The project's name is appended
onto its remote's fetch URL to generate the actual URL to configure the Git
remote with. The URL gets formed as:
.IP
${remote_fetch}/${project_name}.git
.PP
where ${remote_fetch} is the remote's fetch attribute and ${project_name} is the
project's name attribute. The suffix ".git" is always appended as repo assumes
the upstream is a forest of bare Git repositories. If the project has a parent
element, its name will be prefixed by the parent's.
.PP
The project name must match the name Gerrit knows, if Gerrit is being used for
code reviews.
.PP
"name" must not be empty, and may not be an absolute path or use "." or ".."
path components. It is always interpreted relative to the remote's fetch
settings, so if a different base path is needed, declare a different remote with
the new settings needed. These restrictions are not enforced for [Local
Manifests].
.PP
Attribute `path`: An optional path relative to the top directory of the repo
client where the Git working directory for this project should be placed. If not
supplied the project "name" is used. If the project has a parent element, its
path will be prefixed by the parent's.
.PP
"path" may not be an absolute path or use "." or ".." path components. These
restrictions are not enforced for [Local Manifests].
.PP
If you want to place files into the root of the checkout (e.g. a README or
Makefile or another build script), use the [copyfile] or [linkfile] elements
instead.
.PP
Attribute `remote`: Name of a previously defined remote element. If not supplied
the remote given by the default element is used.
.PP
Attribute `revision`: Name of the Git branch the manifest wants to track for
this project. Names can be relative to refs/heads (e.g. just "main") or absolute
(e.g. "refs/heads/main"). Tags and/or explicit SHA\-1s should work in theory, but
have not been extensively tested. If not supplied the revision given by the
remote element is used if applicable, else the default element is used.
.PP
Attribute `dest\-branch`: Name of a Git branch (e.g. `main`). When using `repo
upload`, changes will be submitted for code review on this branch. If
unspecified both here and in the default element, `revision` is used instead.
.PP
Attribute `groups`: List of groups to which this project belongs, whitespace or
comma separated. All projects belong to the group "all", and each project
automatically belongs to a group of its name:`name` and path:`path`. E.g. for
`<project name="monkeys" path="barrel\-of"/>`, that project definition is
implicitly in the following manifest groups: default, name:monkeys, and
path:barrel\-of. If you place a project in the group "notdefault", it will not be
automatically downloaded by repo. If the project has a parent element, the
`name` and `path` here are the prefixed ones.
.PP
Attribute `sync\-c`: Set to true to only sync the given Git branch (specified in
the `revision` attribute) rather than the whole ref space.
.PP
Attribute `sync\-s`: Set to true to also sync sub\-projects.
.PP
Attribute `upstream`: Name of the Git ref in which a sha1 can be found. Used
when syncing a revision locked manifest in \fB\-c\fR mode to avoid having to sync the
entire ref space.
.PP
Attribute `clone\-depth`: Set the depth to use when fetching this project. If
specified, this value will override any value given to repo init with the
\fB\-\-depth\fR option on the command line.
.PP
Attribute `force\-path`: Set to true to force this project to create the local
mirror repository according to its `path` attribute (if supplied) rather than
the `name` attribute. This attribute only applies to the local mirrors syncing,
it will be ignored when syncing the projects in a client working directory.
.PP
Element extend\-project
.PP
Modify the attributes of the named project.
.PP
This element is mostly useful in a local manifest file, to modify the attributes
of an existing project without completely replacing the existing project
definition. This makes the local manifest more robust against changes to the
original manifest.
.PP
Attribute `path`: If specified, limit the change to projects checked out at the
specified path, rather than all projects with the given name.
.PP
Attribute `dest\-path`: If specified, a path relative to the top directory of the
repo client where the Git working directory for this project should be placed.
This is used to move a project in the checkout by overriding the existing `path`
setting.
.PP
Attribute `groups`: List of additional groups to which this project belongs.
Same syntax as the corresponding element of `project`.
.PP
Attribute `revision`: If specified, overrides the revision of the original
project. Same syntax as the corresponding element of `project`.
.PP
Attribute `remote`: If specified, overrides the remote of the original project.
Same syntax as the corresponding element of `project`.
.PP
Attribute `dest\-branch`: If specified, overrides the dest\-branch of the original
project. Same syntax as the corresponding element of `project`.
.PP
Attribute `upstream`: If specified, overrides the upstream of the original
project. Same syntax as the corresponding element of `project`.
.PP
Attribute `base\-rev`: If specified, adds a check against the revision to be
extended. Manifest parse will fail and give a list of mismatch extends if the
revisions being extended have changed since base\-rev was set. Intended for use
with layered manifests using hash revisions to prevent patch branches hiding
newer upstream revisions. Also compares named refs like branches or tags but is
misleading if branches are used as base\-rev. Same syntax as the corresponding
element of `project`.
.PP
Element annotation
.PP
Zero or more annotation elements may be specified as children of a project or
remote element. Each element describes a name\-value pair. For projects, this
name\-value pair will be exported into each project's environment during a
\&'forall' command, prefixed with `REPO__`. In addition, there is an optional
attribute "keep" which accepts the case insensitive values "true" (default) or
"false". This attribute determines whether or not the annotation will be kept
when exported with the manifest subcommand.
.PP
Element copyfile
.PP
Zero or more copyfile elements may be specified as children of a project
element. Each element describes a src\-dest pair of files; the "src" file will be
copied to the "dest" place during `repo sync` command.
.PP
"src" is project relative, "dest" is relative to the top of the tree. Copying
from paths outside of the project or to paths outside of the repo client is not
allowed.
.PP
"src" and "dest" must be files. Directories or symlinks are not allowed.
Intermediate paths must not be symlinks either.
.PP
Parent directories of "dest" will be automatically created if missing.
.PP
Element linkfile
.PP
It's just like copyfile and runs at the same time as copyfile but instead of
copying it creates a symlink.
.PP
The symlink is created at "dest" (relative to the top of the tree) and points to
the path specified by "src" which is a path in the project.
.PP
Parent directories of "dest" will be automatically created if missing.
.PP
The symlink target may be a file or directory, but it may not point outside of
the repo client.
.PP
Element remove\-project
.PP
Deletes a project from the internal manifest table, possibly allowing a
subsequent project element in the same manifest file to replace the project with
a different source.
.PP
This element is mostly useful in a local manifest file, where the user can
remove a project, and possibly replace it with their own definition.
.PP
The project `name` or project `path` can be used to specify the remove target
meaning one of them is required. If only name is specified, all projects with
that name are removed.
.PP
If both name and path are specified, only projects with the same name and path
are removed, meaning projects with the same name but in other locations are
kept.
.PP
If only path is specified, a matching project is removed regardless of its name.
Logic otherwise behaves like both are specified.
.PP
Attribute `optional`: Set to true to ignore remove\-project elements with no
matching `project` element.
.PP
Attribute `base\-rev`: If specified, adds a check against the revision to be
removed. Manifest parse will fail and give a list of mismatch removes if the
revisions being removed have changed since base\-rev was set. Intended for use
with layered manifests using hash revisions to prevent patch branches hiding
newer upstream revisions. Also compares named refs like branches or tags but is
misleading if branches are used as base\-rev. Same syntax as the corresponding
element of `project`.
.PP
Element repo\-hooks
.PP
NB: See the [practical documentation](./repo\-hooks.md) for using repo hooks.
.PP
Only one repo\-hooks element may be specified at a time. Attempting to redefine
it will fail to parse.
.PP
Attribute `in\-project`: The project where the hooks are defined. The value must
match the `name` attribute (**not** the `path` attribute) of a previously
defined `project` element.
.PP
Attribute `enabled\-list`: List of hooks to use, whitespace or comma separated.
.PP
Element superproject
.PP
*** *Note*: This is currently a WIP. ***
.PP
NB: See the [git superprojects documentation](
https://en.wikibooks.org/wiki/Git/Submodules_and_Superprojects) for background
information.
.PP
This element is used to specify the URL of the superproject. It has "name" and
"remote" as atrributes. Only "name" is required while the others have reasonable
defaults. At most one superproject may be specified. Attempting to redefine it
will fail to parse.
.PP
Attribute `name`: A unique name for the superproject. This attribute has the
same meaning as project's name attribute. See the [element
project](#element\-project) for more information.
.PP
Attribute `remote`: Name of a previously defined remote element. If not supplied
the remote given by the default element is used.
.PP
Attribute `revision`: Name of the Git branch the manifest wants to track for
this superproject. If not supplied the revision given by the remote element is
used if applicable, else the default element is used.
.PP
Element contactinfo
.PP
*** *Note*: This is currently a WIP. ***
.PP
This element is used to let manifest authors self\-register contact info. It has
"bugurl" as a required atrribute. This element can be repeated, and any later
entries will clobber earlier ones. This would allow manifest authors who extend
manifests to specify their own contact info.
.PP
Attribute `bugurl`: The URL to file a bug against the manifest owner.
.PP
Element include
.PP
This element provides the capability of including another manifest file into the
originating manifest. Normal rules apply for the target manifest to include \- it
must be a usable manifest on its own.
.PP
Attribute `name`: the manifest to include, specified relative to the manifest
repository's root.
.PP
"name" may not be an absolute path or use "." or ".." path components. These
restrictions are not enforced for [Local Manifests].
.PP
Attribute `groups`: List of additional groups to which all projects in the
included manifest belong. This appends and recurses, meaning all projects in
included manifests carry all parent include groups. Same syntax as the
corresponding element of `project`.
.PP
Attribute `revision`: Name of a Git branch (e.g. `main` or `refs/heads/main`)
default to which all projects in the included manifest belong.
.PP
Local Manifests
.PP
Additional remotes and projects may be added through local manifest files stored
in `$TOP_DIR/.repo/local_manifests/*.xml`.
.PP
For example:
.IP
\f(CW$ ls .repo/local_manifests\fR
.IP
local_manifest.xml
another_local_manifest.xml
.IP
\f(CW$ cat .repo/local_manifests/local_manifest.xml\fR
.IP
<?xml version="1.0" encoding="UTF\-8"?>
<manifest>
.IP
<project path="manifest"
.IP
name="tools/manifest" />
.IP
<project path="platform\-manifest"
.IP
name="platform/manifest" />
.IP
</manifest>
.PP
Users may add projects to the local manifest(s) prior to a `repo sync`
invocation, instructing repo to automatically download and manage these extra
projects.
.PP
Manifest files stored in `$TOP_DIR/.repo/local_manifests/*.xml` will be loaded
in alphabetical order.
.PP
Projects from local manifest files are added into local::<local manifest
filename> group.
.PP
The legacy `$TOP_DIR/.repo/local_manifest.xml` path is no longer supported.
.SS [copyfile]: #Element\-copyfile [linkfile]: #Element\-linkfile [Local Manifests]:
.PP
#local\-manifests
