/**
 * @*************************************** Copyright (c) ***************************************
 * @                              <PERSON>han Qiwu Technology Co., Ltd
 * @*********************************************************************************************
 * @Author: Jiang<PERSON>hen
 * @Date: 2021-12-13 20:38:27
 * @LastEditTime: 2022-02-24 16:07:26
 * @LastEditors: JiangZhen
 * @FilePath: \iGS630_App\Application\App\radio\sensor\sensor_ant\shft\ant_shft_rx.c
 * @Description:ANT SHFT模块.c文件
 * @*********************************************************************************************
 */
#include "app_error.h"
#include "ant_shft.h"
#include "ant_shft_rx.h"
#include "nrf_sdh_ant.h"
#include "nrf_sdh_soc.h"

#include "ant_parameters.h"
#include "ant_interface.h"
#include "basictype.h"

#include "qw_sensor_common.h"
#include "sensor_ant_common.h"
#include "qw_sensor_data.h"
#include "cfg_header_def.h"

#if ANT_SENSOR_SHFT_ENABLED


//--------------------------------------函数申明-------------------------------------------//
static void ant_shft_rx_evt_handler(ant_shft_profile_t * p_profile, ant_shft_evt_t event);
static void shft_ant_evt(ant_evt_t *p_ant_evt, void * p_context);


//--------------------------------------变量定义-------------------------------------------//
SHFT_DISP_PROFILE_CONFIG_DEF(m_ant_shft, ant_shft_rx_evt_handler);
static ant_shft_profile_t m_ant_shft;

//--------------------------------------函数定义-------------------------------------------//
//-------------------------------------------------------------------------------------------
// Function Name : LoadChnConf_shft_rx
// Purpose       : 加载ANT SHFT接收通道默认配置
// Param[in]     : ant_channel_config_t  *p_channel_config
// Param[out]    : None
// Return type   : static
// Comment       : 2019-02-27
//-------------------------------------------------------------------------------------------
static void LoadChnConf_shft_rx(ant_channel_config_t  *p_channel_config)
{
    p_channel_config->channel_number    = sensor_ant_channel_num_get(SENSOR_TYPE_SHFT);
    p_channel_config->channel_type      = CHANNEL_TYPE_SLAVE;      //ant认证要求使用0x00类型 BSC_DISP_CHANNEL_TYPE;
    p_channel_config->ext_assign        = SHFT_EXT_ASSIGN;
    p_channel_config->rf_freq           = SHFT_ANTPLUS_RF_FREQ;    ///< Frequency, decimal 57 (2457 MHz).
    p_channel_config->transmission_type = CHAN_ID_TRANS_TYPE;
    p_channel_config->device_type       = SHFT_DEVICE_TYPE;
    p_channel_config->channel_period    = SHFT_MSG_PERIOD;         //8086
    p_channel_config->network_number    = ANTPLUS_NETWORK_NUM;
}

//-------------------------------------------------------------------------------------------
// Function Name : ant_shft_rx_evt_handler
// Purpose       : ANT SHFT接收通道中断处理函数
// Param[in]     : ant_shft_profile_t * p_profile
//                 ant_shft_evt_t event
// Param[out]    : None
// Return type   : static
// Comment       : 2019-02-27
//-------------------------------------------------------------------------------------------
static void ant_shft_rx_evt_handler(ant_shft_profile_t * p_profile, ant_shft_evt_t event)
{
    sensor_search_infor_t       sensor_search_infor;
    // sensor_connect_infor_t      *p_sensor_connect       = sensor_connect_infor_get(SENSOR_TYPE_SHFT);
    sensor_connect_infor_t      sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_SHFT, &sensor_connect);

    sensor_module_evt_handler   evt_handler             = sensor_module_evt_handler_get();
    sensor_saved_work_t         *p_sensor_saved         = NULL;
    sensor_work_state_t         sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    sensor_module_param_input_t *p_param                = sensor_module_param_input_get();
    sensor_original_data_t      *p_sensor_original_data = sensor_original_data_get();
    int8_t                      index                   = -1;
    uint8_t                     battery_index           = 0;
    uint8_t front_gear = 0;
    uint8_t rear_gear = 0;
    static uint8_t              low_power_indicate_flag = FALSE;

    sensor_ant_leave_rx_search(SENSOR_TYPE_SHFT);

    switch (event)
    {
        case ANT_SHFT_PAGE_1_UPDATED:
            front_gear = (p_profile->page_1.current_gear & 0xe0) >> 5;
            rear_gear = p_profile->page_1.current_gear & 0x1f;

            if(7 != front_gear) //有效判断
            {
                front_gear++; //0是第1个gear
            }
            else
            {
                front_gear = 0;
            }

            if(31 != rear_gear) //有效判断
            {
                rear_gear++; //0是第1个gear
            }
            else
            {
                rear_gear = 0;
            }

            p_sensor_original_data->shftData.current_gear = ((front_gear << 5) & 0xe0) | rear_gear;
            p_sensor_original_data->shftData.total_gear = p_profile->page_1.total_gear;

            memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
            memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
            sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
            sensor_search_infor.sensor_type = SENSOR_TYPE_SHFT;
            // sensor_saved_work_infor_get(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state);

            if (SENSOR_CONNECT_STATE_CONNECTING == sensor_connect.state)
            {
                sensor_connect.state = SENSOR_CONNECT_STATE_CONNECTED;
                sensor_connect_infor_set(SENSOR_TYPE_SHFT, &sensor_connect);

                if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
                {
                    if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
                    {
                        p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                        p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
                        cfg_mark_update(enum_cfg_ant_ble_dev);
                    }
                    else if (SENSOR_WORK_STATE_FORBIDDEN == sensor_work_state)
                    {
                        sensor_infor_t sensor_infor = {0};
                        sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                        sensor_disconnect(&sensor_infor);
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }
                else
                {
                    if(sensor_disconnect_item_check(&sensor_search_infor))
                    {
                        sensor_disconnect_info_remove(&sensor_search_infor);
                        sensor_infor_t sensor_infor = {0};
                        sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                        sensor_disconnect(&sensor_infor);
                        return;
                    }
                    sensor_saved_work_infor_add(&sensor_search_infor);
                    sensor_search_infor_del(&sensor_search_infor);
                }

                if (NULL != evt_handler)
                {
                    evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, TRUE);
                    evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, 0);
                    evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, TRUE);
                }

                // low_power_indicate_flag = TRUE;
            }
            else if (SENSOR_CONNECT_STATE_CONNECTED == sensor_connect.state)
            {
                if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
                {
                    if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
                    {
                        p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                        p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }
            }
            break;
#if SENSOR_DEVICE_INFO_ENABLED
        case ANT_SHFT_PAGE_80_UPDATED:
            memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
            memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
            sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
            sensor_search_infor.sensor_type = SENSOR_TYPE_SHFT;
            if (sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
            {
                if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
                {
#if SENSOR_DEVICE_INFO_ENABLED
                    if (p_sensor_saved->sensor_manufacturer[index].manufacturer_ant != p_profile->page_80.manufacturer_id ||
                            p_sensor_saved->sensor_hw_version[index].version_ant != p_profile->page_80.hw_revision ||
                            p_sensor_saved->sensor_model[index].model_ant != p_profile->page_80.model_number)
                    {
                        p_sensor_saved->sensor_manufacturer[index].manufacturer_ant = p_profile->page_80.manufacturer_id;
                        p_sensor_saved->sensor_hw_version[index].version_ant      = p_profile->page_80.hw_revision;
                        p_sensor_saved->sensor_model[index].model_ant        = p_profile->page_80.model_number;
                        evt_handler(EVENT_SENSOR_MANUFACTURER_RECEIVED, NULL, sensor_search_infor.sensor_type, 0, index);
                    }
#endif
                    p_sensor_saved->sensor_manufacturer[index].manufacturer_ant = p_profile->page_80.manufacturer_id;
                    p_sensor_saved->sensor_hw_version  [index].version_ant      = p_profile->page_80.hw_revision;
                    p_sensor_saved->sensor_model       [index].model_ant        = p_profile->page_80.model_number;
                }
                sensor_saved_work_infor_release_write_lock(index);
            }
            break;
        case ANT_SHFT_PAGE_81_UPDATED:
            memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
            memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
            sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
            sensor_search_infor.sensor_type = SENSOR_TYPE_SHFT;
            if (sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
            {
                if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
                {
#if SENSOR_DEVICE_INFO_ENABLED
                    if (p_sensor_saved->sensor_serial[index].serial_ant != p_profile->page_81.serial_number ||
                            p_sensor_saved->sensor_sw_version[index].version_ant != ((((uint16_t)p_profile->page_81.sw_revision_major) << 8) | p_profile->page_81.sw_revision_minor))
                    {
                        p_sensor_saved->sensor_serial[index].serial_ant = p_profile->page_81.serial_number;
                        p_sensor_saved->sensor_sw_version[index].version_ant = ((((uint16_t)p_profile->page_81.sw_revision_major) << 8) | p_profile->page_81.sw_revision_minor);
                        evt_handler(EVENT_SENSOR_MANUFACTURER_RECEIVED, NULL, sensor_search_infor.sensor_type, 0, index);
                    }
#endif
                    p_sensor_saved->sensor_serial      [index].serial_ant       = p_profile->page_81.serial_number;
                    p_sensor_saved->sensor_sw_version  [index].version_ant      = ((((uint16_t)p_profile->page_81.sw_revision_major) << 8) | p_profile->page_81.sw_revision_minor);
                }
                sensor_saved_work_infor_release_write_lock(index);
            }
            break;
#endif
        case ANT_SHFT_PAGE_82_UPDATED:
            if(0xff == p_profile->page_82.battery_identifier.byte)  //电池ID无效值
            {
                p_sensor_original_data->shftData.number_of_battery = 1;
                battery_index = 0;
            }
            else
            {
                p_sensor_original_data->shftData.number_of_battery = p_profile->page_82.battery_identifier.items.number;
                battery_index = p_profile->page_82.battery_identifier.items.identifier - 1;
            }

            if(4 > battery_index)
            {
                if (0x0f == p_profile->page_82.descriptive_bit_field.items.coarse_battery_voltage) //电压无效值
                {
                    p_sensor_original_data->shftData.battery_voltage[battery_index] = 0xffff;
                }
                else
                {
                    p_sensor_original_data->shftData.battery_voltage[battery_index] = (uint16_t)p_profile->page_82.descriptive_bit_field.items.coarse_battery_voltage * 1000 +
                                                (uint32_t)p_profile->page_82.fractional_battery_voltage * 1000 / 256;
                }

                p_sensor_original_data->shftData.battery_status[battery_index] = p_profile->page_82.descriptive_bit_field.items.battery_status;

                if (p_sensor_original_data->shftData.number_of_battery == 1)
                {
                    //显示单块电池
                    if (p_sensor_original_data->shftData.battery_status[0] >= SENSOR_BATT_NEW &&
                            p_sensor_original_data->shftData.battery_status[0] <= SENSOR_BATT_CRITICAL)
                    {
                        p_sensor_original_data->battery_list.shft = (SENSOR_BATT_MAX - p_sensor_original_data->shftData.battery_status[0]) * 20;
                    }
                }
                else if (p_sensor_original_data->shftData.number_of_battery > 1)
                {
                    //多块电池时, 显示后拨电量, 因为后拨比前拨更常用
                    if (p_sensor_original_data->shftData.battery_status[1] >= SENSOR_BATT_NEW &&
                            p_sensor_original_data->shftData.battery_status[1] <= SENSOR_BATT_CRITICAL)
                    {
                        p_sensor_original_data->battery_list.shft = (SENSOR_BATT_MAX - p_sensor_original_data->shftData.battery_status[1]) * 20;
                    }
                }
            }
            break;
        default:
            break;
    }
}

//-------------------------------------------------------------------------------------------
// Function Name : shft_ant_evt
// Purpose       : SHFT ANT事件处理函数
// Param[in]     : ant_evt_t *p_ant_evt
//                 void * p_context
// Param[out]    : None
// Return type   : static
// Comment       : 2020-04-16
//-------------------------------------------------------------------------------------------
static void shft_ant_evt(ant_evt_t *p_ant_evt, void * p_context)
{
    sensor_search_infor_t     sensor_search_infor;
    // sensor_connect_infor_t    *p_sensor_connect       = sensor_connect_infor_get(SENSOR_TYPE_SHFT);
    sensor_connect_infor_t    sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_SHFT, &sensor_connect);

    sensor_module_evt_handler evt_handler             = sensor_module_evt_handler_get();
    sensor_saved_work_t       *p_sensor_saved         = NULL;
    sensor_original_data_t    *p_sensor_original_data = sensor_original_data_get();
    sensor_work_state_t       sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    uint8_t                   channelstate            = 0;
    int8_t                    index                   = -1;
    ret_code_t                err_code                = NRF_SUCCESS;

    sensor_systime_update();

    ant_shft_disp_evt_handler(p_ant_evt, p_context);

    if (p_ant_evt->channel == m_ant_shft.channel_number)
    {
        switch (p_ant_evt->event)
        {
            case EVENT_CHANNEL_CLOSED:
                //更新显示信息
                memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
                memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
                sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
                sensor_search_infor.sensor_type = SENSOR_TYPE_SHFT;

                sensor_ant_leave_rx_search(SENSOR_TYPE_SHFT);

                err_code = sd_ant_channel_unassign(m_ant_shft.channel_number);
                APP_ERROR_CHECK(err_code);
                m_ant_shft.channel_number = 0;
                sensor_ant_channel_num_unassign(SENSOR_TYPE_SHFT);

                bool forbidden_mask = sensor_connect_infor_get_forbidden_mask(SENSOR_TYPE_SHFT);
                if(sensor_connect_infor_get(SENSOR_TYPE_SHFT, &sensor_connect))
                {
                    sensor_connect_infor_clear(SENSOR_TYPE_SHFT);
                    if (NULL != evt_handler && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)
                    {
                        evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                    }
                }

                if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
                {
                    if (SENSOR_WORK_STATE_IDLE != sensor_work_state)
                    {
                        p_sensor_saved->rssi             [index] = 0;
                        p_sensor_saved->battery_voltage  [index] = 0xff;
                        p_sensor_saved->sensor_work_state[index] = SENSOR_WORK_STATE_SAVED;
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }

                if((!forbidden_mask && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)       //连接完成后异常断连
                    || (forbidden_mask && sensor_connect.state == SENSOR_CONNECT_STATE_CLOSE_WAIT)
                    || sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)                      //连接超时
                {
                    // connected状态下断连，检索saved数组是否有同类型sensor并进行连接
                    sensor_connect_from_saved_info(sensor_search_infor.sensor_type);
                }

                if (NULL != evt_handler)
                {
                    evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                    evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                }
                p_sensor_original_data->battery_list.shft = 0;
                break;
            case EVENT_RX_FAIL_GO_TO_SEARCH:
                // sd_ant_channel_close(SHFT_CHANNEL_NUMBER);
                // sensor_ant_close(SENSOR_TYPE_SHFT);
                sensor_ant_enter_rx_search(SENSOR_TYPE_SHFT);
                break;
            case EVENT_RX_SEARCH_TIMEOUT:
            default:
                break;
        }
    }
}

NRF_SDH_ANT_OBSERVER(m_shft_ant_observer, ANT_BSC_ANT_OBSERVER_PRIO, shft_ant_evt, &m_ant_shft);

/**
 * @*********************************************************************************************
 * @description: 设置shft通道
 * @param {ant_id_t} *id
 * @return {*}
 * @*********************************************************************************************
 */
void ant_shft_rx_profile_setup(ant_id_t *id)
{
    // sensor_connect_infor_t *p_sensor_connect = sensor_connect_infor_get(SENSOR_TYPE_SHFT);
    sensor_connect_infor_t    sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_SHFT, &sensor_connect);
    ret_code_t             err_code          = NRF_SUCCESS;
    ant_channel_config_t channel_config;

    memcpy ((uint8_t *)&sensor_connect.sensor_id.ant_id, (uint8_t *)id, sizeof(ant_id_t));
    sensor_connect_infor_set(SENSOR_TYPE_SHFT, &sensor_connect);

    /*
    //device num的组成
    //1byte   1byte    |     1byte      |      1byte                  从左到右高到低
    //   device id     | device type    |MSN:extended device number LSN:Transmission Type
    */
    uint16_t sensor_id = (uint16_t)id->id;
    uint8_t trans_type = CHAN_ID_TRANS_TYPE;
    if (id->id > 0xffff)
    {
        trans_type = id->trans_type;
    }

    //加载参数
    LoadChnConf_shft_rx(&channel_config);
    channel_config.device_number     = sensor_id;
    channel_config.transmission_type = trans_type;

    err_code = ant_shft_disp_init(&m_ant_shft,
                                 (const ant_channel_config_t *)&channel_config,
                                 SHFT_DISP_PROFILE_CONFIG(m_ant_shft));
    APP_ERROR_CHECK(err_code);
}

/**
 * @*********************************************************************************************
 * @description: 开启SHFT通道
 * @param {*}
 * @return {*}
 * @*********************************************************************************************
 */
void ant_shft_rx_open(void)
{
    ret_code_t             err_code          = NRF_SUCCESS;

    err_code = ant_shft_disp_open(&m_ant_shft);
    APP_ERROR_CHECK(err_code);
}

#endif //ANT_SENSOR_SHFT_ENABLED
