#include <string.h>
#include <stdbool.h>
#include "navi_sign.h"

//标记点分段，所有标记点处理完毕后，获取最终结果即可，中间结果没有意义
int navi_sign_segmentor_exec(NaviSignSegmentor *self, const NaviSign *sign)
{
    if (self == NULL || sign == NULL)
    {
        return -1;
    }

    if (self->cnt == 0)
    {
        self->len = 0;
        dseg_update(&self->seg_buf[self->len].dseg, 0.0f, sign->dist);
        self->seg_buf[self->len].range.start = self->cnt;
        self->cnt += 1;
        return 0;
    }

    //更新分段数据
    self->seg_buf[self->len].dseg.dist_max = sign->dist;
    self->cnt += 1;

    if (self->cnt < (self->len + 1) * self->interval)
    {
        //尚未完成一个分段，返回即可
        return 0;
    }

    //完成一个分段
    self->seg_buf[self->len].range.end = self->cnt;
    self->len += 1;

    if (self->len < self->capacity)
    {
        //创建一个新的分段
        dseg_update(&self->seg_buf[self->len].dseg, sign->dist, sign->dist);
        self->seg_buf[self->len].range.start = self->cnt;
    }
    else
    {
        //分段数量已经达到最大数量，则两两合并，以减少一半分段数量
        self->interval *= 2;
        const uint32_t merge_num = self->capacity / 2;

        //两两合并
        for (uint32_t i = 0; i < merge_num; i++)
        {
            self->seg_buf[i*2].range.end = self->seg_buf[i*2+1].range.end;
            self->seg_buf[i*2].dseg.dist_max = self->seg_buf[i*2+1].dseg.dist_max;
        }

        //将分段移动到前半部分
        for (uint32_t i = 1; i < merge_num; i++)
        {
            dseg_copy(&self->seg_buf[i].dseg, &self->seg_buf[i*2].dseg);
            range_copy(&self->seg_buf[i].range, &self->seg_buf[i*2].range);
        }

        self->len = merge_num;

        //分段容量为偶数，则两两合并后没有多余的分段，开始新的分段即可
        if (self->capacity % 2 == 0)
        {
            dseg_update(&self->seg_buf[self->len].dseg, sign->dist, sign->dist);
            self->seg_buf[self->len].range.start = self->cnt;
        }
        else
        {
            //分段容量为奇数，则两两合并后多余一个分段，基于该分段继续进行分段计算
            dseg_copy(&self->seg_buf[self->len].dseg, &self->seg_buf[self->capacity-1].dseg);
            range_copy(&self->seg_buf[self->len].range, &self->seg_buf[self->capacity-1].range);
        }
    }

    return 0;
}

//完成标记点分段，在所有标记点处理完毕后必须调用，以处理最后一个未完成的的分段
int navi_sign_segmentor_end(NaviSignSegmentor *self, float dist_end)
{
    if (self == NULL)
    {
        return -1;
    }

    //将路书终点距离赋给最后一个分段，使得分段能够覆盖整个路书
    if (dist_end > self->seg_buf[self->len].dseg.dist_max)
    {
        self->seg_buf[self->len].dseg.dist_max = dist_end;
    }

    self->seg_buf[self->len].range.end = self->cnt;

    if (self->len > 0)
    {
        const uint32_t cnt = self->seg_buf[self->len].range.end - self->seg_buf[self->len].range.start;

        //最后一个分段不足一半，将最后一个未完成分段合并到上一个分段中，上一个分段必定是完成的
        if (cnt < self->interval / 2)
        {
            self->seg_buf[self->len-1].range.end = self->seg_buf[self->len].range.end;
            self->seg_buf[self->len-1].dseg.dist_max = self->seg_buf[self->len].dseg.dist_max;
        }
        else
        {
            //最后一个分段已经达到一半，则创建一个新的分段
            self->len += 1;
        }
    }
    else
    {
        //如果一个分段都没有完成，那么直接创建一个分段
        self->len = 1;
    }

    return 0;
}

//获取标记点分段数据，仅当所有分段处理完毕后才能调用
int navi_sign_segmentor_data_get(NaviSignSegmentor *self, NaviSignSegmentArray *output)
{
    if (self == NULL || output == NULL)
    {
        return -1;
    }

    output->len = self->len;
    output->segments = self->seg_buf;

    return 0;
}

//重置标记点分段器
void navi_sign_segmentor_reset(NaviSignSegmentor *self)
{
    if (self != NULL)
    {
        self->len = 0;
        self->cnt = 0;
        self->interval = self->INTERVAL;
    }
}

//标记点匹配
int navi_sign_matcher_exec(NaviSignMatcher *self, float dist, uint8_t is_reverse, NaviSign *output, uint32_t *idx)
{
    if (self == NULL || output == NULL || idx == NULL)
    {
        return -1;
    }

    const float total_dist = self->seg_array->segments[self->seg_array->len-1].dseg.dist_max;

    if (is_reverse == true)
    {
        dist = total_dist - dist;
    }

    uint32_t seg_idx = 0;
    uint8_t is_find = false;

    //遍历所有分段，找到包含当前距离的分段
    for (uint32_t i = 0; i < self->seg_array->len; i++)
    {
        if (dseg_is_contain(&self->seg_array->segments[i].dseg, dist) == true)
        {
            is_find = true;
            seg_idx = i;
            break;
        }
    }

    if (is_find == false)
    {
        return 1;
    }

    NaviSign sign = { 0 };

    const uint32_t start = self->seg_array->segments[seg_idx].range.start;
    const uint32_t end = self->seg_array->segments[seg_idx].range.end;

    //从找到的分段中找到下一个转向
    if (is_reverse == true)
    {
        uint8_t is_sign_valid = false;
        uint32_t sign_idx = 0;

        for (uint32_t i = start; i < end; i++)
        {
            if (navi_sign_list_get(self->sign_list, i, &sign) == 0)
            {
                if (sign.dist > dist)
                {
                    if (i > 0)
                    {
                        is_sign_valid = true;
                        sign_idx = i - 1;
                    }
                    break;
                }
            }

            if (i == end - 1)
            {
                is_sign_valid = true;
                sign_idx = i;
            }
        }

        if (is_sign_valid == true)
        {
            if (navi_sign_list_get(self->sign_list, sign_idx, &sign) == 0)
            {
                navi_sign_copy(output, &sign);

                output->dist = total_dist - output->dist;
                *idx = self->sign_list->len - 1 - sign_idx;

                return 0;
            }
        }
    }
    else
    {
        for (uint32_t i = start; i < end; i++)
        {
            if (navi_sign_list_get(self->sign_list, i, &sign) == 0)
            {
                if (sign.dist >= dist)
                {
                    navi_sign_copy(output, &sign);
                    *idx = i;
                    return 0;
                }
            }
        }
    }

    return 1;
}

//加载指定标记点前后的若干标记点
//-1 - 加载失败
//0 - 加载成功
int navi_sign_loader_exec(NaviSignLoader *self, uint8_t is_reverse, uint32_t idx)
{
    if (self == NULL)
    {
        return -1;
    }

    if (idx >= self->sign_list->len)
    {
        return -1;
    }

    if (is_reverse == true)
    {
        idx = self->sign_list->len - 1 - idx;
    }

    const uint32_t half = self->capacity / 2;

    Range range = { 0 };

    //计算要加载的标记点的范围
    if (idx >= half)
    {
        range.start = idx - half;
    }
    else
    {
        range.start = 0;
    }

    uint32_t end = 0;

    if (self->capacity % 2 == 0)
    {
        end = idx + half;
    }
    else
    {
        end = idx + half + 1;
    }

    if (end <= self->sign_list->len)
    {
        range.end = end;
    }
    else
    {
        range.end = self->sign_list->len;
    }

    const uint32_t capacity = range.end - range.start;

    //尽可能占满缓冲区
    if (capacity < self->capacity)
    {
        const uint32_t mtr = self->capacity - capacity;

        if (range.start > mtr)
        {
            range.start -= mtr;
        }
        else
        {
            range.start = 0;
        }

        if (range.end + mtr <= self->sign_list->len)
        {
            range.end += mtr;
        }
        else
        {
            range.end = self->sign_list->len;
        }
    }

    //缓冲区没有发生变化，不必进行加载
    if (self->range.start == range.start && self->range.end == range.end)
    {
        return 0;
    }

    //将已经加载的标记点复制到正确的位置
    if (self->range.start < range.end && self->range.end > range.start)
    {
        //标记点前移，则前向遍历，否则标记点会被覆盖，导致所有数据相同
        if (range.start >= self->range.start)
        {
            for (uint32_t i = range.start; i < range.end; i++)
            {
                if (i >= self->range.start && i < self->range.end)
                {
                    if (is_reverse == true)
                    {
                        navi_sign_copy(&self->buf[self->len-1-i+range.start], &self->buf[self->len-1-i+self->range.start]);
                    }
                    else
                    {
                        navi_sign_copy(&self->buf[i-range.start], &self->buf[i-self->range.start]);
                    }
                }
            }
        }
        else
        {
            //标记点后移，则后向遍历，否则标记点会被覆盖，导致所有数据相同
            for (uint32_t i = range.end - 1; i > range.start; i--)
            {
                if (i >= self->range.start && i < self->range.end)
                {
                    if (is_reverse == true)
                    {
                        navi_sign_copy(&self->buf[self->len-1-i+range.start], &self->buf[self->len-1-i+self->range.start]);
                    }
                    else
                    {
                        navi_sign_copy(&self->buf[i-range.start], &self->buf[i-self->range.start]);
                    }
                }
            }
        }
    }

    const float total_dist = self->seg_array->segments[self->seg_array->len-1].dseg.dist_max;

    const uint32_t len = range.end - range.start;

    //加载剩余的标记点
    for (uint32_t i = range.start; i < range.end; i++)
    {
        if (i >= self->range.start && i < self->range.end)
        {
            continue;
        }

        NaviSign *sign = NULL;

        if (is_reverse == true)
        {
            sign = &self->buf[len-1-i+range.start];
        }
        else
        {
            sign = &self->buf[i-range.start];
        }

        if (navi_sign_list_get(self->sign_list, i, sign) != 0)
        {
            return -1;
        }

        if (is_reverse == true)
        {
            sign->dist = total_dist - sign->dist;
        }
    }

    self->range.start = range.start;
    self->range.end = range.end;
    self->len = self->range.end - self->range.start;

    return  0;
}

//获取加载的标记点
void navi_sign_loader_data_get(NaviSignLoader *self, NaviSignNearby *sign_nearby)
{
    if (self != NULL && sign_nearby != NULL)
    {
        sign_nearby->len = self->len;
        sign_nearby->buf = self->buf;
    }
}

//重置标记点加载器
void navi_sign_loader_reset(NaviSignLoader *self)
{
    if (self != NULL)
    {
        self->len = 0;
        self->range.start = 0;
        self->range.end = 0;
    }
}
