/************************************************************************​
*Copyright(c) 2025, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#include <stddef.h>
#include "alg_vam.h"

//计算VAM
//-1 - 输入无效
//0 - 计算成功
int vam_calculator_exec(VamCalculator *self, const VamCalcInput *input, VamCalcOutput *output)
{
    if (self == NULL || input == NULL || output == NULL)
    {
        return -1;
    }

    output->vam = 0.0f;
    output->vam_30s = 0.0f;

    if (input->alt < -999.0f)
    {
        vam_calculator_reset(self);
        return 0;
    }

    if (self->len == 0)
    {
        self->timestamp = input->timestamp;
        self->buf[self->next] = input->alt;
        self->len += 1;

        self->next += 1;
        if (self->next >= self->capacity)
        {
            self->next = 0;
        }

        return 0;
    }

    //检查时间戳
    if (input->timestamp < self->timestamp || input->timestamp - self->timestamp > 30)
    {
        vam_calculator_reset(self);
        return 0;
    }

    //输出的vam实际上是3s平均vam，因此至少要有3个海拔输入
    if (self->len < 3)
    {
        self->timestamp = input->timestamp;
        self->buf[self->next] = input->alt;
        self->len += 1;

        self->next += 1;
        if (self->next >= self->capacity)
        {
            self->next = 0;
        }

        return 0;
    }

    self->timestamp = input->timestamp;
    self->buf[self->next] = input->alt;

    if (self->len < self->capacity)
    {
        self->len += 1;
    }

    //当前海拔
    const float alt_cur = self->buf[self->next];

    const uint32_t idx_3s_before = self->next >= 3 ? self->next - 3 : self->capacity + self->next - 3;

    //3s前的海拔
    const float alt_3s_before = self->buf[idx_3s_before];

    //计算vam，注意单位为m/h
    //(alt_cur - alt_3s_before) / 3 * 3600 => (alt_cur - alt_3s_before) * 1200，下同
    output->vam = (alt_cur - alt_3s_before) * 1200.0f;

    if (self->len >= self->capacity)
    {
        uint32_t idx_30s_before = self->next + 1;
        if (idx_30s_before >= self->capacity)
        {
            idx_30s_before = 0;
        }

        const float alt_30s_before = self->buf[idx_30s_before];

        output->vam_30s = (alt_cur - alt_30s_before) * 120.0f;
    }

    self->next += 1;
    if (self->next >= self->capacity)
    {
        self->next = 0;
    }

    return 0;
}

//重置VAM计算
void vam_calculator_reset(VamCalculator *self)
{
    if (self != NULL)
    {
        self->len = 0;
        self->next = 0;
        self->timestamp = 0;
    }
}
