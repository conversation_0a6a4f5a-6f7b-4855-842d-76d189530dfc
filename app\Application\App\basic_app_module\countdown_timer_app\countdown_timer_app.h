/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   countdown_timer_app.h
@Time    :   2024/12/28 15:01:00
* 
**************************************************************************/

#ifndef __COUNTDOWN_TIMER_APP__
#define __COUNTDOWN_TIMER_APP__

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

    #define COUNTDOWN_TIMER_NUM 1000
    #define COUNTDOWN_TIMER_GPS_WAIT_NUM 180000
    #define COUNTDOWN_TIMER_GPS_WAIT_START_NUM 0
    #define COUNTDOWN_TIMER_START_WAIT_NUM 60000
    #define COUNTDOWN_TIMER_OUT_SPORT_NUM 10000

    #define SPORT_START_GPS_ANGLE 17            // 起始角度
    #define SPORT_START_GPS_INIT_ANGLE 29       // 初始度数
    #define SPORT_START_GPS_ANGLE_COUNT 264     // 变化度数
    #define SPORT_START_GPS_ANGLE_END (SPORT_START_GPS_INIT_ANGLE + SPORT_START_GPS_ANGLE_COUNT)       // 结束角度


    typedef enum
    {
        COUNTDOWN_TIMER_GPS_WAIT,
        COUNTDOWN_TIMER_START_WAIT,
        // COUNTDOWN_TIMER_START_SPORT,
        // COUNTDOWN_TIMER_OUT_SPORT,
        COUNTDOWN_TIMER__MAX,
    }COUNTDOWN_TIMER_TYPE;

    typedef enum
    {
        COUNTDOWN_TIMER_EVENT_GPS_WAIT,     // 重新等待gps
        COUNTDOWN_TIMER_EVENT_START_WAIT,   // 等待退出运动
        COUNTDOWN_TIMER_EVENT_RESET,        // 打断退出弹窗，重置等待计时或者开启计时
        COUNTDOWN_TIMER_EVENT_OUT_SPORT,    // 呼出退出运动弹窗
        COUNTDOWN_TIMER_EVENT__MAX,
    }COUNTDOWN_TIMER_EVENT;

    void countdown_timer_app_init(void);
    void countdown_timer_start(COUNTDOWN_TIMER_TYPE type);
    void countdown_timer_stop();

#ifdef __cplusplus
}
#endif

#endif	// __COUNTDOWN_TIMER_APP__
