/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   compass_ctrl.c
@Time    :   2025/01/08 10:15:35
*
**************************************************************************/

#include "compass_ctrl.h"
#ifndef SIMULATOR
#include "qw_sensor.h"
#else
#include <stdint.h>
#include <stdbool.h>
#endif   // !SIMULATOR

static bool s_start_flag = false;
static int s_compass_align = INT32_MAX;
static int s_compass_angle = INT32_MAX;
#ifndef SIMULATOR
static struct sensor_mag g_mag_data = {0};
static struct sensor_accel g_accel_data = {0};


//用户注册的回调
static void (*user_trigger_callback)(int compass_align,int compass_angle) = RT_NULL;
int compass_module_register_callback(void (*callback)(int compass_align,int compass_angle))
{
	if(callback == RT_NULL)
	{
		return -1;
	}
	user_trigger_callback = callback;
	return 0;
}

/**
 *
 * @brief 地磁订阅回调
 * @param stat 校准状态
 * @param angel 角度
 */
static void compass_result_cb(ecompass_status_t state,int angel)
{
    s_compass_align = state;
    if(state == enumEC_CALIBRATE_SUCCESS)
    {
        s_compass_angle = angel;
    }
    else
    {
        s_compass_angle = UINT32_MAX;
    }
    // 用户回调
    if(user_trigger_callback != RT_NULL) user_trigger_callback(s_compass_align,s_compass_angle);
}

/**
 *
 * @brief 地磁订阅回调
 * @param type 数据类型
 * @param data 发布的数据
 * @param len 数据长度
 */
static void compass_in_raw_cb(int type, const void* data, int len)
{
    if (SENSOR_TYPE_ACCELEROMETER == type)
    {
        struct sensor_accel* in = (struct sensor_accel*) data;
        g_accel_data.x = in->x;
        g_accel_data.y = in->y;
        g_accel_data.z = in->z;
        g_accel_data.timestamp = in->timestamp;
        // rt_kprintf("accel timestamp:%d x:%d y:%d z:%d\n", g_accel_data.timestamp, g_accel_data.x, g_accel_data.y, g_accel_data.z);
    }
    else if (SENSOR_TYPE_MAGNETIC_FIELD == type)
    {
        struct sensor_mag* in = (struct sensor_mag*) data;
        g_mag_data.x = in->x;
        g_mag_data.y = in->y;
        g_mag_data.z = in->z;
        g_mag_data.timestamp = in->timestamp;
        // rt_kprintf("mag timestamp:%d x:%d y:%d z:%d\n", g_mag_data.timestamp, g_mag_data.x, g_mag_data.y, g_mag_data.z);
    }
}
static ecompass_listener_t listener = {&compass_in_raw_cb, &compass_result_cb};
#endif   // !SIMULATOR

void start_compass(void)
{
    if(s_start_flag)
    {
        return;
    }
    s_start_flag = true;
#ifndef SIMULATOR
    s_compass_align = ecompass_status_get(); // 获取初始校准状态
    ecompass_calibrate_start(&listener);
#endif   // !SIMULATOR
}

void stop_compass(void)
{
    if(!s_start_flag)
    {
        return;
    }
    s_start_flag = false;
#ifndef SIMULATOR
    ecompass_calibrate_stop();
#endif   // !SIMULATOR
    s_compass_align = INT32_MAX;
    s_compass_angle = INT32_MAX;
}

int get_compass_align(void)
{
    return s_compass_align;
}

int get_compass_angle(void)
{
    return s_compass_angle;
}

static void compass_ctrl(int argc, char **argv)
{
    if( argc < 2)
    {
        return;
    }

    if( strcmp(argv[1], "s_start_flag") == 0)
    {
        rt_kprintf("s_start_flag:%d\n", s_start_flag);
    }
}

MSH_CMD_EXPORT(compass_ctrl, compass_ctrl);
