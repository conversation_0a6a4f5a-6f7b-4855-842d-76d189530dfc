/**
 * @*************************************** Copyright (c) ***************************************
 * @                              <PERSON>han <PERSON>wu Technology Co., Ltd
 * @*********************************************************************************************
 * @Author: <PERSON><PERSON>hen
 * @Date: 2021-11-11 10:01:30
 * @LastEditTime: 2022-03-04 17:02:03
 * @LastEditors: JiangZhen
 * @FilePath: \iGS630_App\Application\App\radio\ble\ble_central.c
 * @Description:
 * @*********************************************************************************************
 */

#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include "ble_common.h"
#include "ble_central.h"
#include "ble_hrs_c.h"
#include "ble_cscs_c.h"
#include "ble_dis_c.h"
#include "ble_bas_c.h"
#include "app_timer.h"
#include "cbsc_rx.h"

#include "ble_db_discovery.h"
#include "ble_srv_common.h"

#include "ble.h"
#include "ble_conn_state.h"
#include "nrf_sdh.h"
#include "nrf_sdh_soc.h"
#include "nrf_sdh_ble.h"

#include "nrf_fstorage.h"
#include "nrf_ble_scan.h"

#include "peer_manager.h"

#include "app_error.h"
#include "nordic_common.h"
#include "nrf_log.h"
#include "nrf_log_ctrl.h"
#include "nrf_log_default_backends.h"


#include "qw_sensor_common.h"
#include "basictype.h"
#include "ble_nus_c.h"
#include "ble_nus_c_srv.h"
#include "sensor_ble.h"
#include "peripheral_light_pb.h"
#include "ble_light_cfg.h"
#include "ble_hrm_cfg.h"
#include "ble_bas_cfg.h"
#include "ble_bsc_cfg.h"
#include "ble_cad_cfg.h"
#include "ble_spd_cfg.h"
#include "ble_nus_evt.h"
#include "ser_user_data_def.h"
#include "blt_inbike_pb.h"
#include "qw_sensor_data.h"
#include "qw_sensor_scan.h"
#include "ble_dis_service.h"
#include "pm_process.h"

// #define APP_SOC_OBSERVER_PRIO                       1                  /**< Applications' SoC observer priority. You shouldn't need to modify this value. */

// #define BLE_C_DISCOVERY_INDEX_MAX                   (NRF_SDH_BLE_CENTRAL_LINK_COUNT - 1)

// #define CONN_TIMEOUT                                8000
// #define NOTIFICATION_TIMEOUT                        5000
// #define DATA_RX_TIMEOUT                             5000
// #define DISCONN_TIMEOUT                             5000

// BLE_HRS_C_DEF(m_hrs_c);                                             /**< Structure used to identify the heart rate client module. */
// BLE_BAS_C_ARRAY_DEF(m_bas_c, NRF_SDH_BLE_CENTRAL_LINK_COUNT);                                             /**< Structure used to identify the Battery Service client module. */
// BLE_DIS_C_DEF(m_dis_c);
// BLE_CSCS_C_ARRAY_DEF(m_cscs_c, 3);
// BLE_NUS_C_ARRAY_DEF(m_ble_nus_c, MAX_SENSOR_PERIPH_BLE);                                             /**< BLE Nordic UART Service (NUS) client instance. */

// static uint16_t m_conn_handle_hrs_c  = BLE_CONN_HANDLE_INVALID;  /**< Connection handle for the HRS central application */
// static uint16_t m_conn_handle_cscs_c = BLE_CONN_HANDLE_INVALID;  /**< Connection handle for the CSCS central application */
// static uint16_t m_conn_handle_spd_c  = BLE_CONN_HANDLE_INVALID;
// static uint16_t m_conn_handle_cad_c  = BLE_CONN_HANDLE_INVALID;

// //低电量提示功耗，每个传感器每次连接只提示一次
// static uint8_t hrm_low_power_indicate_flag  = false;
// static uint8_t spd_low_power_indicate_flag  = false;
// static uint8_t cad_low_power_indicate_flag  = false;
// static uint8_t cbsc_low_power_indicate_flag = false;

static bool m_memory_access_in_progress;                        /**< Flag to keep track of ongoing operations on persistent memory. */ //
static bool scan_and_connect_flag = false;                      //搜索并连接标志，true则搜索到后直接连接
// static bool ant_search_status_stop_flag = false;

static ble_gap_scan_params_t g_ble_gap_scan_params;
static ble_gap_conn_params_t g_conn_params;
static ble_gap_addr_t g_ble_gap_addr;

// static uint8_t ble_conn_addr[BLE_GAP_ADDR_LEN] = {0x00};
static uint8_t ble_scan_pending = false;
static sensor_type_t sensor_connecting_type = SENSOR_TYPE_INVALID;

#define BLE_CONNECT_RETRY_MAX 3
static uint8_t ble_connect_retry = 0;

#if ANT_SENSOR_BPWR_ENABLED
extern uint8_t g_pwrCad;
extern uint8_t g_pwrSpd;
#else
static uint8_t g_pwrCad = 0;
static uint8_t g_pwrSpd = 0;
#endif

#define MODULE_PRINTF rt_kprintf
// #define MODULE_PRINTF(...)
//--------------------------------------传感器的状态信息----------------------------------//
// typedef enum
// {
//     ble_sensor_state_idle          = 0,   //空闲状态
//     ble_sensor_state_connecting    = 1,   //连接状态
//     ble_sensor_state_discovery     = 2,   //发现服务
//     ble_sensor_state_data_rx       = 3,   //数据接收状态
//     ble_sensor_state_disconnecting = 4    //断连中
// }ble_sensor_state_st;

// typedef enum
// {
//     ble_sensor_event_connecting_timeout   = 0,
//     ble_sensor_event_discovery_timeout    = 1,
//     ble_sensor_event_notification_timeout = 2,
//     ble_sensor_event_data_rx_timeout      = 3
// }ble_sensor_event_st;

// typedef struct ble_sensor {
//     uint8_t dev_mac[BLE_GAP_ADDR_LEN];
//     ble_sensor_state_st  state;
//     uint16_t ticks;                 //单位ms
//     uint16_t conn_handle;
// }ble_sensor_st;

// ble_sensor_st ble_sensor_hrm =
// {
//     .conn_handle = BLE_CONN_HANDLE_INVALID
// };
// ble_sensor_st ble_sensor_spd =
// {
//     .conn_handle = BLE_CONN_HANDLE_INVALID
// };
// ble_sensor_st ble_sensor_cad =
// {
//     .conn_handle = BLE_CONN_HANDLE_INVALID
// };
// ble_sensor_st ble_sensor_cscs =
// {
//     .conn_handle = BLE_CONN_HANDLE_INVALID
// };
//--------------------------------------宏定义---------------------------------------------//
// #define TIMER_INTERVAL APP_TIMER_TICKS(50)//APP_TIMER_TICKS(100)

//--------------------------------------变量定义-------------------------------------------//
APP_TIMER_DEF(timer_id); //1000ms定时器   ID


/**@brief UUIDs that the central application scans for if the name above is set to an empty string,
 * and that are to be advertised by the peripherals.
 */
#define HART_RATE_SERVICE_UUID_IDX                  0                   /**< Hart Rate service UUID index in array. */
#define CSCS_SERVICE_UUID_IDX                       1                   /**< CSCS service UUID index in array. */
#define IGS_LIGHT_UUID_IDX                          2
#define IGS_REMOTE_UUID_IDX


static ble_uuid_t m_uuids[] =
{
    {BLE_UUID_HEART_RATE_SERVICE       , BLE_UUID_TYPE_BLE},
    {BLE_UUID_CYCLING_SPEED_AND_CADENCE, BLE_UUID_TYPE_BLE},
#if defined(SENSOR_LIGHT_ENABLED) && (SENSOR_LIGHT_ENABLED != 0)
    {IGS_BLE_LIGHT_UUID, BLE_UUID_TYPE_VENDOR_BEGIN},
#endif
    //{IGS_BLE_REMOTE_UUID, BLE_UUID_TYPE_VENDOR_BEGIN + 1},
};

static const char *m_name_filter_list[] = {
    // "Nordic_UART",
    "TL30",
    "VS1200",
    "VS800",
};

static ble_gap_scan_params_t m_scan_param =                 /**< Scan parameters requested for scanning and connection. */
{
    .active        = 0x01,
    .interval      = NRF_BLE_SCAN_SCAN_INTERVAL,
    .window        = NRF_BLE_SCAN_SCAN_WINDOW,
    .filter_policy = BLE_GAP_SCAN_FP_ACCEPT_ALL,
    .timeout       = NRF_BLE_SCAN_SCAN_DURATION,
    .scan_phys     = BLE_GAP_PHY_1MBPS,
    .extended      = true,
};

NRF_BLE_SCAN_DEF(m_scan);                                           /**< Scanning module instance. */

bool ble_light_adv_parse_process_found(sensor_search_infor_t* p_sensor_search_infor, uint8_array_t* p_advdata, uint8_array_t* p_parse);
bool ble_light_adv_parse_process_first(sensor_search_infor_t* p_sensor_search_infor, uint8_array_t* p_advdata, uint8_array_t* p_parse);
// static void sensor_state_refresh(sensor_type_t sensor_type, uint8_t *addr, ble_sensor_state_st  state, uint16_t conn_handle);

nrf_ble_scan_t* get_nrf_ble_scan_handler(void)
{
    return &m_scan;
}

ble_gap_scan_params_t* get_nrf_ble_scan_default_params(void)
{
    return &m_scan_param;
}

/************************************************************************/
static uint16_t gap_connect_timeout = 0;
static uint8_t cur_connect_mac[BLE_GAP_ADDR_LEN];
static uint32_t ble_gap_connect_request(ble_gap_addr_t const *p_peer_addr, ble_gap_scan_params_t const *p_scan_params, ble_gap_conn_params_t const *p_conn_params, uint8_t conn_cfg_tag)
{
    if(gap_connect_timeout == 0)
    {
        gap_connect_timeout = 200; //200*100 = 20s
        memcpy(cur_connect_mac, p_peer_addr->addr, BLE_GAP_ADDR_LEN);

        sensor_ble_scan_stop();
        return sd_ble_gap_connect(p_peer_addr, p_scan_params, p_conn_params, conn_cfg_tag);
    }
    return 0;
}

static bool ble_gap_connect_allowed(void)
{
    return (gap_connect_timeout == 0);
}

static void ble_gap_connect_timeout_check(void)
{
    if(gap_connect_timeout > 0)
    {
        gap_connect_timeout --;
        if(gap_connect_timeout == 0)
        {
            if(sensor_connecting_type != SENSOR_TYPE_INVALID)
            {
                sensor_connect_infor_t sensor_connect;
                if (sensor_connect_infor_get(sensor_connecting_type, &sensor_connect))
                {
                    if (SENSOR_CONNECT_STATE_CONNECTING == sensor_connect.state)
                    {
                        sd_ble_gap_connect_cancel();
                    }
                    else
                    {
                        ble_central_disconnect(sensor_connecting_type);
                    }
                }
                ble_sensor_disconnect(sensor_connecting_type, true);
                sensor_connecting_type = SENSOR_TYPE_INVALID;
                MODULE_PRINTF("ble_gap_connect_timeout\n");
            }
        }
    }
}

void ble_gap_connect_release(void)
{
    if(gap_connect_timeout)
    {
        MODULE_PRINTF("%s\n", __func__);
        gap_connect_timeout = 0;
        sensor_connecting_type = SENSOR_TYPE_INVALID;
    }
}

static void ble_gap_connect_clear_check(uint8_t *mac)
{
    if(gap_connect_timeout)
    {
        if(!memcmp(cur_connect_mac, mac, BLE_GAP_ADDR_LEN))
        {
            MODULE_PRINTF("%s\n", __func__);
            gap_connect_timeout = 0;
        }
    }
}

/**
 * @*********************************************************************************************
 * @description: 获取连接中的传感器类型
 * @param {*}
 * @return {*}
 * @*********************************************************************************************
 */
sensor_type_t sensor_connecting_dev_type_get(void)
{
    sensor_connect_infor_t sensor_connect;

    sensor_connect_infor_get(SENSOR_TYPE_CAD, &sensor_connect);
    if ((sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING || sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)
     && SENSOR_RADIO_TYPE_BLE == sensor_connect.radio_type)
    {
        return SENSOR_TYPE_CAD;
    }

    sensor_connect_infor_get(SENSOR_TYPE_SPD, &sensor_connect);
    if ((sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING || sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)
     && SENSOR_RADIO_TYPE_BLE == sensor_connect.radio_type)
    {
        return SENSOR_TYPE_SPD;
    }

    sensor_connect_infor_get(SENSOR_TYPE_CBSC, &sensor_connect);
    if ((sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING || sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)
     && SENSOR_RADIO_TYPE_BLE == sensor_connect.radio_type)
    {
        return SENSOR_TYPE_CBSC;
    }

    sensor_connect_infor_get(SENSOR_TYPE_HRM, &sensor_connect);
    if ((sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING || sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)
     && SENSOR_RADIO_TYPE_BLE == sensor_connect.radio_type)
    {
        return SENSOR_TYPE_HRM;
    }

    return SENSOR_TYPE_INVALID;
}


/***********************************************************************************************/
//-------------------------------------------------------------------------------------------
// Function Name : ble_central_services_init
// Purpose       : BLE主服务初始化
// Param[in]     : void
// Param[out]    : None
// Return type   :
// Comment       : 2020-04-28
//-------------------------------------------------------------------------------------------
void ble_central_services_init(void)
{
    hrs_c_init();

    cscs_c_init();
#if (SENSOR_DEVICE_INFO_ENABLED)
    dis_c_init();
#endif
    bas_c_init();

    nus_c_init();

    // ble_center_timer_create();

}

/**
 * @brief Parses advertisement data, providing length and location of the field in case
 *        matching data is found.
 *
 * @param[in]  type       Type of data to be looked for in advertisement data.
 * @param[in]  p_advdata  Advertisement report length and pointer to report.
 * @param[out] p_typedata If data type requested is found in the data report, type data length and
 *                        pointer to data will be populated here.
 *
 * @retval NRF_SUCCESS if the data type is found in the report.
 * @retval NRF_ERROR_NOT_FOUND if the data type could not be found.
 */
static uint32_t adv_report_parse(uint8_t type, uint8_array_t * p_advdata, uint8_array_t * p_typedata)
{
    uint32_t  index = 0;
    uint8_t * p_data;

    p_data = p_advdata->p_data;

    while (index < p_advdata->size)
    {
        uint8_t field_length = p_data[index];
        uint8_t field_type   = p_data[index + 1];

        if (field_type == type)
        {
            p_typedata->p_data = &p_data[index + 2];
            p_typedata->size   = field_length - 1;
            return NRF_SUCCESS;
        }
        index += field_length + 1;
    }
    return NRF_ERROR_NOT_FOUND;
}

/**@brief Function for handling the advertising report BLE event.
 *
 * @param[in] p_ble_evt  Bluetooth stack event.
 */
static void on_adv_report(scan_evt_t const * p_scan_evt)
{
    sensor_work_state_t    sensor_work_state = SENSOR_WORK_STATE_IDLE;
    uint16_t               uuid              = 0;
    sensor_connect_infor_t sensor_connect;
    uint32_t               err_code;
    uint8_array_t          adv_data;
    uint8_array_t          adv_parse;
    sensor_search_infor_t  sensor_search_infor;
    sensor_search_infor_t  store_sensor_search_infor;
    bool is_nus_sim_device  = false;
    bool found = false;

    ble_gap_evt_adv_report_t const *p_adv = p_scan_evt->params.filter_match.p_adv_report;
    ble_gap_scan_params_t    const *p_scan_param = p_scan_evt->p_scan_params;

    memset (&sensor_search_infor, 0, sizeof(sensor_search_infor_t));

    sensor_search_infor.radio_type = SENSOR_RADIO_TYPE_BLE;
    sensor_search_infor.rssi       = p_adv->rssi;
    memcpy (sensor_search_infor.sensor_id.ble_mac_addr, p_adv->peer_addr.addr, BLE_GAP_ADDR_LEN);

    adv_data.p_data = (uint8_t *)p_adv->data.p_data;
    adv_data.size   = p_adv->data.len;

    found = sensor_ble_search_infor_get(sensor_search_infor.sensor_id.ble_mac_addr, &store_sensor_search_infor);
    if (found)
    {
    	store_sensor_search_infor.rssi = sensor_search_infor.rssi;
    	memcpy(&sensor_search_infor, &store_sensor_search_infor, sizeof(sensor_search_infor_t));

        if (ser_user_config_blelight_periph_support() && sensor_ble_connect_buffer_mailbox_query(sensor_search_infor.sensor_id.ble_mac_addr)) //查询队列里是否有搜索到的蓝牙mac
        {
            is_nus_sim_device = ble_light_adv_parse_process_found(&sensor_search_infor, &adv_data, &adv_parse);
            goto end_1;
        }
        else
        {
            goto end_2;
        }
    }
    is_nus_sim_device = ble_light_adv_parse_process_first(&sensor_search_infor, &adv_data, &adv_parse);

    if (sensor_search_infor.sensor_type == SENSOR_TYPE_INVALID) {
        //根据UUID，获取传感器类型
        for (uint8_t i = 0; i < adv_parse.size / sizeof(uint16_t); i++)
        {
            memcpy((uint8_t*)&uuid, adv_parse.p_data, sizeof(uint16_t));

            switch (uuid)
            {
            case BLE_UUID_HEART_RATE_SERVICE:
                sensor_search_infor.sensor_type = SENSOR_TYPE_HRM;
                break;
            case BLE_UUID_CYCLING_SPEED_AND_CADENCE:
                sensor_search_infor.sensor_type = ble_cscs_dev_type_get(p_adv->peer_addr.addr);
                break;
            default:
                return;
            }
        }

        // 解析蓝牙名称
        err_code = adv_report_parse(BLE_GAP_AD_TYPE_COMPLETE_LOCAL_NAME,
                                    &adv_data,
                                    &adv_parse);
        if (NRF_SUCCESS == err_code)
        {
            // 防止拷贝溢出
            if (adv_parse.size >= SENSOR_BLE_NAME_LENGTH_MAX)
            {
                memcpy(sensor_search_infor.ble_name, adv_parse.p_data, SENSOR_BLE_NAME_LENGTH_MAX - 1);
            }
            else
            {
                memcpy(sensor_search_infor.ble_name, adv_parse.p_data, adv_parse.size);
            }
        }
        goto end_2;
    }
end_1:
    if (ser_user_config_blelight_periph_support()){
        ble_light_rssi_update(p_adv->peer_addr.addr, p_adv->rssi);
        // MODULE_PRINTF("[is_nus_sim_device] %d\n", is_nus_sim_device);
        if(is_nus_sim_device)
        {
            MODULE_PRINTF("[sensor name] %s\n", sensor_search_infor.ble_name);
            if(sensor_ble_channel_status_get(p_adv->peer_addr.addr, SENSOR_BLE_CHANNEL_DISCOVERY)){
                bool search_found  = sensor_search_infor_get(&sensor_search_infor);
                if(search_found){
                    sensor_search_infor_del(&sensor_search_infor);             //从搜索到的列表中删除要连接的蓝牙传感器
                }
            }
            else if(!sensor_ble_channel_status_is_busy(p_adv->peer_addr.addr) && sensor_ble_channel_connect_is_allowed() && ble_gap_connect_allowed()
                && ((BLE_LIGHT_STATUS_IDLE == ble_light_status_get(p_adv->peer_addr.addr)) || (BLE_LIGHT_STATUS_DISCONNECT == ble_light_status_get(p_adv->peer_addr.addr))))
            {
                // sensor_ble_connecting_handler_set(p_adv->peer_addr.addr);
                sensor_ble_channel_request(p_adv->peer_addr.addr);          //存蓝牙传感器的mac地址
                MODULE_PRINTF("ble_light_connect--->%02x,%02x,%02x,%02x,%02x,%02x\n", p_adv->peer_addr.addr[0], p_adv->peer_addr.addr[1], p_adv->peer_addr.addr[2],
                    p_adv->peer_addr.addr[3], p_adv->peer_addr.addr[4], p_adv->peer_addr.addr[5]);
                sensor_ble_channel_status_set(p_adv->peer_addr.addr, SENSOR_BLE_CHANNEL_CONNECTING, BLE_CONN_HANDLE_INVALID, sensor_search_infor.ble_name);
                //rt_kprintf("[sensor name] %s\n", sensor_search_infor.ble_name);
                err_code = ble_gap_connect_request(&p_adv->peer_addr, p_scan_param, &m_scan.conn_params, APP_BLE_CONN_CFG_TAG);          //连接蓝牙传感器
                APP_ERROR_CHECK(err_code);
            }
            memcpy(&g_ble_gap_scan_params, p_scan_param, sizeof(ble_gap_scan_params_t));
            memcpy(&g_conn_params, &m_scan.conn_params, sizeof(ble_gap_conn_params_t));
            return;
        }
    }
end_2:
    //修改搜索到的传感器信息
    sensor_display_infor_set(&sensor_search_infor);
    /***********************************************************/

    // MODULE_PRINTF("ble.sensor_type = %d\n", sensor_search_infor.sensor_type);
    if (FALSE == sensor_support_check(sensor_search_infor.sensor_type))
    {
        return;
    }

    /***二合一传感器，连接断连后保存成了spd，重新搜索到的是bsc.***/
    sensor_wait_connect_t wait_connect = {0};
    wait_connect.radio_type = SENSOR_RADIO_TYPE_BLE;
    memcpy(&wait_connect.sensor_id, &sensor_search_infor.sensor_id, sizeof(sensor_id_t));

    if(sensor_wait_connect_has_id_mac(&wait_connect)) //搜索到的ble地址和wait里边的ble地址相同
    {
        sensor_search_infor.sensor_type = wait_connect.sensor_type;
    }
    else
    {
        wait_connect.sensor_type = sensor_search_infor.sensor_type;
    }

    if (FALSE == sensor_channel_busy_check(sensor_search_infor.sensor_type) && ble_gap_connect_allowed())
    {
        if(sensor_wait_connect_has_item(&wait_connect))
        {
            memset(&sensor_connect, 0, sizeof(sensor_connect_infor_t));
            sensor_connect.radio_type = SENSOR_RADIO_TYPE_BLE;
            memcpy((uint8_t *)&sensor_connect.sensor_id, &sensor_search_infor.sensor_id, sizeof(sensor_id_t));

            sensor_connect_infor_prepare(sensor_search_infor.sensor_type, &sensor_connect);
            sensor_wait_connect_info_remove(&wait_connect);

            sensor_connect.state      = SENSOR_CONNECT_STATE_CONNECTING;
            sensor_connect_infor_set(wait_connect.sensor_type, &sensor_connect);

            sensor_connecting_type = sensor_search_infor.sensor_type;
            MODULE_PRINTF("ble_sensor_connect-->%x-%x-%x\n",sensor_search_infor.sensor_id.ble_mac_addr[3],\
                            sensor_search_infor.sensor_id.ble_mac_addr[4],sensor_search_infor.sensor_id.ble_mac_addr[5]);

            err_code = ble_gap_connect_request(&p_adv->peer_addr, p_scan_param, &m_scan.conn_params, APP_BLE_CONN_CFG_TAG);
            APP_ERROR_CHECK(err_code);

            memcpy(&g_ble_gap_scan_params, p_scan_param, sizeof(ble_gap_scan_params_t));
            memcpy(&g_conn_params, &m_scan.conn_params, sizeof(ble_gap_conn_params_t));
        }
    }
}

static void on_adv_not_found_report(scan_evt_t const * p_scan_evt)
{
    uint32_t               err_code;
    uint8_array_t          adv_data;
    uint8_array_t          adv_parse;
    sensor_search_infor_t  store_sensor_search_infor;

    ble_gap_evt_adv_report_t const *p_adv = p_scan_evt->params.filter_match.p_adv_report;

    adv_data.p_data = (uint8_t *)p_adv->data.p_data;
    adv_data.size   = p_adv->data.len;

    bool found = sensor_ble_search_infor_get((uint8_t *)p_adv->peer_addr.addr, &store_sensor_search_infor);
    if (found && !strlen ((char *)store_sensor_search_infor.ble_name))
    {
    	// 解析蓝牙名称
		err_code = adv_report_parse(BLE_GAP_AD_TYPE_COMPLETE_LOCAL_NAME,
									&adv_data,
									&adv_parse);
		if (NRF_SUCCESS == err_code)
		{
			//防止拷贝溢出
			if (adv_parse.size >= SENSOR_BLE_NAME_LENGTH_MAX)
			{
				memcpy (store_sensor_search_infor.ble_name, adv_parse.p_data, SENSOR_BLE_NAME_LENGTH_MAX - 1);
			}
			else
			{
				memcpy (store_sensor_search_infor.ble_name, adv_parse.p_data, adv_parse.size);
			}
            sensor_search_infor_set(&store_sensor_search_infor);
		}
    }
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_scan_filter_set
// Purpose       : 设置扫描过滤机制
// Param[in]     : uint32_t filter
//                 uint8_t *addr
// Param[out]    : None
// Return type   : static
// Comment       : 2021-03-24
//-------------------------------------------------------------------------------------------
static  void ble_scan_filter_set(uint32_t filter, uint8_t *addr)
{
    ret_code_t err_code;

    if (SCAN_FILTER_UNREFRESH != filter)
    {
        nrf_ble_scan_all_filter_remove(&m_scan);
    }

    if (filter & SCAN_FILTER_HRM_ENABLED)
    {
        err_code = nrf_ble_scan_filter_set(&m_scan,
                                           SCAN_UUID_FILTER,
                                           &m_uuids[HART_RATE_SERVICE_UUID_IDX]);
        APP_ERROR_CHECK(err_code);
    }

    if (filter & SCAN_FILTER_CSCS_ENABLED)
    {
        err_code = nrf_ble_scan_filter_set(&m_scan,
                                           SCAN_UUID_FILTER,
                                           &m_uuids[CSCS_SERVICE_UUID_IDX]);
        APP_ERROR_CHECK(err_code);
    }

    if(filter & SCAN_FILTER_NUS_ENABLED)
    {
#if defined(SENSOR_LIGHT_ENABLED) && (SENSOR_LIGHT_ENABLED != 0)
        for (uint8_t i = 0; i < sizeof(m_name_filter_list) / sizeof(m_name_filter_list[0]); i++)
        {
            err_code = nrf_ble_scan_filter_set(&m_scan,
                                            SCAN_NAME_FILTER,
                                                m_name_filter_list[i]);
            APP_ERROR_CHECK(err_code);
        }
#endif
        err_code = nrf_ble_scan_filter_set(&m_scan,
                                            SCAN_UUID_FILTER,
                                                &m_uuids[IGS_LIGHT_UUID_IDX]);
        APP_ERROR_CHECK(err_code);
    }

    if ((filter & SCAN_FILTER_ADDR_ENABLED)
        && (NULL != addr))
    {
        err_code = nrf_ble_scan_filter_set(&m_scan,
                                           SCAN_ADDR_FILTER,
                                           addr);
        APP_ERROR_CHECK(err_code);
    }

    if (SCAN_FILTER_UNREFRESH != filter)
    {
        err_code = nrf_ble_scan_filters_enable(&m_scan,
                                               NRF_BLE_SCAN_ALL_FILTER,
                                               false);
        APP_ERROR_CHECK(err_code);
    }
}


/**@brief Function for initializing the scanning.
 */
void ble_scan_start(uint32_t filter, uint8_t *addr)
{
    if(!sensor_get_close_all_status())
	{
		if(!(SENSOR_SEARCH_MODE_FACTORY & sensor_search_mode_get()))
        {
            ret_code_t err_code;

            ble_scan_filter_set(filter, addr);

            //check if there are no flash operations in progress
            if (!nrf_fstorage_is_busy(NULL))
            {
                sensor_ble_scan_params_set();
                err_code = nrf_ble_scan_start(&m_scan);
                if ((err_code != NRF_ERROR_INVALID_STATE) && (err_code != NRF_SUCCESS))
                {
                    MODULE_PRINTF("%s:err_code = %x.\n", __func__, err_code);
                }
            }
            else
            {
                m_memory_access_in_progress = true;
            }
        }
    }
}

/**
 * @*********************************************************************************************
 * @description: 停止搜索
 * @param {*}
 * @return {*}
 * @*********************************************************************************************
 */
void ble_scan_stop(void)
{
    nrf_ble_scan_stop();
}

static void scan_evt_handler(scan_evt_t const * p_scan_evt)
{
    switch(p_scan_evt->scan_evt_id)
    {
        case NRF_BLE_SCAN_EVT_FILTER_MATCH:
            on_adv_report(p_scan_evt);
            break;
        case NRF_BLE_SCAN_EVT_NOT_FOUND:
			on_adv_not_found_report(p_scan_evt);
			break;
        case NRF_BLE_SCAN_EVT_SCAN_TIMEOUT:
            sensor_ble_scan_start(SCAN_FILTER_UNREFRESH);
            break;
        default:
            break;
    }
}

/**@brief SoftDevice SoC event handler.
 *
 * @param[in]   evt_id      SoC event.
 * @param[in]   p_context   Context.
 */
static void soc_evt_handler(uint32_t evt_id, void * p_context)
{
    switch (evt_id)
    {
        case NRF_EVT_FLASH_OPERATION_SUCCESS:
            /* fall through */
        case NRF_EVT_FLASH_OPERATION_ERROR:

            if (m_memory_access_in_progress)
            {
                m_memory_access_in_progress = false;
                sensor_ble_scan_start(SCAN_FILTER_UNREFRESH);
            }
            break;

        default:
            // No implementation needed.
            break;
    }
}



/**@brief Function for initialization the scanning and setting the filters.
 */
void scan_init(void)
{
    ret_code_t          err_code;
    nrf_ble_scan_init_t init_scan;

    memset(&init_scan, 0, sizeof(init_scan));

    init_scan.conn_cfg_tag = APP_BLE_CONN_CFG_TAG;
    init_scan.p_scan_param = &m_scan_param;
    err_code = nrf_ble_scan_init(&m_scan, &init_scan, scan_evt_handler);
    APP_ERROR_CHECK(err_code);
    //添加车灯uuid
    uint8_t uuid_type;
    ble_uuid128_t ble_light = IGS_LIGHT_UUID_BASE;
    err_code = sd_ble_uuid_vs_add(&ble_light, &uuid_type);
    APP_ERROR_CHECK(err_code);

    // //添加遥控器uuid
    // ble_uuid128_t ble_remote = IGS_REMOTE_UUID_BASE;
    // err_code = sd_ble_uuid_vs_add(&ble_remote, &uuid_type);
    // APP_ERROR_CHECK(err_code);

    NRF_SDH_SOC_OBSERVER(m_soc_observer, APP_SOC_OBSERVER_PRIO, soc_evt_handler, NULL);
}

//-------------------------------------------------------------------------------------------
// Function Name : gap_conn_params_c_set
// Purpose       : 设置作为主蓝牙gap参数
// Param[in]     : ble_gap_conn_params_t  *gap_conn_params
// Param[out]    : None
// Return type   :
// Comment       : 2020-04-28
//-------------------------------------------------------------------------------------------
void gap_conn_params_c_set(ble_gap_conn_params_t  *gap_conn_params)
{
    gap_conn_params->min_conn_interval = m_scan.conn_params.min_conn_interval;
    gap_conn_params->max_conn_interval = m_scan.conn_params.max_conn_interval;
    gap_conn_params->slave_latency     = m_scan.conn_params.slave_latency;
    gap_conn_params->conn_sup_timeout  = m_scan.conn_params.conn_sup_timeout;
}

/**@brief Function for handling database discovery events.
 *
 * @details This function is a callback function to handle events from the database discovery module.
 *          Depending on the UUIDs that are discovered, this function forwards the events
 *          to their respective services.
 *
 * @param[in] p_event  Pointer to the database discovery event.
 */
void ble_central_disc_handler(ble_db_discovery_evt_t * p_evt)
{
    if(p_evt->conn_handle == ble_hrs_conn_handle_get()) //心率
    {
        ble_central_hrs_disc_handler(p_evt);
    }
    if(p_evt->conn_handle == ble_cscs_conn_handle_get()
        || p_evt->conn_handle == ble_spd_conn_handle_get()
        || p_evt->conn_handle == ble_cad_conn_handle_get()) //速度踏频
    {
        ble_central_cscs_disc_handler(p_evt);
    }
    //电量服务
    ble_central_bas_disc_handler(p_evt);
    //nus
    ble_central_nus_disc_handler(p_evt);
}

void ble_sensor_disconnect(sensor_type_t sensor_type, bool reconnect)
{
    sensor_search_infor_t     sensor_search_infor;
    sensor_module_evt_handler evt_handler             = sensor_module_evt_handler_get();
    sensor_saved_work_t       *p_sensor_saved         = NULL;
    sensor_original_data_t    *p_sensor_original_data = sensor_original_data_get();
    sensor_work_state_t       sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    int8_t                    index                   = -1;
    sensor_connect_infor_t    sensor_connect;
    bool found = sensor_connect_infor_get(sensor_type, &sensor_connect);
    if(!found)
    {
        return;
    }

    memset ((uint8_t *)&sensor_search_infor, 0x00, sizeof(sensor_search_infor_t));

    memcpy ((uint8_t *)&sensor_search_infor.sensor_id, &sensor_connect.sensor_id, sizeof(sensor_id_t));
    sensor_search_infor.sensor_type = sensor_type;
    sensor_search_infor.radio_type  = sensor_connect.radio_type;

    bool forbidden_mask = sensor_connect_infor_get_forbidden_mask(sensor_type);
    if (sensor_connect_infor_get(sensor_type, &sensor_connect))
    {
        sensor_connect_infor_clear(sensor_type);
        if (NULL != evt_handler && (sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING
            || sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED) && (false == reconnect))
        {
            evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
        }
    }

    if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
    {
        if (SENSOR_WORK_STATE_IDLE != sensor_work_state)
        {
            p_sensor_saved->rssi             [index] = 0;
            p_sensor_saved->battery_voltage  [index] = 0xff;
            p_sensor_saved->sensor_work_state[index] = SENSOR_WORK_STATE_SAVED;
        }
        sensor_saved_work_infor_release_write_lock(index);
    }

    switch (sensor_type)
    {
        case SENSOR_TYPE_HRM:
            if (BLE_CONN_HANDLE_INVALID != ble_hrs_conn_handle_get() && NULL != evt_handler)
            {
                evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, SENSOR_TYPE_HRM, SENSOR_RADIO_TYPE_BLE, FALSE);
            }
            ble_sensor_low_power_indicate_set(sensor_type, false);
            p_sensor_original_data->hrData.heart_rate = 0xff;
            // ble_hrs_conn_handle_set(BLE_CONN_HANDLE_INVALID);
            sensor_connect_infor_clear(SENSOR_TYPE_HRM);
            break;
        case SENSOR_TYPE_CBSC:
            if (BLE_CONN_HANDLE_INVALID != ble_cscs_conn_handle_get() && NULL != evt_handler)
            {
                evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, SENSOR_TYPE_CBSC, SENSOR_RADIO_TYPE_BLE, FALSE);
                InitAntSpdAndCadStatus();
            }
            ble_sensor_low_power_indicate_set(sensor_type, false);
            p_sensor_original_data->cbscData.cadence     = 0xff;
            p_sensor_original_data->cbscData.wheel_delta = 0xffff;
            p_sensor_original_data->cbscData.wheel_speed = 0xffff;
            // ble_cscs_conn_handle_set(BLE_CONN_HANDLE_INVALID);
            sensor_connect_infor_clear(SENSOR_TYPE_CBSC);
            break;
        case SENSOR_TYPE_CAD:
            if (BLE_CONN_HANDLE_INVALID != ble_cad_conn_handle_get() && NULL != evt_handler)
            {
                evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, SENSOR_TYPE_CAD, SENSOR_RADIO_TYPE_BLE, FALSE);
                InitAntCadStatus();
            }
            ble_sensor_low_power_indicate_set(sensor_type, false);
            p_sensor_original_data->cadData.cadence = 0xff;
            // ble_cad_conn_handle_set(BLE_CONN_HANDLE_INVALID);
            sensor_connect_infor_clear(SENSOR_TYPE_CAD);
            break;
        case SENSOR_TYPE_SPD:
            if (BLE_CONN_HANDLE_INVALID != ble_spd_conn_handle_get() && NULL != evt_handler)
            {
                evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, SENSOR_TYPE_SPD, SENSOR_RADIO_TYPE_BLE, FALSE);
                InitAntSpdStatus();
            }
            ble_sensor_low_power_indicate_set(sensor_type, false);
            p_sensor_original_data->spdData.wheel_delta = 0xffff;
            p_sensor_original_data->spdData.wheel_speed = 0xffff;
            // ble_spd_conn_handle_set(BLE_CONN_HANDLE_INVALID);
            sensor_connect_infor_clear(SENSOR_TYPE_SPD);
            break;
        default:
            break;
    }

    if (!forbidden_mask && reconnect)
    {
        if(SENSOR_WORK_STATE_IDLE == sensor_work_state)
        {
            if(ble_connect_retry < BLE_CONNECT_RETRY_MAX)
            {
                sensor_wait_connect_info_loadfrom_search_info(&sensor_search_infor);
                ble_connect_retry ++;
            }
            else
            {
                ble_connect_retry = 0;
            }
        }
        else if(SENSOR_WORK_STATE_SAVED == sensor_work_state)
        {
            sensor_wait_connect_info_loadfrom_search_info(&sensor_search_infor);
        }
    }
}

static sensor_type_t ble_sensor_get_type(uint16_t con)
{
    sensor_type_t sensor_type = SENSOR_TYPE_INVALID;
    if (con == ble_hrs_conn_handle_get())
    {
        sensor_type = SENSOR_TYPE_HRM;
    }
    else if (con == ble_cscs_conn_handle_get())
    {
        sensor_type = SENSOR_TYPE_CBSC;
    }
    else if (con == ble_cad_conn_handle_get())
    {
        sensor_type = SENSOR_TYPE_CAD;
    }
    else if (con == ble_spd_conn_handle_get())
    {
        sensor_type = SENSOR_TYPE_SPD;
    }
    if(SENSOR_TYPE_INVALID == sensor_type)
    {
        if(sensor_connecting_type != SENSOR_TYPE_INVALID)
        {
            sensor_type = sensor_connecting_type;
        }
    }
    return sensor_type;
}

/**@brief   Function for handling BLE events from the central application.
 *
 * @details This function parses scanning reports and initiates a connection to peripherals when a
 *          target UUID is found. It updates the status of LEDs used to report the central application
 *          activity.
 *
 * @param[in]   p_ble_evt   Bluetooth stack event.
 */
void on_ble_central_evt(ble_evt_t const * p_ble_evt)
{
    ret_code_t            err_code;
    ble_gap_evt_t const       *p_gap_evt  = &p_ble_evt->evt.gap_evt;

    sensor_type_t sensor_type = SENSOR_TYPE_INVALID;

    switch (p_ble_evt->header.evt_id)
    {
        // Upon connection, check which peripheral is connected (HR or CSC), initiate DB
        // discovery, update LEDs status, and resume scanning, if necessary.
        case BLE_GAP_EVT_CONNECTED:
        {
            MODULE_PRINTF("Central connected\n");
            sensor_type = sensor_connecting_dev_type_get();
            MODULE_PRINTF("sensor_connecting_dev_type_get = %d\n", sensor_type);

            if(sensor_ble_channel_status_get(p_ble_evt->evt.gap_evt.params.connected.peer_addr.addr,SENSOR_BLE_CHANNEL_CONNECTING))
            {
                //rt_kprintf("connect p_gap_evt->conn_handle == %d\n",p_gap_evt->conn_handle);
                sensor_ble_channel_status_set(p_ble_evt->evt.gap_evt.params.connected.peer_addr.addr,SENSOR_BLE_CHANNEL_CONNECTED,p_gap_evt->conn_handle, NULL);
            }
            else if (SENSOR_TYPE_INVALID != sensor_type)
            {
                sensor_connect_infor_t      sensor_connect;
                if(sensor_connect_infor_get(sensor_type, &sensor_connect))
                {
                    if (SENSOR_CONNECT_STATE_CONNECTING == sensor_connect.state)
                    {
                        sensor_connect.state = SENSOR_CONNECT_STATE_CONNECTED;
                        sensor_connect_infor_set(sensor_type, &sensor_connect);
                        ble_sensor_conn_handle_set(sensor_type, p_gap_evt->conn_handle);
                    }
                }
            }

            for (uint8_t i = 0; i <= BLE_C_DISCOVERY_INDEX_MAX; i++)
            {
                err_code = ble_discovery_start(i, p_gap_evt->conn_handle);

                if (err_code == NRF_ERROR_BUSY)
                {
                    continue;
                }
                else
                {
                    APP_ERROR_CHECK(err_code);
                    break;
                }
            }
            // Assign connection handle to the QWR module.
            multi_qwr_conn_handle_assign(p_gap_evt->conn_handle);

            //ble_scan_start(SCAN_FILTER_CSCS_ENABLED | SCAN_FILTER_HRM_ENABLED | SCAN_FILTER_NUS_ENABLED, NULL);
        } break; // BLE_GAP_EVT_CONNECTED

        // Upon disconnection, reset the connection handle of the peer that disconnected,
        // update the LEDs status and start scanning again.
        case BLE_GAP_EVT_DISCONNECTED:
        {
            sensor_type = ble_sensor_get_type(p_gap_evt->conn_handle);
            MODULE_PRINTF("BLE %d central disconnected (reason: %x)\n",sensor_type, p_gap_evt->params.disconnected.reason);

            if(sensor_type == SENSOR_TYPE_CBSC) //防止连接上二合一sensor后显示的type为spd或cad，断连时返回的sensor_type仍为cbsc
            {
                sensor_connect_infor_t sensor_connect_info;
                if(!sensor_connect_infor_get_state(SENSOR_TYPE_CBSC, SENSOR_CONNECT_STATE_CLOSE_WAIT|SENSOR_CONNECT_STATE_CONNECTING|SENSOR_CONNECT_STATE_CONNECTED|SENSOR_CONNECT_STATE_DISCOVERY))
                {
                    if(sensor_connect_infor_get_state(SENSOR_TYPE_CAD, SENSOR_CONNECT_STATE_CLOSE_WAIT))
                    {
                        sensor_type = SENSOR_TYPE_CAD;
                    }
                    else if(sensor_connect_infor_get_state(SENSOR_TYPE_SPD, SENSOR_CONNECT_STATE_CLOSE_WAIT))
                    {
                        sensor_type = SENSOR_TYPE_SPD;
                    }
                }
                else
                {
                    MODULE_PRINTF("%s-%d:one cbsc sensor connected.\n",__func__,__LINE__);
                }
            }

            if(sensor_type != SENSOR_TYPE_INVALID)
            {
                sensor_connect_infor_t sensor_connect_info = {0};
                bool found = sensor_connect_infor_get(sensor_type, &sensor_connect_info);
                bool forbidden = sensor_connect_infor_get_forbidden_mask(sensor_type);
                ble_gap_connect_release();
                if (0x3e == p_gap_evt->params.disconnected.reason) //BLE_HCI_CONN_FAILED_TO_BE_ESTABLISHED
                {
                    ble_sensor_disconnect(sensor_type, true);
                }
                else
                {
                    ble_sensor_disconnect(sensor_type, false);
                }
                if (!sensor_ant_get_close_status() && FALSE == sensor_channel_busy_check(sensor_type))
                {
                    //有保存的同类型sensor，自动连接
                    if((!forbidden && sensor_connect_info.state == SENSOR_CONNECT_STATE_DISCOVERY)
                        || (forbidden && SENSOR_CONNECT_STATE_CLOSE_WAIT == sensor_connect_info.state))
                    {
                        sensor_connect_from_saved_info(sensor_type); //点击禁用后，从saved_info连接已保存的同类型sensor
                    }
                }
            }
            else
            {
                const uint8_t* tmp_mac = sensor_ble_channel_mac_addr_get(p_gap_evt->conn_handle);
                //rt_kprintf("p_gap_evt->conn_handle == %d\n",p_gap_evt->conn_handle);
                if (tmp_mac) {
                    MODULE_PRINTF("disconnect tmp_mac %x:%x:%x:%x:%x:%x\n",tmp_mac[0],tmp_mac[1],tmp_mac[2],tmp_mac[3],tmp_mac[4],tmp_mac[5]);
                    memcpy(g_ble_gap_addr.addr, tmp_mac, BLE_GAP_ADDR_LEN);
                    sensor_type = SENSOR_TYPE_LIGHT;

                    for(uint8_t i = 0; i < sensor_ble_channel_cnt_get(); i ++)
                    {
                        const uint8_t *mac = sensor_ble_channel_mac_get(i);
                        if(mac)
                        {
                            if (memcmp(mac, tmp_mac, BLE_GAP_ADDR_LEN) == 0) {
                                sensor_module_evt_handler   evt_handler = sensor_module_evt_handler_get();
                                sensor_ble_connecting_handler_clear();
                                if (evt_handler != NULL && 0x3e != p_gap_evt->params.disconnected.reason)
                                {
                                    if(BLE_HCI_LOCAL_HOST_TERMINATED_CONNECTION != p_gap_evt->params.disconnected.reason)
                                    {
                                        evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, NULL, sensor_type, SENSOR_RADIO_TYPE_BLE, FALSE);
                                    }
                                    evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, SENSOR_TYPE_LIGHT, SENSOR_RADIO_TYPE_BLE, 0);
                                }
                            }
                            if (0x3e == p_gap_evt->params.disconnected.reason || 0x08 == p_gap_evt->params.disconnected.reason)
                            {
                                sensor_ble_connect_buffer_mailbox_push(tmp_mac, false);
                            }
                        }
                    }
                    sensor_ble_channel_status_set(g_ble_gap_addr.addr, SENSOR_BLE_CHANNEL_DISCONNECTED, BLE_CONN_HANDLE_INVALID, NULL);
                }
                ble_gap_connect_release();
            }

            ble_sensor_conn_handle_set(sensor_type, BLE_CONN_HANDLE_INVALID);
            ble_gap_connect_clear_check(g_ble_gap_addr.addr);

            sensor_ant_scan_start();
            sensor_ble_scan_start(SCAN_FILTER_CSCS_ENABLED | SCAN_FILTER_HRM_ENABLED | SCAN_FILTER_NUS_ENABLED);
            NRF_LOG_INFO("BLE central disconnected (reason: %x)",
                             p_gap_evt->params.disconnected.reason);
        } break; // BLE_GAP_EVT_DISCONNECTED
        case BLE_GAP_EVT_TIMEOUT:
        {
            // No timeout for scanning is specified, so only connection attemps can timeout.
            if (p_gap_evt->params.timeout.src == BLE_GAP_TIMEOUT_SRC_CONN)
            {
                NRF_LOG_INFO("Connection Request timed out.");
            }
        } break;

        case BLE_GAP_EVT_CONN_PARAM_UPDATE_REQUEST:
        {
            // Accept parameters requested by peer.
            err_code = sd_ble_gap_conn_param_update(p_gap_evt->conn_handle, //NULL);
                                        &p_gap_evt->params.conn_param_update_request.conn_params);    //修改原因为，首次连接蓝牙传感器，接收到此事件后，会引起断连
            if (NRF_SUCCESS != err_code)
            {
                sd_ble_gap_disconnect(p_gap_evt->conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
            }
        } break;

        case BLE_GAP_EVT_PHY_UPDATE_REQUEST:
        {
            NRF_LOG_DEBUG("PHY update request.");
            ble_gap_phys_t const phys =
            {
                .rx_phys = BLE_GAP_PHY_AUTO,
                .tx_phys = BLE_GAP_PHY_AUTO,
            };
            err_code = sd_ble_gap_phy_update(p_ble_evt->evt.gap_evt.conn_handle, &phys);
            APP_ERROR_CHECK(err_code);
        } break;

        case BLE_GATTC_EVT_TIMEOUT:
            // Disconnect on GATT Client timeout event.
            MODULE_PRINTF("GATT Client Timeout.");
            err_code = sd_ble_gap_disconnect(p_ble_evt->evt.gattc_evt.conn_handle,
                                             BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
            APP_ERROR_CHECK(err_code);
            sensor_type = ble_sensor_get_type(p_gap_evt->conn_handle);
            if(sensor_type != SENSOR_TYPE_INVALID)
            {
                ble_sensor_disconnect(sensor_type, true);
                sensor_ble_scan_start(SCAN_FILTER_CSCS_ENABLED | SCAN_FILTER_HRM_ENABLED | SCAN_FILTER_NUS_ENABLED);
            }
            break;

        case BLE_GATTS_EVT_TIMEOUT:
            // Disconnect on GATT Server timeout event.
            MODULE_PRINTF("GATT Server Timeout.");
            err_code = sd_ble_gap_disconnect(p_ble_evt->evt.gatts_evt.conn_handle,
                                             BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
            APP_ERROR_CHECK(err_code);
            sensor_type = ble_sensor_get_type(p_gap_evt->conn_handle);
            if(sensor_type != SENSOR_TYPE_INVALID)
            {
                ble_sensor_disconnect(sensor_type, true);
                sensor_ble_scan_start(SCAN_FILTER_CSCS_ENABLED | SCAN_FILTER_HRM_ENABLED | SCAN_FILTER_NUS_ENABLED);
            }
            break;

        default:
            // No implementation needed.
            break;
    }
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_central_disconnect
// Purpose       : 断开连接
// Param[in]     : dev_type_st dev_type
// Param[out]    : None
// Return type   :
// Comment       : 2020-05-06
//-------------------------------------------------------------------------------------------
void ble_central_disconnect(sensor_type_t sensor_type)
{
    sensor_connect_infor_t    sensor_connect;
    bool found = sensor_connect_infor_get(sensor_type, &sensor_connect);
    MODULE_PRINTF("%s disconnect %s at %s\n", __func__, ant_ble_sensor_type_decription_str_get(sensor_type), ant_ble_sensor_type_states_str_get(sensor_connect.state));

    switch (sensor_type)
    {
        case SENSOR_TYPE_HRM:
        	if(BLE_CONN_HANDLE_INVALID != ble_hrs_conn_handle_get())
        	{
                if(sensor_connect.state == SENSOR_CONNECT_STATE_DISCOVERY ||
                    sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)
                {
                    sensor_connect.state = SENSOR_CONNECT_STATE_CLOSE_WAIT;
                    sensor_connect_infor_set(sensor_type, &sensor_connect);
                }
        		sd_ble_gap_disconnect(ble_hrs_conn_handle_get(), BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
        	}
            break;
        case SENSOR_TYPE_CBSC:
        	if(BLE_CONN_HANDLE_INVALID != ble_cscs_conn_handle_get())
        	{
                if (sensor_connect.state == SENSOR_CONNECT_STATE_DISCOVERY ||
                    sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)
                {
                    sensor_connect.state = SENSOR_CONNECT_STATE_CLOSE_WAIT;
                    sensor_connect_infor_set(sensor_type, &sensor_connect);
                }
        		sd_ble_gap_disconnect(ble_cscs_conn_handle_get(), BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
        	}
            break;
        case SENSOR_TYPE_CAD:
        	if(BLE_CONN_HANDLE_INVALID != ble_cad_conn_handle_get())
        	{
                if (sensor_connect.state == SENSOR_CONNECT_STATE_DISCOVERY ||
                    sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)
                {
                    sensor_connect.state = SENSOR_CONNECT_STATE_CLOSE_WAIT;
                    sensor_connect_infor_set(sensor_type, &sensor_connect);
                }
        		sd_ble_gap_disconnect(ble_cad_conn_handle_get(), BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
        	}
            break;
        case SENSOR_TYPE_SPD:
        	if(BLE_CONN_HANDLE_INVALID != ble_spd_conn_handle_get())
        	{
                if (sensor_connect.state == SENSOR_CONNECT_STATE_DISCOVERY ||
                    sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)
                {
                    sensor_connect.state = SENSOR_CONNECT_STATE_CLOSE_WAIT;
                    sensor_connect_infor_set(sensor_type, &sensor_connect);
                }
        		sd_ble_gap_disconnect(ble_spd_conn_handle_get(), BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
        	}
            break;
        default:
            break;
    }
    // nrf_delay_ms(200);
}

//-------------------------------------------------------------------------------------------
// Function Name : timer_handler
// Purpose       : 定时器中断处理
// Param[in]     : void *p_context
// Param[out]    : None
// Return type   : static
// Comment       : 2020-08-31
//-------------------------------------------------------------------------------------------
#include "message_manager.h"
static bool ble_central_timer_handler(EventData* eventData)
{
    static uint16_t timeout_100ms = 0;

    timeout_100ms ++;
    if(timeout_100ms == 30){
        sensor_ble_connect_buffer_mailbox_timeout_check();
        timeout_100ms = 0;
    }

    ble_gap_connect_timeout_check();

    if(sensor_ble_connecting_handler_timeout())
    {
        sensor_ble_scan_start(SCAN_FILTER_UNREFRESH);
    }

    if(ble_scan_pending)
    {
        ble_scan_pending = false;
        sensor_ble_scan_start(SCAN_FILTER_CSCS_ENABLED | SCAN_FILTER_HRM_ENABLED | SCAN_FILTER_NUS_ENABLED);
    }

    return true;
}

/***************************************************************************/
extern void ant_ble_sensor_operation_submit(EventWorker work,void *data);
static void ble_central_timer_check(void *p_context)
{
    ant_ble_sensor_operation_submit(ble_central_timer_handler,NULL);
}
/***************************************************************************/
//-------------------------------------------------------------------------------------------
// Function Name : ble_center_timer_create
// Purpose       : 创建定时器，用于连接成功后定时检测是否发现服务，如果没有
//                   发现则断开重新连接
// Param[in]     : void
// Param[out]    : None
// Return type   : static
// Comment       : 2020-08-31
//-------------------------------------------------------------------------------------------
void ble_center_timer_create(void)
{
    ret_code_t err_code;

    gap_connect_timeout = 0;

    err_code = app_timer_create(&timer_id, APP_TIMER_MODE_REPEATED, ble_central_timer_check);
    APP_ERROR_CHECK(err_code);

    // err_code = app_timer_start(timer_id, TIMER_INTERVAL, NULL, "ble_central");
    // APP_ERROR_CHECK(err_code);
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_center_timer_start
// Purpose       : 启动定时器
// Param[in]     : void
// Param[out]    : None
// Return type   : static
// Comment       : 2020-08-31
//-------------------------------------------------------------------------------------------
void ble_center_timer_start(void)
{
    ret_code_t err_code;

    gap_connect_timeout = 0;

    err_code = app_timer_start(timer_id, TIMER_INTERVAL, NULL, "ble_central");
    APP_ERROR_CHECK(err_code);
}

/**
 * @*********************************************************************************************
 * @description: 停止定时器
 * @param {*}
 * @return {*}
 * @*********************************************************************************************
 */
void ble_center_timer_stop(void)
{
    ret_code_t err_code;

    err_code = app_timer_stop(timer_id);
    APP_ERROR_CHECK(err_code);

    sensor_ble_scan_stop();
    sensor_ble_connect_buffer_mailbox_clear();
    ble_scan_pending = false;
    ble_connect_retry = 0;
    ble_gap_connect_release();
}

void ble_center_sensor_clear(void)
{
    //reserved interface, no need to implement now.
}

/**
 * @*********************************************************************************************
 * @description: 搜索并连接标志设置，用于手动搜索时先搜索ANT，后搜索蓝牙
 * @param {uint8_t} flag
 * @return {*}
 * @*********************************************************************************************
 */
void ble_scan_and_connect_flag_set(uint8_t flag)
{
    scan_and_connect_flag = flag;
}

/**
 * @*********************************************************************************************
 * @description: 已经存在ble light_sensor
 * @param
 * @return
 * @*********************************************************************************************
 */
bool ble_light_adv_parse_process_found(sensor_search_infor_t* p_sensor_search_infor, uint8_array_t* p_advdata, uint8_array_t* p_parse)
{
    uint32_t err_code;
    bool ret = false;

    if ((SENSOR_SEARCH_MODE_MANUAL & sensor_search_mode_get())  //手动搜索
        && !ble_light_get_info_by_mac(p_sensor_search_infor->sensor_id.ble_mac_addr, NULL)) //没有找到对应mac的设备
    {
        sensor_display_infor_set(p_sensor_search_infor); //更新搜索到的传感器的状态
    }
    ret = true;

    //noxstella  先判断是不是1.0协议的灯
    err_code = adv_report_parse(BLE_GAP_AD_TYPE_COMPLETE_LOCAL_NAME, p_advdata, p_parse);
    if (err_code == NRF_SUCCESS)
    {
        for (uint8_t i = 0; i < sizeof(m_name_filter_list) / sizeof(m_name_filter_list[0]); i++) {
            if (!strncmp(m_name_filter_list[i], (const char*)p_parse->p_data, strlen(m_name_filter_list[i]))
                && (p_parse->size == strlen(m_name_filter_list[i])))
            {
                sensor_ble_device_type_set(p_sensor_search_infor->sensor_id.ble_mac_addr, DEV_TYPE_LIGHT_V1);
                break;
            }
        }
    }

    err_code = adv_report_parse(BLE_GAP_AD_TYPE_128BIT_SERVICE_UUID_COMPLETE, p_advdata, p_parse);
    if (NRF_SUCCESS == err_code)
    {
        ble_uuid128_t ble_light = IGS_LIGHT_UUID_BASE;
        if (memcmp(ble_light.uuid128, p_parse->p_data, p_parse->size) == 0) //二代协议车灯
        {
            sensor_ble_device_type_set(p_sensor_search_infor->sensor_id.ble_mac_addr, DEV_TYPE_LIGHT_V2);
        }
        else
        {
        }
    }
    return ret;
}

/**
 * @*********************************************************************************************
 * @description: 初次搜索ble light_sensor
 * @param
 * @return
 * @*********************************************************************************************
 */
bool ble_light_adv_parse_process_first(sensor_search_infor_t* p_sensor_search_infor, uint8_array_t* p_advdata, uint8_array_t* p_parse)
{
    uint32_t err_code;
    bool ret = false;

    //解析服务UUID
    err_code = adv_report_parse(BLE_GAP_AD_TYPE_16BIT_SERVICE_UUID_COMPLETE, p_advdata, p_parse);
    if (NRF_ERROR_NOT_FOUND == err_code)
    {
		err_code = adv_report_parse(BLE_GAP_AD_TYPE_16BIT_SERVICE_UUID_MORE_AVAILABLE, p_advdata, p_parse);
		if (NRF_ERROR_NOT_FOUND == err_code)
		{
            //需要先解析128位uuid,后续低协议版本的车灯升级的时候，会优先解uuid
            err_code = adv_report_parse(BLE_GAP_AD_TYPE_128BIT_SERVICE_UUID_COMPLETE, p_advdata, p_parse);
            if (NRF_SUCCESS == err_code)
            {
                ble_uuid128_t ble_light = IGS_LIGHT_UUID_BASE;
                if (memcmp(ble_light.uuid128, p_parse->p_data, p_parse->size) == 0) //二代协议车灯
                {
                    if (ser_user_config_blelight_periph_support())
                    {
                        err_code = adv_report_parse(BLE_GAP_AD_TYPE_COMPLETE_LOCAL_NAME, p_advdata, p_parse);
                        if (err_code == NRF_SUCCESS)
                        {
                            p_sensor_search_infor->sensor_type = SENSOR_TYPE_LIGHT;
                            memcpy(p_sensor_search_infor->ble_name, p_parse->p_data, p_parse->size);
                            sensor_ble_device_type_set(p_sensor_search_infor->sensor_id.ble_mac_addr, DEV_TYPE_LIGHT_V2);

                            if ((SENSOR_SEARCH_MODE_MANUAL & sensor_search_mode_get()) && !ble_light_get_info_by_mac(p_sensor_search_infor->sensor_id.ble_mac_addr, NULL))
                            {
                                sensor_display_infor_set(p_sensor_search_infor);
                            }
                            if (sensor_ble_connecting_handler_is_idle())
                            {
                                if (((sensor_ble_channel_is_register(p_sensor_search_infor->sensor_id.ble_mac_addr) && !sensor_ble_channel_status_is_busy(p_sensor_search_infor->sensor_id.ble_mac_addr)) ||
                                    (!sensor_ble_channel_is_register(p_sensor_search_infor->sensor_id.ble_mac_addr) && !sensor_ble_channel_is_full() && sensor_ble_connect_buffer_mailbox_query(p_sensor_search_infor->sensor_id.ble_mac_addr))))
                                {
                                    ret = true;
                                }
                            }
                        }
                    }
                }
                else {
                    //todo 蓝牙遥控器
                }
            }
            else
            {
                if (ser_user_config_blelight_periph_support())
                {
                    err_code = adv_report_parse(BLE_GAP_AD_TYPE_COMPLETE_LOCAL_NAME, p_advdata, p_parse);
                    if (NRF_SUCCESS == err_code)
                    {
                        uint8_t i = 0;
                        for (; i < sizeof(m_name_filter_list) / sizeof(m_name_filter_list[0]); i++)
                        {
                            if (!strncmp(m_name_filter_list[i], (const char*)p_parse->p_data, strlen(m_name_filter_list[i]))
                                && (p_parse->size == strlen(m_name_filter_list[i])))
                            {
                                p_sensor_search_infor->sensor_type = SENSOR_TYPE_LIGHT;
                                memcpy(p_sensor_search_infor->ble_name, p_parse->p_data, strlen(m_name_filter_list[i])); //蓝牙名称解析到传感器搜索列表里
                                sensor_ble_device_type_set(p_sensor_search_infor->sensor_id.ble_mac_addr, DEV_TYPE_LIGHT_V1);

                                // MODULE_PRINTF("[DEV_TYPE_LIGHT_V1] %s:%02x:%02x:%02x:%02x:%02x:%02x--%d\n", m_name_filter_list[i], p_sensor_search_infor->sensor_id.ble_mac_addr[0], p_sensor_search_infor->sensor_id.ble_mac_addr[1],
                                //     p_sensor_search_infor->sensor_id.ble_mac_addr[2],p_sensor_search_infor->sensor_id.ble_mac_addr[3],p_sensor_search_infor->sensor_id.ble_mac_addr[4],p_sensor_search_infor->sensor_id.ble_mac_addr[5], sensor_ble_connecting_handler_is_idle());

                                if ((SENSOR_SEARCH_MODE_MANUAL & sensor_search_mode_get()) && !ble_light_get_info_by_mac(p_sensor_search_infor->sensor_id.ble_mac_addr, NULL))
                                {
                                    sensor_display_infor_set(p_sensor_search_infor);
                                }

                                // MODULE_PRINTF("condition 1 = %d, (%d & %d)\n",sensor_ble_channel_is_register(p_sensor_search_infor->sensor_id.ble_mac_addr) && !sensor_ble_channel_status_is_busy(p_sensor_search_infor->sensor_id.ble_mac_addr),
                                //     sensor_ble_channel_is_register(p_sensor_search_infor->sensor_id.ble_mac_addr) , !sensor_ble_channel_status_is_busy(p_sensor_search_infor->sensor_id.ble_mac_addr));
                                // MODULE_PRINTF("condition 2 = %d, (%d & %d & %d)\n",(!sensor_ble_channel_is_register(p_sensor_search_infor->sensor_id.ble_mac_addr) && !sensor_ble_channel_is_full() && sensor_ble_connect_buffer_mailbox_query(p_sensor_search_infor->sensor_id.ble_mac_addr)),
                                //     !sensor_ble_channel_is_register(p_sensor_search_infor->sensor_id.ble_mac_addr) , !sensor_ble_channel_is_full() , sensor_ble_connect_buffer_mailbox_query(p_sensor_search_infor->sensor_id.ble_mac_addr));

                                if (sensor_ble_connecting_handler_is_idle())
                                {
                                    if (((sensor_ble_channel_is_register(p_sensor_search_infor->sensor_id.ble_mac_addr) && !sensor_ble_channel_status_is_busy(p_sensor_search_infor->sensor_id.ble_mac_addr)) ||
                                        (!sensor_ble_channel_is_register(p_sensor_search_infor->sensor_id.ble_mac_addr) && !sensor_ble_channel_is_full() && sensor_ble_connect_buffer_mailbox_query(p_sensor_search_infor->sensor_id.ble_mac_addr))))
                                    {
                                        ret = true;
                                        break;
                                    }
                                }
                                else
                                {
                                    break;
                                }
                            }
                        }
                        if (i == sizeof(m_name_filter_list) / sizeof(m_name_filter_list[0]))
                        {
                            return ret;
                        }
                    }
                    else
                    {
                        return ret;
                    }
                }
            }
        }
    }
    return ret;
}

// int ble_sensor_connect_state_get(sensor_type_t sensor_type, uint8_t *addr)
// {
//     ble_sensor_st *sensor = NULL;

//     switch(sensor_type)
//     {
//         case SENSOR_TYPE_HRM:
//             sensor = &ble_sensor_hrm;
//             break;
//         case SENSOR_TYPE_SPD:
//             sensor = &ble_sensor_spd;
//             break;
//         case SENSOR_TYPE_CAD:
//             sensor = &ble_sensor_cad;
//             break;
//         case SENSOR_TYPE_CBSC:
//             sensor = &ble_sensor_cscs;
//             break;
//         default:
//             return 0;
//     }

//     if (memcmp(sensor->dev_mac, addr, BLE_GAP_ADDR_LEN) == 0)
//     {
//         return sensor->state;
//     }
//     return 0;
// }

void ble_sensor_conn_handle_set(sensor_type_t type, uint16_t conn_handle)
{
    switch (type)
    {
    case SENSOR_TYPE_HRM:
        ble_hrs_conn_handle_set(conn_handle);
        break;
    case SENSOR_TYPE_CBSC:
        ble_cscs_conn_handle_set(conn_handle);
        break;
    case SENSOR_TYPE_CAD:
        ble_cad_conn_handle_set(conn_handle);
        break;
    case SENSOR_TYPE_SPD:
        ble_spd_conn_handle_set(conn_handle);
        break;
    default:
        break;
    }
}
