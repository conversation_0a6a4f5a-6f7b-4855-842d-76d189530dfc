/**
 * @Author: Mars
 * @Date: 2021-11-01 13:46:21
 * @LastEditTime: 2021-11-01 14:07:01
 * @LastEditors: Mars
 * @Description: 关机基础应用
 * @FilePath: \iGS630_App\Application\App\basic_app_module\poweroff_app.c
 * @ Copyright (C), 2021, <PERSON>han <PERSON> Technology Co., Ltd.
 */
/*********************
 *      INCLUDES
 *********************/
#include "ff.h"
#include "basic_app.h"
#include "stdint.h"
#include "cfg_header_def.h"
#include "factory_data.h"
#include "igs_dev_config.h"
/*********************
 *      DEFINES
 *********************/
#define INFOR_LNGTH 50
/*********************
 *      TYPEDEFS
 *********************/

/*********************
 *  STATIC PROTOTYPES
 *********************/

/*********************
 *  STATIC VARIABLES
 *********************/
static uint32_t s_factory_status = false;
/*********************
 *      MACROS
 *********************/

/*********************
 *   GLOBAL FUNCTIONS
 *********************/
void device_info_app(void)
{
    char dev_infor_str[INFOR_LNGTH];
	uint16_t BLE_VERSION = get_ble_soc_version();
    uint32_t br;
	FIL *fdst;
    fdst = ff_malloc(sizeof(FIL));
	if (NULL == fdst)
    {
        return;
    }

	DEBUG_PRINTF("文件%s不存在\r\n", DEVICE_BIN_FILE);
	if (FR_OK == f_open(fdst, DEVICE_BIN_FILE, FA_CREATE_ALWAYS | FA_WRITE | FA_READ)) //文件打开正确
	{
		memset(dev_infor_str, 0, INFOR_LNGTH);
		sprintf(dev_infor_str, "#PRO,%s,%s\r\n", DEVICE_NAME, MANUFACTURER_NAME);

		f_write(fdst, dev_infor_str, strlen(dev_infor_str), &br);

		memset(dev_infor_str, 0, INFOR_LNGTH);
		sprintf(dev_infor_str, "#HW,%d.%02d\r\n", HARDWARE_VERSION / 100, HARDWARE_VERSION % 100);
		f_write(fdst, dev_infor_str, strlen(dev_infor_str), &br);

		memset(dev_infor_str, 0, INFOR_LNGTH);
		sprintf(dev_infor_str, "#AW,%s\r\n", get_app_version_str());
		f_write(fdst, dev_infor_str, strlen(dev_infor_str), &br);

		memset(dev_infor_str, 0, INFOR_LNGTH);
		uint32_t t_boot_version = get_boot_version();
		sprintf(dev_infor_str, "#BOOT,%d.%d.%d.%d\r\n", (uint8_t)((t_boot_version >> 24) & 0xFF),(uint8_t)((t_boot_version >> 16) & 0xFF),(uint8_t)((t_boot_version >> 8) & 0xFF),(uint8_t)((t_boot_version) & 0xFF));
		f_write(fdst, dev_infor_str, strlen(dev_infor_str), &br);

		memset(dev_infor_str, 0, INFOR_LNGTH);
		sprintf(dev_infor_str, "#BLE,%d.%02d\r\n", BLE_VERSION / 100, BLE_VERSION % 100);
		f_write(fdst, dev_infor_str, strlen(dev_infor_str), &br);

		memset(dev_infor_str, 0, INFOR_LNGTH);
        #ifdef USE_SN_OLD
		if(0x00 != (get_serial_number_high() & 0xff000000))//使用的是芯片id
		{
			sprintf(dev_infor_str, "#ID,0x%x%x\r\n", get_serial_number_high(), get_serial_number_low());
		}else
		{
			sprintf(dev_infor_str, "#ID,0x%x%x\r\n", get_serial_number_high(), get_serial_number_low());//使用的是蓝牙写入
		}
        #else
        snprintf(dev_infor_str, 18, "#SN,%s\r\n", factory_data_get_sn());
        #endif
		f_write(fdst, dev_infor_str, strlen(dev_infor_str), &br);

		DEBUG_PRINTF("重新创建文件%s成功\r\n", DEVICE_INFOR_FILE);
		f_close(fdst);
	}
	else
	{
		DEBUG_PRINTF("XXXXXXXXXERR:InitDeviceInfro重新加载文件失败\r\n");
	}

	ff_free(fdst);
	return;
}

void device_factory_creat_app(void)
{
	if(!get_system_is_activation())
	{
		FIL *fp_check;
		fp_check = ff_malloc(sizeof(FIL));
		if (NULL == fp_check)
		{
			return;
		}

		f_open(fp_check, FCT_CHECK_FILE, FA_CREATE_ALWAYS | FA_WRITE | FA_READ);
		f_close(fp_check);
		ff_free(fp_check);
	}
	return;
}

void device_factory_check_app(void)
{
	FIL *fp_check;
    fp_check = ff_malloc(sizeof(FIL));
	if (NULL == fp_check)
    {
        return;
    }

    if (FR_OK == f_open(fp_check, FCT_CHECK_FILE, FA_READ))
    {
		s_factory_status = true;
		f_close(fp_check);
    }
	ff_free(fp_check);
	return;
}

uint32_t device_factory_status_get()
{
	return s_factory_status;
}
/*======================
 * Add/remove functions
 *=====================*/

/*======================
 * Setter functions
 *=====================*/

/*======================
 * Getter functions
 *=====================*/

/*======================
 * Other functions
 *=====================*/

/*********************
 *   STATIC FUNCTIONS
 *********************/
