/**
 * @file algo_service_altitude.c
 * <AUTHOR> (yang<PERSON><PERSON>@igpsport.com)
 * @brief 高度打点算法组件实现
 * @version 0.1
 * @date 2025-02-20
 *
 * @copyright Copyright (c) 2024-2025, <PERSON>han <PERSON>wu Technology Co., Ltd
 *
 */
#include "algo_service_altitude_dot.h"
#include "../../qwos/driver/sensor/baro/lps28dfw/lps28dfw.h"   //气压计
#include "../../qwos/driver/sensor/baro/qw_barosensor.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_task.h"
#include "qw_sensor.h"
#include "qw_time_util.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"
#include "utility.h"

static uint8_t s_cal_type = ALTITUDE_CALIBRATION_NOT;
// 发布数据
static algo_altitude_pub_t s_algo_out = {0x7fffffff, 0x7fffffff};
static uint8_t altitudecount = 0;

static void algo_altitude_in_callback(const void *out, uint32_t len);

/**
 * @brief 1min到数据算法输入处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_one_min_reached_in_callback(const void *in, uint32_t len)
{
    // 输入数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_ALTITUDE_DOT;
    head.input_type = DATA_ID_EVENT_ONE_MIN_REACH;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_click_alt_cal_in_callback(const void *in, uint32_t len)
{
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_ALTITUDE_DOT;
    head.input_type = DATA_ID_EVENT_ALTITUDE_CAL;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_altitude_dot_out_callback(const void *out, uint32_t len)
{
    // 算法输出后发布或者2次处理
    if (qw_dataserver_publish_id(DATA_ID_ALGO_ALTITUDE_DOT, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("%s publish error", __FUNCTION__);
    }
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node_alt[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_altitude",
        .topic_id = DATA_ID_ALGO_ALTITUDE,
        .callback = algo_altitude_in_callback,
    },
};

/**
 * @brief 算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_altitude_in_callback(const void *out, uint32_t len)
{
    const algo_altitude_pub_t *algo_altitude = (algo_altitude_pub_t *) out;
    if ((algo_altitude->altitude_show != 0x7fffffff || algo_altitude->altitude != 0x7fffffff) && algo_altitude->pressure > 0)
    {
        s_algo_out.altitude = algo_altitude->altitude;
        s_algo_out.altitude_show = algo_altitude->altitude_show;
        s_algo_out.calibrated = algo_altitude->calibrated;
        s_algo_out.pressure = algo_altitude->pressure;

        if ((algo_altitude->calibrated == 0 && altitudecount < 180) || (algo_altitude->calibrated == 1 && altitudecount < 10))
        {
            altitudecount++;
        }
        else
        {
            algo_topic_list_unsubscribe(s_algo_topic_node_alt, sizeof(s_algo_topic_node_alt) / sizeof(s_algo_topic_node_alt[0]));

            //数据发布
            algo_altitude_dot_out_callback(&s_algo_out, sizeof(algo_altitude_pub_t));
            altitudecount = 0;
        }
    }
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_ONE_MINUTE,
        .topic_id = DATA_ID_EVENT_ONE_MIN_REACH,
        .callback = algo_one_min_reached_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_CLICK_ALT,
        .topic_id = DATA_ID_EVENT_ALTITUDE_CAL,
        .callback = algo_click_alt_cal_in_callback,
    },
};

/**
 * @brief 算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_altitude_dot_init(void)
{
    return 0;
}

/**
 * @brief 打开算法
 *
 * @return int32_t 结果
 */
static int32_t algo_altitude_dot_open(void)
{
    return algo_topic_list_subscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
}

/**
 * @brief feed算法
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_altitude_dot_feed(uint32_t input_type, void *data, uint32_t len)
{
    int ret = 0;

    if (DATA_ID_EVENT_ONE_MIN_REACH == input_type && data != NULL)
    {
        struct tm tm_info = {0};
        utc_to_localtime(get_sec_from_rtc(), &tm_info);
        if (tm_info.tm_min % 10 == 9)
        {
            ret = algo_topic_list_subscribe(s_algo_topic_node_alt, sizeof(s_algo_topic_node_alt) / sizeof(s_algo_topic_node_alt[0]));
            if (ret != 0)
            {
                return ret;
            }
        }
    }
    else if (DATA_ID_EVENT_ALTITUDE_CAL == input_type && data != NULL)
    {
        gui_altitude_calibration_t *click_alt = (gui_altitude_calibration_t *) data;
        s_cal_type = click_alt->event_type;
        optional_config_t config = {
            .sampling_rate = 0,
        };
        if (click_alt->event_type == ALTITUDE_CALIBRATION_CANCEL)
        {
            algo_topic_list_unsubscribe(s_algo_topic_node_alt, sizeof(s_algo_topic_node_alt) / sizeof(s_algo_topic_node_alt[0]));
        }
        else if (click_alt->event_type == ALTITUDE_CALIBRATION_GPS || click_alt->event_type == ALTITUDE_CALIBRATION_USER)
        {
            ret = algo_topic_list_subscribe(s_algo_topic_node_alt, sizeof(s_algo_topic_node_alt) / sizeof(s_algo_topic_node_alt[0]));
            if (ret != 0)
            {
                return ret;
            }
        }

        algo_svr_head_t head;
        head.cid = ALGO_CMD_ID_FEED;
        head.algo_type = ALGO_TYPE_ALTITUDE;
        head.input_type = DATA_ID_EVENT_ALTITUDE_CAL;
        if (send_msg_to_algo_fwk(head, click_alt, len) != 0)
        {
            ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
        }
    }

    return ret;
}

/**
 * @brief 关闭算法
 *
 * @return int32_t 结果
 */
static int32_t algo_altitude_dot_close(void)
{
    algo_topic_list_unsubscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
    return 0;
}

// 算法组件实现
static algo_compent_ops_t s_altitude_dot_algo = {
    .init = algo_altitude_dot_init,
    .open = algo_altitude_dot_open,
    .feed = algo_altitude_dot_feed,
    .close = algo_altitude_dot_close,
    .ioctl = NULL,
};

/**
 * @brief 组件注册
 *
 * @return int32_t 结果
 */
int32_t register_altitude_dot_algo(void)
{
    algo_compnent_register(ALGO_TYPE_ALTITUDE_DOT, &s_altitude_dot_algo);
    return 0;
}
