﻿/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   compass_algorithm_v2.c
@Time    :   2025/08/22
@Description: 电子罗盘V2：集成陀螺仪进行姿态补偿，优化航向精度。
*
**************************************************************************/
#include "compass_algorithm.h"
#include "basic_app.h"
#include "basictype.h"
#include "igs_dev_config.h"
#include "cfg_header_def.h"
#include "task_thread_helper.h"
#include <rtthread.h>
#include <stdint.h>
#include <string.h>
#include <math.h>
#ifdef IGS_DEV
#include "arm_math.h"
#include "qw_circbuf.h"
#include "qw_fs.h"
#include "qw_sensor.h"
#include "qw_time_util.h"
#include "subscribe_data.h"
#include "subscribe_service.h"

/****************************************************************************
*Pre-processor Definitions
****************************************************************************/

/****************************************************************************
COMPASS_ALGO_SKIP_CALIBRATION 仅测试干扰时能用
忽略校准过程,只显示角度值,
正式使用时需要有校准过程,要undef COMPASS_ALGO_SKIP_CALIBRATION.
****************************************************************************/
// #define COMPASS_ALGO_SKIP_CALIBRATION 1

#define MAG_EIGEN_POINTS_MIN_NUM 32   //地磁数据特征点最小数量
//#define MAG_EIGEN_POINTS_MIN_DIST               16000000        //地磁数据各特征点之间最小距离（欧氏距离的平方）
#define MAG_EIGEN_POINTS_MIN_DIST \
    5000000               //地磁数据各特征点之间最小距离（欧氏距离的平方）
#define MAG_SCALE 16384   //地磁读数到实际值之间的比值
#define EARTH_MAG_AVG_SCALE \
    8192                  //地磁场在0.4~0.6 Gauss之间，取平均0.5 Gauss，其对应的读数转换比

#define FLOAT_ZERO_APPROXIMATION 1.0e-5   //浮点数小于该值时认为为0
#define MAG_MA_FILTER_ORDER      16       //地磁滑动平均滤波阶数
#define MAG_CALIB_FRAME_TICK     100      //地磁校准采样周期
#define MAG_COURSE_FRAME_TICK    100      //地磁航向采样周期
#define SENSOR_QUEUE_SIZE        100      //acc && mag sensor queue cache buff size.
#undef FORCE_CALIBRATE_EVERY_TIME         //定义此宏,则每次都会进行校准

// 融合算法相关宏定义
#define COMPASS_FUSION_PI                   3.14159265358979323846f
#define COMPASS_FUSION_RAD_TO_DEG          (180.0f / COMPASS_FUSION_PI)
#define COMPASS_FUSION_DEG_TO_RAD          (COMPASS_FUSION_PI / 180.0f)
#define COMPASS_FUSION_GYRO_SCALE          (180.0f / COMPASS_FUSION_PI)  // 陀螺仪数据缩放因子 (rad/s to deg/s)
#define COMPASS_FUSION_MAX_ANGLE_DIFF      180.0f           // 最大角度差
#define COMPASS_FUSION_BIAS_SAMPLES        100              // 偏置估计样本数
#define COMPASS_FUSION_STABILIZE_TIME      3000             // 陀螺仪稳定时间 (ms) - 3秒
#define CALIBRATION_TEST 0
#if CALIBRATION_TEST
#define CALIBRATION_TEST_RES "0:/EC_CALIB"
#endif
#undef COMPASS_ALGO_CAPTURE
#ifdef COMPASS_ALGO_CAPTURE
#define COMPASS_CAPTURE_FILE_END_FIX "_compass.csv"
#define COMPASS_CAPTURE_FILE_HEAD_LINE                                                   \
    "LocalTime(S),EventTimestamp(ms),FeedCnt,ACC_x,ACC_y,ACC_z,MAG_x,MAG_y,MAG_z,State," \
    "Angle"
static QW_FIL *capture_fp = NULL;
static char capture_buff[512];
#endif


/****************************************************************************
*Private Types
****************************************************************************/
//（磁、加速度）传感器数据结构体
typedef struct
{
    int16_t x;
    int16_t y;
    int16_t z;
} sensor_data_t;

// 融合算法相关数据结构
typedef struct {
    float x;                // 绕X轴角速度 (rad/s)
    float y;                // 绕Y轴角速度 (rad/s)
    float z;                // 绕Z轴角速度 (rad/s)
    uint64_t timestamp;     // 时间戳 (微秒)
} gyro_data_t;

typedef struct {
    float yaw_gyro;          // 陀螺仪积分得到的偏航角 (度)
    float yaw_mag;           // 地磁计算得到的偏航角 (度)
    float yaw_fused;         // 融合后的偏航角 (度)
    float gyro_bias_z;       // Z轴陀螺仪偏置 (dps)
    uint64_t last_timestamp; // 上次更新时间戳 (ms)
    bool initialized;        // 初始化标志
    uint32_t sample_count;   // 样本计数
    float bias_sum;          // 偏置累积和

    // 陀螺仪稳定期相关
    bool gyro_ready;         // 陀螺仪准备就绪标志
    uint32_t stabilize_start_time;  // 稳定期开始时间
} compass_fusion_state_t;

typedef struct {
    float gyro_weight;              // 陀螺仪权重 (0.0-1.0)
    float mag_weight;               // 地磁权重 (0.0-1.0)
    float bias_alpha;               // 偏置估计滤波系数 (0.0-1.0)
    float fusion_alpha;             // 融合滤波系数 (0.0-1.0)
    float gyro_threshold;           // 陀螺仪静止阈值 (dps)
    uint32_t bias_update_interval;  // 偏置更新间隔 (ms)
} compass_fusion_config_t;

typedef struct
{
    sensor_data_t p[MAG_EIGEN_POINTS_MIN_NUM];
    uint16_t num;
} mag_eigen_points_t;

//椭球拟合中间结果矩阵的值的结构体
typedef struct
{
    float64_t K[MAG_EIGEN_POINTS_MIN_NUM * 9];
    float64_t Y[MAG_EIGEN_POINTS_MIN_NUM];
    float64_t KT[9 * MAG_EIGEN_POINTS_MIN_NUM];
    float64_t KTK[9 * 9];
    float64_t KTKI[9 * 9];
    float64_t KTKIKT[9 * MAG_EIGEN_POINTS_MIN_NUM];
    float64_t KTKIKTY[9];
} ellipsoid_fit_mat_value_t;

//椭球拟合中间结果矩阵结构体
typedef struct
{
    arm_matrix_instance_f64 K;
    arm_matrix_instance_f64 Y;
    arm_matrix_instance_f64 KT;
    arm_matrix_instance_f64 KTK;
    arm_matrix_instance_f64 KTKI;
    arm_matrix_instance_f64 KTKIKT;
    arm_matrix_instance_f64 KTKIKTY;
} ellipsoid_fit_mat_t;

//椭球校准中间结果的值的结构体
typedef struct
{
    float64_t E[3 * 3];
    float64_t EI[3 * 3];
    float64_t F[3];
    float64_t ET[3 * 3];
    float64_t ETE[3 * 3];
    float64_t U[3 * 3];
    float64_t S[3 * 3];
    float64_t V[3 * 3];
    float64_t EV[3 * 3];
    float64_t VT[3 * 3];
    float64_t US[3 * 3];
} ellipsoid_calib_mat_value_t;

//椭球校准中间结果结构体
typedef struct
{
    arm_matrix_instance_f64 E;
    arm_matrix_instance_f64 EI;
    arm_matrix_instance_f64 F;
    arm_matrix_instance_f64 ET;
    arm_matrix_instance_f64 ETE;
    arm_matrix_instance_f64 U;
    arm_matrix_instance_f64 S;
    arm_matrix_instance_f64 V;
    arm_matrix_instance_f64 EV;
    arm_matrix_instance_f64 VT;
    arm_matrix_instance_f64 US;
} ellipsoid_calib_mat_t;

//地磁校正矩阵结构体
typedef struct
{
    arm_matrix_instance_f64 soft;
    arm_matrix_instance_f64 hard;
} mag_calib_mat_t;

//3x3矩阵特征分解元素（特征值和其特征向量）
typedef struct
{
    float64_t eval;      //特征值
    float64_t evec[3];   //特征向量
} mat_3x3_eigen_elem_t;

//3x3矩阵特征值分解
typedef struct
{
    mat_3x3_eigen_elem_t elem1;
    mat_3x3_eigen_elem_t elem2;
    mat_3x3_eigen_elem_t elem3;
} mat_3x3_eigendecomposition_t;

//平面向量结构体
typedef struct
{
    int16_t x;
    int16_t y;
} plane_vector_t;

typedef struct
{
    int16_t yaw;
    int16_t roll;
    int16_t pitch;
} attitude_t;

/****************************************************************************
*Extern  Functions
****************************************************************************/
extern arm_status arm_mat_mult_f64(const arm_matrix_instance_f64 *pSrcA,
                                   const arm_matrix_instance_f64 *pSrcB,
                                   arm_matrix_instance_f64 *pDst);
extern arm_status arm_mat_trans_f64(const arm_matrix_instance_f64 *pSrc,
                                    arm_matrix_instance_f64 *pDst);

/****************************************************************************
*Private varible
****************************************************************************/
static cfg_mag_calib_t *p_mag_calib = NULL;//外部传入的校准参数,可以从文件中读取参数.
static uint16_t s_yaw_angle = 0x7fff;

static uint8_t s_start_calibrate = false;
static uint8_t s_stop_calibrate = false;
static ecompass_status_t s_ecompass_status = enumEC_NO_CALIBRATE;
// static uint8_t s_ecompass_fail = 0;

static mag_eigen_points_t *sp_mag_eigen_points = NULL;
static ellipsoid_fit_mat_t *sp_ellipsoid_fit_mat = NULL;
static ellipsoid_fit_mat_value_t *sp_ellipsoid_fit_mat_value = NULL;
static ellipsoid_calib_mat_t *sp_ellipsoid_calib_mat = NULL;
static ellipsoid_calib_mat_value_t *sp_ellipsoid_calib_mat_value = NULL;
static mag_calib_mat_t *sp_mag_calib_mat = NULL;
static mat_3x3_eigendecomposition_t *sp_mag_coff_mat_decomposition = NULL;

static sensor_data_t s_mag = {0, 0, 0};
static sensor_data_t s_acc = {0, 0, 0};
static sports_data_t *g_p_mag_course_data = NULL;
static void *alg_res_data = NULL;
static struct sensor_mag g_mag_data = {0};
static struct sensor_accel g_accel_data = {0};
static struct sensor_gyro g_gyro_data = {0};  // 陀螺仪数据
static ecompass_listener_t *ecompass_listener = NULL;
static struct rt_mutex sensor_acc_lock; /* Manages exclusive access && mag for compass algo */
static struct rt_mutex sensor_mag_lock; /* Manages exclusive access && mag for compass algo */
static struct rt_semaphore compass_algo_sem;   // 传感器数据信号量
static struct rt_thread compass_algo_thread;
static bool ecompass_algo_init_flag = false;   //算法初始化标记.
static rt_uint8_t compass_algo_stack[8192];
static uint32_t s_mag_data_cnt = 0;            //统计收到的地磁个数.
static uint32_t s_acc_data_cnt = 0;            //统计收到的acc个数.
static uint32_t s_feed_cnt = 0;   //统计输入次数,即acc和地磁时间戳能对齐的个数.
static bool is_running = false;   //算法运行状态.

// 替换Nordic队列为思澈平台原生循环缓冲区
typedef struct {
    struct circbuf_s circbuf;
    struct rt_mutex  mutex;
    uint8_t buffer[SENSOR_QUEUE_SIZE * sizeof(struct sensor_accel)];
    bool initialized;
} sensor_accel_queue_t;

typedef struct {
    struct circbuf_s circbuf;
    struct rt_mutex  mutex;
    uint8_t buffer[SENSOR_QUEUE_SIZE * sizeof(struct sensor_mag)];
    bool initialized;
} sensor_mag_queue_t;

static sensor_accel_queue_t accel_queue = {0};
static sensor_mag_queue_t mag_queue = {0};

// 陀螺仪队列定义
typedef struct {
    struct circbuf_s circbuf;
    struct rt_mutex  mutex;
    uint8_t buffer[SENSOR_QUEUE_SIZE * 32];  // 使用固定大小避免编译时计算
    bool initialized;
} sensor_gyro_queue_t;

static sensor_gyro_queue_t gyro_queue = {0};

// 融合算法相关变量
static bool s_fusion_enabled = true;                    // 融合算法使能标志
static uint32_t s_gyro_data_cnt = 0;                   // 陀螺仪数据计数
static compass_fusion_config_t s_fusion_config = {0};   // 融合算法配置
static compass_fusion_state_t s_fusion_state = {0};     // 融合算法状态
static bool s_fusion_initialized = false;               // 融合算法初始化标志

/****************************************************************************
*Private  Functions
****************************************************************************/

// 融合算法函数声明
static float normalize_angle(float angle);
static float angle_difference(float angle1, float angle2);
static float complementary_filter(float gyro_angle, float mag_angle, float alpha);
static void update_gyro_bias(float gyro_z);
void compass_fusion_get_default_config(compass_fusion_config_t *config);
int compass_fusion_init(const compass_fusion_config_t *config);
void compass_fusion_deinit(void);
int compass_fusion_update_gyro(const gyro_data_t *gyro);
float compass_fusion_update_mag(float mag_angle);
void compass_fusion_reset(void);
static inline uint64_t get_diff_by_unsigned(uint64_t x1, uint64_t x2)
{
    if (x1 > x2)
    {
        return x1 - x2;
    }
    else if (x1 < x2)
    {
        return x2 - x1;
    }
    else
    {
        return 0;
    }
}

// 队列初始化函数
static int sensor_queue_init(void)
{
    int ret = 0;

    // 初始化加速度计队列
    if (!accel_queue.initialized) {
        ret = circbuf_init(&accel_queue.circbuf, accel_queue.buffer, sizeof(accel_queue.buffer));
        if (ret < 0) {
            BASIC_APP_LOG_E("accel queue circbuf init failed: %d", ret);
            return ret;
        }
        rt_mutex_init(&accel_queue.mutex, "accel_queue", RT_IPC_FLAG_FIFO);
        accel_queue.initialized = true;
    }

    // 初始化磁力计队列
    if (!mag_queue.initialized) {
        ret = circbuf_init(&mag_queue.circbuf, mag_queue.buffer, sizeof(mag_queue.buffer));
        if (ret < 0) {
            BASIC_APP_LOG_E("mag queue circbuf init failed: %d", ret);
            rt_mutex_detach(&accel_queue.mutex);
            return ret;
        }
        rt_mutex_init(&mag_queue.mutex, "mag_queue", RT_IPC_FLAG_FIFO);
        mag_queue.initialized = true;
    }

    // 初始化陀螺仪队列
    if (!gyro_queue.initialized) {
        ret = circbuf_init(&gyro_queue.circbuf, gyro_queue.buffer, sizeof(gyro_queue.buffer));
        if (ret < 0) {
            BASIC_APP_LOG_E("gyro queue circbuf init failed: %d", ret);
            rt_mutex_detach(&accel_queue.mutex);
            rt_mutex_detach(&mag_queue.mutex);
            return ret;
        }
        rt_mutex_init(&gyro_queue.mutex, "gyro_queue", RT_IPC_FLAG_FIFO);
        gyro_queue.initialized = true;
    }

    return 0;
}

// 队列反初始化函数
static void sensor_queue_uninit(void)
{
    if (accel_queue.initialized) {
        rt_mutex_detach(&accel_queue.mutex);
        circbuf_uninit(&accel_queue.circbuf);
        accel_queue.initialized = false;
    }

    if (mag_queue.initialized) {
        rt_mutex_detach(&mag_queue.mutex);
        circbuf_uninit(&mag_queue.circbuf);
        mag_queue.initialized = false;
    }

    if (gyro_queue.initialized) {
        rt_mutex_detach(&gyro_queue.mutex);
        circbuf_uninit(&gyro_queue.circbuf);
        gyro_queue.initialized = false;
    }
}

#ifdef COMPASS_ALGO_CAPTURE
static bool write_capture_file(const char *fmt, ...)
{
    uint32_t real_wsize = 0;
    uint32_t req_wize = 0;
    int ret = 0;
    if (!capture_fp)
        return false;
    va_list ap;
    va_start(ap, fmt);
    vsnprintf(capture_buff, sizeof(capture_buff), fmt, ap);
    req_wize = strlen(capture_buff) + 1;

    ret = qw_f_write(capture_fp, capture_buff, req_wize, &real_wsize);
    if (0 != ret || req_wize != real_wsize)
    {
        BASIC_APP_LOG_E("compass capture file wr:[%d,%d] @%d", req_wize, real_wsize, ret);
        return false;
    }
    //qw_f_sync(capture_fp);
    return true;
}

static bool open_capture_file(void)
{
    int ret = 0;
    char file_name[128] = {0};
    struct tm local_tm;
    utc_to_localtime(time(NULL), &local_tm);
    sprintf(file_name, "%04d%02d%02d_%02d%02d%02d%s", 1900 + local_tm.tm_year,
            local_tm.tm_mon + 1, local_tm.tm_mday, local_tm.tm_hour, local_tm.tm_min,
            local_tm.tm_sec, COMPASS_CAPTURE_FILE_END_FIX);
    ret = qw_f_open(&capture_fp, file_name,
                    QW_FA_OPEN_ALWAYS | QW_FA_WRITE | QW_FA_APPEND);
    if (0 != ret)
    {
        BASIC_APP_LOG_E("open %s fail ret %d\n", file_name, ret);
        return false;
    }
    return write_capture_file("%s\n", COMPASS_CAPTURE_FILE_HEAD_LINE);
}

static void close_capture_file(void)
{
    if (NULL != capture_fp)
    {
        qw_f_sync(capture_fp);
        qw_f_close(capture_fp);
    }
}
#endif

/************************************************************************
 *@function:static bool alg_compass_process(sensor_data_t* mag, sensor_data_t* accel,
                                void** alg_res_data);
 *@brief:地磁算法处理函数.
 *@param:mag:地磁传感器数据,单位Gauss, accel:acc数据,单位mg
 *@return:计算逻辑正确返回true,否则返回false.
*************************************************************************/
static bool alg_compass_process(sensor_data_t *mag, sensor_data_t *accel,
                                void **alg_res_data);

static int mag_calib_tmp_results_init(void)
{
    int ret = 0;
    BASIC_APP_LOG_I("into mag_calib_tmp_results_init");
    sp_mag_eigen_points = (mag_eigen_points_t *) MY_MALLOC(sizeof(mag_eigen_points_t));
    if (sp_mag_eigen_points == NULL)
    {
        goto error;
    }
    memset(sp_mag_eigen_points, 0, sizeof(mag_eigen_points_t));

    sp_ellipsoid_fit_mat = (ellipsoid_fit_mat_t *) MY_MALLOC(sizeof(ellipsoid_fit_mat_t));
    if (sp_ellipsoid_fit_mat == NULL)
    {
        goto clean_eigen_points;
    }
    memset(sp_ellipsoid_fit_mat, 0, sizeof(ellipsoid_fit_mat_t));

    sp_ellipsoid_fit_mat_value = (ellipsoid_fit_mat_value_t *) MY_MALLOC(
        sizeof(ellipsoid_fit_mat_value_t));
    if (sp_ellipsoid_fit_mat_value == NULL)
    {
        goto clean_ellipsoid_fit_mat;
    }
    memset(sp_ellipsoid_fit_mat_value, 0, sizeof(ellipsoid_fit_mat_value_t));

    sp_ellipsoid_calib_mat = (ellipsoid_calib_mat_t *) MY_MALLOC(
        sizeof(ellipsoid_calib_mat_t));
    if (sp_ellipsoid_calib_mat == NULL)
    {
        goto clean_ellipsoid_fit_mat_value;
    }
    memset(sp_ellipsoid_calib_mat, 0, sizeof(ellipsoid_calib_mat_t));

    sp_ellipsoid_calib_mat_value = (ellipsoid_calib_mat_value_t *) MY_MALLOC(
        sizeof(ellipsoid_calib_mat_value_t));
    if (sp_ellipsoid_calib_mat_value == NULL)
    {
        goto clean_ellipsoid_calib_mat;
    }
    memset(sp_ellipsoid_calib_mat_value, 0, sizeof(ellipsoid_calib_mat_value_t));

    sp_mag_calib_mat = (mag_calib_mat_t *) MY_MALLOC(sizeof(mag_calib_mat_t));
    if (sp_mag_calib_mat == NULL)
    {
        goto clean_ellipsoid_calib_mat_value;
    }
    memset(sp_mag_calib_mat, 0, sizeof(mag_calib_mat_t));

    sp_mag_coff_mat_decomposition = (mat_3x3_eigendecomposition_t *) MY_MALLOC(
        sizeof(mat_3x3_eigendecomposition_t));
    if (sp_mag_coff_mat_decomposition == NULL)
    {
        goto clean_mag_calib_mat;
    }
    memset(sp_mag_coff_mat_decomposition, 0, sizeof(mat_3x3_eigendecomposition_t));

    return ret;

clean_mag_calib_mat:
    MY_FREE(sp_mag_calib_mat);
    sp_mag_calib_mat = NULL;

clean_ellipsoid_calib_mat_value:
    MY_FREE(sp_ellipsoid_calib_mat_value);
    sp_ellipsoid_calib_mat_value = NULL;

clean_ellipsoid_calib_mat:
    MY_FREE(sp_ellipsoid_calib_mat);
    sp_ellipsoid_calib_mat = NULL;

clean_ellipsoid_fit_mat_value:
    MY_FREE(sp_ellipsoid_fit_mat_value);
    sp_ellipsoid_fit_mat_value = NULL;

clean_ellipsoid_fit_mat:
    MY_FREE(sp_ellipsoid_fit_mat);
    sp_ellipsoid_fit_mat = NULL;

clean_eigen_points:
    MY_FREE(sp_mag_eigen_points);
    sp_mag_eigen_points = NULL;

error:
    ret = -1;
    return ret;
}

static void mag_calib_tmp_results_clean(void)
{
    BASIC_APP_LOG_I("into mag_calib_tmp_results_clean");
    if (sp_mag_coff_mat_decomposition)
    {
        MY_FREE(sp_mag_coff_mat_decomposition);
        sp_mag_coff_mat_decomposition = NULL;
    }
    if (sp_mag_calib_mat)
    {
        MY_FREE(sp_mag_calib_mat);
        sp_mag_calib_mat = NULL;
    }
    if (sp_ellipsoid_calib_mat_value)
    {
        MY_FREE(sp_ellipsoid_calib_mat_value);
        sp_ellipsoid_calib_mat_value = NULL;
    }
    if (sp_ellipsoid_calib_mat)
    {
        MY_FREE(sp_ellipsoid_calib_mat);
        sp_ellipsoid_calib_mat = NULL;
    }
    if (sp_ellipsoid_fit_mat_value)
    {
        MY_FREE(sp_ellipsoid_fit_mat_value);
        sp_ellipsoid_fit_mat_value = NULL;
    }
    if (sp_ellipsoid_fit_mat)
    {
        MY_FREE(sp_ellipsoid_fit_mat);
        sp_ellipsoid_fit_mat = NULL;
    }
    if (sp_mag_eigen_points)
    {
        MY_FREE(sp_mag_eigen_points);
        sp_mag_eigen_points = NULL;
    }
}

/**
 * *********************************************************************************************
 * @name: mag_dist_calc
 * @brief: 计算两点之间的距离（欧氏距离的平方）
 * @param {sensor_data_t} *p1 - 第一个点
 * @param {sensor_data_t} *p2 - 第二个点
 * @return {*} 距离
 * *********************************************************************************************
 */
static uint32_t mag_dist_calc(sensor_data_t *p1, sensor_data_t *p2)
{
    uint32_t dist = 0;
    int32_t dx = p1->x - p2->x;
    int32_t dy = p1->y - p2->y;
    int32_t dz = p1->z - p2->z;
    dist = dx * dx + dy * dy + dz * dz;
    return dist;
}

/**
 * *********************************************************************************************
 * @name: mag_eigen_point_push
 * @brief: 保存地磁特征点
 * @param {mag_eigen_points_t} *eps - 已有地磁特征点
 * @param {sensor_data_t} *p - 当前地磁数据
 * @return {*}
 * *********************************************************************************************
 */
static void mag_eigen_point_push(mag_eigen_points_t *eps, sensor_data_t *p)
{
    uint32_t idx = eps->num;
    eps->p[idx].x = p->x;
    eps->p[idx].y = p->y;
    eps->p[idx].z = p->z;
    eps->num++;
}

/**
 * *********************************************************************************************
 * @name: mag_eigen_point_check
 * @brief: 地磁数据特征点选取，用于椭球拟合
 * @param {mag_eigen_points_t} *eps - 已有地磁特征点
 * @param {sensor_data_t} *cur_p - 当前地磁数据
 * @return {*} true - 已获取足够数量特征点 false - 尚未获得足够特征点
 * *********************************************************************************************
 */
static int mag_eigen_point_check(mag_eigen_points_t *eps, sensor_data_t *cur_p)
{
    int ret = false;

    if (eps->num < MAG_EIGEN_POINTS_MIN_NUM)
    {
        int is_valid = true;
        for (int i = 0; i < eps->num; i++)
        {
            uint32_t dist = mag_dist_calc(eps->p + i, cur_p);
            if (dist < MAG_EIGEN_POINTS_MIN_DIST)
            {
                is_valid = false;
                break;
            }
        }
        if (is_valid == true)
        {
            mag_eigen_point_push(eps, cur_p);
        }
    }

    if (eps->num >= MAG_EIGEN_POINTS_MIN_NUM)
    {
        ret = true;
    }

    return ret;
}

/**
 * *********************************************************************************************
 * @name: ellipsoid_fit_mat_init
 * @brief: 椭球拟合中间结果矩阵初始化
 * @param {ellipsoid_fit_mat_t} *mat - 中间结果矩阵
 * @param {ellipsoid_fit_mat_value_t} *value - 中间结果矩阵的值
 * @return {*}
 * *********************************************************************************************
 */
static void ellipsoid_fit_mat_init(ellipsoid_fit_mat_t *mat,
                                   ellipsoid_fit_mat_value_t *value)
{
    mat->K.numRows = MAG_EIGEN_POINTS_MIN_NUM;
    mat->K.numCols = 9;
    mat->K.pData = value->K;

    mat->Y.numRows = MAG_EIGEN_POINTS_MIN_NUM;
    mat->Y.numCols = 1;
    mat->Y.pData = value->Y;

    mat->KT.numRows = 9;
    mat->KT.numCols = MAG_EIGEN_POINTS_MIN_NUM;
    mat->KT.pData = value->KT;

    mat->KTK.numRows = mat->KTK.numCols = 9;
    mat->KTK.pData = value->KTK;

    mat->KTKI.numRows = mat->KTKI.numCols = 9;
    mat->KTKI.pData = value->KTKI;

    mat->KTKIKT.numRows = 9;
    mat->KTKIKT.numCols = MAG_EIGEN_POINTS_MIN_NUM;
    mat->KTKIKT.pData = value->KTKIKT;

    mat->KTKIKTY.numRows = 9;
    mat->KTKIKTY.numCols = 1;
    mat->KTKIKTY.pData = value->KTKIKTY;
}

/**
 * *********************************************************************************************
 * @name: ellipsoid_fit_mat_init_value_calc
 * @brief: 椭球拟合矩阵初始值计算
 * @param {sensor_data_t} *mag - 地磁原始数据
 * @param {float64_t} *mat - 矩阵
 * @return {*}
 * *********************************************************************************************
 */
static void ellipsoid_fit_mat_init_value_calc(sensor_data_t *mag, float64_t *mat)
{
    const uint32_t SCALE_POW2 = 268435456;   //16384**2

    int16_t x = mag->x;
    int16_t y = mag->y;
    int16_t z = mag->z;

    *(mat) = (float64_t) (x * x) / (float64_t) (SCALE_POW2);
    *(mat + 1) = (float64_t) (y * y) / (float64_t) (SCALE_POW2);
    *(mat + 2) = (float64_t) (z * z) / (float64_t) (SCALE_POW2);
    *(mat + 3) = (float64_t) (2 * x * y) / (float64_t) (SCALE_POW2);
    *(mat + 4) = (float64_t) (2 * x * z) / (float64_t) (SCALE_POW2);
    *(mat + 5) = (float64_t) (2 * y * z) / (float64_t) (SCALE_POW2);
    *(mat + 6) = (float64_t) (2 * x) / (float64_t) (MAG_SCALE);
    *(mat + 7) = (float64_t) (2 * y) / (float64_t) (MAG_SCALE);
    *(mat + 8) = (float64_t) (2 * z) / (float64_t) (MAG_SCALE);
}

/**
 * *********************************************************************************************
 * @name: ellipsoid_fit_coff_calc
 * @brief: 椭球拟合系数计算
 * @param {mag_eigen_points_t} *eps - 地磁特征点
 * @param {ellipsoid_fit_mat_t} *fit_mat - 椭球拟合中间结果矩阵
 * @return {*} false - 拟合失败 true - 拟合成功
 * *********************************************************************************************
 */
static int ellipsoid_fit_coff_calc(mag_eigen_points_t *eps, ellipsoid_fit_mat_t *fit_mat)
{
    int ret = 0;

    for (int i = 0; i < eps->num; i++)
    {
        ellipsoid_fit_mat_init_value_calc(eps->p + i, fit_mat->K.pData + i * 9);
    }

    for (int i = 0; i < eps->num; i++)
    {
        fit_mat->Y.pData[i] = 1.0;
    }

    arm_status status = ARM_MATH_SUCCESS;

    status = arm_mat_trans_f64(&fit_mat->K, &fit_mat->KT);
    if (status != ARM_MATH_SUCCESS)
    {
        ret = -1;
        goto error;
    }
    status = arm_mat_mult_f64(&fit_mat->KT, &fit_mat->K, &fit_mat->KTK);
    if (status != ARM_MATH_SUCCESS)
    {
        ret = -2;
        goto error;
    }
    status = arm_mat_inverse_f64(&fit_mat->KTK, &fit_mat->KTKI);
    if (status != ARM_MATH_SUCCESS)
    {
        ret = -3;
        goto error;
    }
    status = arm_mat_mult_f64(&fit_mat->KTKI, &fit_mat->KT, &fit_mat->KTKIKT);
    if (status != ARM_MATH_SUCCESS)
    {
        ret = -4;
        goto error;
    }
    status = arm_mat_mult_f64(&fit_mat->KTKIKT, &fit_mat->Y, &fit_mat->KTKIKTY);
    if (status != ARM_MATH_SUCCESS)
    {
        ret = -5;
        goto error;
    }

    float64_t a, b, c, f, g, h;
    a = fit_mat->KTKIKTY.pData[0];
    b = fit_mat->KTKIKTY.pData[1];
    c = fit_mat->KTKIKTY.pData[2];
    f = fit_mat->KTKIKTY.pData[3];
    g = fit_mat->KTKIKTY.pData[4];
    h = fit_mat->KTKIKTY.pData[5];

    float64_t I, J;
    I = a + b + c;
    J = a * b + b * c + a * c - f * f - g * g - h * h;

    //判断拟合结果是否是椭球
    if (4 * J <= I * I)
    {
        ret = -6;
        goto error;
    }

error:
    return ret;
}

/**
 * *********************************************************************************************
 * @name: ellipsoid_calib_mat_value_init
 * @brief: 椭球校准矩阵中间结果结构体的值初始化
 * @param {ellipsoid_calib_mat_value_t} *value
 * @return {*}
 * *********************************************************************************************
 */
static void ellipsoid_calib_mat_value_init(ellipsoid_calib_mat_value_t *value)
{
    for (int i = 0; i < 9; i++)
    {
        value->S[i] = 0.0;
    }
}

/**
 * *********************************************************************************************
 * @name: ellipsoid_calib_tmp_mat_init
 * @brief: 椭球校准中间结果结构体初始化
 * @param {ellipsoid_calib_mat_t} *mat - 中间结果矩阵
 * @param {ellipsoid_calib_mat_value_t} *value - 中间结果矩阵的值
 * @return {*}
 * *********************************************************************************************
 */
static void ellipsoid_calib_mat_init(ellipsoid_calib_mat_t *mat,
                                     ellipsoid_calib_mat_value_t *value)
{
    mat->E.numRows = mat->E.numCols = 3;
    mat->E.pData = value->E;

    mat->EI.numRows = mat->EI.numCols = 3;
    mat->EI.pData = value->EI;

    mat->F.numRows = 3;
    mat->F.numCols = 1;
    mat->F.pData = value->F;

    mat->ET.numRows = mat->ET.numCols = 3;
    mat->ET.pData = value->ET;

    mat->ETE.numRows = mat->ETE.numCols = 3;
    mat->ETE.pData = value->ETE;

    mat->U.numRows = mat->U.numCols = 3;
    mat->U.pData = value->U;

    mat->S.numRows = mat->S.numCols = 3;
    mat->S.pData = value->S;

    mat->V.numRows = mat->V.numCols = 3;
    mat->V.pData = value->V;

    mat->EV.numRows = mat->EV.numCols = 3;
    mat->EV.pData = value->EV;

    mat->VT.numRows = mat->VT.numCols = 3;
    mat->VT.pData = value->VT;

    mat->US.numRows = mat->US.numCols = 3;
    mat->US.pData = value->US;
}

/**
 * *********************************************************************************************
 * @name: mag_calib_mat_value_init
 * @brief: 地磁校准矩阵的值初始化
 * @param {cfg_mag_calib_t} *value - 地磁校准矩阵的值
 * @return {*}
 * *********************************************************************************************
 */
static void mag_calib_mat_value_init(cfg_mag_calib_t *value)
{
    for (int i = 0; i < 9; i++)
    {
        *(value->soft_calib + i) = 0.0;
    }
    *(value->soft_calib) = *(value->soft_calib + 4) = *(value->soft_calib + 8) = 1.0;
    *(value->hard_calib) = *(value->hard_calib + 1) = *(value->hard_calib + 2) = 0.0;
    value->is_calibrated = false;
}

/**
 * *********************************************************************************************
 * @name: mag_calib_mat_init
 * @brief: 地磁校准矩阵初始化
 * @param {mag_calib_mat_t} *mat - 地磁校准矩阵
 * @param {cfg_mag_calib_t} *value - 地磁校准矩阵的值
 * @return {*}
 * *********************************************************************************************
 */
static void mag_calib_mat_init(mag_calib_mat_t *mat, cfg_mag_calib_t *value)
{
    mat->soft.numRows = mat->soft.numCols = 3;
    mat->soft.pData = value->soft_calib;

    mat->hard.numRows = 3;
    mat->hard.numCols = 1;
    mat->hard.pData = value->hard_calib;
}

/**
 * *********************************************************************************************
 * @name: mag_calib_coff_mat_eigenvalue_calc
 * @brief: 椭球拟合系数构造的系数矩阵特征值求取
 * @param {arm_matrix_instance_f64} *coff_mat - 系数矩阵
 * @param {mat_3x3_eigendecomposition_t} *ev - 特征值
 * @return {*} true - 特征值存在 false - 特征值不存在
 * *********************************************************************************************
 */
static int mag_calib_coff_mat_eigenvalue_calc(arm_matrix_instance_f64 *coff_mat,
                                              mat_3x3_eigendecomposition_t *ev)
{
    int ret = false;

    /*
     * 计算原理：系数矩阵是3x3矩阵，维数较小，可通过构造对应的行列式计算特征值，即：
     * |λI - A| = 0
     * 展开后是一个一元三次方程，再通过其求根公式计算其三个解，即为特征值
     */

    float64_t a11, a12, a13, a21, a22, a23, a31, a32, a33;
    a11 = coff_mat->pData[0];
    a12 = coff_mat->pData[1];
    a13 = coff_mat->pData[2];
    a21 = coff_mat->pData[3];
    a22 = coff_mat->pData[4];
    a23 = coff_mat->pData[5];
    a31 = coff_mat->pData[6];
    a32 = coff_mat->pData[7];
    a33 = coff_mat->pData[8];

    float64_t c1, c2, c3;
    c1 = -(a11 + a22 + a33);
    c2 = a11 * a22 + a11 * a33 + a22 * a33 - a13 * a31 - a12 * a21 - a32 * a23;
    c3 = a31 * a13 * a22 + a11 * a32 * a23 + a21 * a12 * a33 - a12 * a23 * a31
       - a21 * a32 * a13 - a11 * a22 * a33;

    float64_t Q, R;
    Q = (c1 * c1 - 3.0 * c2) / 9.0;
    R = (2.0 * (c1 * c1 * c1) - 9.0 * c1 * c2 + 27.0 * c3) / 54.0;

    float64_t R_pow2 = R * R;
    float64_t Q_pow3 = Q * Q * Q;

    float64_t x1, x2, x3;

    //这里的判断条件确保了Q>=0
    if (R_pow2 < Q_pow3)
    {
        float64_t theta = acos(R / sqrt(Q_pow3));
        float64_t Q_sqrt = sqrt(Q);
        float64_t C = c1 / 3.0;

        x1 = -2.0 * Q_sqrt * cos(theta / 3.0) - C;
        x2 = -2.0 * Q_sqrt * cos((theta + 2.0 * PI) / 3.0) - C;
        x3 = -2.0 * Q_sqrt * cos((theta - 2.0 * PI) / 3.0) - C;

        if (x1 < 0.0 || x2 < 0.0 || x3 < 0.0)
        {
            goto error;
        }

        //按照从大到小的顺序排序输出
        float64_t buf[3] = {x1, x2, x3};
        for (uint8_t i = 1; i < 3; i++)
        {
            if (buf[i] < buf[i - 1])
            {
                float64_t tmp = buf[i];
                buf[i] = buf[i - 1];
                buf[i - 1] = tmp;
            }
        }

        if (buf[1] < buf[0])
        {
            float64_t tmp = buf[1];
            buf[1] = buf[0];
            buf[0] = tmp;
        }

        ev->elem1.eval = buf[2];
        ev->elem2.eval = buf[1];
        ev->elem3.eval = buf[0];

        ret = true;
    }

error:
    return ret;
}

/**
 * *********************************************************************************************
 * @name: mat_3x3_row_exchange
 * @brief: 交换矩阵两行
 * @param {float64_t} *row1
 * @param {float64_t} *row2
 * @return {*}
 * *********************************************************************************************
 */
static void mat_3x3_row_exchange(float64_t *row1, float64_t *row2)
{
    float64_t tmp = 0.0;
    for (int i = 0; i < 3; i++)
    {
        tmp = row2[i];
        row2[i] = row1[i];
        row1[i] = tmp;
    }
}

/**
 * *********************************************************************************************
 * @name: mat_3x3_eigenvector_calc
 * @brief: 已知矩阵的特征值计算特征值对应的特征向量
 * @param {arm_matrix_instance_f64} *mat - 矩阵
 * @param {mat_3x3_eigen_elem_t} *elem - 特征值和特征向量
 * @return {*} true - 计算成功 false - 计算失败
 * *********************************************************************************************
 */
static int mat_3x3_eigenvector_calc(arm_matrix_instance_f64 *mat,
                                    mat_3x3_eigen_elem_t *elem)
{
    int ret = false;

    float64_t emat[3][3] = {{0.0}, {0.0}, {0.0}};
    float64_t coff = 0.0;

    if (mat->numRows != 3 || mat->numCols != 3)
    {
        goto error;
    }

    memcpy(emat, mat->pData, sizeof(emat));

    //系数矩阵减去特征值
    emat[0][0] -= elem->eval;
    emat[1][1] -= elem->eval;
    emat[2][2] -= elem->eval;

    float64_t r1_abs, r2_abs, r3_abs;
    r1_abs = fabs(emat[0][0]);
    r2_abs = fabs(emat[1][0]);
    r3_abs = fabs(emat[2][0]);

    //主元行index
    uint32_t e_idx = 0;

    //选主元
    if (r1_abs >= r2_abs)
    {
        if (r1_abs >= r3_abs)
        {
            e_idx = 0;
        }
        else
        {
            e_idx = 2;
        }
    }
    else
    {
        if (r2_abs >= r3_abs)
        {
            e_idx = 1;
        }
        else
        {
            e_idx = 2;
        }
    }

    if (e_idx != 0)
    {
        mat_3x3_row_exchange(emat[0], emat[e_idx]);
    }

    //现在a11已经是最大的了，但若仍为0，则无法进行后续计算，出错
    if (fabs(emat[0][0]) < FLOAT_ZERO_APPROXIMATION)
    {
        goto error;
    }

    //对最后一行进行初等行变换，将其首元素消除为0
    coff = emat[2][0] / emat[0][0];
    for (int i = 0; i < 3; i++)
    {
        emat[2][i] -= emat[0][i] * coff;
    }

    //对倒数第二行进行初等行变换，将其首元素消除为0
    coff = emat[1][0] / emat[0][0];
    for (int i = 0; i < 3; i++)
    {
        emat[1][i] -= emat[0][i] * coff;
    }

    //剩余项重选主元
    if (emat[1][1] < emat[2][1])
    {
        mat_3x3_row_exchange(emat[1], emat[2]);
    }

    //对最后一行进行初等行变换，将其第二个元素消除为0
    coff = emat[2][1] / emat[1][1];
    for (int i = 1; i < 3; i++)
    {
        emat[2][i] -= emat[1][i] * coff;
    }

    //经过以上操作，a33应该为0，否则无法计算特征矩阵，出错
    // if (fabs(emat[2][2]) > FLOAT_ZERO_APPROXIMATION)
    // {
    //     goto error;
    // }

    float64_t x1, x2, x3;
    x3 = 1.0;
    x2 = -(emat[1][2] * x3) / emat[1][1];
    x1 = -(emat[0][1] * x2 + emat[0][2] * x3) / emat[0][0];

    //归一化处理
    float64_t mag;
    mag = sqrt(x1 * x1 + x2 * x2 + x3 * x3);
    x1 /= mag;
    x2 /= mag;
    x3 /= mag;

    elem->evec[0] = x1;
    elem->evec[1] = x2;
    elem->evec[2] = x3;

    ret = true;

error:
    return ret;
}

/**
 * *********************************************************************************************
 * @name: mag_calib_coff_mat_eigenvector_calc
 * @brief: 地磁椭球拟合系数矩阵特征向量求取
 * @param {arm_matrix_instance_f64} *coff_mat - 系数矩阵
 * @param {mat_3x3_eigendecomposition_t} *ev - 特征值和特征向量
 * @return {*} true - 计算成功 false - 计算失败
 * *********************************************************************************************
 */
static int mag_calib_coff_mat_eigenvector_calc(arm_matrix_instance_f64 *coff_mat,
                                               mat_3x3_eigendecomposition_t *ev)
{
    int ret = true;
    if (mat_3x3_eigenvector_calc(coff_mat, &ev->elem1) == false)
    {
        ret = false;
        goto error;
    }
    if (mat_3x3_eigenvector_calc(coff_mat, &ev->elem2) == false)
    {
        ret = false;
        goto error;
    }
    if (mat_3x3_eigenvector_calc(coff_mat, &ev->elem3) == false)
    {
        ret = false;
        goto error;
    }

error:
    return ret;
}

/**
 * *********************************************************************************************
 * @name: mag_calib_coff_mat_eigendecomposition
 * @brief: 地磁校准椭球拟合系数矩阵特征值分解
 * @param {arm_matrix_instance_f64} *coff_mat - 系数矩阵
 * @param {mat_3x3_eigendecomposition_t} *res - 计算结果
 * @return {*} true - 计算成功 false - 计算失败
 * *********************************************************************************************
 */
static int mag_calib_coff_mat_eigendecomposition(arm_matrix_instance_f64 *coff_mat,
                                                 mat_3x3_eigendecomposition_t *res)
{
    int ret = true;
    if (mag_calib_coff_mat_eigenvalue_calc(coff_mat, res) == false)
    {
        ret = false;
        goto error;
    }
    if (mag_calib_coff_mat_eigenvector_calc(coff_mat, res) == false)
    {
        ret = false;
        goto error;
    }
error:
    return ret;
}

/**
 * *********************************************************************************************
 * @name: ellipsoid_calib_fit_mat_svd_left_smat_calc
 * @brief: 椭球校正拟合系数矩阵奇异值分解左奇异矩阵计算
 * @param {ellipsoid_calib_mat_t} *calib_mat
 * @return {*} true - 成功，false - 失败
 * *********************************************************************************************
 */
static int ellipsoid_calib_fit_mat_svd_left_smat_calc(ellipsoid_calib_mat_t *calib_mat)
{
    int ret = true;

    arm_status status = ARM_MATH_SUCCESS;

    status = arm_mat_mult_f64(&calib_mat->E, &calib_mat->V, &calib_mat->EV);
    if (status != ARM_MATH_SUCCESS)
    {
        ret = false;
        goto error;
    }

    calib_mat->U.pData[0] = calib_mat->EV.pData[0] / calib_mat->S.pData[0];
    calib_mat->U.pData[3] = calib_mat->EV.pData[3] / calib_mat->S.pData[0];
    calib_mat->U.pData[6] = calib_mat->EV.pData[6] / calib_mat->S.pData[0];

    calib_mat->U.pData[1] = calib_mat->EV.pData[1] / calib_mat->S.pData[4];
    calib_mat->U.pData[4] = calib_mat->EV.pData[4] / calib_mat->S.pData[4];
    calib_mat->U.pData[7] = calib_mat->EV.pData[7] / calib_mat->S.pData[4];

    calib_mat->U.pData[2] = calib_mat->EV.pData[2] / calib_mat->S.pData[8];
    calib_mat->U.pData[5] = calib_mat->EV.pData[5] / calib_mat->S.pData[8];
    calib_mat->U.pData[8] = calib_mat->EV.pData[8] / calib_mat->S.pData[8];

    return ret;

error:
    return ret;
}

/**
 * *********************************************************************************************
 * @name: matrix_type_check
 * @brief: 检查矩阵类型（正定、负定、其他）
 * @param {arm_matrix_instance_f64} *mat
 * @return {*}
 * *********************************************************************************************
 */
static matrix_type_t matrix_type_check(arm_matrix_instance_f64 *mat)
{
    matrix_type_t ret = enumMATRIX_OTHERS;

    float64_t a11 = mat->pData[0];
    float64_t a12 = mat->pData[3];
    float64_t a13 = mat->pData[4];

    float64_t a21 = mat->pData[3];
    float64_t a22 = mat->pData[1];
    float64_t a23 = mat->pData[5];

    float64_t a31 = mat->pData[4];
    float64_t a32 = mat->pData[5];
    float64_t a33 = mat->pData[2];

    //计算各阶顺序主子式
    float64_t A1 = a11;
    float64_t A2 = a11 * a22 - a21 * a12;
    float64_t A3 = a11 * a22 * a33 + a21 * a32 * a13 + a12 * a23 * a31 - a31 * a22 * a13
                 - a21 * a12 * a33 - a11 * a32 * a23;

    if (A1 > 0 && A2 > 0 && A3 > 0)
    {
        ret = enumMATRIX_POSITIVE_DEFINITE;
    }
    else if (A1 < 0 && A2 > 0 && A3 < 0)
    {
        ret = enumMATRIX_NEGATIVE_DEFINITE;
    }
    else
    {
        ret = enumMATRIX_OTHERS;
    }

    return ret;
}

/**
 * *********************************************************************************************
 * @name: ellipsoid_calib_mat_calc
 * @brief: 椭球校准矩阵计算
 * @param {ellipsoid_fit_mat_t} *fit_mat - 拟合系数
 * @param {ellipsoid_calib_mat_t} *calib_mat - 中间结果
 * @param {mat_3x3_eigendecomposition_t} *coff_edc - 用于特征值分解
 * @param {mag_calib_mat_t} *mag_calib_mat - 结果，包括软磁校正和硬磁校正
 * @return {*} true - 校正成功 false - 校正失败
 * *********************************************************************************************
 */
static int ellipsoid_calib_mat_calc(ellipsoid_fit_mat_t *fit_mat,
                                    ellipsoid_calib_mat_t *calib_mat,
                                    mat_3x3_eigendecomposition_t *coff_edc,
                                    mag_calib_mat_t *mag_calib_mat)
{
    int ret = true;

    arm_status status = ARM_MATH_SUCCESS;

    calib_mat->E.pData[0] = fit_mat->KTKIKTY.pData[0];
    calib_mat->E.pData[4] = fit_mat->KTKIKTY.pData[1];
    calib_mat->E.pData[8] = fit_mat->KTKIKTY.pData[2];
    calib_mat->E.pData[1] = calib_mat->E.pData[3] = fit_mat->KTKIKTY.pData[3];
    calib_mat->E.pData[2] = calib_mat->E.pData[6] = fit_mat->KTKIKTY.pData[4];
    calib_mat->E.pData[5] = calib_mat->E.pData[7] = fit_mat->KTKIKTY.pData[5];

    calib_mat->F.pData[0] = fit_mat->KTKIKTY.pData[6];
    calib_mat->F.pData[1] = fit_mat->KTKIKTY.pData[7];
    calib_mat->F.pData[2] = fit_mat->KTKIKTY.pData[8];

    status = arm_mat_trans_f64(&calib_mat->E, &calib_mat->ET);
    if (status != ARM_MATH_SUCCESS)
    {
        ret = false;
        goto error;
    }

    status = arm_mat_mult_f64(&calib_mat->ET, &calib_mat->E, &calib_mat->ETE);
    if (status != ARM_MATH_SUCCESS)
    {
        ret = false;
        goto error;
    }

    //计算实对称矩阵ETE的特征值和对应特征向量
    if (mag_calib_coff_mat_eigendecomposition(&calib_mat->ETE, coff_edc) == false)
    {
        ret = false;
        goto error;
    }

    //特征值构造对角矩阵
    float64_t eval1 = sqrt(coff_edc->elem1.eval);
    float64_t eval2 = sqrt(coff_edc->elem2.eval);
    float64_t eval3 = sqrt(coff_edc->elem3.eval);

    calib_mat->S.pData[0] = eval1;
    calib_mat->S.pData[4] = eval2;
    calib_mat->S.pData[8] = eval3;

    //特征向量构造特征矩阵
    calib_mat->V.pData[0] = coff_edc->elem1.evec[0];
    calib_mat->V.pData[3] = coff_edc->elem1.evec[1];
    calib_mat->V.pData[6] = coff_edc->elem1.evec[2];
    calib_mat->V.pData[1] = coff_edc->elem2.evec[0];
    calib_mat->V.pData[4] = coff_edc->elem2.evec[1];
    calib_mat->V.pData[7] = coff_edc->elem2.evec[2];
    calib_mat->V.pData[2] = coff_edc->elem3.evec[0];
    calib_mat->V.pData[5] = coff_edc->elem3.evec[1];
    calib_mat->V.pData[8] = coff_edc->elem3.evec[2];

    if (ellipsoid_calib_fit_mat_svd_left_smat_calc(calib_mat) == false)
    {
        ret = false;
        goto error;
    }

    calib_mat->S.pData[0] = sqrt(calib_mat->S.pData[0]);
    calib_mat->S.pData[4] = sqrt(calib_mat->S.pData[4]);
    calib_mat->S.pData[8] = sqrt(calib_mat->S.pData[8]);

    status = arm_mat_trans_f64(&calib_mat->V, &calib_mat->VT);
    if (status != ARM_MATH_SUCCESS)
    {
        ret = false;
        goto error;
    }

    //计算软磁校正矩阵
    status = arm_mat_mult_f64(&calib_mat->U, &calib_mat->S, &calib_mat->US);
    if (status != ARM_MATH_SUCCESS)
    {
        ret = false;
        goto error;
    }

    status = arm_mat_mult_f64(&calib_mat->US, &calib_mat->VT, &mag_calib_mat->soft);
    if (status != ARM_MATH_SUCCESS)
    {
        ret = false;
        goto error;
    }

    status = arm_mat_inverse_f64(&calib_mat->E, &calib_mat->EI);
    if (status != ARM_MATH_SUCCESS)
    {
        ret = false;
        goto error;
    }

    //计算硬磁校正矩阵
    status = arm_mat_mult_f64(&calib_mat->EI, &calib_mat->F, &mag_calib_mat->hard);
    if (status != ARM_MATH_SUCCESS)
    {
        ret = false;
        goto error;
    }

    mag_calib_mat->hard.pData[0] = -mag_calib_mat->hard.pData[0];
    mag_calib_mat->hard.pData[1] = -mag_calib_mat->hard.pData[1];
    mag_calib_mat->hard.pData[2] = -mag_calib_mat->hard.pData[2];

error:
    return ret;
}

/**
 * *********************************************************************************************
 * @name: mag_calib
 * @brief: 地磁数据校准
 * @param {cfg_mag_calib_t} *calib_mat - 校准矩阵
 * @param {sensor_data_t} *mag - 地磁原始数据
 * @return {*}
 * *********************************************************************************************
 */
static void mag_calib(cfg_mag_calib_t *calib_mat, sensor_data_t *mag)
{
    if (calib_mat->is_calibrated == true)
    {
        float64_t x, y, z;
        x = (float64_t) mag->x - calib_mat->hard_calib[0] * MAG_SCALE;
        y = (float64_t) mag->y - calib_mat->hard_calib[1] * MAG_SCALE;
        z = (float64_t) mag->z - calib_mat->hard_calib[2] * MAG_SCALE;

        float64_t x_calib, y_calib, z_calib;
        x_calib = calib_mat->soft_calib[0] * x + calib_mat->soft_calib[1] * y
                + calib_mat->soft_calib[2] * z;
        y_calib = calib_mat->soft_calib[3] * x + calib_mat->soft_calib[4] * y
                + calib_mat->soft_calib[5] * z;
        z_calib = calib_mat->soft_calib[6] * x + calib_mat->soft_calib[7] * y
                + calib_mat->soft_calib[8] * z;

        //归一化处理，避免校准后的值溢出
        float64_t mag_mag, scale;
        //地磁幅值
        mag_mag = sqrt(x_calib * x_calib + y_calib * y_calib + z_calib * z_calib);
        //缩放比
        scale = (float64_t) EARTH_MAG_AVG_SCALE / mag_mag;

        x_calib *= scale;
        y_calib *= scale;
        z_calib *= scale;

        mag->x = (int16_t) x_calib;
        mag->y = (int16_t) y_calib;
        mag->z = (int16_t) z_calib;
    }
}

/**
 * *********************************************************************************************
 * @name: sensor_data_preprocessing
 * @brief: 传感器数据预处理，主要是加速度，需校正轴向和读数
 * @param {sensor_data_t} *mag - 地磁原始数据
 * @param {sensor_data_t} *acc - 加速度原始数据
 * @return {*}
 * *********************************************************************************************
 */
static void sensor_data_preprocessing(sensor_data_t *mag, sensor_data_t *acc)
{
    acc->x >>= 6;
    acc->y >>= 6;
    acc->z >>= 6;

    // acc->z = -acc->z;
}

/**
 * *********************************************************************************************
 * @name: mag_plane_projection_calc
 * @brief: 地磁平面投影分量计算，使用了加速度数据进行倾角补偿
 * @param {sensor_data_t} *mag - 地磁原始数据
 * @param {sensor_data_t} *acc - 加速度原始数据
 * @param {plane_vector_t} *vec - 地磁平面投影向量
 * @return {*} true - 计算成功 false - 计算失败
 * *********************************************************************************************
 */
static int mag_plane_projection_calc(sensor_data_t *mag, sensor_data_t *acc,
                                     plane_vector_t *vec)
{
    int ret = true;

    float64_t phi, theta;

    if (acc->y == 0 && acc->z == 0)
    {
        phi = 0.0;
    }
    else
    {
        phi = PI + atan2(acc->y, acc->z);
    }

    if (acc->x == 0 && acc->z == 0)
    {
        theta = 0.0;
    }
    else
    {
        theta = PI - atan2(acc->x, acc->z);
    }

    float64_t mx, my;

    mx = cos(theta) * (float64_t) mag->x + sin(phi) * sin(theta) * (float64_t) mag->y
       + cos(phi) * sin(theta) * (float64_t) mag->z;
    my = cos(phi) * (float64_t) mag->y - sin(phi) * (float64_t) mag->z;
    if (fabs(mx) < FLOAT_ZERO_APPROXIMATION && fabs(my) < FLOAT_ZERO_APPROXIMATION)
    {
        ret = false;
        goto error;
    }
    else
    {
        vec->x = (int16_t) mx;
        vec->y = (int16_t) my;
#if 0
        psai = atan2(mx, my);
        if (mx < 0.0)
        {
            psai = -psai;
        }
        else
        {
            psai = 2 * PI - psai;
        }
#endif
    }

error:
    return ret;
}

/**
 * *********************************************************************************************
 * @name: mag_moving_avg_filter
 * @brief: 地磁平面向量移动平均滤波
 * @param {plane_vector_t} *vec - 当前平面向量，函数返回时值已被更新
 * @return {*}
 * *********************************************************************************************
 */
static void mag_moving_avg_filter(plane_vector_t *vec)
{
    int32_t sum_x = 0;
    int32_t sum_y = 0;

    static plane_vector_t s_mag_delay[MAG_MA_FILTER_ORDER] = {0};
    static uint16_t cnt = 0;

    if (cnt < MAG_MA_FILTER_ORDER)
    {
        s_mag_delay[cnt].x = vec->x;
        s_mag_delay[cnt].y = vec->y;
        cnt++;
    }
    else
    {
        for (uint16_t i = 0; i < MAG_MA_FILTER_ORDER - 1; i++)
        {
            s_mag_delay[i].x = s_mag_delay[i + 1].x;
            s_mag_delay[i].y = s_mag_delay[i + 1].y;
        }
        s_mag_delay[MAG_MA_FILTER_ORDER - 1].x = vec->x;
        s_mag_delay[MAG_MA_FILTER_ORDER - 1].y = vec->y;
    }

    for (uint16_t i = 0; i < cnt; i++)
    {
        sum_x += s_mag_delay[i].x;
        sum_y += s_mag_delay[i].y;
    }

    vec->x = (int16_t) (sum_x / cnt);
    vec->y = (int16_t) (sum_y / cnt);
}

/**
 * *********************************************************************************************
 * @name: yaw_calc
 * @brief: 计算航向角
 * @param {plane_vector_t} *vec - 平滑处理后的平面向量
 * @return {*} 航向角
 * *********************************************************************************************
 */
static uint16_t yaw_calc(plane_vector_t *vec)
{
    uint16_t ret = 0;

    float64_t psai;

    if (vec->x == 0 && vec->y == 0)
    {
        psai = 0.0;
    }
    else
    {
        psai = atan2(vec->x, vec->y);
        if (vec->x < 0)
        {
            psai = -psai;
        }
        else
        {
            psai = 2 * PI - psai;
        }
    }

    ret = (uint16_t) (psai * 180.0 / PI) % 360;

    return ret;
}

static int accel_queue_push(struct sensor_accel *in_acc)
{
    ssize_t overwrite_bytes = 0;
    rt_mutex_take(&accel_queue.mutex, RT_WAITING_FOREVER);

    // 使用overwrite接口，自动处理缓冲区满的情况
    overwrite_bytes = circbuf_overwrite(&accel_queue.circbuf, in_acc, sizeof(struct sensor_accel));
    s_acc_data_cnt++;

    // 如果有数据被覆盖，记录日志
    if (overwrite_bytes > 0)
    {
        BASIC_APP_LOG_W("accel queue overwrite %d bytes", overwrite_bytes);
    }

    rt_mutex_release(&accel_queue.mutex);
    return 0; // overwrite总是成功的
}

static bool accel_queue_pop(struct sensor_accel *out_accel)
{
    ssize_t read_bytes = 0;
    rt_mutex_take(&accel_queue.mutex, RT_WAITING_FOREVER);

    if (!circbuf_is_empty(&accel_queue.circbuf))
    {
        read_bytes = circbuf_read(&accel_queue.circbuf, out_accel, sizeof(struct sensor_accel));
        if (read_bytes != sizeof(struct sensor_accel))
        {
            BASIC_APP_LOG_E("accel queue pop fail: read=%d", read_bytes);
        }
    }

    rt_mutex_release(&accel_queue.mutex);
    return (read_bytes == sizeof(struct sensor_accel));
}

static int mag_queue_push(struct sensor_mag *in_mag)
{
    ssize_t overwrite_bytes = 0;
    rt_mutex_take(&mag_queue.mutex, RT_WAITING_FOREVER);

    // 使用overwrite接口，自动处理缓冲区满的情况
    overwrite_bytes = circbuf_overwrite(&mag_queue.circbuf, in_mag, sizeof(struct sensor_mag));
    s_mag_data_cnt++;

    // 如果有数据被覆盖，记录日志
    if (overwrite_bytes > 0)
    {
        BASIC_APP_LOG_W("mag queue overwrite %d bytes", overwrite_bytes);
    }

    rt_mutex_release(&mag_queue.mutex);
    return 0; // overwrite总是成功的
}

static bool mag_queue_pop(struct sensor_mag *out_mag)
{
    ssize_t read_bytes = 0;
    rt_mutex_take(&mag_queue.mutex, RT_WAITING_FOREVER);

    if (!circbuf_is_empty(&mag_queue.circbuf))
    {
        read_bytes = circbuf_read(&mag_queue.circbuf, out_mag, sizeof(struct sensor_mag));
        if (read_bytes != sizeof(struct sensor_mag))
        {
            BASIC_APP_LOG_E("mag queue pop fail: read=%d", read_bytes);
        }
    }

    rt_mutex_release(&mag_queue.mutex);
    return (read_bytes == sizeof(struct sensor_mag));
}

/************************************************************************
 *@function: gyro_queue_push
 *@brief: 陀螺仪数据入队
 *@param: in_gyro - 陀螺仪数据指针
 *@return: 成功返回0
*************************************************************************/
static int gyro_queue_push(gyro_data_t *in_gyro)
{
    ssize_t overwrite_bytes = 0;
    rt_mutex_take(&gyro_queue.mutex, RT_WAITING_FOREVER);

    // 使用overwrite接口，自动处理缓冲区满的情况
    overwrite_bytes = circbuf_overwrite(&gyro_queue.circbuf, in_gyro, sizeof(gyro_data_t));
    s_gyro_data_cnt++;

    // // 如果有数据被覆盖，记录日志
    // if (overwrite_bytes > 0)
    // {
    //     BASIC_APP_LOG_W("gyro queue overwrite %d bytes", overwrite_bytes);
    // }

    rt_mutex_release(&gyro_queue.mutex);
    return 0; // overwrite总是成功的
}

/************************************************************************
 *@function: gyro_queue_pop
 *@brief: 陀螺仪数据出队
 *@param: out_gyro - 陀螺仪数据输出指针
 *@return: 成功返回true，失败返回false
*************************************************************************/
static bool gyro_queue_pop(gyro_data_t *out_gyro)
{
    ssize_t read_bytes = 0;
    rt_mutex_take(&gyro_queue.mutex, RT_WAITING_FOREVER);

    if (!circbuf_is_empty(&gyro_queue.circbuf))
    {
        read_bytes = circbuf_read(&gyro_queue.circbuf, out_gyro, sizeof(gyro_data_t));
        if (read_bytes != sizeof(gyro_data_t))
        {
            BASIC_APP_LOG_E("gyro queue pop fail: read=%d", read_bytes);
        }
    }

    rt_mutex_release(&gyro_queue.mutex);
    return (read_bytes == sizeof(gyro_data_t));
}


/************************************************************************
 *@function:static void compass_recv_acc(const void* data, uint32_t len)
 *@brief:ACC传感器数据通知回调函数
*************************************************************************/
static void compass_recv_acc(const void *data, uint32_t len)
{
    if (!is_running)
        return;
    const int acc_size = sizeof(struct sensor_accel);
    const int cnt = len / acc_size;
    struct sensor_accel *in_acc = (struct sensor_accel *) data;
    for (int i = 0; i < cnt; i++)
    {
        accel_queue_push(&(in_acc[i]));
        //  rt_kprintf("recv acc:%u @%u\n", (uint32_t) in_acc[i].timestamp,
        //             (uint32_t) get_boot_msec());
    }

    rt_sem_release(&compass_algo_sem);
}

/************************************************************************
 *@function:static void compass_recv_mag(const void* data, uint32_t len)
 *@brief:MAG传感器数据通知回调函数
*************************************************************************/
static void compass_recv_mag(const void *data, uint32_t len)
{
    if (!is_running)
        return;
    //todo callback param need resure type.
    const int mag_size = sizeof(struct sensor_mag);
    const int cnt = len / mag_size;
    struct sensor_mag *in_mag = (struct sensor_mag *) data;
    for (int i = 0; i < cnt; i += 2)  // 保持原来的步长2
    {
        mag_queue_push(&(in_mag[i]));
        //   rt_kprintf("recv mag:%u @%u\n", (uint32_t) in_mag[i].timestamp,
        //             (uint32_t) get_boot_msec());
    }
    rt_sem_release(&compass_algo_sem);
}

/************************************************************************
 *@function:static void compass_recv_gyro(const void* data, uint32_t len)
 *@brief:陀螺仪传感器数据通知回调函数
*************************************************************************/
static void compass_recv_gyro(const void *data, uint32_t len)
{
    if (!is_running || !s_fusion_enabled)
        return;

    // 陀螺仪数据格式
    const int gyro_size = sizeof(struct sensor_gyro);
    const int cnt = len / gyro_size;
    struct sensor_gyro *in_gyro = (struct sensor_gyro *) data;

    for (int i = 0; i < cnt; i++)
    {
        // 保存最新的陀螺仪数据到全局变量
        g_gyro_data = in_gyro[i];

        // 转换为融合算法需要的数据格式
        gyro_data_t gyro_data;
        gyro_data.x = in_gyro[i].x;        // rad/s
        gyro_data.y = in_gyro[i].y;        // rad/s
        gyro_data.z = in_gyro[i].z;        // rad/s
        gyro_data.timestamp = in_gyro[i].timestamp;  // 微秒

        gyro_queue_push(&gyro_data);

        // 实时更新陀螺仪数据到融合算法
        if (s_fusion_enabled)
        {
            // const uint32_t t1 = rt_tick_get();
            compass_fusion_update_gyro(&gyro_data);
            // const uint32_t t2 = rt_tick_get();
            // rt_kprintf("fusion cost:%u\n", (t2-t1));
        }
    }
    rt_sem_release(&compass_algo_sem);
}

/************************************************************************
 *@function:static void compass_algo_process(void* parameter)
 *@brief:指南针算法执行函数,内部会将acc、mag数据进行对齐.
 *************************************************************************/
static void compass_algo_process(void *parameter)
{
    struct sensor_mag tmp_mag = {0};
    struct sensor_accel tmp_accel = {0};
    //取出最新的acc和mag，且时间戳同步
    while (1)
    {
        // 等待信号量
        rt_sem_take(&compass_algo_sem, RT_WAITING_FOREVER);
        // 恢复原来的简单处理逻辑，因为优化已在入队阶段完成
        while (!circbuf_is_empty(&mag_queue.circbuf) && !circbuf_is_empty(&accel_queue.circbuf))
        {
            memset(&tmp_mag, 0, sizeof(tmp_mag));
            memset(&tmp_accel, 0, sizeof(tmp_accel));
            const bool acc_ready = accel_queue_pop(&tmp_accel);
            const bool mag_ready = mag_queue_pop(&tmp_mag);
            if (acc_ready && mag_ready)
            {
                if (ecompass_listener && ecompass_listener->mag_acc_cb)
                {
                    ecompass_listener->mag_acc_cb(SENSOR_TYPE_ACCELEROMETER, &tmp_accel,
                                                  sizeof(tmp_accel));
                    ecompass_listener->mag_acc_cb(SENSOR_TYPE_MAGNETIC_FIELD, &tmp_mag,
                                                  sizeof(tmp_mag));
                }
                //输入要求acc和mag时间戳误差在50ms以内.
                BASIC_APP_LOG_D("ts %llu %llu",  tmp_accel.timestamp,
                                   tmp_mag.timestamp);
                if (get_diff_by_unsigned(tmp_accel.timestamp, tmp_mag.timestamp) <= 50)
                {
                    s_mag.x = -(int16_t) ((float) tmp_mag.x);
                    s_mag.y = -(int16_t) ((float) tmp_mag.y);
                    s_mag.z = (int16_t) ((float) tmp_mag.z);
                    s_acc.x = (int16_t) tmp_accel.x;
                    s_acc.y = (int16_t) tmp_accel.y;
                    s_acc.z = -(int16_t) tmp_accel.z;
                    s_feed_cnt++;
                    //时间戳对齐后开始运算数据.
                    // const uint32_t t1 = rt_tick_get();
                    alg_compass_process(&s_mag, &s_acc, &alg_res_data);
                    // const uint32_t t2 = rt_tick_get();
                    // rt_kprintf("time cost:%u\n", (t2-t1));
#ifdef COMPASS_ALGO_CAPTURE
                    write_capture_file("%d,%u,%d,%d,%d,%d,%d,%d,%d,%d,%d\n",
                                       (uint32_t) time(NULL),
                                       (uint32_t) tmp_accel.timestamp, s_feed_cnt,
                                       s_acc.x, s_acc.y, s_acc.z, s_mag.x, s_mag.y,
                                       s_mag.z, s_ecompass_status, s_yaw_angle);
#endif
                }
                else if (tmp_accel.timestamp > tmp_mag.timestamp)
                {
                    //剔除时间戳无法对齐的地磁数据
                    mag_queue_pop(&tmp_mag);
                    BASIC_APP_LOG_I("drop tmp_mag @%llu",tmp_mag.timestamp);
                }
                else
                {
                    //剔除时间戳无法对齐的ACC数据
                    accel_queue_pop(&tmp_accel);
                    BASIC_APP_LOG_I("drop tmp_accel @%llu",tmp_accel.timestamp);
                }
            }
            else
            {
                //有数据尚未准备好
                BASIC_APP_LOG_E("ready %d %d\n", acc_ready, mag_ready);
            }
        }
    }
}

/************************************************************************
 *@function:static void ecompass_algo_init(void)
 *@brief:指南针算法初始化
*************************************************************************/
static void ecompass_algo_init(void)
{
#ifdef IGS_DEV
    // 初始化传感器队列
    if (sensor_queue_init() != 0) {
        BASIC_APP_LOG_E("sensor queue init failed");
        return;
    }

    // 初始化传感器融合算法
    if (s_fusion_enabled)
    {
        compass_fusion_get_default_config(&s_fusion_config);
        if (compass_fusion_init(&s_fusion_config) != 0)
        {
            BASIC_APP_LOG_E("compass fusion init failed");
            s_fusion_enabled = false;
        }
        else
        {
            BASIC_APP_LOG_I("compass fusion algorithm initialized");
        }
    }

    rt_mutex_init(&sensor_acc_lock, "compass_acc", RT_IPC_FLAG_FIFO);
    rt_mutex_init(&sensor_mag_lock, "compass_mag", RT_IPC_FLAG_FIFO);
    rt_sem_init(&compass_algo_sem, "compass_algo", 0, RT_IPC_FLAG_FIFO);
    if (rt_thread_find("ecompass_algo") == RT_NULL)
    {
        rt_thread_init(&compass_algo_thread, "ecompass_algo", compass_algo_process,
                       RT_NULL, &compass_algo_stack[0], sizeof(compass_algo_stack),
                       RT_THREAD_PRIORITY_MIDDLE, 10);
    }
    /* startup */
    rt_thread_startup(&compass_algo_thread);
    BASIC_APP_LOG_I("s_ecompass_status :%d", s_ecompass_status);
#endif   // IGS_DEV
}

/************************************************************************
 *@function:static void ecompass_algo_uninit(void)
 *@brief:指南针算法反初始化
*************************************************************************/
static void ecompass_algo_uninit(void)
{
#ifdef IGS_DEV
    if (rt_thread_find("ecompass_algo") != RT_NULL)
    {
        rt_thread_detach(&compass_algo_thread);
    }

    // 反初始化传感器融合算法
    if (s_fusion_enabled)
    {
        compass_fusion_deinit();
        BASIC_APP_LOG_I("compass fusion algorithm deinitialized");
    }

    // 反初始化传感器队列
    sensor_queue_uninit();

    rt_mutex_detach(&sensor_acc_lock);
    rt_mutex_detach(&sensor_mag_lock);
    rt_sem_detach(&compass_algo_sem);
    BASIC_APP_LOG_I("ecompass_algo_uninit");
    if (enumEC_IN_CALIBRATING == s_ecompass_status)
        mag_calib_tmp_results_clean();
#endif   // IGS_DEV
}

/************************************************************************
 *@function:void ecompass_calibrate_start(ecompass_listener_t* listener)
 *@brief:开启地磁算法,算法内部会自动完成校准和航向角度计算.
 *@param:地磁算法输入输出回调
*************************************************************************/
void ecompass_calibrate_start(ecompass_listener_t *listener)
{
    if (is_running)
        return;
    p_mag_calib = get_calibrate_data();
    s_ecompass_status = get_is_calibrated() == true ? enumEC_CALIBRATE_SUCCESS : enumEC_CALIBRATE_FAILED;
#ifdef COMPASS_ALGO_CAPTURE
    open_capture_file();
#endif
    ecompass_algo_init();
    //开启校准
    BASIC_APP_LOG_I("%s %d", __func__, __LINE__);
    memset(&s_mag, 0, sizeof(s_mag));
    memset(&s_acc, 0, sizeof(s_acc));
    s_acc_data_cnt = s_mag_data_cnt = s_feed_cnt = 0;

    // 重置时间戳记录
    s_start_calibrate = true;
    s_stop_calibrate = false;

#ifdef FORCE_CALIBRATE_EVERY_TIME
    memset(p_mag_calib, 0, sizeof(cfg_mag_calib_t));
    p_mag_calib->type = enumMATRIX_OTHERS;
    s_ecompass_status = enumEC_NO_CALIBRATE;   //调试阶段,每次都重新校准.
#else
    s_ecompass_status = p_mag_calib->is_calibrated == true ? enumEC_CALIBRATE_SUCCESS
                                                           : enumEC_NO_CALIBRATE;
#endif
    BASIC_APP_LOG_I("s_ecompass_status :%d @%d", s_ecompass_status, __LINE__);
    optional_config_t config = {.sampling_rate = 50};
    int ret = qw_dataserver_subscribe_id(DATA_ID_RAW_MAG, compass_recv_mag,
                                      &config);
    if (ret != 0)
    {
        BASIC_APP_LOG_I("%s sub %s error ret:%d", __func__, CONFIG_QW_SENSOR_NAME_MAG,
                        ret);
    }
    config.sampling_rate = 26;
    ret = qw_dataserver_subscribe_id(DATA_ID_RAW_ACC, compass_recv_acc, &config);
    if (ret != 0)
    {
        BASIC_APP_LOG_I("%s sub %s error ret:%d", __func__, CONFIG_QW_SENSOR_NAME_ACC,
                        ret);
    }

    // 如果启用了传感器融合，注册陀螺仪回调
    if (s_fusion_enabled)
    {
        ecompass_register_gyro_callback();
    }

    ecompass_listener = listener;
    is_running = true;
}

/************************************************************************
 *@function:void ecompass_calibrate_stop(void)
 *@brief:结束地磁算法
 *************************************************************************/
void ecompass_calibrate_stop(void)
{
    if (!is_running)
        return;
    is_running = false;
    BASIC_APP_LOG_I("%s %d", __func__, __LINE__);
    int ret = qw_dataserver_unsubscribe_id(DATA_ID_RAW_MAG, compass_recv_mag);
    if (ret != 0)
    {
        BASIC_APP_LOG_I("%s unsubscribe %s error %d", __func__, CONFIG_QW_SENSOR_NAME_MAG,
                        ret);
    }
    ret = qw_dataserver_unsubscribe_id(DATA_ID_RAW_ACC, compass_recv_acc);
    if (ret != 0)
    {
        BASIC_APP_LOG_I("%s unsubscribe %s error %d", __func__, CONFIG_QW_SENSOR_NAME_ACC,
                        ret);
    }

    // 取消订阅陀螺仪数据
    if (s_fusion_enabled)
    {
        ret = qw_dataserver_unsubscribe_id(DATA_ID_RAW_GYRO, compass_recv_gyro);
        if (ret != 0)
        {
            BASIC_APP_LOG_I("%s unsubscribe gyro error %d", __func__, ret);
        }
        else
        {
            BASIC_APP_LOG_I("Gyro callback unsubscribed successfully");
        }
    }
    s_stop_calibrate = true;
    s_start_calibrate = false;
    ecompass_listener = NULL;
    BASIC_APP_LOG_I("stop recv_acc:%d, recv_mag:%d, feed_cnt:%d", s_acc_data_cnt,
                    s_mag_data_cnt, s_feed_cnt);
    ecompass_algo_uninit();
#ifdef COMPASS_ALGO_CAPTURE
    close_capture_file();
#endif
}

ecompass_status_t ecompass_status_get(void)
{
    //返回电子罗盘校准状态
    s_ecompass_status = get_is_calibrated() == true ? enumEC_CALIBRATE_SUCCESS : enumEC_CALIBRATE_FAILED;
    return s_ecompass_status;
}

/************************************************************************
 *@function:void reset_ecompass_status(void)
 *@brief:重置指南针算法校准状态,每次进入指南针页面都需要重新校准.
*************************************************************************/
void reset_ecompass_status(void)
{
    BASIC_APP_LOG_W("reset ecompass");
    set_is_calibrated(false);
    s_ecompass_status = enumEC_NO_CALIBRATE;
}

// uint8_t ecompass_fail_get(void)
// {
//     return s_ecompass_fail;
// }


/************************************************************************
 *@function:static bool compass_calib_process(sensor_data_t* mag,
                    sensor_data_t* accel, void** alg_res_data)
 *@brief:指南针校准处理函数
 *@param:mag:地磁数据,accel:ACC数据,mag和acc可以有一个为空
 *@return:算法逻辑运行正常返回true,否则返回false.
*************************************************************************/
static bool compass_calib_process(sensor_data_t *mag, sensor_data_t *accel,
                                  void **alg_res_data)
{
    static uint8_t s_in_calibrating = false;
    bool ret = true;
    if (s_start_calibrate == true)
    {
        //正在校准中，不接受再次校准
        if (s_ecompass_status == enumEC_IN_CALIBRATING)
        {
            s_start_calibrate = false;
            BASIC_APP_LOG_E("already calibrate!");
            return false;
        }

        //分配资源
        if (mag_calib_tmp_results_init() == -1)
        {
            s_ecompass_status = enumEC_CALIBRATE_FAILED;
            BASIC_APP_LOG_I("s_ecompass_status :%d @%d", s_ecompass_status, __LINE__);

            return false;
        }

        //开始校准
        s_start_calibrate = false;
        s_ecompass_status = enumEC_IN_CALIBRATING;
        BASIC_APP_LOG_I("s_ecompass_status :%d @%d", s_ecompass_status, __LINE__);

        s_in_calibrating = true;
        memset(p_mag_calib, 0, sizeof(cfg_mag_calib_t));
        s_yaw_angle = 0x7fff;
    }

    if (s_stop_calibrate == true)
    {
        if (s_ecompass_status != enumEC_IN_CALIBRATING)
        {
            s_stop_calibrate = false;
            BASIC_APP_LOG_E("compass stop at not calibrating time!");
            return false;
        }

        //停止校准
        s_stop_calibrate = false;
        s_ecompass_status = enumEC_CALIBRATE_FAILED;
        s_in_calibrating = false;
        BASIC_APP_LOG_I("s_ecompass_status :%d @%d", s_ecompass_status, __LINE__);
        mag_calib_tmp_results_clean();
    }
    //校准中
    if (p_mag_calib->is_calibrated == false && s_in_calibrating == true)
    {
        //检查是否获得了足够数量的特征点
        if (mag_eigen_point_check(sp_mag_eigen_points, &s_mag) == true)
        {
            //初始化椭球拟合矩阵
            ellipsoid_fit_mat_init(sp_ellipsoid_fit_mat, sp_ellipsoid_fit_mat_value);
            //计算椭球拟合系数
            int coff_result = 0;
            if ((coff_result = ellipsoid_fit_coff_calc(sp_mag_eigen_points,
                                                       sp_ellipsoid_fit_mat))
                == 0)
            {
                //初始化椭球校准矩阵
                ellipsoid_calib_mat_value_init(sp_ellipsoid_calib_mat_value);
                ellipsoid_calib_mat_init(sp_ellipsoid_calib_mat,
                                         sp_ellipsoid_calib_mat_value);
                //初始化地磁校准矩阵
                mag_calib_mat_value_init(p_mag_calib);
                mag_calib_mat_init(sp_mag_calib_mat, p_mag_calib);
                //计算椭球校准矩阵
                if (ellipsoid_calib_mat_calc(sp_ellipsoid_fit_mat, sp_ellipsoid_calib_mat,
                                             sp_mag_coff_mat_decomposition,
                                             sp_mag_calib_mat)
                    == true)
                {
                    p_mag_calib->type = matrix_type_check(&sp_ellipsoid_fit_mat->KTKIKTY);
                    if (p_mag_calib->type != enumMATRIX_OTHERS)
                    {
                        p_mag_calib->is_calibrated = true;
                        s_ecompass_status = enumEC_CALIBRATE_SUCCESS;
                        BASIC_APP_LOG_I("s_ecompass_status :%d @%d", s_ecompass_status,
                                        __LINE__);
                        mag_calib_tmp_results_clean();
                        //cfg_mark_update(enum_cfg_mag);
                        set_is_calibrated(true);
                    }
                    else
                    {
                        p_mag_calib->is_calibrated = false;
                        BASIC_APP_LOG_E("p_mag_calib->type error");
                        s_ecompass_status = enumEC_CALIBRATE_FAILED;
                        ret = false;
                        BASIC_APP_LOG_I("s_ecompass_status :%d @%d", s_ecompass_status,
                                        __LINE__);
                        s_in_calibrating = false;
                        mag_calib_tmp_results_clean();
                        //cfg_mark_update(enum_cfg_mag);
                        set_is_calibrated(false);
                    }
                }
                else
                {
                    //椭球校准矩阵计算失败
                    BASIC_APP_LOG_E("ellipsoid_calib_mat_calc error");
                    s_ecompass_status = enumEC_CALIBRATE_FAILED;
                    s_in_calibrating = false;
                    ret = false;
                    mag_calib_tmp_results_clean();
                    //cfg_mark_update(enum_cfg_mag);
                    set_is_calibrated(false);
                    BASIC_APP_LOG_I("s_ecompass_status :%d @%d", s_ecompass_status,
                                    __LINE__);
                }
            }
            else
            {
                //椭球拟合失败
                BASIC_APP_LOG_E("ellipsoid_fit_coff_calc error %d ret:%d", __LINE__,
                                coff_result);
                s_ecompass_status = enumEC_CALIBRATE_FAILED;
                s_in_calibrating = false;
                ret = false;
                mag_calib_tmp_results_clean();
                //cfg_mark_update(enum_cfg_mag);
                set_is_calibrated(false);
                BASIC_APP_LOG_I("s_ecompass_status :%d @%d", s_ecompass_status, __LINE__);
            }
        }
        else
        {
            //尚未获得足够数量特征点
            s_ecompass_status = enumEC_IN_CALIBRATING;
            //BASIC_APP_LOG_I("s_ecompass_status :%d @%d", s_ecompass_status, __LINE__);
            ret = false;
        }
    }
    return ret;
}

/************************************************************************
 *@function:static bool compass_course_process(sensor_data_t* mag, sensor_data_t* accel, void** alg_res_data)
 *@brief:航向角计算
 *@param:mag:地磁数据,accel:ACC数据,mag和acc可以有一个为空
 *@return:算法逻辑运行正常返回true,否则返回false.
*************************************************************************/
static bool compass_course_process(sensor_data_t *mag, sensor_data_t *accel,
                                   void **alg_res_data)
{
#ifndef COMPASS_ALGO_SKIP_CALIBRATION
    if (p_mag_calib->is_calibrated)
    {
        //传感器数据预处理
        //  sensor_data_preprocessing(&s_mag, &s_acc);
        //校正地磁数据
        mag_calib(p_mag_calib, &s_mag);
#else
    if (1)
    {
        //传感器数据预处理
        //  sensor_data_preprocessing(&s_mag, &s_acc);
#endif
        plane_vector_t vec = {0, 0};
        //根据地磁投影及其分量，计算航向角
        if (mag_plane_projection_calc(&s_mag, &s_acc, &vec) == true)
        {
            mag_moving_avg_filter(&vec);
            uint16_t mag_yaw = yaw_calc(&vec);

            if (p_mag_calib->type == enumMATRIX_POSITIVE_DEFINITE)
            {
                //Do nothing
            }
            else
            {
                mag_yaw = (mag_yaw + 180) % 360;
            }

            // 如果启用了传感器融合，使用融合算法
            if (s_fusion_enabled)
            {
                float fused_yaw = compass_fusion_update_mag((float)mag_yaw);
                s_yaw_angle = (uint16_t)(fused_yaw + 0.5f); // 四舍五入

                BASIC_APP_LOG_D("Mag: %d, Fused: %.1f, Final: %d", mag_yaw, fused_yaw, s_yaw_angle);
            }
            else
            {
                s_yaw_angle = mag_yaw;
            }

            if (g_p_mag_course_data != NULL)
            {
                g_p_mag_course_data->value = s_yaw_angle;
            }
        }
        else
        {
            s_yaw_angle = 0x7fff;
            g_p_mag_course_data->value = s_yaw_angle;
            invaild_sports_data(g_p_mag_course_data);
        }
    }
    return true;
}

/************************************************************************
 *@function:static bool alg_compass_process(sensor_data_t* mag, sensor_data_t* accel,
                                void** alg_res_data)
 *@brief:罗盘算法处理函数
 *@param:mag:地磁数据,accel:ACC数据,mag和acc可以有一个为空
 *@return:算法逻辑运行正常返回true,否则返回false.
*************************************************************************/
static bool alg_compass_process(sensor_data_t *mag, sensor_data_t *accel,
                                void **alg_res_data)
{
    bool ret = false;
    //如果校准成功,则直接运行航向角算法,否则执行校准流程.
    if (enumEC_IN_CALIBRATING == s_ecompass_status
        || enumEC_NO_CALIBRATE == s_ecompass_status)
    {
        ret = compass_calib_process(mag, accel, alg_res_data);
        if (!ret)
        {
            //BASIC_APP_LOG_E("compass_calib_process wrong!");
        }
    }
    else if (enumEC_CALIBRATE_SUCCESS == s_ecompass_status)
    {
        /*航向角*/
        ret = compass_course_process(mag, accel, alg_res_data);
    }
    if (ecompass_listener)
    {
        ecompass_listener->result_cb(s_ecompass_status, s_yaw_angle);
    }
    return ret;
}
#endif   // IGS_DEV

/************************************************************************
 *@function:void ecompass_fusion_enable(bool enable)
 *@brief:启用或禁用传感器融合算法
 *@param:enable - true启用，false禁用
*************************************************************************/
void ecompass_fusion_enable(bool enable)
{
    s_fusion_enabled = enable;
    if (enable)
    {
        compass_fusion_get_default_config(&s_fusion_config);
        if (compass_fusion_init(&s_fusion_config) != 0)
        {
            BASIC_APP_LOG_E("Failed to initialize compass fusion");
            s_fusion_enabled = false;
        }
        else
        {
            BASIC_APP_LOG_I("Compass fusion enabled");
        }
    }
    else
    {
        compass_fusion_deinit();
        BASIC_APP_LOG_I("Compass fusion disabled");
    }
}

/************************************************************************
 *@function:bool ecompass_fusion_is_enabled(void)
 *@brief:获取传感器融合算法启用状态
 *@return:true已启用，false已禁用
*************************************************************************/
bool ecompass_fusion_is_enabled(void)
{
    return s_fusion_enabled;
}

/************************************************************************
 *@function:void ecompass_fusion_reset(void)
 *@brief:重置传感器融合算法状态
*************************************************************************/
void ecompass_fusion_reset(void)
{
    if (s_fusion_enabled)
    {
        compass_fusion_reset();
        BASIC_APP_LOG_I("Compass fusion state reset");
    }
}

/************************************************************************
 *@function:void ecompass_register_gyro_callback(void)
 *@brief:注册陀螺仪数据回调
*************************************************************************/
void ecompass_register_gyro_callback(void)
{
#ifdef IGS_DEV
    if (!s_fusion_enabled)
    {
        BASIC_APP_LOG_W("Fusion not enabled, skip gyro callback registration");
        return;
    }

    // 配置陀螺仪采样率，与加速度计保持一致
    optional_config_t config = {.sampling_rate = 26};
    int ret = qw_dataserver_subscribe_id(DATA_ID_RAW_GYRO, compass_recv_gyro, &config);
    if (ret != 0)
    {
        BASIC_APP_LOG_E("Subscribe gyro data error, ret: %d", ret);
        s_fusion_enabled = false;  // 订阅失败则禁用融合算法
    }
    else
    {
        BASIC_APP_LOG_I("Gyro callback registered successfully, sampling rate: %d Hz", config.sampling_rate);
    }
#endif
}

/****************************************************************************
*融合算法内部函数实现
****************************************************************************/

/************************************************************************
 *@function: normalize_angle
 *@brief: 角度归一化到[0, 360)范围
 *@param: angle - 输入角度
 *@return: 归一化后的角度
*************************************************************************/
static float normalize_angle(float angle)
{
    angle = fmodf(angle, 360.0f);
    if (angle < 0.0f)
    {
        angle += 360.0f;
    }
    return angle;
}

/************************************************************************
 *@function: angle_difference
 *@brief: 计算两个角度之间的最小差值
 *@param: angle1 - 角度1
 *@param: angle2 - 角度2
 *@return: 角度差值 (-180, 180]
*************************************************************************/
static float angle_difference(float angle1, float angle2)
{
    float diff = fmodf(angle1 - angle2, 360.0f);
    if (diff > 180.0f)
    {
        diff -= 360.0f;
    }
    else if (diff <= -180.0f)
    {
        diff += 360.0f;
    }
    return diff;
}

/************************************************************************
 *@function: complementary_filter
 *@brief: 互补滤波器
 *@param: gyro_angle - 陀螺仪角度
 *@param: mag_angle - 地磁角度
 *@param: alpha - 滤波系数
 *@return: 融合后的角度
*************************************************************************/
static float complementary_filter(float gyro_angle, float mag_angle, float alpha)
{
    float diff = angle_difference(mag_angle, gyro_angle);
    return normalize_angle(gyro_angle + alpha * diff);
}

/************************************************************************
 *@function: update_gyro_bias
 *@brief: 更新陀螺仪偏置
 *@param: gyro_z - Z轴陀螺仪数据
 *@return: 无
*************************************************************************/
static void update_gyro_bias(float gyro_z)
{
    if (s_fusion_state.sample_count < COMPASS_FUSION_BIAS_SAMPLES) {
        s_fusion_state.bias_sum += gyro_z;
        s_fusion_state.sample_count++;

        // // 每20个样本输出一次进度
        // if (s_fusion_state.sample_count % 20 == 0)
        // {
        //     rt_kprintf("Bias estimation: %d/%d, current_gyro=%.6f, avg_bias=%.6f\n",
        //               s_fusion_state.sample_count, COMPASS_FUSION_BIAS_SAMPLES,
        //               gyro_z, s_fusion_state.bias_sum / s_fusion_state.sample_count);
        // }

        if (s_fusion_state.sample_count == COMPASS_FUSION_BIAS_SAMPLES)
        {
            s_fusion_state.gyro_bias_z = s_fusion_state.bias_sum / COMPASS_FUSION_BIAS_SAMPLES;
            // rt_kprintf("Gyro bias estimation completed: %.6f dps\n", s_fusion_state.gyro_bias_z);
        }
    }
    else
    {
        // 使用更严格的静止阈值进行偏置更新
        if (fabsf(gyro_z) < 0.2f)
        {  // 更严格的静止阈值
            float old_bias = s_fusion_state.gyro_bias_z;
            s_fusion_state.gyro_bias_z = s_fusion_state.gyro_bias_z * (1.0f - s_fusion_config.bias_alpha) +
                                        gyro_z * s_fusion_config.bias_alpha;

            // // 偏置变化较大时输出日志
            // if (fabsf(s_fusion_state.gyro_bias_z - old_bias) > 0.001f)
            // {
            //     rt_kprintf("Bias updated: %.6f -> %.6f (gyro=%.6f)\n",
            //               old_bias, s_fusion_state.gyro_bias_z, gyro_z);
            // }
        }
    }
}

/****************************************************************************
*融合算法外部函数实现
****************************************************************************/

/************************************************************************
 *@function: compass_fusion_get_default_config
 *@brief: 获取默认配置参数
 *@param: config - 配置参数输出指针
 *@return: 无
*************************************************************************/
void compass_fusion_get_default_config(compass_fusion_config_t *config)
{
    if (config == NULL)
    {
        BASIC_APP_LOG_E("Config pointer is NULL");
        return;
    }

    config->gyro_weight = 0.98f;           // 陀螺仪权重较高，提供短期精度
    config->mag_weight = 0.02f;            // 地磁权重较低，提供长期稳定性
    config->bias_alpha = 0.005f;           // 偏置估计滤波系数
    config->fusion_alpha = 0.08f;          // 融合滤波系数
    config->gyro_threshold = 1.0f;         // 陀螺仪静止阈值 (dps)
    config->bias_update_interval = 1000;   // 偏置更新间隔 (ms)
}

/************************************************************************
 *@function: compass_fusion_init
 *@brief: 初始化传感器融合指南针算法
 *@param: config - 配置参数指针
 *@return: 成功返回0，失败返回负值
*************************************************************************/
int compass_fusion_init(const compass_fusion_config_t *config)
{
    if (config == NULL)
    {
        BASIC_APP_LOG_E("Config pointer is NULL");
        return -1;
    }

    // 复制配置参数
    memcpy(&s_fusion_config, config, sizeof(compass_fusion_config_t));

    // 初始化状态
    memset(&s_fusion_state, 0, sizeof(compass_fusion_state_t));

    s_fusion_state.initialized = false;
    s_fusion_state.gyro_ready = false;  // 陀螺仪未准备就绪
    s_fusion_state.stabilize_start_time = get_boot_msec();  // 记录稳定期开始时间
    s_fusion_initialized = true;

    BASIC_APP_LOG_I("Compass fusion init: gyro stabilization period started (3s)");;

    BASIC_APP_LOG_I("Compass fusion algorithm initialized");
    return 0;
}

/************************************************************************
 *@function: compass_fusion_deinit
 *@brief: 反初始化传感器融合指南针算法
 *@param: 无
 *@return: 无
*************************************************************************/
void compass_fusion_deinit(void)
{
    s_fusion_initialized = false;
    memset(&s_fusion_state, 0, sizeof(compass_fusion_state_t));
    BASIC_APP_LOG_I("Compass fusion algorithm deinitialized");
}

/************************************************************************
 *@function: compass_fusion_update_gyro
 *@brief: 更新陀螺仪数据
 *@param: gyro - 陀螺仪数据指针
 *@return: 成功返回0，失败返回负值
*************************************************************************/
int compass_fusion_update_gyro(const gyro_data_t *gyro)
{
    if (!s_fusion_initialized || gyro == NULL)
    {
        BASIC_APP_LOG_E("Fusion not initialized or gyro data is NULL");
        return -1;
    }

    float gyro_z_dps = (float)gyro->z * COMPASS_FUSION_GYRO_SCALE;

    // 检查陀螺仪数据是否异常
    static uint32_t data_check_count = 0;
    // if (++data_check_count % 50 == 0)
    // {  // 每50次检查一次
    //     rt_kprintf("Gyro data check: raw=%.6f, scale=%.6f, scaled=%.6f dps\n",
    //               gyro->z, COMPASS_FUSION_GYRO_SCALE, gyro_z_dps);
    // }

    // 检查陀螺仪稳定期
    uint32_t current_time = get_boot_msec();
    if (!s_fusion_state.gyro_ready)
    {
        uint32_t stabilize_duration = current_time - s_fusion_state.stabilize_start_time;

        if (stabilize_duration >= COMPASS_FUSION_STABILIZE_TIME)
        {
            s_fusion_state.gyro_ready = true;
            // rt_kprintf("Gyro stabilization completed! Ready for integration after %dms\n", stabilize_duration);
            // rt_kprintf("Final bias: %.6f dps, samples: %d\n", s_fusion_state.gyro_bias_z, s_fusion_state.sample_count);
        }
        else
        {
            // 稳定期内，只更新偏置，不进行积分
            update_gyro_bias(gyro_z_dps);
            s_fusion_state.last_timestamp = gyro->timestamp;

            // // 每秒输出一次稳定进度
            // static uint32_t stabilize_log_count = 0;
            // if (++stabilize_log_count % 26 == 0) // 26Hz采样率，每秒输出一次
            // {
            //     rt_kprintf("Gyro stabilizing... %dms remaining\n", COMPASS_FUSION_STABILIZE_TIME - stabilize_duration);
            // }
            return 0;
        }
    }

    // 更新陀螺仪偏置
    update_gyro_bias(gyro_z_dps);

    // 如果有上次时间戳，计算角度积分
    if (s_fusion_state.last_timestamp > 0)
    {
        float dt = (float)(gyro->timestamp - s_fusion_state.last_timestamp) / 1000000.0f; // 微秒转换为秒

        if (dt > 0.001f && dt < 0.1f)
        { // 更严格的时间间隔检查 (1ms - 100ms)
            // 去除偏置后的角速度
            float corrected_gyro_z = gyro_z_dps - s_fusion_state.gyro_bias_z;

            // 积分得到角度变化
            float angle_delta = corrected_gyro_z * dt;

            // // 添加调试日志和保护
            // static uint32_t debug_count = 0;
            // if (++debug_count % 10 == 0)
            // {  // 每10次输出一次
            //     rt_kprintf("Gyro: raw=%.6f, bias=%.3f, corrected=%.3f, dt=%.6f, delta=%.3f\n",
            //               gyro->z, s_fusion_state.gyro_bias_z, corrected_gyro_z, dt, angle_delta);
            // }

            // 限制单次角度变化，防止异常跳跃
            if (fabsf(angle_delta) > 2.0f)
            {
                // rt_kprintf("Large delta detected: %.3f°, raw_gyro=%.6f, dt=%.6f, limited to 2°\n",
                //           angle_delta, gyro->z, dt);
                angle_delta = angle_delta > 0 ? 2.0f : -2.0f;
            }

            s_fusion_state.yaw_gyro = normalize_angle(s_fusion_state.yaw_gyro + angle_delta);
        }
        // else
        // {
        //     rt_kprintf("Abnormal dt: %.6f, skip integration\n", dt);
        // }
    }

    s_fusion_state.last_timestamp = gyro->timestamp;

    return 0;
}

/************************************************************************
 *@function: compass_fusion_update_mag
 *@brief: 更新地磁数据并执行融合
 *@param: mag_angle - 地磁计算的偏航角 (度)
 *@return: 融合后的偏航角 (度)
*************************************************************************/
float compass_fusion_update_mag(float mag_angle)
{
    if (!s_fusion_initialized)
    {
        BASIC_APP_LOG_E("Fusion not initialized");
        return mag_angle;
    }

    s_fusion_state.yaw_mag = normalize_angle(mag_angle);

    if (!s_fusion_state.initialized)
    {
        // 首次初始化，使用地磁角度
        s_fusion_state.yaw_gyro = s_fusion_state.yaw_mag;
        s_fusion_state.yaw_fused = s_fusion_state.yaw_mag;
        s_fusion_state.initialized = true;
        BASIC_APP_LOG_I("Fusion initialized with mag angle: %.1f", mag_angle);
    }
    else
    {
        // 执行互补滤波融合
        s_fusion_state.yaw_fused = complementary_filter(
            s_fusion_state.yaw_gyro,
            s_fusion_state.yaw_mag,
            s_fusion_config.fusion_alpha
        );

        // 更新陀螺仪角度，减少累积误差
        s_fusion_state.yaw_gyro = s_fusion_state.yaw_fused;
    }

    return s_fusion_state.yaw_fused;
}

/************************************************************************
 *@function: compass_fusion_reset
 *@brief: 重置融合算法状态
 *@param: 无
 *@return: 无
*************************************************************************/
void compass_fusion_reset(void)
{
    if (!s_fusion_initialized)
    {
        return;
    }

    s_fusion_state.initialized = false;
    s_fusion_state.yaw_gyro = 0.0f;
    s_fusion_state.yaw_mag = 0.0f;
    s_fusion_state.yaw_fused = 0.0f;
    s_fusion_state.gyro_bias_z = 0.0f;
    s_fusion_state.last_timestamp = 0;
    s_fusion_state.sample_count = 0;
    s_fusion_state.bias_sum = 0.0f;

    // 重置稳定期状态
    s_fusion_state.gyro_ready = false;
    s_fusion_state.stabilize_start_time = get_boot_msec();

    BASIC_APP_LOG_I("Compass fusion state reset");
}
