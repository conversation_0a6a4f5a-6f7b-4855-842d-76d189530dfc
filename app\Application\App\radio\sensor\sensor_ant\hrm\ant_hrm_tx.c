/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ant_hrm_tx.h
@Time    :   2024/12/24 9:22:41
<AUTHOR>   txy
*
**************************************************************************/

#include "ant_hrm_tx.h"
#include "nrf_log.h"
#include "nrf_sdh.h"
#include "nrf_sdh_ant.h"
#include "ant_hrm.h"
#include "app_error.h"
#include "ant_parameters.h"
#include "ant_interface.h"
#include "basictype.h"

#include "rtthread.h"
#include "sensor_ant_common.h"
#include "cfg_header_def.h"
#include "ser_user_data_def.h"
#include "qw_sensor_common_config.h"
// #include "hr_push.h"  // 心率推送功能已移至ant_ble_hr_send.c
#include "qw_timer.h"
#include "ble_ant_module.h"
#include "ant_ble_hr_send.h"  // 包含心率获取接口
#define INERATION_ANT_CYCLES HRM_MSG_PERIOD_4Hz ///< period of calculation [1/32678 s], defined in ANT device profile
#define INERATION_PERIOD (INERATION_ANT_CYCLES * 1024 / ANT_CLOCK_FREQUENCY) ///< integer part of calculation's period [1/1024 s]
#define INERATION_FRACTION (INERATION_ANT_CYCLES * 1024 % ANT_CLOCK_FREQUENCY) ///< fractional part of calculation's period [1/32678 s]

#define DEV_HRM_SENS_CHANNEL_CONFIG_DEF(NAME,                                     \
                                        CHANNEL_NUMBER,                           \
                                        TRANSMISSION_TYPE,                        \
                                        DEVICE_NUMBER,                            \
                                        NETWORK_NUMBER)                           \
    static ant_channel_config_t CONCAT_2(NAME, _channel_hrm_sens_config) = {      \
        .channel_number = (CHANNEL_NUMBER),                                       \
        .channel_type = HRM_SENS_CHANNEL_TYPE,                                    \
		.ext_assign = HRM_EXT_ASSIGN,                                             \
		.rf_freq = HRM_ANTPLUS_RF_FREQ,                                           \
		.transmission_type = (TRANSMISSION_TYPE),                                 \
		.device_type = HRM_DEVICE_TYPE,                                           \
		.device_number = (DEVICE_NUMBER),                                         \
		.channel_period = HRM_MSG_PERIOD_4Hz,                                     \
		.network_number = (NETWORK_NUMBER),                                       \
	}

// 模拟心率相关定义已移至ant_ble_hr_send.c
//--------------------------------------变量定义-------------------------------------------//
static ant_hrm_profile_t s_ant_hrm;
static uint32_t m_time_since_last_hb;
static uint64_t m_fraction_since_last_hb;

static bool is_hr_open = false; // 心率订阅打开标记（后续ble心率推送完成时，统一变量）

// 模拟心率相关变量已移至ant_ble_hr_send.c
//--------------------------------------函数定义-------------------------------------------//
static void ant_hrm_evt_handler(ant_hrm_profile_t *p_profile, ant_hrm_evt_t event);
static void ant_hrm_tx_evt_handler(ant_evt_t * p_ant_evt, void * p_context);

DEV_HRM_SENS_CHANNEL_CONFIG_DEF(s_ant_hrm, INVALID_CHANNEL_NUMBER, CHAN_ID_TX_TRANS_TYPE, HRM_TX_CHAN_ID_DEV_NUM, ANTPLUS_NETWORK_NUM);
HRM_SENS_PROFILE_CONFIG_DEF(s_ant_hrm, true, ANT_HRM_PAGE_0, ant_hrm_evt_handler);
NRF_SDH_ANT_OBSERVER(m_hrm_tx_ant_observer, ANT_HRM_ANT_OBSERVER_PRIO, ant_hrm_tx_evt_handler, &s_ant_hrm);
/************************************************************************
 *@function:static void ant_heart_rate_get(void);
 *@brief:获取心率值
 *@param:null
 *@return:null
*************************************************************************/
static void ant_heart_rate_get(void)
{
    static uint32_t current_hb_pulse_interval = 0;
    // 从ant_ble_hr_send.c获取当前心率值
    uint8_t l_hr_rate = get_push_hr_value();
    if (l_hr_rate == 0)
    {
        s_ant_hrm.HRM_PROFILE_computed_heart_rate = (uint8_t) l_hr_rate;
    }
    else
    {
        // @note: This implementation assumes that the current instantaneous heart can
        // vary and this function is called with static frequency. value and the heart
        // rate pulse interval is derived from it. The computation is based on 60
        // seconds in a minute and the used time base is 1/1024 seconds.
        current_hb_pulse_interval = 60 * 1024 / l_hr_rate;
    }

    // time = 1min / hr_rate

    // update time from last hb detected
    m_time_since_last_hb += INERATION_PERIOD;

    // extended celculadion by fraction make calculating accurat in long time
    // perspective
    m_fraction_since_last_hb += INERATION_FRACTION;
    uint32_t add_period = m_fraction_since_last_hb / ANT_CLOCK_FREQUENCY;

    if (add_period > 0)
    {
        m_time_since_last_hb++;
        m_fraction_since_last_hb %= ANT_CLOCK_FREQUENCY;
    }

    // calc number of hb as will fill
    uint32_t new_beats = m_time_since_last_hb;
    uint32_t add_event_time = new_beats;
    if (current_hb_pulse_interval > 0)
    {
        new_beats /= current_hb_pulse_interval;
        add_event_time *= current_hb_pulse_interval;
    }

    if (new_beats > 0)
    {
        s_ant_hrm.HRM_PROFILE_computed_heart_rate = (uint8_t)l_hr_rate;
        // Current heart beat event time is the previous event time added with the
        // current heart rate pulse interval.
        uint32_t current_heart_beat_event_time = s_ant_hrm.HRM_PROFILE_beat_time + add_event_time;

        // Set current event time. <- B<4,5> <-
        s_ant_hrm.HRM_PROFILE_beat_time = current_heart_beat_event_time;

        // Set previous event time. p4.B<2,3> <- B<4,5>
        s_ant_hrm.HRM_PROFILE_prev_beat = s_ant_hrm.HRM_PROFILE_beat_time - current_hb_pulse_interval;

        // Event count. B<6>
        s_ant_hrm.HRM_PROFILE_beat_count += new_beats;

        m_time_since_last_hb -= add_event_time;
    }
}

static void ant_hrm_evt_handler(ant_hrm_profile_t *p_profile, ant_hrm_evt_t event)
{
    // rt_kprintf("ant_hrm_tx_evt_handler channel_number = %d\n", p_profile->channel_number);
    switch (event)
    {
        case ANT_HRM_PAGE_0_UPDATED:
            /* fall through */
        case ANT_HRM_PAGE_1_UPDATED:
            /* fall through */
        case ANT_HRM_PAGE_2_UPDATED:
            /* fall through */
        case ANT_HRM_PAGE_3_UPDATED:
            /* fall through */
        case ANT_HRM_PAGE_4_UPDATED:
        {
            ant_heart_rate_get();
        }
        break;
        default:
            break;
    }
}

static void ant_hrm_tx_evt_handler(ant_evt_t * p_ant_evt, void * p_context)
{
    ant_hrm_profile_t * p_profile = (ant_hrm_profile_t *)p_context;
    ret_code_t err_code = NRF_SUCCESS;
    extern void ant_message_send(ant_hrm_profile_t * p_profile);
    if (p_ant_evt->channel == p_profile->channel_number)
    {
        switch (p_ant_evt->event)
        {
            case EVENT_TX:
                ant_message_send(p_profile);
                break;
            case EVENT_CHANNEL_CLOSED:
                err_code = sd_ant_channel_unassign(p_profile->channel_number);
                APP_ERROR_CHECK(err_code);
                sensor_ant_channel_num_unassign(SENSOR_TYPE_HR_TX);
                break;
            default:
                break;
        }
    }
}


/************************************************************************
 *@function:void ant_hrm_tx_profile_setup(void);
 *@brief:ANT心率主机配置
 *@param:null
 *@return:null
*************************************************************************/
void ant_hrm_tx_profile_setup(void)
{
    ret_code_t err_code;
    // const uint16_t device_info_num = (uint16_t)ser_user_settings_read_uuid();
    extern uint64_t device_info_num;
    (HRM_SENS_CHANNEL_CONFIG(s_ant_hrm))->device_number = (uint16_t)device_info_num;
    (HRM_SENS_CHANNEL_CONFIG(s_ant_hrm))->channel_number = sensor_ant_channel_num_assign(SENSOR_TYPE_HR_TX);
    ANT_HRM_TX_LOG_I("HRM TX channel_number = %d\n", (HRM_SENS_CHANNEL_CONFIG(s_ant_hrm))->channel_number);
    err_code = ant_hrm_sens_init(&s_ant_hrm,
                HRM_SENS_CHANNEL_CONFIG(s_ant_hrm),
                HRM_SENS_PROFILE_CONFIG(s_ant_hrm));
    // if (err_code != NRF_SUCCESS)
    // {
    //     rt_kprintf("ant_hrm_sens_init err_code = %d\n", err_code);
    // }
    APP_ERROR_CHECK(err_code);

    err_code = sd_ant_channel_radio_tx_power_set(s_ant_hrm.channel_number,
                                                RADIO_TX_POWER_LVL_4, 0);
    APP_ERROR_CHECK(err_code);

    s_ant_hrm.HRM_PROFILE_manuf_id = get_manufacturer_id();
    s_ant_hrm.HRM_PROFILE_serial_num = device_info_num;
    ANT_HRM_TX_LOG_I("HRM TX serial num: %d!!!!!!!\n", s_ant_hrm.HRM_PROFILE_serial_num);
    s_ant_hrm.HRM_PROFILE_hw_version = get_hardware_version();
    s_ant_hrm.HRM_PROFILE_sw_version = get_major_app_version();
    s_ant_hrm.HRM_PROFILE_model_num  = HRM_TX_MODEL_NUMBER;
    // err_code = ant_hrm_sens_open(&s_ant_hrm);
    // APP_ERROR_CHECK(err_code);
}

/************************************************************************
 *@function:void ant_hrm_tx_open(void);
 *@brief:开启心率推送
 *@param:null
 *@return:null
*************************************************************************/
void ant_hrm_tx_open(void)
{
    ret_code_t err_code;
    err_code = ant_hrm_sens_open(&s_ant_hrm);
    APP_ERROR_CHECK(err_code);
}

/************************************************************************
 *@function:void ant_hrm_tx_close(void);
 *@brief:关闭心率推送
 *@param:null
 *@return:null
*************************************************************************/
void ant_hrm_tx_close(void)
{
    ret_code_t err_code;
    err_code = sd_ant_channel_close(s_ant_hrm.channel_number);
    ANT_HRM_TX_LOG_I("hr tx close err_code = %d\n", err_code);
    if (!nrf_is_ready_stop(NRF_USED_TYPE_HR_SEND))
    {
        APP_ERROR_CHECK(err_code);
    }
    // err_code = sd_ant_channel_unassign(s_ant_hrm.channel_number);
    // if (err_code != NRF_SUCCESS)
    // {
    //     rt_kprintf("sd_ant_channel_unassign err_code = %d, channel_number = %d\n", err_code, s_ant_hrm.channel_number);
    // }
    // APP_ERROR_CHECK(err_code);
    // sensor_ant_channel_num_unassign(SENSOR_TYPE_HR_TX);
}



// 模拟心率定时器回调函数已移至ant_ble_hr_send.c

/************************************************************************
 *@function:uint32_t ant_hr_open(void);
 *@brief:开启并订阅ppg心率数据
 *@param: null
 *@return: 0 - 成功，-1 - 订阅失败
*************************************************************************/
uint32_t ant_hr_open(void)
{
    if (is_hr_open)
    {
        ANT_HRM_TX_LOG_I("HRM TX is already open!\n");
        return 0;
    }
    // 心率订阅逻辑已移至ant_ble_hr_send.c的enable_ble_send_hr函数中
    // ANT模块只负责ANT协议的数据传输，不再直接订阅心率数据
    is_hr_open = true;
    return 0;
}

/************************************************************************
 *@function:uint32_t ant_hr_close(void);
 *@brief:关闭并取消订阅ppg心率数据
 *@param: 0 - 成功，-1 - 订阅失败
 *@return:null
*************************************************************************/
uint32_t ant_hr_close(void)
{
    // 心率取消订阅逻辑已移至ant_ble_hr_send.c的enable_ble_send_hr函数中
    // ANT模块只负责ANT协议的数据传输
    if (!is_hr_open)
    {
        ANT_HRM_TX_LOG_I("HRM TX is already close!\n");
        return 0;
    }
    is_hr_open = false;
    return 0;
}

// 模拟心率相关函数已移至ant_ble_hr_send.c

int ant_hrm_tx_test(int argc, char *argv[])
{
    if (argc < 2)
    {
        ANT_HRM_TX_LOG_I("argc err! Usage: %s [init|open|close|sim]", argv[0]);
        return 0;
    }

    if (strcmp(argv[1], "init") == 0)
    {
        ant_hrm_tx_profile_setup();
    }

    else if (strcmp(argv[1], "open") == 0)
    {
        ant_hr_open();
        ant_hrm_tx_open();
    }

    else if (strcmp(argv[1], "close") == 0)
    {
        ant_hr_close();
        ant_hrm_tx_close();
    }
    // sim相关测试命令已移至ant_ble_hr_send.c
    else
    {
        ANT_HRM_TX_LOG_I("Usage: %s [init|open|close]", argv[0]);
    }
    return 0;
}

FINSH_FUNCTION_EXPORT_ALIAS(ant_hrm_tx_test, __cmd_ant_hr, test for ant hrm tx);
