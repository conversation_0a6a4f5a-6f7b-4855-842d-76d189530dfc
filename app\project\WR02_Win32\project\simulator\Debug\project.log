﻿  dial_app_api.c
  dial_common_api.c
  dial_config_api.c
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_app_api.h(8,10): error C2085: “get_dial_cfg_theme_color_num_to_app”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_app_api.h(9,6): error C2085: “set_dial_cfg_color_inuse_index_to_app”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_app_api.h(10,10): error C2085: “get_dial_cfg_color_inuse_index_to_app”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_app_api.h(11,6): error C2085: “get_edit_color_type_to_app”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_app_api.h(12,10): error C2085: “get_dial_edit_type_num_to_app”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_app_api.h(13,6): error C2085: “get_edit_data_type_to_app”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_app_api.h(14,10): error C2085: “get_dial_data_num_to_app”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_app_api.h(15,6): error C2085: “get_using_data_type_to_app”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_app_api.h(16,6): error C2085: “set_dial_data_type_to_app”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(8,10): error C2085: “get_dial_them_color”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(274,19): error C2085: “__crt_bool”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(9,10): error C2085: “get_using_dial_index”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(274,19): error C2085: “__crt_bool”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(10,6): error C2085: “set_inuse_dial_index”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(362,27): error C2085: “_invalid_parameter”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(362,27): error C2085: “_invalid_parameter”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(11,10): error C2085: “get_dial_counts”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(371,27): error C2085: “_invalid_parameter_noinfo”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(12,10): error C2085: “get_cfg_current_using_dial_goodsid”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(371,27): error C2085: “_invalid_parameter_noinfo”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(372,44): error C2085: “_invalid_parameter_noinfo_noreturn”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(13,10): error C2085: “get_cfg_dial_goodsid_by_index”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(372,44): error C2085: “_invalid_parameter_noinfo_noreturn”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(375,23): error C2085: “_invoke_watson”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(14,13): error C2085: “get_cfg_dial_type”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(375,23): error C2085: “_invoke_watson”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(15,9): error C2085: “dial_cfg_is_exist”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(604,39): error C2085: “errno_t”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(604,39): error C2085: “errno_t”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(16,10): error C2085: “get_dial_edit_type”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(17,10): error C2085: “get_dial_edit_type_by_index”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(605,39): error C2085: “wint_t”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(605,39): error C2085: “wint_t”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(18,10): error C2085: “get_dial_cfg_theme_color_num”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(606,39): error C2085: “wctype_t”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(606,39): error C2085: “wctype_t”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(19,6): error C2085: “set_dial_cfg_color_inuse_index”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(607,39): error C2085: “__time32_t”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(607,39): error C2085: “__time32_t”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(20,10): error C2085: “get_dial_cfg_color_inuse_index”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(608,39): error C2085: “__time64_t”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(608,39): error C2085: “__time64_t”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(21,10): error C2085: “get_dial_edit_type_num”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(22,6): error C2085: “get_edit_data_type”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(615,3): error C2085: “__crt_locale_data_public”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(615,3): error C2085: “__crt_locale_data_public”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(23,10): error C2085: “get_dial_data_num”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(621,3): error C2085: “__crt_locale_pointers”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(621,3): error C2085: “__crt_locale_pointers”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(24,10): error C2085: “get_dial_data_type_by_index”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(623,9): error C2085: “__crt_locale_pointers”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(25,6): error C2085: “set_dial_data_type_by_index”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(623,9): error C2085: “__crt_locale_pointers”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(623,30): error C2143: 语法错误: 缺少“;”(在“*”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(623,30): error C2143: 语法错误: 缺少“;”(在“*”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_config_api.h(26,10): error C2085: “get_js_support_aod_flag”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(645,28): error C2061: 语法错误: 标识符“time_t”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(645,28): error C2061: 语法错误: 标识符“time_t”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(645,28): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt.h(645,28): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h(38,22): error C2143: 语法错误: 缺少“{”(在“__cdecl”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h(38,22): error C2143: 语法错误: 缺少“{”(在“__cdecl”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h(39,22): error C2143: 语法错误: 缺少“{”(在“__cdecl”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\stddef.h(39,22): error C2143: 语法错误: 缺少“{”(在“__cdecl”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.42.34433\include\excpt.h(28,3): error C2085: “EXCEPTION_DISPOSITION”: 不在形参表中
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.42.34433\include\excpt.h(38,5): error C2061: 语法错误: 标识符“EXCEPTION_DISPOSITION”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(36,28): error C2143: 语法错误: 缺少“{”(在“*”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(36,28): error C2143: 语法错误: 缺少“{”(在“*”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(67,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(67,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(67,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(67,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(67,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(67,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(67,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(67,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(68,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(68,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(68,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(68,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(68,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(68,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(68,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(68,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(69,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(69,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(69,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(69,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(69,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(69,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(69,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(70,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(69,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(70,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(70,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(70,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(70,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(70,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(70,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(71,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(70,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(71,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(71,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(71,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(71,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(71,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(71,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(74,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(71,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(74,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(74,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(74,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(74,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(74,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(74,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(76,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(74,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(76,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(76,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(76,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(76,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(76,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(76,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(77,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(76,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(77,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(77,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(77,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(77,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(77,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(77,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(77,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(78,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(78,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(78,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(78,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(78,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(78,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(78,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(78,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(79,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(79,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(79,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(79,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(79,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(79,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(79,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(79,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(80,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(80,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(80,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(80,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(80,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(80,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(80,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(80,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(81,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(81,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(81,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(81,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(81,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(81,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(81,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(81,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(82,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(82,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(82,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(82,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(82,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(82,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(82,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(82,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(83,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(83,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(83,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(83,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(83,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(83,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(83,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(83,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(84,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(84,60): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(84,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(84,60): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(84,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(84,60): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(84,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(84,62): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(86,62): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(86,62): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(86,62): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(86,62): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(86,62): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(86,62): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(86,64): error C2059: 语法错误:“,”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(86,64): error C2059: 语法错误:“,”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(86,92): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(86,92): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(87,62): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(87,62): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(87,62): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(87,62): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(87,62): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(87,62): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(87,64): error C2059: 语法错误:“,”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(87,64): error C2059: 语法错误:“,”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(87,92): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(88,62): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(87,92): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(88,62): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(88,62): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(88,62): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(88,62): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(88,64): error C2059: 语法错误:“,”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(88,62): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(88,64): error C2059: 语法错误:“,”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(88,92): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(88,92): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(89,62): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(89,62): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(89,62): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(89,62): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(89,62): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(89,62): error C2059: 语法错误:“;”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(89,64): error C2059: 语法错误:“,”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(89,64): error C2059: 语法错误:“,”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(89,92): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(89,92): error C2059: 语法错误:“)”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(90,62): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(90,62): error C2146: 语法错误: 缺少“)”(在标识符“_C”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(90,62): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(90,62): error C2061: 语法错误: 标识符“_C”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(90,62): error C1003: 错误计数超过 100；正在停止编译
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_config_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\ucrt\corecrt_wctype.h(90,62): error C1003: 错误计数超过 100；正在停止编译
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_app_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(1442,1): error C2143: 语法错误: 缺少“{”(在“__stdcall”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(1450,27): error C2143: 语法错误: 缺少“{”(在“*”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(6875,5): error C2061: 语法错误: 标识符“PEXCEPTION_ROUTINE”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(6881,1): error C2059: 语法错误:“}”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(12276,5): error C2061: 语法错误: 标识符“PEXCEPTION_ROUTINE”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(12277,1): error C2059: 语法错误:“}”
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
C:\Program Files (x86)\Windows Kits\10\Include\10.0.22621.0\um\winnt.h(12279,39): error C2143: 语法错误: 缺少“{”(在“*”的前面)
  (编译源文件“../../../../../qw_platform/qwos/module/touchx/touchgfx_js/dial_common_api.c”)
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(38,16): warning C4013: “get_dial_data_num”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(156,29): warning C4013: “assert”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(428,23): warning C4013: “get_cfg_dial_goodsid_by_index”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(489,34): warning C4013: “get_using_dial_index”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(494,9): warning C4013: “js_destory_dial”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(506,28): warning C4013: “get_cfg_dial_type”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(506,17): warning C4047: “初始化”:“const char *”与“int”的间接级别不同
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(587,28): error C2146: 语法错误: 缺少“;”(在标识符“dialInfo”的前面)
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(587,28): error C2065: “dialInfo”: 未声明的标识符
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(587,39): error C2059: 语法错误:“{”
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(588,26): warning C4013: “get_dial_cache_buf”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(590,9): error C2065: “dialInfo”: 未声明的标识符
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(590,18): error C2224: “.buf”的左侧必须具有结构/联合类型
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(591,9): error C2065: “dialInfo”: 未声明的标识符
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(591,18): error C2224: “.goodsid”的左侧必须具有结构/联合类型
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(592,9): warning C4013: “js_recover_dial_snapshot”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(592,35): error C2065: “dialInfo”: 未声明的标识符
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(596,9): warning C4013: “js_create_new_dial_snapshot”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(588,15): warning C4047: “初始化”:“void *”与“int”的间接级别不同
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(614,5): warning C4013: “submit_gui_event”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(614,22): error C2065: “GUI_EVT_SYNC_DIAL”: 未声明的标识符
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(637,24): warning C4013: “get_cfg_current_using_dial_goodsid”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(636,17): warning C4047: “初始化”:“const char *”与“int”的间接级别不同
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(652,27): warning C4013: “get_dial_them_color”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(653,5): warning C4013: “qjs_touchgfx_set_theme_color”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(658,5): warning C4013: “qjs_touchgfx_restore_all_edit_data_preview_view”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(662,5): warning C4013: “qjs_touchgfx_refresh_dial_view”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(667,5): warning C4013: “qjs_touchgfx_update_edit_data_preview”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(669,6): error C2084: 函数“void set_js_aod_mode(const char *,bool)”已有主体
      E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.h(101,6):
      参见“set_js_aod_mode”的前一个定义
  
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\dial_common_api.c(671,5): warning C4013: “qjs_touchgfx_set_aod_mode”未定义；假设外部返回 int
