/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   factory_mudule.h
@Time    :   2025/01/21 14:53:30
<AUTHOR>   nullptr
*
**************************************************************************/


#include "factory_mudule.h"
#include "backlight_module/backlight_module.h"
#include "qw_log.h"
#include "qw_time_api.h"
#include "qw_time_util.h"
#include "view_page_model.h"
#include "qw_timer.h"

#if defined IGS_DEV && defined FACTORY_MODE_ENABLE
#include "gps_dev.h"
#endif

#define FAT_USE_MUTEX 0
const char *g_factory_main_page = "FactoryMenu";

const char *g_factory_page_list[] = {
    "SensorSelfCheck",         //传感器自检
    "BatteruChargingStatus",   //电池
    "CurrentTestControl",      //电流测试
    "ScreenBrightnessTest",    //亮度
    "FAT_ScreenColor",         //屏幕颜色
    "TouchPanelTest",          //触摸屏
    "FAT_LightSensor",         //光感
    "FAT_KeyTest",             //按键
    "BuzzerTest",              //蜂鸣器
    "MotorTest",               //马达
    "FAT_Barometer",           //气压计
    "CompassData",             //电子罗盘
    "SixAxisSensorData",       //六轴传感器
    "FactoryGoMore",           //Go More
    "FactoryGpsSignal",        //GPS信号
    "FactoryGpsStartTest",     //GPS启动测试
    "FactorySensorMenu",       //传感器
    "FactorySensorLink",       //传感器测试
    "FAT_AgingTest",           //老化测试
    "FactoryMode",             //工厂/APP模式
    "FactoryQrCode",           //二维码
    "RTCAccuracyTest",         //时间
    "ImageTest",               //看图模式
    "DeveloperMode",           //开发者模式
    "FAT_VersionInfo",         //关于
};

static factory_state_t factory_cur_status = factory_max;
static fat_file_t s_file_info = {0};

// 修改时需同步修改 fat_data_t
char file_name_arry[FAT_DATA_TYPE_MXA][FAT_DATA_FILE_LEN] = {
    "light_sensor",
    "knob_sensor",
    "battery",
    "baro"
};

#ifdef SIMULATOR
static void init_pc_simulator_factory_dir()
{
    int res = 0;
    res = chdir(FAT_DATA_PATH);
    if (res != 0)
    {
        res = mkdir(FAT_DATA_PATH);
        if (res != 0)
        {
            assert(false && "[FAT_DATA]: create factory_data dir err");
        }
    }
    else
    {
        QW_LOG_D("FAT_DATA:", "\n dir exist\n");
    }
}
#endif

#ifndef SIMULATOR
static void init_factory_dir_check()
{
    FRESULT ret = FR_OK;
    QW_DIR  dir;
    ret = f_opendir(&dir, FAT_DATA_PATH);
    if (ret != FR_OK)
    {
        if (FR_NO_FILESYSTEM == ret)
        {
            assert(false && "[FAT_DATA]:FR_NO_FILESYSTEM err");
        }
        else
        {
            if (f_mkdir(FAT_DATA_PATH) != FR_OK)
            {
                assert(false && "[FAT_DATA]: create factory_data dir err");
            }
        }
    }
    else
    {
        f_closedir(&dir);
    }
}
#endif

void factory_module_init(void)
{
    //检查是否存在文件夹，如不存在,创建;
#ifdef SIMULATOR
    init_pc_simulator_factory_dir();
#else
    init_factory_dir_check();
#endif
    factory_cur_status = factory_close;
}

void factory_module_deinit(void)
{
    factory_cur_status = factory_close;
    for (int i = 0; i < FAT_DATA_TYPE_MXA; i++)
    {
        factory_module_close_file((fat_data_t) i);
    }
}

void factory_module_open_file(QW_FIL **fp, fat_data_t type)
{
#if FAT_USE_MUTEX
    if (s_file_info.mutex[type] == NULL)
    {
        s_file_info.mutex[type] = osMutexNew(NULL);
    }
    if (s_file_info.mutex[type] != NULL)
    {
        osMutexAcquire(s_file_info.mutex[type], osWaitForever);
    }
    else
    {
        QW_LOG_D("FAT_DATA:", "%d:create new Mutex err\n", type);
        //创建信号量失败
        return;
    }
#endif
    memset(s_file_info.filename[type], 0, FAT_DATA_FILE_LEN);
    sprintf(s_file_info.filename[type], "%s", file_name_arry[type]);

    qw_tm_t tm_time = {0};
    char path_buf[FAT_DATA_FILE_LEN] = {0};
    utc_to_localtime(get_sec_from_rtc(), &tm_time);
    if (snprintf(path_buf, FAT_DATA_FILE_LEN, "%s/%s_%02d-%02d-%02d-%02d-%02d.csv",
                 FAT_DATA_PATH, s_file_info.filename[type], (tm_time.tm_mon + 1),
                 tm_time.tm_mday, tm_time.tm_hour, tm_time.tm_min, tm_time.tm_sec)
        < 0)
    {
        QW_LOG_W("FAT_DATA:", "snprintf file name error\n");
#if FAT_USE_MUTEX

        osMutexRelease(s_file_info.mutex[type]);
        #endif
        return;
    }

    if (s_file_info.fp[type] == NULL)
    {
        qw_f_open(&s_file_info.fp[type], path_buf, QW_FA_CREATE_NEW | QW_FA_WRITE);
        if (s_file_info.fp[type] == NULL)
        {
            QW_LOG_W("FAT_DATA:", "open file %s err\n", s_file_info.filename[type]);
#if FAT_USE_MUTEX
            osMutexRelease(s_file_info.mutex[type]);
#endif
            return;
        }
        *fp = s_file_info.fp[type];
        s_file_info.file_state[type] = FAT_FILE_OPEN_SUC;
    }
#if FAT_USE_MUTEX
    osMutexRelease(s_file_info.mutex[type]);
#endif

    return;
}

void factory_module_write_file(fat_data_t type, const char *format, ...)
{
    if (s_file_info.file_state[type] != FAT_FILE_OPEN_SUC && s_file_info.file_state[type]
        != FAT_FILE_WR_OVER)
    {
        QW_LOG_W("FAT_DATA:", "%s :file write err :%d (fat_file_state_t)\n", __func__,
                 s_file_info.file_state[type]);
        return;
    }
    if (s_file_info.fp[type] == NULL)
    {
        QW_LOG_W("FAT_DATA:", "%s :open file err\n", __func__);
        return;
    }
#if FAT_USE_MUTEX
    if (s_file_info.mutex[type] != NULL)
    {
        osMutexAcquire(s_file_info.mutex[type], osWaitForever);
    }
    else
    {
        QW_LOG_D("FAT_DATA:", "\n %d:write file but no Mutex\n", type);
        return;
    }
#endif
    s_file_info.file_state[type] = FAT_FILE_WR_RUN;
    va_list _ArgList;
    va_start(_ArgList, format);
    char buf[FAT_DATA_LEN] = {0};
    int len = 0;
    len = vsnprintf(buf, FAT_DATA_LEN, format, _ArgList);

    if (len < FAT_DATA_LEN && len > 0)
    {
        uint32_t br;
        int32_t ret = qw_f_write(s_file_info.fp[type], buf, len, &br);
        if (ret != 0 || br != len)
        {
            QW_LOG_W("FAT_DATA:", "write err ret:%d,br:%d,len:%d\n", ret, br, len);
        }
    }
    else
    {
        QW_LOG_W("FAT_DATA:", "write err data input len bigger than %d \n", len);
    }
    va_end(_ArgList);
#if FAT_USE_MUTEX
    osMutexRelease(s_file_info.mutex[type]);
#endif
    s_file_info.file_state[type] = FAT_FILE_WR_OVER;
}

void factory_module_close_file(fat_data_t type)
{
    if (s_file_info.fp[type] == NULL)
    {
        QW_LOG_W("FAT_DATA:", "close but no open \n");
        return;
    }
#if FAT_USE_MUTEX
    if (s_file_info.mutex[type] != NULL)
    {
        osMutexAcquire(s_file_info.mutex[type], osWaitForever);
    }
    else
    {
        QW_LOG_D("FAT_DATA:", "%d:close file but no Mutex\n", type);
        return;
    }
#endif
    int32_t ret = qw_f_close(s_file_info.fp[type]);
    if (ret != 0)
    {
        QW_LOG_W("FAT_DATA:", "close erro ret:%d \n", ret);
    }
    s_file_info.fp[type] = NULL;
#if FAT_USE_MUTEX
        osMutexRelease(s_file_info.mutex[type]);
        osMutexDelete(s_file_info.mutex[type]);
        s_file_info.mutex[type] = NULL;
#endif
    s_file_info.file_state[type] = FAT_FILE_CLOSE;
}

factory_state_t factory_module_get_state(void)
{
    return factory_cur_status;
}

#if defined IGS_DEV && defined FACTORY_MODE_ENABLE
static qw_timer check_start_timer_;  // 开机自检启动定时器
static bool     check_gps_time_out = false;  // 检测GPS上电超时
static bool     is_gps_check = false;  // GPS上电检测:避免反复进出工模界面重复检测
// 定时器回调函数
void check_start_timer_callback(void* parameter)
{
    check_gps_time_out = true;
    // 关闭GPS
    GPS_DEV_POWER_MODE mode = GPS_DEV_POWER_MODE_POWER_OFF;
    gps_dev_control(GPS_DEV_CMD_SWITCH_POWER_MODE, (void *)&mode);
}
#endif

void factory_module_set_state(factory_state_t state)
{
    if(factory_cur_status == factory_max)
    {
        factory_module_init();
    }
    if (state == factory_runging)
    {
        QW_LOG_I("FAT_DATA:", "Enter Factory Mode \n");
        //TODO 进入工模状态
        //常亮
        //设置亮屏时间
        //关抬腕
        set_backlight_force_on(BK_FORCE_EVT);   //
        if (get_lift_wrist())
        {
            unsubscribe_backlight_algo(BK_ALGO_WRISTUP);
        }
#if defined IGS_DEV && defined FACTORY_MODE_ENABLE
        if(!is_gps_check)
        {
            //短暂开启GPS检测上电是否正常。
            GPS_DEV_POWER_MODE mode = GPS_DEV_POWER_MODE_NORMAL;
            gps_dev_control(GPS_DEV_CMD_SWITCH_POWER_MODE, (void *)&mode);
            check_gps_time_out = false;
            //5S超时自检结束，初始化定时器
            qw_timer_init(&check_start_timer_, QW_TIMER_FLAG_ONE_SHOT | QW_TIMER_FLAG_SOFT_TIMER,
                    check_start_timer_callback);
            qw_timer_start(&check_start_timer_, 3000, NULL,
                        "check_start_timer");        // 启动定时器
        }
#endif
    }
    else if (state == factory_close)
    {
        QW_LOG_I("FAT_DATA:", "Quit Factory Mode \n");
        //TODO 退出工模状态
        set_backlight_force_on(BK_FORCE_DISABLE);   // 调整强制背光
        if (get_lift_wrist())
        {
            subscribe_backlight_algo(BK_ALGO_WRISTUP);
        }
#if defined IGS_DEV && defined FACTORY_MODE_ENABLE
        if(!is_gps_check)
        {
            // 停止定时器
            qw_timer_stop(&check_start_timer_);
            if(!check_gps_time_out){ // 快速退出时处理
                // 关闭GPS
                GPS_DEV_POWER_MODE mode = GPS_DEV_POWER_MODE_POWER_OFF;
                gps_dev_control(GPS_DEV_CMD_SWITCH_POWER_MODE, (void *)&mode);
            }
            is_gps_check = true;
        }
#endif
    }
    factory_cur_status = state;
}

QW_FIL **factory_module_get_file_handle(fat_data_t type)
{
    return &s_file_info.fp[type];
}


bool factory_module_upload_key_event(uint8_t *key, const char *page_name)
{
    if (strcmp(page_name, "FAT_KeyTest") == 0 || strcmp(page_name, "ImageTest") == 0)
    {
        return false;
    }
    if(*key == 1)
    {
        factory_module_sleep(true);
        return false;
    }
    return false;
}

//
//工模中进入休眠
void factory_module_sleep(bool s)
{
    if(factory_cur_status != factory_runging)
    {
        return ;
    }
    if(!s)
    {
        set_backlight_force_on(BK_FORCE_EVT);
        backlight_open_app();
    }
    else
    {
        set_backlight_force_on(BK_FORCE_DISABLE);
        backlight_close_app();
    }
}
