/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ant_rd_rx.c
@Time    :   2024/12/16 10:52:51
<AUTHOR>   txy
*
**************************************************************************/

#include "app_error.h"
#include "ant_rd.h"
#include "ant_rd_rx.h"
#include "qw_sensor_common.h"
#include "sensor_ant_common.h"
#include "ant_parameters.h"
#include "ant_interface.h"
#include "basictype.h"
#include "ant_hrm_rx.h"
#include "subscribe_service.h"
#include "qw_sensor_data.h"
#include "cfg_header_def.h"

#if ANT_SENSOR_RD_ENABLED

static void ant_rd_rx_evt_handler(ant_rd_profile_t * p_profile, ant_rd_evt_t event);
static void rd_ant_evt(ant_evt_t *p_ant_evt, void * p_context);
static int32_t ant_hrm_rd_speed_open(void);
static int32_t ant_hrm_rd_speed_close(void);

RD_DISP_PROFILE_CONFIG_DEF(m_ant_rd, ant_rd_rx_evt_handler);

static ant_rd_profile_t m_ant_rd;
static int ant_module_init = 0;
static uint16_t rd_dev_id = 0xffff;
static uint8_t page1_data_updata_flag = false;
static bool speed_is_open = false;
static bool is_hr_rd_device = false;
static uint16_t rd_speed = 0xffff;

NRF_SDH_ANT_OBSERVER(m_rd_ant_observer, ANT_RD_ANT_OBSERVER_PRIO,
                     rd_ant_evt, &m_ant_rd);

static void LoadChnConf_rd_rx(ant_channel_config_t  *p_channel_config)
{
    sensor_original_data_t    *p_sensor_original_data = sensor_original_data_get();
    extern const uint8_t rd_chn_rf_frequency[5];
    p_channel_config->channel_number = sensor_ant_channel_num_get(SENSOR_TYPE_RD);
    p_channel_config->channel_type = CHANNEL_TYPE_SLAVE;
    p_channel_config->ext_assign = RD_EXT_ASSIGN;

    if(ENUM_RD_CHN_FREQ_INVALID >= p_sensor_original_data->rdData.chn_freq || ENUM_RD_CHN_FREQ_2461MHZ < p_sensor_original_data->rdData.chn_freq)
    {
        is_hr_rd_device = false;
        p_channel_config->rf_freq = RD_ANTPLUS_RF_FREQ;
    }
    else
    {
        is_hr_rd_device = true;
        p_channel_config->rf_freq = rd_chn_rf_frequency[p_sensor_original_data->rdData.chn_freq];
    }
    // p_channel_config->rf_freq = RD_ANTPLUS_RF_FREQ;
    p_channel_config->transmission_type = CHAN_ID_TRANS_TYPE;
    p_channel_config->device_type = RD_DEVICE_TYPE;
    if (!is_hr_rd_device)
    {
        p_channel_config->channel_period = RD_MSG_PERIOD;
    }
    else
    {
        p_channel_config->channel_period = HR_RD_MSG_PERIOD;
    }
    // p_channel_config->channel_period = 8070;//RD_MSG_PERIOD;
    p_channel_config->network_number = ANTPLUS_NETWORK_NUM;
}

static void ant_rd_rx_evt_handler(ant_rd_profile_t * p_profile, ant_rd_evt_t event)
{
    sensor_search_infor_t       sensor_search_infor;
    // sensor_connect_infor_t      *p_sensor_connect       = sensor_connect_infor_get(SENSOR_TYPE_RD);
    sensor_connect_infor_t      sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_RD, &sensor_connect);

    sensor_module_evt_handler   evt_handler             = sensor_module_evt_handler_get();
    sensor_saved_work_t         *p_sensor_saved         = NULL;
    sensor_work_state_t         sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    sensor_module_param_input_t *p_param                = sensor_module_param_input_get();
    sensor_original_data_t      *p_sensor_original_data = sensor_original_data_get();
    int8_t                      index                   = -1;
    uint8_t                     battery_index           = 0;

    sensor_ant_leave_rx_search(SENSOR_TYPE_RD);
    // rt_kprintf("[ant_rd_rx_evt_handler] event = %d\n", event);
    switch (event)
    {
        case ANT_RD_PAGE_0_UPDATED:
            break;

        case ANT_RD_PAGE_1_UPDATED:
            break;

        case ANT_RD_PAGE_16_UPDATED:
            break;

        case ANT_RD_PAGE_80_UPDATED:
            memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
            memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
            sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
            sensor_search_infor.sensor_type = SENSOR_TYPE_RD;
            // sensor_saved_work_infor_get(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state);

            if (sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
            {
                if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
                {
#if SENSOR_DEVICE_INFO_ENABLED
                    if (p_sensor_saved->sensor_manufacturer[index].manufacturer_ant != p_profile->page_80.manufacturer_id ||
                        p_sensor_saved->sensor_hw_version[index].version_ant != p_profile->page_80.hw_revision ||
                        p_sensor_saved->sensor_model[index].model_ant != p_profile->page_80.model_number)
                    {
                        p_sensor_saved->sensor_manufacturer[index].manufacturer_ant = p_profile->page_80.manufacturer_id;
                        p_sensor_saved->sensor_hw_version[index].version_ant        = p_profile->page_80.hw_revision;
                        p_sensor_saved->sensor_model[index].model_ant               = p_profile->page_80.model_number;
                        evt_handler(EVENT_SENSOR_MANUFACTURER_RECEIVED, NULL, sensor_search_infor.sensor_type, 0, index);
                    }
#endif
                    p_sensor_saved->sensor_manufacturer[index].manufacturer_ant = p_profile->page_80.manufacturer_id;
                    p_sensor_saved->sensor_hw_version  [index].version_ant      = p_profile->page_80.hw_revision;
                    p_sensor_saved->sensor_model       [index].model_ant        = p_profile->page_80.model_number;
                }
                sensor_saved_work_infor_release_write_lock(index);
            }
            break;

        case ANT_RD_PAGE_81_UPDATED:
            memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
            memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
            sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
            sensor_search_infor.sensor_type = SENSOR_TYPE_RD;
            // sensor_saved_work_infor_get(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state);

            if (sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
            {
                if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
                {
                    if (p_sensor_saved->sensor_serial[index].serial_ant != p_profile->page_81.serial_number ||
                        p_sensor_saved->sensor_sw_version[index].version_ant != ((((uint16_t)p_profile->page_81.sw_revision_major) << 8) | p_profile->page_81.sw_revision_minor))
                    {
                        p_sensor_saved->sensor_serial[index].serial_ant = p_profile->page_81.serial_number;
                        p_sensor_saved->sensor_sw_version[index].version_ant = ((((uint16_t)p_profile->page_81.sw_revision_major) << 8) | p_profile->page_81.sw_revision_minor);
                        evt_handler(EVENT_SENSOR_MANUFACTURER_RECEIVED, NULL, sensor_search_infor.sensor_type, 0, index);
                    }

                    p_sensor_saved->sensor_serial      [index].serial_ant       = p_profile->page_81.serial_number;
                    p_sensor_saved->sensor_sw_version  [index].version_ant      = ((((uint16_t)p_profile->page_81.sw_revision_major) << 8) | p_profile->page_81.sw_revision_minor);
                }
                sensor_saved_work_infor_release_write_lock(index);
            }
            break;

        case ANT_RD_PAGE_82_UPDATED:
            break;

        default:
            break;
    }

    //更新显示信息
    memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
    memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
    sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
    sensor_search_infor.sensor_type = SENSOR_TYPE_RD;

    // sensor_saved_work_infor_get(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state);

    if (SENSOR_CONNECT_STATE_CONNECTING == sensor_connect.state)
    {
        sensor_connect.state = SENSOR_CONNECT_STATE_CONNECTED;

        if (is_hr_rd_device)
        {
            ant_hrm_rd_speed_open();
        }

        sensor_connect_infor_set(SENSOR_TYPE_RD, &sensor_connect);
        if (sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
        {
            if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
            {
                p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
                cfg_mark_update(enum_cfg_ant_ble_dev);
            }
            else if (SENSOR_WORK_STATE_FORBIDDEN == sensor_work_state)
            {
                sensor_infor_t sensor_infor = {0};
                sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                sensor_disconnect(&sensor_infor);
            }
            sensor_saved_work_infor_release_write_lock(index);
        }
        else
        {
            if(sensor_disconnect_item_check(&sensor_search_infor))
            {
                sensor_disconnect_info_remove(&sensor_search_infor);
                sensor_infor_t sensor_infor = {0};
                sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                sensor_disconnect(&sensor_infor);
                return;
            }
            sensor_saved_work_infor_add(&sensor_search_infor);
            sensor_search_infor_del(&sensor_search_infor);
        }

        if (NULL != evt_handler)
        {
            evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, TRUE);
            evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, SENSOR_TYPE_RD, SENSOR_RADIO_TYPE_ANT, 0);
            evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, SENSOR_TYPE_RD, SENSOR_RADIO_TYPE_ANT, TRUE);
        }

    }
    if (SENSOR_CONNECT_STATE_CONNECTED == sensor_connect.state)
    {
        if (NULL != evt_handler)
        {
            evt_handler(EVENT_SENSOR_DATA_UPDATE, NULL, SENSOR_TYPE_RD, SENSOR_RADIO_TYPE_ANT, 0);
        }
        if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
        {
            if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
            {
                p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
            }
            sensor_saved_work_infor_release_write_lock(index);
        }
    }
}

/**
 * @brief RD ANT事件处理函数
 * @param channel_number 通道号
 * @param device_number 设备号
 */
static void rd_ant_evt(ant_evt_t *p_ant_evt, void * p_context)
{
    sensor_search_infor_t     sensor_search_infor;
    // sensor_connect_infor_t    *p_sensor_connect       = sensor_connect_infor_get(SENSOR_TYPE_RD);
    sensor_connect_infor_t    sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_RD, &sensor_connect);

    sensor_module_evt_handler evt_handler             = sensor_module_evt_handler_get();
    sensor_saved_work_t       *p_sensor_saved         = NULL;
    sensor_original_data_t    *p_sensor_original_data = sensor_original_data_get();
    sensor_work_state_t       sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    uint8_t                   channelstate            = 0;
    int8_t                    index                   = -1;
    ret_code_t                err_code                = NRF_SUCCESS;
    bool                   manual_connect_status_sent = false;

    sensor_systime_update();

    ant_rd_disp_evt_handler(p_ant_evt, p_context, &p_sensor_original_data->rdData);

    if (p_ant_evt->channel == m_ant_rd.channel_number)
    {
        // rt_kprintf("[rd_ant_evt] event = %d\n", p_ant_evt->event);
        switch (p_ant_evt->event)
        {
            case EVENT_CHANNEL_CLOSED:
                //更新显示信息
                memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
                memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
                sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
                sensor_search_infor.sensor_type = SENSOR_TYPE_RD;

                sensor_ant_leave_rx_search(SENSOR_TYPE_RD);

                err_code = sd_ant_channel_unassign(m_ant_rd.channel_number);
                APP_ERROR_CHECK(err_code);
                m_ant_rd.channel_number = 0;
                sensor_ant_channel_num_unassign(SENSOR_TYPE_RD);

                bool forbidden_mask = sensor_connect_infor_get_forbidden_mask(SENSOR_TYPE_RD);
                if(sensor_connect_infor_get(SENSOR_TYPE_RD, &sensor_connect))
                {
                    sensor_connect_infor_clear(SENSOR_TYPE_RD);
                    if (NULL != evt_handler && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)
                    {
                        // evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                        evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, &sensor_connect.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                        manual_connect_status_sent = true;
                    }
                }

                // sensor_saved_work_infor_get(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state);
                if (sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
                {
                    if (SENSOR_WORK_STATE_IDLE != sensor_work_state)
                    {
                        p_sensor_saved->rssi             [index] = 0;
                        p_sensor_saved->battery_voltage  [index] = 0xff;
                        p_sensor_saved->sensor_work_state[index] = SENSOR_WORK_STATE_SAVED;
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }

                if((!forbidden_mask && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)       //连接完成后异常断连
                    || (forbidden_mask && sensor_connect.state == SENSOR_CONNECT_STATE_CLOSE_WAIT)
                    || sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)                      //连接超时
                {
                    // connected状态下断连，检索saved数组是否有同类型sensor并进行连接
                    sensor_connect_from_saved_info(sensor_search_infor.sensor_type);
                }

                if (NULL != evt_handler)
                {
                    evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                    // evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                    if (!manual_connect_status_sent)
                    {
                        evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                    }
                }
                p_sensor_original_data->battery_list.rd = 0;
                p_sensor_original_data->rdData.chn_freq = ENUM_RD_CHN_FREQ_INVALID;

                if (is_hr_rd_device)
                {
                    ant_hrm_rd_speed_close();
                }
                break;
            case EVENT_RX_FAIL_GO_TO_SEARCH:
                // sd_ant_channel_close(RD_CHANNEL_NUMBER);
                // sensor_ant_close(SENSOR_TYPE_RD);
                sensor_ant_enter_rx_search(SENSOR_TYPE_RD);
                break;
            case EVENT_RX_SEARCH_TIMEOUT:
                break;
            default:
                break;
        }
    }
}

// NRF_SDH_ANT_OBSERVER(m_rd_ant_observer, ANT_RD_ANT_OBSERVER_PRIO, rd_ant_evt, &m_ant_rd);

/**
 * @brief 设置RD通道
 * @param id 设备ID信息
 */
void ant_rd_rx_profile_setup(ant_id_t *id)
{
    sensor_connect_infor_t    sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_RD, &sensor_connect);
    ret_code_t             err_code          = NRF_SUCCESS;
    ant_channel_config_t channel_config;

    memcpy ((uint8_t *)&sensor_connect.sensor_id.ant_id, (uint8_t *)id, sizeof(ant_id_t));
    sensor_connect_infor_set(SENSOR_TYPE_RD, &sensor_connect);

    /*
    //device num的组成
    //1byte   1byte    |     1byte      |      1byte                  从左到右高到低
    //   device id     | device type    |MSN:extended device number LSN:Transmission Type
    */
    uint16_t sensor_id = (uint16_t)id->id;
    uint8_t trans_type = CHAN_ID_TRANS_TYPE;
    if (id->id > 0xffff)
    {
        trans_type = id->trans_type;
    }

    //加载参数
    LoadChnConf_rd_rx(&channel_config);
    channel_config.device_number     = sensor_id;
    channel_config.transmission_type = trans_type;

    err_code = ant_rd_disp_init(&m_ant_rd,
                                (const ant_channel_config_t *)&channel_config,
                                RD_DISP_PROFILE_CONFIG(m_ant_rd));
    APP_ERROR_CHECK(err_code);
}

/**
 * @brief 开启RD通道
 */
void ant_rd_rx_open(void)
{
    ret_code_t             err_code          = NRF_SUCCESS;

    err_code = ant_rd_disp_open(&m_ant_rd);
    APP_ERROR_CHECK(err_code);
}

static void speed_in_callback(const void *in, uint32_t len)
{
    algo_speed_pub_t *speed_data = (algo_speed_pub_t *)in;

    rd_speed = (uint16_t)speed_data->enhanced_speed;
}

static int32_t ant_hrm_rd_speed_open(void)
{
    int32_t ret = 0;

    if (speed_is_open)
    {
        ret = -1;
        return ret;
    }

    speed_is_open = true;
    rd_speed = 0xffff;
    optional_config_t config = {.sampling_rate = 0};
    ret = qw_dataserver_subscribe_id(DATA_ID_ALGO_SPEED, speed_in_callback, &config);

    return ret;
}

static int32_t ant_hrm_rd_speed_close(void)
{
    int32_t ret = 0;

    if (!speed_is_open)
    {
        ret = -1;
        return ret;
    }

    speed_is_open = false;
    rd_speed = 0xffff;

    ret = qw_dataserver_unsubscribe_id(DATA_ID_ALGO_SPEED, speed_in_callback);

    return ret;
}

static ret_code_t ant_rd_speed_transmit(uint16_t speed)
{
    ant_rd_page16_data_t page_16;
    ant_rd_profile_t *p_profile = &m_ant_rd;

    if (speed == 0xffff)
    {
        page_16.horizontal_speed = 0x0f;
        page_16.fractional_horizontal_speed = 0xff;
    }
    else
    {
        page_16.horizontal_speed = speed / 100;
        page_16.fractional_horizontal_speed = speed % 100;
    }

    return ant_rd_session_leader_speed_metrics_tx(p_profile, &page_16);
}

static void ant_rd_search_connect(uint16_t hrm_id)
{
    sensor_infor_t sensor_infor;
    sensor_infor.radio_type = SENSOR_RADIO_TYPE_ANT;
    sensor_infor.sensor_type = SENSOR_TYPE_RD;
    sensor_infor.sensor_id.ant_id.id = hrm_id;

    sensor_connect(&sensor_infor);
}

void ant_hr_rd_handle(void)
{
    sensor_connect_infor_t *p_sensor_connect_hrm;
    sensor_connect_infor_t *p_sensor_connect_rd;
    sensor_connect_infor_get(SENSOR_TYPE_HRM, p_sensor_connect_hrm);
    sensor_connect_infor_get(SENSOR_TYPE_RD, p_sensor_connect_rd);
    sensor_original_data_t *p_sensor_original_data  = sensor_original_data_get();
    uint8_t chn_freq = p_sensor_original_data->rdData.chn_freq;


    if ((p_sensor_connect_hrm->state != SENSOR_CONNECT_STATE_CONNECTED) || (p_sensor_connect_hrm->radio_type != SENSOR_RADIO_TYPE_ANT))
    {
        return;
    }

    if ((p_sensor_connect_rd->state != SENSOR_CONNECT_STATE_CONNECTED)
        && ((ENUM_RD_CHN_FREQ_INVALID == chn_freq) || (ENUM_RD_CHN_FREQ_2461MHZ < chn_freq))) //RD not online and channel not opened.
    {
        chn_freq = ENUM_RD_CHN_FREQ_2461MHZ;
        extern uint64_t device_info_num;
        uint16_t serial_number = (uint16_t)device_info_num;
        ant_hrm_rd_chn_cfg(serial_number, chn_freq);
    }
    else
    {
        if (p_sensor_connect_rd->state != SENSOR_CONNECT_STATE_CONNECTED) //Channel already opened but RD not online.
        {
            ant_rd_search_connect(p_sensor_connect_hrm->sensor_id.ant_id.id);
        }
        else //RD data still being received. Send data page 0x10 at 1Hz on the RD channel. (Ping sensor).
        {
            if (rd_speed != 0xffff)
            {
                ant_rd_speed_transmit(rd_speed);
            }
            else
            {
                ant_rd_speed_transmit(0xffff);
            }
        }
    }
}

#endif // ANT_SENSOR_RD_ENABLED
