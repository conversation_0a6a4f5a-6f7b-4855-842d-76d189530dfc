#include "lvgl.h" 

#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP ALIGN(4)
#endif

#ifndef eZIP_RGBARGB888A
#define eZIP_RGBARGB888A
#endif


LV_ATTRIBUTE_MEM_ALIGN_EZIP const  uint8_t sys_set_personal_data_map[] SECTION(".ROM3_IMG_EZIP.sys_set_personal_data") = { 
    0x00,0x00,0x06,0x58,0x46,0x08,0x20,0x00,0x00,0x48,0x00,0x48,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0x03,0x00,0x00,0x00,0x94,0x00,0x00,0x03,0x3c,0x00,0x00,0x05,0x70,
    0x3d,0x33,0x88,0x6d,0x9b,0x8c,0xe2,0xf8,0x7b,0x4e,0xd4,0x69,0xed,0x8d,0x8a,0x12,0x55,0x53,0x0b,0x24,0xe9,0x50,0x76,0x18,0xd2,0x54,0x44,0xd5,0x4d,0x74,0x1b,0xf4,
    0x02,0xda,0x24,0x10,0x2d,0x68,0xec,0x04,0x0c,0x54,0x09,0x09,0x38,0xa0,0x26,0xed,0x96,0x84,0x35,0x49,0x0f,0xdc,0x10,0x63,0x0c,0x09,0x4d,0xda,0x7a,0x68,0xe1,0x42,
    0x0f,0x3b,0xd0,0x4e,0xeb,0xb4,0x55,0xea,0xc4,0x76,0x61,0x50,0xda,0x24,0x63,0x6b,0x85,0xaa,0xad,0x63,0xdb,0x85,0xb5,0x5a,0x59,0xfd,0xf1,0x3e,0xbb,0x6e,0xd3,0x35,
    0x4e,0xec,0xd8,0xb1,0x9d,0x34,0xaf,0xb2,0xfd,0x35,0xb6,0x3f,0xdb,0x3f,0xff,0xdf,0xfb,0xde,0xfb,0x0c,0x44,0x70,0x90,0xb1,0x5e,0x9f,0x17,0x96,0x71,0x27,0x35,0x03,
    0x00,0xe8,0x03,0x84,0x06,0x5a,0x3c,0x20,0x42,0x2d,0x6d,0xeb,0xe4,0x83,0xe8,0x0f,0xe1,0x11,0xb5,0x6e,0xd3,0x32,0x47,0xed,0x1b,0xb4,0x7f,0x12,0x90,0x5d,0x83,0xaa,
    0x87,0xe3,0x18,0x99,0x5b,0x30,0xf3,0x9e,0x6c,0x05,0xc4,0xba,0xbd,0x3e,0x70,0xb9,0xda,0x81,0xe1,0x5e,0x60,0x6c,0xb7,0x04,0xc3,0x70,0xa7,0x30,0x4a,0xb0,0x86,0x01,
    0x1e,0x0f,0x61,0xfc,0xe6,0x9d,0x92,0x03,0xc4,0xba,0x5f,0x78,0x16,0x04,0xd6,0x49,0xad,0xb7,0xe8,0xdf,0xe6,0xa2,0x5e,0x0c,0xe1,0x0c,0x80,0x78,0x02,0x63,0xe9,0x09,
    0xc7,0x03,0x62,0x21,0xff,0xeb,0x74,0xb9,0x0f,0xa9,0x79,0xd0,0x7a,0xa9,0xc2,0x20,0x81,0xfa,0x12,0x13,0xe9,0x49,0xc7,0x01,0x62,0x3d,0xfe,0x77,0xc9,0x85,0x3e,0xa5,
    0xe6,0x4b,0xf6,0x07,0x39,0x76,0x14,0x13,0xa9,0x3e,0x47,0x00,0x62,0x41,0xff,0x1b,0x80,0x42,0x88,0x5a,0x2d,0xe0,0x2c,0x3b,0x0f,0x22,0x3b,0x82,0xfd,0xa9,0xbf,0x6c,
    0x01,0xc4,0xba,0xfd,0xcf,0x03,0x62,0x9c,0x7a,0xee,0x74,0x18,0x98,0x4c,0x97,0x9b,0xa7,0x40,0xfe,0x1e,0xc6,0x53,0x23,0x96,0x02,0xa2,0x38,0xf3,0x09,0x75,0xf9,0x15,
    0x35,0xab,0xa0,0x24,0x0c,0x3b,0x31,0x3e,0x3d,0x54,0x74,0x40,0x2c,0xe8,0x7b,0x1a,0xd0,0xf5,0xed,0xca,0xc8,0x54,0x62,0x96,0x1b,0x92,0x61,0x40,0xac,0x77,0xfb,0x3e,
    0xf2,0xe9,0x1f,0xa8,0xd9,0x08,0x25,0x6b,0xac,0x5d,0xcd,0xdd,0x0c,0x01,0x62,0xa1,0xa6,0x0f,0x68,0xf3,0x3d,0x94,0xba,0xf1,0x98,0xc4,0x58,0x4b,0xb6,0xc0,0x2d,0x18,
    0x88,0x37,0x3d,0x65,0x01,0x47,0x96,0x49,0x1d,0x08,0x78,0x2a,0xdb,0xae,0x82,0x00,0xb1,0x60,0x53,0x82,0x7a,0xed,0x2b,0x0b,0x38,0x6b,0xb6,0x9f,0x52,0x93,0x5e,0xc3,
    0x2e,0x26,0xc1,0x41,0xe8,0x86,0x72,0x35,0x26,0xee,0xc8,0xcc,0xb8,0x05,0xdd,0x6e,0x55,0xce,0x70,0x64,0x24,0xc7,0x0a,0x52,0x50,0xd9,0x04,0x64,0x4d,0x31,0x49,0x6c,
    0x51,0x0a,0x5c,0xb7,0x8e,0xa1,0xdc,0x34,0x38,0x63,0x33,0x0b,0x10,0xbd,0x7c,0x5f,0xda,0x6a,0xb5,0xf0,0x9e,0x5a,0x88,0xd0,0x62,0x91,0x8a,0xba,0x68,0x35,0xa1,0x49,
    0x41,0x72,0x12,0x28,0xfc,0x6a,0x66,0x9e,0x83,0xf1,0x64,0x41,0xe7,0xb5,0x35,0x56,0xc3,0x85,0x43,0xdb,0x2c,0x82,0xf4,0x9f,0x87,0xcf,0x27,0xe5,0x57,0x90,0x9c,0x21,
    0x37,0xea,0x51,0x87,0xb4,0x9d,0x5d,0xdc,0xb0,0x8f,0x2b,0x40,0x8f,0x6a,0x54,0xfb,0xa6,0x6d,0xb6,0xfe,0x95,0x6b,0x98,0x63,0xee,0x0e,0x5a,0x7d,0xed,0xce,0x5f,0x5b,
    0x69,0x2f,0x1f,0x22,0x97,0xee,0x41,0x94,0x16,0x55,0x05,0x34,0x6c,0x35,0x74,0xcb,0xe1,0x95,0x87,0xe7,0x70,0xd4,0xae,0xc3,0x7f,0xe7,0x2a,0xe3,0x6a,0x33,0x36,0x9a,
    0xe1,0x01,0x0e,0x08,0x73,0x56,0xe5,0x02,0xfe,0xa9,0xa7,0xf0,0xcc,0xe7,0x3a,0x8a,0x8b,0x70,0x90,0x17,0x55,0x14,0x90,0xcb,0x14,0xf7,0xd2,0xe2,0xa2,0x34,0xa8,0x18,
    0x17,0x51,0xd5,0xbf,0x35,0xea,0x0a,0xe2,0x53,0x16,0x26,0x57,0xe5,0xdc,0x35,0x38,0x9c,0x42,0xdd,0x40,0x09,0xee,0x5a,0x8f,0x35,0xac,0xa2,0xa5,0x9a,0x56,0xb7,0xfa,
    0x64,0x57,0x71,0xe6,0x73,0xa2,0x2b,0x6e,0x18,0xd6,0x09,0x89,0x2b,0xce,0x48,0xfc,0x2a,0xd0,0xcd,0x76,0x65,0x57,0x90,0x3c,0x13,0x58,0xd4,0x6b,0xe7,0x8a,0x55,0xce,
    0xc9,0x19,0x21,0x20,0x64,0x9d,0x43,0x76,0xde,0x34,0xa9,0x5d,0x55,0xbe,0x77,0x63,0xa9,0x21,0x4f,0xb0,0x57,0x4c,0xb6,0x7a,0x61,0xe3,0xa7,0x19,0x07,0x7c,0x7d,0x70,
    0x8e,0x79,0x9e,0x50,0x90,0xf4,0xdd,0xaa,0x62,0x6b,0x2e,0xb6,0xc5,0xbd,0xee,0x8b,0x27,0x88,0x07,0xed,0xb8,0x0f,0x3e,0x1c,0x87,0x77,0x3f,0xb5,0xda,0x56,0x32,0x65,
    0x5b,0x46,0xae,0xf5,0xa5,0x3c,0xae,0x8d,0x62,0x28,0xbe,0x63,0xc7,0x3d,0x64,0x2b,0x42,0x39,0x24,0x25,0x87,0xc9,0x97,0x9d,0x17,0xdb,0x32,0x00,0xc1,0x9b,0x4e,0x80,
    0xa3,0x56,0x5b,0xd9,0x05,0x49,0x8a,0x41,0xac,0xdb,0xeb,0xa3,0x4d,0xb3,0xd5,0x17,0xd7,0x9a,0x51,0xf3,0xe3,0x0c,0x67,0xc5,0x05,0xda,0xff,0x00,0x44,0x20,0x63,0x3d,
    0x4d,0x5d,0xc0,0xe0,0x1b,0x30,0x68,0x18,0x4f,0x6a,0x3e,0x36,0xbc,0xa7,0x16,0x22,0xb4,0x68,0xb5,0xc8,0xa5,0x7b,0x10,0xa5,0x45,0xab,0x5d,0x38,0xb4,0x0d,0xda,0x1a,
    0xab,0x8d,0x3e,0x12,0x08,0xd2,0x9a,0xe1,0x3e,0x70,0xb8,0xb5,0x35,0x6c,0xb5,0xe5,0xba,0x32,0x20,0x64,0xad,0x9b,0xe5,0x81,0x75,0x03,0x62,0xbd,0x3e,0x2f,0xb9,0x97,
    0xc7,0xe9,0x37,0x3a,0x36,0xbb,0x68,0x93,0x82,0x96,0x71,0xa7,0x1d,0x17,0x8e,0x5e,0xbe,0x5f,0x0a,0x02,0x5a,0xe4,0x2e,0x16,0xb0,0x45,0x11,0x33,0x0b,0xd2,0xa2,0xf5,
    0x58,0x3d,0x01,0xda,0x34,0x63,0xf0,0x40,0x00,0x01,0x7d,0x76,0xbd,0x9e,0xbd,0x03,0x7f,0x3b,0x5b,0x6d,0x08,0xb7,0x04,0xa2,0xd4,0x60,0xa7,0x86,0x79,0x6a,0x10,0x51,
    0x51,0x07,0x57,0x0e,0x87,0xa8,0x55,0x69,0x45,0x50,0xd0,0x8c,0x9b,0x28,0x79,0xa8,0x61,0xab,0x71,0xf7,0xb9,0x48,0x41,0xf8,0x95,0x8c,0x91,0x8d,0xff,0x6f,0x1b,0x98,
    0xd5,0xb7,0xc7,0xd2,0x6e,0x10,0xa1,0x16,0xa4,0x74,0xd1,0xe6,0x51,0x4a,0x47,0x4c,0xb2,0xd0,0xa6,0xb8,0x82,0xea,0x6c,0x49,0xfc,0x28,0xcb,0xcd,0xa5,0x18,0x25,0x0b,
    0x56,0x8e,0xb1,0x45,0x51,0x2e,0xd7,0x75,0xb7,0x1d,0x70,0x72,0x95,0x01,0x1c,0x82,0xda,0x3e,0xbd,0xe5,0x86,0xd1,0x21,0x1e,0x8f,0x4f,0x5d,0xe7,0x41,0x9a,0x39,0x05,
    0x4e,0xa6,0x72,0xb2,0x02,0xa2,0xda,0x8d,0x9f,0x6f,0x4d,0x80,0x66,0x57,0xe4,0x44,0x11,0xe1,0x91,0x55,0x70,0x78,0x81,0x6a,0xb4,0x80,0xe4,0xe7,0x87,0x75,0x14,0xb9,
    0x85,0x07,0x68,0x61,0x5c,0xa9,0xc5,0x6e,0x5b,0x05,0x28,0x62,0xd2,0x83,0x45,0xac,0x00,0x24,0x8a,0x63,0x0a,0xa0,0x39,0x28,0x41,0x33,0x63,0x2a,0x23,0xa7,0x80,0xfa,
    0x53,0xa3,0x8a,0x8b,0xdd,0xb0,0x4c,0x41,0x26,0x05,0x58,0xde,0x4f,0x91,0x47,0xb4,0x21,0xa5,0xc1,0xf3,0xa0,0x49,0xab,0xf2,0xa0,0xa8,0xb5,0xa3,0x90,0x11,0xf7,0x1a,
    0x5e,0xab,0xe6,0x91,0x5d,0x83,0x8a,0x65,0x8e,0x5e,0xb3,0xd8,0x9f,0x1e,0x58,0x03,0x54,0xf5,0x70,0xbc,0x42,0x65,0xdd,0x04,0x50,0x62,0xdd,0xbf,0x18,0x99,0x5b,0xa0,
    0x4c,0x68,0xb4,0x42,0x46,0xb2,0xab,0x18,0x4b,0x9d,0xcc,0x36,0xe5,0x3a,0x5c,0x61,0x23,0xd1,0x08,0x67,0x9f,0x93,0x86,0xc7,0x43,0x15,0x3a,0x70,0x1a,0xfb,0x92,0xe7,
    0xb2,0x02,0xc2,0xf8,0xcd,0x3b,0x34,0x92,0x9d,0x29,0xa7,0xa7,0xd5,0x99,0x27,0xdd,0x05,0x26,0x7e,0xa1,0xfe,0x55,0x43,0x1e,0xdb,0x4e,0x38,0x3d,0x79,0xd3,0x53,0xd2,
    0xe8,0x4c,0x0b,0x3f,0xc7,0x44,0xfa,0x6e,0x4e,0x40,0x18,0x4b,0x4f,0x50,0xb0,0x1e,0x34,0x5a,0x88,0x5a,0x52,0x27,0xe5,0x79,0x49,0xfa,0x4a,0x11,0xfc,0x0e,0xe3,0xd3,
    0x67,0xd5,0x67,0x5d,0x33,0x53,0x80,0xa0,0x2f,0x40,0x45,0xda,0x1f,0x9b,0x26,0xea,0xf0,0x1c,0x30,0x96,0x6a,0x26,0x08,0x2c,0xf7,0x87,0x43,0xe5,0xf8,0x44,0x7a,0x92,
    0x12,0xa5,0xa3,0x9b,0x67,0xd4,0xc2,0xf7,0x73,0xc1,0xd9,0xa0,0xa0,0x55,0x25,0x85,0x9a,0x78,0x5e,0xb4,0xbf,0xbc,0x33,0x66,0x7c,0x1b,0x13,0xd3,0x3f,0x69,0xfb,0xf4,
    0xbc,0xa1,0x16,0x61,0x47,0x88,0xeb,0x7c,0xf9,0xba,0x16,0x7e,0xa6,0x05,0x8e,0xaa,0x82,0x64,0x15,0xf9,0x5f,0xa3,0xdd,0xbf,0x6c,0x56,0xe5,0xe4,0x56,0x90,0x94,0x1b,
    0xa5,0x46,0x68,0xdd,0x59,0x56,0x01,0xd9,0x05,0x2f,0xea,0x81,0xc3,0xed,0x7f,0x00,0x45,0xc8,0x63,0x2c,0xb4,0xbd,0x83,0xd6,0x83,0x50,0xda,0x76,0x0a,0x62,0xc9,0x8f,
    0x11,0x81,0xe9,0x3d,0x51,0xc8,0x77,0x00,0xc6,0xa7,0x87,0x08,0x50,0x3b,0x75,0x3d,0x5f,0x82,0x60,0xfe,0xa1,0x27,0x38,0x8c,0xf1,0xe4,0x47,0x85,0xc0,0xd1,0x04,0x48,
    0x86,0x94,0x1a,0x01,0xc6,0x5a,0xa8,0x79,0xbe,0x84,0xe0,0x9c,0x86,0xa5,0xe5,0x1d,0xf4,0x82,0xcf,0x1a,0xe9,0x04,0xf5,0x9e,0xc0,0x82,0xfe,0x5e,0x40,0x3c,0xee,0x60,
    0x30,0x57,0xe9,0xb5,0x87,0xb1,0x2f,0x79,0xce,0x8c,0xce,0xb0,0x90,0x93,0x58,0xd0,0x17,0x20,0xf1,0x1d,0xa3,0xb3,0x3b,0x1d,0x83,0x85,0xb1,0x59,0x02,0x93,0xc0,0x58,
    0xea,0xa4,0x99,0xdd,0xa2,0xa1,0x7b,0xea,0xf1,0xbd,0x4c,0xa0,0xba,0xc8,0xbb,0x0f,0xdb,0x07,0x06,0x7e,0x04,0x26,0xfe,0x8c,0xfd,0xe9,0x81,0x62,0x74,0x8f,0xa6,0xdc,
    0x63,0xe8,0xb9,0x67,0x00,0xdc,0x1d,0xc0,0xf0,0x00,0xf5,0xf8,0x6a,0x11,0x71,0x2c,0x92,0x52,0xae,0x00,0x0a,0xe3,0x20,0x8a,0x63,0xd8,0x9f,0x1a,0x2d,0x36,0x7f,0x34,
    0xfd,0x85,0x46,0xea,0xab,0x61,0xa9,0xa6,0x95,0x60,0xed,0x22,0xc9,0x07,0xe8,0x0d,0x7b,0xe9,0xe7,0x7a,0x5a,0x3c,0xd4,0xde,0x42,0x57,0xc4,0xfc,0x10,0xe0,0x01,0x1d,
    0x75,0x8b,0xb6,0x33,0x80,0x2c,0x4d,0xbf,0x4d,0x91,0x52,0x7f,0xa7,0x80,0xfb,0x9b,0xd5,0x02,0xfd,0x1f,0xef,0xea,0x48,0x22,
};


#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER ALIGN(4)
#endif
LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER const eZIP_RGBARGB888A  lv_img_dsc_t sys_set_personal_data SECTION(".ROM3_IMG_EZIP_HEADER.sys_set_personal_data") = { 
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .header.always_zero = 0,
  .header.w = 72,
  .header.h = 72,
  .data_size  = 1624,
  .data = sys_set_personal_data_map
};
