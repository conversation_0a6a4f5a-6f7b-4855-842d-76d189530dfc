/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   lv_lcd.c
@Time    :   2024/12/23 18:12:03
*
**************************************************************************/
#include "lv_lcd.h"
#include "lvgl.h"
#include <rtthread.h>
#include <rtdevice.h>
#include "mem_map.h"
#include "mem_section.h"
#include "../basic_app_module/backlight_module/backlight_module.h"
#ifndef BSP_USING_KNOB
#include "mt3503.h"  // 后面上传rt_device方式
#endif
#include "service_wristup.h"

/** @brief 是否使用动画缓冲区 */
#define ANIMATION_BUFFER    1
/** @brief 是否使用GPU加速 */
#define LV_USING_GPU        1
/** @brief 是否使用最小刷新区域 */
// #define MIN_REDRAW_AREA     1   //最小更新面积更新缓冲
/** @brief 是否使用LCDC拷贝 */
#define USING_LCDC_COPY     0
/** @brief 动画缓冲区是否放在PSRAM */
#define ANIMATIONBUF_PSRAM  1
/** @brief 屏幕缓存是否放在PSRAM */
#define SCREENCACHEDBUF_PSRAM   1

/** @brief LCD图形信息结构体 */
struct rt_device_graphic_info info;

//static bool touch_device_state = true; //默认开机是开启
//static bool knob_device_state = true;  //默认开机是开启

#if SCREENCACHEDBUF_PSRAM

#ifdef SOC_BF0_HCPU
    #ifdef BSP_USING_PSRAM
        EXEC_REGION_DEF(RW_PSRAM_NON_RET);
        /** @brief PSRAM非保留区起始地址 */
        #define PSRAM_NO_RETENTION_RAM_START_ADDR (EXEC_REGION_START_ADDR(RW_PSRAM_NON_RET))
    #endif
#endif

// /** @brief 动画缓冲区,放在L2非保留区 */
// L2_RET_BSS_SECT_BEGIN(animation_mem)
// ALIGN(4) static uint8_t animationBuffer[FB_WIDTH * FB_HEIGHT * FB_PIXEL_BYTES];
// L2_RET_BSS_SECT_END

/** @brief 屏幕缓存1和2,放在L2保留区 */
#if LV_COLOR_DEPTH == 16
L2_RET_BSS_SECT_BEGIN(cacha1_mem)
ALIGN(4) static uint8_t screenCachedBuffer[FB_WIDTH * FB_HEIGHT * FB_PIXEL_BYTES * 3];//暂定
L2_RET_BSS_SECT_END
#elif LV_COLOR_DEPTH == 24
L2_RET_BSS_SECT_BEGIN(cacha1_mem)
ALIGN(4) static uint8_t screenCachedBuffer[FB_WIDTH * FB_HEIGHT * (FB_PIXEL_BYTES * 3 + (FB_PIXEL_BYTES + 1))];//3个3字节屏幕缓存+1个四字节屏幕缓存
L2_RET_BSS_SECT_END
#elif LV_COLOR_DEPTH == 32
L2_RET_BSS_SECT_BEGIN(cacha1_mem)
ALIGN(4) static uint8_t screenCachedBuffer[FB_WIDTH * FB_HEIGHT * FB_PIXEL_BYTES * 3];
L2_RET_BSS_SECT_END
#endif

// /** @brief 屏幕缓存2,放在L2保留区 */
// L2_RET_BSS_SECT_BEGIN(cacha2_mem)
// ALIGN(4) static uint8_t screenCached2Buffer[FB_WIDTH * FB_HEIGHT * FB_PIXEL_BYTES];
// L2_RET_BSS_SECT_END

#endif

/** @brief 主帧缓冲区,放在L2非保留区 */
L1_NON_RET_BSS_SECT_BEGIN(frambuf)
L1_NON_RET_BSS_SECT(frambuf, ALIGN(4) static uint8_t frambuffer[FB_WIDTH * FB_HEIGHT * FB_PIXEL_BYTES]);
L1_NON_RET_BSS_SECT_END

// 第二帧缓冲区(暂未使用)
// L2_NON_RET_BSS_SECT_BEGIN(frambuf_2)
// L2_NON_RET_BSS_SECT(frambuf_2, ALIGN(4) static uint8_t frambuffer_2[FB_WIDTH * FB_HEIGHT * FB_PIXEL_BYTES]);
// L2_NON_RET_BSS_SECT_END

#if LV_USING_GPU
/** @brief GPU初始化函数声明 */
extern void lv_gpu_init(lv_disp_drv_t *drv);
#endif

// 同步回调函数(暂未使用)
// static void(*lcd_sync_wait)(void) = NULL;
// static void(*lcd_sync_ready)(void) = NULL;

/** @brief LVGL显示驱动句柄 */
static lv_disp_drv_t *lcd_disp_drv = NULL;
/** @brief LCD显示就绪标志 */
static bool lcd_display_ready = false;

/** @brief 非保留区偏移量 */
#define NO_RETENTION_OFFSET     (4*1024UL + 0x4B000UL)
/** @brief 大块非保留内存大小 */
#define NO_RETENTION_HUGE_MEM   496*1024UL
/** @brief 小块非保留内存大小 */
#define NO_RETENTION_SMALL_MEM   48*1024UL

typedef enum
{
    LCD_STATUS_NONE = 0,        /* Not executed lcd open*/
    LCD_STATUS_OPENING,
    LCD_STATUS_NOT_FIND_LCD,
    LCD_STATUS_INITIALIZED,     /*Finded LCD*/
    LCD_STATUS_DISPLAY_ON,
    LCD_STATUS_DISPLAY_OFF,
    LCD_STATUS_DISPLAY_TIMEOUT,
    LCD_STATUS_IDLE_MODE,
} LCD_DrvStatusTypeDef;

/**
 * @brief 获取LCD显示就绪状态
 * @return true:就绪 false:未就绪
 */
bool lcd_display_is_ready(void)
{
    return lcd_display_ready;
}

/**
 * @brief 获取大块非保留内存缓冲区
 * @param size 请求的大小
 * @return 缓冲区指针
 */
uint8_t *lv_display_no_retention_mem_buf_huge(uint32_t size)
{
    // if(size > NO_RETENTION_HUGE_MEM){
    //     return NULL;
    // }
    return (uint8_t *)(((uint32_t)PSRAM_NO_RETENTION_RAM_START_ADDR) + NO_RETENTION_OFFSET);
    // return animationBuffer;
}

/**
 * @brief 获取小块非保留内存缓冲区
 * @param size 请求的大小
 * @return 缓冲区指针
 */
uint8_t *lv_display_no_retention_mem_buf_small(uint32_t size)
{
    // if(size > NO_RETENTION_SMALL_MEM){
    //     return NULL;
    // }
    return (uint8_t *)(((uint32_t)PSRAM_NO_RETENTION_RAM_START_ADDR) + NO_RETENTION_HUGE_MEM + NO_RETENTION_OFFSET);
    // return screenCached1Buffer;
}

/** @brief LCD刷新同步信号量 */
static struct rt_semaphore lcd_sem;

/**
 * @brief LCD刷新完成回调函数
 * @param dev 设备句柄
 * @param buffer 缓冲区指针
 * @return 错误码
 */
static rt_err_t lcd_flush_done(rt_device_t dev, void *buffer)
{
#if !USING_LCDC_COPY
    // if(lcd_sync_ready){
    //     lcd_sync_ready();
    // }
    lv_disp_flush_ready(lcd_disp_drv);
#endif
#ifdef RT_USING_PM
    rt_pm_release(PM_SLEEP_MODE_IDLE);
    rt_pm_hw_device_stop();
#endif  /* RT_USING_PM */
    rt_sem_release(&lcd_sem);
    //rt_kprintf("lcd_flush_done %d\n", rt_tick_get());

    // 打印抬腕亮屏时间
    service_wristup_over_record();
    return RT_EOK;
}

/**
 * @brief LVGL显示驱动刷新回调函数
 * @param disp_drv 显示驱动句柄
 * @param area 刷新区域
 * @param color_p 颜色数据指针
 */
static void lv_display_driver_flush_callback(
    lv_disp_drv_t* disp_drv,
    const lv_area_t* area,
    lv_color_t* color_p)
{
#if MIN_REDRAW_AREA
    const char* minBuff;
    lv_area_t reDrawArea = disp_drv->minRedrawArea;
#endif
    rt_device_t lcd_device = (rt_device_t)disp_drv->user_data;
    rt_sem_take(&lcd_sem, RT_WAITING_FOREVER);
    #if TEMP_SHOW_GPS_SNR
    if(get_ldc_off_gui_skip() == true)
    {
        bk_close_screen_poweroff();
        lv_disp_flush_ready(disp_drv);
        rt_sem_release(&lcd_sem);
        return ;
    }
    #endif
#ifdef RT_USING_PM
    rt_pm_request(PM_SLEEP_MODE_IDLE);
    rt_pm_hw_device_start();
#endif  /* RT_USING_PM */
#if MIN_REDRAW_AREA
    //修正刷新区域,地址必须连续且4字节对齐
    reDrawArea.x1 = 0;
    reDrawArea.x2 = disp_drv->hor_res - 1;
    if (0 != (reDrawArea.y1 & 1)) {
        reDrawArea.y1 -= 1;
    }
    if (1 != (reDrawArea.y2 & 1)) {
        reDrawArea.y2 += 1;
    }
    // rt_kprintf("reDrawArea[%d,%d,%d,%d]\n", reDrawArea.x1, reDrawArea.y1, reDrawArea.x2, reDrawArea.y2);
    minBuff = (const char *)(color_p + reDrawArea.y1 * disp_drv->hor_res + reDrawArea.x1);
    rt_graphix_ops(lcd_device)->set_window(reDrawArea.x1, reDrawArea.y1, reDrawArea.x2, reDrawArea.y2);
    rt_graphix_ops(lcd_device)->draw_rect_async(minBuff, reDrawArea.x1, reDrawArea.y1, reDrawArea.x2, reDrawArea.y2);
#else
    if(!lcd_display_ready){
        lcd_display_ready = true;
    }

    //rt_kprintf("lv_display_driver_flush_callback %d\n", rt_tick_get());

    rt_graphix_ops(lcd_device)->set_window(0, 0, FB_WIDTH - 1, FB_HEIGHT - 1);
    rt_graphix_ops(lcd_device)->draw_rect_async((const char *)&frambuffer, 0, 0, FB_WIDTH - 1, FB_HEIGHT - 1);
#endif
}

/**
 * @brief 等待LCD刷新完成
 * @param disp_drv 显示驱动句柄
 */
static void lv_display_flush_wait_free(struct _lv_disp_drv_t* disp_drv)
{
    // if(lcd_sync_wait){
    //     lcd_sync_wait();
    // }
    rt_err_t err = rt_sem_take(&lcd_sem, RT_WAITING_FOREVER);
    RT_ASSERT(RT_EOK == err);
    err = rt_sem_release(&lcd_sem);
}

/**
 * @brief LCD硬件初始化
 * @return LCD设备句柄
 */
static rt_device_t lv_engine_hw_init(void)
{
    rt_err_t ret;
    uint8_t color = 0;
    struct rt_device_graphic_info info;

    uint16_t framebuffer_color_format = FB_COLOR_FORMAT;
    uint8_t brightness = 50;

    /* LCD设备初始化 */
    rt_device_t lcd_device = rt_device_find("lcd");
    RT_ASSERT(lcd_device != RT_NULL);
    rt_device_set_tx_complete(lcd_device, lcd_flush_done);
    if (rt_device_open(lcd_device, RT_DEVICE_OFLAG_RDWR) == RT_EOK){
        rt_device_control(lcd_device, RTGRAPHIC_CTRL_GET_INFO, &info);
    rt_kprintf("Lcd info w:%d, h:%d, bits_per_pixel:%d, pixel_format:%d, draw_align:%d, is_round:%d, reserved:%d, framebuffer:%p, bandwidth:%d\r\n",
               info.width, info.height, info.bits_per_pixel, info.pixel_format, info.draw_align,
               info.is_round, info.reserved, info.framebuffer, info.bandwidth);
    }

    rt_device_control(lcd_device, RTGRAPHIC_CTRL_POWERON, NULL);
    rt_device_control(lcd_device, RTGRAPHIC_CTRL_SET_BUF_FORMAT, &framebuffer_color_format);
    rt_device_control(lcd_device, RTGRAPHIC_CTRL_SET_BRIGHTNESS, &brightness);

    rt_sem_init(&lcd_sem, "lcdsem", 1, RT_IPC_FLAG_FIFO);

    return lcd_device;
}

/**
 * @brief LVGL显示引擎初始化
 * @return 显示驱动句柄
 */
void* touchx_lv_engine_display_init(void)
{
    static lv_disp_draw_buf_t disp_buf;
    static lv_disp_drv_t disp_drv;

    rt_device_t lcd_device = lv_engine_hw_init();

    lv_disp_draw_buf_init(&disp_buf, (lv_color_t*)frambuffer, NULL, FB_WIDTH * FB_HEIGHT);
    lv_disp_drv_init(&disp_drv);
    disp_drv.hor_res = FB_WIDTH;
    disp_drv.ver_res = FB_HEIGHT;

    disp_drv.flush_cb = lv_display_driver_flush_callback;
    disp_drv.wait_cb = lv_display_flush_wait_free;
    disp_drv.draw_buf = &disp_buf;

    disp_drv.full_refresh = true;
    disp_drv.user_data = (void *)lcd_device;

#if LV_USING_GPU
    lv_gpu_init(&disp_drv);
#endif

    lv_disp_drv_register(&disp_drv);

    lcd_disp_drv = &disp_drv;

    return &disp_drv;
}

/**
 * @brief 获取帧缓冲区指针
 * @return 帧缓冲区指针
 */
uint8_t* lv_display_framebuffer_get(void)
{
    return frambuffer;
}

#if LV_COLOR_DEPTH == 16
/**
 * @brief 获取动画缓冲区指针
 * @return 动画缓冲区指针
 */
uint8_t* lv_display_animation_framebuffer_get(void)
{
#ifdef ANIMATION_BUFFER
    return (uint8_t *)(screenCachedBuffer + (FB_WIDTH * FB_HEIGHT * FB_PIXEL_BYTES) * 2);
#else
    return NULL;
#endif
}

/**
 * @brief 获取屏幕缓存1指针
 * @return 屏幕缓存1指针
 */
uint8_t* lv_display_cachebuffer_get(void)
{
    return (uint8_t *)screenCachedBuffer;
}

/**
 * @brief 获取屏幕缓存2指针
 * @return 屏幕缓存2指针
 */
uint8_t* lv_display_cachebuffer1_get(void)
{
    return (uint8_t *)(screenCachedBuffer + (FB_WIDTH * FB_HEIGHT * FB_PIXEL_BYTES));
}
#elif LV_COLOR_DEPTH == 24
uint8_t* lv_display_cachebuffer_get()
{
    return (uint8_t *)(screenCachedBuffer);
}
#elif LV_COLOR_DEPTH == 32
/**
 * @brief 获取动画缓冲区指针
 * @return 动画缓冲区指针
 */
uint8_t* lv_display_animation_framebuffer_get(void)
{
#ifdef ANIMATION_BUFFER
    return (uint8_t *)(screenCachedBuffer + (FB_WIDTH * FB_HEIGHT * FB_PIXEL_BYTES) * 2);
#else
    return NULL;
#endif
}

/**
 * @brief 获取屏幕缓存1指针
 * @return 屏幕缓存1指针
 */
uint8_t* lv_display_cachebuffer_get(void)
{
    return (uint8_t *)screenCachedBuffer;
}

/**
 * @brief 获取屏幕缓存2指针
 * @return 屏幕缓存2指针
 */
uint8_t* lv_display_cachebuffer1_get(void)
{
    return (uint8_t *)(screenCachedBuffer + (FB_WIDTH * FB_HEIGHT * FB_PIXEL_BYTES));
}
#endif

#if true
// 定义work结构体
static struct rt_delayed_work lcd_on_delay_work;
// work处理函数
static void lcd_on_delay_work_handler(struct rt_work *work, void *work_data)
{
    // 打开touch 和 konb。
    // if(!touch_device_state)
    // {
    //     rt_device_t touch_device = rt_device_find("touch");
    //     RT_ASSERT(touch_device != RT_NULL);
    //     rt_device_control(touch_device, RTGRAPHIC_CTRL_POWERON, NULL);
    //     touch_device_state = true;
    // }
    //if(!knob_device_state)
    {
    #if BSP_USING_KNOB
        rt_device_t knob_device = rt_device_find("knob");
        RT_ASSERT(knob_device != RT_NULL);
        rt_device_control(knob_device, RTGRAPHIC_CTRL_POWERON, NULL);
        #else
        mt3503_control(MT3503_CMD_START, NULL); //后面 使用rt_device_control隔离
    #endif
        //knob_device_state = true;
    }
}
#endif

/**
 * @brief LCD掉电处理
 */
void lv_lcd_power_down(void)
{
    bool lcd_drawing = false;
    uint8_t cnt = 0;
    rt_device_t lcd_device = rt_device_find("lcd");
    do{
        rt_device_control(lcd_device, RTGRAPHIC_CTRL_GET_BUSY, &lcd_drawing);
        if (!lcd_drawing){
            break;
        }else{
            rt_thread_delay(10);
        }
    }while(++cnt < 20);
#if true
    // 关闭
    if (lcd_on_delay_work.work.flags == RT_WORK_STATE_SUBMITTING) {
        rt_work_cancel(&lcd_on_delay_work.work);
    }
#endif
    rt_device_control(lcd_device, RTGRAPHIC_CTRL_POWEROFF, NULL);
    //if(knob_device_state)
    {
        #if BSP_USING_KNOB
        rt_device_t knob_device = rt_device_find("knob");
        RT_ASSERT(knob_device != RT_NULL);
        rt_device_control(knob_device, RTGRAPHIC_CTRL_POWEROFF, NULL);
        #else
        mt3503_control(MT3503_CMD_STOP, NULL);  //后面 使用rt_device_control隔离
        #endif
        //knob_device_state = false;
    }
    // if(touch_device_state)
    {
        // 获取光旋钮开关状态
        bool knob_device_state = false;
        #if BSP_USING_KNOB
        rt_device_t knob_device = rt_device_find("knob");
        RT_ASSERT(knob_device != RT_NULL);
        rt_device_control(knob_device, RTGRAPHIC_CTRL_GET_STATE, static_cast<void*>(&knob_device_state));
        #else
        mt3503_control(MT3503_CMD_GET_STATE, (void*)&knob_device_state);  //后面 使用rt_device_control隔离
        #endif
        rt_device_t touch_device = rt_device_find("touch");
        RT_ASSERT(touch_device != RT_NULL);
        rt_device_control(touch_device, RTGRAPHIC_CTRL_POWEROFF, &knob_device_state);
        //touch_device_state = false;
    }
}

void lv_lcd_power_on(void)
{
    uint16_t framebuffer_color_format = FB_COLOR_FORMAT;
    rt_device_t lcd_device = rt_device_find("lcd");
    RT_ASSERT(lcd_device != RT_NULL);
    rt_device_control(lcd_device, RTGRAPHIC_CTRL_POWERON, NULL);
    rt_device_control(lcd_device, RTGRAPHIC_CTRL_SET_BUF_FORMAT, &framebuffer_color_format);
#if true
    // 开启定时器，延时150ms开启
    rt_delayed_work_init(&lcd_on_delay_work, lcd_on_delay_work_handler, NULL);
    rt_work_submit(&lcd_on_delay_work.work, 150);
#else
    // if(!touch_device_state)
    {
        rt_device_t touch_device = rt_device_find("touch");
        RT_ASSERT(touch_device != RT_NULL);
        rt_device_control(touch_device, RTGRAPHIC_CTRL_POWERON, NULL);
        //touch_device_state = true;
    }
    if(!knob_device_state)
    {
    #if BSP_USING_KNOB
        rt_device_t knob_device = rt_device_find("knob");
        RT_ASSERT(knob_device != RT_NULL);
        rt_device_control(knob_device, RTGRAPHIC_CTRL_POWERON, NULL);
        #else
        mt3503_control(MT3503_CMD_START, NULL); //后面 使用rt_device_control隔离
    #endif
        knob_device_state = true;
    }
#endif
}

void lv_lcd_set_aod_mode(bool enable)
{
    static uint8_t s_aod_mode_on = 0;

    uint8_t idle_mode_on = enable ? 1 : 0;
    if (s_aod_mode_on != idle_mode_on)
    {
        s_aod_mode_on = idle_mode_on;
        rt_device_t lcd_device = rt_device_find("lcd");
        RT_ASSERT(lcd_device != RT_NULL);
        rt_device_control(lcd_device, RTGRAPHIC_CTRL_SET_MODE, &idle_mode_on);
    }
}

void lv_lcd_set_brightness(int val)
{
    rt_device_t touch_device = rt_device_find("lcd");
    RT_ASSERT(touch_device != RT_NULL);
    rt_device_control(touch_device, RTGRAPHIC_CTRL_SET_BRIGHTNESS, &val);
}

void lv_lcd_set_hbm_brightness(int val)
{
    rt_device_t touch_device = rt_device_find("lcd");
    RT_ASSERT(touch_device != RT_NULL);
    rt_device_control(touch_device, RTGRAPHIC_CTRL_SET_HBM_BRIGHTNESS, &val);
}

// bool lv_lcd_is_power_on()
// {
//     int ret = 0;
//     rt_device_t touch_device = rt_device_find("lcd");
//     RT_ASSERT(touch_device != RT_NULL);
//     rt_device_control(touch_device, RTGRAPHIC_CTRL_GET_STATE, &ret);
//     return (ret == LCD_STATUS_DISPLAY_ON);
// }

#include "system_utils.h"

#define LCD_MSG_QUEUE_SIZE      10
#define LCD_MSG_EVENT           0x2000
static osThreadId_t lcd_task_thread = NULL;
static os_QueueHandle lcd_task_msg_queue = NULL;
static rt_sem_t s_lcxd_vsync;

static bool lcd_event_handle(EventData* eventData, void* data)
{
    uint32_t evt = eventData->arg1;
    uint32_t arg = eventData->arg2;
    void* p_context = eventData->data;

    rt_sem_take(s_lcxd_vsync, RT_WAITING_FOREVER);

    switch (evt)
    {
        // 关闭屏幕
        case LCD_EVT_SERVICE_POWER_DOWN:
        {
            lv_lcd_power_down();
            break;
        }

        // 开启屏幕
        case LCD_EVT_SERVICE_POWER_ON:
        {
            lv_lcd_power_on();
            break;
        }

        // AOD控制（Always On Display）
        case LCD_EVT_SERVICE_AOD_MODE:
        {
            lv_lcd_set_aod_mode(arg > 0 ? true : false);
            break;
        }

        // 设置亮度
        case LCD_EVT_SERVICE_SET_BRIGHTNESS:
        {
            lv_lcd_set_brightness(arg);
            break;
        }

        // 设置HBM亮度（High Brightness Mode）
        case LCD_EVT_SERVICE_SET_HBM_BRIGHTNESS:
        {
            lv_lcd_set_hbm_brightness(arg);
            break;
        }

        // 默认情况，处理未定义的事件
        default:
        {
            printf("Unknown LCD event received: %d\n", evt);
            break;
        }
    }

    rt_sem_release(s_lcxd_vsync);
    return true;
}

static void lcd_task_process(void* parameter)
{
    lcd_task_msg_queue = user_msg_queue_create(LCD_MSG_QUEUE_SIZE);
    message_registerTask(lcd_task_thread, lcd_task_msg_queue);
    system_bindlistener(lcd_task_thread, LCD_MSG_EVENT, lcd_event_handle, NULL);

    while (1)
    {
        message_GetAndDispatchedEvent(lcd_task_msg_queue, osWaitForever, 1);
    }
}

void lv_lcd_task_init(void)
{
    s_lcxd_vsync = rt_sem_create("lcd_vsync", 1, RT_IPC_FLAG_FIFO);

    osThreadAttr_t task_attr = {0};
    task_attr.name = "lcd_evt";
    task_attr.priority = osPriorityNormal5;
    task_attr.stack_size = 2048;

    lcd_task_thread = osThreadCreate(lcd_task_process, NULL, &task_attr);
    if (lcd_task_thread)
    {
        osThreadStartUp(lcd_task_thread);
    }
    else
    {
        rt_kprintf("[ERROR]lcd task create failed\n");
    }
}

void submit_lcd_event(LCD_EVT_SERVICE evt, int32_t arg, void* context)
{
    if(lcd_task_thread != NULL)
    {
        EventData* pEvent = message_allocEventData();
        message_eventDataInit(pEvent, evt, arg, context);
        system_send_event(lcd_task_thread, LCD_MSG_EVENT, pEvent);
    }
}

void lv_gpu_down(void)
{
    rt_sem_take(s_lcxd_vsync, RT_WAITING_FOREVER);

    rt_device_t lcd_device = rt_device_find("lcd");
    RT_ASSERT(lcd_device != RT_NULL);
    rt_device_control(lcd_device, RTGRAPHIC_CTRL_GPUOFF, NULL);

    rt_sem_release(s_lcxd_vsync);
}

void lv_gpu_on(void)
{
    rt_sem_take(s_lcxd_vsync, RT_WAITING_FOREVER);

    rt_device_t lcd_device = rt_device_find("lcd");
    RT_ASSERT(lcd_device != RT_NULL);
    rt_device_control(lcd_device, RTGRAPHIC_CTRL_GPUON, NULL);

    rt_sem_release(s_lcxd_vsync);
}

/**
 * @brief GUI内存使用情况命令
 * @param argc 参数个数
 * @param argv 参数数组
 * @return 错误码
 */
static rt_err_t gui_mem(int argc, char **argv)
{
#ifndef SIMULATOR
	lv_mem_monitor_t mon;
    rt_enter_critical();
	lv_mem_monitor(&mon);
    rt_exit_critical();
	rt_kprintf("gui_mem: total = %d, free = %d\n", mon.total_size, mon.free_size);
#endif
    return 0;
}

MSH_CMD_EXPORT(gui_mem, usage: gui_mem);


#include "qw_screenshot.h"
/**
 * @brief LCD屏幕截图命令
 * @param argc 参数个数
 * @param argv 参数数组
 * @return 错误码
 */
static int lcd_dump(int argc, char **argv)
{
    if (argc == 1)
	{
        screenshot_bmp_creat(lv_display_framebuffer_get(), NULL, NULL);
	}
    return RT_EOK;
}

#ifdef FINSH_USING_MSH
#include <finsh.h>
MSH_CMD_EXPORT(lcd_dump, lcd_dump);
#endif /* FINSH_USING_MSH */