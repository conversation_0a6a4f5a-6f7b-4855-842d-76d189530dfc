/************************************************************************​
*Copyright(c) 2025, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#include <stddef.h>
#include "alg_power_estimation.h"

static void power_estimator_reset(PowerEstimator *self);

//初始化功率估计器
void power_estimator_init(PowerEstimator *self, const PowerEstimatorInit *init)
{
    if (self != NULL && init != NULL)
    {
        self->body_weight = init->body_weight;
        self->bike_weight = init->bike_weight;

        power_estimator_reset(self);
    }
}

//功率估计
float power_estimator_exec(PowerEstimator *self, const PowerEstimatorInput *input)
{
    if (self == NULL || input == NULL)
    {
        return 0.0f;
    }

    //首次初始化
    if (self->cnt == 0)
    {
        self->timestamp = input->timestamp;
        self->spd = input->spd;
        self->cnt += 1;
        return 0.0f;
    }

    //时间戳检查
    if (input->timestamp < self->timestamp || input->timestamp - self->timestamp > 30)
    {
        power_estimator_reset(self);
        return 0.0f;
    }

    //时间变化量
    const uint32_t dt = input->timestamp - self->timestamp;

    if (dt == 0)
    {
        return 0.0f;
    }

    //加速度（m/s^2）
    const float acc = (input->spd - self->spd) / dt;

    self->timestamp = input->timestamp;
    self->spd = input->spd;
    self->cnt += 1;

    //速度为0，则功率为0
    if (input->spd < 0.001f)
    {
        return 0.0f;
    }

    //质量
    const float m = self->body_weight + self->bike_weight;  //kg
    //重力加速度
    const float g = 9.8f;   //m/s^2
    //滚阻系数（合并链条等处阻力系数）
    const float Cr = 0.005f;
    //风阻系数
    const float Ca = 0.335f;

    //滚动阻力
    const float Fr = m * g * Cr;
    //斜坡重力分量
    const float Fg = m * g * input->grade;
    //空气阻力
    const float Fa = input->spd * input->spd * Ca;

    //加速度对应的力
    float Facc = 0.0f;
    if (acc > 0.001f)
    {
        Facc = m * acc;
    }

    //合力
    const float F = Fr + Fg + Fa + Facc;

    if (F < 0.001f)
    {
        return 0.0f;
    }

    //合力乘以速度等于功率
    float P = F * input->spd;

    //限幅，人体功率很难超过1kw，而且只能维持很短时间
    if (P > 1000.0f)
    {
        P = 1000.0f;
    }

    return P;
}

//重置功率估计
static void power_estimator_reset(PowerEstimator *self)
{
    if (self != NULL)
    {
        self->cnt = 0;
        self->timestamp = 0;
        self->spd = 0.0f;
    }
}
