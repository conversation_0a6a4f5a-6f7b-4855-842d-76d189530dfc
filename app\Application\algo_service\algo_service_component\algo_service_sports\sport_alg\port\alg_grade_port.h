/************************************************************************​
*Copyright(c) 2025, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#ifndef ALG_GRADE_PORT_H
#define ALG_GRADE_PORT_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

#define ALG_GRADE_MAF_BUF_SIZE              5
#define ALG_GRADE_DIR_CALC_BUF_SIZE         5
#define ALG_GRADE_DIFF_DATA_BUF_SIZE        5
#define ALG_GRADE_CALC_TIMEOUT              2

typedef struct _alg_grade_input
{
    uint32_t timestamp;
    float dist;
    float alt;
} alg_grade_input_t;

typedef struct _alg_grade_output
{
    float grade;
    float dalt;
} alg_grade_output_t;

void alg_grade_init(void);

int alg_grade_exec(const alg_grade_input_t *input, alg_grade_output_t *output);

#ifdef __cplusplus
}
#endif

#endif