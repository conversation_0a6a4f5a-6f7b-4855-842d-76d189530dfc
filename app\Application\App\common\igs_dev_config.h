/************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   igs_dev_config.h
@Time    :   2021/07/07 17:28:46
*
************************************************************/
#ifndef IGS_DEV_CONFIG_H_
#define IGS_DEV_CONFIG_H_

#if defined(__cplusplus)
extern "C" {
#endif

#include "qw_macro_config.h"
#include "max_config.h"
#include "basictype.h"
#include "version_infor.h"



/*********************
*      MACROS
*********************/
//首次创建文件夹数量
#define DEFAULT_WORKOUT_NUMBER 4

#define HARDWARE_VERSION 3

//默认版本号
#define DEFAULT_SOFTWARE_VERSION 86
#define DEFAULT_DEBUG_VERSION 4

// Ensure version macros are defined even if version_infor.h exists but doesn't define them
#ifdef SOFTWARE_VERSION
#if (SOFTWARE_VERSION == 12345)
    #undef SOFTWARE_VERSION
    #define SOFTWARE_VERSION DEFAULT_SOFTWARE_VERSION
#endif
#else
    #define SOFTWARE_VERSION DEFAULT_SOFTWARE_VERSION
#endif

#ifdef DEBUG_VERSION
#if (DEBUG_VERSION == 67890)
    #undef DEBUG_VERSION
    #define DEBUG_VERSION DEFAULT_DEBUG_VERSION
#endif
#else
    #define DEBUG_VERSION DEFAULT_DEBUG_VERSION
#endif

#define MAP_PACKAGE_VERSION 100
#define FONT_PACKAGE_VERSION 131
#define IMG_PACKAGE_VERSION 100
#define GPS_PACKAGE_VERSION 111
#define SYS_CONFIG_VERSION 103

//加入公共升级文件名 update
#define BIN_UPDATE_FILENAME_APP            "0:/update.bin"
#define BIN_UPDATE_FILENAME_BOOT           "0:/update_boot.bin"
#define RSC_UPDATE_FILENAME_ALL            "update.rsc"

//rsc解包路径
typedef enum
{
    enum_patch_type_none,                   //删除
    enum_patch_type_delay_update,           //资源延迟升级
    enum_patch_type_font,
    enum_patch_type_img,
    enum_patch_type_theme,

    enum_patch_type_root,
    enum_patch_type_fit,
    enum_patch_type_navi,
    enum_patch_type_setting,
    enum_patch_type_sys,
    enum_patch_type_workout,
    enum_patch_type_schedule,
    enum_patch_type_segment,

    enum_patch_type_gps_update,

    enum_patch_type_res_root,

    enum_patch_type_factory_update,         //工厂出厂手动加载资源升级
    enum_patch_type_lcpu_update,            //升级lcpu固件

    enum_patch_type_end,
} patch_type_e;

//主分驱下文件路径
typedef enum
{
    enum_mian_disk_base,        //根目录
    enum_mian_disk_activity,    //活动文件
    enum_mian_disk_total,       //统计数据
    enum_mian_disk_workouts,    //训练文件
    enum_mian_disk_schedule,    //训练计划
    enum_mian_disk_segments,    //赛段文件
    enum_mian_disk_courses,     //导航文件
    enum_mian_disk_location,    //高度打点文件
    enum_mian_disk_settings,    //系统设置
    enum_mian_disk_system,      //系统其他,日志等
    enum_mian_disk_health,      //健康文件
    enum_mian_disk_notification,//智能通知

    enum_mian_disk_path_end,
} main_disk_path_e;

//隐藏分驱下rsc资源文件路径
typedef enum
{
    enum_res_path_temp,                 //临时文件,rsc解包使用,升级完删除
    enum_res_path_backup,               //备份文件夹
    enum_res_path_delay_update,         //延迟升级
    enum_res_path_font,                 //字体文件夹
    enum_res_path_img,                  //外挂图片文件夹
    enum_res_path_theme,                //主题参数资源文件夹

    enum_res_path_factory,              //工厂模式主动触发的升级项

    enum_res_path_wifi,                 //wifi下载使用的文件夹

    enum_res_path_end,
} res_path_e;

//主分区目录
extern const char* g_fileDirName[enum_mian_disk_path_end];

//隐藏分区
extern const char* g_assetDirName[enum_res_path_end];

//下述目录加了/
extern const char* ROOT_PATH;
extern const char* BASE_PATH;
extern const char* STATISTICAL_PATH;
extern const char* WORKOUTS_PATH;
extern const char* SCHEDULE_PATH;
extern const char* SEGMENTS_PATH;
extern const char* COURSES_PATH;
extern const char* SLEEP_PATH;
extern const char* SETTINGS_PATH;
extern const char* SYSTEM_PATH;
extern const char* DIAL_PATH;

/*常用文件名*/
extern const char* DEVICE_CFG_FILE;
extern const char* SYSTEM_LOG_DIR;
extern const char* GPS_INFO_FILE;
extern const char* FCT_CHECK_FILE;
extern const char* DEVICE_BIN_FILE;
extern const char* SCREENSHOTS_DIR;
extern const char* GPS_EPO_FILE;
extern const char* TRACK_RIDE_PATH;
extern const char* SYSTEM_LOG_HFAULT_DIR;

// 文件夹名称
extern const char* ACTIVITY_FOLDER;
extern const char* STATISTICAL_FOLDER;
extern const char* WORKOUTS_FOLDER;
extern const char* SCHEDULE_FOLDER;
extern const char* SEGMENTS_FOLDER;
extern const char* COURSES_FOLDER;
extern const char* SLEEP_FOLDER;
extern const char* SETTINGS_FOLDER;
extern const char* SYSTEM_FOLDER;
extern const char* SCHEDULE_COURSE;
extern const char* SCHEDULE_COURSE_CACHE;
extern const char* SCHEDULE_CACHE;

/*目标文件名*/
extern char COURSES_FILE_NAME[20];	 //也支持xml类型,此处仅为默认文件名

// device.bin中的信息
extern const char* DEVICE_NAME;
extern const char* MANUFACTURER_NAME;

//内置训练文件
extern const char* g_defaultWorkoutName[DEFAULT_WORKOUT_NUMBER];

/// <summary>
/// 在系统启动时调用,获取各种版本号信息,填充到参数结构中
/// </summary>
void device_version_info_init();

#if defined(__cplusplus)
}
#endif

#endif /* APP_COMMON_IGS_DEV_CONFIG_H_ */
