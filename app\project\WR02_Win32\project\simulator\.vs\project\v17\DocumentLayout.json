{"Version": 1, "WorkspaceRootPath": "E:\\Snowa\\app\\project\\WR02_Win32\\project\\simulator\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\Launcher\\LauncherView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos_app\\navi\\navi_file_manager.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|E:\\Snowa\\qw_platform\\qwos_app\\navi\\navi_new\\port\\navi_port.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|E:\\Snowa\\app\\Application\\sensorhub\\sensorhub_common.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\basic_app_module\\activity_record\\activity_fit_app.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelectView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwMenuItem\\QwMenuView.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelect.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\misc\\lv_txt.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\font\\lv_font.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\Launcher\\Launcher.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|E:\\Snowa\\qw_platform\\qwos\\inc\\qw_list.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\SelectDialView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\js_data_servers.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\transitions\\SlideTransition.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\Container.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\QwWeekBarChart.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\Tools\\ToolsMenu\\ToolsMenuView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\TraingStatus\\HistroyTrendPage.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\MvcApp.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files (x86)\\Windows Kits\\10\\Include\\10.0.22621.0\\ucrt\\corecrt_malloc.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|E:\\Snowa\\qw_platform\\qwos\\inc\\qw_os_adaptor.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 8, "Children": [{"$type": "Document", "DocumentIndex": 3, "Title": "sensorhub_common.h", "DocumentMoniker": "E:\\Snowa\\app\\Application\\sensorhub\\sensorhub_common.h", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\sensorhub\\sensorhub_common.h", "ToolTip": "E:\\Snowa\\app\\Application\\sensorhub\\sensorhub_common.h", "RelativeToolTip": "..\\..\\..\\..\\Application\\sensorhub\\sensorhub_common.h", "ViewState": "AgIAAE0AAAAAAAAAAAAswGAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-09-06T10:26:37.235Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "activity_fit_app.c", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\basic_app_module\\activity_record\\activity_fit_app.c", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\basic_app_module\\activity_record\\activity_fit_app.c", "ToolTip": "E:\\Snowa\\app\\Application\\App\\basic_app_module\\activity_record\\activity_fit_app.c", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\basic_app_module\\activity_record\\activity_fit_app.c", "ViewState": "AgIAAOIBAAAAAAAAAAAYwPIBAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000423|", "WhenOpened": "2025-09-06T10:11:45.362Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "qw_os_adaptor.h", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\inc\\qw_os_adaptor.h", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\inc\\qw_os_adaptor.h", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\inc\\qw_os_adaptor.h", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\inc\\qw_os_adaptor.h", "ViewState": "AgIAAAYAAAAAAAAAAAAAAAQAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-09-06T10:00:17.785Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "navi_port.h", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos_app\\navi\\navi_new\\port\\navi_port.h", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos_app\\navi\\navi_new\\port\\navi_port.h", "ToolTip": "E:\\Snowa\\qw_platform\\qwos_app\\navi\\navi_new\\port\\navi_port.h", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos_app\\navi\\navi_new\\port\\navi_port.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-09-06T10:00:16.396Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "DialDataSelect.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelect.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelect.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelect.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelect.cpp", "ViewState": "AgIAABMAAAAAAAAAAAAAwCMAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-06T03:47:46.834Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "lv_txt.c", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\misc\\lv_txt.c", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\misc\\lv_txt.c", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\misc\\lv_txt.c", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\misc\\lv_txt.c", "ViewState": "AgIAACQBAAAAAAAAAAAAwDQBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000423|", "WhenOpened": "2025-09-06T03:47:10.156Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "lv_font.c", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\font\\lv_font.c", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\font\\lv_font.c", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\font\\lv_font.c", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\font\\lv_font.c", "ViewState": "AgIAAHsAAAAAAAAAAAAAwIsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000423|", "WhenOpened": "2025-09-06T03:47:07.721Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "QwMenuView.hpp", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwMenuItem\\QwMenuView.hpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwMenuItem\\QwMenuView.hpp", "ToolTip": "E:\\Snowa\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwMenuItem\\QwMenuView.hpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos_app\\GUI\\QwWidget\\QwMenuItem\\QwMenuView.hpp", "ViewState": "AgIAAJkAAAAAAAAAAAAAwLMAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-09-06T03:46:29.62Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "LauncherView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Launcher\\LauncherView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Launcher\\LauncherView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Launcher\\LauncherView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Launcher\\LauncherView.cpp", "ViewState": "AgIAAF8AAAAAAAAAAAAAwG8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-05T02:16:13.776Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "navi_file_manager.c", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos_app\\navi\\navi_file_manager.c", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos_app\\navi\\navi_file_manager.c", "ToolTip": "E:\\Snowa\\qw_platform\\qwos_app\\navi\\navi_file_manager.c", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos_app\\navi\\navi_file_manager.c", "ViewState": "AgIAALoBAAAAAAAAAAAswMYBAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000423|", "WhenOpened": "2025-09-06T10:32:45.479Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "DialDataSelectView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelectView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelectView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelectView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelectView.cpp", "ViewState": "AgIAAL8AAAAAAAAAAAAAwNgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-06T03:45:56.387Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "qw_list.h", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\inc\\qw_list.h", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\inc\\qw_list.h", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\inc\\qw_list.h", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\inc\\qw_list.h", "ViewState": "AgIAAEoAAAAAAAAAAAAAwFoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-09-05T03:52:13.268Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "js_data_servers.c", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\js_data_servers.c", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\js_data_servers.c", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\js_data_servers.c", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\js_data_servers.c", "ViewState": "AgIAAEEBAAAAAAAAAAAswFIBAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000423|", "WhenOpened": "2025-09-05T03:51:23.707Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "corecrt_malloc.h", "DocumentMoniker": "C:\\Program Files (x86)\\Windows Kits\\10\\Include\\10.0.22621.0\\ucrt\\corecrt_malloc.h", "ToolTip": "C:\\Program Files (x86)\\Windows Kits\\10\\Include\\10.0.22621.0\\ucrt\\corecrt_malloc.h", "ViewState": "AgIAAEcAAAAAAAAAAAAswFgAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-09-05T03:51:20.729Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "Launcher.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Launcher\\Launcher.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Launcher\\Launcher.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Launcher\\Launcher.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Launcher\\Launcher.cpp", "ViewState": "AgIAABUAAAAAAAAAAAAAwCIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-05T03:39:04.816Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "SlideTransition.hpp", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\transitions\\SlideTransition.hpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\transitions\\SlideTransition.hpp", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\transitions\\SlideTransition.hpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\transitions\\SlideTransition.hpp", "ViewState": "AgIAAMAAAAAAAAAAAAAAwNAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-09-05T03:38:11.403Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "Container.cpp", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\Container.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\Container.cpp", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\Container.cpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\containers\\Container.cpp", "ViewState": "AgIAABQBAAAAAAAAAAAAwCQBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-05T03:38:09.394Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "SelectDialView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\SelectDialView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\SelectDialView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\SelectDialView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\SelectDialView.cpp", "ViewState": "AgIAAJEBAAAAAAAAAAAAwKIBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-05T02:14:37.206Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "ToolsMenuView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Tools\\ToolsMenu\\ToolsMenuView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Tools\\ToolsMenu\\ToolsMenuView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Tools\\ToolsMenu\\ToolsMenuView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Tools\\ToolsMenu\\ToolsMenuView.cpp", "ViewState": "AgIAAAUBAAAAAAAAAAAAwBMBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-04T11:45:04.222Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "QwWeekBarChart.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\QwWeekBarChart.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\QwWeekBarChart.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\QwWeekBarChart.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\QwWeekBarChart.cpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-04T03:39:12.716Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "HistroyTrendPage.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\TraingStatus\\HistroyTrendPage.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\TraingStatus\\HistroyTrendPage.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\TraingStatus\\HistroyTrendPage.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\TraingStatus\\HistroyTrendPage.cpp", "ViewState": "AgIAAG8AAAAAAAAAAADwv30AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-04T03:03:32.61Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "MvcApp.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\MvcApp.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\MvcApp.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\MvcApp.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\MvcApp.cpp", "ViewState": "AgIAAPYBAAAAAAAAAAAYwAICAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-02T01:41:34.938Z"}]}]}]}