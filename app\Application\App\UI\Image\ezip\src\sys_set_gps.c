#include "lvgl.h" 

#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP ALIGN(4)
#endif

#ifndef eZIP_RGBARGB888A
#define eZIP_RGBARGB888A
#endif


LV_ATTRIBUTE_MEM_ALIGN_EZIP const  uint8_t sys_set_gps_map[] SECTION(".ROM3_IMG_EZIP.sys_set_gps") = { 
    0x00,0x00,0x06,0xdc,0x46,0x08,0x20,0x00,0x00,0x48,0x00,0x48,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0x03,0x00,0x00,0x00,0x94,0x00,0x00,0x03,0x44,0x00,0x00,0x05,0xf4,
    0x3c,0xb3,0x8b,0x8d,0xa2,0x8a,0xe2,0xf8,0x39,0xd3,0x5a,0x02,0xbc,0xd1,0x08,0x0d,0x21,0x45,0xec,0x6e,0x31,0xe5,0x01,0x13,0x52,0x63,0x03,0x84,0xf2,0x21,0x2f,0x18,
    0x4c,0x34,0x52,0x0d,0xf6,0xc1,0x44,0x91,0x90,0x90,0xa8,0x4f,0xed,0xb6,0xb2,0x5b,0xdb,0xee,0xf6,0xc1,0x27,0x13,0x51,0x21,0x31,0x44,0x6d,0x22,0xd5,0x17,0x79,0xe0,
    0xc1,0x42,0xa8,0x81,0x26,0x10,0xe1,0x41,0x51,0xa4,0xbb,0x8b,0xd2,0x86,0x10,0x04,0x81,0x17,0xa1,0x01,0x65,0x8e,0xe7,0xee,0x74,0xd3,0x8f,0x9d,0xaf,0xed,0xdc,0x7b,
    0x67,0x77,0x39,0x64,0x76,0x2e,0x9d,0x99,0x3b,0x73,0x7f,0xf3,0xbf,0xff,0x7b,0xee,0x1d,0x00,0x00,0x00,0x44,0x28,0xa1,0xa0,0xee,0x48,0x03,0x3c,0xc2,0xb5,0x5c,0x6c,
    0x02,0xc0,0x08,0x20,0xd4,0xf3,0x56,0x07,0x26,0xd4,0xf2,0x7e,0xa9,0x75,0x12,0xff,0x43,0x78,0xc0,0xa5,0x1b,0xbc,0x5d,0xe7,0xf2,0x15,0x3e,0x7e,0x09,0x90,0x2e,0x40,
    0xcd,0xbd,0x51,0x4c,0x5c,0xbf,0x2f,0xf3,0x99,0x42,0x05,0x44,0x1d,0x0d,0x11,0xa8,0xaa,0xda,0x0e,0x84,0x9b,0x81,0x68,0x43,0x0e,0x46,0xe0,0x4a,0xe1,0x04,0xc3,0x3a,
    0x06,0xf0,0xdf,0x10,0x26,0xff,0xfc,0xab,0xec,0x00,0x51,0xc7,0x33,0x4f,0x81,0x41,0x6d,0x5c,0x7a,0x85,0xff,0xdb,0xac,0xf4,0x66,0x08,0x5f,0x01,0x98,0x07,0xb1,0x3f,
    0x7b,0xb6,0xe4,0x01,0x51,0x2c,0xba,0x83,0x6f,0xf7,0x36,0x17,0x5f,0xd2,0x2f,0x55,0x38,0xca,0xa0,0x3e,0xc4,0x54,0xf6,0x52,0xc9,0x01,0xa2,0xae,0xe8,0xeb,0xdc,0x85,
    0xde,0xe5,0xe2,0x73,0xe1,0x9b,0x1c,0x7d,0x80,0xa9,0x4c,0x5f,0x49,0x00,0xa2,0xce,0xe8,0x8b,0x80,0x46,0x8c,0x4b,0x2d,0x50,0x5a,0x71,0x12,0x4c,0xda,0x83,0x03,0x99,
    0x3f,0x42,0x01,0x44,0x1d,0xd1,0xa7,0x01,0x31,0xc9,0x35,0xb7,0x95,0x18,0x98,0x99,0x5d,0xee,0x26,0x1b,0xf9,0x1b,0x98,0xcc,0x0c,0x6b,0x05,0xc4,0x3e,0xb3,0x9f,0xab,
    0xfc,0x88,0x8b,0x35,0x50,0x16,0x81,0x6d,0x98,0x1c,0x1b,0x52,0x0e,0x88,0x3a,0x23,0x4f,0x02,0x56,0x7d,0x3a,0x35,0x32,0x95,0x59,0xb8,0x43,0x0a,0x0c,0x88,0xba,0x57,
    0x6f,0xe1,0x3e,0xfd,0x05,0x17,0x57,0x42,0xd9,0x06,0x6d,0x77,0xea,0x6e,0x81,0x00,0x51,0xac,0xf1,0x2d,0xde,0x1d,0x86,0x72,0x0f,0xe1,0x49,0x44,0x2d,0x76,0xc6,0x6d,
    0x04,0xf0,0x9b,0xae,0x8a,0x80,0x63,0xc9,0x64,0x29,0x18,0x78,0xc8,0xee,0xd0,0xbc,0x00,0x51,0x67,0x63,0x8a,0x6b,0xed,0xab,0x08,0x38,0xd3,0xb1,0x95,0x53,0x93,0xee,
    0xc0,0x5d,0x2c,0x07,0x07,0xa1,0x03,0x2a,0x35,0xc8,0x5c,0x33,0x33,0xe3,0x36,0x8a,0xee,0x56,0x95,0x0c,0xc7,0x42,0x72,0x60,0x5e,0x0a,0x52,0x6d,0xc8,0x23,0xe3,0x85,
    0xab,0x14,0xad,0x2b,0x17,0x85,0xe4,0x49,0x66,0x4b,0x7e,0x82,0x5b,0x5d,0xc4,0x50,0x7e,0x58,0x05,0x94,0x91,0x89,0x49,0xe8,0x39,0x7d,0xdb,0xf1,0x1c,0x01,0x29,0xbe,
    0x61,0x89,0x66,0x58,0xc6,0x3e,0xfe,0x39,0xeb,0x4b,0x41,0x56,0x12,0x68,0xfc,0x24,0x3b,0xcf,0x49,0x30,0x14,0x37,0x30,0x76,0x11,0xdf,0x58,0x0b,0x09,0xde,0xf4,0xc4,
    0xbf,0x75,0x62,0x3d,0xc9,0xdb,0x83,0x72,0x19,0xb2,0x3c,0x38,0x02,0x0c,0x26,0xd3,0x45,0xc3,0x11,0x21,0xae,0xd9,0x3c,0x78,0x4d,0x13,0xa0,0xea,0x5d,0x9e,0x26,0x6d,
    0xcd,0xad,0xe4,0x4d,0x1f,0xe6,0xa3,0x1a,0xbb,0x6e,0xa9,0x05,0x12,0xe1,0x4e,0xd7,0x2e,0x96,0x9b,0x95,0x1b,0xf8,0xbb,0xac,0x89,0xa7,0xec,0x86,0x09,0x4f,0x3a,0xb5,
    0x7b,0x85,0x5a,0x48,0x35,0xff,0x2c,0x76,0x56,0x90,0x58,0xb2,0x90,0x38,0x2b,0xef,0x39,0x73,0x47,0xba,0xc1,0x27,0x02,0xaa,0xd1,0x33,0x1e,0x2e,0x5e,0x6f,0x38,0x2f,
    0x76,0xc9,0x5b,0xcf,0x11,0x0d,0xb1,0x1b,0xc6,0x6d,0x47,0x2c,0x36,0x61,0xb1,0xf7,0x33,0x6a,0xf5,0xa8,0x06,0x44,0xb8,0xce,0x7e,0x98,0xb7,0x56,0x02,0xa5,0xdd,0xc7,
    0xab,0x21,0x4e,0xa3,0x93,0x80,0x2a,0x94,0xe7,0x06,0x57,0xc0,0x57,0x36,0xb2,0x19,0xd0,0x64,0xd8,0xae,0x21,0x4b,0x5c,0x26,0xf5,0x52,0x8e,0xdb,0xd0,0x9d,0xf7,0x19,
    0x37,0x35,0x29,0x55,0x11,0x41,0x43,0x61,0x17,0xb3,0x16,0xd8,0x41,0x87,0xf7,0x88,0x86,0xfb,0x79,0xfb,0x22,0x51,0x0c,0xf2,0x12,0x02,0xc4,0x72,0xa3,0xf0,0xd3,0x8c,
    0xbe,0xaf,0x0f,0x5e,0x0d,0x9f,0xeb,0x4d,0x8e,0x80,0x38,0x1b,0x57,0x14,0x75,0x73,0x14,0x94,0xfb,0x6e,0xa5,0x2d,0x42,0x9b,0x6b,0xf9,0xef,0x62,0x0b,0x8c,0x59,0x5f,
    0x3c,0x15,0x7c,0xd4,0x73,0x92,0x7f,0xb1,0x70,0x5a,0xeb,0x17,0x86,0xb1,0x90,0x86,0xd3,0x0a,0x42,0xf3,0x35,0x9d,0x2a,0x91,0xe9,0x1b,0x2a,0xe1,0xcd,0x00,0x04,0x2f,
    0xab,0xb8,0xc1,0x26,0x97,0x87,0x2f,0x06,0x92,0xec,0x44,0xb3,0x28,0x40,0xd4,0xd1,0x10,0xe1,0x5d,0xb3,0xee,0x9b,0xfb,0x35,0xd7,0xdc,0xb2,0x88,0x0b,0x4c,0x95,0x5e,
    0xf6,0x3f,0x00,0x00,0x44,0xe0,0xa0,0xae,0xc6,0x7d,0x40,0xf0,0x09,0x28,0x0a,0x4c,0xa6,0x1d,0x8f,0xc5,0x37,0xd6,0x42,0x82,0x37,0xb7,0xd8,0x3c,0x78,0x0d,0x46,0xc6,
    0xef,0xdb,0x1e,0x6b,0x5d,0xb9,0x08,0x4e,0xed,0x5e,0xa1,0xea,0xd1,0xc1,0xc8,0xfd,0x12,0x6e,0x01,0x85,0x11,0x77,0x01,0xd0,0x73,0xfa,0x76,0x0e,0x80,0x5d,0x24,0xf8,
    0x98,0x80,0xeb,0x04,0x27,0x57,0xf7,0x86,0x25,0x2a,0x1f,0x1d,0xaa,0xad,0x57,0x4c,0xeb,0x59,0x41,0xa1,0x85,0x00,0x20,0x40,0x08,0x35,0x88,0xd8,0x54,0xbf,0x30,0x07,
    0xce,0x2b,0xc4,0xf9,0xf9,0x6b,0x94,0xa9,0x9f,0xba,0x23,0x0d,0x60,0x1a,0x59,0xd5,0x10,0xdc,0xba,0x59,0x10,0x65,0x7a,0x75,0xcf,0xe0,0x5d,0xec,0x11,0xae,0xd5,0xa1,
    0x92,0xb8,0x82,0x86,0xa8,0x86,0xc3,0x31,0x29,0x3c,0xa8,0x49,0x07,0x20,0xd9,0x8d,0x89,0xab,0x87,0xc3,0xde,0x0c,0x77,0x0d,0x30,0x30,0xa2,0xcb,0x6b,0x64,0x36,0x4a,
    0x83,0x7a,0xd8,0x17,0xe0,0xaa,0xc1,0x94,0xea,0x75,0x01,0x92,0xd5,0x28,0x2d,0xea,0xb1,0x14,0x34,0x6e,0x30,0xa5,0x3a,0x9d,0x23,0x96,0x8c,0xc6,0x25,0x74,0x01,0x42,
    0xca,0x1a,0x60,0x42,0xad,0x4e,0x40,0x41,0x1b,0xa7,0x4d,0x3d,0x56,0x5c,0x16,0x0a,0x5a,0xaa,0x3b,0xef,0x09,0xd2,0xc8,0x84,0x4e,0x40,0x55,0x55,0x17,0xab,0xc3,0x48,
    0x0c,0xf3,0x8d,0xf4,0x93,0x0c,0xce,0x0c,0x95,0x53,0x0a,0xbb,0x21,0x1e,0x7b,0x2f,0x5f,0x14,0x26,0x1d,0x4a,0x0e,0xdd,0xca,0xd9,0x72,0x51,0xe7,0x6b,0xc8,0x9a,0x67,
    0x1b,0x34,0x9d,0xb3,0x12,0x45,0x84,0x07,0xa1,0x00,0xe2,0xc6,0x16,0xd3,0xd5,0x54,0xcf,0xb9,0x0a,0x0d,0xda,0x18,0xcd,0x4f,0x56,0x6f,0x84,0x35,0x07,0xf3,0xeb,0x27,
    0xda,0xd5,0x23,0xc2,0x34,0x47,0xf2,0x80,0xae,0x87,0x37,0x4d,0xf5,0x67,0xd8,0x9a,0xbd,0xc7,0x12,0xd0,0x40,0xe6,0x44,0xbe,0x8b,0x5d,0x09,0x13,0x90,0x50,0x91,0x9b,
    0x3a,0x34,0x0f,0xeb,0xf9,0x18,0x9a,0x9e,0xac,0x9a,0x70,0x09,0x42,0x0e,0x27,0x7f,0x11,0xe0,0x12,0x61,0x00,0x32,0xcd,0x63,0xd3,0x80,0x90,0x2e,0x84,0xb1,0xfe,0xe3,
    0xc7,0xb0,0xb5,0x1b,0xb3,0x35,0x7a,0x4d,0xe0,0x40,0x76,0x70,0x1a,0x50,0xcd,0xbd,0x51,0x1d,0x40,0xc4,0xaa,0xa1,0xd8,0xc4,0x2a,0xa1,0x5d,0x97,0x9a,0xab,0x94,0xb8,
    0x43,0xd7,0x9b,0x5b,0x97,0x82,0x05,0xa0,0xd4,0xec,0xf9,0xaa,0x80,0xd6,0xd9,0x38,0xcc,0xa5,0x6d,0xd2,0xfd,0x85,0x1b,0x30,0x33,0x19,0xf4,0x5a,0x3f,0xce,0x9f,0xef,
    0xf7,0xbc,0xb9,0x3e,0x25,0xa1,0x3b,0x9e,0xc7,0x64,0xba,0xb9,0x10,0x50,0x2c,0xba,0x9f,0x8b,0x1f,0xab,0x84,0xe3,0xb7,0x31,0x42,0x21,0x6e,0xa6,0x9d,0x57,0x90,0xd3,
    0x68,0x17,0x28,0x1d,0x30,0x60,0x07,0xf6,0xa5,0x8f,0xdb,0x00,0x5a,0xb5,0x0c,0xe0,0x89,0x1b,0xb2,0xbb,0x54,0x31,0x43,0xbc,0x5b,0x66,0xdd,0x73,0xe6,0x8e,0xad,0x77,
    0x49,0xfe,0xc2,0x71,0x84,0xd5,0xf3,0x66,0xe1,0x92,0x50,0xde,0x9b,0xba,0x1a,0xbf,0xe4,0x49,0x47,0xbb,0x6a,0x05,0xa9,0x8c,0x00,0x0a,0xba,0x05,0x64,0xae,0xc1,0x54,
    0xf6,0x96,0xfd,0x57,0x0d,0x6b,0x6c,0x3b,0xc8,0x1a,0x6b,0x57,0x91,0x25,0xab,0x04,0x25,0x80,0x88,0xaf,0x20,0xc1,0xfc,0x07,0xdf,0xb7,0x83,0x33,0x4b,0x41,0x53,0x66,
    0xfd,0x0d,0xff,0xa5,0x4d,0xd5,0x48,0x36,0x32,0x31,0x09,0x3f,0xf2,0xe6,0xa7,0xbb,0xb8,0x01,0x99,0x99,0x02,0x04,0x9f,0x82,0xe0,0xe7,0x98,0x1c,0xdb,0xeb,0xbc,0xea,
    0x3a,0x0b,0x50,0xa4,0x89,0x27,0x69,0xbf,0x85,0x99,0x13,0x79,0x81,0x91,0xbc,0x62,0x78,0x01,0xfa,0x33,0xcd,0x0c,0x81,0x7c,0x01,0xb2,0x20,0x45,0xbb,0x01,0xb1,0x17,
    0x1e,0x87,0xa8,0x82,0x67,0xb1,0x37,0xfd,0xb3,0xfb,0xba,0xbd,0x5d,0x32,0x19,0x6b,0x14,0x13,0xb5,0xad,0x15,0x0d,0x87,0xf0,0x55,0x4c,0x8d,0x7d,0xe7,0xef,0xdb,0x7c,
    0xc1,0x5c,0x84,0xf6,0xb0,0xe8,0x6e,0x56,0x2c,0x1c,0xc4,0xf7,0xfc,0xc0,0x71,0x54,0xd0,0x54,0xf2,0xf8,0x02,0x1f,0xfe,0xe1,0x71,0x55,0x8e,0xbb,0x82,0x72,0xdf,0xd2,
    0x33,0xc3,0xfc,0xdb,0x56,0x39,0xaa,0x61,0x43,0x16,0x9e,0x53,0x04,0x1c,0x11,0xff,0x03,0x00,0x00,0x00,0x45,0xf0,0x08,0x8a,0xad,0xde,0xc5,0xbf,0x47,0xa1,0xbc,0xe3,
    0x10,0xf4,0xa7,0xf7,0x22,0x02,0x15,0x7b,0xa1,0xe1,0x75,0x02,0x26,0xc7,0x86,0x18,0xd0,0x76,0xae,0xfa,0x66,0x19,0x82,0xf9,0x9b,0x5b,0xd0,0x8e,0xc9,0xf4,0x3b,0xf3,
    0x81,0xe3,0x0b,0x90,0x05,0x29,0x33,0x0c,0x44,0x2d,0x5c,0x3c,0x59,0x46,0x70,0x8e,0xc0,0xc3,0x47,0x6b,0xf8,0x05,0x7f,0x1d,0xa4,0x12,0x2c,0xf6,0x02,0xea,0x8c,0x76,
    0x03,0x62,0x6f,0x09,0x83,0x39,0xcf,0xaf,0x3d,0x8e,0x7d,0xe9,0xe3,0x32,0x2a,0xc3,0xf9,0x5c,0x44,0x9d,0x91,0x26,0x16,0xdf,0x01,0xbe,0xba,0xad,0x64,0xb0,0x10,0x4d,
    0x30,0x98,0x14,0xf6,0x67,0x3e,0x93,0x59,0x2d,0x06,0x7a,0xa6,0xae,0xc8,0xf3,0x0c,0x6a,0x1f,0xf7,0xee,0xf6,0xf0,0xc0,0xc0,0xb7,0x40,0xe6,0xf7,0x38,0x90,0x1d,0x54,
    0x51,0x3d,0x4a,0x79,0xc6,0xd8,0xaa,0x65,0x00,0xd5,0xbb,0x80,0x70,0x27,0xd7,0xb8,0x4d,0x21,0x8e,0x49,0x56,0xca,0x39,0x40,0x63,0x14,0x4c,0x73,0x04,0x07,0x32,0x27,
    0x54,0xf3,0x47,0xe9,0x2f,0x34,0xb1,0x7c,0x11,0x3c,0x5c,0xbc,0x9e,0x61,0xad,0x63,0xc9,0x37,0xf1,0x1b,0x6e,0xe0,0x3f,0x2f,0xe7,0xad,0x8e,0xcb,0x0b,0xf8,0x8e,0xe8,
    0x0d,0x01,0xee,0xf2,0x59,0x57,0x79,0x3f,0x0e,0x48,0x59,0xfe,0xdb,0x65,0x56,0xea,0xaf,0x6c,0xb8,0xbf,0xe8,0x16,0xe8,0xff,0x3b,0x37,0x82,0x1d,
};


#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER ALIGN(4)
#endif
LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER const eZIP_RGBARGB888A  lv_img_dsc_t sys_set_gps SECTION(".ROM3_IMG_EZIP_HEADER.sys_set_gps") = { 
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .header.always_zero = 0,
  .header.w = 72,
  .header.h = 72,
  .data_size  = 1756,
  .data = sys_set_gps_map
};
