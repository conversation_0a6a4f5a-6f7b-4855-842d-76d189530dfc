#ifndef NAVI_TURN_H
#define NAVI_TURN_H

#ifdef __cplusplus
extern "C" {
#endif

#include "navi_tnav.h"

//导航转向扩展，目的是为了导出转向结束点信息，用于可能的转向路名检测
typedef struct _NaviTurnEx
{
    NaviWaypointDc start;                   //转向起点
    NaviWaypointDc end;                     //转向终点
    float angle;                            //转向角度
} NaviTurnEx;

//转向计算器
typedef struct _NaviTurnCalculator
{
    NaviWaypointDc last_wp;                 //上一个路点
    NaviWaypointDc cur_wp;                  //当前路点
    NaviWaypointDc start;                   //转向起点
    float angle_sum;                        //转向角度求和（°）
    uint32_t cnt;                           //路点计数
    int32_t dir;                            //转向方向，0-直行 1-右转 -1-左转
} NaviTurnCalculator;

//转向分段器
typedef struct _NaviTurnSegmentor
{
    NaviTurnSegment *seg_buf;               //缓冲区，保存转向分段
    uint32_t capacity;                      //缓冲区容量
    uint32_t len;                           //缓冲区已保存转向分段数量
    uint32_t interval;                      //当前分段间隔
    uint32_t INTERVAL;                      //起始分段间隔
    uint32_t cnt;                           //转向计数
} NaviTurnSegmentor;

//转向匹配器
typedef struct _NaviTurnMatcher
{
    NaviTurnList *turn_list;                //转向list
    NaviTurnSegmentArray *seg_array;        //转向分段数组
} NaviTurnMatcher;

int navi_turn_calculator_exec(NaviTurnCalculator *self, const NaviWaypointDc *wpdc, NaviTurnEx *output);

int navi_turn_calculator_end(NaviTurnCalculator *self, NaviTurnEx *output);

void navi_turn_calculator_reset(NaviTurnCalculator *self);

int navi_turn_segmentor_exec(NaviTurnSegmentor *self, const NaviTurn *turn);

int navi_turn_segmentor_end(NaviTurnSegmentor *self, float dist_end);

int navi_turn_segmentor_data_get(NaviTurnSegmentor *self, NaviTurnSegmentArray *output);

void navi_turn_segmentor_reset(NaviTurnSegmentor *self);

int navi_turn_matcher_exec(NaviTurnMatcher *self, float dist, uint8_t is_reverse, NaviTurn *output, uint32_t *idx);

#ifdef __cplusplus
}
#endif

#endif