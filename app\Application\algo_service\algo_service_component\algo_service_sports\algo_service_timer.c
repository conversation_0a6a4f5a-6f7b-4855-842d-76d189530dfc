﻿/*************************************************************************
 * @file algo_service_timer.c
 * <AUTHOR> (<EMAIL>)
 * @brief 计时算法组件实现
 * @version 0.1
 * @date 2024-11-28
 *
 * @copyright Copyright (c) 2024-2025, Wuhan Qiwu Technology Co., Ltd
 *
 ************************************************************************/
#include "algo_service_timer.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_task.h"
#include "cfg_header_def.h"
#include "qw_fit_api.h"
#include "service_datetime.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"

// 输入数据
typedef struct
{
    uint32_t systime;                //当前的时间戳 单位S FIT时间
    int32_t time_zone;               //时区 单位S
    saving_status_e saving_status;   //数据记录的状态
    uint8_t move_status;             // 0:静止 1:运动
    uint8_t sports_type;             // 运动类型
    bool in_rest;                    //是否在休息状态
} algo_timer_sub_t;

static algo_timer_sub_t s_algo_in = {0};

// 发布数据
static algo_timer_pub_t s_algo_out = {0};

// 本算法打开标记
static bool s_is_open = false;
static uint32_t g_last_time = 0;

//时间警示报警检测
static void auto_alert_time_check(sports_alert_t *p_alert, uint32_t total_timer_time)
{
    uint32_t timeMoving = total_timer_time / 1000;
    SPORTTYPE sport_type = get_current_sport_mode();
    int time_alert_used = get_sport_remind_en(sport_type, SPORT_REMIND_TIME, MAIN_EN);
    static uint32_t alertTime = 0;
    uint32_t time_threshold = get_sport_remind_value(sport_type, SPORT_REMIND_TIME, true);   //警示阈值
    sports_alert_t alert_msg = {0, 0, 0, 0};

    if (time_alert_used && time_threshold > 0)   //当前报警开启
    {
        if (timeMoving >= time_threshold)
        {
            alert_msg.high_alert_status = true;
        }
        else
        {
            alert_msg.high_alert_status = false;
        }

        //如果阈值变化, 记录的基准值立即向下调整到阈值的整数倍, 如果计算后当前值大于
        if (0 < (alertTime % time_threshold))
        {
            alertTime = alertTime / time_threshold * time_threshold;
        }

        //基准值总是等于阈值的整数倍
        if (timeMoving >= time_threshold + alertTime)
        {
            alertTime += (timeMoving - alertTime) / time_threshold * time_threshold;
            alert_msg.high_alert_event = true;
        }
        else
        {
            alert_msg.high_alert_event = false;
        }
    }
    else
    {
        alert_msg.high_alert_event = false;
    }

    memcpy(p_alert, &alert_msg, sizeof(sports_alert_t));
}

/**
 * @brief 算法处理
 *
 * @param algo_out 输出数据
 * @param algo_in 输入数据
 */
static void algo_timer_deal(algo_timer_pub_t *algo_out, const algo_timer_sub_t *algo_in)
{
    uint32_t systime = service_datetime_get_fit_time();
    uint32_t diff_time_once = 0;

    if (algo_in->saving_status <= enum_status_ready)
    {
        g_last_time = systime;
        return;   // 准备开始状态下不处理(未开始状态也是)
    }

    //更新在暂停情况下也需要更新的数据
    if (enum_status_free != algo_in->saving_status)
    {
        //总时间(旅程时间)=当前时间-该圈的起始时间
        algo_out->total_elapsed_time = (systime - algo_out->timer_total.start_time + 1) * 1000;   // 1000 * s,Time (includes pauses)
        algo_out->timer_total.timestamp = systime;                                                //时间戳 单位S FIT时间
        algo_out->timer_lap.timestamp = systime;                                                  //时间戳 单位S FIT时间
    }

    //该函数调用的时间差异不大于1s
    if (systime - g_last_time > 0)
    {
        if (systime - g_last_time < 10)
        {
            diff_time_once = (uint8_t) (systime - g_last_time);
        }
        else
        {
            diff_time_once = 0;
        }
        //更新时间
        g_last_time = systime;
    }

    //设备不处于存储状态时不更新统计数据
    if (enum_status_saving == algo_in->saving_status)
    {
        algo_out->timer_total.timer_time += diff_time_once * 1000;
        algo_out->timer_lap.timer_time += diff_time_once * 1000;

        if (!algo_in->in_rest)
        {
            if (algo_in->sports_type == FIT_SPORTS_JUMP_ROPE || algo_in->sports_type == FIT_SPORTS_STRENGTH_TRAINING
                || algo_in->sports_type == FIT_SPORTS_ROWING_MACHINE)
            {
                algo_out->timer_total.moving_time += diff_time_once * 1000;
                algo_out->timer_lap.moving_time += diff_time_once * 1000;
            }
            else
            {
                if (algo_in->move_status)
                {
                    algo_out->timer_total.moving_time += diff_time_once * 1000;
                    algo_out->timer_lap.moving_time += diff_time_once * 1000;
                }
            }
        }

        //总时间>=记录时间>=骑行时间（记录时间与骑行时间已统一）
        if (algo_out->total_elapsed_time < algo_out->timer_total.timer_time)
        {
            algo_out->timer_total.timer_time = algo_out->total_elapsed_time;
        }

        if (algo_out->total_elapsed_time < algo_out->timer_lap.timer_time)
        {
            algo_out->timer_lap.timer_time = algo_out->total_elapsed_time;
        }

        // 运动警示
        auto_alert_time_check(&algo_out->alert, algo_out->timer_total.timer_time);
    }
}

/**
 * @brief 算法控制
 *
 * @param algo_out 输出数据
 * @param ctrl_type 控制类型
 */
static void algo_timer_ctrl(algo_timer_pub_t *algo_out, const algo_sports_ctrl_t *ctrl)
{
    uint32_t systime = service_datetime_get_fit_time();
    ctrl_type_e ctrl_type = ctrl->ctrl_type;
    evt_lap_type_e lap_type = ctrl->lap_type;
    SPORTTYPE sport_type = get_current_sport_mode();
    uint32_t auto_lap_time = get_auto_record_lap_value(sport_type, AUTO_RECORD_LAP_TIMES);   //警示阈值

    if (ctrl->sports_type == FIT_SPORTS_JUMP_ROPE || ctrl->sports_type == FIT_SPORTS_STRENGTH_TRAINING || ctrl->sports_type == FIT_SPORTS_ROWING_MACHINE)
    {
        if (enum_ctrl_start == ctrl_type)
        {
            g_last_time = systime;
            algo_out->timer_total.timestamp = systime;
            algo_out->timer_lap.timestamp = systime;
            algo_out->timer_total.start_time = systime;
            algo_out->timer_lap.start_time = systime;
            algo_out->total_elapsed_time = 0;
            algo_out->timer_total.timer_time = 0;
            algo_out->timer_lap.timer_time = 0;
            algo_out->timer_pre_lap.timer_time = 0;
            algo_out->timer_total.moving_time = 0;
            algo_out->timer_lap.moving_time = 0;
            algo_out->timer_pre_lap.moving_time = 0;
            algo_out->best_lap_time = 0;
            algo_out->avg_lap_time = 0;
            algo_out->num_laps = 0;

            s_algo_in.in_rest = false;
        }
        else if (enum_ctrl_lap == ctrl_type)
        {
            s_algo_in.in_rest = true;
        }
        else if (enum_ctrl_rest_resume == ctrl_type)
        {
            algo_out->timer_pre_lap.start_time = algo_out->timer_lap.start_time;
            algo_out->timer_pre_lap.timer_time = algo_out->timer_lap.timer_time;
            algo_out->timer_pre_lap.moving_time = algo_out->timer_lap.moving_time;
            algo_out->timer_pre_lap.timestamp = systime;
            algo_out->timer_lap.timestamp = systime;
            algo_out->timer_lap.start_time = systime;
            algo_out->timer_lap.timer_time = 0;
            algo_out->timer_lap.moving_time = 0;
            algo_out->num_laps++;
            algo_out->avg_lap_time = algo_out->num_laps == 0 ? UINT32_MAX : algo_out->timer_total.timer_time / algo_out->num_laps;

            // 自动计圈时间修正
            if (enum_lap_time == lap_type)
            {
                if (auto_lap_time * 1000 < algo_out->timer_pre_lap.timer_time)
                {
                    algo_out->timer_lap.timer_time = algo_out->timer_pre_lap.timer_time - auto_lap_time * 1000;
                }

                algo_out->timer_pre_lap.timer_time = auto_lap_time * 1000;
            }

            if (0 == algo_out->best_lap_time || algo_out->best_lap_time > algo_out->timer_pre_lap.timer_time)
            {
                algo_out->best_lap_time = algo_out->timer_pre_lap.timer_time;
            }

            s_algo_in.in_rest = false;
        }
        else if (enum_ctrl_stop == ctrl_type)
        {
            algo_out->timer_lap.timestamp = systime;
            algo_out->timer_total.timestamp = systime;
            algo_out->total_elapsed_time = (algo_out->timer_total.timestamp - algo_out->timer_total.start_time + 1) * 1000;   // 1000 * s,Time (includes pauses)
            // algo_out->num_laps++; //enum_ctrl_stop之前会先发enum_ctrl_lap
            algo_out->avg_lap_time = algo_out->num_laps == 0 ? UINT32_MAX : algo_out->timer_total.timer_time / algo_out->num_laps;

            if (0 == algo_out->best_lap_time || algo_out->best_lap_time > algo_out->timer_lap.timer_time)
            {
                algo_out->best_lap_time = algo_out->timer_lap.timer_time;
            }
        }
    }
    else
    {
        if (enum_ctrl_start == ctrl_type)
        {
            g_last_time = systime;
            algo_out->timer_total.timestamp = systime;
            algo_out->timer_lap.timestamp = systime;
            algo_out->timer_total.start_time = systime;
            algo_out->timer_lap.start_time = systime;
            algo_out->total_elapsed_time = 0;
            algo_out->timer_total.timer_time = 0;
            algo_out->timer_lap.timer_time = 0;
            algo_out->timer_pre_lap.timer_time = 0;
            algo_out->timer_total.moving_time = 0;
            algo_out->timer_lap.moving_time = 0;
            algo_out->timer_pre_lap.moving_time = 0;
            algo_out->best_lap_time = 0;
            algo_out->avg_lap_time = 0;
            algo_out->num_laps = 0;

            s_algo_in.in_rest = false;
        }
        else if (enum_ctrl_lap == ctrl_type)
        {
            algo_out->timer_pre_lap.start_time = algo_out->timer_lap.start_time;
            algo_out->timer_pre_lap.timer_time = algo_out->timer_lap.timer_time;
            algo_out->timer_pre_lap.moving_time = algo_out->timer_lap.moving_time;
            algo_out->timer_pre_lap.timestamp = systime;
            algo_out->timer_lap.timestamp = systime;
            algo_out->timer_lap.start_time = systime;
            algo_out->timer_lap.timer_time = 0;
            algo_out->timer_lap.moving_time = 0;
            algo_out->num_laps++;
            algo_out->avg_lap_time = algo_out->num_laps == 0 ? UINT32_MAX : algo_out->timer_total.timer_time / algo_out->num_laps;

            // 自动计圈时间修正
            if (enum_lap_time == lap_type)
            {
                if (auto_lap_time * 1000 < algo_out->timer_pre_lap.timer_time)
                {
                    algo_out->timer_lap.timer_time = algo_out->timer_pre_lap.timer_time - auto_lap_time * 1000;
                }

                algo_out->timer_pre_lap.timer_time = auto_lap_time * 1000;
            }

            if (0 == algo_out->best_lap_time || algo_out->best_lap_time > algo_out->timer_pre_lap.timer_time)
            {
                algo_out->best_lap_time = algo_out->timer_pre_lap.timer_time;
            }
        }
        else if (enum_ctrl_stop == ctrl_type)
        {
            algo_out->timer_lap.timestamp = systime;
            algo_out->timer_total.timestamp = systime;
            algo_out->total_elapsed_time = (algo_out->timer_total.timestamp - algo_out->timer_total.start_time + 1) * 1000;   // 1000 * s,Time (includes pauses)
            // algo_out->num_laps++; //enum_ctrl_stop之前会先发enum_ctrl_lap
            algo_out->avg_lap_time = algo_out->num_laps == 0 ? UINT32_MAX : algo_out->timer_total.timer_time / algo_out->num_laps;

            if (0 == algo_out->best_lap_time || algo_out->best_lap_time > algo_out->timer_lap.timer_time)
            {
                algo_out->best_lap_time = algo_out->timer_lap.timer_time;
            }
        }
    }
}

/**
 * @brief 算法控制订阅处理
 *
 * @param in 控制数据
 * @param len 数据长度
 */
static void algo_sports_ctrl_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_TIMER;
    head.input_type = DATA_ID_EVENT_SPORTS_CTRL;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_move_status_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_TIMER;
    head.input_type = ALGO_TYPE_MOVE_STATUS;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_timer_out_callback(const void *out, uint32_t len)
{
    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_ALGO_TIMER, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("%s publish error", __FUNCTION__);
    }
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_ALG_NAME_MOVE_STATUS,
        .topic_id = DATA_ID_ALGO_MOVE_STATUS,
        .callback = algo_move_status_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
};

/**
 * @brief 算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_timer_init(void)
{
    algo_timer_sub_t *algo_in = &s_algo_in;
    algo_timer_pub_t *algo_out = &s_algo_out;

    memset(algo_in, 0, sizeof(algo_timer_sub_t));
    memset(algo_out, 0, sizeof(algo_timer_pub_t));
    return 0;
}

/**
 * @brief 算法open
 *
 * @return int32_t 结果
 */
static int32_t algo_timer_open(void)
{
    if (s_is_open)
    {
        ALGO_COMP_LOG_W("%s already open", __FUNCTION__);
        return 0;
    }

    ALGO_COMP_LOG_D("%s open", __FUNCTION__);

    s_is_open = true;
    algo_timer_init();

    return algo_topic_list_subscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
}

/**
 * @brief 算法feed
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_timer_feed(uint32_t input_type, void *data, uint32_t len)
{
    algo_timer_sub_t *algo_in = &s_algo_in;
    algo_timer_pub_t *algo_out = &s_algo_out;

    switch (input_type)
    {
    case DATA_ID_EVENT_SPORTS_CTRL:
    {
        const algo_sports_ctrl_t *sports_ctrl = (algo_sports_ctrl_t *) data;
        algo_in->saving_status = sports_ctrl->saving_status;
        algo_in->sports_type = sports_ctrl->sports_type;

        /* 由于timer算法只订阅了sports_ctrl，算法处理接口algo_timer_deal()只有在这里调用。
                要求sports_ctrl算法内实现每秒发布一个null类型的数据到timer */
        if (enum_ctrl_null == sports_ctrl->ctrl_type)
        {
            //算法处理
            algo_timer_deal(algo_out, algo_in);
        }
        else
        {
            //算法控制
            algo_timer_ctrl(algo_out, sports_ctrl);
        }
        break;
    }
    case ALGO_TYPE_MOVE_STATUS:
    {
        const algo_move_status_pub_t *algo_status = (const algo_move_status_pub_t *) data;
        algo_in->move_status = algo_status->move_status;
        break;
    }

    default:
        break;
    }

    //数据发布
    algo_timer_out_callback(algo_out, sizeof(algo_timer_pub_t));
    return 0;
}

/**
 * @brief 算法close
 *
 * @return int32_t 结果
 */
static int32_t algo_timer_close(void)
{
    if (!s_is_open)
    {
        ALGO_COMP_LOG_W("%s already close", __FUNCTION__);
        return 0;
    }

    algo_topic_list_unsubscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));

    s_is_open = false;
    ALGO_COMP_LOG_D("%s close", __FUNCTION__);
    return 0;
}

// 算法组件实现
static algo_compent_ops_t s_timer_algo = {
    .init = algo_timer_init,
    .open = algo_timer_open,
    .feed = algo_timer_feed,
    .close = algo_timer_close,
    .ioctl = NULL,
};

/**
 * @brief 组件注册
 *
 * @return int32_t 结果
 */
int32_t register_timer_algo(void)
{
    algo_compnent_register(ALGO_TYPE_TIMER, &s_timer_algo);
    return 0;
}