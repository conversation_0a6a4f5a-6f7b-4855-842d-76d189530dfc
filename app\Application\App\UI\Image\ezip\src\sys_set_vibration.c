#include "lvgl.h" 

#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP ALIGN(4)
#endif

#ifndef eZIP_RGBARGB888A
#define eZIP_RGBARGB888A
#endif


LV_ATTRIBUTE_MEM_ALIGN_EZIP const  uint8_t sys_set_vibration_map[] SECTION(".ROM3_IMG_EZIP.sys_set_vibration") = { 
    0x00,0x00,0x07,0x47,0x46,0x08,0x20,0x00,0x00,0x48,0x00,0x48,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0x03,0x00,0x00,0x00,0x94,0x00,0x00,0x03,0x70,0x00,0x00,0x06,0x60,
    0x3c,0x6b,0x8b,0x8d,0xaa,0x8a,0xa2,0x6b,0x9f,0x99,0xbe,0x10,0xe8,0x83,0xb4,0x34,0x7e,0x40,0x23,0xfd,0xb1,0x90,0xb4,0x86,0xd4,0xd8,0x60,0xa4,0xd3,0x0e,0x18,0x1f,
    0x40,0x94,0xd8,0xaa,0xb1,0x36,0x82,0xa0,0xc1,0x34,0x41,0xfc,0xd0,0x28,0xd0,0x56,0x89,0x7c,0x68,0x34,0x51,0x41,0xc4,0x84,0x50,0x84,0x0f,0xaa,0xd1,0x08,0x89,0x1f,
    0x32,0x7d,0x60,0x42,0x84,0x08,0x06,0x14,0x0a,0x1f,0x60,0x0a,0x1f,0x0d,0x16,0xe8,0xf0,0xb0,0x9d,0x96,0xb6,0x73,0xdc,0x77,0x98,0x19,0x6e,0xe7,0xd1,0xce,0xdc,0x57,
    0xe7,0xb1,0x93,0x33,0x73,0x72,0xe7,0xce,0x39,0xfb,0xae,0xb3,0xf6,0xda,0xfb,0x9c,0x0b,0x00,0x00,0x00,0x24,0x24,0x90,0xe5,0x76,0xc8,0x05,0x44,0x28,0x87,0x44,0x99,
    0x20,0x94,0x4a,0x89,0x79,0x10,0x28,0x26,0x2f,0xe6,0x48,0x42,0x91,0xff,0x36,0xc9,0x6d,0x84,0xdb,0x55,0x6e,0x7d,0xdc,0x2e,0xf1,0x95,0x1e,0x92,0x38,0x95,0xed,0xc1,
    0xb1,0xbe,0x15,0x34,0x64,0xa4,0x4f,0xd3,0x0a,0xd0,0xec,0x2e,0x59,0x6a,0x93,0x58,0xce,0x0f,0xef,0xe0,0x07,0x7c,0x9c,0x2f,0x15,0xeb,0x1d,0x93,0xd1,0x73,0xf1,0xc7,
    0xa1,0x4c,0x89,0xf6,0x7e,0x27,0xfd,0x9b,0x74,0x00,0xe5,0x75,0xc9,0x12,0x9e,0xb4,0x9e,0x1f,0x64,0x35,0x83,0x52,0x69,0xf2,0x74,0xdf,0x31,0x23,0x77,0x0e,0x38,0xe8,
    0x78,0xc2,0x03,0x94,0xdf,0x29,0x9f,0xe1,0xaf,0x75,0xdc,0x56,0x4d,0x03,0x59,0x0f,0x72,0x98,0x7e,0x38,0xe0,0xa4,0x9e,0x84,0x03,0x28,0xbf,0x43,0xbe,0xc4,0x21,0xb4,
    0x91,0x27,0x7a,0x74,0xba,0x35,0x8e,0x35,0x6d,0xcb,0xcd,0x5a,0xda,0x96,0x10,0x00,0xe5,0xbb,0xe4,0xb3,0xb0,0xe1,0x7d,0xd6,0x83,0xaa,0x44,0x4a,0x04,0xfc,0xc0,0x1d,
    0xe3,0x5e,0xac,0xbf,0xe5,0xa4,0x7f,0xa6,0x05,0xa0,0x5c,0x97,0x7c,0x48,0x08,0x7c,0xcc,0xdd,0x7a,0x24,0xa8,0xf1,0x43,0xf7,0xc3,0x8b,0x57,0x38,0xe4,0x8e,0x58,0x0a,
    0x50,0x5e,0x87,0x6c,0x62,0x51,0xfc,0x94,0xbb,0x99,0x48,0x02,0x53,0x92,0xc5,0x40,0x0d,0xb5,0x9b,0x0e,0xd0,0xcc,0xdf,0x64,0xa1,0x7d,0x0c,0x5f,0xf3,0x60,0xab,0x91,
    0x64,0x36,0x15,0x48,0x64,0x40,0x71,0x57,0xc3,0x45,0xdd,0x1e,0xee,0xce,0x47,0x92,0x1a,0x67,0xb8,0xe5,0xd1,0xc2,0x4d,0x17,0x40,0x05,0x5d,0xf2,0x75,0xce,0x0c,0xdf,
    0x22,0xc9,0x4d,0xd1,0x24,0x16,0xee,0xaa,0x48,0xc2,0x2d,0x34,0x67,0xa9,0x2e,0xf9,0x41,0x2a,0x80,0xe3,0xaf,0xbe,0x8b,0x6c,0x02,0xbb,0x23,0xfd,0x26,0x34,0xd6,0x36,
    0xdb,0x79,0xd4,0x6d,0xa9,0x00,0x8e,0x0a,0xa4,0x5a,0x4e,0x32,0x9b,0x75,0x87,0x98,0x0f,0x1c,0xc2,0x7b,0x48,0x51,0x63,0x3d,0x5a,0xa8,0xae,0xb8,0x45,0xbc,0x61,0x95,
    0xca,0xe0,0xf8,0x98,0x24,0xb0,0x55,0x13,0x83,0x52,0x45,0x90,0x63,0x62,0x11,0xa1,0x2a,0xb0,0xc1,0x15,0xb1,0xa6,0xf2,0x74,0x01,0xc7,0xbf,0x67,0xdb,0x10,0x33,0x83,
    0x94,0x22,0x30,0x63,0x0c,0x7f,0x24,0x73,0x9d,0xa3,0xc5,0x32,0xbc,0x28,0x56,0xce,0x93,0xa6,0x64,0x90,0x52,0x21,0xa7,0x1b,0x38,0x8a,0xdd,0x25,0xd4,0x4d,0x19,0x62,
    0xbe,0xbd,0x55,0x12,0x6e,0x1f,0x0c,0xaa,0x1e,0x57,0x4e,0x1a,0x62,0xbe,0x5d,0x39,0xe1,0x3c,0xdf,0x91,0x99,0x96,0x00,0xb1,0xe5,0x0c,0xe2,0x01,0x7b,0xb4,0x1f,0xfd,
    0x47,0x16,0x69,0x0b,0x8e,0x62,0xc3,0x39,0x58,0x62,0x8f,0x7a,0xd8,0x65,0xd1,0x79,0xce,0xe8,0xef,0x87,0xe1,0xed,0xbb,0x34,0x35,0xe3,0x73,0x0b,0x91,0xb1,0x78,0x19,
    0x28,0xbf,0xc8,0xba,0x6c,0x46,0x58,0x1c,0x99,0x41,0xf7,0x4e,0x02,0x4d,0xb5,0x91,0x9f,0xbe,0xc2,0xd0,0x97,0x4d,0x71,0xff,0x2f,0x6b,0xf5,0x46,0xcc,0x78,0xeb,0x73,
    0xab,0x74,0xa8,0xcc,0x16,0xe9,0x0c,0x99,0xbf,0x36,0x9a,0x9a,0x21,0xba,0xdb,0x31,0xf4,0xc9,0x1a,0x4d,0xff,0x1d,0x3f,0xcf,0xf5,0xdb,0xe0,0x6d,0x64,0x54,0x3e,0x69,
    0x05,0x44,0x83,0x61,0x22,0x9d,0xd7,0x29,0x4f,0x98,0x7d,0xc0,0x7e,0xf3,0xe9,0x99,0x90,0xc3,0x83,0xfa,0x4e,0x2f,0x7f,0xec,0x07,0xe5,0x15,0x9a,0x0d,0x50,0xaf,0x08,
    0x7d,0x35,0x63,0x36,0x38,0xde,0x81,0xab,0xba,0xc1,0xf1,0x69,0xd7,0x9f,0x2e,0x2b,0x18,0x54,0x1c,0x5a,0x07,0xad,0x33,0x7a,0x06,0x4f,0x5b,0x0b,0xc6,0x4e,0x77,0xdf,
    0x17,0xbe,0xdb,0x37,0x8c,0x11,0xd0,0x5b,0xd7,0x27,0xcc,0x11,0x3a,0x8f,0x41,0x96,0x65,0x57,0xbf,0xf1,0x64,0x61,0x5e,0x65,0x34,0x38,0xc3,0x6d,0xad,0x18,0x46,0x2b,
    0xb2,0x1b,0x9b,0x91,0xd3,0xd8,0x62,0xf8,0x12,0x2b,0xa0,0x28,0x73,0xf8,0xfa,0x15,0x47,0x31,0xab,0xa2,0xda,0x50,0x99,0x16,0xaa,0x73,0x90,0x17,0xf5,0x38,0x19,0x69,
    0xf5,0x02,0x8e,0x5b,0x65,0x91,0xfc,0xd0,0xcb,0x2c,0xa1,0x3a,0x07,0x79,0x5e,0xab,0x53,0x77,0x36,0x39,0x82,0x4d,0x7d,0x7d,0xc2,0xe6,0xaf,0xbc,0xda,0x14,0x50,0xec,
    0x21,0x8c,0x19,0x3d,0xd3,0x1d,0xc6,0x60,0xc5,0x2f,0xa5,0xaf,0x19,0xa0,0xd9,0x5d,0xb2,0x94,0x24,0x2a,0x35,0x85,0xd1,0xbe,0xd6,0x88,0xa0,0xa8,0xaf,0x2b,0x0f,0x61,
    0x37,0x96,0xfa,0x13,0x4c,0x09,0xdf,0xa9,0x58,0xab,0x95,0xcd,0xff,0x03,0x00,0x00,0x24,0xb0,0xe5,0x77,0xca,0x0d,0xfc,0xb5,0x03,0x1a,0xcc,0x5d,0x43,0xc1,0xbe,0xbd,
    0xa2,0x1a,0xb3,0x3e,0xeb,0xf2,0xf5,0x3d,0x6d,0x2d,0x18,0x6e,0x6b,0x0d,0xbb,0x3e,0xde,0x7b,0x0e,0xb7,0xd7,0x2c,0x82,0x5e,0x9b,0xd1,0xf4,0x05,0xb2,0x9e,0x6b,0xf2,
    0xf5,0xef,0x6c,0x72,0x60,0xec,0x74,0x77,0xf0,0x37,0x7e,0x9e,0x30,0x1f,0x14,0xcb,0x6e,0x6c,0x46,0x4e,0x63,0x4b,0x5c,0xf3,0x08,0xe5,0x83,0x87,0xab,0xd1,0xe2,0xa4,
    0xda,0x29,0xc5,0x72,0x5e,0x6d,0x8e,0xe9,0x3e,0x33,0x4d,0x59,0x8c,0xa0,0x3f,0x71,0x82,0x11,0x15,0x20,0xe6,0xc0,0x12,0xbd,0xce,0x28,0x36,0x7a,0xa6,0x3b,0xaa,0x73,
    0xca,0x6a,0x9a,0x61,0x0a,0xf8,0xea,0x05,0x50,0x2f,0x52,0xe8,0xc2,0x64,0x94,0x57,0xc7,0x0f,0x50,0x6e,0x87,0x5c,0xc0,0xdf,0xc5,0x5a,0x1d,0x54,0x68,0x1b,0x74,0xe8,
    0xcc,0xd1,0x49,0x01,0xb4,0x9a,0x41,0x9e,0x7d,0xad,0x13,0xae,0x6b,0xf1,0x47,0x10,0xa1,0x5c,0xd7,0x0a,0x86,0x80,0x12,0x6d,0x65,0xb5,0xac,0x9e,0x16,0x16,0xab,0x99,
    0x6a,0x2f,0x5f,0xaa,0x37,0xcc,0x3d,0x82,0x05,0xa8,0xcc,0x28,0x7a,0xab,0x1d,0x32,0x62,0xf5,0xb4,0xb0,0x58,0x2d,0xca,0x61,0x4f,0x1b,0x6f,0x98,0x4b,0xb8,0x85,0x20,
    0x94,0x1a,0xe5,0xa8,0x59,0x2c,0xd1,0x6a,0xba,0x45,0x9a,0xd0,0x2b,0x24,0x61,0x9e,0x51,0xf4,0x56,0x8b,0xb4,0x01,0xf4,0xd6,0x14,0xe6,0x6a,0x9f,0x74,0x8b,0xb4,0xc4,
    0x65,0xa1,0x47,0xa0,0x27,0x13,0xe9,0x44,0xc8,0x62,0x7a,0xc3,0x9c,0xf5,0xf9,0xa2,0x20,0x89,0x39,0x66,0x88,0x74,0x22,0x64,0x31,0xbd,0x2c,0xe6,0xfa,0xf0,0x82,0xe0,
    0x8f,0x22,0xb3,0x6a,0x90,0xe9,0xce,0x62,0xba,0x59,0x6c,0xc3,0xdf,0xc2,0x48,0x67,0xd5,0x1a,0xa4,0xee,0x9b,0xcd,0x26,0x75,0x98,0x1b,0x68,0x1e,0xf7,0x52,0xf2,0x01,
    0x24,0x8d,0x5a,0xbd,0x58,0x84,0xd3,0x6c,0x53,0xa7,0xf9,0x50,0xc6,0xc4,0xc9,0xe2,0x13,0x81,0xad,0xc6,0x88,0x19,0x35,0x48,0x42,0x64,0xb1,0x90,0xeb,0x71,0x2d,0x94,
    0xc4,0xb1,0x00,0x40,0x57,0xf5,0xd6,0x1a,0x81,0x89,0xd5,0x60,0x85,0xae,0x96,0x59,0x59,0x2c,0x74,0x01,0xd4,0x3a,0xa8,0x5e,0xa4,0x68,0x1b,0xe9,0xa8,0x5b,0x0c,0x09,
    0xdf,0xa0,0x76,0x6e,0x7d,0xdc,0x4a,0xf4,0x38,0x18,0x38,0xca,0x08,0x0d,0x3f,0xa5,0x99,0xce,0x9e,0xd3,0xd1,0xb5,0x4e,0x59,0x3c,0xad,0xc5,0xe2,0x0d,0x27,0xb9,0x02,
    0x0c,0xba,0x64,0x5a,0xca,0x55,0xad,0xa0,0x15,0x59,0xcc,0x40,0xb1,0x6e,0x0f,0x8e,0xcf,0xb1,0xd6,0x83,0xfb,0x67,0x5e,0x86,0x97,0xfa,0x01,0x60,0xcc,0x14,0xea,0x00,
    0x83,0x0d,0x9b,0xc3,0x8b,0x43,0x41,0x80,0xb8,0x50,0x3c,0x25,0x4d,0x02,0x28,0x92,0xd3,0xb6,0xf9,0x0f,0x1b,0x32,0xae,0xad,0x64,0x91,0x39,0x59,0x52,0xe2,0x8a,0xdb,
    0x49,0x07,0x82,0x5a,0x94,0xed,0xb9,0xa7,0xd6,0x96,0x19,0x09,0x64,0x3d,0xb5,0x56,0xdf,0x10,0x99,0xd9,0xb0,0x3f,0xe2,0x30,0xcb,0xc3,0xed,0x13,0xc4,0xba,0x6f,0x05,
    0x0d,0x71,0x21,0xe4,0xb2,0x12,0xa3,0xec,0x86,0xcd,0xa0,0xbc,0x42,0xed,0xe7,0xd1,0xef,0xee,0x35,0x67,0xed,0x80,0x93,0xee,0x5a,0xda,0x15,0x76,0xe4,0xca,0xb4,0x3a,
    0x64,0x25,0x40,0xa2,0xb8,0x04,0xb9,0x7b,0xce,0x22,0xbb,0xee,0x1d,0xd0,0xec,0xd8,0xb6,0x82,0x94,0x95,0x83,0xcc,0xda,0x97,0x31,0x6b,0xc7,0x71,0x64,0x3a,0xea,0x4d,
    0xf1,0x8b,0x89,0xd2,0x1c,0x01,0x34,0xde,0x8c,0xb9,0xe4,0xdc,0x51,0xa1,0xaf,0x1e,0xd2,0xe5,0xd8,0x88,0x27,0x26,0x80,0x4c,0x8d,0x7c,0x89,0xbd,0x03,0xb5,0xf4,0x5a,
    0x44,0x80,0xfc,0xaf,0x4a,0xf6,0xf1,0x57,0x03,0xd2,0xd1,0x08,0xd7,0x46,0x6d,0x58,0xf8,0xdf,0x13,0x74,0x2d,0xe2,0x5b,0x0d,0xff,0xd9,0xc7,0x4e,0xa4,0xab,0x8d,0x63,
    0x53,0x24,0x70,0x26,0x00,0x34,0xe0,0xa0,0xe3,0xfc,0x75,0x30,0x0d,0xd9,0xf3,0x0d,0xa7,0xf5,0xfd,0x93,0x08,0xf7,0x7d,0x2b,0x70,0xc9,0x32,0x29,0x70,0x2e,0x8d,0xe0,
    0x39,0xe5,0x76,0xa0,0x92,0xc3,0x47,0x4e,0xfa,0xe2,0x30,0xc8,0x22,0x27,0xf5,0x48,0x89,0x2d,0xe9,0x82,0x0e,0xa3,0xb2,0x76,0x32,0x70,0xc2,0x18,0x14,0x64,0x52,0xa7,
    0x74,0xf1,0xbf,0x6a,0x53,0x3a,0xb2,0x08,0x2f,0xb0,0xac,0xfc,0x10,0xd3,0xab,0xe7,0x30,0xcd,0xf2,0x62,0x3d,0x23,0xd7,0x9f,0xb2,0xcc,0x91,0x78,0x3b,0x16,0x70,0xa2,
    0x32,0xc8,0xaf,0x47,0xcb,0x58,0x8f,0x7e,0x4d,0x57,0xe6,0x4c,0xca,0x20,0xbf,0x1e,0x1d,0x61,0xf4,0xea,0x53,0x49,0x90,0x59,0x36,0x2a,0xe2,0x01,0x47,0xb1,0xff,0x01,
    0x25,0x4c,0x61,0x05,0x9d,0xb2,0x4e,0x02,0x07,0x91,0xdc,0xb6,0xdb,0xed,0xc0,0x9b,0x20,0x92,0xf1,0xfe,0x51,0x4c,0x75,0xc3,0x40,0x0d,0xb5,0x93,0x17,0xcb,0x19,0xc9,
    0xfe,0x24,0x04,0xe6,0x3a,0xbc,0x68,0x70,0xd7,0xd0,0x1b,0x5a,0xc0,0x89,0x09,0x20,0x1f,0x48,0x4e,0x3a,0x32,0xee,0x45,0x15,0x83,0xd4,0x91,0x2c,0xc8,0x10,0x61,0xef,
    0xe8,0x28,0x16,0xba,0x9d,0xb4,0x5f,0xd7,0x38,0xf1,0xfe,0x21,0xaf,0x43,0x6e,0xe6,0xc9,0x3f,0x4a,0x58,0x60,0x80,0x93,0x4c,0x95,0x66,0x66,0xcd,0x2f,0x06,0x8d,0x17,
    0xbf,0x15,0xb8,0x64,0x99,0x14,0xd8,0xca,0xdd,0xfa,0x84,0x41,0x46,0xe2,0x0a,0x7f,0x6e,0x77,0xd7,0xd2,0x2e,0x83,0x01,0xd7,0x6e,0x05,0x5d,0xf2,0x31,0x29,0xb1,0x81,
    0xbb,0x0d,0xd3,0x86,0x8b,0xc4,0xf7,0xac,0x2e,0x3f,0x73,0x28,0x1d,0x30,0x89,0x91,0xfa,0xad,0xc8,0x25,0xe7,0xde,0x25,0xd4,0xf1,0x68,0x2b,0x79,0x40,0xa7,0x89,0x78,
    0x78,0xb8,0x9d,0x60,0xb6,0x1c,0x13,0x12,0xdd,0x37,0x9c,0xe4,0xb2,0x20,0x64,0x8d,0xb5,0x07,0x0f,0xcb,0x19,0xc3,0x39,0x58,0x22,0x09,0x8b,0x79,0xf4,0x32,0xbe,0xb4,
    0x40,0xb9,0xcc,0xad,0x98,0x5b,0x56,0x0c,0x73,0x7a,0x18,0x00,0x37,0xdf,0xd5,0xcb,0xfd,0xcb,0x7c,0xf3,0x45,0xd6,0x94,0x0b,0x9c,0x8d,0xce,0x32,0x4b,0xfe,0xb2,0x9a,
    0xa1,0xff,0x03,0xe0,0xd3,0xbb,0x21,
};


#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER ALIGN(4)
#endif
LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER const eZIP_RGBARGB888A  lv_img_dsc_t sys_set_vibration SECTION(".ROM3_IMG_EZIP_HEADER.sys_set_vibration") = { 
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .header.always_zero = 0,
  .header.w = 72,
  .header.h = 72,
  .data_size  = 1863,
  .data = sys_set_vibration_map
};
