﻿/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   algo_service_position.c
<AUTHOR>   yukai (<EMAIL>)
@Brief    :    坐标位置统计算法组件实现
@Date    :   2024/12/20
*
**************************************************************************/

#include "algo_service_position.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_task.h"
#include "gps_convert.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"

// 输入数据
typedef struct
{
    int32_t lat;                     //纬度 * 10000000
    int32_t lon;                     //经度 * 10000000
    saving_status_e saving_status;   //数据记录的状态
} algo_position_sub_t;

static algo_position_sub_t s_algo_in = {0};

// 发布数据
static algo_position_pub_t s_algo_out = {0};

// 本算法打开标记
static bool s_is_open = false;

// 算法数据初始化
static void position_data_init(void)
{
    algo_position_sub_t *algo_in = &s_algo_in;
    algo_position_pub_t *algo_out = &s_algo_out;

    algo_in->lat = 0x7fffffff;
    algo_in->lon = 0x7fffffff;
    algo_in->saving_status = 0;
    algo_out->start_lat = 0x7fffffff;
    algo_out->start_lon = 0x7fffffff;
    algo_out->nec_lat = 0x7fffffff;
    algo_out->nec_lon = 0x7fffffff;
    algo_out->swc_lat = 0x7fffffff;
    algo_out->swc_lon = 0x7fffffff;
    algo_out->start_lat_lap = 0x7fffffff;
    algo_out->start_lon_lap = 0x7fffffff;
    algo_out->end_lat_lap = 0x7fffffff;
    algo_out->end_lon_lap = 0x7fffffff;
    algo_out->start_lat_pre_lap = 0x7fffffff;
    algo_out->start_lon_pre_lap = 0x7fffffff;
    algo_out->end_lat_pre_lap = 0x7fffffff;
    algo_out->end_lon_pre_lap = 0x7fffffff;
}

/**
 * @brief 算法处理
 *
 * @param algo_out 输出数据
 * @param algo_in 输入数据
 */
static void algo_position_deal(algo_position_pub_t *algo_out, const algo_position_sub_t *algo_in)
{
    if (enum_status_saving == algo_in->saving_status)                                   //记录状态
    {
        if (false != point_valid_check(algo_in->lat, algo_in->lon))                     //坐标有效
        {
            if (false == point_valid_check(algo_out->start_lat, algo_out->start_lon))   //起始点无效
            {
                algo_out->start_lat = algo_in->lat;
                algo_out->start_lon = algo_in->lon;
            }

            if (false == point_valid_check(algo_out->start_lat_lap, algo_out->start_lon_lap))   //Lap起始点无效
            {
                algo_out->start_lat_lap = algo_in->lat;
                algo_out->start_lon_lap = algo_in->lon;
            }

            if (algo_out->nec_lat < algo_in->lat || 0x7fffffff == algo_out->nec_lat)
            {
                algo_out->nec_lat = algo_in->lat;
            }

            if (algo_out->nec_lon < algo_in->lon || 0x7fffffff == algo_out->nec_lon)
            {
                algo_out->nec_lon = algo_in->lon;
            }

            if (algo_out->swc_lat > algo_in->lat || 0x7fffffff == algo_out->swc_lat)
            {
                algo_out->swc_lat = algo_in->lat;
            }

            if (algo_out->swc_lon > algo_in->lon || 0x7fffffff == algo_out->swc_lon)
            {
                algo_out->swc_lon = algo_in->lon;
            }

            algo_out->end_lat_lap = algo_in->lat;
            algo_out->end_lon_lap = algo_in->lon;
        }
    }
}

/**
 * @brief 算法控制
 *
 * @param algo_out 输出数据
 * @param ctrl_type 控制类型
 */
static void algo_position_ctrl(algo_position_pub_t *algo_out, ctrl_type_e ctrl_type)
{
    if (enum_ctrl_start == ctrl_type)
    {
        position_data_init();
    }
    else if (enum_ctrl_lap == ctrl_type)
    {
        algo_out->start_lat_pre_lap = algo_out->start_lat_lap;
        algo_out->start_lon_pre_lap = algo_out->start_lon_lap;
        algo_out->end_lat_pre_lap = algo_out->end_lat_lap;
        algo_out->end_lon_pre_lap = algo_out->end_lon_lap;
        algo_out->start_lat_lap = 0x7fffffff;
        algo_out->start_lon_lap = 0x7fffffff;
        algo_out->end_lat_lap = 0x7fffffff;
        algo_out->end_lon_lap = 0x7fffffff;
    }
}

/**
 * @brief 算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_gps_data_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_POSITION;
    head.input_type = DATA_ID_ALGO_GPS_DATA;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法控制订阅处理
 *
 * @param in 控制数据
 * @param len 数据长度
 */
static void algo_sports_ctrl_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_POSITION;
    head.input_type = DATA_ID_EVENT_SPORTS_CTRL;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_position_out_callback(const void *out, uint32_t len)
{
    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_ALGO_POSITION, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("%s publish error", __FUNCTION__);
    }
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_gps_data",
        .topic_id = DATA_ID_ALGO_GPS_DATA,
        .callback = algo_gps_data_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id =DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
};

/**
 * @brief 算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_position_init(void)
{
    return 0;
}

/**
 * @brief 算法open
 *
 * @return int32_t 结果
 */
static int32_t algo_position_open(void)
{
    if (s_is_open)
    {
        ALGO_COMP_LOG_W("%s already open", __FUNCTION__);
        return 0;
    }

    ALGO_COMP_LOG_D("%s open", __FUNCTION__);

    s_is_open = true;
    position_data_init();   //算法数据初始化

    return algo_topic_list_subscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
}

/**
 * @brief 算法feed
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_position_feed(uint32_t input_type, void *data, uint32_t len)
{
    algo_position_sub_t *algo_in = &s_algo_in;
    algo_position_pub_t *algo_out = &s_algo_out;

    switch (input_type)
    {
    case DATA_ID_ALGO_GPS_DATA:
    {
        const gps_pub_t *gps_data = (gps_pub_t *) data;
        algo_in->lat = minmea_to_int(&gps_data->latitude, DEF_MINMEA_SCALE);
        algo_in->lon = minmea_to_int(&gps_data->longitude, DEF_MINMEA_SCALE);

        //算法处理
        algo_position_deal(algo_out, algo_in);
    }
    break;
    case DATA_ID_EVENT_SPORTS_CTRL:
    {
        const algo_sports_ctrl_t *sports_ctrl = (algo_sports_ctrl_t *) data;
        algo_in->saving_status = sports_ctrl->saving_status;

        //算法控制
        algo_position_ctrl(algo_out, sports_ctrl->ctrl_type);
    }
    break;
    default:
        break;
    }

    //数据发布
    algo_position_out_callback(algo_out, sizeof(algo_position_pub_t));
    return 0;
}

/**
 * @brief 算法close
 *
 * @return int32_t 结果
 */
static int32_t algo_position_close(void)
{
    int ret = -1;

    if (!s_is_open)
    {
        ALGO_COMP_LOG_W("%s already close", __FUNCTION__);
        return 0;
    }

    algo_topic_list_unsubscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));

    s_is_open = false;
    ALGO_COMP_LOG_D("%s close", __FUNCTION__);
    return 0;
}

// 算法组件实现
static algo_compent_ops_t s_position_algo = {
    .init = algo_position_init,
    .open = algo_position_open,
    .feed = algo_position_feed,
    .close = algo_position_close,
    .ioctl = NULL,
};

/**
 * @brief 组件注册
 *
 * @return int32_t 结果
 */
int32_t register_position_algo(void)
{
    algo_compnent_register(ALGO_TYPE_POSITION, &s_position_algo);
    return 0;
}