/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   key_module.c
@Time    :   2025/02/15 18:18:11
*
**************************************************************************/

#include "key_module.h"
#include <rtthread.h>
#include "drv_gpio.h"
#include "algo_service_config.h"
#include "subscribe_service.h"
#include "power_key_monitor.h"
#include "bf0_hal_gpio.h"
#include "button_config.h"
#include "qw_log.h"
#include "version_check_api.h"
#include "bf0_pm.h"

#define KEY_MODULE_LOG_TAG	"key_srv"

static rt_timer_t key_timer;
static rt_timer_t key_trace_timer;
static rt_timer_t wakeup_timer;

static struct Button keys_group[JOYn];
static struct Button keys_combine[LONG_COMBINE_BTN_SIZE];

static bool key_module_inited = false;
static void key_module_hw_init(void);

static bool key_input_paused = false;
static bool key_is_triggered = false;
static bool log_press_hold_flag = false;

//独立按键长按禁止标志位
static bool  key_long_press_disabled = false;

uint8_t keys_pin[JOYn] = {
    POWER_KEY_PIN,   //(96+46), PB46 POWER
    OK_KEY_PIN,      //A3 board PB45, A4 board PA77
    BACK_KEY_PIN     //(0+80),  PA80
};

static void hal_key_irq_handler(void* arg);
static  void local_key_irq_init(void);
static void keys_trace_timer_callback(void *p_context);
//用户注册的回调
static void (*user_trigger_callback)(uint8_t key,uint8_t key_event) = RT_NULL;

static uint8_t keys_get_state(JOYState_TypeDef joykey)
{
  return rt_pin_read(keys_pin[joykey]);
}

int keys_init(void)
{
    keys_pin[KEY3] = (hw_version_get() == HARDWARE_VERSION_A5) ? BACK_KEY_PIN_A5 : BACK_KEY_PIN;
    keys_pin[KEY2] = (hw_version_get() == HARDWARE_VERSION_A3) ? OK_KEY_PIN : \
                     (hw_version_get() == HARDWARE_VERSION_A4) ? OK_KEY_PIN_A4 : OK_KEY_PIN_A5;
    for(uint8_t i = 0; i < JOYn; i++){
        rt_pin_mode(keys_pin[i],PIN_MODE_INPUT);
    }
    return 0;
}
INIT_DEVICE_EXPORT(keys_init);

#define KEY_SIGNAL(name,n)  		\
	static uint8_t name(void)		\
	{								\
		return keys_get_state(n);	\
	}								\

KEY_SIGNAL(key1_signal,KEY1)
KEY_SIGNAL(key2_signal,KEY2)
KEY_SIGNAL(key3_signal,KEY3)


#define KEY_COMBINE_SIGNAL(name,m,n)  		\
	static uint8_t name(void)		\
	{								\
		return (keys_get_state(m)|keys_get_state(n));	\
	}
KEY_COMBINE_SIGNAL(key1_Key2_signal,KEY1,KEY2)
KEY_COMBINE_SIGNAL(key1_Key3_signal,KEY1,KEY3)
KEY_COMBINE_SIGNAL(key2_Key3_signal,KEY2,KEY3)

static uint8_t (*const key_active_signal[JOYn])(void) = {
		key1_signal,
		key2_signal,
		key3_signal,
};

static uint8_t (*const key_combine_active_signal[LONG_COMBINE_BTN_SIZE])(void) = {
		key1_Key2_signal,
		key1_Key3_signal,
		key2_Key3_signal,
};

void key_module_data_suspend(void)
{
	rt_timer_stop(key_timer);
    rt_timer_stop(key_trace_timer);
    keys_trace_timer_callback(NULL);
}

void key_module_data_resume(void)
{
    key_is_triggered = false;
	rt_timer_start(key_timer);
	//rt_timer_start(key_trace_timer);
}

void enable_all_key_interrupts(uint8_t enable)
{
	for (uint8_t i = 0; i < JOYn; i++) {
        if (keys_pin[i] <= GPIO1_PIN_NUM)
            rt_pin_irq_enable(keys_pin[i], enable);
    }
}

/**之前触发的长按值*/
static uint32_t long_trg_key_value = 0xFF;

/**
 * @brief 按下处理函数
 *
 * @param btn
 */
static void key_press_handler(void* btn)
{

	if(btn == RT_NULL)
	{
		return;
	}
	keys_status key_sta;
	key_sta.key = ((struct Button *)btn)->private_data;
	key_sta.key_event = PRESS_DOWN;

	if(key_sta.key == KEY1)
	{
		if(button_get_status(&keys_group[KEY3]) == PRESS_DOWN)
		{
			button_set_combinalock(&keys_group[KEY3]);
		}
	}
	else if (key_sta.key == KEY3)
	{
		if(button_get_status(&keys_group[KEY1]) == PRESS_DOWN)
		{
			button_set_combinalock(&keys_group[KEY1]);
		}
	}
	else
	{
		//don't do anything
	}
	power_key_handle(key_sta.key, key_sta.key_event);
	//加个回调
	if(user_trigger_callback)
	{
		user_trigger_callback(key_sta.key,key_sta.key_event);
	}
}

/**
 * @brief 按键抬起处理函数
 * @param btn
 */
static void key_release_handler(void* btn)
{
    if (btn == RT_NULL)
    {
        return;
    }
    keys_status key_sta;
    key_sta.key = ((struct Button *) btn)->private_data;
    key_sta.key_event = PRESS_UP;

    power_key_handle(key_sta.key, key_sta.key_event);
	if (log_press_hold_flag)
	{
		log_press_hold_flag = false;
		key_module_data_suspend();
	}
	//加个回调
	if(user_trigger_callback)
	{
		user_trigger_callback(key_sta.key,key_sta.key_event);
	}
}

/**
 * @brief 单击处理函数
 *
 * @param btn
 */
static void key_single_click_handler(void* btn)
{
	keys_status key_sta;
	key_sta.key = ((struct Button *)btn)->private_data;
	key_sta.key_event = SINGLE_CLICK;

	//调用用户注册的回调
	if(user_trigger_callback){
		user_trigger_callback(key_sta.key,key_sta.key_event);
		key_module_data_suspend();
		enable_all_key_interrupts(PIN_IRQ_ENABLE);
	}
}

/**
 * @brief  双击处理函数
 *
 * @param btn
 */
static void key_double_click_handler(void* btn)
{
	keys_status key_sta;
	key_sta.key = ((struct Button *)btn)->private_data;
	key_sta.key_event = DOUBLE_CLICK;
	key_module_data_suspend();
	enable_all_key_interrupts(PIN_IRQ_ENABLE);
}

/**
 * @brief 长按处理函数
 *
 * @param btn
 */
static void key_long_press_hold_handler(void* btn)
{
	keys_status key_sta;
	key_sta.key = ((struct Button *)btn)->private_data;
	key_sta.key_event = LONG_PRESS_HOLD;

	if(user_trigger_callback)
	{
		user_trigger_callback(key_sta.key,key_sta.key_event);
		log_press_hold_flag = true;
		enable_all_key_interrupts(PIN_IRQ_ENABLE);
	}
}

/**
 * @brief 组合按键单击处理函数
 *
 * @param btn
 */
static void key_combine_long_press_hold_handler(void* btn)
{
	keys_status key_sta;
	key_sta.key = ((struct Button *)btn)->private_data;
	key_sta.key_event = LONG_PRESS_HOLD;
	if(key_sta.key == LONG_PRESS_K1_K3)
	{
		button_set_combinalock(&keys_group[KEY3]);
		button_set_combinalock(&keys_group[KEY1]);
	}
	if(user_trigger_callback)
	{
		user_trigger_callback(key_sta.key,key_sta.key_event);
		key_module_data_suspend();
		key_is_triggered = false;
		enable_all_key_interrupts(PIN_IRQ_ENABLE);
	}
}

static bool key_input_is_idle(void)
{
	return (keys_get_state(KEY1)&&keys_get_state(KEY2)&&keys_get_state(KEY3));
}

static void keys_timer_callback(void *p_context)
{
	if(key_module_inited)
	{
		button_ticks();
        if(!key_is_triggered && !key_input_is_idle()){
            key_is_triggered = true;
            //rt_pm_hw_device_start();
        }else if(key_is_triggered && !key_input_is_idle()){

        }
	}else{
		key_module_hw_init();
	}
}

/*按键防呆保护机制,正常情况不使用*/
static void keys_trace_timer_callback(void *p_context)
{
	if(key_is_triggered){
        key_is_triggered = false;
        //rt_pm_hw_device_stop();
	}
	enable_all_key_interrupts(PIN_IRQ_ENABLE);
}



static void key_module_hw_init(void)
{
	if(!key_module_inited)
	{
		uint8_t i = 0;
		if(!key_input_is_idle())return;
		for(;i < JOYn; i++){
			button_init(&keys_group[i], key_active_signal[i], 0);
			button_set_private_data(&keys_group[i],i);
			button_attach(&keys_group[i], PRESS_DOWN, key_press_handler);
			button_attach(&keys_group[i], PRESS_UP, key_release_handler);
			button_attach(&keys_group[i], SINGLE_CLICK, key_single_click_handler);
			button_attach(&keys_group[i], DOUBLE_CLICK, key_double_click_handler);
			button_attach(&keys_group[i], LONG_PRESS_HOLD, key_long_press_hold_handler);

			button_attach(&keys_combine[i], LONG_COMBINE_HOLD, key_combine_long_press_hold_handler);


			button_start(&keys_group[i]);
		}

		for(i = 0;i < LONG_COMBINE_BTN_SIZE; i++){
			button_init(&keys_combine[i], key_combine_active_signal[i], 0);
			button_set_private_data(&keys_combine[i],i+LONG_PRESS_K1_K2);
			button_attach(&keys_combine[i], LONG_PRESS_HOLD, key_combine_long_press_hold_handler);
			button_start(&keys_combine[i]);
		}

		key_module_inited = true;
	}
}

static int local_btn = 0;
static void wakeup_timer_callback(void *p_context)
{
    // Tell app a single click is generated.
    user_trigger_callback(local_btn, SINGLE_CLICK);
}

static void hal_key_irq_handler(void* arg)
{
    // rt_kprintf("Key IRQ triggered, pin: %d\n", (int)arg);
    // rt_kprintf("KEY1 %d, KEY2 %d, KEY3 %d\n", keys_get_state(KEY1), keys_get_state(KEY2), keys_get_state(KEY3));
    /*
     * if HCPU wake up from standby by local key, it may miss the key press down low level state.
     * Then no key event is generated, which may lead to LCD not light up. And key timer will neven stop.
     */
    if (key_input_is_idle() && pm_get_power_mode() == PM_SLEEP_MODE_STANDBY)
    {
        local_btn = (int)arg;
        for (int i = 0; i < sizeof(keys_pin); i++)
        {
            if (local_btn == keys_pin[i])
            {
                local_btn = i;
                break;
            }
        }

        rt_timer_start(wakeup_timer);
    }
    else
    {
        //恢复定时器
        key_module_data_resume();
        //关闭中断
        enable_all_key_interrupts(PIN_IRQ_DISABLE);
    }

}

static  void local_key_irq_init(void)
{
    // 初始化这三个按键的中断
    for (uint8_t i = 0; i < JOYn; i++)
    {
        if (keys_pin[i] <= GPIO1_PIN_NUM)	//PB口中断由LCPU控制
        {
            rt_pin_attach_irq(keys_pin[i], PIN_IRQ_MODE_FALLING, hal_key_irq_handler,
                              (void *) (uintptr_t) keys_pin[i]);
            rt_pin_irq_enable(keys_pin[i], PIN_IRQ_ENABLE);
        }
    }
}

static void remote_button_event_cb(const void *in, uint32_t len)
{
    int pin = *(int *)in;
    uint32_t last_power_mode = pm_get_power_mode();
    TRACE_KEY_LOG("detect remote key:%d, last power mode %d", pin, last_power_mode);
    /*
     * if HCPU wake up from standby by remote key, it may miss the key press down low level state.
     * Then no key event is generated, which may lead to LCD not light up. And key timer will neven stop.
    */
    if (key_input_is_idle() && last_power_mode == PM_SLEEP_MODE_STANDBY)
    {
        int remote_btn = 0;
        if (pin == 1)
            remote_btn = KEY1;
        else if (pin == 2)
            remote_btn = KEY2;
        else if (pin == 4)
            remote_btn = KEY3;

        // Tell app a single click is generated.
        user_trigger_callback(remote_btn, SINGLE_CLICK);
    }
    else
    {
        /*恢复定时器,由于remote端已经做过防抖操作,此处可以立即执行button_click,但由于中断处理函数
        不适合做耗时和阻塞操作,这里仍用定时器线程处理按键*/
        key_module_data_resume();
    }
}

/**
 * @brief 初始化按键模块
 *
 * @return int
 */
static int key_module_init(void)
{
   key_module_hw_init();
   key_trace_timer = rt_timer_create("kt0",keys_trace_timer_callback,NULL,1000,RT_TIMER_FLAG_PERIODIC|RT_TIMER_FLAG_SOFT_TIMER);

   key_timer = rt_timer_create("key_srv",keys_timer_callback,NULL,5,RT_TIMER_FLAG_PERIODIC|RT_TIMER_FLAG_SOFT_TIMER);

   wakeup_timer = rt_timer_create("wakeup", wakeup_timer_callback, NULL, 5, RT_TIMER_FLAG_ONE_SHOT | RT_TIMER_FLAG_SOFT_TIMER);

   //初始化硬件中断
   local_key_irq_init();
   power_key_monitor_init();
   return 0;
}

/**
 * @brief 订阅跨核唤醒事件
 *
 * @return int
 */
int key_module_subscribe_wakeup_event(void)
{
    key_module_init();
    optional_config_t config = {.sampling_rate = 0};
    int ret = qw_dataserver_subscribe_id(DATA_ID_EVENT_WAKEUP, remote_button_event_cb,
                                      &config);
    if (ERRO_CODE_OK != ret)
    {
        QW_LOG_E(KEY_MODULE_LOG_TAG, "key module sub fail:%d", ret);
    }
	return ret;
}


/**
 * @brief  注册按键中断
 *
 * @param  callback
 * @return int
 */
int key_module_register_callback(void (*callback)(uint8_t key,uint8_t key_event))
{
	if(callback == RT_NULL)
	{
		return -1;
	}
	user_trigger_callback = callback;
	return 0;
}

// 用户注册的光旋钮回调
static void (*user_knob_callback)(int32_t keyval,int32_t key_event) = RT_NULL;
/**
 * @brief  注册光旋钮回调
 *
 * @param  callback
 * @return int
 */
int knob_module_register_callback(void (*callback)(int32_t keyval,int32_t key_event))
{
	if(callback == RT_NULL)
	{
		return -1;
	}
	user_knob_callback = callback;
	return 0;
}

/**
 * @brief  上下旋转处理函数
 *
 * @param btn
 */
int32_t knob_key_handler(int32_t key_event,int32_t keyval)
{
	int32_t ret = 0;
	knob_status key_sta;
	key_sta.keyval = keyval;
	key_sta.key_event = key_event;
    if(key_event == KNOB_MOTION_RELEASE) return 0;  /* 过滤掉释放 */

    //加个回调:在光旋钮发送上下旋时通知
	if(user_trigger_callback)
	{
        if(key_event == KNOB_MOTION_ROTATE_CW)  user_trigger_callback(0xF1,0);
        if(key_event == KNOB_MOTION_ROTATE_CCW)  user_trigger_callback(0xF2,0);
	}

    // 调用用户注册的回调
	if(user_knob_callback){
		user_knob_callback(key_sta.keyval,key_sta.key_event);
		return 0;
	}else{
		return 1;
	}
}

/************************************************************************
 *@function:bool key_is_pressed(JOYState_TypeDef idx)
 *@brief:检测KEY1~KEY3是否有按下状态.
 *@param:idx key type,see JOYState_TypeDef defined
 *@return:the key idx was pressed.
*************************************************************************/
bool key_is_pressed(JOYState_TypeDef idx)
{
    if (idx >= JOY_NONE)
        return false;
    return PIN_LOW == keys_get_state(idx);
}