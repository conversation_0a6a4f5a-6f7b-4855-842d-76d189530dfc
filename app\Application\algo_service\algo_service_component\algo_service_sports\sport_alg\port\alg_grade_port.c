/************************************************************************​
*Copyright(c) 2025, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#include "alg_grade_port.h"
#include "alg_grade.h"

static float s_alg_grade_maf_buf[ALG_GRADE_MAF_BUF_SIZE] = { 0 };
static float s_alg_grade_dir_calc_buf[ALG_GRADE_DIR_CALC_BUF_SIZE] = { 0 };
static GradeDiffData s_alg_grade_diff_data_buf[ALG_GRADE_DIFF_DATA_BUF_SIZE] = { 0 };

#ifdef IGS_DEV
static GradeCalculator s_grade_calculator;
#else
static GradeCalculator s_grade_calculator = {
    .dir_calculator = {
        .dalt_maf = {
            .buf = s_alg_grade_maf_buf,
            .capacity = ALG_GRADE_MAF_BUF_SIZE,
        },
        .buf = s_alg_grade_dir_calc_buf,
        .capacity = ALG_GRADE_DIR_CALC_BUF_SIZE,
    },
    .buf = s_alg_grade_diff_data_buf,
    .capacity = ALG_GRADE_DIFF_DATA_BUF_SIZE,
    .timeout = ALG_GRADE_CALC_TIMEOUT,
};
#endif

void alg_grade_init(void)
{
#ifdef IGS_DEV
    s_grade_calculator.dir_calculator.dalt_maf.buf = s_alg_grade_maf_buf;
    s_grade_calculator.dir_calculator.dalt_maf.capacity = ALG_GRADE_MAF_BUF_SIZE;
    s_grade_calculator.dir_calculator.buf = s_alg_grade_dir_calc_buf;
    s_grade_calculator.dir_calculator.capacity = ALG_GRADE_DIR_CALC_BUF_SIZE;
    s_grade_calculator.buf = s_alg_grade_diff_data_buf;
    s_grade_calculator.capacity = ALG_GRADE_DIFF_DATA_BUF_SIZE;
    s_grade_calculator.timeout = ALG_GRADE_CALC_TIMEOUT;
#endif

    grade_calculator_reset(&s_grade_calculator);
}

int alg_grade_exec(const alg_grade_input_t *input, alg_grade_output_t *output)
{
    return grade_calculator_exec(&s_grade_calculator, (GradeCalcInput *)input, (GradeCalcOutput *)output);
}
