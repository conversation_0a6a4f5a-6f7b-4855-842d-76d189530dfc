/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   Translate.h
@Time    :   2024/12/11 13:57:31
*
**************************************************************************/

#ifndef __TRANSLATE_H
#define __TRANSLATE_H

#include "cfg_header_def.h"

#define ONLY_ENABLE_CN_ENG 0 //只支持中文和英文

 #define EXPORT_TRANSLATE_KEY

void declare_list(const char** list, int count);

extern "C" const char* translate_text(const char* key);

struct TM_DECLARE_MACHINE
{
    TM_DECLARE_MACHINE(const char** arg, int count)
	{
		declare_list(arg, count);
    }
};

// 添加字符串到翻译键值表, 会自动忽略重复项和空字串
#define TM_DECLARE(list) static TM_DECLARE_MACHINE TM_DECLARE_##list(list, sizeof(list) / sizeof(char*));
#define TM_KEY(key) static TM_DECLARE_MACHINE TM_KEY_##key((const char**)key, 0);

// 翻译文本
#define _TM(key) translate_text(key)

#ifdef EXPORT_TRANSLATE_KEY
#define TM_DECLARE_END_STR "TM_DECLARE_END"
#endif // EXPORT_TRANSLATE_KEY

#endif //__TRANSLATE_H

