[core]
	repositoryFormatVersion = 1
	filemode = false
	symlinks = false
	ignorecase = true
[extensions]
	worktreeConfig = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[remote "origin"]
	url = ssh://yanx<PERSON>qiang@********:29418/qw_platform
	review = http://********:8081
	projectname = qw_platform
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "develop"]
	remote = origin
	merge = refs/heads/develop
[branch "wr02_release"]
	remote = origin
	merge = refs/heads/wr02_release
	vscode-merge-base = origin/wr02_release
