PID: 43040 START: 1752630631412630016 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, -u, ssh://yanxuqiang@********:29418/wr02, -m, wearable-sifli-wr02-develop.xml, -b, develop, --worktree [sid=repo-20250716T015028Z-P0000bafc/repo-20250716T015031Z-P0000a820]

PID: 43040 START: 1752630631518537472 :git command E:/Snowa/.repo/manifests.git ['git', 'init'] with debug: : export GIT_DIR=E:/Snowa/.repo/manifests.git
: git init 1>| 2>|

PID: 43040 END: 1752630631565381120 :git command E:/Snowa/.repo/manifests.git ['git', 'init'] with debug: : export GIT_DIR=E:/Snowa/.repo/manifests.git
: git init 1>| 2>|

PID: 43040 START: 1752630631574368768 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --null --list 1>| 2>|

PID: 43040 END: 1752630631608251648 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --null --list 1>| 2>|

PID: 43040 START: 1752630631610248704 :: parsing C:\Users\<USER>\Users\qwkj/.gitconfig

PID: 43040 START: 1752630631611242240 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 43040 END: 1752630631646126080 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 43040 START: 1752630631647122176 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 43040 END: 1752630631682006016 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 43040 START: 1752630631683002368 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --unset-all core.bare 1>| 2>|

PID: 43040 END: 1752630631719879168 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --unset-all core.bare 1>| 2>|

PID: 43040 START: 1752630631720875264 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'remote.origin.url', 'ssh://yanxuqiang@********:29418/wr02'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all remote.origin.url ssh://yanxuqiang@********:29418/wr02 1>| 2>|

PID: 43040 END: 1752630631758752768 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'remote.origin.url', 'ssh://yanxuqiang@********:29418/wr02'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all remote.origin.url ssh://yanxuqiang@********:29418/wr02 1>| 2>|

PID: 43040 START: 1752630631758752768 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 43040 END: 1752630631796622592 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 43040 START: 1752630631797621760 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'manifest.platform', 'auto'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all manifest.platform auto 1>| 2>|

PID: 43040 END: 1752630631833498880 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'manifest.platform', 'auto'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all manifest.platform auto 1>| 2>|

PID: 43040 START: 1752630631833498880 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.worktree', 'true'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.worktree true 1>| 2>|

PID: 43040 END: 1752630631869378816 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.worktree', 'true'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.worktree true 1>| 2>|

PID: 43040 START: 1752630631870378496 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 43040 END: 1752630631907253504 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 43040 START: 1752630631908249344 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'extensions.preciousObjects', 'true'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all extensions.preciousObjects true 1>| 2>|

PID: 43040 END: 1752630631943132416 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'extensions.preciousObjects', 'true'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all extensions.preciousObjects true 1>| 2>|

PID: 43040 START: 1752630631944128512 :git command E:/Snowa/.repo/manifests.git ['git', 'fetch', '--quiet', 'origin', '--recurse-submodules=no', '--tags', '+refs/heads/develop:refs/remotes/origin/develop', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_OBJECT_DIRECTORY=E:/Snowa/.repo/manifests.git/objects
: git fetch --quiet origin --recurse-submodules=no --tags +refs/heads/develop:refs/remotes/origin/develop +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 43040 END: 1752630632353611520 :git command E:/Snowa/.repo/manifests.git ['git', 'fetch', '--quiet', 'origin', '--recurse-submodules=no', '--tags', '+refs/heads/develop:refs/remotes/origin/develop', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_OBJECT_DIRECTORY=E:/Snowa/.repo/manifests.git/objects
: git fetch --quiet origin --recurse-submodules=no --tags +refs/heads/develop:refs/remotes/origin/develop +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 43040 START: 1752630632353611520 :git command E:/Snowa/.repo/manifests.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 43040 END: 1752630632419392512 :git command E:/Snowa/.repo/manifests.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 43040 START: 1752630632420388096 :git command E:/Snowa/.repo/manifests.git ['git', 'worktree', 'prune'] with debug: : git worktree prune 1>| 2>|

PID: 43040 END: 1752630632455275008 :git command E:/Snowa/.repo/manifests.git ['git', 'worktree', 'prune'] with debug: : git worktree prune 1>| 2>|

PID: 43040 START: 1752630632455275008 :git command E:/Snowa/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/develop^0'] with debug: : git rev-parse --verify refs/remotes/origin/develop^0 1>| 2>|

PID: 43040 END: 1752630632491155200 :git command E:/Snowa/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/develop^0'] with debug: : git rev-parse --verify refs/remotes/origin/develop^0 1>| 2>|

PID: 43040 START: 1752630632491155200 :git command E:/Snowa/.repo/manifests.git ['git', 'worktree', 'add', '-ff', '--checkout', '--detach', '--lock', 'E:/Snowa/.repo/manifests', '2c85f3670bb5044819f6a5faa3cc8d9ddc4fa5f7'] with debug: : git worktree add -ff --checkout --detach --lock E:/Snowa/.repo/manifests 2c85f3670bb5044819f6a5faa3cc8d9ddc4fa5f7 1>| 2>|

PID: 43040 END: 1752630632655612928 :git command E:/Snowa/.repo/manifests.git ['git', 'worktree', 'add', '-ff', '--checkout', '--detach', '--lock', 'E:/Snowa/.repo/manifests', '2c85f3670bb5044819f6a5faa3cc8d9ddc4fa5f7'] with debug: : git worktree add -ff --checkout --detach --lock E:/Snowa/.repo/manifests 2c85f3670bb5044819f6a5faa3cc8d9ddc4fa5f7 1>| 2>|

PID: 43040 START: 1752630632656610816 :: load refs E:/Snowa/.repo/manifests.git

PID: 43040 END: 1752630632657605120 :: load refs E:/Snowa/.repo/manifests.git

PID: 43040 START: 1752630632658601472 :git command E:/Snowa/.repo/manifests.git ['git', 'checkout', '-q', '2c85f3670bb5044819f6a5faa3cc8d9ddc4fa5f7', '--'] with debug: 
: cd E:/Snowa/.repo/manifests
: git checkout -q 2c85f3670bb5044819f6a5faa3cc8d9ddc4fa5f7 -- 2>|

PID: 43040 END: 1752630632710428672 :git command E:/Snowa/.repo/manifests.git ['git', 'checkout', '-q', '2c85f3670bb5044819f6a5faa3cc8d9ddc4fa5f7', '--'] with debug: 
: cd E:/Snowa/.repo/manifests
: git checkout -q 2c85f3670bb5044819f6a5faa3cc8d9ddc4fa5f7 -- 2>|

PID: 43040 START: 1752630632711425280 :git command E:/Snowa/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 43040 END: 1752630632748301568 :git command E:/Snowa/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 43040 START: 1752630632748301568 :: scan refs E:/Snowa/.repo/manifests.git

PID: 43040 END: 1752630632749298432 :: scan refs E:/Snowa/.repo/manifests.git

PID: 43040 START: 1752630632750298112 :: scan refs E:/Snowa/.repo/manifests.git

PID: 43040 END: 1752630632750298112 :: scan refs E:/Snowa/.repo/manifests.git

PID: 43040 START: 1752630632750298112 :git command E:/Snowa/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '2c85f3670bb5044819f6a5faa3cc8d9ddc4fa5f7'] with debug: : git update-ref refs/heads/default 2c85f3670bb5044819f6a5faa3cc8d9ddc4fa5f7 1>| 2>|

PID: 43040 END: 1752630632790164736 :git command E:/Snowa/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '2c85f3670bb5044819f6a5faa3cc8d9ddc4fa5f7'] with debug: : git update-ref refs/heads/default 2c85f3670bb5044819f6a5faa3cc8d9ddc4fa5f7 1>| 2>|

PID: 43040 START: 1752630632791158784 :git command E:/Snowa/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 43040 END: 1752630632828035328 :git command E:/Snowa/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 43040 START: 1752630632850129152 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 43040 END: 1752630632884015872 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 43040 END: 1752630632885009408 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, -u, ssh://yanxuqiang@********:29418/wr02, -m, wearable-sifli-wr02-develop.xml, -b, develop, --worktree [sid=repo-20250716T015028Z-P0000bafc/repo-20250716T015031Z-P0000a820]

PID: 30820 START: 1752630640465103872 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync [sid=repo-20250716T015040Z-P00003868/repo-20250716T015040Z-P00007864]

PID: 30820 START: 1752630640530881536 :git command None ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --null --list 1>| 2>|

PID: 30820 END: 1752630640566764288 :git command None ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --null --list 1>| 2>|

PID: 30820 START: 1752630640567757312 :: parsing C:\Users\<USER>\Users\qwkj/.gitconfig

PID: 30820 START: 1752630640577729536 :git command None ['git', 'config', '--file', 'E:/Snowa/.repo/repo/.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/repo/.git\config --includes --null --list 1>| 2>|

PID: 30820 END: 1752630640611614208 :git command None ['git', 'config', '--file', 'E:/Snowa/.repo/repo/.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/repo/.git\config --includes --null --list 1>| 2>|

PID: 30820 START: 1752630640633544704 :git command E:/Snowa/.repo/manifests.git ['git', 'fetch', '--quiet', 'origin', '--recurse-submodules=no', '--tags', '+refs/heads/*:refs/remotes/origin/*', '+refs/heads/develop:refs/remotes/origin/develop', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_DIR=E:/Snowa/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=E:/Snowa/.repo/manifests.git/objects
: git fetch --quiet origin --recurse-submodules=no --tags +refs/heads/*:refs/remotes/origin/* +refs/heads/develop:refs/remotes/origin/develop +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 30820 END: 1752630640983036416 :git command E:/Snowa/.repo/manifests.git ['git', 'fetch', '--quiet', 'origin', '--recurse-submodules=no', '--tags', '+refs/heads/*:refs/remotes/origin/*', '+refs/heads/develop:refs/remotes/origin/develop', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_DIR=E:/Snowa/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=E:/Snowa/.repo/manifests.git/objects
: git fetch --quiet origin --recurse-submodules=no --tags +refs/heads/*:refs/remotes/origin/* +refs/heads/develop:refs/remotes/origin/develop +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 30820 START: 1752630640983036416 :: load refs E:/Snowa/.repo/manifests.git

PID: 30820 END: 1752630640985026048 :: load refs E:/Snowa/.repo/manifests.git

PID: 30820 START: 1752630640986025472 :git command E:/Snowa/.repo/manifests.git ['git', 'symbolic-ref', '-m', 'redirecting to worktree scope', 'refs/remotes/m/develop', 'refs/worktree/m/develop'] with debug: : git symbolic-ref -m redirecting to worktree scope refs/remotes/m/develop refs/worktree/m/develop 1>| 2>|

PID: 30820 END: 1752630641021902848 :git command E:/Snowa/.repo/manifests.git ['git', 'symbolic-ref', '-m', 'redirecting to worktree scope', 'refs/remotes/m/develop', 'refs/worktree/m/develop'] with debug: : git symbolic-ref -m redirecting to worktree scope refs/remotes/m/develop refs/worktree/m/develop 1>| 2>|

PID: 30820 START: 1752630641022902272 :: scan refs E:/Snowa/.repo/manifests.git

PID: 30820 END: 1752630641022902272 :: scan refs E:/Snowa/.repo/manifests.git

PID: 30820 START: 1752630641022902272 :: load refs E:/Snowa/.repo/manifests.git

PID: 30820 END: 1752630641024896000 :: load refs E:/Snowa/.repo/manifests.git

PID: 30820 START: 1752630641025889024 :git command E:/Snowa/.repo/manifests.git ['git', 'update-ref', '-m', 'manifest set to refs/heads/develop', '--no-deref', 'HEAD', 'refs/remotes/origin/develop'] with debug: : git update-ref -m manifest set to refs/heads/develop --no-deref HEAD refs/remotes/origin/develop 1>| 2>|

PID: 30820 END: 1752630641064759296 :git command E:/Snowa/.repo/manifests.git ['git', 'update-ref', '-m', 'manifest set to refs/heads/develop', '--no-deref', 'HEAD', 'refs/remotes/origin/develop'] with debug: : git update-ref -m manifest set to refs/heads/develop --no-deref HEAD refs/remotes/origin/develop 1>| 2>|

PID: 30820 START: 1752630641065755904 :: scan refs E:/Snowa/.repo/manifests.git

PID: 30820 END: 1752630641065755904 :: scan refs E:/Snowa/.repo/manifests.git

PID: 30820 START: 1752630641066752512 :: load refs E:/Snowa/.repo/manifests.git

PID: 30820 END: 1752630641068748800 :: load refs E:/Snowa/.repo/manifests.git

PID: 30820 START: 1752630641068748800 :git command E:/Snowa/.repo/manifests.git ['git', 'symbolic-ref', '-m', 'manifest set to refs/heads/develop', 'refs/worktree/m/develop', 'refs/remotes/origin/develop'] with debug: 
: cd E:/Snowa/.repo/manifests
: git symbolic-ref -m manifest set to refs/heads/develop refs/worktree/m/develop refs/remotes/origin/develop 1>| 2>|

PID: 30820 END: 1752630641105625856 :git command E:/Snowa/.repo/manifests.git ['git', 'symbolic-ref', '-m', 'manifest set to refs/heads/develop', 'refs/worktree/m/develop', 'refs/remotes/origin/develop'] with debug: 
: cd E:/Snowa/.repo/manifests
: git symbolic-ref -m manifest set to refs/heads/develop refs/worktree/m/develop refs/remotes/origin/develop 1>| 2>|

PID: 30820 START: 1752630641106622464 :: scan refs E:/Snowa/.repo/manifests.git

PID: 30820 END: 1752630641106622464 :: scan refs E:/Snowa/.repo/manifests.git

PID: 30820 START: 1752630641107618816 :: scan refs E:/Snowa/.repo/manifests.git

PID: 30820 END: 1752630641107618816 :: scan refs E:/Snowa/.repo/manifests.git

PID: 30820 START: 1752630641108612352 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.existingprojectcount', '0'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.existingprojectcount 0 1>| 2>|

PID: 30820 END: 1752630641144492800 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.existingprojectcount', '0'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.existingprojectcount 0 1>| 2>|

PID: 30820 START: 1752630641144492800 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.newprojectcount', '6'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.newprojectcount 6 1>| 2>|

PID: 30820 END: 1752630641180908288 :git command E:/Snowa/.repo/manifests.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.newprojectcount', '6'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.newprojectcount 6 1>| 2>|

PID: 30820 START: 1752630641805832960 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'init'] with debug: 
: export GIT_DIR=E:/Snowa/.repo/worktrees/app.git
: git init 1>| 2>|

PID: 30820 END: 1752630641854665984 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'init'] with debug: 
: export GIT_DIR=E:/Snowa/.repo/worktrees/app.git
: git init 1>| 2>|

PID: 30820 START: 1752630641858655232 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --null --list 1>| 2>|

PID: 30820 END: 1752630641894531840 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --null --list 1>| 2>|

PID: 30820 START: 1752630641895534080 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 30820 END: 1752630641932420096 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 30820 START: 1752630641933416448 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--replace-all', 'extensions.worktreeConfig', 'true'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --replace-all extensions.worktreeConfig true 1>| 2>|

PID: 30820 END: 1752630641968296704 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--replace-all', 'extensions.worktreeConfig', 'true'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --replace-all extensions.worktreeConfig true 1>| 2>|

PID: 30820 START: 1752630641969296896 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 30820 END: 1752630642007166720 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 30820 START: 1752630642008163328 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 30820 END: 1752630642043046656 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 30820 START: 1752630642044043520 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --unset-all core.bare 1>| 2>|

PID: 30820 END: 1752630642079924224 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --unset-all core.bare 1>| 2>|

PID: 30820 START: 1752630642080923648 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--replace-all', 'remote.origin.url', 'ssh://yanxuqiang@********:29418/app'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --replace-all remote.origin.url ssh://yanxuqiang@********:29418/app 1>| 2>|

PID: 30820 END: 1752630642116799744 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--replace-all', 'remote.origin.url', 'ssh://yanxuqiang@********:29418/app'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --replace-all remote.origin.url ssh://yanxuqiang@********:29418/app 1>| 2>|

PID: 30820 START: 1752630642116799744 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--replace-all', 'remote.origin.review', 'http://********:8081'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --replace-all remote.origin.review http://********:8081 1>| 2>|

PID: 30820 END: 1752630642151683072 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--replace-all', 'remote.origin.review', 'http://********:8081'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --replace-all remote.origin.review http://********:8081 1>| 2>|

PID: 30820 START: 1752630642152683008 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--replace-all', 'remote.origin.projectname', 'app'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --replace-all remote.origin.projectname app 1>| 2>|

PID: 30820 END: 1752630642188346624 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--replace-all', 'remote.origin.projectname', 'app'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --replace-all remote.origin.projectname app 1>| 2>|

PID: 30820 START: 1752630642188346624 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 30820 END: 1752630642224726016 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 30820 START: 1752630642225722880 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'fetch', '--quiet', 'origin', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/*:refs/remotes/origin/*', '+refs/heads/develop:refs/remotes/origin/develop', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_OBJECT_DIRECTORY=E:/Snowa/.repo/worktrees/app.git/objects
: git fetch --quiet origin --prune --recurse-submodules=no --tags +refs/heads/*:refs/remotes/origin/* +refs/heads/develop:refs/remotes/origin/develop +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 30820 END: 1752630717914651648 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'fetch', '--quiet', 'origin', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/*:refs/remotes/origin/*', '+refs/heads/develop:refs/remotes/origin/develop', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_OBJECT_DIRECTORY=E:/Snowa/.repo/worktrees/app.git/objects
: git fetch --quiet origin --prune --recurse-submodules=no --tags +refs/heads/*:refs/remotes/origin/* +refs/heads/develop:refs/remotes/origin/develop +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 30820 START: 1752630717914651648 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 30820 END: 1752630717991395584 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 30820 START: 1752630717991395584 :: load refs E:/Snowa/.repo/worktrees/app.git

PID: 30820 END: 1752630717993388288 :: load refs E:/Snowa/.repo/worktrees/app.git

PID: 30820 START: 1752630717993388288 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'symbolic-ref', '-m', 'redirecting to worktree scope', 'refs/remotes/m/develop', 'refs/worktree/m/develop'] with debug: : git symbolic-ref -m redirecting to worktree scope refs/remotes/m/develop refs/worktree/m/develop 1>| 2>|

PID: 30820 END: 1752630718029264896 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'symbolic-ref', '-m', 'redirecting to worktree scope', 'refs/remotes/m/develop', 'refs/worktree/m/develop'] with debug: : git symbolic-ref -m redirecting to worktree scope refs/remotes/m/develop refs/worktree/m/develop 1>| 2>|

PID: 30820 START: 1752630718030262016 :: scan refs E:/Snowa/.repo/worktrees/app.git

PID: 30820 END: 1752630718030262016 :: scan refs E:/Snowa/.repo/worktrees/app.git

PID: 30820 START: 1752630718031258624 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'init'] with debug: 
: export GIT_DIR=E:/Snowa/.repo/worktrees/qw_algo/navigation.git
: git init 1>| 2>|

PID: 30820 END: 1752630718077108736 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'init'] with debug: 
: export GIT_DIR=E:/Snowa/.repo/worktrees/qw_algo/navigation.git
: git init 1>| 2>|

PID: 30820 START: 1752630718080099072 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --null --list 1>| 2>|

PID: 30820 END: 1752630718114981888 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --null --list 1>| 2>|

PID: 30820 START: 1752630718116974848 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 30820 END: 1752630718151855104 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 30820 START: 1752630718151855104 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--replace-all', 'extensions.worktreeConfig', 'true'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --replace-all extensions.worktreeConfig true 1>| 2>|

PID: 30820 END: 1752630718187735040 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--replace-all', 'extensions.worktreeConfig', 'true'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --replace-all extensions.worktreeConfig true 1>| 2>|

PID: 30820 START: 1752630718188735744 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 30820 END: 1752630718223615232 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 30820 START: 1752630718224615168 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 30820 END: 1752630718259497984 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 30820 START: 1752630718259497984 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --unset-all core.bare 1>| 2>|

PID: 30820 END: 1752630718295379712 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --unset-all core.bare 1>| 2>|

PID: 30820 START: 1752630718295379712 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--replace-all', 'remote.origin.url', 'ssh://yanxuqiang@********:29418/qw_algo/navigation'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --replace-all remote.origin.url ssh://yanxuqiang@********:29418/qw_algo/navigation 1>| 2>|

PID: 30820 END: 1752630718331255296 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--replace-all', 'remote.origin.url', 'ssh://yanxuqiang@********:29418/qw_algo/navigation'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --replace-all remote.origin.url ssh://yanxuqiang@********:29418/qw_algo/navigation 1>| 2>|

PID: 30820 START: 1752630718331255296 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--replace-all', 'remote.origin.review', 'http://********:8081'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --replace-all remote.origin.review http://********:8081 1>| 2>|

PID: 30820 END: 1752630718367134976 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--replace-all', 'remote.origin.review', 'http://********:8081'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --replace-all remote.origin.review http://********:8081 1>| 2>|

PID: 30820 START: 1752630718367134976 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--replace-all', 'remote.origin.projectname', 'qw_algo/navigation'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --replace-all remote.origin.projectname qw_algo/navigation 1>| 2>|

PID: 30820 END: 1752630718403014912 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--replace-all', 'remote.origin.projectname', 'qw_algo/navigation'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --replace-all remote.origin.projectname qw_algo/navigation 1>| 2>|

PID: 30820 START: 1752630718403014912 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 30820 END: 1752630718438895104 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 30820 START: 1752630718439894784 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'fetch', '--quiet', 'origin', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/*:refs/remotes/origin/*', '+refs/heads/cm_develop:refs/remotes/origin/cm_develop', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_OBJECT_DIRECTORY=E:/Snowa/.repo/worktrees/qw_algo/navigation.git/objects
: git fetch --quiet origin --prune --recurse-submodules=no --tags +refs/heads/*:refs/remotes/origin/* +refs/heads/cm_develop:refs/remotes/origin/cm_develop +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 30820 END: 1752630718738643712 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'fetch', '--quiet', 'origin', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/*:refs/remotes/origin/*', '+refs/heads/cm_develop:refs/remotes/origin/cm_develop', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_OBJECT_DIRECTORY=E:/Snowa/.repo/worktrees/qw_algo/navigation.git/objects
: git fetch --quiet origin --prune --recurse-submodules=no --tags +refs/heads/*:refs/remotes/origin/* +refs/heads/cm_develop:refs/remotes/origin/cm_develop +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 30820 START: 1752630718738643712 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 30820 END: 1752630718782496512 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 30820 START: 1752630718782496512 :: load refs E:/Snowa/.repo/worktrees/qw_algo/navigation.git

PID: 30820 END: 1752630718784493824 :: load refs E:/Snowa/.repo/worktrees/qw_algo/navigation.git

PID: 30820 START: 1752630718784493824 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'symbolic-ref', '-m', 'redirecting to worktree scope', 'refs/remotes/m/develop', 'refs/worktree/m/develop'] with debug: : git symbolic-ref -m redirecting to worktree scope refs/remotes/m/develop refs/worktree/m/develop 1>| 2>|

PID: 30820 END: 1752630718820373504 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'symbolic-ref', '-m', 'redirecting to worktree scope', 'refs/remotes/m/develop', 'refs/worktree/m/develop'] with debug: : git symbolic-ref -m redirecting to worktree scope refs/remotes/m/develop refs/worktree/m/develop 1>| 2>|

PID: 30820 START: 1752630718820373504 :: scan refs E:/Snowa/.repo/worktrees/qw_algo/navigation.git

PID: 30820 END: 1752630718821366784 :: scan refs E:/Snowa/.repo/worktrees/qw_algo/navigation.git

PID: 30820 START: 1752630718821366784 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'init'] with debug: 
: export GIT_DIR=E:/Snowa/.repo/worktrees/qw_platform.git
: git init 1>| 2>|

PID: 30820 END: 1752630718867216896 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'init'] with debug: 
: export GIT_DIR=E:/Snowa/.repo/worktrees/qw_platform.git
: git init 1>| 2>|

PID: 30820 START: 1752630718870206464 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --null --list 1>| 2>|

PID: 30820 END: 1752630718904089856 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --null --list 1>| 2>|

PID: 30820 START: 1752630718906086400 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 30820 END: 1752630718941963008 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 30820 START: 1752630718941963008 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--replace-all', 'extensions.worktreeConfig', 'true'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --replace-all extensions.worktreeConfig true 1>| 2>|

PID: 30820 END: 1752630718978840576 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--replace-all', 'extensions.worktreeConfig', 'true'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --replace-all extensions.worktreeConfig true 1>| 2>|

PID: 30820 START: 1752630718979836928 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 30820 END: 1752630719014723072 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 30820 START: 1752630719015720192 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 30820 END: 1752630719050599936 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 30820 START: 1752630719050599936 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --unset-all core.bare 1>| 2>|

PID: 30820 END: 1752630719086479616 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --unset-all core.bare 1>| 2>|

PID: 30820 START: 1752630719087476736 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--replace-all', 'remote.origin.url', 'ssh://yanxuqiang@********:29418/qw_platform'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --replace-all remote.origin.url ssh://yanxuqiang@********:29418/qw_platform 1>| 2>|

PID: 30820 END: 1752630719122359808 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--replace-all', 'remote.origin.url', 'ssh://yanxuqiang@********:29418/qw_platform'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --replace-all remote.origin.url ssh://yanxuqiang@********:29418/qw_platform 1>| 2>|

PID: 30820 START: 1752630719123356672 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--replace-all', 'remote.origin.review', 'http://********:8081'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --replace-all remote.origin.review http://********:8081 1>| 2>|

PID: 30820 END: 1752630719159239680 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--replace-all', 'remote.origin.review', 'http://********:8081'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --replace-all remote.origin.review http://********:8081 1>| 2>|

PID: 30820 START: 1752630719159239680 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--replace-all', 'remote.origin.projectname', 'qw_platform'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --replace-all remote.origin.projectname qw_platform 1>| 2>|

PID: 30820 END: 1752630719195116544 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--replace-all', 'remote.origin.projectname', 'qw_platform'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --replace-all remote.origin.projectname qw_platform 1>| 2>|

PID: 30820 START: 1752630719195116544 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 30820 END: 1752630719230996224 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 30820 START: 1752630719231996160 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'fetch', '--quiet', 'origin', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/*:refs/remotes/origin/*', '+refs/heads/develop:refs/remotes/origin/develop', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_OBJECT_DIRECTORY=E:/Snowa/.repo/worktrees/qw_platform.git/objects
: git fetch --quiet origin --prune --recurse-submodules=no --tags +refs/heads/*:refs/remotes/origin/* +refs/heads/develop:refs/remotes/origin/develop +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 30820 END: 1752630729516524288 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'fetch', '--quiet', 'origin', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/*:refs/remotes/origin/*', '+refs/heads/develop:refs/remotes/origin/develop', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_OBJECT_DIRECTORY=E:/Snowa/.repo/worktrees/qw_platform.git/objects
: git fetch --quiet origin --prune --recurse-submodules=no --tags +refs/heads/*:refs/remotes/origin/* +refs/heads/develop:refs/remotes/origin/develop +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 30820 START: 1752630729517517056 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 30820 END: 1752630729585294080 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 30820 START: 1752630729585294080 :: load refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 30820 END: 1752630729586881792 :: load refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 30820 START: 1752630729587449088 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'symbolic-ref', '-m', 'redirecting to worktree scope', 'refs/remotes/m/develop', 'refs/worktree/m/develop'] with debug: : git symbolic-ref -m redirecting to worktree scope refs/remotes/m/develop refs/worktree/m/develop 1>| 2>|

PID: 30820 END: 1752630729623219712 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'symbolic-ref', '-m', 'redirecting to worktree scope', 'refs/remotes/m/develop', 'refs/worktree/m/develop'] with debug: : git symbolic-ref -m redirecting to worktree scope refs/remotes/m/develop refs/worktree/m/develop 1>| 2>|

PID: 30820 START: 1752630729623739648 :: scan refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 30820 END: 1752630729624285696 :: scan refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 30820 START: 1752630729624828672 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'init'] with debug: 
: export GIT_DIR=E:/Snowa/.repo/worktrees/sifli.git
: git init 1>| 2>|

PID: 30820 END: 1752630729682758400 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'init'] with debug: 
: export GIT_DIR=E:/Snowa/.repo/worktrees/sifli.git
: git init 1>| 2>|

PID: 30820 START: 1752630729686035200 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --null --list 1>| 2>|

PID: 30820 END: 1752630729720475392 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --null --list 1>| 2>|

PID: 30820 START: 1752630729721568256 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 30820 END: 1752630729755879168 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 30820 START: 1752630729756409856 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--replace-all', 'extensions.worktreeConfig', 'true'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --replace-all extensions.worktreeConfig true 1>| 2>|

PID: 30820 END: 1752630729791830272 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--replace-all', 'extensions.worktreeConfig', 'true'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --replace-all extensions.worktreeConfig true 1>| 2>|

PID: 30820 START: 1752630729792356096 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 30820 END: 1752630729826586880 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 30820 START: 1752630729827112192 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 30820 END: 1752630729862874112 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 30820 START: 1752630729863393792 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --unset-all core.bare 1>| 2>|

PID: 30820 END: 1752630729899058176 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --unset-all core.bare 1>| 2>|

PID: 30820 START: 1752630729899584256 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--replace-all', 'remote.origin.url', 'ssh://yanxuqiang@********:29418/sifli'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --replace-all remote.origin.url ssh://yanxuqiang@********:29418/sifli 1>| 2>|

PID: 30820 END: 1752630729934376704 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--replace-all', 'remote.origin.url', 'ssh://yanxuqiang@********:29418/sifli'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --replace-all remote.origin.url ssh://yanxuqiang@********:29418/sifli 1>| 2>|

PID: 30820 START: 1752630729934891008 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--replace-all', 'remote.origin.review', 'http://********:8081'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --replace-all remote.origin.review http://********:8081 1>| 2>|

PID: 30820 END: 1752630729970257408 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--replace-all', 'remote.origin.review', 'http://********:8081'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --replace-all remote.origin.review http://********:8081 1>| 2>|

PID: 30820 START: 1752630729970303744 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--replace-all', 'remote.origin.projectname', 'sifli'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --replace-all remote.origin.projectname sifli 1>| 2>|

PID: 30820 END: 1752630730005269504 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--replace-all', 'remote.origin.projectname', 'sifli'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --replace-all remote.origin.projectname sifli 1>| 2>|

PID: 30820 START: 1752630730005787136 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 30820 END: 1752630730040453888 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 30820 START: 1752630730040982528 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'fetch', '--quiet', 'origin', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/*:refs/remotes/origin/*', '+refs/heads/develop:refs/remotes/origin/develop', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_OBJECT_DIRECTORY=E:/Snowa/.repo/worktrees/sifli.git/objects
: git fetch --quiet origin --prune --recurse-submodules=no --tags +refs/heads/*:refs/remotes/origin/* +refs/heads/develop:refs/remotes/origin/develop +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 30820 END: 1752630785508625152 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'fetch', '--quiet', 'origin', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/*:refs/remotes/origin/*', '+refs/heads/develop:refs/remotes/origin/develop', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_OBJECT_DIRECTORY=E:/Snowa/.repo/worktrees/sifli.git/objects
: git fetch --quiet origin --prune --recurse-submodules=no --tags +refs/heads/*:refs/remotes/origin/* +refs/heads/develop:refs/remotes/origin/develop +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 30820 START: 1752630785509625088 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 30820 END: 1752630785587369728 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 30820 START: 1752630785588366336 :: load refs E:/Snowa/.repo/worktrees/sifli.git

PID: 30820 END: 1752630785589363456 :: load refs E:/Snowa/.repo/worktrees/sifli.git

PID: 30820 START: 1752630785589363456 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'symbolic-ref', '-m', 'redirecting to worktree scope', 'refs/remotes/m/develop', 'refs/worktree/m/develop'] with debug: : git symbolic-ref -m redirecting to worktree scope refs/remotes/m/develop refs/worktree/m/develop 1>| 2>|

PID: 30820 END: 1752630785629227776 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'symbolic-ref', '-m', 'redirecting to worktree scope', 'refs/remotes/m/develop', 'refs/worktree/m/develop'] with debug: : git symbolic-ref -m redirecting to worktree scope refs/remotes/m/develop refs/worktree/m/develop 1>| 2>|

PID: 30820 START: 1752630785630227200 :: scan refs E:/Snowa/.repo/worktrees/sifli.git

PID: 30820 END: 1752630785630227200 :: scan refs E:/Snowa/.repo/worktrees/sifli.git

PID: 30820 START: 1752630785631222528 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'init'] with debug: 
: export GIT_DIR=E:/Snowa/.repo/worktrees/tools.git
: git init 1>| 2>|

PID: 30820 END: 1752630785682050048 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'init'] with debug: 
: export GIT_DIR=E:/Snowa/.repo/worktrees/tools.git
: git init 1>| 2>|

PID: 30820 START: 1752630785687034112 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --null --list 1>| 2>|

PID: 30820 END: 1752630785725902848 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --null --list 1>| 2>|

PID: 30820 START: 1752630785727896064 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 30820 END: 1752630785764776448 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 30820 START: 1752630785764776448 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--replace-all', 'extensions.worktreeConfig', 'true'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --replace-all extensions.worktreeConfig true 1>| 2>|

PID: 30820 END: 1752630785812613120 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--replace-all', 'extensions.worktreeConfig', 'true'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --replace-all extensions.worktreeConfig true 1>| 2>|

PID: 30820 START: 1752630785813609984 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 30820 END: 1752630785857466880 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 30820 START: 1752630785857466880 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 30820 END: 1752630785894339328 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 30820 START: 1752630785895339776 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --unset-all core.bare 1>| 2>|

PID: 30820 END: 1752630785931215616 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --unset-all core.bare 1>| 2>|

PID: 30820 START: 1752630785932215808 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--replace-all', 'remote.origin.url', 'ssh://yanxuqiang@********:29418/tools'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --replace-all remote.origin.url ssh://yanxuqiang@********:29418/tools 1>| 2>|

PID: 30820 END: 1752630785969089536 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--replace-all', 'remote.origin.url', 'ssh://yanxuqiang@********:29418/tools'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --replace-all remote.origin.url ssh://yanxuqiang@********:29418/tools 1>| 2>|

PID: 30820 START: 1752630785969089536 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--replace-all', 'remote.origin.review', 'http://********:8081'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --replace-all remote.origin.review http://********:8081 1>| 2>|

PID: 30820 END: 1752630786010949376 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--replace-all', 'remote.origin.review', 'http://********:8081'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --replace-all remote.origin.review http://********:8081 1>| 2>|

PID: 30820 START: 1752630786010949376 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--replace-all', 'remote.origin.projectname', 'tools'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --replace-all remote.origin.projectname tools 1>| 2>|

PID: 30820 END: 1752630786050816000 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--replace-all', 'remote.origin.projectname', 'tools'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --replace-all remote.origin.projectname tools 1>| 2>|

PID: 30820 START: 1752630786051812608 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 30820 END: 1752630786087696128 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 30820 START: 1752630786088692736 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'fetch', '--quiet', 'origin', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/*:refs/remotes/origin/*', '+refs/heads/develop:refs/remotes/origin/develop', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_OBJECT_DIRECTORY=E:/Snowa/.repo/worktrees/tools.git/objects
: git fetch --quiet origin --prune --recurse-submodules=no --tags +refs/heads/*:refs/remotes/origin/* +refs/heads/develop:refs/remotes/origin/develop +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 30820 END: 1752630817523533824 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'fetch', '--quiet', 'origin', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/*:refs/remotes/origin/*', '+refs/heads/develop:refs/remotes/origin/develop', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_OBJECT_DIRECTORY=E:/Snowa/.repo/worktrees/tools.git/objects
: git fetch --quiet origin --prune --recurse-submodules=no --tags +refs/heads/*:refs/remotes/origin/* +refs/heads/develop:refs/remotes/origin/develop +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 30820 START: 1752630817524530432 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 30820 END: 1752630817597287424 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 30820 START: 1752630817598283776 :: load refs E:/Snowa/.repo/worktrees/tools.git

PID: 30820 END: 1752630817599283456 :: load refs E:/Snowa/.repo/worktrees/tools.git

PID: 30820 START: 1752630817599283456 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'symbolic-ref', '-m', 'redirecting to worktree scope', 'refs/remotes/m/develop', 'refs/worktree/m/develop'] with debug: : git symbolic-ref -m redirecting to worktree scope refs/remotes/m/develop refs/worktree/m/develop 1>| 2>|

PID: 30820 END: 1752630817635160320 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'symbolic-ref', '-m', 'redirecting to worktree scope', 'refs/remotes/m/develop', 'refs/worktree/m/develop'] with debug: : git symbolic-ref -m redirecting to worktree scope refs/remotes/m/develop refs/worktree/m/develop 1>| 2>|

PID: 30820 START: 1752630817636160768 :: scan refs E:/Snowa/.repo/worktrees/tools.git

PID: 30820 END: 1752630817636160768 :: scan refs E:/Snowa/.repo/worktrees/tools.git

PID: 30820 START: 1752630817637156864 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'init'] with debug: 
: export GIT_DIR=E:/Snowa/.repo/worktrees/vendor.git
: git init 1>| 2>|

PID: 30820 END: 1752630817683003392 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'init'] with debug: 
: export GIT_DIR=E:/Snowa/.repo/worktrees/vendor.git
: git init 1>| 2>|

PID: 30820 START: 1752630817686991104 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --null --list 1>| 2>|

PID: 30820 END: 1752630817721873920 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --null --list 1>| 2>|

PID: 30820 START: 1752630817722870016 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 30820 END: 1752630817757754624 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 30820 START: 1752630817757754624 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--replace-all', 'extensions.worktreeConfig', 'true'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --replace-all extensions.worktreeConfig true 1>| 2>|

PID: 30820 END: 1752630817792633600 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--replace-all', 'extensions.worktreeConfig', 'true'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --replace-all extensions.worktreeConfig true 1>| 2>|

PID: 30820 START: 1752630817793630208 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 30820 END: 1752630817828518400 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 30820 START: 1752630817829510400 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 30820 END: 1752630817866386944 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 30820 START: 1752630817867387136 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --unset-all core.bare 1>| 2>|

PID: 30820 END: 1752630817903264512 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --unset-all core.bare 1>| 2>|

PID: 30820 START: 1752630817904260096 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--replace-all', 'remote.origin.url', 'ssh://yanxuqiang@********:29418/vendor'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --replace-all remote.origin.url ssh://yanxuqiang@********:29418/vendor 1>| 2>|

PID: 30820 END: 1752630817939147008 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--replace-all', 'remote.origin.url', 'ssh://yanxuqiang@********:29418/vendor'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --replace-all remote.origin.url ssh://yanxuqiang@********:29418/vendor 1>| 2>|

PID: 30820 START: 1752630817939147008 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--replace-all', 'remote.origin.review', 'http://********:8081'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --replace-all remote.origin.review http://********:8081 1>| 2>|

PID: 30820 END: 1752630817974026752 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--replace-all', 'remote.origin.review', 'http://********:8081'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --replace-all remote.origin.review http://********:8081 1>| 2>|

PID: 30820 START: 1752630817975026432 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--replace-all', 'remote.origin.projectname', 'vendor'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --replace-all remote.origin.projectname vendor 1>| 2>|

PID: 30820 END: 1752630818009906688 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--replace-all', 'remote.origin.projectname', 'vendor'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --replace-all remote.origin.projectname vendor 1>| 2>|

PID: 30820 START: 1752630818009906688 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 30820 END: 1752630818045786880 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 30820 START: 1752630818046787072 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'fetch', '--quiet', 'origin', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/*:refs/remotes/origin/*', '+refs/heads/develop:refs/remotes/origin/develop', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_OBJECT_DIRECTORY=E:/Snowa/.repo/worktrees/vendor.git/objects
: git fetch --quiet origin --prune --recurse-submodules=no --tags +refs/heads/*:refs/remotes/origin/* +refs/heads/develop:refs/remotes/origin/develop +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 30820 END: 1752630860544912384 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'fetch', '--quiet', 'origin', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/*:refs/remotes/origin/*', '+refs/heads/develop:refs/remotes/origin/develop', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_OBJECT_DIRECTORY=E:/Snowa/.repo/worktrees/vendor.git/objects
: git fetch --quiet origin --prune --recurse-submodules=no --tags +refs/heads/*:refs/remotes/origin/* +refs/heads/develop:refs/remotes/origin/develop +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 30820 START: 1752630860545908992 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 30820 END: 1752630860616675584 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 30820 START: 1752630860617669120 :: load refs E:/Snowa/.repo/worktrees/vendor.git

PID: 30820 END: 1752630860618669312 :: load refs E:/Snowa/.repo/worktrees/vendor.git

PID: 30820 START: 1752630860618669312 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'symbolic-ref', '-m', 'redirecting to worktree scope', 'refs/remotes/m/develop', 'refs/worktree/m/develop'] with debug: : git symbolic-ref -m redirecting to worktree scope refs/remotes/m/develop refs/worktree/m/develop 1>| 2>|

PID: 30820 END: 1752630860654549504 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'symbolic-ref', '-m', 'redirecting to worktree scope', 'refs/remotes/m/develop', 'refs/worktree/m/develop'] with debug: : git symbolic-ref -m redirecting to worktree scope refs/remotes/m/develop refs/worktree/m/develop 1>| 2>|

PID: 30820 START: 1752630860654549504 :: scan refs E:/Snowa/.repo/worktrees/vendor.git

PID: 30820 END: 1752630860655546112 :: scan refs E:/Snowa/.repo/worktrees/vendor.git

PID: 45756 START: 1752630862880601088 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'worktree', 'prune'] with debug: : export GIT_DIR=E:/Snowa/.repo/worktrees/qw_algo/navigation.git
: git worktree prune 1>| 2>|

PID: 23616 START: 1752630862881597440 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'worktree', 'prune'] with debug: : export GIT_DIR=E:/Snowa/.repo/worktrees/app.git
: git worktree prune 1>| 2>|

PID: 46448 START: 1752630862894554112 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'worktree', 'prune'] with debug: : export GIT_DIR=E:/Snowa/.repo/worktrees/qw_platform.git
: git worktree prune 1>| 2>|

PID: 23460 START: 1752630862900534016 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'worktree', 'prune'] with debug: : export GIT_DIR=E:/Snowa/.repo/worktrees/tools.git
: git worktree prune 1>| 2>|

PID: 40164 START: 1752630862916480768 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'worktree', 'prune'] with debug: : export GIT_DIR=E:/Snowa/.repo/worktrees/vendor.git
: git worktree prune 1>| 2>|

PID: 46448 END: 1752630862927444480 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'worktree', 'prune'] with debug: : export GIT_DIR=E:/Snowa/.repo/worktrees/qw_platform.git
: git worktree prune 1>| 2>|

ne 1>| 2>|

PID: 23616 START: 1752630862928441600 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/app.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/app.git\config --includes --null --list 1>| 2>|

PID: 45756 START: 1752630862928441600 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_algo/navigation.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config --includes --null --list 1>| 2>|

PID: 23460 END: 1752630862947377408 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'worktree', 'prune'] with debug: : export GIT_DIR=E:/Snowa/.repo/worktrees/tools.git
: git worktree prune 1>| 2>|

PID: 23460 START: 1752630862948374272 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --null --list 1>| 2>|

PID: 40164 END: 1752630862963323904 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'worktree', 'prune'] with debug: : export GIT_DIR=E:/Snowa/.repo/worktrees/vendor.git
: git worktree prune 1>| 2>|

 --file E:/Snowa/.repo/worktrees/app.git\config --includes --null --list 1>| 2>|

PID: 40164 START: 1752630862963831552 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --null --list 1>| 2>|

PID: 23616 START: 1752630862964823040 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/develop^0'] with debug: : git rev-parse --verify refs/remotes/origin/develop^0 1>| 2>|

PID: 46448 END: 1752630862978784512 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/qw_platform.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/qw_platform.git\config --includes --null --list 1>| 2>|

ll --list 1>| 2>|

PID: 23460 END: 1752630862986757888 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/tools.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/tools.git\config --includes --null --list 1>| 2>|

PID: 46448 START: 1752630862987754752 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/develop^0'] with debug: : git rev-parse --verify refs/remotes/origin/develop^0 1>| 2>|

PID: 45756 START: 1752630862987754752 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/cm_develop^0'] with debug: : git rev-parse --verify refs/remotes/origin/cm_develop^0 1>| 2>|

PID: 23460 START: 1752630862988751616 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/develop^0'] with debug: : git rev-parse --verify refs/remotes/origin/develop^0 1>| 2>|

PID: 20740 END: 1752630863002704640 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/sifli.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/sifli.git\config --includes --null --list 1>| 2>|

PID: 20740 START: 1752630863004697344 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/develop^0'] with debug: : git rev-parse --verify refs/remotes/origin/develop^0 1>| 2>|

PID: 23616 END: 1752630863006691328 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/develop^0'] with debug: : git rev-parse --verify refs/remotes/origin/develop^0 1>| 2>|

PID: 40164 END: 1752630863006691328 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/worktrees/vendor.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/worktrees/vendor.git\config --includes --null --list 1>| 2>|

PID: 23616 START: 1752630863006691328 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'worktree', 'add', '-ff', '--checkout', '--detach', '--lock', 'E:/Snowa/app', 'd82a4057878482e9d29b8d1d2965925c6d69326e'] with debug: : git worktree add -ff --checkout --detach --lock E:/Snowa/app d82a4057878482e9d29b8d1d2965925c6d69326e 1>| 2>|

PID: 40164 START: 1752630863008684544 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/develop^0'] with debug: : git rev-parse --verify refs/remotes/origin/develop^0 1>| 2>|

PID: 45756 END: 1752630863028617728 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/cm_develop^0'] with debug: : git rev-parse --verify refs/remotes/origin/cm_develop^0 1>| 2>|

PID: 45756 START: 1752630863029614336 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'worktree', 'add', '-ff', '--checkout', '--detach', '--lock', 'E:/Snowa/qw_algo/navigation', 'd930065bbee6d6242f8fb714e6c15b2de1ebb4a6'] with debug: : git worktree add -ff --checkout --detach --lock E:/Snowa/qw_algo/navigation d930065bbee6d6242f8fb714e6c15b2de1ebb4a6 1>| 2>|

PID: 46448 END: 1752630863031607808 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/develop^0'] with debug: : git rev-parse --verify refs/remotes/origin/develop^0 1>| 2>|

PID: 46448 START: 1752630863032604416 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'worktree', 'add', '-ff', '--checkout', '--detach', '--lock', 'E:/Snowa/qw_platform', '6163754d711afe3586024cbf14a5994692d70f29'] with debug: : git worktree add -ff --checkout --detach --lock E:/Snowa/qw_platform 6163754d711afe3586024cbf14a5994692d70f29 1>| 2>|

PID: 20740 END: 1752630863044564480 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/develop^0'] with debug: : git rev-parse --verify refs/remotes/origin/develop^0 1>| 2>|

PID: 23460 END: 1752630863044564480 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/develop^0'] with debug: : git rev-parse --verify refs/remotes/origin/develop^0 1>| 2>|

PID: 20740 START: 1752630863045561088 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'worktree', 'add', '-ff', '--checkout', '--detach', '--lock', 'E:/Snowa/sifli', 'b0a5bc420a7079f1f3dbd71588d4b8a70ee5d8e1'] with debug: : git worktree add -ff --checkout --detach --lock E:/Snowa/sifli b0a5bc420a7079f1f3dbd71588d4b8a70ee5d8e1 1>| 2>|

PID: 23460 START: 1752630863045561088 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'worktree', 'add', '-ff', '--checkout', '--detach', '--lock', 'E:/Snowa/tools', '845db730e5db9901880a9037d88c5d4488f48b5b'] with debug: : git worktree add -ff --checkout --detach --lock E:/Snowa/tools 845db730e5db9901880a9037d88c5d4488f48b5b 1>| 2>|

PID: 40164 END: 1752630863059514368 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/develop^0'] with debug: : git rev-parse --verify refs/remotes/origin/develop^0 1>| 2>|

PID: 40164 START: 1752630863060510976 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'worktree', 'add', '-ff', '--checkout', '--detach', '--lock', 'E:/Snowa/vendor', '49aa2937fd4577cf36cf466b5c8768a5fc425c74'] with debug: : git worktree add -ff --checkout --detach --lock E:/Snowa/vendor 49aa2937fd4577cf36cf466b5c8768a5fc425c74 1>| 2>|

PID: 45756 END: 1752630863170144512 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'worktree', 'add', '-ff', '--checkout', '--detach', '--lock', 'E:/Snowa/qw_algo/navigation', 'd930065bbee6d6242f8fb714e6c15b2de1ebb4a6'] with debug: : git worktree add -ff --checkout --detach --lock E:/Snowa/qw_algo/navigation d930065bbee6d6242f8fb714e6c15b2de1ebb4a6 1>| 2>|

PID: 45756 START: 1752630863172137472 :: load refs E:/Snowa/.repo/worktrees/qw_algo/navigation.git

PID: 45756 END: 1752630863173134592 :: load refs E:/Snowa/.repo/worktrees/qw_algo/navigation.git

PID: 45756 START: 1752630863174130944 :: scan refs E:/Snowa/.repo/worktrees/qw_algo/navigation.git

PID: 45756 END: 1752630863174130944 :: scan refs E:/Snowa/.repo/worktrees/qw_algo/navigation.git

PID: 45756 START: 1752630863175128320 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'update-ref', '-m', 'manifest set to cm_develop', '--no-deref', 'HEAD', 'refs/remotes/origin/cm_develop'] with debug: : git update-ref -m manifest set to cm_develop --no-deref HEAD refs/remotes/origin/cm_develop 1>| 2>|

PID: 45756 END: 1752630863227951104 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'update-ref', '-m', 'manifest set to cm_develop', '--no-deref', 'HEAD', 'refs/remotes/origin/cm_develop'] with debug: : git update-ref -m manifest set to cm_develop --no-deref HEAD refs/remotes/origin/cm_develop 1>| 2>|

PID: 45756 START: 1752630863227951104 :: scan refs E:/Snowa/.repo/worktrees/qw_algo/navigation.git

PID: 45756 END: 1752630863228947456 :: scan refs E:/Snowa/.repo/worktrees/qw_algo/navigation.git

PID: 45756 START: 1752630863228947456 :: load refs E:/Snowa/.repo/worktrees/qw_algo/navigation.git

PID: 45756 END: 1752630863230940928 :: load refs E:/Snowa/.repo/worktrees/qw_algo/navigation.git

PID: 45756 START: 1752630863231937280 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'symbolic-ref', '-m', 'manifest set to cm_develop', 'refs/worktree/m/develop', 'refs/remotes/origin/cm_develop'] with debug: 
: cd E:/Snowa/qw_algo/navigation
: git symbolic-ref -m manifest set to cm_develop refs/worktree/m/develop refs/remotes/origin/cm_develop 1>| 2>|

PID: 45756 END: 1752630863285758208 :git command E:/Snowa/.repo/worktrees/qw_algo/navigation.git ['git', 'symbolic-ref', '-m', 'manifest set to cm_develop', 'refs/worktree/m/develop', 'refs/remotes/origin/cm_develop'] with debug: 
: cd E:/Snowa/qw_algo/navigation
: git symbolic-ref -m manifest set to cm_develop refs/worktree/m/develop refs/remotes/origin/cm_develop 1>| 2>|

PID: 45756 START: 1752630863285758208 :: scan refs E:/Snowa/.repo/worktrees/qw_algo/navigation.git

PID: 45756 END: 1752630863286754560 :: scan refs E:/Snowa/.repo/worktrees/qw_algo/navigation.git

PID: 30820 START: 1752630863288747776 :: load refs E:/Snowa/.repo/worktrees/qw_algo/navigation.git

PID: 30820 END: 1752630863290740992 :: load refs E:/Snowa/.repo/worktrees/qw_algo/navigation.git

PID: 30820 START: 1752630863290740992 :: parsing E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config

PID: 30820 END: 1752630863291738624 :: parsing E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config

PID: 23460 END: 1752630864998243584 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'worktree', 'add', '-ff', '--checkout', '--detach', '--lock', 'E:/Snowa/tools', '845db730e5db9901880a9037d88c5d4488f48b5b'] with debug: : git worktree add -ff --checkout --detach --lock E:/Snowa/tools 845db730e5db9901880a9037d88c5d4488f48b5b 1>| 2>|

PID: 23460 START: 1752630864999822848 :: load refs E:/Snowa/.repo/worktrees/tools.git

PID: 23460 END: 1752630865002475264 :: load refs E:/Snowa/.repo/worktrees/tools.git

PID: 23460 START: 1752630865003000320 :: scan refs E:/Snowa/.repo/worktrees/tools.git

PID: 23460 END: 1752630865003524864 :: scan refs E:/Snowa/.repo/worktrees/tools.git

PID: 23460 START: 1752630865004053248 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'update-ref', '-m', 'manifest set to develop', '--no-deref', 'HEAD', 'refs/remotes/origin/develop'] with debug: : git update-ref -m manifest set to develop --no-deref HEAD refs/remotes/origin/develop 1>| 2>|

PID: 23460 END: 1752630865050789120 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'update-ref', '-m', 'manifest set to develop', '--no-deref', 'HEAD', 'refs/remotes/origin/develop'] with debug: : git update-ref -m manifest set to develop --no-deref HEAD refs/remotes/origin/develop 1>| 2>|

PID: 23460 START: 1752630865051311872 :: scan refs E:/Snowa/.repo/worktrees/tools.git

PID: 23460 END: 1752630865051835648 :: scan refs E:/Snowa/.repo/worktrees/tools.git

PID: 23460 START: 1752630865052358912 :: load refs E:/Snowa/.repo/worktrees/tools.git

PID: 23460 END: 1752630865054466816 :: load refs E:/Snowa/.repo/worktrees/tools.git

PID: 23460 START: 1752630865054466816 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'symbolic-ref', '-m', 'manifest set to develop', 'refs/worktree/m/develop', 'refs/remotes/origin/develop'] with debug: 
: cd E:/Snowa/tools
: git symbolic-ref -m manifest set to develop refs/worktree/m/develop refs/remotes/origin/develop 1>| 2>|

PID: 23460 END: 1752630865102951424 :git command E:/Snowa/.repo/worktrees/tools.git ['git', 'symbolic-ref', '-m', 'manifest set to develop', 'refs/worktree/m/develop', 'refs/remotes/origin/develop'] with debug: 
: cd E:/Snowa/tools
: git symbolic-ref -m manifest set to develop refs/worktree/m/develop refs/remotes/origin/develop 1>| 2>|

PID: 23460 START: 1752630865103473408 :: scan refs E:/Snowa/.repo/worktrees/tools.git

PID: 23460 END: 1752630865103997440 :: scan refs E:/Snowa/.repo/worktrees/tools.git

PID: 30820 START: 1752630865105050112 :: load refs E:/Snowa/.repo/worktrees/tools.git

PID: 30820 END: 1752630865107670784 :: load refs E:/Snowa/.repo/worktrees/tools.git

PID: 30820 START: 1752630865107670784 :: parsing E:/Snowa/.repo/worktrees/tools.git\config

PID: 30820 END: 1752630865108200960 :: parsing E:/Snowa/.repo/worktrees/tools.git\config

PID: 46448 END: 1752630865931218688 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'worktree', 'add', '-ff', '--checkout', '--detach', '--lock', 'E:/Snowa/qw_platform', '6163754d711afe3586024cbf14a5994692d70f29'] with debug: : git worktree add -ff --checkout --detach --lock E:/Snowa/qw_platform 6163754d711afe3586024cbf14a5994692d70f29 1>| 2>|

PID: 46448 START: 1752630865933212416 :: load refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 46448 END: 1752630865934212864 :: load refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 46448 START: 1752630865935208960 :: scan refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 46448 END: 1752630865935208960 :: scan refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 46448 START: 1752630865935208960 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'update-ref', '-m', 'manifest set to develop', '--no-deref', 'HEAD', 'refs/remotes/origin/develop'] with debug: : git update-ref -m manifest set to develop --no-deref HEAD refs/remotes/origin/develop 1>| 2>|

PID: 46448 END: 1752630865978062080 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'update-ref', '-m', 'manifest set to develop', '--no-deref', 'HEAD', 'refs/remotes/origin/develop'] with debug: : git update-ref -m manifest set to develop --no-deref HEAD refs/remotes/origin/develop 1>| 2>|

PID: 46448 START: 1752630865978062080 :: scan refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 46448 END: 1752630865979058688 :: scan refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 46448 START: 1752630865979058688 :: load refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 46448 END: 1752630865981051904 :: load refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 46448 START: 1752630865981051904 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'symbolic-ref', '-m', 'manifest set to develop', 'refs/worktree/m/develop', 'refs/remotes/origin/develop'] with debug: 
: cd E:/Snowa/qw_platform
: git symbolic-ref -m manifest set to develop refs/worktree/m/develop refs/remotes/origin/develop 1>| 2>|

PID: 46448 END: 1752630866030884864 :git command E:/Snowa/.repo/worktrees/qw_platform.git ['git', 'symbolic-ref', '-m', 'manifest set to develop', 'refs/worktree/m/develop', 'refs/remotes/origin/develop'] with debug: 
: cd E:/Snowa/qw_platform
: git symbolic-ref -m manifest set to develop refs/worktree/m/develop refs/remotes/origin/develop 1>| 2>|

PID: 46448 START: 1752630866031881984 :: scan refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 46448 END: 1752630866031881984 :: scan refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 30820 START: 1752630866032881664 :: load refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 30820 END: 1752630866034871552 :: load refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 30820 START: 1752630866034871552 :: parsing E:/Snowa/.repo/worktrees/qw_platform.git\config

PID: 30820 END: 1752630866035871488 :: parsing E:/Snowa/.repo/worktrees/qw_platform.git\config

PID: 40164 END: 1752630867865867008 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'worktree', 'add', '-ff', '--checkout', '--detach', '--lock', 'E:/Snowa/vendor', '49aa2937fd4577cf36cf466b5c8768a5fc425c74'] with debug: : git worktree add -ff --checkout --detach --lock E:/Snowa/vendor 49aa2937fd4577cf36cf466b5c8768a5fc425c74 1>| 2>|

PID: 40164 START: 1752630867866863104 :: load refs E:/Snowa/.repo/worktrees/vendor.git

PID: 40164 END: 1752630867868856576 :: load refs E:/Snowa/.repo/worktrees/vendor.git

PID: 40164 START: 1752630867868856576 :: scan refs E:/Snowa/.repo/worktrees/vendor.git

PID: 40164 END: 1752630867868856576 :: scan refs E:/Snowa/.repo/worktrees/vendor.git

PID: 40164 START: 1752630867869853184 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'update-ref', '-m', 'manifest set to develop', '--no-deref', 'HEAD', 'refs/remotes/origin/develop'] with debug: : git update-ref -m manifest set to develop --no-deref HEAD refs/remotes/origin/develop 1>| 2>|

PID: 40164 END: 1752630867950583296 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'update-ref', '-m', 'manifest set to develop', '--no-deref', 'HEAD', 'refs/remotes/origin/develop'] with debug: : git update-ref -m manifest set to develop --no-deref HEAD refs/remotes/origin/develop 1>| 2>|

PID: 40164 START: 1752630867951579904 :: scan refs E:/Snowa/.repo/worktrees/vendor.git

PID: 40164 END: 1752630867951579904 :: scan refs E:/Snowa/.repo/worktrees/vendor.git

PID: 40164 START: 1752630867951579904 :: load refs E:/Snowa/.repo/worktrees/vendor.git

PID: 40164 END: 1752630867953573120 :: load refs E:/Snowa/.repo/worktrees/vendor.git

PID: 40164 START: 1752630867954569728 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'symbolic-ref', '-m', 'manifest set to develop', 'refs/worktree/m/develop', 'refs/remotes/origin/develop'] with debug: 
: cd E:/Snowa/vendor
: git symbolic-ref -m manifest set to develop refs/worktree/m/develop refs/remotes/origin/develop 1>| 2>|

PID: 40164 END: 1752630867996430336 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'symbolic-ref', '-m', 'manifest set to develop', 'refs/worktree/m/develop', 'refs/remotes/origin/develop'] with debug: 
: cd E:/Snowa/vendor
: git symbolic-ref -m manifest set to develop refs/worktree/m/develop refs/remotes/origin/develop 1>| 2>|

PID: 40164 START: 1752630867996430336 :: scan refs E:/Snowa/.repo/worktrees/vendor.git

PID: 40164 END: 1752630867997427200 :: scan refs E:/Snowa/.repo/worktrees/vendor.git

PID: 30820 START: 1752630867998423808 :: load refs E:/Snowa/.repo/worktrees/vendor.git

PID: 30820 END: 1752630868000417024 :: load refs E:/Snowa/.repo/worktrees/vendor.git

PID: 30820 START: 1752630868001413120 :: parsing E:/Snowa/.repo/worktrees/vendor.git\config

PID: 30820 END: 1752630868001413120 :: parsing E:/Snowa/.repo/worktrees/vendor.git\config

PID: 23616 END: 1752630880837146624 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'worktree', 'add', '-ff', '--checkout', '--detach', '--lock', 'E:/Snowa/app', 'd82a4057878482e9d29b8d1d2965925c6d69326e'] with debug: : git worktree add -ff --checkout --detach --lock E:/Snowa/app d82a4057878482e9d29b8d1d2965925c6d69326e 1>| 2>|

PID: 23616 START: 1752630880838139904 :: load refs E:/Snowa/.repo/worktrees/app.git

PID: 23616 END: 1752630880839140352 :: load refs E:/Snowa/.repo/worktrees/app.git

PID: 23616 START: 1752630880840136448 :: scan refs E:/Snowa/.repo/worktrees/app.git

PID: 23616 END: 1752630880840136448 :: scan refs E:/Snowa/.repo/worktrees/app.git

PID: 23616 START: 1752630880840136448 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'update-ref', '-m', 'manifest set to develop', '--no-deref', 'HEAD', 'refs/remotes/origin/develop'] with debug: : git update-ref -m manifest set to develop --no-deref HEAD refs/remotes/origin/develop 1>| 2>|

PID: 23616 END: 1752630880885982976 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'update-ref', '-m', 'manifest set to develop', '--no-deref', 'HEAD', 'refs/remotes/origin/develop'] with debug: : git update-ref -m manifest set to develop --no-deref HEAD refs/remotes/origin/develop 1>| 2>|

PID: 23616 START: 1752630880885982976 :: scan refs E:/Snowa/.repo/worktrees/app.git

PID: 23616 END: 1752630880886980352 :: scan refs E:/Snowa/.repo/worktrees/app.git

PID: 23616 START: 1752630880886980352 :: load refs E:/Snowa/.repo/worktrees/app.git

PID: 23616 END: 1752630880887976704 :: load refs E:/Snowa/.repo/worktrees/app.git

PID: 23616 START: 1752630880888972800 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'symbolic-ref', '-m', 'manifest set to develop', 'refs/worktree/m/develop', 'refs/remotes/origin/develop'] with debug: 
: cd E:/Snowa/app
: git symbolic-ref -m manifest set to develop refs/worktree/m/develop refs/remotes/origin/develop 1>| 2>|

PID: 23616 END: 1752630880941793280 :git command E:/Snowa/.repo/worktrees/app.git ['git', 'symbolic-ref', '-m', 'manifest set to develop', 'refs/worktree/m/develop', 'refs/remotes/origin/develop'] with debug: 
: cd E:/Snowa/app
: git symbolic-ref -m manifest set to develop refs/worktree/m/develop refs/remotes/origin/develop 1>| 2>|

PID: 23616 START: 1752630880942790144 :: scan refs E:/Snowa/.repo/worktrees/app.git

PID: 23616 END: 1752630880942790144 :: scan refs E:/Snowa/.repo/worktrees/app.git

PID: 30820 START: 1752630880943790592 :: load refs E:/Snowa/.repo/worktrees/app.git

PID: 30820 END: 1752630880945783808 :: load refs E:/Snowa/.repo/worktrees/app.git

PID: 30820 START: 1752630880945783808 :: parsing E:/Snowa/.repo/worktrees/app.git\config

PID: 30820 END: 1752630880946780160 :: parsing E:/Snowa/.repo/worktrees/app.git\config

PID: 20740 END: 1752630929498215424 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'worktree', 'add', '-ff', '--checkout', '--detach', '--lock', 'E:/Snowa/sifli', 'b0a5bc420a7079f1f3dbd71588d4b8a70ee5d8e1'] with debug: : git worktree add -ff --checkout --detach --lock E:/Snowa/sifli b0a5bc420a7079f1f3dbd71588d4b8a70ee5d8e1 1>| 2>|

PID: 20740 START: 1752630929499851520 :: load refs E:/Snowa/.repo/worktrees/sifli.git

PID: 20740 END: 1752630929501509632 :: load refs E:/Snowa/.repo/worktrees/sifli.git

PID: 20740 START: 1752630929501509632 :: scan refs E:/Snowa/.repo/worktrees/sifli.git

PID: 20740 END: 1752630929502046464 :: scan refs E:/Snowa/.repo/worktrees/sifli.git

PID: 20740 START: 1752630929502046464 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'update-ref', '-m', 'manifest set to develop', '--no-deref', 'HEAD', 'refs/remotes/origin/develop'] with debug: : git update-ref -m manifest set to develop --no-deref HEAD refs/remotes/origin/develop 1>| 2>|

PID: 20740 END: 1752630929551809280 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'update-ref', '-m', 'manifest set to develop', '--no-deref', 'HEAD', 'refs/remotes/origin/develop'] with debug: : git update-ref -m manifest set to develop --no-deref HEAD refs/remotes/origin/develop 1>| 2>|

PID: 20740 START: 1752630929551809280 :: scan refs E:/Snowa/.repo/worktrees/sifli.git

PID: 20740 END: 1752630929552330752 :: scan refs E:/Snowa/.repo/worktrees/sifli.git

PID: 20740 START: 1752630929552900864 :: load refs E:/Snowa/.repo/worktrees/sifli.git

PID: 20740 END: 1752630929554007296 :: load refs E:/Snowa/.repo/worktrees/sifli.git

PID: 20740 START: 1752630929554556672 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'symbolic-ref', '-m', 'manifest set to develop', 'refs/worktree/m/develop', 'refs/remotes/origin/develop'] with debug: 
: cd E:/Snowa/sifli
: git symbolic-ref -m manifest set to develop refs/worktree/m/develop refs/remotes/origin/develop 1>| 2>|

PID: 20740 END: 1752630929601873408 :git command E:/Snowa/.repo/worktrees/sifli.git ['git', 'symbolic-ref', '-m', 'manifest set to develop', 'refs/worktree/m/develop', 'refs/remotes/origin/develop'] with debug: 
: cd E:/Snowa/sifli
: git symbolic-ref -m manifest set to develop refs/worktree/m/develop refs/remotes/origin/develop 1>| 2>|

PID: 20740 START: 1752630929601873408 :: scan refs E:/Snowa/.repo/worktrees/sifli.git

PID: 20740 END: 1752630929602427904 :: scan refs E:/Snowa/.repo/worktrees/sifli.git

PID: 30820 START: 1752630929604098816 :: load refs E:/Snowa/.repo/worktrees/sifli.git

PID: 30820 END: 1752630929605764096 :: load refs E:/Snowa/.repo/worktrees/sifli.git

PID: 30820 START: 1752630929605764096 :: parsing E:/Snowa/.repo/worktrees/sifli.git\config

PID: 30820 END: 1752630929606309120 :: parsing E:/Snowa/.repo/worktrees/sifli.git\config

PID: 30820 START: 1752630929622074368 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-07-16T01:55:29.621981+00:00'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.main.synctime 2025-07-16T01:55:29.621981+00:00 1>| 2>|

PID: 30820 END: 1752630929658676992 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-07-16T01:55:29.621981+00:00'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.main.synctime 2025-07-16T01:55:29.621981+00:00 1>| 2>|

PID: 30820 START: 1752630929659226624 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.main.version', '1'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.main.version 1 1>| 2>|

PID: 30820 END: 1752630929696838656 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.main.version', '1'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.main.version 1 1>| 2>|

PID: 30820 START: 1752630929697358336 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.sys.argv', "['E:\\\\Snowa\\\\.repo\\\\repo/main.py', '--repo-dir=E:\\\\Snowa\\\\.repo', '--wrapper-version=2.48', '--wrapper-path=C:\\\\Users\\\\<USER>\\\\bin\\\\repo', '--', 'sync']"] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.sys.argv ['E:\\Snowa\\.repo\\repo/main.py', '--repo-dir=E:\\Snowa\\.repo', '--wrapper-version=2.48', '--wrapper-path=C:\\Users\\<USER>\\bin\\repo', '--', 'sync'] 1>| 2>|

PID: 30820 END: 1752630929781742080 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.sys.argv', "['E:\\\\Snowa\\\\.repo\\\\repo/main.py', '--repo-dir=E:\\\\Snowa\\\\.repo', '--wrapper-version=2.48', '--wrapper-path=C:\\\\Users\\\\<USER>\\\\bin\\\\repo', '--', 'sync']"] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.sys.argv ['E:\\Snowa\\.repo\\repo/main.py', '--repo-dir=E:\\Snowa\\.repo', '--wrapper-version=2.48', '--wrapper-path=C:\\Users\\<USER>\\bin\\repo', '--', 'sync'] 1>| 2>|

PID: 30820 START: 1752630929782272768 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.jobs', '12'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.jobs 12 1>| 2>|

PID: 30820 END: 1752630929818504960 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.jobs', '12'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.jobs 12 1>| 2>|

PID: 30820 START: 1752630929819065600 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.outermanifest', 'true'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.outermanifest true 1>| 2>|

PID: 30820 END: 1752630929855235584 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.outermanifest', 'true'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.outermanifest true 1>| 2>|

PID: 30820 START: 1752630929855753984 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.jobsnetwork', '1'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.jobsnetwork 1 1>| 2>|

PID: 30820 END: 1752630929891904512 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.jobsnetwork', '1'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.jobsnetwork 1 1>| 2>|

PID: 30820 START: 1752630929892426752 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.jobscheckout', '8'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.jobscheckout 8 1>| 2>|

PID: 30820 END: 1752630929929913344 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.jobscheckout', '8'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.jobscheckout 8 1>| 2>|

PID: 30820 START: 1752630929930468864 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.mpupdate', 'true'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.mpupdate true 1>| 2>|

PID: 30820 END: 1752630929966106368 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.mpupdate', 'true'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.mpupdate true 1>| 2>|

PID: 30820 START: 1752630929966621696 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.clonebundle', 'true'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.clonebundle true 1>| 2>|

PID: 30820 END: 1752630930002498304 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.clonebundle', 'true'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.clonebundle true 1>| 2>|

PID: 30820 START: 1752630930003028224 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.retryfetches', '0'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.retryfetches 0 1>| 2>|

PID: 30820 END: 1752630930039227392 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.retryfetches', '0'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.retryfetches 0 1>| 2>|

PID: 30820 START: 1752630930039270400 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.prune', 'true'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.prune true 1>| 2>|

PID: 30820 END: 1752630930075053824 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.prune', 'true'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.prune true 1>| 2>|

PID: 30820 START: 1752630930075053824 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.repoverify', 'true'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.repoverify true 1>| 2>|

PID: 30820 END: 1752630930111506688 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.repoverify', 'true'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.repoverify true 1>| 2>|

PID: 30820 START: 1752630930112036096 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.quiet', 'false'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.quiet false 1>| 2>|

PID: 30820 END: 1752630930148929792 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.quiet', 'false'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.quiet false 1>| 2>|

PID: 30820 START: 1752630930149926400 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.verbose', 'false'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.verbose false 1>| 2>|

PID: 30820 END: 1752630930185807872 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.options.verbose', 'false'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.options.verbose false 1>| 2>|

PID: 30820 START: 1752630930185807872 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.remote.origin.url', 'ssh://yanxuqiang@********:29418/wr02'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.remote.origin.url ssh://yanxuqiang@********:29418/wr02 1>| 2>|

PID: 30820 END: 1752630930221687040 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.remote.origin.url', 'ssh://yanxuqiang@********:29418/wr02'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.remote.origin.url ssh://yanxuqiang@********:29418/wr02 1>| 2>|

PID: 30820 START: 1752630930222686464 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 30820 END: 1752630930257566976 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 30820 START: 1752630930258563584 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.repo.worktree', 'true'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.repo.worktree true 1>| 2>|

PID: 30820 END: 1752630930294446848 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.repo.worktree', 'true'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.repo.worktree true 1>| 2>|

PID: 30820 START: 1752630930294446848 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.branch.default.remote', 'origin'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.branch.default.remote origin 1>| 2>|

PID: 30820 END: 1752630930332317696 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.branch.default.remote', 'origin'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.branch.default.remote origin 1>| 2>|

PID: 30820 START: 1752630930333313536 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.branch.default.merge', 'refs/heads/develop'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.branch.default.merge refs/heads/develop 1>| 2>|

PID: 30820 END: 1752630930372183296 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.branch.default.merge', 'refs/heads/develop'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.branch.default.merge refs/heads/develop 1>| 2>|

PID: 30820 START: 1752630930372183296 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.repo.existingprojectcount', '0'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.repo.existingprojectcount 0 1>| 2>|

PID: 30820 END: 1752630930412050432 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.repo.existingprojectcount', '0'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.repo.existingprojectcount 0 1>| 2>|

PID: 30820 START: 1752630930412050432 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.repo.newprojectcount', '6'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.repo.newprojectcount 6 1>| 2>|

PID: 30820 END: 1752630930448930304 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--replace-all', 'repo.syncstate.repo.newprojectcount', '6'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --replace-all repo.syncstate.repo.newprojectcount 6 1>| 2>|

PID: 30820 START: 1752630930449926912 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 30820 END: 1752630930484806912 :git command E:/Snowa/.repo/worktrees/vendor.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 30820 END: 1752630930485803264 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync [sid=repo-20250716T015040Z-P00003868/repo-20250716T015040Z-P00007864]

PID: 13388 START: 1752631101949527040 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: start, --all, develop [sid=repo-20250716T015821Z-P00001b74/repo-20250716T015821Z-P0000344c]

PID: 13388 START: 1752631102015309056 :git command None ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --null --list 1>| 2>|

PID: 13388 END: 1752631102051185408 :git command None ['git', 'config', '--file', 'E:/Snowa/.repo/manifests.git\\config', '--includes', '--null', '--list'] with debug: : git config --file E:/Snowa/.repo/manifests.git\config --includes --null --list 1>| 2>|

PID: 13388 START: 1752631102053182208 :: parsing C:\Users\<USER>\Users\qwkj/.gitconfig

PID: 13388 START: 1752631102063149312 :git command None ['git', 'check-ref-format', 'heads/develop'] with debug: : git check-ref-format heads/develop 2>|

PID: 13388 END: 1752631102097053696 :git command None ['git', 'check-ref-format', 'heads/develop'] with debug: : git check-ref-format heads/develop 2>|

PID: 34536 START: 1752631102918568960 :: load refs E:/Snowa/.repo/worktrees/app.git

gation.git

PID: 6640 START: 1752631102918568960 :: load refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 15856 START: 1752631102918568960 :: load refs E:/Snowa/.repo/worktrees/sifli.git

PID: 19432 START: 1752631102919562496 :: load refs E:/Snowa/.repo/worktrees/vendor.git

PID: 19432 END: 1752631102964413184 :: load refs E:/Snowa/.repo/worktrees/vendor.git

PID: 6640 END: 1752631102965409792 :: load refs E:/Snowa/.repo/worktrees/qw_platform.git

PID: 19432 START: 1752631102965409792 :: parsing E:/Snowa/.repo/worktrees/vendor.git\config

PID: 6640 START: 1752631102965409792 :: parsing E:/Snowa/.repo/worktrees/qw_platform.git\config

PID: 19432 END: 1752631102965409792 :: parsing E:/Snowa/.repo/worktrees/vendor.git\config

PID: 6640 END: 1752631102966407168 :: parsing E:/Snowa/.repo/worktrees/qw_platform.git\config

PID: 15856 END: 1752631102966407168 :: load refs E:/Snowa/.repo/worktrees/sifli.git

PID: 15856 START: 1752631102966407168 :: parsing E:/Snowa/.repo/worktrees/sifli.git\config

PID: 7368 END: 1752631102967403008 :: load refs E:/Snowa/.repo/worktrees/qw_algo/navigation.git

PID: 7368 START: 1752631102967403008 :: parsing E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config

PID: 7368 END: 1752631102968399872 :: parsing E:/Snowa/.repo/worktrees/qw_algo/navigation.git\config

PID: 16400 END: 1752631102973383680 :: load refs E:/Snowa/.repo/worktrees/tools.git

PID: 16400 START: 1752631102973383680 :: parsing E:/Snowa/.repo/worktrees/tools.git\config

PID: 16400 END: 1752631102974379264 :: parsing E:/Snowa/.repo/worktrees/tools.git\config

PID: 34536 END: 1752631102978366720 :: load refs E:/Snowa/.repo/worktrees/app.git

PID: 34536 START: 1752631102978366720 :: parsing E:/Snowa/.repo/worktrees/app.git\config

PID: 34536 END: 1752631102979363840 :: parsing E:/Snowa/.repo/worktrees/app.git\config

PID: 19432 START: 1752631103093979648 :git command None ['git', 'update-ref', 'refs/heads/develop', '49aa2937fd4577cf36cf466b5c8768a5fc425c74'] with debug: : cd E:/Snowa/vendor
: git update-ref refs/heads/develop 49aa2937fd4577cf36cf466b5c8768a5fc425c74 1>| 2>|

PID: 6640 START: 1752631103104942080 :git command None ['git', 'update-ref', 'refs/heads/develop', '6163754d711afe3586024cbf14a5994692d70f29'] with debug: : cd E:/Snowa/qw_platform
: git update-ref refs/heads/develop 6163754d711afe3586024cbf14a5994692d70f29 1>| 2>|

PID: 15856 START: 1752631103117899008 :git command None ['git', 'update-ref', 'refs/heads/develop', 'b0a5bc420a7079f1f3dbd71588d4b8a70ee5d8e1'] with debug: : cd E:/Snowa/sifli
: git update-ref refs/heads/develop b0a5bc420a7079f1f3dbd71588d4b8a70ee5d8e1 1>| 2>|

PID: 16400 START: 1752631103117899008 :git command None ['git', 'update-ref', 'refs/heads/develop', '845db730e5db9901880a9037d88c5d4488f48b5b'] with debug: : cd E:/Snowa/tools
: git update-ref refs/heads/develop 845db730e5db9901880a9037d88c5d4488f48b5b 1>| 2>|

PID: 7368 START: 1752631103134842112 :git command None ['git', 'update-ref', 'refs/heads/develop', 'd930065bbee6d6242f8fb714e6c15b2de1ebb4a6'] with debug: : cd E:/Snowa/qw_algo/navigation
: git update-ref refs/heads/develop d930065bbee6d6242f8fb714e6c15b2de1ebb4a6 1>| 2>|

PID: 19432 END: 1752631103135840256 :git command None ['git', 'update-ref', 'refs/heads/develop', '49aa2937fd4577cf36cf466b5c8768a5fc425c74'] with debug: : cd E:/Snowa/vendor
: git update-ref refs/heads/develop 49aa2937fd4577cf36cf466b5c8768a5fc425c74 1>| 2>|

PID: 19432 START: 1752631103136835840 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/develop'] with debug: : git symbolic-ref HEAD refs/heads/develop 1>| 2>|

PID: 6640 END: 1752631103160755712 :git command None ['git', 'update-ref', 'refs/heads/develop', '6163754d711afe3586024cbf14a5994692d70f29'] with debug: : cd E:/Snowa/qw_platform
: git update-ref refs/heads/develop 6163754d711afe3586024cbf14a5994692d70f29 1>| 2>|

PID: 6640 START: 1752631103161752320 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/develop'] with debug: : git symbolic-ref HEAD refs/heads/develop 1>| 2>|

PID: 16400 END: 1752631103167732480 :git command None ['git', 'update-ref', 'refs/heads/develop', '845db730e5db9901880a9037d88c5d4488f48b5b'] with debug: : cd E:/Snowa/tools
: git update-ref refs/heads/develop 845db730e5db9901880a9037d88c5d4488f48b5b 1>| 2>|

PID: 16400 START: 1752631103168729088 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/develop'] with debug: : git symbolic-ref HEAD refs/heads/develop 1>| 2>|

 git update-ref refs/heads/develop b0a5bc420a7079f1f3dbd71588d4b8a70ee5d8e1 1>| 2>|

PID: 15856 START: 1752631103168729088 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/develop'] with debug: : git symbolic-ref HEAD refs/heads/develop 1>| 2>|

PID: 34536 END: 1752631103184677376 :git command None ['git', 'update-ref', 'refs/heads/develop', 'd82a4057878482e9d29b8d1d2965925c6d69326e'] with debug: : cd E:/Snowa/app
: git update-ref refs/heads/develop d82a4057878482e9d29b8d1d2965925c6d69326e 1>| 2>|

a6 1>| 2>|

PID: 34536 START: 1752631103185672960 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/develop'] with debug: : git symbolic-ref HEAD refs/heads/develop 1>| 2>|

PID: 7368 START: 1752631103185672960 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/develop'] with debug: : git symbolic-ref HEAD refs/heads/develop 1>| 2>|

PID: 6640 END: 1752631103208595712 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/develop'] with debug: : git symbolic-ref HEAD refs/heads/develop 1>| 2>|


PID: 16400 END: 1752631103211586304 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/develop'] with debug: : git symbolic-ref HEAD refs/heads/develop 1>| 2>|

PID: 15856 END: 1752631103215572480 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/develop'] with debug: : git symbolic-ref HEAD refs/heads/develop 1>| 2>|

PID: 34536 END: 1752631103227532544 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/develop'] with debug: : git symbolic-ref HEAD refs/heads/develop 1>| 2>|

PID: 7368 END: 1752631103236504320 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/develop'] with debug: : git symbolic-ref HEAD refs/heads/develop 1>| 2>|

PID: 13388 START: 1752631103250455552 :git command None ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 13388 END: 1752631103287332352 :git command None ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 13388 END: 1752631103287332352 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: start, --all, develop [sid=repo-20250716T015821Z-P00001b74/repo-20250716T015821Z-P0000344c]

