#ifndef _DIAL_COMMON_API_H_
#define _DIAL_COMMON_API_H_
#include "stdint.h"
#include "stdbool.h"
#ifdef __cplusplus
extern "C" {
#endif

#define MAX_DIALS           15      // 最大表盘数量
#define MAX_THEME_COLOR     7       // 最大主题颜色数量
#define MAX_EDIT_DATA_NUM   6       // 最大修改数据数量

#ifdef SIMULATOR
#define DIAL_CACHE_PATH "./Dial/Snapshot"
#define DIAL_COMMON_CFG_PATH "./Dial/dialCommonCfg.cfg"
#else
#define DIAL_CACHE_PATH "0:/iGPSPORT/Dial/Snapshot"
#define DIAL_COMMON_CFG_PATH "0:/iGPSPORT/Dial/dialCommonCfg.cfg"
#endif

typedef enum {
    EDIT_TYPE_NOTHING,
    EDIT_TYPE_COLOR = 1,// 修改主题颜色
    EDIT_TYPE_DATA,     // 修改数据
    EDIT_TYPE_ALL       // 修改主题色和数据
}EDIT_TYPE_E;

typedef enum{
    EDIT_MODE_LONG_PRESS = 0,    // 长按进入编辑模式
    EDIT_MODE_CLICK,             // 点击进入编辑模式
} EDIT_MODE_E;

// app操作类型
typedef enum{
    APP_DO_NOTHING = 0,      // 无操作
    APP_ADD_DIAL,            // 新增表盘
    APP_DEL_DIAL,            // 删除表盘
    APP_EDIT_DIAL,           // 编辑表盘(颜色或者数据)
    APP_USE_DIAL,            // 使用表盘
} APP_OPERATE_E;
// 定义操作类型枚举
typedef enum {
    OPERATION_MODIFY,       // 修改
    OPERATION_ADD,          // 添加
    OPERATION_DELETE        // 删除
} OPERATION_TYPE_E;

// 用于返回launcher前的一些判断，例如在表盘主题色页面设置颜色不保存，
// 黑屏后返回launcher，需要恢复表盘
typedef enum{
    BACK_LAUNCHER_STATUS_NONE = 0,      // 未使用
    BACK_LAUNCHER_STATUS_KEY,           // 按键使用
    BACK_LAUNCHER_STATUS_CLICK,         // 点击使用
} BACK_LAUNCHER_STATUS_E;
typedef struct edit_data_positon {
    int16_t x;      //数据x坐标
    int16_t y;      //数据y坐标
    int16_t width;  //数据宽度
    int16_t height; //数据高度
}edit_data_positon_t;

EDIT_MODE_E get_enter_dial_edit_type(void);
void set_enter_dial_edit_type(EDIT_MODE_E enter_type);
void init_dial_edit_data_index();
uint32_t get_dial_edit_data_index();
void set_dial_edit_data_index_increase(void);
void set_dial_edit_data_index_decrease(void);
void init_dial_prv_edit_data_index();
int8_t get_dial_prv_edit_data_index(void);
void set_dial_prv_edit_data_index(int8_t index);
static void set_current_dial_goodsid(uint32_t goodsId);
uint32_t get_current_dial_goodsid(void);
void set_app_operate_type(APP_OPERATE_E type);
APP_OPERATE_E get_app_operate_type(void);
void set_is_from_launcher_flag(bool flag);
bool get_is_from_launcher_flag();

bool get_dial_value_from_cfg(const char* file_path, const char* parse_key, uint32_t* parse_value, const char **str);
static bool set_dial_value_in_cfg(const char* file_path, const char* parse_key, uint32_t new_value, OPERATION_TYPE_E operation);
bool delete_backup_config_file();
bool restore_backup_config_file();
bool backup_cfg_file();
void add_dial(uint32_t goodsId, void* buff);
void delete_dial(uint32_t goodsId);
static void dial_path_clear(uint32_t goodsId);
bool delete_dial_snapshot(uint32_t goodsId);
bool create_dial_snapshot(uint32_t goodsId, void *buf, bool isRecover, uint32_t index);
bool is_dial_snapshot_exist(const char *path);
void send_msg_to_gui_thread(uint32_t goodsId);

uint32_t get_app_operate_goodsId();
char *get_dial_config_path();
char *get_dial_js_path();
void set_dial_config_path(void);


void show_js_dial_theme_color_preview_view(const char* jsPath, uint32_t index);
void restore_all_js_edit_data_preview_view(const char* jsPath, uint32_t editDataIndex);
void refresh_js_dial_pview(const char* jsPath);
void set_js_edit_data_preview(const char* jsPath, uint32_t editDataIndex);
void set_js_aod_mode(const char* jsPath, bool isAodMode)
#ifdef __cplusplus
}
#endif

#endif

