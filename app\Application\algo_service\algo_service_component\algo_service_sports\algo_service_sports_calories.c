﻿/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   algo_service_sports_calories.c
<AUTHOR>   yukai (<EMAIL>)
@Brief    :    骑行卡路里算法组件实现
@Date    :   2024/12/24
*
**************************************************************************/

#include "algo_service_sports_calories.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_task.h"
#include "cfg_header_def.h"
#include "gps_convert.h"
#include "qw_time_service.h"
#include "qw_time_util.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"
#include "qw_fit_api.h"

// 输入数据
typedef struct
{
    uint16_t fat_delta_drv;          //脂肪消耗，直接从GM 获取（cal, 增量）
    uint16_t carb_delta_drv;         //碳水消耗，直接从GM 获取（cal, 增量）
    uint16_t calories_delta_drv;     //算法卡路里，直接从GM 获取（cal, 增量）
    saving_status_e saving_status;   //数据记录的状态
    bool in_rest;                    // 是否在休息
} algo_sports_calories_sub_t;

static algo_sports_calories_sub_t s_algo_in = {0};

// 发布数据
static algo_sports_calories_pub_t s_algo_out = {0};

// 中间数据
static uint32_t session_fat_1000 = 0;
static uint32_t session_carb_1000 = 0;
static uint32_t session_cal_1000 = 0;
static uint32_t lap_cal_1000 = 0;
// static uint16_t s_last_calories = 0;

// 本算法打开标记
static bool s_is_open = false;

//卡路里警示检测
static void auto_alert_calorie_check(sports_alert_t *p_alert, uint32_t total_calories)
{
    uint32_t calorie = total_calories;   //卡路里
    SPORTTYPE sport_type = get_current_sport_mode();
    int cal_alert_used = get_sport_remind_en(sport_type, SPORT_REMIND_CONSUME, MAIN_EN);
    static uint32_t alertCalorie = 0;
    uint32_t cal_threshold = get_sport_remind_value(sport_type, SPORT_REMIND_CONSUME, true);   //警示阈值
    sports_alert_t alert_msg = {0, 0, 0, 0};

    //TEST
    // cal_alert_used = true;
    // cal_threshold = 2;

    if (NULL == p_alert)
    {
        return;
    }

    if (cal_alert_used && cal_threshold > 0)   //当前报警开启
    {
        if (calorie > cal_threshold)
        {
            alert_msg.high_alert_status = true;
        }
        else
        {
            alert_msg.high_alert_status = false;
        }

        //如果阈值变化, 记录的基准值立即向下调整到阈值的整数倍, 如果计算后当前值大于
        if (0 < (alertCalorie % cal_threshold))
        {
            alertCalorie = alertCalorie / cal_threshold * cal_threshold;
        }

        //基准值总是等于阈值的整数倍
        if (calorie >= cal_threshold + alertCalorie)
        {
            alertCalorie += (calorie - alertCalorie) / cal_threshold * cal_threshold;
            alert_msg.high_alert_event = true;
        }
        else
        {
            alert_msg.high_alert_event = false;
        }
    }

    memcpy(p_alert, &alert_msg, sizeof(sports_alert_t));
}

// 算法数据初始化
static void sports_calories_data_init(void)
{
    algo_sports_calories_sub_t *algo_in = &s_algo_in;
    algo_sports_calories_pub_t *algo_out = &s_algo_out;

    memset(algo_in, 0, sizeof(algo_sports_calories_sub_t));
    session_fat_1000 = 0;
    session_carb_1000 = 0;
    session_cal_1000 = 0;
    lap_cal_1000 = 0;
    // s_last_calories = 0;
    algo_out->total_calories = 0;
    algo_out->total_calories_lap = 0;
    algo_out->total_calories_pre_lap = 0;
}

/**
 * @brief 算法处理
 *
 * @param algo_out 输出数据
 * @param algo_in 输入数据
 */
static void algo_sports_calories_deal(algo_sports_calories_pub_t *algo_out, const algo_sports_calories_sub_t *algo_in)
{
    uint16_t cal_delta = 0;                             //两点间的卡路里增量kal*1000

    if (enum_status_saving == algo_in->saving_status)   //记录状态
    {
        if (!algo_in->in_rest)
        {
            session_fat_1000 += algo_in->fat_delta_drv;
            session_carb_1000 += algo_in->carb_delta_drv;
            session_cal_1000 += algo_in->calories_delta_drv;
            lap_cal_1000 += algo_in->calories_delta_drv;
        }

        algo_out->total_fat = (uint16_t) (session_fat_1000 / 1000);        // kcal
        algo_out->total_carb = (uint16_t) (session_carb_1000 / 1000);      // kcal
        algo_out->total_calories = (uint16_t) (session_cal_1000 / 1000);   // kcal
        algo_out->total_calories_lap = (uint16_t) (lap_cal_1000 / 1000);   // kcal

        // 运动警示
        auto_alert_calorie_check(&algo_out->alert, algo_out->total_calories);
    }
}

/**
 * @brief 算法控制
 *
 * @param algo_out 输出数据
 * @param ctrl_type 控制类型
 */
static void algo_sports_calories_ctrl(algo_sports_calories_pub_t *algo_out, const algo_sports_ctrl_t *ctrl)
{
    if (ctrl->sports_type == FIT_SPORTS_JUMP_ROPE || ctrl->sports_type == FIT_SPORTS_STRENGTH_TRAINING || ctrl->sports_type == FIT_SPORTS_ROWING_MACHINE)
    {
        if (enum_ctrl_start == ctrl->ctrl_type)
        {
            sports_calories_data_init();
            s_algo_in.in_rest = false;
        }
        else if (enum_ctrl_lap == ctrl->ctrl_type)
        {
            s_algo_in.in_rest = true;
        }
        else if (enum_ctrl_rest_resume == ctrl->ctrl_type)
        {
            algo_out->total_calories_pre_lap = algo_out->total_calories_lap;
            algo_out->total_calories_lap = 0;
            lap_cal_1000 = 0;

            s_algo_in.in_rest = false;
        }
    }
    else
    {
        if (enum_ctrl_start == ctrl->ctrl_type)
        {
            sports_calories_data_init();
            s_algo_in.in_rest = false;
        }
        else if (enum_ctrl_lap == ctrl->ctrl_type)
        {
            algo_out->total_calories_pre_lap = algo_out->total_calories_lap;
            algo_out->total_calories_lap = 0;
            lap_cal_1000 = 0;
        }
    }
}

/**
 * @brief 算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_calories_drv_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_SPORTS_CALORIES;
    head.input_type = DATA_ID_ALGO_FITNESS;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法控制订阅处理
 *
 * @param in 控制数据
 * @param len 数据长度
 */
static void algo_sports_ctrl_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_SPORTS_CALORIES;
    head.input_type = DATA_ID_EVENT_SPORTS_CTRL;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_sports_calories_out_callback(const void *out, uint32_t len)
{
    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_ALGO_SPORTS_CALORIES, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("%s publish error", __FUNCTION__);
    }
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_CALORIES_DRV,
        .topic_id = DATA_ID_EVENT_SPORTS_CALORIES_DRV,
        .callback = algo_calories_drv_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
};

/**
 * @brief 算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_sports_calories_init(void)
{
    return 0;
}

/**
 * @brief 算法open
 *
 * @return int32_t 结果
 */
static int32_t algo_sports_calories_open(void)
{
    int32_t ret = -1;
    optional_config_t config = {.sampling_rate = 0};

    if (s_is_open)
    {
        ALGO_COMP_LOG_W("%s already open", __FUNCTION__);
        return 0;
    }

    ALGO_COMP_LOG_D("%s open", __FUNCTION__);

    s_is_open = true;
    sports_calories_data_init();   //算法数据初始化

    return algo_topic_list_subscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
}

/**
 * @brief 算法feed
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_sports_calories_feed(uint32_t input_type, void *data, uint32_t len)
{
    algo_sports_calories_sub_t *algo_in = &s_algo_in;
    algo_sports_calories_pub_t *algo_out = &s_algo_out;

    switch (input_type)
    {
    case DATA_ID_ALGO_FITNESS:
    {
        const algo_kcal_pub_t *fitness = (const algo_kcal_pub_t *) data;
        if (fitness->calories_delta != UINT16_MAX)
        {
            algo_in->calories_delta_drv = fitness->calories_delta;
        }
        if (fitness->fat_delta != UINT16_MAX)
        {
            algo_in->fat_delta_drv = fitness->fat_delta;
        }
        if (fitness->carb_delta != UINT16_MAX)
        {
            algo_in->carb_delta_drv = fitness->carb_delta;
        }
    }
    break;
    case DATA_ID_EVENT_SPORTS_CTRL:
    {
        const algo_sports_ctrl_t *sports_ctrl = (algo_sports_ctrl_t *) data;
        algo_in->saving_status = sports_ctrl->saving_status;

        //算法控制
        algo_sports_calories_ctrl(algo_out, sports_ctrl);

        //数据发布
        if (sports_ctrl->ctrl_type == enum_ctrl_null)
        {
            //算法处理
            algo_sports_calories_deal(algo_out, algo_in);

            //数据发布
            algo_sports_calories_out_callback(algo_out, sizeof(algo_sports_calories_pub_t));
        }
        else if (sports_ctrl->ctrl_type == enum_ctrl_lap)
        {
            //记圈后马上数据发布
            algo_sports_calories_out_callback(algo_out, sizeof(algo_sports_calories_pub_t));
        }
    }
    break;
    default:
        break;
    }
    return 0;
}

/**
 * @brief 算法close
 *
 * @return int32_t 结果
 */
static int32_t algo_sports_calories_close(void)
{
    int ret = -1;

    if (!s_is_open)
    {
        ALGO_COMP_LOG_W("%s already close", __FUNCTION__);
        return 0;
    }

    algo_topic_list_unsubscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));

    s_is_open = false;
    ALGO_COMP_LOG_D("%s close", __FUNCTION__);
    return 0;
}

// 算法组件实现
static algo_compent_ops_t s_sports_calories_algo = {
    .init = algo_sports_calories_init,
    .open = algo_sports_calories_open,
    .feed = algo_sports_calories_feed,
    .close = algo_sports_calories_close,
    .ioctl = NULL,
};

/**
 * @brief 组件注册
 *
 * @return int32_t 结果
 */
int32_t register_sports_calories_algo(void)
{
    algo_compnent_register(ALGO_TYPE_SPORTS_CALORIES, &s_sports_calories_algo);
    return 0;
}