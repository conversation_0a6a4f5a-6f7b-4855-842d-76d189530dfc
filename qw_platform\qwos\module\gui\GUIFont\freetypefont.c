/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   freetypefont.c
@Time    :   2024/12/10 14:26:35
*
**************************************************************************/

#include "freetypefont.h"
#include "igs_dev_config.h"
#include "rtthread.h"
#include <truetype/lv_freetype.h>
#include <truetype/lv_freetype_alloc.h>
#include <truetype/lv_freetype_lru.h>
#include <truetype/lv_freetype_tools.h>

// 宏定义

#define TTF_BPP_CHECK(bpp) ((bpp) == 1 || (bpp) == 2 || (bpp) == 4 || (bpp) == 8)   // 检查位深

// lru算法缓存200个字符
#define TTF_ROUND(x, y)     (((x) + (y) - 1) / (y) * (y))   // 对x向上取y整数倍
#define TTF_LETTER_BMP_SIZE 1536                            // 单个字符缓存块大小 需要 >= (ttf_lru_data_t + 最大字体点阵大小)
#define TTF_LETTER_BMP_NUM  200                             // 缓存字符个数

// lru算法框架使用的缓存大小
// lru句柄内容 + (lru节点句柄内容 + lru节点指针 + key内容) * 字符个数
// lv_ftt_lru_t + (lv_ftt_lru_item_t + 4 + ttf_lru_head_t) * TTF_LETTER_BMP_NUM
// 因为tlsf分配不规则内存的特殊性 大量小块内存效率比较低 卡太死容易分配不出来 x2保平安
#define TTF_LRU_POOL_SIZE TTF_ROUND(sizeof(lv_ftt_lru_t) + (sizeof(lv_ftt_lru_item_t) + 4 + sizeof(ttf_lru_head_t)) * TTF_LETTER_BMP_NUM * 2, 512)

// 字符数据缓存大小
// 字符点阵使用的缓存 = TTF_LETTER_BMP_SIZE * TTF_LETTER_BMP_NUM
#define TTF_DATA_POOL_SIZE TTF_ROUND(TTF_LETTER_BMP_SIZE *TTF_LETTER_BMP_NUM, 512)

// 字符数据缓存大小
// 字符点阵使用的缓存额外增加10个字符
#define TTF_DATA_POOL_EX_SIZE TTF_ROUND(TTF_LETTER_BMP_SIZE * 10, 512)

// 总缓存大小
#define TTF_TOTOAL_POOL_SIZE (TTF_LRU_POOL_SIZE + TTF_DATA_POOL_SIZE + TTF_DATA_POOL_EX_SIZE)

#ifdef IGS_DEV
#include "mem_section.h"
#include <src/misc/lv_lru.h>
L2_RET_BSS_SECT_BEGIN(freetype_mem)
ALIGN(4) static uint32_t ft_lru_mem_int[TTF_TOTOAL_POOL_SIZE / sizeof(uint32_t)];
L2_RET_BSS_SECT_END
#else
static uint32_t ft_lru_mem_int[TTF_TOTOAL_POOL_SIZE / sizeof(uint32_t)];
#endif

// extern
extern const lv_font_t lv_font_DINPro_bold_56;   // 默认内部字体
extern const uint16_t g_all_text_list[];         // 所有字符unicode列表
extern const uint32_t g_all_text_list_size;      // 所有字符unicode个数

// typedef
typedef bool (*get_all_font_dsc)(const struct _lv_font_t *, lv_font_glyph_dsc_t *, uint32_t, uint32_t);
typedef const uint8_t *(*get_all_font_bitmap)(const struct _lv_font_t *, uint32_t);

// 静态定义

static uint8_t *p_ft_lru_mem_int = (uint8_t *) ft_lru_mem_int;                        // lru算法缓存池
static uint8_t *p_ft_data_mem_int = (uint8_t *) ft_lru_mem_int + TTF_LRU_POOL_SIZE;   // 字符数据缓存池

static lv_tlsf_t lv_ft_data_tlsf;                                                     // 字符数据内存池
static lv_ftt_lru_t *g_texture_cache = NULL;                                          // lru算法句柄

static ttf_lru_data_t *g_font_lru_buf = NULL;                                         // lru数据指针 包含字符描述和字符点阵
static get_all_font_dsc g_bak_all_font_dsc_func = NULL;   // 缓存一个truetype_font字体描述符回调函数 这个函数是各个字体通用的 指向的是 get_glyph_dsc_cb_nocache
static get_all_font_bitmap g_bak_all_font_bmp_func = NULL;   // 缓存一个truetype_font字体点阵回调函数 这个函数是各个字体通用的 指向的是 get_glyph_bitmap_cb_nocache
static int g_freetype_check_pct = 0;                         // 字体检测百分比
static uint32_t g_max_walk_size = 0;                         // 字体检测最大字符点阵大小

static void lv_ftt_free(void *data)
{
    rt_enter_critical();
    ttf_lru_data_t *p_lru_item = (ttf_lru_data_t *) data;
    if (p_lru_item != NULL)
    {
        if (p_lru_item->buf != NULL)    // 会存在某个lru item的buf == NULL的情况 例如空格字符
        {
            lv_fttmem_free(lv_ft_data_tlsf, p_lru_item->buf);
        }  
        lv_fttmem_free(lv_ft_data_tlsf, p_lru_item);
    }
    rt_exit_critical();
}

static void *lv_ftt_malloc(size_t size)
{
    rt_enter_critical();
    void *buf = lv_fttmem_malloc(lv_ft_data_tlsf, size);
    rt_exit_critical();
    return buf;
}

static int binsearch(int start, int end, int val)
{
    if (end < start || val < g_all_text_list[start] || val > g_all_text_list[end])
    {
        return -1;
    }

    int mid = (end - start) / 2 + start;
    if (g_all_text_list[mid] == val)
    {
        return mid;
    }
    else if (g_all_text_list[start] == val)
    {
        return start;
    }
    else if (g_all_text_list[end] == val)
    {
        return end;
    }
    else if (end == mid || start == mid)
    {
        return -1;
    }
    else if (g_all_text_list[mid] < val)
    {
        return binsearch(mid + 1, end - 1, val);
    }
    else
    {
        return binsearch(start + 1, mid - 1, val);
    }
}

static int find_letter_in_list(uint32_t unicode_letter)
{
    return binsearch(0, g_all_text_list_size - 1, unicode_letter);
}

static bool file_write(QW_FIL *fp, unsigned char *buf, uint32_t size)
{
    if (fp == NULL || buf == NULL)
    {
        return false;
    }

    if (size == 0)
    {
        return true;
    }

    UINT num = 0;
    if (qw_f_write(fp, buf, size, &num) == QW_OK && num == size)
    {
        return true;
    }
    else
    {
        return false;
    }
}

static bool file_read(QW_FIL *fp, unsigned char *buf, uint32_t size)
{
    if (fp == NULL || buf == NULL)
    {
        return false;
    }

    if (size == 0)
    {
        return true;
    }

    UINT num = 0;
    if (qw_f_read(fp, buf, size, &num) == QW_OK)
    {
        return (num > 0);
    }
    else
    {
        return false;
    }
}

static bool get_ttf_dsc_cb(const lv_font_t *font, lv_font_glyph_dsc_t *dsc_out, uint32_t unicode_letter, uint32_t unicode_letter_next)
{
    LV_UNUSED(unicode_letter_next);
    LV_ASSERT_NULL(font);

    static ttf_lru_head_t lru_head = {0};
    ttf_user_data_t *p_user_data = (ttf_user_data_t *) font->user_data;

    if (dsc_out != NULL && p_user_data != NULL && p_user_data->mark > 0)
    {
        dsc_out->adv_w = 0;
        dsc_out->box_h = 0;
        dsc_out->box_w = 0;
        dsc_out->ofs_x = 0;
        dsc_out->ofs_y = 0;
        dsc_out->bpp = 0;

        // lru键值信息
        lru_head.letter = unicode_letter;    // 字符
        lru_head.mark = p_user_data->mark;   // 字体标记

        if (g_font_lru_buf != NULL && g_font_lru_buf->mark == p_user_data->mark && g_font_lru_buf->letter == unicode_letter)
        {            
            // 成功命中上一次缓存
            g_max_walk_size = g_max_walk_size < g_font_lru_buf->size ? g_font_lru_buf->size : g_max_walk_size;
            memcpy(dsc_out, &g_font_lru_buf->dsc, sizeof(lv_font_glyph_dsc_t));
            return true;
        }

        lv_ftt_lru_get(g_texture_cache, &lru_head, sizeof(ttf_lru_head_t), (void **) &g_font_lru_buf);
        if (g_font_lru_buf != NULL)
        {
            // 成功命中缓存
            g_max_walk_size = g_max_walk_size < g_font_lru_buf->size ? g_font_lru_buf->size : g_max_walk_size;
            memcpy(dsc_out, &g_font_lru_buf->dsc, sizeof(lv_font_glyph_dsc_t));
        }
        else
        {
            // 未命中缓存 从文件查找 并添加到lru缓存
            uint32_t bmp_size = 0;
            uint32_t ch_offset = 0;
            int list_pos = find_letter_in_list(unicode_letter);
            if (list_pos >= 0)
            {
                uint32_t index_offset = sizeof(ttf_idx_head_t) + list_pos * sizeof(uint32_t);
#ifdef SIMULATOR
                if (qw_f_lseek(p_user_data->fp_idx, index_offset) == QW_OK && file_read(p_user_data->fp_idx, (unsigned char *) &ch_offset, sizeof(uint32_t)))
                {
                    uint32_t file_size = qw_f_size(p_user_data->fp_buffer);
                    RT_ASSERT(ch_offset < file_size);
                    if (qw_f_lseek(p_user_data->fp_buffer, ch_offset) == QW_OK)
                    {
                        g_font_lru_buf = (ttf_lru_data_t *) lv_ftt_malloc(sizeof(ttf_lru_data_t));
                        if (g_font_lru_buf != NULL)
                        {
                            memset(g_font_lru_buf, 0, sizeof(ttf_lru_data_t));

                            g_font_lru_buf->letter = lru_head.letter;
                            file_read(p_user_data->fp_buffer, (unsigned char *) &g_font_lru_buf->dsc, sizeof(lv_font_glyph_dsc_t));
                            file_read(p_user_data->fp_buffer, (unsigned char *) &g_font_lru_buf->size, sizeof(uint32_t));

                            RT_ASSERT(g_font_lru_buf->size < TTF_LETTER_BMP_SIZE - sizeof(ttf_lru_data_t));
                            if (g_font_lru_buf->size > 0 && g_font_lru_buf->size < TTF_LETTER_BMP_SIZE - sizeof(ttf_lru_data_t)
                                && TTF_BPP_CHECK(g_font_lru_buf->dsc.bpp))
                            {
                                g_font_lru_buf->buf = (uint8_t *) lv_ftt_malloc(g_font_lru_buf->size);

                                file_read(p_user_data->fp_buffer, (unsigned char *) g_font_lru_buf->buf, g_font_lru_buf->size);

                                g_max_walk_size = g_max_walk_size < g_font_lru_buf->size ? g_font_lru_buf->size : g_max_walk_size;

                                lv_ftt_lru_set(g_texture_cache, &lru_head, sizeof(ttf_lru_head_t), (void *) g_font_lru_buf, TTF_LETTER_BMP_SIZE);
                            }
                        }
                        else
                        {
                            g_font_lru_buf = NULL;
                        }
                    }
                }
#else
                if (qw_f_lseek(&p_user_data->fp_idx, index_offset) == QW_OK && file_read(&p_user_data->fp_idx, (unsigned char *) &ch_offset, sizeof(uint32_t)))
                {
                    uint32_t file_size = qw_f_size(&p_user_data->fp_buffer);
                    RT_ASSERT(ch_offset < file_size);
                    if (qw_f_lseek(&p_user_data->fp_buffer, ch_offset) == QW_OK)
                    {
                        // 申请 lru item缓存
                        g_font_lru_buf = (ttf_lru_data_t *) lv_ftt_malloc(sizeof(ttf_lru_data_t));
                        if (g_font_lru_buf != NULL)
                        {
                            memset(g_font_lru_buf, 0, sizeof(ttf_lru_data_t));

                            // 设置节点信息
                            g_font_lru_buf->letter = lru_head.letter;
                            g_font_lru_buf->mark = lru_head.mark;
                            // 从内部字库文件读取字符信息
                            file_read(&p_user_data->fp_buffer, (unsigned char *) &g_font_lru_buf->dsc, sizeof(lv_font_glyph_dsc_t));
                            file_read(&p_user_data->fp_buffer, (unsigned char *) &g_font_lru_buf->size, sizeof(uint32_t));

                            if (g_font_lru_buf->size < TTF_LETTER_BMP_SIZE - sizeof(ttf_lru_data_t)
                                && TTF_BPP_CHECK(g_font_lru_buf->dsc.bpp))
                            {
                                if (g_font_lru_buf->size > 0)
                                {
                                    // 正常字符

                                    // 申请字符点阵缓冲区
                                    g_font_lru_buf->buf = (uint8_t *) lv_ftt_malloc(g_font_lru_buf->size);
                                    if (g_font_lru_buf->buf != NULL)
                                    {
                                        memset(g_font_lru_buf->buf, 0, g_font_lru_buf->size);

                                        // 从内部字库文件读取字符点阵数据
                                        file_read(&p_user_data->fp_buffer, (unsigned char *) g_font_lru_buf->buf, g_font_lru_buf->size);

                                        g_max_walk_size = g_max_walk_size < g_font_lru_buf->size ? g_font_lru_buf->size : g_max_walk_size;

                                        // 插入lru
                                        lv_ftt_lru_set(g_texture_cache, &lru_head, sizeof(ttf_lru_head_t), (void *) g_font_lru_buf, TTF_LETTER_BMP_SIZE);
                                    }
                                    else
                                    {
                                        // 申请失败 释放整个item
                                        lv_ftt_free(g_font_lru_buf);
                                        g_font_lru_buf = NULL;
                                    }
                                }
                                else
                                {
                                    // g_font_lru_buf->size == 0的字符 例如空格 也放进lru 避免多次访问文件

                                    g_font_lru_buf->buf = NULL;
                                    lv_ftt_lru_set(g_texture_cache, &lru_head, sizeof(ttf_lru_head_t), (void *) g_font_lru_buf, TTF_LETTER_BMP_SIZE);
                                }
                            }
                            else
                            {
                                lv_ftt_free(g_font_lru_buf);
                                g_font_lru_buf = NULL;
                            }
                        }
                    }
                }
#endif   // SIMULATOR
            }
        }
    }
    return true;
}

static const uint8_t *get_ttf_bitmap_cb(const lv_font_t *font, uint32_t unicode_letter)
{
    LV_UNUSED(font);
    LV_UNUSED(unicode_letter);
    if (g_font_lru_buf != NULL && g_font_lru_buf->letter == unicode_letter)
    {
        return (const uint8_t *) g_font_lru_buf->buf;
    }
    else
    {
        return NULL;
    }
}

static uint32_t lv_get_font_cache_size(lv_font_glyph_dsc_t *dsc_out, uint8_t bpp)
{
    if (bpp > 0)
    {
        uint8_t alignMask = 8 / bpp - 1;
        uint32_t bitmapSize = dsc_out->box_w * dsc_out->box_h;
        uint32_t dataSize = ((dsc_out->box_w + alignMask) & ~alignMask) * dsc_out->box_h * bpp / 8;
        dataSize = (dataSize + 3) & ~0x03;

        return dataSize;
    }
    else
    {
        return 0;
    }
}

static bool get_ttf_all_font_dsc_cb(const lv_font_t *font, lv_font_glyph_dsc_t *dsc_out, uint32_t unicode_letter, uint32_t unicode_letter_next)
{
    LV_UNUSED(unicode_letter_next);
    LV_ASSERT_NULL(font);

    static ttf_lru_head_t lru_all_head = {0};
    ttf_user_data_t *p_user_data = (ttf_user_data_t *) font->user_data;

    if (dsc_out != NULL && p_user_data != NULL && p_user_data->mark > 0)
    {
        // 初始化
        dsc_out->adv_w = 0;
        dsc_out->box_h = 0;
        dsc_out->box_w = 0;
        dsc_out->ofs_x = 0;
        dsc_out->ofs_y = 0;
        dsc_out->bpp = 0;

        // 设置lru键值信息
        lru_all_head.letter = unicode_letter;    // 字符
        lru_all_head.mark = p_user_data->mark;   // 字体标记

        if (g_font_lru_buf != NULL && g_font_lru_buf->mark == p_user_data->mark && g_font_lru_buf->letter == unicode_letter)
        {            
            // 成功命中上一次缓存 不必再访问lru
            g_max_walk_size = g_max_walk_size < g_font_lru_buf->size ? g_font_lru_buf->size : g_max_walk_size;
            memcpy(dsc_out, &g_font_lru_buf->dsc, sizeof(lv_font_glyph_dsc_t));
            return true;
        }

        // 从lru缓存中获取
        lv_ftt_lru_get(g_texture_cache, &lru_all_head, sizeof(ttf_lru_head_t), (void **) &g_font_lru_buf);
        if (g_font_lru_buf != NULL)
        {
            // 成功命中缓存
            g_max_walk_size = g_max_walk_size < g_font_lru_buf->size ? g_font_lru_buf->size : g_max_walk_size;
            memcpy(dsc_out, &g_font_lru_buf->dsc, sizeof(lv_font_glyph_dsc_t));
        }
        else if (g_bak_all_font_dsc_func != NULL && g_bak_all_font_bmp_func != NULL)
        {
            // 没有命中缓存，从ttf中获取
            if (g_bak_all_font_dsc_func(font, dsc_out, unicode_letter, unicode_letter_next))    // 动态创建字符和点阵图 获取dsc
            {
                uint32_t buf_size = lv_get_font_cache_size(dsc_out, dsc_out->bpp);

                if (buf_size < TTF_LETTER_BMP_SIZE - sizeof(ttf_lru_data_t) && TTF_BPP_CHECK(dsc_out->bpp)) // 校验
                {
                    // 申请lru item缓存
                    g_font_lru_buf = (ttf_lru_data_t *) lv_ftt_malloc(sizeof(ttf_lru_data_t));
                    if (g_font_lru_buf != NULL)
                    {
                        // 设置缓存信息
                        g_font_lru_buf->letter = lru_all_head.letter;
                        g_font_lru_buf->mark = lru_all_head.mark;
                        g_font_lru_buf->size = buf_size;
                        memcpy(&g_font_lru_buf->dsc, dsc_out, sizeof(lv_font_glyph_dsc_t));
                        if (buf_size > 0)
                        {
                            // 正常字符

                            // 申请字符点阵缓存
                            g_font_lru_buf->buf = (uint8_t *) lv_ftt_malloc(g_font_lru_buf->size);
                            if (g_font_lru_buf->buf != NULL)
                            {
                                // 从ttf点阵缓存中获取点阵数据 拷贝到lru item
                                const uint8_t *buf = g_bak_all_font_bmp_func(font, unicode_letter);
                                memcpy(g_font_lru_buf->buf, buf, g_font_lru_buf->size);

                                // 插入lru item
                                lv_ftt_lru_set(g_texture_cache, &lru_all_head, sizeof(ttf_lru_head_t), (void *) g_font_lru_buf, TTF_LETTER_BMP_SIZE);
                            }
                            else
                            {
                                // 缓存申请失败 释放整个字体信息
                                lv_ftt_free(g_font_lru_buf);
                                g_font_lru_buf = NULL;
                            }
                        }
                        else
                        {
                            // buf_size == 0 的字符 空格等等 也需要存入lru中 避免频繁访问ttf创建字符 消耗时间
                            g_font_lru_buf->buf = NULL;
                            lv_ftt_lru_set(g_texture_cache, &lru_all_head, sizeof(ttf_lru_head_t), (void *) g_font_lru_buf, TTF_LETTER_BMP_SIZE);
                        }
                    }
                }
            }
        }
    }
    return true;
}

static const uint8_t *get_ttf_all_font_bitmap_cb(const lv_font_t *font, uint32_t unicode_letter)
{
    LV_UNUSED(font);
    LV_UNUSED(unicode_letter);
    if (g_font_lru_buf != NULL && g_font_lru_buf->letter == unicode_letter)
    {
        return (const uint8_t *) g_font_lru_buf->buf;
    }
    else
    {
        return NULL;
    }
}

static bool CreateFTTBufferFile(const char *buf_path, const char *ttf_path, uint8_t size, uint8_t font_total_num)
{
    if (buf_path == NULL || !lv_ft_font_tool_init(ttf_path, size))
    {
        return false;
    }

    char index_path[260] = {0};
    lv_ft_tools_info_t ttf_info = {0};
    lv_ft_tools_head_t ttf_head = {0};
    unsigned char *buf = NULL;
    QW_FIL *fp_font = NULL;
    QW_FIL *fp_idx = NULL;
    int count = 0;
    uint32_t offset = 0;
    bool ret = true;

    ttf_idx_head_t head = {0};
    ttf_idx_tail_t tail = {0};

    memset(&head, 0, sizeof(ttf_idx_head_t));
    memset(&tail, 0, sizeof(ttf_idx_tail_t));

    head.version = 0;
    head.total_num = g_all_text_list_size;

    sprintf_array(index_path, "%s.idx", buf_path);

    float pct_tick = (100.0f / (font_total_num)) / head.total_num;
    float last_pct = 0;
    int last_pct_int = 0;

    if (qw_f_open(&fp_idx, index_path, QW_FA_CREATE_ALWAYS | QW_FA_WRITE) == QW_OK)
    {
        if (lv_ft_font_tool_head_get(&ttf_head))
        {
            head.line_height = ttf_head.line_height;
            head.base_line = ttf_head.base_line;
            head.subpx = ttf_head.subpx;
            head.underline_position = ttf_head.underline_position;
            head.underline_thickness = ttf_head.underline_thickness;

            if (!file_write(fp_idx, (unsigned char *) &head, sizeof(ttf_idx_head_t)))
            {
                qw_f_close(fp_idx);
                lv_ft_font_tool_destroy();
                return false;
            }
        }

        if (qw_f_open(&fp_font, buf_path, QW_FA_CREATE_ALWAYS | QW_FA_WRITE) == QW_OK)
        {
            for (size_t i = 0; i < head.total_num; i++)
            {
                memset(&ttf_info, 0, sizeof(lv_ft_tools_info_t));
                buf = NULL;
                if (lv_ft_font_tool_ch_get(g_all_text_list[i], &ttf_info, &buf))
                {
                    if (!file_write(fp_font, (unsigned char *) &ttf_info.dsc, sizeof(lv_font_glyph_dsc_t)))
                    {
                        ret = false;
                        break;
                    }

                    if (!file_write(fp_font, (unsigned char *) &ttf_info.buf_size, sizeof(uint32_t)))
                    {
                        ret = false;
                        break;
                    }

                    if (ttf_info.buf_size > 0)
                    {
                        if (!file_write(fp_font, buf, ttf_info.buf_size))
                        {
                            ret = false;
                            break;
                        }

                        if (tail.max_size < ttf_info.buf_size)
                        {
                            tail.max_size = ttf_info.buf_size;
                        }
                    }

                    if (!file_write(fp_idx, (unsigned char *) &offset, sizeof(uint32_t)))
                    {
                        ret = false;
                        break;
                    }

                    offset += (ttf_info.buf_size + sizeof(lv_font_glyph_dsc_t) + sizeof(uint32_t));

                    count++;
                }
                else
                {
                    rt_kprintf("lv_ft_font_tool_ch_get: error\n");
                }

                last_pct += pct_tick;
                if (last_pct_int < (int) last_pct)
                {
                    g_freetype_check_pct += ((int) last_pct - last_pct_int);
                    last_pct_int = (int) last_pct;
                }
            }
            qw_f_close(fp_font);
        }
        else
        {
            rt_kprintf("buf_path: %s open error\n", buf_path);
        }

        if (ret)
        {
            if (!file_write(fp_idx, (unsigned char *) &tail, sizeof(ttf_idx_tail_t)))
            {
                qw_f_close(fp_idx);
                lv_ft_font_tool_destroy();
                return false;
            }

            head.version = FONT_PACKAGE_VERSION;
            qw_f_lseek(fp_idx, 0);
            if (!file_write(fp_idx, (unsigned char *) &head, sizeof(ttf_idx_head_t)))
            {
                qw_f_close(fp_idx);
                lv_ft_font_tool_destroy();
                return false;
            }
        }

        qw_f_close(fp_idx);
    }
    else
    {
        rt_kprintf("index_path: %s open error\n", index_path);
    }

    lv_ft_font_tool_destroy();

#ifdef SIMULATOR
    LV_ASSERT(count == head.total_num);
#else
    if (count != head.total_num)
    {
        qw_f_unlink(buf_path);
        qw_f_unlink(index_path);
    }
#endif   // SIMULATOR

    return ret;
}

int freetypefont_check_buffer(qw_font_type_t *qw_font_text, const char *save_path)
{
#if FONT_MAKER_AUTO_TEST_ENABLE
    return true;
#endif
    if (qw_font_text == NULL || save_path == NULL)
    {
        return false;
    }

    char path[100] = {0};
    ttf_idx_head_t ttf_head = {0};
    QW_FIL *fp_check = NULL;
    for (uint8_t i = 0; i < qw_font_text->size_num; i++)
    {
        bool rebuild = false;

        sprintf_array(path, "%s%s_%d.ttb", save_path, qw_font_text->g_freetypefont_head_def[i].id, qw_font_text->g_freetypefont_head_def[i].size);

        if (qw_f_open(&fp_check, path, QW_FA_OPEN_EXISTING | QW_FA_READ) == QW_OK)
        {
            qw_f_close(fp_check);
        }
        else
        {
            return true;
        }

        strcat(path, ".idx");

        if (qw_f_open(&fp_check, path, QW_FA_OPEN_EXISTING | QW_FA_READ) == QW_OK)
        {
            if (file_read(fp_check, (unsigned char *) &ttf_head, sizeof(ttf_idx_head_t)))
            {
                if (ttf_head.version != FONT_PACKAGE_VERSION)
                {
                    qw_f_close(fp_check);
                    return true;
                }
            }
            else
            {
                qw_f_close(fp_check);
                return true;
            }

            qw_f_close(fp_check);
        }
        else
        {
            return true;
        }
    }
    return false;
}

void freetypefont_create_buffer(qw_font_type_t *qw_font_text, const char *save_path)
{
    if (qw_font_text == NULL || save_path == NULL)
    {
        return;
    }

    char path[100] = {0};
    ttf_idx_head_t ttf_head = {0};
    QW_FIL *fp_check = NULL;
    g_freetype_check_pct = 0;
    for (uint8_t i = 0; i < qw_font_text->size_num; i++)
    {
        g_freetype_check_pct = i * 100 / qw_font_text->size_num;

        bool rebuild = false;

        sprintf_array(path, "%s%s_%d.ttb", save_path, qw_font_text->g_freetypefont_head_def[i].id, qw_font_text->g_freetypefont_head_def[i].size);
        rt_kprintf("[Font] freetypefont_create_buffer: %s\n", path);
        if (qw_f_open(&fp_check, path, QW_FA_OPEN_EXISTING | QW_FA_READ) == QW_OK)
        {
            qw_f_close(fp_check);
        }
        else
        {
            rebuild = true;
        }
        //打个标
        if (!rebuild)
        {
            strcat(path, ".idx");

            if (qw_f_open(&fp_check, path, QW_FA_OPEN_EXISTING | QW_FA_READ) == QW_OK)
            {
                if (file_read(fp_check, (unsigned char *) &ttf_head, sizeof(ttf_idx_head_t)))
                {
                    rebuild = (ttf_head.version != FONT_PACKAGE_VERSION);
                }
                else
                {
                    rebuild = true;
                }

                qw_f_close(fp_check);
            }
            else
            {
                rebuild = true;
            }
        }
#if FONT_MAKER_AUTO_TEST_ENABLE
        rebuild = true;
#endif

        if (rebuild)
        {
            sprintf_array(path, "%s%s_%d.ttb", save_path, qw_font_text->g_freetypefont_head_def[i].id, qw_font_text->g_freetypefont_head_def[i].size);
            CreateFTTBufferFile(path, qw_font_text->g_freetypefont_head_def[i].path, qw_font_text->g_freetypefont_head_def[i].size, qw_font_text->size_num);
            rt_kprintf("[Font] CreateFTTBufferFile: %s\n", path);
#ifdef SIMULATOR
            Sleep(1500);   //创建缓存时加延迟拟真,不然进度条弹不出来
#endif                     // SIMULATOR
        }
    }
}

int freetypefont_check_pct_get()
{
    return g_freetype_check_pct;
}

void freetypefont_init_base_part(qw_font_type_t *qw_font_full)
{
    if (qw_font_full == NULL)
    {
        return;
    }

    g_freetype_check_pct = 0;

    lv_ft_data_tlsf = lv_fttmem_alloc_init(p_ft_data_mem_int, TTF_DATA_POOL_SIZE + TTF_DATA_POOL_EX_SIZE);
    g_texture_cache = lv_ftt_lru_create((void *) p_ft_lru_mem_int, TTF_LRU_POOL_SIZE, TTF_DATA_POOL_SIZE, TTF_LETTER_BMP_SIZE, lv_ftt_free, NULL);

    //全字库
    const lv_font_t *ret = NULL;
    for (int i = 0; i < qw_font_full->size_num; i++)
    {
        font_manager_add_font(qw_font_full->g_freetypefont_head_def[i].path, qw_font_full->g_freetypefont_head_def[i].size);

        ret = font_manager_get_font(qw_font_full->g_freetypefont_head_def[i].path, qw_font_full->g_freetypefont_head_def[i].size);
        if (ret != NULL)
        {
            qw_font_full->g_font_data[i].mark = 0xA0 + i;

            memcpy(&qw_font_full->g_font_lsit[i], ret, sizeof(lv_font_t));

            g_bak_all_font_dsc_func = qw_font_full->g_font_lsit[i].get_glyph_dsc;
            g_bak_all_font_bmp_func = qw_font_full->g_font_lsit[i].get_glyph_bitmap;

            qw_font_full->g_font_lsit[i].get_glyph_dsc = get_ttf_all_font_dsc_cb;
            qw_font_full->g_font_lsit[i].get_glyph_bitmap = get_ttf_all_font_bitmap_cb;
            qw_font_full->g_font_lsit[i].user_data = (void *) &qw_font_full->g_font_data[i];
            qw_font_full->g_font_lsit[i].fallback = NULL;
        }
        else
        {
            memcpy(&qw_font_full->g_font_lsit[i], &lv_font_DINPro_bold_56, sizeof(lv_font_t));
        }
    }
}

void freetypefont_init(qw_font_type_t *qw_font_text, const char *save_path)
{
    if (qw_font_text == NULL || save_path == NULL)
    {
        return;
    }

    //缓存字体
    ttf_idx_head_t ttf_head = {0};
    ttf_idx_tail_t ttf_tail = {0};
    char path[100] = {0};

    int font_mark = 1;   //分配标识码

    for (uint8_t i = 0; i < qw_font_text->size_num; i++)
    {
        memset(&qw_font_text->g_font_data[i], 0, sizeof(ttf_user_data_t));

        sprintf_array(path, "%s%s_%d.ttb", save_path, qw_font_text->g_freetypefont_head_def[i].id, qw_font_text->g_freetypefont_head_def[i].size);

#ifdef SIMULATOR
        if (qw_f_open(&qw_font_text->g_font_data[i].fp_buffer, path, QW_FA_OPEN_EXISTING | QW_FA_READ) == QW_OK)
        {
            strcat(path, ".idx");

            memset(&ttf_head, 0, sizeof(ttf_idx_head_t));

            if (qw_f_open(&qw_font_text->g_font_data[i].fp_idx, path, QW_FA_OPEN_EXISTING | QW_FA_READ) == QW_OK)
            {
                if (file_read(qw_font_text->g_font_data[i].fp_idx, (unsigned char *) &ttf_head, sizeof(ttf_idx_head_t))
                    && ttf_head.version == FONT_PACKAGE_VERSION)
                {
                    qw_font_text->g_font_data[i].mark = font_mark;
                    font_mark++;

                    qw_font_text->g_font_lsit[i].line_height = ttf_head.line_height;
                    qw_font_text->g_font_lsit[i].base_line = ttf_head.base_line;
                    qw_font_text->g_font_lsit[i].subpx = ttf_head.subpx;
                    qw_font_text->g_font_lsit[i].underline_position = ttf_head.underline_position;
                    qw_font_text->g_font_lsit[i].underline_thickness = ttf_head.underline_thickness;
                    qw_font_text->g_font_lsit[i].get_glyph_dsc = get_ttf_dsc_cb;
                    qw_font_text->g_font_lsit[i].get_glyph_bitmap = get_ttf_bitmap_cb;
                    qw_font_text->g_font_lsit[i].user_data = (void *) &qw_font_text->g_font_data[i];

                    //缓存大小检查
                    memset(&ttf_tail, 0, sizeof(ttf_idx_tail_t));
                    uint32_t tail_pos = sizeof(ttf_idx_head_t) + ttf_head.total_num * sizeof(uint32_t);
                    qw_f_lseek(qw_font_text->g_font_data[i].fp_idx, tail_pos);
                    file_read(qw_font_text->g_font_data[i].fp_idx, (unsigned char *) &ttf_tail, sizeof(ttf_idx_tail_t));

                    // LV_ASSERT(ttf_tail.max_size < TTF_BUFFER_BMP_SIZE);

                    qw_f_lseek(qw_font_text->g_font_data[i].fp_idx, 0);

                    continue;   //加载成功则不关闭文件句柄
                }
                else
                {
                    qw_f_close(qw_font_text->g_font_data[i].fp_idx);
                }
            }

            qw_f_close(qw_font_text->g_font_data[i].fp_buffer);
        }
#else
        if (qw_f_open_static(&qw_font_text->g_font_data[i].fp_buffer, path, QW_FA_OPEN_EXISTING | QW_FA_READ) == QW_OK)
        {
            strcat(path, ".idx");

            memset(&ttf_head, 0, sizeof(ttf_idx_head_t));

            if (qw_f_open_static(&qw_font_text->g_font_data[i].fp_idx, path, QW_FA_OPEN_EXISTING | QW_FA_READ) == QW_OK)
            {
                if (file_read(&qw_font_text->g_font_data[i].fp_idx, (unsigned char *) &ttf_head, sizeof(ttf_idx_head_t))
                    && ttf_head.version == FONT_PACKAGE_VERSION)
                {
                    qw_font_text->g_font_data[i].mark = font_mark;
                    font_mark++;

                    qw_font_text->g_font_lsit[i].line_height = ttf_head.line_height;
                    qw_font_text->g_font_lsit[i].base_line = ttf_head.base_line;
                    qw_font_text->g_font_lsit[i].subpx = ttf_head.subpx;
                    qw_font_text->g_font_lsit[i].underline_position = ttf_head.underline_position;
                    qw_font_text->g_font_lsit[i].underline_thickness = ttf_head.underline_thickness;
                    qw_font_text->g_font_lsit[i].get_glyph_dsc = get_ttf_dsc_cb;
                    qw_font_text->g_font_lsit[i].get_glyph_bitmap = get_ttf_bitmap_cb;
                    qw_font_text->g_font_lsit[i].user_data = (void *) &qw_font_text->g_font_data[i];
                    qw_font_text->g_font_lsit[i].fallback = NULL;

                    //缓存大小检查
                    memset(&ttf_tail, 0, sizeof(ttf_idx_tail_t));
                    uint32_t tail_pos = sizeof(ttf_idx_head_t) + ttf_head.total_num * sizeof(uint32_t);
                    qw_f_lseek(&qw_font_text->g_font_data[i].fp_idx, tail_pos);
                    file_read(&qw_font_text->g_font_data[i].fp_idx, (unsigned char *) &ttf_tail, sizeof(ttf_idx_tail_t));

                    // LV_ASSERT(ttf_tail.max_size < TTF_BUFFER_BMP_SIZE);

                    qw_f_lseek(&qw_font_text->g_font_data[i].fp_idx, 0);

                    continue;   //加载成功则不关闭文件句柄
                }
                else
                {
                    qw_f_close_static(&qw_font_text->g_font_data[i].fp_idx);
                }
            }

            qw_f_close_static(&qw_font_text->g_font_data[i].fp_buffer);
        }
#endif                                                                       // SIMULATOR

        memset(&qw_font_text->g_font_data[i], 0, sizeof(ttf_user_data_t));   //加载失败
    }

    g_freetype_check_pct = 100;
}

void freetypefont_temp_font_add(const FREETYPE_HEADER_t *path_ttf)
{
    if (path_ttf != NULL)
    {
        font_manager_add_font(path_ttf->path, path_ttf->size);
    }
}

void freetypefont_temp_font_delete(const FREETYPE_HEADER_t *path_ttf)
{
    if (path_ttf != NULL)
    {
        font_manager_remove_font(path_ttf->path, path_ttf->size);
    }
}

const lv_font_t *freetypefont_temp_font_get(const FREETYPE_HEADER_t *path_ttf)
{
    const lv_font_t *ret = NULL;
    if (path_ttf != NULL)
    {
        ret = font_manager_get_font(path_ttf->path, path_ttf->size);
    }
    if (ret == NULL)
    {
        return &lv_font_DINPro_bold_56;
    }
    else
    {
        return ret;
    }
}

#include "../../qwos_app/GUI/Font/QwFont.h"
#include "gui_event_service.h"
#include "thread_pool.h"
#ifdef RT_USING_PM
#include "pm.h"
#endif

bool font_walk_process(int num)
{
    lv_font_glyph_dsc_t glyph_dsc = {0};

    rt_kprintf("[Font] walk begin!\n");
    if (num >= QW_FONT_NUM_MEDIUM_24)
    {
        for (size_t i = 0; i < QW_FONT_NUM_MEDIUM_24; i++)
        {
            g_max_walk_size = 0;
            for (size_t j = 0; j < g_all_text_list_size; j++)
            {
                if (i < QW_FONT_FULL_REGULAR_32)
                {
                    get_ttf_dsc_cb(get_font((QW_FONT_TYPE) i), &glyph_dsc, g_all_text_list[j], g_all_text_list[(j < g_all_text_list_size - 1 ? j + 1 : 0)]);
                }
                else
                {
                    get_ttf_all_font_dsc_cb(get_font((QW_FONT_TYPE) i), &glyph_dsc, g_all_text_list[j], g_all_text_list[(j < g_all_text_list_size - 1 ? j + 1 : 0)]);
                }
            }

            rt_kprintf("[Font] walk finished %d g_max_walk_size:%d\n", i, g_max_walk_size);
        }
    }
    else
    {
        for (size_t j = 0; j < g_all_text_list_size; j++)
        {
            get_ttf_dsc_cb(get_font((QW_FONT_TYPE) num), &glyph_dsc, g_all_text_list[j], g_all_text_list[(j < g_all_text_list_size - 1 ? j + 1 : 0)]);
        }
        rt_kprintf("[Font] walk finished %d g_max_walk_size:%d\n", num, g_max_walk_size);
    }
    return true;
}

static bool font_walk_process_2(const thread_pool_task *task_info)
{
    int num = (int) task_info->arg;

    font_walk_process(num);
    return true;
}

static void font_walk(int argc, char **argv)
{
    if (strcmp(argv[1], "run") == 0)
    {
        thread_pool_add_task(font_walk_process_2, (void *) ((int) QW_FONT_FULL_REGULAR_32), NULL, osPriorityBelowNormal2);
        // submit_gui_event(GUI_FONT_WALK, (int32_t)QW_FONT_FULL_REGULAR_32, NULL);
    }
    else if (strcmp(argv[1], "f") == 0)
    {
        int num = atoi(argv[2]);
        rt_kprintf("[Font] font_walk f:%d cmd!\n", num);
        thread_pool_add_task(font_walk_process_2, (void *) num, NULL, osPriorityBelowNormal2);
        // submit_gui_event(GUI_FONT_WALK, (int32_t)num, NULL);
    }
}

MSH_CMD_EXPORT(font_walk, font_walk);
