{"core.repositoryformatversion": ["1"], "core.filemode": ["false"], "core.symlinks": ["false"], "core.ignorecase": ["true"], "filter.lfs.smudge": ["git-lfs smudge --skip -- %f"], "filter.lfs.process": ["git-lfs filter-process --skip"], "remote.origin.url": ["ssh://yanx<PERSON><PERSON><PERSON>@********:29418/wr02"], "remote.origin.fetch": ["+refs/heads/*:refs/remotes/origin/*"], "manifest.platform": ["auto"], "repo.worktree": ["true"], "repo.existingprojectcount": ["0"], "repo.newprojectcount": ["6"], "extensions.preciousobjects": ["true"], "branch.default.remote": ["origin"], "branch.default.merge": ["refs/heads/develop"], "repo.syncstate.main.synctime": ["2025-07-16T01:55:29.621981+00:00"], "repo.syncstate.main.version": ["1"], "repo.syncstate.sys.argv": ["['E:\\\\Snowa\\\\.repo\\\\repo/main.py', '--repo-dir=E:\\\\Snowa\\\\.repo', '--wrapper-version=2.48', '--wrapper-path=C:\\\\Users\\\\<USER>\\\\bin\\\\repo', '--', 'sync']"], "repo.syncstate.options.jobs": ["12"], "repo.syncstate.options.outermanifest": ["true"], "repo.syncstate.options.jobsnetwork": ["1"], "repo.syncstate.options.jobscheckout": ["8"], "repo.syncstate.options.mpupdate": ["true"], "repo.syncstate.options.clonebundle": ["true"], "repo.syncstate.options.retryfetches": ["0"], "repo.syncstate.options.prune": ["true"], "repo.syncstate.options.repoverify": ["true"], "repo.syncstate.options.quiet": ["false"], "repo.syncstate.options.verbose": ["false"], "repo.syncstate.remote.origin.url": ["ssh://yanx<PERSON><PERSON><PERSON>@********:29418/wr02"], "repo.syncstate.remote.origin.fetch": ["+refs/heads/*:refs/remotes/origin/*"], "repo.syncstate.repo.worktree": ["true"], "repo.syncstate.repo.existingprojectcount": ["0"], "repo.syncstate.repo.newprojectcount": ["6"], "repo.syncstate.branch.default.remote": ["origin"], "repo.syncstate.branch.default.merge": ["refs/heads/develop"]}