<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <remote name="origin" fetch="." review="http://10.0.0.3:8081"/>
  
  <default remote="origin" revision="develop"/>
  
  <project name="app" revision="d82a4057878482e9d29b8d1d2965925c6d69326e" upstream="develop" dest-branch="develop"/>
  <project name="qw_algo/navigation" revision="d930065bbee6d6242f8fb714e6c15b2de1ebb4a6" upstream="cm_develop" dest-branch="cm_develop"/>
  <project name="qw_platform" revision="6163754d711afe3586024cbf14a5994692d70f29" upstream="develop" dest-branch="develop"/>
  <project name="sifli" revision="b0a5bc420a7079f1f3dbd71588d4b8a70ee5d8e1" upstream="develop" dest-branch="develop"/>
  <project name="tools" revision="845db730e5db9901880a9037d88c5d4488f48b5b" upstream="develop" dest-branch="develop"/>
  <project name="vendor" revision="49aa2937fd4577cf36cf466b5c8768a5fc425c74" upstream="develop" dest-branch="develop"/>
</manifest>
