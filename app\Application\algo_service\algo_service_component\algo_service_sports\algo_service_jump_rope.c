﻿/***********************************************************
 * @file algo_service_running_dynamics.c
 * <AUTHOR> (<EMAIL>)
 * @brief  跑步动态算法组件实现
 * @version 0.1
 * @date 2025-01-10
 *
 * @copyright Copyright (c) 2024-2025, Wuhan Qiwu Technology Co., Ltd
 *
***********************************************************/
#include "algo_service_jump_rope.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_task.h"
#include "cfg_header_def.h"
#include "gui_event_service.h"
#include "qw_sensor_common.h"   //TODO: ant输入数据结构放在哪？
#include "qw_time_util.h"
#include "remind_response_app/metronome_remind_app.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"
#include "utility_add.h"

#define JUMP_ROPE_REST_TIP_PERIOD     600                                    // 提醒周期:ms

#define JUMP_ROPE_SPEED_WINDOW_SIZE   7                                      // 瞬时速度窗口周期:s
#define JUMP_ROPE_SPEED_WINDOW_PERIOD (JUMP_ROPE_SPEED_WINDOW_SIZE * 1000)   // 瞬时速度窗口周期:ms

//数据记录的状态
typedef enum {
    jumprope_mode_free = 0,   // 自由状态
    jumprope_mode_count,      // 计数模式
    jumprope_mode_time,       // 计时模式
} jumprope_mode_e;

typedef struct
{
    uint16_t count_last;       // 上次次数
    uint16_t count_in_save;    // 记录时的次数
    uint16_t count_in_pause;   // 暂停中的次数
} algo_jumprope_count_t;

// 输入数据
typedef struct
{
    algo_count_times_sports_drv_pub_t drv_jr;
    algo_jumprope_count_t count;      // 计数
    algo_jumprope_count_t trip;       // 绊绳计数
    algo_jumprope_count_t combo;      // 连跳计数
    uint32_t total_moving_time;       // 移动时间 1000 * s,
    uint32_t total_moving_time_lap;   // 圈移动时间 1000 * s,
    uint32_t wkt_time;                // 训练时间 1000 * s,
    uint16_t wkt_count;               // 训练计数,
    uint16_t count_before_group;      // 之前所有组的次数
    uint16_t trip_before_group;       // 之前所有组的绊绳次数
    uint16_t rest_count;              // 休息tick计数 (1000ms),
    uint8_t sports_type;              // 运动类型
    saving_status_e saving_status;    // 数据记录的状态
    jumprope_mode_e wkt_mode;
    bool in_rest;                     // 是否在休息
} algo_count_times_sports_sub_t;

static algo_count_times_sports_sub_t s_algo_in;

// 发布数据
static algo_count_times_sports_pub_t s_algo_out;

// 中间数据
static uint16_t s_window_cnt[JUMP_ROPE_SPEED_WINDOW_SIZE];   // 瞬时速度窗口内的次数
static int64_t s_window_ms[JUMP_ROPE_SPEED_WINDOW_SIZE];     // 瞬时速度窗口内的ms
static uint8_t s_window_index = 0;                           // 瞬时速度窗口索引（当前输入点）
static uint8_t s_window_valid = 0;                           // 瞬时速度窗口内的次数有效个数

// 本算法打开标记
static bool s_is_open = false;

/**
 * @brief 算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_jump_rope_out_callback(const void *out, uint32_t len)
{
    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_ALGO_JUMPROPE, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("%s publish error", __FUNCTION__);
    }
}

/**
 * @brief 算法处理
 *
 * @param algo_out 输出数据
 * @param algo_in 输入数据
 */
static void algo_jump_rope_deal(algo_count_times_sports_pub_t *algo_out, algo_count_times_sports_sub_t *algo_in)
{
    if (enum_status_saving == algo_in->saving_status)
    {
        algo_out->count = algo_in->count.count_in_save;
        algo_out->combo = algo_in->combo.count_in_save;
        algo_out->trip = algo_in->trip.count_in_save;
        algo_out->trip_group = algo_in->trip.count_in_save - algo_in->trip_before_group;      // 单组绊绳次数 = 总绊绳次数 - 组前绊绳次数
        algo_out->count_group = algo_in->count.count_in_save - algo_in->count_before_group;   // 单组次数 = 总次数 - 组前次数

        if (algo_in->wkt_count > algo_out->count_group)
        {
            algo_out->group_remain_count = algo_in->wkt_count - algo_out->count_group;
        }
        else
        {
            algo_out->group_remain_count = 0;
        }
        if (algo_in->wkt_time > algo_in->total_moving_time_lap)
        {
            algo_out->group_remain_time = algo_in->wkt_time - algo_in->total_moving_time_lap - 1000;
        }
        else
        {
            algo_out->group_remain_time = 0;
        }

        // 全程平均速度 = 总次数 / 记录时间 * 60 (spm)
        if (0 == algo_in->total_moving_time)
        {
            algo_out->avg_speed = 0;
        }
        else
        {
            algo_out->avg_speed = (uint16_t) (algo_in->count.count_in_save * 60 / (algo_in->total_moving_time / 1000));   //
        }

        // 单组平均速度 = 组次数 / 组时间 * 60 (spm)
        if (0 == algo_in->total_moving_time_lap)
        {
            algo_out->avg_speed_group = 0;
        }
        else
        {
            algo_out->avg_speed_group = (uint16_t) (algo_out->count_group * 60 / (algo_in->total_moving_time_lap / 1000));   //
        }
    }
}

/**
 * @brief 算法控制
 *
 * @param algo_out 输出数据
 * @param ctrl_type 控制类型
 */
static void algo_jump_rope_ctrl(algo_count_times_sports_pub_t *algo_out, algo_count_times_sports_sub_t *algo_in, const algo_sports_ctrl_t *sports_ctrl)
{
    if (enum_ctrl_start == sports_ctrl->ctrl_type)
    {
        if (get_auto_record_lap(sports_ctrl->sports_type))
        {
            if (get_auto_record_lap_type(sports_ctrl->sports_type) == AUTO_RECORD_LAP_NUMS)
            {
                algo_in->wkt_mode = jumprope_mode_count;
                algo_in->wkt_count = get_auto_record_lap_value(sports_ctrl->sports_type, AUTO_RECORD_LAP_NUMS);
                algo_in->wkt_time = 0;
            }
            else
            {
                algo_in->wkt_mode = jumprope_mode_time;
                algo_in->wkt_time = get_auto_record_lap_value(sports_ctrl->sports_type, AUTO_RECORD_LAP_TIMES) * 1000;
                algo_in->wkt_count = 0;
            }
            submit_gui_event(GUI_EVT_SCROLL_TO_PAGE, GRAPH_CONST_PAGE_OTHER, NULL);
        }
        else
        {
            algo_in->wkt_mode = jumprope_mode_free;
            algo_in->wkt_time = 0;
            algo_in->wkt_count = 0;
        }

        algo_out->trip = 0;
        algo_out->trip_group = 0;
        algo_out->trip_pre_group = 0;
        algo_out->combo = 0;
        algo_out->max_combo = 0;
        algo_out->count = 0;
        algo_out->count_group = 0;
        algo_out->count_pre_group = 0;
        algo_out->speed = 0;
        algo_out->avg_speed = 0;
        algo_out->max_speed = 0;
        algo_out->avg_speed_group = 0;
        algo_out->avg_speed_pre_group = 0;
        algo_out->group_remain_count = algo_in->wkt_count;
        algo_out->group_remain_time = algo_in->wkt_time;

        memset(&algo_in->count, 0, sizeof(algo_jumprope_count_t));
        memset(&algo_in->trip, 0, sizeof(algo_jumprope_count_t));
        memset(&algo_in->combo, 0, sizeof(algo_jumprope_count_t));
        algo_in->count_before_group = 0;
        algo_in->trip_before_group = 0;
        algo_in->total_moving_time = 0;
        algo_in->total_moving_time_lap = 0;
        algo_in->in_rest = false;
        algo_in->sports_type = sports_ctrl->sports_type;

        memset(&s_window_cnt, 0, sizeof(s_window_cnt));
        memset(&s_window_ms, 0, sizeof(s_window_ms));
        s_window_index = 0;
        s_window_valid = 0;

        //开始之后马上数据发布
        algo_jump_rope_out_callback(algo_out, sizeof(algo_count_times_sports_pub_t));
    }
    else if (enum_ctrl_lap == sports_ctrl->ctrl_type)
    {
        AUTO_RECORD_LAP_TYPE type = get_auto_record_lap_type(get_current_sport_mode());
        uint16_t value = get_auto_record_lap_value(get_current_sport_mode(), type);

        // 自动计圈时间修正
        if (AUTO_RECORD_LAP_NUMS == type && sports_ctrl->lap_type == enum_lap_count)
        {
            algo_in->count.count_in_save = algo_in->count_before_group + value;
        }

        algo_in->in_rest = true;
        algo_in->rest_count = 0;

        submit_gui_event(GUI_EVT_RESET_PAGE, true, NULL);

        //记圈后马上数据发布
        algo_jump_rope_out_callback(algo_out, sizeof(algo_count_times_sports_pub_t));
    }
    else if (enum_ctrl_rest_resume == sports_ctrl->ctrl_type)
    {
        algo_in->count_before_group = algo_in->count.count_in_save;
        algo_in->trip_before_group = algo_in->trip.count_in_save;
        algo_out->trip_pre_group = algo_out->trip_group;
        algo_out->count_pre_group = algo_out->count_group;
        algo_out->avg_speed_pre_group = algo_out->avg_speed_group;

        algo_out->trip_group = 0;
        algo_out->avg_speed_group = 0;
        algo_out->count_group = 0;
        algo_out->group_remain_count = algo_in->wkt_count;
        algo_out->group_remain_time = algo_in->wkt_time;

        algo_in->in_rest = false;

        submit_gui_event(GUI_EVT_RESET_PAGE, false, NULL);

        //记圈后马上数据发布
        algo_jump_rope_out_callback(algo_out, sizeof(algo_count_times_sports_pub_t));
    }
}

static void algo_jump_rope_max_combo(algo_count_times_sports_pub_t *algo_out, algo_count_times_sports_sub_t *algo_in)
{
    static uint32_t s_count_snapshot = 0;
    static int64_t s_last_count_time = 0;

    uint32_t delta_count = 0;
    uint32_t delta_trip = 0;
    uint32_t delta_combo = 0;
    int64_t boot_msec = get_boot_msec();

    if (s_last_count_time == 0)
    {
        s_last_count_time = boot_msec;
        return;
    }

    if (algo_in->drv_jr.count != UINT32_MAX && algo_in->drv_jr.count > algo_in->count.count_last)
    {
        delta_count = algo_in->drv_jr.count - algo_in->count.count_last;

        algo_in->count.count_last = algo_in->drv_jr.count;
    }

    if (algo_in->drv_jr.trip != UINT32_MAX && algo_in->drv_jr.trip > algo_in->trip.count_last)
    {
        delta_trip = algo_in->drv_jr.trip - algo_in->trip.count_last;

        algo_in->trip.count_last = algo_in->drv_jr.trip;

        if (delta_trip > 0)
        {
            // 绊绳了 连跳清空
            algo_in->combo.count_last = 0;
            algo_in->combo.count_in_save = 0;
            algo_in->combo.count_in_pause = 0;
        }
    }

    if (algo_in->drv_jr.combo != UINT32_MAX && algo_in->drv_jr.combo > algo_in->combo.count_last)
    {
        delta_combo = algo_in->drv_jr.combo - algo_in->combo.count_last;

        algo_in->combo.count_last = algo_in->drv_jr.combo;
    }

    if (enum_status_saving == algo_in->saving_status && !algo_in->in_rest)
    {
        algo_in->count.count_in_save += delta_count;
        algo_in->trip.count_in_save += delta_trip;
        algo_in->combo.count_in_save += delta_combo;

        if (algo_out->max_combo < algo_in->combo.count_in_save)
        {
            algo_out->max_combo = algo_in->combo.count_in_save;
        }
    }
    else
    {
        algo_in->count.count_in_pause += delta_count;
        algo_in->trip.count_in_pause += delta_trip;
        algo_in->combo.count_in_pause += delta_combo;
    }

    if (algo_in->drv_jr.cadence != UINT16_MAX)
    {
        algo_out->speed = algo_in->drv_jr.cadence;
    }
    else
    {
        // 算法没有输出速度(频率)，则使用窗口计算

        if (JUMP_ROPE_SPEED_WINDOW_SIZE > s_window_valid)   // 窗口未满
        {
            s_window_cnt[s_window_index] = algo_in->count.count_in_save;
            s_window_ms[s_window_index] = boot_msec;
            s_window_index = QW_RING_ADD(s_window_index, 1, JUMP_ROPE_SPEED_WINDOW_SIZE);
            s_window_valid++;
        }
        else if (boot_msec > s_window_ms[QW_RING_ADD(s_window_index, 1, JUMP_ROPE_SPEED_WINDOW_SIZE)])   // 窗口已满且时间差不为0
        {
            // algo_out->speed = (uint16_t)(((int64_t)algo_in->count.count_in_save - s_count_snapshot) * 60000 / (boot_msec - s_last_count_time));
            algo_out->speed = (uint16_t) (((int64_t) algo_in->count.count_in_save - s_window_cnt[s_window_index]) * 60000
                                        / (boot_msec - s_window_ms[QW_RING_ADD(s_window_index, 1, JUMP_ROPE_SPEED_WINDOW_SIZE)]));

            if (enum_status_saving == algo_in->saving_status && !algo_in->in_rest)
            {
                if (algo_out->max_speed < algo_out->speed)
                {
                    algo_out->max_speed = algo_out->speed;
                }
            }

            s_last_count_time = boot_msec;
            s_count_snapshot = algo_in->count.count_in_save;
            s_window_cnt[s_window_index] = algo_in->count.count_in_save;
            s_window_ms[s_window_index] = boot_msec;
            s_window_index = QW_RING_ADD(s_window_index, 1, JUMP_ROPE_SPEED_WINDOW_SIZE);
        }
    }
}

/**
 * @brief 算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_count_times_drv_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_COUNT_TIMES;
    head.input_type = DATA_ID_EVENT_COUNTTIMES_DRV;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_timer_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_COUNT_TIMES;
    head.input_type = DATA_ID_ALGO_TIMER;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_sports_ctrl_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_COUNT_TIMES;
    head.input_type = DATA_ID_EVENT_SPORTS_CTRL;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_COUNTTIMES_DRV,
        .topic_id = DATA_ID_EVENT_COUNTTIMES_DRV,
        .callback = algo_count_times_drv_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_timer",
        .topic_id = DATA_ID_ALGO_TIMER,
        .callback = algo_timer_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
};

/**
 * @brief 算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_jump_rope_init(void)
{
    return 0;
}

/**
 * @brief 打开算法
 *
 * @return int32_t 结果
 */
static int32_t algo_jump_rope_open(void)
{
    if (s_is_open)
    {
        ALGO_COMP_LOG_W("%s already open", __FUNCTION__);
        return 0;
    }

    ALGO_COMP_LOG_D("%s open", __FUNCTION__);

    s_is_open = true;
    optional_config_t config = {.sampling_rate = 0};
    memset(&s_algo_in, 0xff, sizeof(algo_count_times_sports_sub_t));
    memset(&s_algo_out, 0xff, sizeof(algo_count_times_sports_pub_t));
    memset(&s_window_cnt, 0, sizeof(s_window_cnt));
    memset(&s_window_ms, 0, sizeof(s_window_ms));
    s_window_index = 0;
    s_window_valid = 0;

    return algo_topic_list_subscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
}

/**
 * @brief feed算法
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_jump_rope_feed(uint32_t input_type, void *data, uint32_t len)
{
    algo_count_times_sports_sub_t *algo_in = &s_algo_in;
    algo_count_times_sports_pub_t *algo_out = &s_algo_out;

    switch (input_type)
    {
    case DATA_ID_EVENT_COUNTTIMES_DRV:
    {
        const algo_count_times_sports_drv_pub_t *jr_data = (algo_count_times_sports_drv_pub_t *) data;
        memcpy(&algo_in->drv_jr, jr_data, sizeof(algo_count_times_sports_drv_pub_t));

        algo_jump_rope_max_combo(algo_out, algo_in);
    }
    break;
    case DATA_ID_ALGO_TIMER:
    {
        const algo_timer_pub_t *timer_data = (algo_timer_pub_t *) data;
        algo_in->total_moving_time = timer_data->timer_total.moving_time;
        algo_in->total_moving_time_lap = timer_data->timer_lap.moving_time;
    }
    break;
    case DATA_ID_EVENT_SPORTS_CTRL:
    {
        const algo_sports_ctrl_t *sports_ctrl = (algo_sports_ctrl_t *) data;
        algo_in->saving_status = sports_ctrl->saving_status;

        algo_jump_rope_ctrl(algo_out, algo_in, sports_ctrl);

        //数据发布
        if (sports_ctrl->ctrl_type == enum_ctrl_null)
        {
            //算法处理
            algo_jump_rope_deal(algo_out, algo_in);

            //数据发布
            algo_jump_rope_out_callback(algo_out, sizeof(algo_count_times_sports_pub_t));

            if (algo_in->in_rest)
            {
                algo_in->rest_count++;
                if (algo_in->rest_count > JUMP_ROPE_REST_TIP_PERIOD)
                {
                    algo_in->rest_count = 0;
                    remind_trigger(enumPOPUP_SPORTING_LAP, true);
                }
            }
        }
    }
    break;

    default:
        break;
    }
    return 0;
}

/**
 * @brief 关闭算法
 *
 * @return int32_t 结果
 */
static int32_t algo_jump_rope_close(void)
{
    if (!s_is_open)
    {
        ALGO_COMP_LOG_W("%s already close", __FUNCTION__);
        return 0;
    }

    algo_topic_list_unsubscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));

    s_is_open = false;
    ALGO_COMP_LOG_D("%s close", __FUNCTION__);
    return 0;
}

// 算法组件实现
static algo_compent_ops_t s_jump_rope_algo = {
    .init = algo_jump_rope_init,
    .open = algo_jump_rope_open,
    .feed = algo_jump_rope_feed,
    .close = algo_jump_rope_close,
    .ioctl = NULL,
};

/**
 * @brief 组件注册
 *
 * @return int32_t 结果
 */
int32_t register_jump_rope_algo(void)
{
    algo_compnent_register(ALGO_TYPE_COUNT_TIMES, &s_jump_rope_algo);
    return 0;
}