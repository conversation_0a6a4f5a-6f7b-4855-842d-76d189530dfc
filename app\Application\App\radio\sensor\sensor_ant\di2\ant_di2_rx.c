/***************************************Copyright (c)****************************************/
//                              <PERSON>han <PERSON> Technology Co., Ltd
//
//---------------------------------------File Info--------------------------------------------
// File name         : ant_di2_rx.c
// Created by        : jiangzhen
// Descriptions      : ANT DI2模块.c文件
//--------------------------------------------------------------------------------------------
// History           :
// 2019-02-27        :原始版本
/*********************************************************************************************/

#include "app_error.h"
#include "ant_di2.h"
#include "ant_di2_rx.h"
#include "nrf_sdh_ant.h"
#include "nrf_sdh_soc.h"
#include "ant_parameters.h"
#include "ant_interface.h"
#include "basictype.h"

#include "qw_sensor_common.h"
#include "qw_sensor_data.h"
#include "sensor_ant_common.h"
#include "cfg_header_def.h"

#if ANT_SENSOR_DI2_ENABLED

#define ANT_DI2_SWITCH_NOTIFY_NONE		UINT8_MAX

//--------------------------------------函数申明-------------------------------------------//
static void ant_di2_rx_evt_handler(ant_di2_profile_t * p_profile, ant_di2_evt_t event);
static void di2_ant_evt(ant_evt_t *p_ant_evt, void * p_context);

//Di2码表状态初始化
static void di2_slave_status_update(ant_di2_evt_t event);

//Di2码表状态更新
static void di2_slave_status_init(void);

//--------------------------------------变量定义-------------------------------------------//
DI2_DISP_PROFILE_CONFIG_DEF(m_ant_di2, ant_di2_rx_evt_handler);
static ant_di2_profile_t m_ant_di2;

//bit6: Max num of spds in front, bit7: Max num of spds in rear.
//bit17 - 20: Switch info of D-FLY Ch1 - 4, bit21: Avail functions of bike system.
static uint32_t s_di2_slave_status = 0xffc1ff3f; //80-3F-FF-C1-FF-FF-FF-FF

//-------------------------------------------------------------------------------------------
// Function Name : LoadChnConf_di2_rx
// Purpose       : 加载ANT DI2接收通道默认配置
// Param[in]     : ant_channel_config_t  *p_channel_config
// Param[out]    : None
// Return type   : static
// Comment       : 2019-02-27
//-------------------------------------------------------------------------------------------
static void LoadChnConf_di2_rx(ant_channel_config_t  *p_channel_config)
{
    p_channel_config->channel_number    = sensor_ant_channel_num_get(SENSOR_TYPE_DI2);
    p_channel_config->channel_type      = CHANNEL_TYPE_SLAVE;       //ant认证要求使用0x00类型 BSC_DISP_CHANNEL_TYPE;
    p_channel_config->ext_assign        = DI2_EXT_ASSIGN;
    p_channel_config->rf_freq           = DI2_ANTPLUS_RF_FREQ;      ///< Frequency, decimal 57 (2457 MHz).
    p_channel_config->transmission_type = CHAN_ID_TRANS_TYPE;
    p_channel_config->device_type       = DI2_DEVICE_TYPE;
    p_channel_config->channel_period    = DI2_MSG_PERIOD;           //8086
    p_channel_config->network_number    = DI2_ANTPLUS_NETWORK_NUM;
}

//-------------------------------------------------------------------------------------------
// Function Name : ant_di2_rx_evt_handler
// Purpose       : ANT DI2接收通道中断处理函数
// Param[in]     : ant_di2_profile_t * p_profile
//                 ant_di2_evt_t event
// Param[out]    : None
// Return type   : static
// Comment       : 2019-02-27
//-------------------------------------------------------------------------------------------
static void ant_di2_rx_evt_handler(ant_di2_profile_t * p_profile, ant_di2_evt_t event)
{
    sensor_search_infor_t       sensor_search_infor;
    // sensor_connect_infor_t      *p_sensor_connect       = sensor_connect_infor_get(SENSOR_TYPE_DI2);
    sensor_connect_infor_t      sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_DI2, &sensor_connect);
    sensor_module_evt_handler   evt_handler             	= sensor_module_evt_handler_get();
    sensor_saved_work_t         *p_sensor_saved         	= NULL;
    di2_ch_config_t* p_di2_ch_config 							= NULL;
    sensor_work_state_t         sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    sensor_module_param_input_t *p_param                = sensor_module_param_input_get();
    sensor_original_data_t      *p_sensor_original_data = sensor_original_data_get();
    int8_t                      index                   							= -1;
    //static uint8_t              low_power_indicate_flag 		= FALSE;
    static uint8_t di2_ch_sequence[DI2_DFLY_CH_NUM]       		= {0xFF, 0xFF, 0xFF, 0xFF};
    uint8_t     event_ch_num         										= ANT_DI2_SWITCH_NOTIFY_NONE;
    di2_config_func_t           di2_config_func         		= DI2_FUNC_UNDEFINE;

    sensor_ant_leave_rx_search(SENSOR_TYPE_DI2);

    switch (event)
    {
        case ANT_DI2_PAGE_1_UPDATED:
            //数据显示
            p_sensor_original_data->di2Data.di2_battery_percent = p_profile->page_1.battery_percent;
            p_sensor_original_data->di2Data.di2_front_gear = p_profile->page_1.front_gear;
            p_sensor_original_data->di2Data.di2_rear_gear = p_profile->page_1.rear_gear;
            // if (p_sensor_original_data->di2Data.di2_system_status != p_profile->page_1.system_status)
            // {
                // rt_kprintf("di2_system_status = %d\n", p_profile->page_1.system_status);
            // }
            p_sensor_original_data->di2Data.di2_system_status = p_profile->page_1.system_status;


            p_sensor_original_data->battery_list.di2 = p_profile->page_1.battery_percent;

            memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
            memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
            sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
            sensor_search_infor.sensor_type = SENSOR_TYPE_DI2;
            // sensor_saved_work_infor_get(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state);

            if (SENSOR_CONNECT_STATE_CONNECTING == sensor_connect.state)
            {
                sensor_connect.state = SENSOR_CONNECT_STATE_CONNECTED;
                sensor_connect_infor_set(SENSOR_TYPE_DI2, &sensor_connect);
                if (sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
                {
                    if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
                    {
                        p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                        p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
                        cfg_mark_update(enum_cfg_ant_ble_dev);
                    }
                    else if (SENSOR_WORK_STATE_FORBIDDEN == sensor_work_state)
                    {
                        sensor_infor_t sensor_infor = {0};
                        sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                        sensor_disconnect(&sensor_infor);
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }
                else
                {
                    if(sensor_disconnect_item_check(&sensor_search_infor))
                    {
                        sensor_disconnect_info_remove(&sensor_search_infor);
                        sensor_infor_t sensor_infor = {0};
                        sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                        sensor_disconnect(&sensor_infor);
                        return;
                    }
                    sensor_saved_work_infor_add(&sensor_search_infor);
                    sensor_search_infor_del(&sensor_search_infor);
                }

                if (NULL != evt_handler)
                {
                    evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, TRUE);
                    evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, 0);
                    evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, TRUE);
                }

                //low_power_indicate_flag = TRUE;
                di2_slave_status_init();
            }
            else if (SENSOR_CONNECT_STATE_CONNECTED == sensor_connect.state)
            {
                if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
                {
                    if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
                    {
                        p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                        p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }
            }

            if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
            {
            #if SENSOR_DEVICE_INFO_ENABLED
//                p_sensor_saved->sensor_manufacturer[index].manufacturer_ant = p_profile->page_2.manuf_id;
//                p_sensor_saved->sensor_serial      [index].serial_ant       = p_profile->page_2.serial_num;
//                p_sensor_saved->sensor_hw_version  [index].version_ant      = p_profile->page_3.hw_version;
//                p_sensor_saved->sensor_model       [index].model_ant        = p_profile->page_3.model_num;
//                p_sensor_saved->sensor_sw_version  [index].version_ant      = p_profile->page_3.sw_version;
            #endif
            }
            break;

        case ANT_DI2_PAGE_17_UPDATED:
            p_sensor_original_data->di2Data.di2_front_gear_max = p_profile->page_17.front_gear_max;
            p_sensor_original_data->di2Data.di2_rear_gear_max = p_profile->page_17.rear_gear_max;
            break;

        case ANT_DI2_PAGE_4_UPDATED:
            memcpy(&p_sensor_original_data->di2Data.di2_d_fly_swith_notify, &p_profile->page_4, sizeof(ant_di2_page4_data_t));
            for (uint8_t i = 0; i < DI2_DFLY_CH_NUM; i++)
            {
                if (di2_ch_sequence[i] != p_sensor_original_data->di2Data.di2_d_fly_swith_notify.ch_sequence[i])
                {
                	di2_ch_sequence[i] = p_sensor_original_data->di2Data.di2_d_fly_swith_notify.ch_sequence[i];
                    event_ch_num = i;
                    break;
                }
            }
            break;

        case ANT_DI2_PAGE_6_UPDATED:
        case ANT_DI2_PAGE_7_UPDATED:
        case ANT_DI2_PAGE_8_UPDATED:
        case ANT_DI2_PAGE_9_UPDATED:
            memcpy(&p_sensor_original_data->di2Data.di2_d_fly_ch[event - ANT_DI2_PAGE_6_UPDATED], &p_profile->page_6[event - ANT_DI2_PAGE_6_UPDATED], sizeof(ant_di2_page6_data_t));
            break;

        case ANT_DI2_PAGE_10_UPDATED:
            memcpy(&p_sensor_original_data->di2Data.di2_avail_functions, &p_profile->page_10, sizeof(ant_di2_page10_data_t));
            break;

        default:
            break;
    }

    di2_slave_status_update(event);

    if (DI2_DFLY_CH_NUM > event_ch_num &&
    		(di2_ch_cmd_single == p_sensor_original_data->di2Data.di2_d_fly_swith_notify.ch_command[event_ch_num] ||
			 di2_ch_cmd_long == p_sensor_original_data->di2Data.di2_d_fly_swith_notify.ch_command[event_ch_num] ||
			 di2_ch_cmd_double == p_sensor_original_data->di2Data.di2_d_fly_swith_notify.ch_command[event_ch_num]))
    {
        memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
        memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
        sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
        sensor_search_infor.sensor_type = SENSOR_TYPE_DI2;
        sensor_saved_work_infor_get_read(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state);

        if (SENSOR_WORK_STATE_IDLE == sensor_work_state)
        {
            memset ((uint8_t *)&p_sensor_original_data->di2Data.di2_d_fly_swith_notify.ch_command[event_ch_num], 0xF, sizeof(di2_ch_cmd_e));
            return;
        }

        p_di2_ch_config = (di2_ch_config_t*)p_sensor_saved->sensor_saved_infor->sensor_infor[index].ant_config;

        if (di2_ch_cmd_long == p_sensor_original_data->di2Data.di2_d_fly_swith_notify.ch_command[event_ch_num])
		{
			di2_config_func =  p_di2_ch_config->ch_btn[event_ch_num].long_click;
		}
		else if (di2_ch_cmd_single == p_sensor_original_data->di2Data.di2_d_fly_swith_notify.ch_command[event_ch_num])
		{
			di2_config_func =  p_di2_ch_config->ch_btn[event_ch_num].single_click;
		}
		else
		{
			di2_config_func =  p_di2_ch_config->ch_btn[event_ch_num].double_click;
		}

        if (DI2_FUNC_INVALID != di2_config_func &&  DI2_FUNC_UNDEFINE != di2_config_func)
        {
            if (NULL != evt_handler)
            {
                evt_handler(EVNET_SENOSR_DI2_CTRL, &sensor_search_infor.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, (int16_t)di2_config_func);
            }
        }

        memset ((uint8_t *)&p_sensor_original_data->di2Data.di2_d_fly_swith_notify.ch_command[event_ch_num], 0xF, sizeof(di2_ch_cmd_e));
    }
}

//-------------------------------------------------------------------------------------------
// Function Name : di2_ant_evt
// Purpose       : DI2电变 ANT事件处理函数
// Param[in]     : ant_evt_t *p_ant_evt
//                 void * p_context
// Param[out]    : None
// Return type   : static
// Comment       : 2020-04-18
//-------------------------------------------------------------------------------------------
static void di2_ant_evt(ant_evt_t *p_ant_evt, void * p_context)
{
    sensor_search_infor_t     sensor_search_infor;
    // sensor_connect_infor_t    *p_sensor_connect       = sensor_connect_infor_get(SENSOR_TYPE_DI2);
    sensor_connect_infor_t    sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_DI2, &sensor_connect);
    sensor_module_evt_handler evt_handler             = sensor_module_evt_handler_get();
    sensor_saved_work_t       *p_sensor_saved         = NULL;
    sensor_original_data_t    *p_sensor_original_data = sensor_original_data_get();
    sensor_work_state_t       sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    //uint8_t                   channelstate            = 0;
    int8_t                    index                   = -1;
    ret_code_t                err_code                = NRF_SUCCESS;

    sensor_systime_update();

    ant_di2_disp_evt_handler(p_ant_evt, p_context);

    if (p_ant_evt->channel == m_ant_di2.channel_number)
    {
        switch (p_ant_evt->event)
        {
            case EVENT_CHANNEL_CLOSED:
                //更新显示信息
                memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
                memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
                sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
                sensor_search_infor.sensor_type = SENSOR_TYPE_DI2;

            	sensor_ant_leave_rx_search(SENSOR_TYPE_DI2);

                err_code = sd_ant_channel_unassign(m_ant_di2.channel_number);
                APP_ERROR_CHECK(err_code);
                m_ant_di2.channel_number = 0;
                sensor_ant_channel_num_unassign(SENSOR_TYPE_DI2);

                bool forbidden_mask = sensor_connect_infor_get_forbidden_mask(SENSOR_TYPE_DI2);
                if(sensor_connect_infor_get(SENSOR_TYPE_DI2, &sensor_connect))
                {
                    sensor_connect_infor_clear(SENSOR_TYPE_DI2);
                    if (NULL != evt_handler && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)
                    {
                        evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                    }
                }

                if (sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
                {
                    if (SENSOR_WORK_STATE_IDLE != sensor_work_state)
                    {
                        p_sensor_saved->rssi             [index] = 0;
                        p_sensor_saved->battery_voltage  [index] = 0xff;
                        p_sensor_saved->sensor_work_state[index] = SENSOR_WORK_STATE_SAVED;
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }

                if((!forbidden_mask && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)       //连接完成后异常断连
                    || (forbidden_mask && sensor_connect.state == SENSOR_CONNECT_STATE_CLOSE_WAIT)
                    || sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)                      //连接超时
                {
                    // connected状态下断连，检索saved数组是否有同类型sensor并进行连接
                    sensor_connect_from_saved_info(sensor_search_infor.sensor_type);
                }

                if (NULL != evt_handler)
                {
                    evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                    evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                }

                di2_slave_status_init();
                p_sensor_original_data->battery_list.di2 = 0;
                p_sensor_original_data->di2Data.di2_system_status = 0xFF;
                break;
            case EVENT_RX_FAIL_GO_TO_SEARCH:
                // sd_ant_channel_close(DI2_CHANNEL_NUMBER);
                // sensor_ant_close(SENSOR_TYPE_DI2);
            	sensor_ant_enter_rx_search(SENSOR_TYPE_DI2);
                break;
            case EVENT_RX_SEARCH_TIMEOUT:
            default:
                break;
        }
    }
}

NRF_SDH_ANT_OBSERVER(m_di2_ant_observer, ANT_BSC_ANT_OBSERVER_PRIO, di2_ant_evt, &m_ant_di2);

/**
 * @*********************************************************************************************
 * @description: 设置di2通道
 * @param {ant_id_t} *id
 * @return {*}
 * @*********************************************************************************************
 */
void ant_di2_rx_profile_setup(ant_id_t *id)
{
    // sensor_connect_infor_t *p_sensor_connect = sensor_connect_infor_get(SENSOR_TYPE_DI2);
    sensor_connect_infor_t    sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_DI2, &sensor_connect);
    ret_code_t             err_code          = NRF_SUCCESS;
    ant_channel_config_t channel_config;

    memcpy ((uint8_t *)&sensor_connect.sensor_id.ant_id, (uint8_t *)id, sizeof(ant_id_t));
    sensor_connect_infor_set(SENSOR_TYPE_DI2, &sensor_connect);

    /*
    //device num的组成
    //1byte   1byte    |     1byte      |      1byte                  从左到右高到低
    //   device id     | device type    |MSN:extended device number LSN:Transmission Type
    */
    uint16_t sensor_id = (uint16_t)id->id;
    uint8_t trans_type = CHAN_ID_TRANS_TYPE;
    if (id->id > 0xffff)
    {
        trans_type = id->trans_type;
    }

    //加载参数
    LoadChnConf_di2_rx(&channel_config);
    channel_config.device_number     = sensor_id;
    channel_config.transmission_type = trans_type;

    err_code = ant_di2_disp_init(&m_ant_di2,
                                 (const ant_channel_config_t *)&channel_config,
                                 DI2_DISP_PROFILE_CONFIG(m_ant_di2));
    APP_ERROR_CHECK(err_code);
}

/**
 * @*********************************************************************************************
 * @description: 开启DI2通道
 * @param {*}
 * @return {*}
 * @*********************************************************************************************
 */
void ant_di2_rx_open(void)
{
    ret_code_t             err_code          = NRF_SUCCESS;

    err_code = ant_di2_disp_open(&m_ant_di2);
    APP_ERROR_CHECK(err_code);
}

/*****************************************************************************
 * Function      : ant_di2_slave_status_notify
 * Description   : Send Di2 slave status notification data.
 * Input         : None
 * Output        : None
 * Return        :
 * Others        :
 * Record
 * 1.Date        : 20220211
 *   Author      : Andy
 *   Modification: Created function

*****************************************************************************/
ret_code_t ant_di2_slave_status_notify(uint32_t di2_slave_status)
{
    ant_di2_page128_data_t page_128;
    ant_di2_profile_t *p_profile = &m_ant_di2;

    page_128.slave_status_byte1 = di2_slave_status & 0xff;
    page_128.slave_status_byte2 = (di2_slave_status >> 8) & 0xff;
    page_128.slave_status_byte3 = (di2_slave_status >> 16) & 0xff;
    page_128.slave_status_byte4 = (di2_slave_status >> 24) & 0xff;

    uint32_t err_code = ant_di2_slave_status_notify_tx(p_profile, &page_128);
    return err_code;
}

/*****************************************************************************
 * Function      : ant_di2_shift_mode_trans
 * Description   : Send request of the shifting mode transition.
 * Input         : None
 * Output        : None
 * Return        :
 * Others        :
 * Record
 * 1.Date        : 20220211
 *   Author      : Andy
 *   Modification: Created function

*****************************************************************************/
ret_code_t ant_di2_shift_mode_trans(void)
{
    ant_di2_page48_data_t page_48;
    ant_di2_profile_t *p_profile = &m_ant_di2;

    page_48.sequence_num = 0x0f;
    page_48.sequence_support = 1;

    uint32_t err_code = ant_di2_shift_mode_trans_tx(p_profile, &page_48);
    return err_code;
}

//Di2更新任务
void ant_di2_data_update(uint32_t runtime_ms)
{
    static uint32_t last_time = 0;

    if(!sensor_connect_infor_get_state(SENSOR_TYPE_DI2, SENSOR_CONNECT_STATE_CONNECTED))
    {
        return;
    }

    if(0 == last_time)
    {
        last_time = runtime_ms;
    }
    else if(0xffffffff != s_di2_slave_status && 9000 <= runtime_ms - last_time)
    {
        ant_di2_slave_status_notify(s_di2_slave_status);
        last_time = runtime_ms;
    }
}

uint8_t ant_di2_btn_get_channel_num()
{
    if(!sensor_connect_infor_get_state(SENSOR_TYPE_DI2, SENSOR_CONNECT_STATE_CONNECTED))
    {
        return 0;
    }

    ant_di2_profile_t *p_profile = &m_ant_di2;
    uint8_t cnt = 0;

    for (uint8_t i = 0; i < DI2_DFLY_CH_NUM; i++)
    {
    	if (0 < (p_profile->page_6[i].switch1_left_right & 0x03))
    	{
    		cnt++;
    	}
    }

    return cnt;
}

uint8_t ant_di2_btn_get_channel_index(uint8_t index)
{
    if(!sensor_connect_infor_get_state(SENSOR_TYPE_DI2, SENSOR_CONNECT_STATE_CONNECTED))
    {
        return UINT8_MAX;
    }

    ant_di2_profile_t *p_profile = &m_ant_di2;
    uint8_t cnt = 0;

	if (DI2_DFLY_CH_NUM > index)
	{
	    for (uint8_t i = 0; i < DI2_DFLY_CH_NUM; i++)
	    {
	    	if (0 < (p_profile->page_6[i].switch1_left_right & 0x03))
	    	{
	    		if (cnt == index)
	    		{
		    		return i;
	    		}
	    		cnt++;
	    	}
	    }
	}

	return UINT8_MAX;
}

void ant_di2_btn_get_channel_info(uint8_t channel, uint8_t* left_right, di2_switch_type_e* type, di2_switch_assign_e* assign)
{
    if(!sensor_connect_infor_get_state(SENSOR_TYPE_DI2, SENSOR_CONNECT_STATE_CONNECTED))
    {
        return;
    }

    if (DI2_DFLY_CH_NUM > channel)
    {
	    ant_di2_profile_t *p_profile = &m_ant_di2;

	    *left_right = p_profile->page_6[channel].switch1_left_right;
	    *type = p_profile->page_6[channel].switch1_type;
	    *assign = p_profile->page_6[channel].switch1_assign;
    }
}

//Di2码表状态更新
static void di2_slave_status_update(ant_di2_evt_t event)
{
    switch(event)
    {
        case ANT_DI2_PAGE_17_UPDATED:
            s_di2_slave_status |= 3 << 6;
            break;
        case ANT_DI2_PAGE_6_UPDATED:
            s_di2_slave_status |= 1 << 17;
            break;
        case ANT_DI2_PAGE_7_UPDATED:
            s_di2_slave_status |= 1 << 18;
            break;
        case ANT_DI2_PAGE_8_UPDATED:
            s_di2_slave_status |= 1 << 19;
            break;
        case ANT_DI2_PAGE_9_UPDATED:
            s_di2_slave_status |= 1 << 20;
            break;
        case ANT_DI2_PAGE_10_UPDATED:
            s_di2_slave_status |= 1 << 21;
            break;
        default:
            break;
    }
}

//Di2码表状态初始化
static void di2_slave_status_init(void)
{
    s_di2_slave_status = 0xffc1ff3f; //80-3F-FF-C1-FF-FF-FF-FF
}

#endif //ANT_SENSOR_DI2_ENABLED
