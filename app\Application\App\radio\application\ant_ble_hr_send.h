/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ant_ble_hr_send.h
@Time    :   2025/01/09 14:33:41
<AUTHOR>   txy
*
**************************************************************************/

#ifndef __ANT_BLE_HR_SEND_H__
#define __ANT_BLE_HR_SEND_H__

#include "stdint.h"
#include "stdbool.h"
#include "string.h"

#ifdef __cplusplus
extern "C"
{
#endif

/************************************************************************
 *@function:uint32_t ant_ble_hr_open(uint8_t mode);
 *@brief:开启心率推送功能，支持ANT和BLE两种推送方式
 *@param: mode - 推送模式：0=ANT和BLE都推送, 1=只推送ANT, 其他=只使用BLE发送
 *              注意：BLE是必须功能，ANT是可选功能
 *@return: 0 - 成功，0xff - 已经开启
*************************************************************************/
uint32_t ant_ble_hr_open(uint8_t mode);

/************************************************************************
 *@function:uint32_t ant_ble_hr_open(void);
 *@brief:关闭心率推送
 *@param: 0 - 成功，-1 - 订阅失败
 *@return:null
*************************************************************************/
uint32_t ant_ble_hr_close(void);

/************************************************************************
 *@function: get_push_hr_value
 *@brief: 获取当前心率值，供ANT模块使用
 *@param: none
 *@return: 当前心率值
 *************************************************************************/
uint8_t get_push_hr_value(void);

/************************************************************************
 *@function: update_hr_push_value
 *@brief: 更新心率推送值，统一管理心率数据并调用原有的notify_hr_push_value
 *@param: value - 心率值
 *@return: none
 *************************************************************************/
void update_hr_push_value(uint8_t value);

/************************************************************************
 *@function: get_hr_tx_status
 *@brief: 获取心率推送状态
 *@param: none
 *@return: false - 未开启模拟推送，true - 已开启模拟推送
 *************************************************************************/
bool get_hr_tx_status(void);

/************************************************************************
 *@function: sim_hr_tx_status_set,暂时不生效.
 *@brief: 设置模拟心率推送标志状态（通过宏控制）
 *@param: flag - false: 不开启模拟推送，true: 开启模拟推送
 *@return: false - 设置失败，true - 设置成功
 *************************************************************************/
bool sim_hr_tx_status_set(bool flag);

bool sim_hr_tx_status_get(void);

#ifdef __cplusplus
}
#endif

#endif
