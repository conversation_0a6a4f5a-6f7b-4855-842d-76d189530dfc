﻿/**
 * @file algo_service_lap_count.c
 * <AUTHOR> (<EMAIL>)
 * @brief 圈数算法组件实现
 * @version 0.1
 * @date 2024-11-28
 *
 * @copyright Copyright (c) 2024-2025, <PERSON>han <PERSON>wu Technology Co., Ltd
 *
 */
#include "algo_service_sport_status.h"
#include "../backlight_module/backlight_module.h"
#include "../focus_mode_srv/focus_mode_srv.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_task.h"
#include "remind_response_app/metronome_remind_app.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"

// 发布数据
static saving_status_e last_sport_status = 0;
static bool s_move_status = false;

// 本算法打开标记
static bool s_is_open = false;

/**
 * @brief 算法控制订阅处理
 *
 * @param in 控制数据
 * @param len 数据长度
 */
static void algo_sports_ctrl_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    if (in == NULL)
    {
        ALGO_COMP_LOG_E("%s status_callback error", __FUNCTION__);
        return;
    }

    const algo_sports_ctrl_t *p_ctrl = (const algo_sports_ctrl_t *) in;

    if (p_ctrl->ctrl_type == enum_ctrl_start && p_ctrl->saving_status == enum_status_saving)
    {
        if (last_sport_status < enum_status_saving)
        {
            ALGO_COMP_LOG_I("%s sport_status:%d >>>>>> %d \n", __FUNCTION__, last_sport_status, p_ctrl->saving_status);
            last_sport_status = enum_status_saving;
            remind_trigger(RRT_S_START, true);  // 发送开始提醒

            metronome_remind_response_sport_status(p_ctrl->saving_status);

            // 从任意状态切到free，从free切到任意状态
            sleep_sumit_work(SLEEP_OPS_SPORT, (void*)&p_ctrl->saving_status);
            dnd_sumit_work(DND_OPS_SPORT, (void*)&p_ctrl->saving_status);
        }
    }
    else if (p_ctrl->ctrl_type == enum_ctrl_stop && p_ctrl->saving_status == enum_status_free)
    {
        if (last_sport_status >= enum_status_saving)
        {
            ALGO_COMP_LOG_I("%s sport_status:%d >>>>>> %d \n", __FUNCTION__, last_sport_status, p_ctrl->saving_status);
            last_sport_status = enum_status_free;

            metronome_remind_response_sport_status(p_ctrl->saving_status);

            // 从任意状态切到free，从free切到任意状态
            sleep_sumit_work(SLEEP_OPS_SPORT, (void*)&p_ctrl->saving_status);
            dnd_sumit_work(DND_OPS_SPORT, (void*)&p_ctrl->saving_status);
        }
    }
    else if (p_ctrl->ctrl_type == enum_ctrl_pause_auto && p_ctrl->saving_status == enum_status_pause_auto)
    {
        if (last_sport_status == enum_status_saving)
        {
            ALGO_COMP_LOG_I("%s sport_status:%d >>>>>> %d \n", __FUNCTION__, last_sport_status, p_ctrl->saving_status);
            last_sport_status = enum_status_pause_auto;
            remind_trigger(RRT_S_AUTO_PAUSE, true); // 自动暂停提醒
            
            metronome_remind_response_sport_status(p_ctrl->saving_status);
        }
    }
    else if (p_ctrl->ctrl_type == enum_ctrl_pause && p_ctrl->saving_status == enum_status_pause_manul)
    {
        if (last_sport_status == enum_status_saving || last_sport_status == enum_status_pause_auto)
        {
            ALGO_COMP_LOG_I("%s sport_status:%d >>>>>> %d \n", __FUNCTION__, last_sport_status, p_ctrl->saving_status);
            last_sport_status = enum_status_pause_manul;
            remind_trigger(RRT_S_MANUAL_PAUSE, true);        // 手动暂停提醒
            
            metronome_remind_response_sport_status(p_ctrl->saving_status);
        }
    }
    else if (p_ctrl->ctrl_type == enum_ctrl_resume && p_ctrl->saving_status == enum_status_saving)
    {
        if (last_sport_status >= enum_status_pause_auto)
        {
            ALGO_COMP_LOG_I("%s sport_status:%d >>>>>> %d \n", __FUNCTION__, last_sport_status, p_ctrl->saving_status);
            last_sport_status = enum_status_saving;
            remind_trigger(RRT_S_RESUME, true); // 暂停恢复提醒
            
            metronome_remind_response_sport_status(p_ctrl->saving_status);
        }
    }
    else if (p_ctrl->ctrl_type == enum_ctrl_null && p_ctrl->saving_status == enum_status_ready)
    {
        // ready状态
        if (last_sport_status == enum_status_free)
        {
            ALGO_COMP_LOG_I("%s sport_ready:%d \n", __FUNCTION__, p_ctrl->saving_status);
            s_move_status = false;  // 运动状态重置
        }
    }
}

/**
 * @brief 算法控制订阅处理
 *
 * @param in 控制数据
 * @param len 数据长度
 */
static void algo_move_status_in_callback(const void *in, uint32_t len)
{
    const algo_move_status_pub_t* pub = (const algo_move_status_pub_t*)in;

    if (pub == NULL)
    {
        ALGO_COMP_LOG_E("%s status_callback error", __FUNCTION__);
        return;
    }

    s_move_status = pub->move_status;
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_MOVE_STATUS_CTRL,
        .topic_id = DATA_ID_EVENT_MOVE_STATUS_CTRL,
        .callback = algo_move_status_in_callback,
    },
};

/**
 * @brief 算法open
 *
 * @return int32_t 结果
 */
static int32_t algo_sport_status_open(void)
{
    int32_t ret = -1;

    if (s_is_open)
    {
        ALGO_COMP_LOG_W("%s already open", __FUNCTION__);
        return -1;
    }

    ALGO_COMP_LOG_D("%s open", __FUNCTION__);

    s_is_open = true;

    return algo_topic_list_subscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
}

/**
 * @brief 组件注册
 *
 * @return int32_t 结果
 */
int32_t register_sport_status_algo(void)
{
    algo_sport_status_open();
    return 0;
}

uint8_t get_sport_status(void)
{
    return (const uint8_t)last_sport_status;
}

/**
 * @brief 获取运动状态-->有状态定义的部分运动类型是有意义的
 *
 * @return bool 是否运动
 */
bool get_move_status(void)
{
    return s_move_status;
}