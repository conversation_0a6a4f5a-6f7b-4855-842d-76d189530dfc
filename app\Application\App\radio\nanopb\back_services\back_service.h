/***************************************Copyright (c)****************************************/
//                              <PERSON>han <PERSON> Technology Co., Ltd
//
//---------------------------------------File Info--------------------------------------------
// File name         : back_service.h
// Created by        : jiangzhen
// Descriptions      : ble通信后台服务，包括天气、AGPS
//--------------------------------------------------------------------------------------------
// History           :
// 2020-05-14        :原始版本
/*********************************************************************************************/
#if !defined(__BACK_SERVICE_)
#define __BACK_SERVICE_
#if defined(__cplusplus)
extern "C"
{
#endif

#include "stdint.h"
#include "stdbool.h"
#include "ble_cmd_common.h"
#include "qw_user_debug.h"

#define MAX_DATE_LENGTH                 11
#define MAX_THREE_HOURS_TIME_LENGTH     17
#define MAX_CUR_TIME_LENGTH             20
#define MAX_WIND_DEG_LENGTH             4
#define MAX_WIND_SPD_LENGTH             5
#define MAX_THREE_HOURS_COUNT           12//4
#define MAX_THREE_DAYS_COUNT            5
#define MAX_AGPS_NAME_LENGTH			25			 //如offline_ano_20190904.ubx
#define MAX_REGION_NAME_LENGTH          128
#define GPS_POSITION_SCALE              10000000
#define MAX_MINUTE_LENGTH               6

    //未来三天天气
    typedef struct
    {
        uint32_t three_days_weather_index;
        int32_t three_days_max_temp;
        int32_t three_days_min_temp;
        uint32_t three_days_rain_prob;
        char  date[MAX_DATE_LENGTH];                 //格式“2018-12-23”，共11个字符
        char  sun_rise_time[MAX_MINUTE_LENGTH];      //格式"06:00",共6个字符
        char  sun_set_time[MAX_MINUTE_LENGTH];        //格式"18:00",共6个字符
    }three_days_weather_st;

    //隔三小时天气
    typedef struct
    {
        uint32_t three_hour_weather_index;
        int32_t three_hour_temp;                    
        uint32_t three_hour_rain_prob;
        char time[MAX_THREE_HOURS_TIME_LENGTH];     //格式“2018-12-23 19:00”，共17个字符
        char wind_deg[MAX_WIND_DEG_LENGTH];
        char wind_spd[MAX_WIND_SPD_LENGTH];
        // int32_t three_hour_temp_min;                         //TODO: to yangzhao 未来小时天气中无最高/最低气温，只有对应气温
        // char sun_highest_time[MAX_THREE_HOURS_TIME_LENGTH];
    }three_hours_weather_st;

    //实况天气
    typedef struct
    {
        int32_t cur_temperature;
        uint32_t cur_weather;
        int32_t cur_day_max_temp;
        int32_t cur_day_min_temp;
        char time[MAX_CUR_TIME_LENGTH];                //格式“ 2018-12-23 19：00：00”,共20个字符
        char wind_deg[MAX_WIND_DEG_LENGTH];
        char wind_spd[MAX_WIND_SPD_LENGTH];
        // uint32_t rain_prob;                          //TODO: to yangzhao 当前天气中移除降雨概率字段，需在three_hours_weather_st结构体中获取
        uint32_t humidity;
        uint32_t air_quality;
        char region[MAX_REGION_NAME_LENGTH];
        // uint32_t sun_rise_time;                      //TODO: to yangzhao 当前天气中移除日出时间字段，需在three_days_weather_st结构体中获取
        // uint32_t sun_set_time;                       //TODO: to yangzhao 当前天气中移除日落时间字段，需在three_days_weather_st结构体中获取
    }cur_weather_st;

    //天气信息
    typedef struct
    {
        three_days_weather_st three_days_t[MAX_THREE_DAYS_COUNT];
        cur_weather_st cur_weather_t;
        three_hours_weather_st three_hours_t[MAX_THREE_HOURS_COUNT];
        uint32_t air_pressure;
    } back_weather_st;


    //时间信息
    typedef struct
    {
        uint32_t time;
        int32_t time_zone;
    } back_time_st; 

    //用户信息
    typedef struct
    {
        uint32_t sex;
        uint32_t age;
        uint8_t height;
        uint16_t weight;
        uint16_t step_length;
        uint8_t hr_max;
        uint8_t hr_rest;
        uint8_t hr_lactic_acid;
        uint8_t body_fat_rate;
        uint16_t sport_ftp;
        uint16_t vo2max;
    } back_user_st;
    
    typedef void (*weather_data_update_callback_t)(back_weather_st* weather_data);

    //-------------------------------------------------------------------------------------------
    // Function Name : back_service_status_handle
    // Purpose       : 通道2状态处理接口函数
    // Param[in]     : uint8_t *buf  
    // Param[out]    : None
    // Return type   : 
    // Comment       : 2019-04-06
    //-------------------------------------------------------------------------------------------
    void back_service_status_handle(uint8_t* buf);

    //-------------------------------------------------------------------------------------------
    // Function Name : back_service_decode
    // Purpose       : 后台服务PB解码接口函数
    // Param[in]     : uint8_t * pb_buffer     
    //                 uint16_t buffer_length  
    //                 END_TYPE end_type       
    // Param[out]    : None
    // Return type   : 
    // Comment       : 2020-05-14
    //-------------------------------------------------------------------------------------------
    void back_service_decode(uint8_t* pb_buffer, uint16_t buffer_length, END_TYPE end_type);

    //-------------------------------------------------------------------------------------------
    // Function Name : weather_data_get_cmd
    // Purpose       : 向手机APP发送获取天气命令
    // Param[in]     : None
    // Param[out]    : None
    // Return type   : 
    // Comment       : 2019-08-26
    //-------------------------------------------------------------------------------------------
    void weather_data_get_cmd(void);

    //-------------------------------------------------------------------------------------------
    // Function Name : weather_data_update
    // Purpose       : 应用层获取天气参数接口,避免界面刷新重复获取，只允许进入界面获取一次
    // Param[in]     : flag = true，向APP获取天气  
    // Param[out]    : None
    // Return type   : back_weather_st
    // Comment       : 2019-05-31
    //-------------------------------------------------------------------------------------------
    back_weather_st* weather_data_update(uint8_t flag);

    //-------------------------------------------------------------------------------------------
    // Function Name : weather_data_update_reset
    // Purpose       : 重置 is_weather_received 标志的接口,需要在天气信息过期之后调用
    // Param[in]     : None
    // Param[out]    : None
    // Return type   : None
    // Comment       : 2019-05-31
    //-------------------------------------------------------------------------------------------
    void weather_data_update_reset();

/************************************************************************
 *@function:void weather_data_update_callback_register(weather_data_update_callback_t callback);
 *@brief:注册天气更新回调函数
 *@param: callback - 天气更新回调函数
 *@return:null
*************************************************************************/
void weather_data_update_callback_register(weather_data_update_callback_t callback);

/************************************************************************
 *@function:void weather_data_update_callback_unregister(void);
 *@brief:注销天气更新回调函数
 *@param: null
 *@return:null
*************************************************************************/
void weather_data_update_callback_unregister(void);

/************************************************************************
 *@function:weather_data_update_callback_notify(back_weather_st* weather_data);
 *@brief:通知天气更新
 *@param: back_weather_st* weather_data - 天气数据
 *@return:null
*************************************************************************/
void weather_data_update_callback_notify(back_weather_st* weather_data);

//-------------------------------------------------------------------------------------------
// Function Name : agps_data_get_cmd
// Purpose       : 主动向APP获取AGPS信息
// Param[in]     : void  
// Param[out]    : None
// Return type   : 
// Comment       : 2021-01-13
//-------------------------------------------------------------------------------------------
void agps_data_get_cmd(void);

//-------------------------------------------------------------------------------------------
// Function Name : void designated_agps_data_get_cmd(uint8_t sub_op, uint8_t satellite);
// Purpose       : 主动向APP获取指定卫星AGPS信息
// Param[in]     : uint8_t sub_op - 星历文件时段类型（BACK_SUB_OPERATE_TYPE）
// Param[in]     : uint8_t satellite - 指定卫星类型（GPS_TYPE）
// Param[out]    : None
// Return type   : 
//-------------------------------------------------------------------------------------------
void designated_agps_data_get_cmd(uint8_t sub_op, uint8_t satellite);

//-------------------------------------------------------------------------------------------
// Function Name : void designated_agps_data_get_cmd_with_timestamp(uint8_t sub_op, uint8_t satellite, uint32_t timestamp);
// Purpose       : 主动向APP获取指定卫星AGPS信息
// Param[in]     : uint8_t sub_op - 星历文件时段类型（BACK_SUB_OPERATE_TYPE）
// Param[in]     : uint8_t satellite - 指定卫星类型（GPS_TYPE）
// Param[in]     : uint32_t timestamp - 时间戳
// Param[out]    : None
// Return type   : 
//-------------------------------------------------------------------------------------------
void designated_agps_data_get_cmd_with_timestamp(uint8_t sub_op, uint8_t satellite, uint32_t timestamp);

//-------------------------------------------------------------------------------------------
// Function Name : time_data_get_cmd
// Purpose       : 主动向APP获取时间信息
// Param[in]     : void  
// Param[out]    : None
// Return type   : 
// Comment       : 2025-05-09
//-------------------------------------------------------------------------------------------
void time_data_get_cmd(void);

//-------------------------------------------------------------------------------------------
// Function Name : position_data_get_cmd
// Purpose       : 主动向APP获取位置信息
// Param[in]     : void  
// Param[out]    : None
// Return type   : null
//-------------------------------------------------------------------------------------------
void position_data_get_cmd(void);

#ifdef DEVELOPER_SETTING_EPO_SYNC

// 开始申请epo文件同步，恢复已同步文件数量计数
void epo_sync_request_start(void);

// 每同步成功一个文件，计数减1
void epo_sync_file_add(void);

//-------------------------------------------------------------------------------------------
// Function Name : void epo_sync_test_set(bool state)
// Purpose       : 设置epo文件同步测试
// Param[in]     : bool state - true:开启测试，false:关闭测试
// Param[out]    : None
// Return type   : null
//-------------------------------------------------------------------------------------------
void epo_sync_test_set(bool state);

#endif

#ifdef __cplusplus
}
#endif
#endif /* __BACK_SERVICE_ */
/**
 *@}
 **/
