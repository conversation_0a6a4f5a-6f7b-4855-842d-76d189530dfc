/**
 * @file lib_gm_common.c
 * <AUTHOR> (<EMAIL>)
 * @brief gomore算法针对计步、步频、卡路里、活动强度、睡眠输出的通用实现
 * @version 0.1
 * @date 2024-11-12
 *
 * @copyright Copyright (c) 2024-2025, <PERSON>han Qiwu Technology Co., Ltd
 *
 */

#include "lib_gm_common.h"
#include <rtthread.h>
#include <cmsis_os2.h>
#include "GoMoreLib.h"
#include "GoMoreLibStruct.h"
#include "memory_manager.h"
#include "subscribe_data_protocol.h"
#include "qw_time_util.h"
#include "algo_service_component_log.h"
#include "algo_service_statistics.h"
#include "algo_service_adapter.h"
#include "algo_service_task.h"
#include "subscribe_service.h"
#include "lib_gm_collect.h"
#include "qw_sensor.h"
#include "qw_sensor_common_config.h"
#define LOG_TAG "algo.gm"
#include <drv_log.h>
#include "sensor_hub_app_service.h"
#include "qw_time_api.h"
#include "algo_ppg_task.h"
#include "Ppg.h"
#include "qw_syslog.h"
#include "qw_system_params.h"
#include "qw_piling_fake.h"
#include "qw_user_debug.h"
#include "factory_data.h"
#include "qmc6308.h"

// gomore商用版本使能宏：使能需要同步打开商用lib库
#define GMORE_COMMERCIAL_VERSION_ENABLE 1

// gomoreA5(<DVT3)以下算法兼容宏
#define GMORE_A5_COMPATIBLE_ENABLE 1

// 长睡眠有效时间0h,单位min
#define LONG_SLEEP_VAILD_TIME (0 * 60)

// 短睡眠有效时间20min,单位sec
#define SHORT_SLEEP_VAILD_TIME (20 * 60)


#define LIB_SET_BIT(val, bit) ((val) | (1L << bit))
#define LIB_GET_BIT(val, bit) ((val & (1L << bit)) >> bit)
#define LIB_CLEAR_BIT(val, bit) (val & ~(1L << bit))

// 分钟转秒乘子
#define MIN_TO_SEC 60

// ppg出值时间(开启到出值)
#define PPG_OUTPUT_DELTA_TIME 30

// 分钟定时器计数开心率计数
#define TIMER_EVENT_OPEN_HR_COUNT (1 * MIN_TO_SEC + PPG_OUTPUT_DELTA_TIME)

// 分钟定时器计数关心率计数
#define TIMER_EVENT_CLOSE_HR_COUNT (9 * MIN_TO_SEC - PPG_OUTPUT_DELTA_TIME)

// 1mm/s转1km/h乘子：1mm/s = (1/1000/1000)km /(1/3600)h = 0.0036 km/h
#define MM_PER_S_TO_KM_H 0.0036

// cm转m乘子
#define CM_TO_M 0.01

// mdps转dps乘子
#define MDPS_TO_DPS 0.001

// fitness triger type
#define FITNESS_GYRO52_TRIGGER 0

// fitness实时上报回调开关
#define GOMORE_FITNESS_CALLBACK

#if GMORE_A5_COMPATIBLE_ENABLE
const char *s_def_sn = "W305700000";
const char *s_def_pkey = "LLoeY5EZ0chjnhDBqAgOcN0LHdDnU8xDPzjMsE/rkSWW6AYvyzyxWYNK7/IhkaiM";
#endif

// 输入读写互斥锁
static rt_mutex_t s_input_mutex = NULL;

// For engineering test, expired time is 2024/2/24
static workoutAuthParameters g_authparams = {0};
static void *g_sdkMem = NULL;
static char *g_prevData = NULL;

// 时间同步大核处理完成标志
static bool s_hcpu_reset_ok = false;

static acc_queue_t s_acc_queue = {0};
static acc_queue_t s_acc_queue_bak = {0};

static gyro_queue_t g_gyro52_queue = {0};
static gyro_queue_t g_gyro52_queue_bak = {0};

static mag_queue_t g_mag50_queue = {0};
static mag_queue_t g_mag50_queue_bak = {0};

lib_gm_ppg_queue_t g_ppg_queue = {0};
static float s_ppg_data[GOMORE_WELLNESS_PPG_FRAME] = {0};
static uint32_t s_ppg_time[GOMORE_WELLNESS_PPG_FRAME] = {0};

static lib_gm_ppi_t s_ppi_data = {0}; // ppi数据
static lib_gm_ppi_t s_ppi_data_bak = {0}; // ppi数据备份
static bool s_lib_gm_init = false; // gm算法初始化标记
static uint32_t s_gm_map = 0; // gomore算法输入项map
static callback_t s_call_cbk[GM_DATA_TYPE_MAX] = {0}; // 各种数据输出回调
static uint32_t s_input_open_count[GM_INPUT_MAX] = {0}; // 所有元数据输入订阅次数
static bool s_sleep_stage_sta = false; // 睡眠状态
static bool s_start_update_ppg_input = false; // 标志ppg输入数据准备ok
static uint32_t s_ppg_hr = 0; // ppg心率
static int8_t s_sleep_stages[GOMORE_STAGES_LENTH] = {0}; // 睡眠数据
static float s_altitude = -999; // 高度 单位：m
static float s_sys_speed = -1; // system速度 单位：km/h
static float s_cycling_power = -1; // 骑行功率 单位：w
static float s_cycling_cadence = -1; // 踏频 单位：rpm
static FITNESS_TYPE_E s_workout_type = 0; // 启动锻炼的类型
static uint32_t s_stress_test_mode = 0; // 压力测试模式:0:默认周期测量 1:点测
static uint32_t s_cur_click_boot_time = 0; // 当前开启压力点测开机时间
static lib_gm_user_info_t s_userinfo = {27, 0, 158, 53, -1, -1, -1}; // 用户信息

static void algo_gm_hr_in_callback(const void *in, uint32_t len);
static void algo_ppg_in_callback(const void *in, uint32_t len);
static void algo_ppi_in_callback(const void *in, uint32_t len);

#ifdef RT_USING_FINSH
// acc模拟开关
static bool s_acc_simulate_switch = 0;

// 模拟心率值，用于测试
static uint32_t s_hr_simulate_val = 0;

// 模拟速度值，用于测试
static uint32_t s_speed_simulate_val = 0;

// fitness仿真开关
static bool s_fitness_simulate_switch = 0;

static void lib_gm_sim_hr_input(void);
static void lib_gm_sim_speed_input(void);
static bool lib_gomore_sleep_period(void);
static bool lib_gm_fitness_test(void);
static float sin_over_samples(void);
#endif

/************************************************************************************
* @function:get_gm_lib_init_sta
* @brief:获取gm算法初始化标记
* @param:none
* @return:是否初始化gm算法
************************************************************************************/
bool get_gm_lib_init_sta(void)
{
    return s_lib_gm_init;
}

/************************************************************************************
* @function:lib_gm_get_sleep_stages
* @brief:获取是睡眠否入睡状态
* @param:none
* @return:是否入睡
************************************************************************************/
bool get_sleep_status(void)
{
    return s_sleep_stage_sta;
}

/**********************************************************************************
 * @function:lib_gm_get_sleep_stages
 * @brief:获取睡眠数据
 * @param：none
 * @return：睡眠数据
***********************************************************************************/
int8_t* lib_gm_get_sleep_stages(void)
{
    return s_sleep_stages;
}

/**********************************************************************************
 * @function:lib_gm_set_stress_test_mode
 * @brief:设置压力测试模式
 * @param:mode 压力测试模式
***********************************************************************************/
void lib_gm_set_stress_test_mode(uint32_t mode)
{
    if (s_stress_test_mode == mode) {
        return;
    }
    s_stress_test_mode = mode;
    if (mode == 1)
    {
        s_cur_click_boot_time = get_boot_sec();
    } else {
        s_cur_click_boot_time = 0;
    }
}

/**********************************************************************************
 * @function:lib_gm_get_user_info
 * @brief:获取用户信息
 * @param:lib_gm_user_info_t* 用户信息指针
***********************************************************************************/
lib_gm_user_info_t* lib_gm_get_user_info(void)
{
    return  &s_userinfo;
}

/**********************************************************************************
 * @function:lib_gm_get_workout_type
 * @brief:获取fitness类型
 * @param:uint32_t 运动类型
***********************************************************************************/
uint32_t lib_gm_get_workout_type(void)
{
    return  s_workout_type;
}

//====================================acc queue====================================
// 初始化队列
static void initAccQueue(acc_queue_t *q)
{
    q->front = 0;
    q->rear = 0;
    q->size = 0;
    q->timestamp = 0;
}

// 判断队列是否已满
static int isAccFull(acc_queue_t *q)
{
    return q->size == GOMORE_ACC_BUFF_MAX;
}

// 判断队列是否为空
static int isAccEmpty(acc_queue_t *q)
{
    return q->size == 0;
}

// 入队操作
static void accEnqueue(acc_queue_t *q, float x, float y, float z)
{
    if (isAccFull(q))
    {
        // 队列满时，front指针后移
        q->front = (q->front + 1) % GOMORE_ACC_BUFF_MAX;
        RT_ASSERT(0); // 队列满,无法入队,直接死机
    }
    else
    {
        // 队列未满时，size增加
        q->size++;
    }

    // 将新数据添加到队尾
    q->x[q->rear] = x;
    q->y[q->rear] = y;
    q->z[q->rear] = z;

    // rear指针后移
    q->rear = (q->rear + 1) % GOMORE_ACC_BUFF_MAX;
}

// 出队操作，支持一次出队多个元素
static uint32_t accDequeue(acc_queue_t *in, acc_queue_t *out, uint32_t count)
{
    if (isAccEmpty(in))
    {
        return 0; // 队列为空，返回0
    }

    // 实际出队数量不能超过队列当前的大小
    uint32_t actualCount = (count > in->size) ? in->size : count;
    for (uint32_t i = 0; i < actualCount; i++) {
        // 将队列中的数据复制到输出队列中
        accEnqueue(out, in->x[in->front], in->y[in->front], in->z[in->front]);
        // 更新front指针
        in->front = (in->front + 1) % GOMORE_ACC_BUFF_MAX;
    }

    // 更新队列大小
    in->size -= actualCount;
    return actualCount; // 返回实际出队的元素数量
}
//====================================acc queue====================================
//====================================ppg queue====================================
// 初始化队列
static void initPpgQueue(lib_gm_ppg_queue_t *q)
{
    q->size = 0;
}

// 判断队列是否已满
static int isPpgFull(lib_gm_ppg_queue_t *q)
{
    return q->size == PPG_RAM_LENGTH;
}

// 判断队列里有几个数据
static int ppgQueueSize(lib_gm_ppg_queue_t *q)
{
    return q->size;
}

// 入队操作
static void ppgEnqueue(lib_gm_ppg_queue_t *q, float ppg_data, uint32_t timestamp)
{
    if (isPpgFull(q))
    {
        // 当队列满时，移动数据
        // 从位置 1 开始移动到位置 0，直到队列末尾
        for (int i = 1; i < PPG_RAM_LENGTH; i++)
        {
            q->data[i - 1] = q->data[i];
            q->timestamp[i - 1] = q->timestamp[i];
        }
        // 更新队列的大小
        q->size--;
    }

    // 将新数据添加到队列末尾
    q->data[q->size] = ppg_data;
    q->timestamp[q->size] = timestamp;
    q->size++;
}

// 出队操作
static bool ppgDequeue(lib_gm_ppg_queue_t *q)
{
    if (q->size == 0)
    {
        return false; // 队列为空
    }

    // 移动数据，从位置1开始到位置0，直到队列末尾
    for (int i = 1; i < q->size; i++)
    {
        q->data[i - 1] = q->data[i];
        q->timestamp[i - 1] = q->timestamp[i];
    }

    // 更新队列的大小
    q->size--;
    q->data[q->size] = 0.0;
    q->timestamp[q->size] = 0;
    return true;
}

// 打印队列
static void printPpgQueue(lib_gm_ppg_queue_t *q)
{
    if (q->size == 0)
    {
        printf("ppg Queue is empty\n");
        return;
    }
    for (int j = 0; j < PPG_RAM_LENGTH; j++)
    {
        printf("ppg( %f, %u) ", q->data[j], q->timestamp[j]);
    }
    printf("\n");
}

/**
 * @brief 设置用户信息
 *
 * @param rtc 当前时间
 * @return int32_t 0表示成功，-1表示失败
 */
static int32_t gomore_user_info_set(uint32_t rtc)
{
    if (g_sdkMem == NULL || g_prevData == NULL)
    {
        ALGO_COMP_LOG_E("lib_gm:userinfo set g_sdkMem or g_prevData null");
        return -1;
    }
    qw_tm_t datetime = {0};
    utc_to_localtime(get_sec_from_rtc(), &datetime); // 获取当前时间
    cfg_user_information_t* user_info = sensor_core_get_user_info(); // 获取用户信息
    int32_t age = datetime.tm_year + 1900 - user_info->birth.year;
    if (age >= 10 && age <= 99) // 年龄范围
    {
        s_userinfo.age = age; // 年龄
    }
    if (user_info->gender == 0 || user_info->gender == 1) // 性别范围
    {
        s_userinfo.sex = user_info->gender; // 性别
    }
    if (user_info->height >= 100 && user_info->height <= 220) // 身高范围
    {
        s_userinfo.height = user_info->height; // 身高
    }
    if (user_info->weight >= 100 && user_info->weight <= 1500) // 体重范围
    {
        s_userinfo.weight = user_info->weight / 10; // 体重:100g->kg
    }
    if (user_info->user.hrm_run_max >= 138 && user_info->user.hrm_run_max <= 220) // 最大心率范围
    {
        s_userinfo.hrmax = user_info->user.hrm_run_max;
    }
    ALGO_COMP_LOG_I("lib_gm:userinfo set age:%.2f,sex:%.2f,height:%.2f,weight:%.2f",
        s_userinfo.age, s_userinfo.sex, s_userinfo.height, s_userinfo.weight);
    ALGO_COMP_LOG_I("lib_gm:userinfo set hrmax:%.2f,hrrest:%.2f,vo2max:%.2f",
        s_userinfo.hrmax, s_userinfo.hrrest, s_userinfo.vo2max);
    int16_t ret = healthIndexInitUser(g_sdkMem, rtc, (float *)&s_userinfo, g_prevData);
    if (ret < 0)
    {
        ALGO_COMP_LOG_E("lib_gm:userinfo set ret:%d", ret);
    }
    return ret;
}

/**
 * @brief 重置用户信息
 *
 * @return int32_t 0表示成功，-1表示失败
 */
int32_t gomore_user_info_reset(void)
{
    ALGO_COMP_LOG_I("lib_gm:userinfo reset");
    s_hcpu_reset_ok = true; // 重置用户信息标记置位
    lib_gm_reset(); // 算法复位
    return 0;
}

/**
 * @brief 获取设备ID
 *
 * @param Device_ID 输出设备ID
 * @param Size 设备ID长度
 * @return int8_t 0表示成功，-1表示失败
 */
int8_t gomore_device_id_get(char *Device_ID, uint8_t Size)
{
    if (Device_ID == NULL)
    {
        return -1;
    }
#if GMORE_A5_COMPATIBLE_ENABLE
    if (hw_version_get() < HARDWARE_VERSION_A5)
    {
        rt_memcpy(Device_ID, s_def_sn, Size);
        return 0;
    }
#endif
    rt_memcpy(Device_ID, factory_data_get_sn(), Size);
    return 0;
}

/**
 * @brief 获取产品密钥
 *
 * @param pKey 输出产品密钥
 * @param Size 产品密钥长度
 * @return int8_t 0表示成功，-1表示失败
 */
int8_t gomore_pkey_get(char *pKey, uint8_t Size)
{
    if (pKey == NULL)
    {
        return -1;
    }
#if GMORE_A5_COMPATIBLE_ENABLE
    if (hw_version_get() < HARDWARE_VERSION_A5)
    {
        rt_memcpy(pKey, s_def_pkey, Size);
        return Size;
    }
#endif
    rt_memcpy(pKey, factory_data_get_gomore_license(), Size);
    int ret = Size;
    if (ret != Size)
    {
        return -2;
    }

    return (int8_t)ret;
}

/**
 * @brief gomore sdk权限校验以及信息配置
 *
 * @return true ： 成功  false : 失败
 */
static bool init_gomore_info(void)
{
    uint32_t rtc_tick = (uint32_t)get_sec_from_rtc();
#if GMORE_COMMERCIAL_VERSION_ENABLE
    g_authparams.rtcCurrentTime = rtc_tick;
    // Sdk Auth
    ALGO_COMP_LOG_I("lib_gm:sdk Auth hw_Ver:%d", hw_version_get());
    int16_t ret = setAuthParameters(&g_authparams);
    if (ret < 0)
    {
        ALGO_COMP_LOG_E("lib_gm:sdk Auth ret:%d,rtc_tick:%d,sn:%s,pkey:%s", ret, rtc_tick, factory_data_get_sn(), factory_data_get_gomore_license());
        return false;
    }
#else
    g_authparams.pKey = "8KhDuAipxUcVpNW6vcsD8RjpHyGAiaWYxn7AZuoHj2Lz2S4e5mHuT+rtScl9TwvH";
    g_authparams.deviceId = "lUH8Po2XOW0M";
    g_authparams.devIdLen = strlen(g_authparams.deviceId);
    g_authparams.rtcCurrentTime = rtc_tick;
    // Sdk Auth
    int16_t ret = setAuthParameters(&g_authparams);
    if (ret < 0)
    {
        ALGO_COMP_LOG_E("lib_gm:sdk Auth ret:%d,rtc_tick:%d,deviceId:%s,pkey:%s", ret, rtc_tick, g_authparams.deviceId, g_authparams.pKey);
        return false;
    }
#endif
    int32_t relVer[4] = {0}; // 0主版本号 1次版本号 2维护版本号 3日期
    getReleaseVersion(relVer);
    gm_rel_version_set(relVer, 4);
    ALGO_COMP_LOG_I("lib_gm:sdk Auth sdk_Ver:%d:%d:%d:%d", relVer[0], relVer[1], relVer[2], relVer[3]);

    //给工模传递init信息
    set_system_params()->fat_dev_info[FAT_ALG_GM].dev_type = FAT_ALG_GM;
    set_system_params()->fat_dev_info[FAT_ALG_GM].dev_id = 0;
    set_system_params()->fat_dev_info[FAT_ALG_GM].dev_state = 1;
#ifdef FACTORY_MODE_ENABLE
    // todo:初始化成功，发布通知(包含算法sdk版本号和初始化结果)
#endif

    // initialize SDK
    ret = gomore_user_info_set(rtc_tick);
    if (ret < 0)
    {
        ALGO_COMP_LOG_E("lib_gm:init sdk ret:%d", ret);
        return false;
    }

    // 设置步数输出延迟时间:4s是最佳效果
    ret = setStepCountOutputDelayDuration(4);
    if (ret != 0)
    {
        ALGO_COMP_LOG_E("lib_gm:set count out duty failed, ret = %d", ret);
        return false;
    }

    // 1：ppi模式 0：ppg模式,适用于全天体力、压力、hrv，不适用于睡眠和静息心率
    ret = switchMode(1);
    if (ret != 0)
    {
        ALGO_COMP_LOG_E("lib_gm:switchMode failed, ret = %d", ret);
        return false;
    }
    return true;
}

/**
 * @brief 开始fitness会话
 *
 * @param workout_type 锻炼类型
 * @param value 锻炼值
 */
void lib_start_fitness_session(FITNESS_TYPE_E workout_type, uint32_t value)
{
    struct StartFitnessInput startIn;
    memset(&startIn, 0, sizeof(struct StartFitnessInput));
    s_workout_type = workout_type;
    startIn.workoutType = workout_type;
    cfg_user_information_t* user_info = sensor_core_get_user_info(); // 获取用户信息
    if (workout_type == workout_outdoor_run || workout_type == workout_indoor_run || workout_type == workout_trail_run)
    {
        startIn.workout.run.gender = user_info->gender; // 获取性别信息
        if (user_info->height > 100 && user_info->height < 220) { // 身高范围
            startIn.workout.run.height = user_info->height; // 身高
        } else {
            startIn.workout.run.height = 175; // 默认身高
        }
        if (user_info->weight > 100 && user_info->weight < 1500) { // 体重范围
            startIn.workout.run.weight = user_info->weight / 10; // 体重:100g->kg
        } else {
            startIn.workout.run.weight = 60; // 默认体重
        }
        startIn.workout.run.handFlag = !(user_info->hand_habit); // 获取佩戴左右手信息
    }
    else if (workout_type == workout_swimming)
    {
        startIn.workout.swim.handFlag = !(user_info->hand_habit); // 获取佩戴左右手信息
        startIn.workout.swim.poolSize = (value == 0 ? 0xff : value); // 游泳池大小
    }
    else if (workout_type == workout_dumbbell)
    {
        startIn.workout.dumbbell.workoutType = 1; // 哑铃
    }

    startIn.fitness.trainingInfo = NULL;
    startIn.fitness.rtc = (uint32_t)get_sec_from_rtc();
    startIn.fitness.timeZoneOffset = tz_get()->tz_minuteswest;
    if (workout_type == workout_outdoor_cycling || workout_type == workout_outdoor_run ||
        workout_type == workout_indoor_run || workout_type == workout_trail_run) {
        startIn.autoNotifier = true; // 启动自动暂停恢复通知
    }
    int16_t ret = startFitness(&startIn);
    ALGO_COMP_LOG_I("startFitness,workout_type:%d,rtc:%u,timeZoneOffset:%d,ret:%d", workout_type, startIn.fitness.rtc, startIn.fitness.timeZoneOffset, ret);
    if (ret != 0)
    {
        ALGO_COMP_LOG_E("lib_gm:startFitness failed,ret:%d", ret);
        return;
    }
    ALGO_COMP_LOG_I("lib_gm:startFitness type:%d,value:%u", workout_type, value);
    if (workout_indoor_run == workout_type)
    {
        uint8_t mode = 0;
        ret = startAccSpdCali(mode);
        if (ret != 0)
        {
            ALGO_COMP_LOG_E("lib_gm:startAccSpdCali failed,mode:%u,ret:%d", mode, ret);
        }
        ALGO_COMP_LOG_I("lib_gm:startAccSpdCali mode:%d", mode);
    }
}

/**
 * @brief 保存一次运动数据到共享内存
 *
 */
static void lib_save_previousdata_to_sharemem(void)
{
    uint32_t prev_data_size = getPreviousDataSize();
    uint32_t timestamp = (uint32_t)get_sec_from_rtc();
    ALGO_COMP_LOG_I("lib_gm:save prev_data_size:%d,timestamp:%d", prev_data_size, timestamp);
    if (prev_data_size > ALGO_SHARE_ADDR_SIZE - sizeof(timestamp) - sizeof(prev_data_size))
    {
        ALGO_COMP_LOG_E("lib_gm:save buff overflow,prev_data_size:%d", prev_data_size);
        return;
    }
    char* prevData = get_algo_share_mem();
    memcpy(prevData, &timestamp, sizeof(timestamp));
    memcpy(prevData + sizeof(timestamp), &prev_data_size, sizeof(prev_data_size));
    memcpy(prevData + sizeof(timestamp) + sizeof(prev_data_size), g_prevData, prev_data_size);
}

/**
 * @brief 结束fitness会话
 *
 * @param end_out 结束fitness会话的输出参数
 * @param cali_distance 校准距离
 */
void lib_stop_fitness_session(struct EndFitnessOutput *end_out, uint32_t cali_distance)
{
    struct EndFitnessInput endIn;
    memset(&endIn, 0, sizeof(struct EndFitnessInput));
    endIn.fitness.workoutDate[0] = (uint32_t)get_sec_from_rtc();
    endIn.fitness.hisOP = 1; // 处理本次运动，不放弃
    int16_t ret = endFitness(&endIn, end_out);
    ALGO_COMP_LOG_I("endFitness workout_type:%d,workoutDate:%u,hisOP:%d,ret:%d", s_workout_type, endIn.fitness.workoutDate[0], endIn.fitness.hisOP, ret);
    if (ret != 0)
    {
        ALGO_COMP_LOG_E("lib_gm:endFitness failed,ret:%d", ret);
        return;
    }
    lib_save_previousdata_to_sharemem();
    algo_gomore_collect_fitness_end(s_workout_type, end_out);
    ALGO_COMP_LOG_I("lib_gm:endFitness type:%d", s_workout_type);
    if (workout_indoor_run == s_workout_type)
    {
        float out_dis = 0; // 单位：米
        ret = getAccSpdDist(&out_dis);
        if (ret != 0)
        {
            ALGO_COMP_LOG_E("lib_gm:getAccSpdDist failed,ret:%d", ret);
        }
        float in_dis = (float)cali_distance; // 单位：米
        ret = endAccSpdCali(in_dis);
        if (ret != 0)
        {
            ALGO_COMP_LOG_E("lib_gm:endAccSpdCali failed,ret:%d", ret);
        }
        ALGO_COMP_LOG_I("lib_gm:endAccSpdCali in_dis:%f,out_dis:%f", in_dis, out_dis);
    }
    s_workout_type = 0;
}

/**
 * @brief gomore算法输入进行订阅和取消订阅
 *
 * @param is_disable 是否取消订阅
 * @param topic_id 话题id
 * @param callback 订阅者回调处理
 */
static void lib_gomore_sub_chg(bool is_disable, uint32_t topic_id, callback_t callback, const optional_config_t *config)
{
    if (!is_disable)
    {
        int32_t ret = qw_dataserver_subscribe_id(topic_id, callback, config);
        if (ret != 0)
        {
            ALGO_COMP_LOG_E("lib_gm:chg sub erro ret:%d", ret);
        }
    }
    else
    {
        int32_t ret = qw_dataserver_unsubscribe_id(topic_id, callback);
        if (ret != 0)
        {
            ALGO_COMP_LOG_E("lib_gm:chg unsub erro ret:%d", ret);
        }
    }
}

/**
 * @brief 重置ppg心率和ppi数据输入
 *
 */
static void lib_storage_ppg_hr_reset(void)
{
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    s_ppg_hr = 0;
    s_ppi_data.size = 0;
    s_ppi_data.confidence = 0;
    s_ppi_data_bak.size = 0;
    s_ppi_data_bak.confidence = 0;
    rt_mutex_release(s_input_mutex);
}

/**
 * @brief wellness算法(卡路里、强度、压力)脉冲输入心率和ppi处理
 *
 * @param timestamp 时间戳
 * @param is_enter_sleep 是否进入睡眠
 */
static void lib_gm_calories_hr_pulse(uint32_t timestamp, bool is_enter_sleep)
{
    if (s_call_cbk[GM_CALORIES] == NULL && s_call_cbk[GM_INTENSE_TIME] == NULL &&
        s_call_cbk[GM_STRESS_SCORE] == NULL && s_call_cbk[GM_HRV_TIME] == NULL) {
        return;
    }
    static uint32_t last_timestamp = 0;
    static bool is_sub_hr = false; // 默认心率订阅不开
    optional_config_t config = { .sampling_rate = 0,};

    // fitness被订阅或则压力点测过测量静息心率开启,心率和ppi未被订阅则订阅
    if (s_call_cbk[GM_FITNESS] != NULL || s_call_cbk[GM_RESTING_HR] != NULL || s_stress_test_mode) {
        if (!is_sub_hr) {
            lib_gomore_sub_chg(false, DATA_ID_ALGO_HEART_RATE, algo_gm_hr_in_callback, &config); // ppg hr sub
            config.sampling_rate = 0; // ppi采样率
            lib_gomore_sub_chg(false, DATA_ID_ALGO_PPG_HRV, algo_ppi_in_callback, &config); // ppi sub
            is_sub_hr = true;
        }
        last_timestamp = 0; // fitness结束后恢复卡路里/活动强度算法心率输入
        return;
    }

    // 睡眠算法被订阅且睡眠已经启动
    if (is_enter_sleep) {
        if (is_sub_hr) {
            lib_gomore_sub_chg(true, DATA_ID_ALGO_HEART_RATE, algo_gm_hr_in_callback, &config); // ppg hr unsub
            config.sampling_rate = 0; // ppi采样率
            lib_gomore_sub_chg(true, DATA_ID_ALGO_PPG_HRV, algo_ppi_in_callback, &config); // ppi unsub
            is_sub_hr = false;
        }
        last_timestamp = 0; // 睡眠结束后恢复卡路里/活动强度算法心率输入
        return;
    }

    // 正常卡路里/活动强度算法脉冲输入心率
    uint32_t ditt_time = timestamp - last_timestamp;
    if (!is_sub_hr && ditt_time >= TIMER_EVENT_CLOSE_HR_COUNT) {
        lib_gomore_sub_chg(false, DATA_ID_ALGO_HEART_RATE, algo_gm_hr_in_callback, &config); // ppg hr sub
        config.sampling_rate = 0; // ppi采样率
        lib_gomore_sub_chg(false, DATA_ID_ALGO_PPG_HRV, algo_ppi_in_callback, &config); // ppi sub
        is_sub_hr = true;
        last_timestamp = timestamp;
        ALGO_COMP_LOG_D("lib_gm:hr chg is_sub_hr:%d,timestamp:%u", is_sub_hr, timestamp);
    } else if (is_sub_hr && ditt_time >= TIMER_EVENT_OPEN_HR_COUNT) {
        lib_gomore_sub_chg(true, DATA_ID_ALGO_HEART_RATE, algo_gm_hr_in_callback, &config); // ppg hr unsub
        config.sampling_rate = 0; // ppi采样率
        lib_gomore_sub_chg(true, DATA_ID_ALGO_PPG_HRV, algo_ppi_in_callback, &config); // ppi unsub
        is_sub_hr = false;
        last_timestamp = timestamp;
        lib_storage_ppg_hr_reset();
        ALGO_COMP_LOG_D("lib_gm:hr chg is_sub_hr=%d,timestamp:%u", is_sub_hr, timestamp);
    }
}

/**
 * @brief 重置ppg数据输入
 *
 */
static void lib_storage_ppg_data_reset(void)
{
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    s_start_update_ppg_input = false;
    s_ppg_hr = 0;
    s_ppi_data.size = 0;
    s_ppi_data.confidence = 0;
    s_ppi_data_bak.size = 0;
    s_ppi_data_bak.confidence = 0;
    initPpgQueue(&g_ppg_queue);
    rt_mutex_release(s_input_mutex);
}

/**
 * @brief 睡眠期间做心率和ppg原始数据订阅脉冲调制
 *
 * @param timestamp 时间戳
 * @param is_enter_sleep 是否进入睡眠
 * @param sleep_pulse 脉冲值
 */
static void lib_gomore_sleep_stage(uint32_t timestamp, bool is_enter_sleep, bool sleep_pulse)
{
    static bool last_sleep_pulse = false;

    // 运动期间，不做ppg-duty脉冲调制
    if (s_call_cbk[GM_FITNESS] != NULL)
    {
        return;
    }

    // 睡眠进入退出时做卡路里和活动时长心率订阅脉冲调制
    static bool is_sleep_stage = false;
    if (is_sleep_stage != is_enter_sleep) {
        if (!is_enter_sleep) { // 睡眠退出时，重置数据
            last_sleep_pulse = false;
            lib_storage_ppg_data_reset();
        }
        lib_gm_calories_hr_pulse(timestamp, is_enter_sleep);
        is_sleep_stage = is_enter_sleep;
    }

    // 睡眠期间且每次sleepStagePpgOnOffOut状态变化时，做心率和ppg原始数据订阅脉冲调制
    if (!is_enter_sleep || last_sleep_pulse == sleep_pulse) {
        return;
    }
    last_sleep_pulse = sleep_pulse;
    lib_storage_ppg_data_reset();
    optional_config_t config = { .sampling_rate = 0,};
    if (sleep_pulse) {
        lib_gomore_sub_chg(false, DATA_ID_ALGO_HEART_RATE, algo_gm_hr_in_callback, &config); // ppg hr
        config.sampling_rate = 25; // ppg采样率
        lib_gomore_sub_chg(false, DATA_ID_RAW_PPG_HR, algo_ppg_in_callback, &config); // ppg
        config.sampling_rate = 0; // ppi采样率
        lib_gomore_sub_chg(false, DATA_ID_ALGO_PPG_HRV, algo_ppi_in_callback, &config); // ppi sub
    } else {
        lib_gomore_sub_chg(true, DATA_ID_ALGO_HEART_RATE, algo_gm_hr_in_callback, &config); // ppg hr
        config.sampling_rate = 25; // ppg采样率
        lib_gomore_sub_chg(true, DATA_ID_RAW_PPG_HR, algo_ppg_in_callback, &config); // ppg
        config.sampling_rate = 0; // ppi采样率
        lib_gomore_sub_chg(true, DATA_ID_ALGO_PPG_HRV, algo_ppi_in_callback, &config); // ppi unsub
    }

    // 静息心率算法需要恢复对ppg数据的订阅
    if (s_call_cbk[GM_RESTING_HR] != NULL) {
        lib_gomore_sub_chg(false, DATA_ID_ALGO_HEART_RATE, algo_gm_hr_in_callback, &config); // ppg hr
        config.sampling_rate = 25; // ppg采样率
        lib_gomore_sub_chg(false, DATA_ID_RAW_PPG_HR, algo_ppg_in_callback, &config); // ppg
    }
}

/**
 * @brief 用户活动状态变化通知
 *
 * @param active_type 通知的数据
 */
static void lib_gm_activite_type_chg_notify(int8_t active_type)
{
    static int8_t last_active_type = -99; // 初始值设置为-99，确保第一次变化时通知
    if (last_active_type == active_type)
    {
        return;
    }
    algo_active_type_pub_t pub_data;
    pub_data.active_type = active_type;
    ALGO_COMP_LOG_D("lib_gm:act_type chg active_type:%u", pub_data.active_type);

    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_EVENT_ACTIVETYPE_CHG, &pub_data, sizeof(algo_active_type_pub_t)) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("lib_gm:act_type pub");
    }
    last_active_type = active_type;
    active_type_set(active_type);
}

/**
 * @brief 计步回调
 *
 * @param gm_ouput 回调数据
 */
static void lib_gm_step_count_callback_proc(const IndexIO *gm_ouput)
{
    algo_step_count_pub_t algo_step_count;
    algo_step_count.step_count = gm_ouput->stepCountOut;
#ifdef CONFIG_PILING_FAKE
    algo_step_count.step_count = get_piling_step();
#endif
    algo_step_count.timestamp = gm_ouput->timestamp;
    s_call_cbk[GM_STEP_COUNT](&algo_step_count, sizeof(algo_step_count_pub_t));
}

/**
 * @brief 日常卡路里回调
 *
 * @param gm_ouput 回调数据
 */
static void lib_gm_calories_callback_proc(const IndexIO *gm_ouput)
{
    if (gm_ouput->activityKcalOut <= 0)
    {
        return;
    }
    algo_calories_pub_t algo_calories;
    algo_calories.calories = gm_ouput->activityKcalOut;
#ifdef CONFIG_PILING_FAKE
    algo_calories.calories = get_piling_calorie();
#endif
    algo_calories.timestamp = gm_ouput->timestamp;
    s_call_cbk[GM_CALORIES](&algo_calories, sizeof(algo_calories_pub_t));
}

/**
 * @brief 强度活动时长回调
 *
 * @param gm_ouput 回调数据
 */
static void lib_gm_intense_time_callback_proc(const IndexIO *gm_ouput)
{
    algo_intense_time_pub_t algo_intense;
    algo_intense.is_vaild = true;
    if (gm_ouput->METsOut[2] <= 0 && gm_ouput->METsOut[3] <= 0 && gm_ouput->METsOut[4] <= 0)
    {
        algo_intense.is_vaild = false;
    }
#ifdef CONFIG_PILING_FAKE
    algo_intense.is_vaild = true;
#endif
    ALGO_COMP_LOG_D("lib_gm:IT wristOff:%u,hrRef:%.2f,METsOut[1]:%.2f,METsOut[2]:%.2f,METsOut[3]:%.2f,METsOut[4]:%.2f",
        gm_ouput->wristOff, gm_ouput->hrRef, gm_ouput->METsOut[1], gm_ouput->METsOut[2], gm_ouput->METsOut[3], gm_ouput->METsOut[4]);
    algo_intense.timestamp = gm_ouput->timestamp;
    s_call_cbk[GM_INTENSE_TIME](&algo_intense, sizeof(algo_intense_time_pub_t));
}

/**
 * @brief 睡眠阶段回调
 *
 * @param gm_ouput 回调数据
 */
static void lib_gm_sleep_stage_callback_proc(const IndexIO *gm_ouput)
{
    ALGO_COMP_LOG_D("lib_gm:sleep timestamp:%u sleepPeriodStatusOut:0x%x,sleepStagePpgOnOffOut:%d",
        gm_ouput->timestamp, gm_ouput->sleepPeriodStatusOut, gm_ouput->sleepStagePpgOnOffOut);
    lib_gomore_sleep_stage(gm_ouput->timestamp, s_sleep_stage_sta, gm_ouput->sleepStagePpgOnOffOut);
    if (gm_ouput->sleepPeriodStatusOut & 0x02) { // 睡眠开始
        ALGO_COMP_LOG_I("lib_gm:sleep timestamp:%u sleepPeriodStatusOut:0x%x,sleepStagePpgOnOffOut:%d",
            gm_ouput->timestamp, gm_ouput->sleepPeriodStatusOut, gm_ouput->sleepStagePpgOnOffOut);
        s_sleep_stage_sta = true;
        set_user_sleep_status(true);
        algo_sleep_stage_pub_t sleep_stage = {0};
        sleep_stage.timestamp = gm_ouput->timestamp;
        sleep_stage.event_type = SLEEP_ENTER_EVENT;
        s_call_cbk[GM_SLEEP_STAGE](&sleep_stage, sizeof(algo_sleep_stage_pub_t));
    } else if (gm_ouput->sleepPeriodStatusOut & 0x04) { // 睡眠结束
        ALGO_COMP_LOG_I("lib_gm:sleep timestamp:%u sleepPeriodStatusOut:0x%x,sleepStagePpgOnOffOut:%d",
            gm_ouput->timestamp, gm_ouput->sleepPeriodStatusOut, gm_ouput->sleepStagePpgOnOffOut);
        s_sleep_stage_sta = false;
        set_user_sleep_status(false);
        algo_sleep_stage_pub_t sleep_stage = {0};
        sleep_stage.timestamp = gm_ouput->timestamp;
        sleep_stage.event_type = SLEEP_EXIT_EVENT;
        s_call_cbk[GM_SLEEP_STAGE](&sleep_stage, sizeof(algo_sleep_stage_pub_t));
        if (gm_ouput->sleepPeriodStatusOut & 0x08) { // 睡眠周期有效,则输出睡眠周期总结数据
            ALGO_COMP_LOG_I("lib_gm:sleep timestamp:%u sleepPeriodStatusOut:0x%x,sleepStagePpgOnOffOut:%d",
                gm_ouput->timestamp, gm_ouput->sleepPeriodStatusOut, gm_ouput->sleepStagePpgOnOffOut);
            SleepSummaryOutput sleepOutInfo = {0};
            memset(s_sleep_stages, 0, GOMORE_STAGES_LENTH);
            sleepOutInfo.stages = s_sleep_stages;
            int16_t ret = getEmbeddedSleepSummary(0, &sleepOutInfo);
            if (ret != 0)
            {
                ALGO_COMP_LOG_E("lib_gm:sleep ret:%d", ret);
                return;
            }
            if ((sleepOutInfo.type == GM_LONG_SLEEP && sleepOutInfo.totalSleepTime < LONG_SLEEP_VAILD_TIME) ||
                (sleepOutInfo.type == GM_SHORT_SLEEP && sleepOutInfo.endTS - sleepOutInfo.startTS < SHORT_SLEEP_VAILD_TIME))
            {
                ALGO_COMP_LOG_I("lib_gm:sleep sleepOutInfo.type:%d totalSleepTime:%f, endTS:%d,startTS:%d,diif_time:%u",
                    sleepOutInfo.type, sleepOutInfo.totalSleepTime, sleepOutInfo.endTS, sleepOutInfo.startTS, sleepOutInfo.endTS - sleepOutInfo.startTS);
                return;
            }
            algo_gomore_collect_sleep_end(&sleepOutInfo);
            memset(&sleep_stage, 0, sizeof(algo_sleep_stage_pub_t));
            sleep_stage.timestamp = gm_ouput->timestamp;
            sleep_stage.event_type = SLEEP_SUMMARY_EVENT;
            sleep_stage.sleepStageOut = sleepOutInfo;
            s_call_cbk[GM_SLEEP_STAGE](&sleep_stage, sizeof(algo_sleep_stage_pub_t));
        }
    }
}

/**
 * @brief qw自研静息心率回调:此处输出心率值给自研算法
 *
 * @param gm_ouput 回调数据
 */
static void lib_gm_rhr_qw_callback_proc(const IndexIO *gm_ouput)
{
    algo_rest_hr_pub_t rest_hr;
    rest_hr.timestamp = gm_ouput->timestamp;
    rest_hr.hr = gm_ouput->hrRef;
    s_call_cbk[GM_RESTING_HR_QW](&rest_hr, sizeof(algo_rest_hr_pub_t));
    ALGO_COMP_LOG_D("lib_gm:hrRef = %.2f", gm_ouput->hrRef);
}

/**
 * @brief 静息心率回调
 *
 * @param gm_ouput 回调数据
 */
static void lib_gm_resting_hr_callback_proc(const IndexIO *gm_ouput)
{
    if (gm_ouput->hrRestEstStatusOut != 1) {
        return;
    }
    algo_rest_hr_pub_t rest_hr;
    rest_hr.timestamp = gm_ouput->timestamp;
    rest_hr.hr = gm_ouput->hrRestOut;
    s_call_cbk[GM_RESTING_HR](&rest_hr, sizeof(algo_rest_hr_pub_t));
    // int16_t ret = setRestHrStartOneTimeMeasurement();
    // if (ret != 0) {
    //     ALGO_COMP_LOG_E("lib_gm:setRestHr failed, ret = %d", ret);
    // }
    ALGO_COMP_LOG_I("lib_gm:RestHr = %.2f", gm_ouput->hrRestOut);
}

/**
 * @brief gomore fitness算法输出时回调
 *
 * @param gm_ouput 输出数据
 */
static void lib_gm_fitness_callback_proc(const IndexIO *gm_ouput)
{
#ifdef GOMORE_FITNESS_CALLBACK
    // 锻炼结束不上报实时数据
    if (s_workout_type == 0)
    {
        return;
    }
    algo_fitness_pub_t fitness_rtdata = {0};

    // haeder
    fitness_rtdata.sport_type = s_workout_type;
    fitness_rtdata.data_type = FITNESS_REAKTIME_DATA;
    fitness_rtdata.data_len = sizeof(fitness_rtdata_t);

    // data
    fitness_rtdata.data.rt_data.speedOut = gm_ouput->speedOut;
    fitness_rtdata.data.rt_data.stepCountOut = gm_ouput->stepCountOut;
    fitness_rtdata.data.rt_data.kcalOut = gm_ouput->kcalOut;
    fitness_rtdata.data.rt_data.fatOut = gm_ouput->fatOut;
    fitness_rtdata.data.rt_data.carbOut = gm_ouput->carbOut;
    fitness_rtdata.data.rt_data.activityKcalOut = gm_ouput->activityKcalOut;
    fitness_rtdata.data.rt_data.cadenceOut = gm_ouput->cadenceOut;
    fitness_rtdata.data.rt_data.fitnessOut = gm_ouput->fitnessOut;
    fitness_rtdata.data.rt_data.fitnessNotifierGPS = gm_ouput->fitnessNotifierGPSOut;

    s_call_cbk[GM_FITNESS](&fitness_rtdata, sizeof(algo_fitness_pub_t));
#endif
}

/**
 * @brief 负荷趋势输出数据准备
 *
 * @param tl_trend 负荷趋势数据输出
 * @return int32_t 0表示成功，-1表示失败
 */
int32_t lib_get_tl_trend(float* tl_trend)
{
    if(tl_trend == NULL)
    {
        ALGO_COMP_LOG_E("tl_trend is NULL");
        return -1;
    }

    //运动模式下，不进行负荷趋势输出
    if(s_workout_type != 0)
    {
        ALGO_COMP_LOG_I("tl_trend: in workout");
        return -1;
    }

    struct StartFitnessInput startIn;
    memset(&startIn, 0, sizeof(struct StartFitnessInput));
    startIn.fitness.rtc = (uint32_t)get_sec_from_rtc();
    startIn.fitness.timeZoneOffset = tz_get()->tz_minuteswest;
    startIn.workoutType = 0;

    //非运动模式下继续输出负荷趋势操作
    int16_t ret_start = startFitness(&startIn);
    ALGO_COMP_LOG_I("startFitness,workout_type:%d,rtc:%u,timeZoneOffset:%d,ret_start:%d",
        startIn.workoutType, startIn.fitness.rtc, startIn.fitness.timeZoneOffset, ret_start);
    if (ret_start != 0)
    {
        ALGO_COMP_LOG_E("startFitness: open failed");
        return -1;
    }

    struct EndFitnessInput mfitness;
    struct EndFitnessOutput summary;
    memset(&mfitness, 0, sizeof(struct EndFitnessInput));
    memset(&summary, 0, sizeof(struct EndFitnessOutput));
    mfitness.fitness.hisOP = 1;
    mfitness.fitness.workoutDate[0] = (uint32_t)get_sec_from_rtc();

    int16_t ret = endFitness(&mfitness, &summary);
    if (ret != 0)
    {
        ALGO_COMP_LOG_E("endFitness failed: %d", ret);
        return -1;
    }
    *tl_trend = summary.fitness.tlTrend[0];
    return 0;
}

/**
 * @brief gomore 全天体力算法输出时回调
 *
 * @param gm_ouput 输出数据
 */
static void lib_gm_alldaystamina_callback_proc(const IndexIO *gm_ouput)
{
    if (gm_ouput->allDayStaminaOut == -100) { // 无效值
        return;
    }
    algo_alldaystamina_pub_t algo_alldaystamina = {0};
    algo_alldaystamina.alldaystamina = gm_ouput->allDayStaminaOut;
    algo_alldaystamina.timestamp = gm_ouput->timestamp;
    s_call_cbk[GM_ALLDAYSTAMINA](&algo_alldaystamina, sizeof(algo_alldaystamina_pub_t));
}

/**
 * @brief gomore 压力分数算法输出时回调
 *
 * @param gm_ouput 输出数据
 */
static void lib_gm_stress_score_callback_proc(const IndexIO *gm_ouput)
{
    if (gm_ouput->stressOut == -100) { // 无效值
        return;
    }
    if (s_stress_test_mode) {
        int32_t diff_time = get_boot_sec() - s_cur_click_boot_time;
        if(diff_time < 0) {
            s_cur_click_boot_time = get_boot_sec();
            return;
        } else if (diff_time < 20) { // 压力点测模式20s之后才生效
            return;
        }
        s_cur_click_boot_time = 0;
    }
    algo_stress_score_pub_t stress_score = {0};
    stress_score.stress_out = gm_ouput->stressOut;
#ifdef CONFIG_PILING_FAKE
    stress_score.stress_out = get_piling_stress();
#endif
    stress_score.timestamp = gm_ouput->timestamp;
    s_call_cbk[GM_STRESS_SCORE](&stress_score, sizeof(algo_stress_score_pub_t));
}

/**
 * @brief gomore HRV评估算法输出时回调
 *
 * @param gm_ouput 输出数据
 */
static void lib_gm_hrv_evaluate_callback_proc(const IndexIO *gm_ouput)
{
    if (gm_ouput->hrvOut == 0 || gm_ouput->hrvOut > 130) { // 无效值
        return;
    }
    algo_hrv_evaluate_pub_t hrv_pub = {0};
    hrv_pub.hrv_evaluate = gm_ouput->hrvOut;
    hrv_pub.timestamp = gm_ouput->timestamp;
    s_call_cbk[GM_HRV_TIME](&hrv_pub, sizeof(algo_hrv_evaluate_pub_t));
}

/**
 * @brief gomore算法各个数据输出时回调
 *
 * @param gm_ouput 输出数据
 */
static void lib_gm_callback_proc(const IndexIO *gm_ouput)
{
    // 卡路里和活动时长心率输入脉冲调制
    lib_gm_calories_hr_pulse(gm_ouput->timestamp, s_sleep_stage_sta);

    lib_gm_activite_type_chg_notify(gm_ouput->activeTypeOut);

    static uint32_t count = 0;
    count++;
    if (count > 60) // 每隔1min打印一次
    {
        ALGO_COMP_LOG_E("lib_gm:GM_DBG stepCountOut:%.2f,wristOff:%u,timestamp:%u",
            gm_ouput->stepCountOut, gm_ouput->wristOff, gm_ouput->timestamp);
        count = 0;
    }

    // 算法各个数据回调
    for(uint8_t i = 0; i < GM_DATA_TYPE_MAX; i++)
    {
        if (LIB_GET_BIT(s_gm_map, i) == 0 || s_call_cbk[i] == NULL)
        {
            continue;
        }
        if (i == GM_STEP_COUNT)
        {
            lib_gm_step_count_callback_proc(gm_ouput);
        }
        else if (i == GM_CALORIES)
        {
            lib_gm_calories_callback_proc(gm_ouput);
        }
        else if (i == GM_INTENSE_TIME)
        {
            lib_gm_intense_time_callback_proc(gm_ouput);
        }
        else if (i == GM_SLEEP_STAGE)
        {
            lib_gm_sleep_stage_callback_proc(gm_ouput);
        }
        else if (i == GM_RESTING_HR)
        {
            lib_gm_resting_hr_callback_proc(gm_ouput);
        }
        else if (i == GM_FITNESS)
        {
            lib_gm_fitness_callback_proc(gm_ouput);
        }
        else if (i == GM_ALLDAYSTAMINA)
        {
            lib_gm_alldaystamina_callback_proc(gm_ouput);
        }
        else if (i == GM_STRESS_SCORE)
        {
            lib_gm_stress_score_callback_proc(gm_ouput);
        }
        else if (i == GM_HRV_TIME)
        {
            lib_gm_hrv_evaluate_callback_proc(gm_ouput);
        }
        else if (i == GM_RESTING_HR_QW)
        {
            lib_gm_rhr_qw_callback_proc(gm_ouput);
        }
        else
        {
            ALGO_COMP_LOG_E("lib_gm:gm_data_type:%d", i);
        }
    }
}

/**
 * @brief 系统速度写入缓存
 *
 * @param sdata 系统速度输出数据
 * @param len 系统速度输出数据长度
 */
static void lib_storage_sys_speed_input_write(const void *sdata, uint32_t len)
{
    algo_speed_pub_t *sys_speed = (algo_speed_pub_t *)sdata;
    float speed = -1; // -1:速度异常或默认值

    // 速度不是默认值并且速度不是源于gm:enum_spd_source_gm
    if (sys_speed->enhanced_speed != 0xffffffff && sys_speed->spd_source != enum_spd_source_gm) { // 0xffffffff:速度异常
        speed = MM_PER_S_TO_KM_H * sys_speed->enhanced_speed;
    }
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    s_sys_speed = speed;
    rt_mutex_release(s_input_mutex);
    ALGO_COMP_LOG_D("lib_gm:in speed:%umm/s,%.2fkm/h", sys_speed->enhanced_speed, s_sys_speed);
#ifdef RT_USING_FINSH
    // 模拟喂速度仿真
    if (s_speed_simulate_val) {
        lib_gm_sim_speed_input();
    }
#endif
}

/**
 * @brief 系统速度读取缓存
 *
 * @param speed 系统速度值
 */
static void lib_storage_sys_speed_input_read(float *speed)
{
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    *speed = s_sys_speed;
    rt_mutex_release(s_input_mutex);
}

/**
 * @brief 高度写入缓存
 *
 * @param sdata 高度输出数据
 * @param len 高度输出数据长度
 */
static void lib_storage_altitude_input_write(const void *sdata, uint32_t len)
{
    algo_altitude_pub_t *data = (algo_altitude_pub_t *)sdata;
    float altitude = CM_TO_M * data->altitude;
    if (altitude < -1000 || altitude > 10000) // 高度异常
        altitude = -999;
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    s_altitude = altitude;
    rt_mutex_release(s_input_mutex);
    ALGO_COMP_LOG_D("lib_gm:in altitude:%ucm,%.2fkm", data->altitude, s_altitude);
}

/**
 * @brief 高度读取缓存
 *
 * @param altitude 高度值
 */
static void lib_storage_altitude_input_read(float *altitude)
{
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    *altitude = s_altitude;
    rt_mutex_release(s_input_mutex);
}

/**
 * @brief 功率写入缓存
 *
 * @param sdata 功率输出数据
 * @param len 功率输出数据长度
 */
static void lib_storage_power_input_write(const void *sdata, uint32_t len)
{
    algo_power_pub_t *data = (algo_power_pub_t *)sdata;
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    if (data->power <= 2000) // 功率正常
        s_cycling_power = (float)data->power;
    else
        s_cycling_power = -1; // 默认值，表示power无效
    rt_mutex_release(s_input_mutex);
    ALGO_COMP_LOG_D("lib_gm:in power:%uw,%.2fw", data->power, s_cycling_power);
}

/**
 * @brief 功率读取缓存
 *
 * @param power 功率值
 */
static void lib_storage_power_input_read(float *power)
{
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    *power = s_cycling_power;
    rt_mutex_release(s_input_mutex);
}

/**
 * @brief 踏频写入缓存
 *
 * @param sdata 踏频输出数据
 * @param len 踏频输出数据长度
 */
static void lib_storage_cadence_input_write(const void *sdata, uint32_t len)
{
    algo_cadence_pub_t *data = (algo_cadence_pub_t *)sdata;
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    if (data->cadence <= 250) // 踏频异常
        s_cycling_cadence = (float)data->cadence;
    else
        s_cycling_cadence = -1; // 默认值，表示cadence无效
    rt_mutex_release(s_input_mutex);
    ALGO_COMP_LOG_D("lib_gm:in cadence:%urpm,%.2frpm", data->cadence, s_cycling_cadence);
}

/**
 * @brief 踏频读取缓存
 *
 * @param cadence 踏频值
 */
static void lib_storage_cadence_input_read(float *cadence)
{
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    *cadence = s_cycling_cadence;
    rt_mutex_release(s_input_mutex);
}

/**
 * @brief ppg心率写入缓存
 *
 * @param sdata ppg心率输出数据
 * @param len ppg心率输出数据长度
 */
static void lib_storage_hr_input_write(const void *sdata, uint32_t len)
{
    const uint8_t *algo_heart_rate = (const uint8_t *)sdata;
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    s_ppg_hr = *algo_heart_rate;
    rt_mutex_release(s_input_mutex);
#ifdef RT_USING_FINSH
    // 模拟喂HR仿真
    if (s_hr_simulate_val) {
        lib_gm_sim_hr_input();
    }
#endif
    ALGO_COMP_LOG_D("lib_gm:in hr:%u,%u", *algo_heart_rate, s_ppg_hr);
}

/**
 * @brief ppg心率读取缓存
 *
 * @param ppg_hr ppg心率值
 */
static void lib_storage_hr_input_read(float *ppg_hr)
{
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    *ppg_hr = (float)s_ppg_hr;
    rt_mutex_release(s_input_mutex);
}

/**
 * @brief ppg原始值写入缓存
 *
 * @param sdata ppg原始值
 * @param len ppg原始值长度
 */
static void lib_storage_ppg_input_write(const void *sdata, uint32_t len)
{
    struct sensor_ppg *ppg_raw_pub = (struct sensor_ppg *)sdata;
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    if (g_ppg_queue.size == PPG_RAM_LENGTH) {
        ppgDequeue(&g_ppg_queue);
        s_start_update_ppg_input = true;
    }
    ppgEnqueue(&g_ppg_queue, (float)ppg_raw_pub->ppg[0], (uint32_t)get_sec_from_rtc());
    rt_mutex_release(s_input_mutex);
}

/**
 * @brief ppg原始值读取缓存
 *
 * @param ppg_raw ppg原始值
 * @param len ppg原始值长度
 * @return true：读到有效值 false：没有读到有效值
 */
static bool lib_storage_ppg_input_read(float *ppg_raw, uint32_t len)
{
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    if (len != GOMORE_WELLNESS_PPG_FRAME || g_ppg_queue.size < GOMORE_WELLNESS_PPG_FRAME || !s_start_update_ppg_input) {
        ALGO_COMP_LOG_D("lib_gm:in len:%d,g_ppg_queue.size:%d,ppg_buff_ok:%u",
            len, g_ppg_queue.size, s_start_update_ppg_input);
        rt_mutex_release(s_input_mutex);
        return false;
    }

    // 备份PPG数据和时间戳
    memcpy(ppg_raw, g_ppg_queue.data, sizeof(float) * GOMORE_WELLNESS_PPG_FRAME);
    int32_t i = len;
    while (i--)
        ppgDequeue(&g_ppg_queue);
    rt_mutex_release(s_input_mutex);
    return true;
}

/**
 * @brief 获取ppi原始值置信度缓存
 *
 * @return uint8_t 置信度
 */
uint8_t lib_storage_ppi_confidence_get(void)
{
    return s_ppi_data_bak.confidence;
}

/**
 * @brief ppi原始值写入缓存
 *
 * @param sdata ppi原始值
 * @param len ppi原始值长度
 */
static void lib_storage_ppi_input_write(const void *sdata, uint32_t len)
{
    qw_ppg_algo_hrv_t *ppi_raw = (qw_ppg_algo_hrv_t *)sdata;
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    ALGO_COMP_LOG_D("lib_gm:ppi rriNum:%u,rri:%u", ppi_raw->rriNum, ppi_raw->rri[ppi_raw->rriNum - 1]);
#if 0
    if (ppi_raw->confidence < 50) {
        memset(&s_ppi_data, 0, sizeof(lib_gm_ppi_t));
        rt_mutex_release(s_input_mutex);
        return;
    }
#endif
    uint8_t ppi_vaild_num = 0;
    for (uint8_t i = 0; i < ppi_raw->rriNum; i++)
    {
        if (ppi_raw->rri[i] > 0xffff || ppi_raw->rri[i] == 0) // 超过uint16_t最大值,并过滤掉0值
        {
            continue;
        }
        else
        {
            s_ppi_data.data[ppi_vaild_num] = ppi_raw->rri[i];
            ppi_vaild_num++;
        }
    }
    s_ppi_data.size = ppi_vaild_num;
    s_ppi_data.confidence = ppi_raw->confidence;
    rt_mutex_release(s_input_mutex);
}

/**
 * @brief ppi原始值读取缓存
 *
 * @param ppi_raw ppi原始值
 * @param len ppi数据长度
 */
static void lib_storage_ppi_input_read(uint16_t **ppi_raw, uint8_t *len)
{
    // 备份PPi数据和时间戳
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    if (s_ppi_data.size == 0)
    {
        *ppi_raw = NULL;
        *len = 0;
        rt_mutex_release(s_input_mutex);
        return;
    }
    memcpy(&s_ppi_data_bak, &s_ppi_data, sizeof(lib_gm_ppi_t));
    rt_mutex_release(s_input_mutex);
    *ppi_raw = s_ppi_data_bak.data;
    *len = s_ppi_data_bak.size;
}

/**
 * @brief 加速度原始值写入缓存
 *
 * @param frame_len 数据帧长度
 * @param water_len 水位长度
 * @param accel     加速度数据
 *
 * @return true  满足进行算法triger条件
 * @return false 不满足进行算法triger条件
 */
static bool lib_storage_acc_input_push(uint32_t frame_len, uint32_t water_len, const struct sensor_accel *accel)
{
#if (ALGO_GM_DEBUG_ENABLE)
    static uint32_t count = 0;
#endif
    accEnqueue(&s_acc_queue, accel->x, accel->y, accel->z); // 将数据缓存到队列
    // float val = sin_over_samples();
    // accEnqueue(&s_acc_queue, val, val, val);
    uint32_t current_utc = accel->timestamp / 1000; // 获取当前acc时间戳,单位s
    ALGO_COMP_LOG_D("lib_gm:acc enter frame_len:%u,current_utc:%u,last_time:%u,size:%u",
        frame_len, current_utc, s_acc_queue.timestamp, s_acc_queue.size);
    if (s_acc_queue.timestamp == 0) { // 第一次喂数据
        s_acc_queue.timestamp = current_utc;
    } else if (current_utc > s_acc_queue.timestamp) {
        ALGO_COMP_LOG_D("lib_gm:acc 1s frame_len:%u,current_utc:%u,last_time:%u,size:%u",
            frame_len, current_utc, s_acc_queue.timestamp, s_acc_queue.size);
#if (ALGO_GM_DEBUG_ENABLE)
        count++;
        if (count > 60) // 每隔1min打印一次
        {
            ALGO_COMP_LOG_W("lib_gm:acc frame_len:%u,time:%u,size:%d",
                frame_len, current_utc, s_acc_queue.size);
            count = 0;
        }
#endif
        s_acc_queue.timestamp = current_utc;

        // 1s时间到，取循环队列里的数据，进入算法实体处理
        initAccQueue(&s_acc_queue_bak); // 清空备份队列
        uint32_t real_count = accDequeue(&s_acc_queue, &s_acc_queue_bak, s_acc_queue.size); // 取acc数据
        s_acc_queue_bak.timestamp = current_utc;
        if (real_count < frame_len) {
            ALGO_COMP_LOG_W("lib_gm:acc underflow frame_len:%u,current_utc:%u,real_count:%u,size:%u",
                frame_len, current_utc, real_count, s_acc_queue.size);
        }
        ALGO_COMP_LOG_D("lib_gm:acc frame_len:%u,current_utc:%u,real_count:%u,size:%u",
            frame_len, current_utc, real_count, s_acc_queue.size);
        if (s_workout_type != 0)
        {   // fitness同步传gyro数据给算法
            initAccQueue(&g_gyro52_queue_bak); // 清空备份队列
            uint32_t read_count = g_gyro52_queue.size > s_acc_queue_bak.size ? s_acc_queue_bak.size : g_gyro52_queue.size; // 取gyro数据,与acc一一对应
            real_count = accDequeue(&g_gyro52_queue, &g_gyro52_queue_bak, read_count); // 取gyro数据
            g_gyro52_queue_bak.timestamp = current_utc;
            if (real_count < frame_len)
            {
                ALGO_COMP_LOG_W("lib_gm:gyro underflow  frame_len:%u,current_utc:%u,size:%u",
                    frame_len, current_utc, real_count);
            }
            if (s_workout_type == WORKOUT_SWIMMING) // 游泳需要mag50
            {
                initAccQueue(&g_mag50_queue_bak); // 清空备份队列
#ifdef MAG_USE_PUB_GET_DATA
                read_count = g_mag50_queue.size > GOMORE_FITNESS_ACC_FRAME ? GOMORE_FITNESS_ACC_FRAME : g_mag50_queue.size; // 取mag50数据
                real_count = accDequeue(&g_mag50_queue, &g_mag50_queue_bak, read_count); // 取mag50数据
                g_mag50_queue_bak.timestamp = current_utc;
#else
                read_count = 0;
                mag_fifo_data_t mag_fifo_data[GOMORE_FITNESS_ACC_FRAME] = {0};
                if (0 != qmc6308_read_fifo_data(mag_fifo_data, GOMORE_FITNESS_ACC_FRAME, &read_count))
                {
                    ALGO_COMP_LOG_E("lib_gm:qmc6308_read_fifo_data fail");
                }
                else
                {
                    ALGO_COMP_LOG_D("lib_gm:qmc6308_read_fifo_data read_count:%u,magX:%d", read_count, mag_fifo_data[0].x);
                    real_count = read_count;
                    for (uint32_t i = 0; i < real_count; i++)
                    {
                        g_mag50_queue_bak.x[i] = mag_fifo_data[i].x;
                        g_mag50_queue_bak.y[i] = mag_fifo_data[i].y;
                        g_mag50_queue_bak.z[i] = mag_fifo_data[i].z;
                    }
                    g_mag50_queue_bak.timestamp = current_utc;
                    g_mag50_queue_bak.size = real_count;
                }
#endif // MAG_USE_PUB_GET_DATA
                if (real_count < GOMORE_FITNESS_ACC_FRAME)
                {
                    ALGO_COMP_LOG_W("lib_gm:mag50 underflow frame_len:%u,current_utc:%u,size:%u",
                        GOMORE_FITNESS_ACC_FRAME, current_utc, real_count);
                }
            }
        }
        return true;
    } else if (current_utc < s_acc_queue.timestamp) { // 时间戳回退
        // 重置时间戳,解决时间同步问题
        ALGO_COMP_LOG_W("lib_gm:acc time sync last_utc:%u,current_utc:%u,size:%u",
            s_acc_queue.timestamp, current_utc, s_acc_queue.size);
        s_acc_queue.timestamp = current_utc;
    } else {
        ALGO_COMP_LOG_D("lib_gm:acc normal frame_len:%u,last_utc:%u,current_utc:%u,size:%u",
            frame_len, s_acc_queue.timestamp, current_utc, s_acc_queue.size);
        if (s_acc_queue.size > water_len) {
            ALGO_COMP_LOG_W("lib_gm:acc overflow frame_len:%u,last_utc:%u,current_utc:%u,size:%u",
                frame_len, s_acc_queue.timestamp, current_utc, s_acc_queue.size);
            // 解决wellness和fitness切换队列溢出问题
            // initAccQueue(&s_acc_queue);
        }
    }
    return false;
}

/**
 * @brief acc输入数据
 *
 * @param input_type 输入类型
 * @param sdata 数据
 * @param len 数据长度
 * @return true 成功
 * @return false 失败
 */
static bool lib_storage_acc_input_write(uint32_t input_type, const void *sdata, uint32_t len)
{
    const uint32_t size = sizeof(struct sensor_accel);
    const uint32_t cnt = len / size;
    struct sensor_accel *in_acc = (struct sensor_accel *)sdata;
    uint32_t frame_len = 0;
    uint32_t water_len = 0;
    bool ret = false;
    ALGO_COMP_LOG_D("lib_gm:acc normal len:%u,size:%u,cnt:%u", len, size, cnt);
    if (input_type == GM_INPUT_ACC_26) // 缓存acc26输入
    {
        ALGO_COMP_LOG_D("lib_gm:acc 26");
        frame_len = GOMORE_WELLNESS_ACC_FRAME;
        water_len = GOMORE_WELLNESS_BUFF_WATER;
    }
    else if (input_type == GM_INPUT_ACC_52) // 缓存acc52输入
    {
        ALGO_COMP_LOG_D("lib_gm:acc 52");
        frame_len = GOMORE_FITNESS_ACC_FRAME;
        water_len = GOMORE_FITNESS_BUFF_WATER;
    }
    else
    {
        ALGO_COMP_LOG_E("lib_gm:acc type:%u", input_type);
        return false;
    }
    for (uint32_t i = 0; i < cnt; i++)
    {
        if (lib_storage_acc_input_push(frame_len, water_len, (&(in_acc[i]))))
        {
            ret = true;
        }
    }
    return ret;
}

/** @brief 陀螺仪数据写入
 * @param  frame_len 采样帧长度
 * @param  water_len 水位线长度
 * @param  gyro      陀螺仪数据
 */
static void lib_storage_gyro_input_push(uint32_t frame_len, uint32_t water_len, const struct sensor_gyro *gyro)
{
    accEnqueue(&g_gyro52_queue, gyro->x * MDPS_TO_DPS, gyro->y * MDPS_TO_DPS, gyro->z * MDPS_TO_DPS); // 将数据缓存到队列
    ALGO_COMP_LOG_D("lib_gm:gyro last_utc:%u,size:%u", g_gyro52_queue.timestamp, g_gyro52_queue.size);
    if (g_gyro52_queue.size > water_len) {
        ALGO_COMP_LOG_W("lib_gm:gyro overflow last_utc:%u,size:%u", g_gyro52_queue.timestamp, g_gyro52_queue.size);
        // 重置时间戳,解决时间同步队列溢出问题
        initAccQueue(&g_gyro52_queue);
    }
}

/**
 * @brief gyro 输入数据
 *
 * @param input_type 输入类型
 * @param sdata 数据
 * @param len 数据长度
 */
static void lib_storage_gyro_input_write(uint32_t input_type, const void *sdata, uint32_t len)
{
    const uint32_t size = sizeof(struct sensor_gyro);
    const uint32_t cnt = len / size;
    struct sensor_gyro *in_gyro = (struct sensor_gyro *)sdata;
    uint32_t frame_len = 0;
    uint32_t water_len = 0;
    if (input_type == GM_INPUT_GYRO_26) // 缓存gyro26输入
    {
        ALGO_COMP_LOG_D("lib_gm:gyro 26");
        frame_len = GOMORE_WELLNESS_ACC_FRAME;
        water_len = GOMORE_WELLNESS_BUFF_WATER;
    }
    else if (input_type == GM_INPUT_GYRO_52) // 缓存gyro52输入
    {
        ALGO_COMP_LOG_D("lib_gm:gyro 52");
        frame_len = GOMORE_FITNESS_ACC_FRAME;
        water_len = GOMORE_FITNESS_BUFF_WATER;
    }
    else
    {
        ALGO_COMP_LOG_E("lib_gm:gyro type:%u", input_type);
        return;
    }
    for (uint32_t i = 0; i < cnt; i++)
    {
        lib_storage_gyro_input_push(frame_len, water_len, (&(in_gyro[i])));
    }
}

/**
 * @brief mag50 输入数据
 *
 * @param input_type 输入类型
 * @param sdata 数据
 * @param len 数据长度
 */
static void lib_storage_mag50_input_write(uint32_t input_type, const void *sdata, uint32_t len)
{
    const uint32_t size = sizeof(struct sensor_mag);
    const uint32_t cnt = len / size;
    struct sensor_mag *in_mag = (struct sensor_mag *)sdata;
    for (uint32_t i = 0; i < cnt; i++)
    {
        // static uint64_t last_time = 0;
        // uint32_t diff = (uint32_t)(in_mag[i].timestamp - last_time);
        // last_time = in_mag[i].timestamp;
        // ALGO_COMP_LOG_I("lib_gm:mag50 timestamp:%u.%u,diff:%u",(uint32_t)(in_mag[i].timestamp / 1000), (uint32_t)(in_mag[i].timestamp % 1000), diff);
        accEnqueue(&g_mag50_queue, in_mag[i].x, in_mag[i].y, in_mag[i].z); // 将数据缓存到队列
        ALGO_COMP_LOG_D("lib_gm:mag50 last_utc:%u,size:%u", g_gyro52_queue.timestamp, g_gyro52_queue.size);
        if (g_mag50_queue.size > GOMORE_FITNESS_BUFF_WATER)
        {
            ALGO_COMP_LOG_W("lib_gm:mag50 overflow last_utc:%u,size:%u", g_gyro52_queue.timestamp, g_gyro52_queue.size);
            // 重置时间戳,解决时间同步队列溢出问题
            initAccQueue(&g_mag50_queue);
        }
    }
}

/**
 * @brief 缓存算法输入数据到数据队列
 *
 * @param input_type gomore输入数据类型
 * @param data 算法输入数据
 * @param len 算法输入数据长度
 * @return true ： 缓存完成，进入算法实体处理  false : 缓存未完成
 */
static bool lib_storage_input_to_buff(uint32_t input_type, const void *sdata, uint32_t len)
{
    if (input_type == GM_INPUT_ACC_26 || input_type == GM_INPUT_ACC_52) // 缓存acc52输入
    {
        return lib_storage_acc_input_write(input_type, sdata, len);
    }
    else if (input_type == GM_INPUT_GYRO_26 || input_type == GM_INPUT_GYRO_52) // 缓存gyro52输入
    {
        // 只做缓存，不triger算法,不返回值
        lib_storage_gyro_input_write(input_type, sdata, len);
    }
    else if (input_type == GM_INPUT_MAG_50) // 缓存mag50输入
    {
        // 只做缓存，不triger算法,不返回值
        lib_storage_mag50_input_write(input_type, sdata, len);
    }
    else if (input_type == GM_INPUT_SPEED && sizeof(algo_speed_pub_t) == len) // 缓存速度输入
    {
        lib_storage_sys_speed_input_write(sdata, len);

    }
    else if (input_type == GM_INPUT_ALTITUDE && sizeof(algo_altitude_pub_t) == len) // 缓存高度输入
    {
        lib_storage_altitude_input_write(sdata, len);
    }
    else if (input_type == GM_INPUT_POWER && sizeof(algo_power_pub_t) == len) // 缓存功率输入
    {
        lib_storage_power_input_write(sdata, len);
    }
    else if (input_type == GM_INPUT_CADENCE && sizeof(algo_cadence_pub_t) == len) // 缓存踏频输入
    {
        lib_storage_cadence_input_write(sdata, len);
    }
    else if (input_type == GM_INPUT_HR && sizeof(uint8_t) == len) // 缓存心率输入
    {
        lib_storage_hr_input_write(sdata, len);
    }
    else if (input_type == GM_INPUT_PPG && sizeof(struct sensor_ppg) == len) // 缓存ppg输入
    {
        lib_storage_ppg_input_write(sdata, len);
    }
    else if (input_type == GM_INPUT_PPI && sizeof(qw_ppg_algo_hrv_t) == len) // 缓存ppi输入
    {
        lib_storage_ppi_input_write(sdata, len);
    }
    return false;
}

/**
 * @brief 输出acc缓存队列数据
 *
 * @param acc acc数据
 */
static void print_acc_queue(acc_queue_t *acc)
{
    if (acc->size == 0)
    {
        ALGO_COMP_LOG_D("lib_gm:acc is empty");
        return;
    }
    for (int i = 0; i < acc->size; i++)
    {
        ALGO_COMP_LOG_D("%.2f  ", acc->x[i]);
    }
    ALGO_COMP_LOG_D("\n");
}

#ifdef RT_USING_FINSH
#include <math.h>   // 包含sin函数的头文件

// 声明周期和振幅的常量
#define AMPLITUDE     1000.0                  // 正弦波的振幅
#define SAMPLING_RATE 25                      // 采样率（25Hz）
#define SAMPLE_PERIOD (1.0 / SAMPLING_RATE)   // 采样周期（1/采样率）
#define PI_           3.141592654

/**********************************************************************************
 * @function:sin_over_samples
 * @brief:根据采样点n计算正弦值
 * @param：none
 * @return：float 正弦值
***********************************************************************************/
static float sin_over_samples(void)
{
    // 将采样点n转换为时间t
    static uint32_t n = 0;
    float t = n % SAMPLING_RATE * SAMPLE_PERIOD;
    n++;

    // 计算并返回正弦值
    return AMPLITUDE * sin(t * PI_);
}

/**********************************************************************************
 * @function:lib_gm_sim_accel_input
 * @brief:模拟acc传感器输入
 * @param：none
 * @return：none
***********************************************************************************/
static void lib_gm_sim_accel_input(void)
{
    for (uint32_t i = 0; i < s_acc_queue_bak.size; i++)
    {
        s_acc_queue_bak.x[i] = sin_over_samples();
        s_acc_queue_bak.y[i] = s_acc_queue_bak.x[i];
        s_acc_queue_bak.z[i] = s_acc_queue_bak.x[i];
    }
}

/**********************************************************************************
 * @function:lib_gm_sim_hr_input
 * @brief:设置模拟心率值，用于测试
 * @param：none
 * @return：none
***********************************************************************************/
static void lib_gm_sim_hr_input(void)
{
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    s_ppg_hr = s_hr_simulate_val + rand() % 20;
    rt_mutex_release(s_input_mutex);
}

/**********************************************************************************
 * @function:lib_gm_sim_speed_input
 * @brief:设置模拟速度值，用于测试
 * @param：none
 * @return：none
***********************************************************************************/
static void lib_gm_sim_speed_input(void)
{
    rt_mutex_take(s_input_mutex, RT_WAITING_FOREVER);
    s_sys_speed = (float)s_speed_simulate_val  + rand() % 5;
    rt_mutex_release(s_input_mutex);
}
#endif

/**
 * @brief gomore算法喂数据入口
 *
 * @param input_type gomore输入数据类型
 * @param data 数据
 * @param len 数据长度
 */
void lib_gomore_feed(uint32_t input_type, const void *data, uint32_t len)
{
#ifdef RT_USING_FINSH
    // 模拟喂正弦ACC仿真
    if (s_acc_simulate_switch) {
        lib_gm_sim_accel_input();
    }

    // 模拟睡眠数据
    if (lib_gomore_sleep_period()) {
        ALGO_COMP_LOG_D("lib_gm:slp sim");
        return;
    }

    // 模拟睡眠数据
    if (lib_gm_fitness_test()) {
        ALGO_COMP_LOG_D("lib_gm:fitness sim");
        return;
    }
#endif
    if (DATA_ID_EVENT_TIME_SYNC == input_type)
    {
        lib_gm_reset();
        return;
    }
    IndexIO gm_input = {0};

    // acc数据启动一次算法调度
    gm_input.timeZoneOffset = tz_get()->tz_minuteswest;
    gm_input.wristOff = !algo_ppg_wear_get();
    print_acc_queue(&s_acc_queue_bak);
    ALGO_COMP_LOG_D("lib_gm:gm_map:0x%2x", s_gm_map);

    // 当开始fitness时喂入52Hz的acc数据,wellness喂26hz
    if (LIB_GET_BIT(s_gm_map, GM_FITNESS)) {
        gm_input.accX = s_acc_queue_bak.x;
        gm_input.accY = s_acc_queue_bak.y;
        gm_input.accZ = s_acc_queue_bak.z;
        gm_input.accLength = s_acc_queue_bak.size;
        gm_input.timestamp = s_acc_queue_bak.timestamp;
        gm_input.gyroX = g_gyro52_queue_bak.x;
        gm_input.gyroY = g_gyro52_queue_bak.y;
        gm_input.gyroZ = g_gyro52_queue_bak.z;
        gm_input.gyroLength = g_gyro52_queue_bak.size;
        lib_storage_sys_speed_input_read(&gm_input.speedRef);
        lib_storage_altitude_input_read(&gm_input.altitude);
        lib_storage_cadence_input_read(&gm_input.cyclingCadence);
        lib_storage_power_input_read(&gm_input.cyclingPower);

        // 游泳喂入地磁
        if (s_workout_type == WORKOUT_SWIMMING)
        {
            gm_input.magX = g_mag50_queue_bak.x;
            gm_input.magY = g_mag50_queue_bak.y;
            gm_input.magZ = g_mag50_queue_bak.z;
            gm_input.magLength = g_mag50_queue_bak.size;
        }

        // 室内运动使用acc-to-speed
        if (s_workout_type == WORKOUT_INDOOR_CYCLING || s_workout_type == WORKOUT_INDOOR_RUNNING) {
            gm_input.speedRef = -1;
        }

        // 默认值，使用系统速度
        gm_input.gpsAccuracy = -1;
        gm_input.gpsSpeed = -1;
        gm_input.longitude = -999.0f;
        gm_input.latitude = -999.0f;

        // 自动暂停恢复,二期直接使用系统速度,需要去掉
        if (s_workout_type == WORKOUT_OUTDOOR_CYCLING || s_workout_type == WORKOUT_OUTDOOR_RUNNING ||
            s_workout_type == WORKOUT_TRAIL_RUNNING || s_workout_type == WORKOUT_OUTDOOR_WLAKING) {
            gm_input.gpsAccuracy = 1;
            gm_input.gpsSpeed = gm_input.speedRef;
        }
        ALGO_COMP_LOG_D("lib_gm:timestamp:%u,activeTypeOut:%d,accLength:%u,gyroLength:%u",
            gm_input.timestamp, gm_input.activeTypeOut, gm_input.accLength, gm_input.gyroLength);
    } else if (LIB_GET_BIT(s_gm_map, GM_STEP_COUNT) || LIB_GET_BIT(s_gm_map, GM_CALORIES) || LIB_GET_BIT(s_gm_map, GM_INTENSE_TIME)) {
        gm_input.accX = s_acc_queue_bak.x;
        gm_input.accY = s_acc_queue_bak.y;
        gm_input.accZ = s_acc_queue_bak.z;
        gm_input.accLength = s_acc_queue_bak.size;
        gm_input.timestamp = s_acc_queue_bak.timestamp;
    }

    // 卡路里和活动时长以及fitness喂心率
    if (LIB_GET_BIT(s_gm_map, GM_CALORIES) || LIB_GET_BIT(s_gm_map, GM_INTENSE_TIME) ||
        LIB_GET_BIT(s_gm_map, GM_FITNESS) || LIB_GET_BIT(s_gm_map, GM_ALLDAYSTAMINA) ||
        LIB_GET_BIT(s_gm_map, GM_RESTING_HR_QW)) {
        lib_storage_hr_input_read(&gm_input.hrRef);
    }

    // 睡眠和静息心率喂ppg
    if (LIB_GET_BIT(s_gm_map, GM_SLEEP_STAGE) || LIB_GET_BIT(s_gm_map, GM_RESTING_HR)) {
        if (lib_storage_ppg_input_read(s_ppg_data, GOMORE_WELLNESS_PPG_FRAME)) {
            gm_input.ppg1 = s_ppg_data;
            gm_input.ppgNumChannels = 1;
            gm_input.ppgLength = GOMORE_WELLNESS_PPG_FRAME;
        }
    }

    // 全天体力/压力/HRV评估喂入ppi
    if (LIB_GET_BIT(s_gm_map, GM_ALLDAYSTAMINA) || LIB_GET_BIT(s_gm_map, GM_STRESS_SCORE) || LIB_GET_BIT(s_gm_map, GM_HRV_TIME)) {
        lib_storage_ppi_input_read(&gm_input.ppiRef, &gm_input.ppiSize);
    }

    // gomore算法trigger
    // osKernelLock();
    algo_start_statistics(ALGO_GOMORE);
    uint32_t run_tick = rt_tick_get();
    int16_t ret = updateIndex(&gm_input);
    uint32_t end_tick = rt_tick_get();
    algo_stop_statistics(ALGO_GOMORE);
    // osKernelUnlock();
    algo_save_statistics("gomore", "gomore", ALGO_GOMORE);
    algo_gomore_collect(&gm_input, end_tick - run_tick, ret);
    ALGO_COMP_LOG_D("lib_gm:timestamp:%u,activeTypeOut:%d,ppgLength:%u,ret:%d",
       gm_input.timestamp, gm_input.activeTypeOut, gm_input.ppgLength, ret);
    ALGO_COMP_LOG_D("lib_gm:wristOff:%u,ppiSize:%u,hrRef:%.2f,hrvOut:%.2f,stressOut:%.4f,allDayStaminaOut:%.2f",
        gm_input.wristOff, gm_input.ppiSize, gm_input.hrRef, gm_input.hrvOut, gm_input.stressOut, gm_input.allDayStaminaOut);
    ALGO_COMP_LOG_D("lib_gm:cadence:%.2f,Step len:%.2f,distance:%.7f",
        gm_input.fitnessOut.workout.run.cadence, gm_input.fitnessOut.workout.run.stepLen, gm_input.fitnessOut.workout.run.distance);
    if (ret >= 0) {
        // 此处算法输出数据
        lib_gm_callback_proc(&gm_input);
    } else {
        ALGO_COMP_LOG_E("lib_gm:feed %d", ret);
    }
}

/**
 * @brief 算法输入acc26hz订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_acc26hz_in_callback(const void *in, uint32_t len)
{
    // 缓存数据到队列
    if (!lib_storage_input_to_buff(GM_INPUT_ACC_26, in, len))
    {
        return;
    }

    // 缓存满后,驱动数据发布后通知算法线程
    ALGO_COMP_LOG_D("lib_gm:acc26 len:%d", len);
    algo_svr_head_t head;
    if (s_call_cbk[GM_STEP_COUNT])
    {
        head.algo_type = ALGO_TYPE_STEP_COUNT;
    }
    else if (s_call_cbk[GM_CALORIES])
    {
        head.algo_type = ALGO_TYPE_CALORIES;
    }
    else if (s_call_cbk[GM_INTENSE_TIME])
    {
        head.algo_type = ALGO_TYPE_ACTIVITY_INTENSE;
    }
    else if (s_call_cbk[GM_SLEEP_STAGE])
    {
        head.algo_type = ALGO_TYPE_SLEEP_STAGE;
    }
    else if (s_call_cbk[GM_ALLDAYSTAMINA])
    {
        head.algo_type = ALGO_TYPE_ALLDAYSTAMINA;
    }
    else if (s_call_cbk[GM_STRESS_SCORE])
    {
        head.algo_type = ALGO_TYPE_STRESS_SCORE;
    }
    else if (s_call_cbk[GM_HRV_TIME])
    {
        head.algo_type = ALGO_TYPE_HRV_EVALUATE;
    }
    else if (s_call_cbk[GM_FITNESS])
    {
        head.algo_type = ALGO_TYPE_FITNESS;
    }
    else
    {
        ALGO_COMP_LOG_E("lib_gm:acc26 no support cbk");
        return;
    }

    head.cid = ALGO_CMD_ID_FEED;
    head.input_type = DATA_ID_RAW_ACC;
    uint8_t data = 0; // 传一个默认值，不允许为空
    if (send_msg_to_algo_fwk(head, &data, sizeof(uint8_t)) != 0)
    {
        ALGO_COMP_LOG_E("lib_gm:acc26 msg");
    }
}

/**
 * @brief 算法输入acc52hz订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_acc52hz_in_callback(const void *in, uint32_t len)
{
    // 缓存数据到队列
    if (!lib_storage_input_to_buff(GM_INPUT_ACC_52, in, len))
    {
        return;
    }

    // 缓存满后,驱动数据发布后通知算法线程
    ALGO_COMP_LOG_D("lib_gm:acc52 len:%d", len);
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_FITNESS;
    head.input_type = DATA_ID_RAW_ACC;
    uint8_t data = 0; // 传一个默认值，不允许为空
    if (send_msg_to_algo_fwk(head, &data, sizeof(uint8_t)) != 0)
    {
        ALGO_COMP_LOG_E("lib_gm:acc52 msg");
    }
}

/**
 * @brief 算法输入gyro 26hz订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_gyro26hz_in_callback(const void *in, uint32_t len)
{
    ALGO_COMP_LOG_D("lib_gm:gyro26 len:%d", len);

    // 缓存数据到队列
    if (!lib_storage_input_to_buff(GM_INPUT_GYRO_26, in, len))
    {
        return;
    }

    // 缓存满后,驱动数据发布后通知算法线程
    ALGO_COMP_LOG_D("lib_gm:gyro26 len:%d", len);
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_FITNESS;
    head.input_type = DATA_ID_RAW_GYRO;
    uint8_t data = 0; // 传一个默认值，不允许为空
    if (send_msg_to_algo_fwk(head, &data, sizeof(uint8_t)) != 0)
    {
        ALGO_COMP_LOG_E("lib_gm:gyro26 msg");
    }
}

/**
 * @brief 算法输入gyro 52hz订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_gyro52hz_in_callback(const void *in, uint32_t len)
{
    ALGO_COMP_LOG_D("lib_gm:gyro52 len:%d", len);

    // 缓存数据到队列
    if (!lib_storage_input_to_buff(GM_INPUT_GYRO_52, in, len))
    {
        return;
    }

    // 缓存满后,驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_FITNESS;
    head.input_type = DATA_ID_RAW_GYRO;
    uint8_t data = 0; // 传一个默认值，不允许为空
    if (send_msg_to_algo_fwk(head, &data, sizeof(uint8_t)) != 0)
    {
        ALGO_COMP_LOG_E("lib_gm:gyro52 msg");
    }
}

/**
 * @brief 算法输入地磁 50hz订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_mag50hz_in_callback(const void *in, uint32_t len)
{
    ALGO_COMP_LOG_D("lib_gm:mag50 len:%d", len);

    // 缓存数据到队列
    if (!lib_storage_input_to_buff(GM_INPUT_MAG_50, in, len))
    {
        return;
    }
}

/**
 * @brief 算法输入速度订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_speed_in_callback(const void *in, uint32_t len)
{
    // 缓存数据到buff
    if (!lib_storage_input_to_buff(GM_INPUT_SPEED, in, len))
    {
        return;
    }
}

/**
 * @brief 算法输入高度订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_altitude_in_callback(const void *in, uint32_t len)
{
    // 缓存数据到buff
    if (!lib_storage_input_to_buff(GM_INPUT_ALTITUDE, in, len))
    {
        return;
    }
}


/**
 * @brief fitness算法外接功率传感器输入订阅处理
 *
 * @param in 输入数据
 * @param len 输出数据长度
 */
static void algo_fitness_power_in_callback(const void *in, uint32_t len)
{
    // 缓存数据到buff
    ALGO_COMP_LOG_D("lib_gm:power len:%u", len);
    if (!lib_storage_input_to_buff(GM_INPUT_POWER, in, len))
    {
        return;
    }
}

/**
 * @brief fitness算法外接踏频传感器输入订阅处理
 *
 * @param in 输入数据
 * @param len 输出数据长度
 */
static void algo_fitness_cadence_in_callback(const void *in, uint32_t len)
{
    // 缓存数据到buff
    ALGO_COMP_LOG_D("lib_gm:cadence len:%u", len);
    if (!lib_storage_input_to_buff(GM_INPUT_CADENCE, in, len))
    {
        return;
    }
}

/**
 * @brief 算法输入心率订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_gm_hr_in_callback(const void *in, uint32_t len)
{
    // 缓存数据到buff
    (void)lib_storage_input_to_buff(GM_INPUT_HR, in, len);
}

/**
 * @brief 算法输入ppg订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_ppg_in_callback(const void *in, uint32_t len)
{
    // 缓存数据到buff
    (void)lib_storage_input_to_buff(GM_INPUT_PPG, in, len);
}

/**
 * @brief 算法输入ppi订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_ppi_in_callback(const void *in, uint32_t len)
{
    // 缓存数据到buff
    (void)lib_storage_input_to_buff(GM_INPUT_PPI, in, len);
}

/**
 * @brief 算法输入睡眠主动暂停处理
 *
 * @param void
 */
void algo_gm_sleep_manual_in_callback(void)
{
    if (!s_sleep_stage_sta) {
        ALGO_COMP_LOG_E("lib_gm:manual not enter slp");
        return;
    }
    SleepOutput sleep_output = {0};
    int16_t ret = endSleepPeriod(1, &sleep_output);
    if (ret != 0) {
        ALGO_COMP_LOG_E("lib_gm:manual end ret:%d", ret);
        return;
    }

    ALGO_COMP_LOG_I("lib_gm:manual slpSta:%d, StartTSOut:%u, EndTSOut:%u",
    sleep_output.sleepPeriodStatusOut, sleep_output.sleepPeriodStartTSOut, sleep_output.sleepPeriodEndTSOut);
    if (sleep_output.sleepPeriodStatusOut & 0x04) { // 睡眠结束
        set_user_sleep_status(false);
        s_sleep_stage_sta = false;
        algo_sleep_stage_pub_t sleep_stage = {0};
        sleep_stage.timestamp = sleep_output.sleepPeriodEndTSOut;
        sleep_stage.event_type = SLEEP_EXIT_EVENT;
        if (s_call_cbk[GM_SLEEP_STAGE] != NULL) {
            s_call_cbk[GM_SLEEP_STAGE](&sleep_stage, sizeof(algo_sleep_stage_pub_t));
        }
        if (sleep_output.sleepPeriodStatusOut & 0x08) { // 睡眠周期有效,则输出睡眠周期总结数据
            SleepSummaryOutput sleepOutInfo = {0};
            memset(s_sleep_stages, 0, GOMORE_STAGES_LENTH);
            sleepOutInfo.stages = s_sleep_stages;
            ret = getEmbeddedSleepSummary(0, &sleepOutInfo);
            if (ret != 0)
            {
                ALGO_COMP_LOG_E("lib_gm:manual get ret:%d", ret);
                return;
            }
            if ((sleepOutInfo.type == GM_LONG_SLEEP && sleepOutInfo.totalSleepTime < LONG_SLEEP_VAILD_TIME) ||
                (sleepOutInfo.type == GM_SHORT_SLEEP && sleepOutInfo.endTS - sleepOutInfo.startTS < SHORT_SLEEP_VAILD_TIME))
            {
                ALGO_COMP_LOG_I("lib_gm:manual slp_type:%d totalTime:%f, endTS:%d,startTS:%d,diif:%u",
                    sleepOutInfo.type, sleepOutInfo.totalSleepTime, sleepOutInfo.endTS, sleepOutInfo.startTS, sleepOutInfo.endTS - sleepOutInfo.startTS);
                return;
            }
            algo_gomore_collect_sleep_end(&sleepOutInfo);
            ALGO_COMP_LOG_I("lib_gm:manual slp_type:%u startTS:%u,endTS:%u,totalTime:%.2f",
                sleepOutInfo.type, sleepOutInfo.startTS, sleepOutInfo.endTS, sleepOutInfo.totalSleepTime);
            memset(&sleep_stage, 0, sizeof(algo_sleep_stage_pub_t));
            sleep_stage.timestamp = sleep_output.sleepPeriodEndTSOut;
            sleep_stage.event_type = SLEEP_SUMMARY_EVENT;
            sleep_stage.sleepStageOut = sleepOutInfo;
            if (s_call_cbk[GM_SLEEP_STAGE] != NULL) {
                s_call_cbk[GM_SLEEP_STAGE](&sleep_stage, sizeof(algo_sleep_stage_pub_t));
            }
        }
    }
}

/**
 * @brief 对变化的输入进行订阅和取消订阅
 *
 * @param input_type 输入数据类型
 * @param is_add 是不是自加
 */
static void lib_gomore_input_chg(uint32_t input_type, bool is_add)
{
    // 自减并且不为0,或则自加且大于2时,不需要处理gomore输入订阅管理
    if ((!is_add && s_input_open_count[input_type] > 0) || (is_add && s_input_open_count[input_type] >= 2))
    {
        return;
    }
    bool is_disable = s_input_open_count[input_type] == 0 ? true : false;
    optional_config_t config = { .sampling_rate = 1,};
    switch (input_type)
    {
        case GM_INPUT_ACC_26:
        {
            config.sampling_rate = 26; // acc采样率
            lib_gomore_sub_chg(is_disable, DATA_ID_RAW_ACC, algo_acc26hz_in_callback, &config);
            break;
        }
        case GM_INPUT_ACC_52:
        {
            config.sampling_rate = 52; // 六轴采样率
            lib_gomore_sub_chg(is_disable, DATA_ID_RAW_ACC, algo_acc52hz_in_callback, &config);
            break;
        }
        case GM_INPUT_GYRO_26:
        {
            config.sampling_rate = 26; // 六轴采样率
            lib_gomore_sub_chg(is_disable, DATA_ID_RAW_GYRO, algo_gyro26hz_in_callback, &config);
            break;
        }
        case GM_INPUT_GYRO_52:
        {
            config.sampling_rate = 52; // 六轴采样率
            lib_gomore_sub_chg(is_disable, DATA_ID_RAW_GYRO, algo_gyro52hz_in_callback, &config);
            break;
        }
        case GM_INPUT_MAG_50:
        {
#ifdef MAG_USE_PUB_GET_DATA
            config.sampling_rate = 50; // 地磁采样率
            lib_gomore_sub_chg(is_disable, DATA_ID_RAW_MAG, algo_mag50hz_in_callback, &config);
#endif
            break;
        }
        case GM_INPUT_SPEED:
        {
            config.sampling_rate = 0; // 速度采样率
            lib_gomore_sub_chg(is_disable, DATA_ID_ALGO_SPEED, algo_speed_in_callback, &config);
            break;
        }
        case GM_INPUT_ALTITUDE:
        {
            config.sampling_rate = 0; // 高度采样率
            lib_gomore_sub_chg(is_disable, DATA_ID_ALGO_ALTITUDE, algo_altitude_in_callback, &config);
            break;
        }
        case GM_INPUT_POWER:
        {
            config.sampling_rate = 0; // 功率采样率
            lib_gomore_sub_chg(is_disable, DATA_ID_ALGO_POWER, algo_fitness_power_in_callback, &config);
            break;
        }
        case GM_INPUT_CADENCE:
        {
            config.sampling_rate = 0; // 踏频采样率
            lib_gomore_sub_chg(is_disable, DATA_ID_ALGO_CADENCE, algo_fitness_cadence_in_callback, &config);
            break;
        }
        case GM_INPUT_HR:
        {
            lib_gomore_sub_chg(is_disable, DATA_ID_ALGO_HEART_RATE, algo_gm_hr_in_callback, &config);
            break;
        }
        case GM_INPUT_PPG:
        {
            config.sampling_rate = 25; // ppg采样率
            lib_gomore_sub_chg(is_disable, DATA_ID_RAW_PPG_HR, algo_ppg_in_callback, &config);
            break;
        }
        case GM_INPUT_PPI:
        {
            config.sampling_rate = 0; // ppi采样率
            lib_gomore_sub_chg(is_disable, DATA_ID_ALGO_PPG_HRV, algo_ppi_in_callback, &config);
            break;
        }
        default:
            break;
    }

}

/**
 * @brief 设置gomore算法输入项
 *
 * @param type 输入项类型
 * @param cbk 算法输出回调
 *
 * @return 结果
 */
int32_t lib_gomore_set_open_map(uint8_t type, callback_t cbk)
{
    s_gm_map = LIB_SET_BIT(s_gm_map, type);
    s_call_cbk[type] = cbk;

    // 订阅模式调整
    switch (type)
    {
        case GM_STEP_COUNT:
            s_input_open_count[GM_INPUT_ACC_26]++;
            lib_gomore_input_chg(GM_INPUT_ACC_26, true); // acc
            break;
        case GM_FITNESS:
            s_input_open_count[GM_INPUT_SPEED]++;
            lib_gomore_input_chg(GM_INPUT_SPEED, true); // 速度
            s_input_open_count[GM_INPUT_ALTITUDE]++;
            lib_gomore_input_chg(GM_INPUT_ALTITUDE, true); // 高度
            s_input_open_count[GM_INPUT_HR]++;
            lib_gomore_input_chg(GM_INPUT_HR, true); // hr
            if (s_workout_type == WORKOUT_OUTDOOR_RUNNING || s_workout_type == WORKOUT_INDOOR_RUNNING ||
                s_workout_type == WORKOUT_TRAIL_RUNNING)
            {
                s_input_open_count[GM_INPUT_GYRO_52]++;
                lib_gomore_input_chg(GM_INPUT_GYRO_52, true); // 陀螺仪
                if (s_input_open_count[GM_INPUT_ACC_26])
                {
                    optional_config_t config = { .sampling_rate = 1,};
                    config.sampling_rate = 26; // acc采样率
                    lib_gomore_sub_chg(true, DATA_ID_RAW_ACC, algo_acc26hz_in_callback, &config);; // acc
                }
                // 解决wellness和fitness切换队列溢出问题
                initAccQueue(&s_acc_queue);
                s_input_open_count[GM_INPUT_ACC_52]++;
                lib_gomore_input_chg(GM_INPUT_ACC_52, true); // acc
            }
            else if (s_workout_type == WORKOUT_SWIMMING)
            {
                int32_t ret = qmc6308_enable_sensor();
                if (ret == RT_EOK)
                {
                    ALGO_COMP_LOG_E("QMC6308 sensor en");
                }
                s_input_open_count[GM_INPUT_MAG_50]++;
                lib_gomore_input_chg(GM_INPUT_MAG_50, true); // mag
                s_input_open_count[GM_INPUT_GYRO_52]++;
                lib_gomore_input_chg(GM_INPUT_GYRO_52, true); // 陀螺仪
                if (s_input_open_count[GM_INPUT_ACC_26])
                {
                    optional_config_t config = { .sampling_rate = 1,};
                    config.sampling_rate = 26; // acc采样率
                    lib_gomore_sub_chg(true, DATA_ID_RAW_ACC, algo_acc26hz_in_callback, &config);; // acc
                }
                // 解决wellness和fitness切换队列溢出问题
                initAccQueue(&s_acc_queue);
                s_input_open_count[GM_INPUT_ACC_52]++;
                lib_gomore_input_chg(GM_INPUT_ACC_52, true); // acc
            }
            else
            {
                s_input_open_count[GM_INPUT_GYRO_26]++;
                lib_gomore_input_chg(GM_INPUT_GYRO_26, true); // 陀螺仪
                s_input_open_count[GM_INPUT_ACC_26]++;
                lib_gomore_input_chg(GM_INPUT_ACC_26, true); // acc
            }

            // 骑行需要订阅外部功率传感器和踏频传感器
            if (s_workout_type == WORKOUT_OUTDOOR_CYCLING || s_workout_type == WORKOUT_INDOOR_CYCLING)
            {
                s_input_open_count[GM_INPUT_POWER]++;
                lib_gomore_input_chg(GM_INPUT_POWER, true); // 功率
                s_input_open_count[GM_INPUT_CADENCE]++;
                lib_gomore_input_chg(GM_INPUT_CADENCE, true); // 踏频
            }
            break;
        case GM_CALORIES:
        case GM_INTENSE_TIME:
            s_input_open_count[GM_INPUT_ACC_26]++;
            lib_gomore_input_chg(GM_INPUT_ACC_26, true); // acc
            s_input_open_count[GM_INPUT_HR]++;
            // lib_gomore_input_chg(GM_INPUT_HR, true); // hr
            break;
        case GM_SLEEP_STAGE:
        {
            // 1: module默认值
            // mode :
            // 100: PPG input on 90 seconds and off 90 seconds.
            // 101: PPG input on 90 seconds and off 180 seconds.
            // 102: PPG input on 90 seconds and off 270 seconds.
            int16_t ret = setCompetitorMode(1, 102);
            if (ret != 0) {
                ALGO_COMP_LOG_E("lib_gm:setCompetitorMode ret=%d", ret);
            }
            s_input_open_count[GM_INPUT_ACC_26]++;
            lib_gomore_input_chg(GM_INPUT_ACC_26, true); // acc
            s_input_open_count[GM_INPUT_HR]++;
            // lib_gomore_input_chg(GM_INPUT_HR, true); // hr
            s_input_open_count[GM_INPUT_PPG]++;
            // lib_gomore_input_chg(GM_INPUT_PPG, true); // ppg
            break;
        }
        case GM_RESTING_HR:
        {
            int16_t ret = setRestHrStartOneTimeMeasurement();
            if (ret != 0) {
                ALGO_COMP_LOG_E("lib_gm:setRestHrStart ret=%d", ret);
            }
            s_input_open_count[GM_INPUT_PPG]++;
            // 强制打开ppg
            optional_config_t config = { .sampling_rate = 25,}; // ppg采样率
            lib_gomore_sub_chg(false, DATA_ID_RAW_PPG_HR, algo_ppg_in_callback, &config); // ppg
            // lib_gomore_input_chg(GM_INPUT_PPG, true); // ppg
            s_input_open_count[GM_INPUT_HR]++;
            // lib_gomore_input_chg(GM_INPUT_HR, true); // hr
            break;
        }
        case GM_ALLDAYSTAMINA:
        case GM_STRESS_SCORE:
        {
            s_input_open_count[GM_INPUT_PPI]++;
            // lib_gomore_input_chg(GM_INPUT_PPI, true); // ppi
            s_input_open_count[GM_INPUT_HR]++;
            // lib_gomore_input_chg(GM_INPUT_HR, true); // hr
            break;
        }
        case GM_HRV_TIME:
        {
            s_input_open_count[GM_INPUT_PPI]++;
            // lib_gomore_input_chg(GM_INPUT_PPI, true); // ppi
            break;
        }
        case GM_RESTING_HR_QW:
            break;
        default:
            break;
    }
    return 0;
}

/**
 * @brief 复位gomore算法输入项
 *
 * @param type 输入项类型
 */
void lib_gomore_reset_open_map(uint8_t type)
{
    s_gm_map = LIB_CLEAR_BIT(s_gm_map, type);
    s_call_cbk[type] = NULL;

    // 订阅模式调整
    switch (type)
    {
        case GM_STEP_COUNT:
            if (s_input_open_count[GM_INPUT_ACC_26])
            {
                s_input_open_count[GM_INPUT_ACC_26]--;
                lib_gomore_input_chg(GM_INPUT_ACC_26, false); // acc26
            }
            break;
        case GM_FITNESS:
            if (s_input_open_count[GM_INPUT_SPEED])
            {
                s_input_open_count[GM_INPUT_SPEED]--;
                lib_gomore_input_chg(GM_INPUT_SPEED, false); // 速度
            }
            if (s_input_open_count[GM_INPUT_ALTITUDE])
            {
                s_input_open_count[GM_INPUT_ALTITUDE]--;
                lib_gomore_input_chg(GM_INPUT_ALTITUDE, false); // 高度
            }
            if (s_input_open_count[GM_INPUT_POWER])
            {
                s_input_open_count[GM_INPUT_POWER]--;
                lib_gomore_input_chg(GM_INPUT_POWER, false); // 功率
            }
            if (s_input_open_count[GM_INPUT_CADENCE])
            {
                s_input_open_count[GM_INPUT_CADENCE]--;
                lib_gomore_input_chg(GM_INPUT_CADENCE, false); // 踏频
            }
            if (s_input_open_count[GM_INPUT_HR])
            {
                s_input_open_count[GM_INPUT_HR]--;
                lib_gomore_input_chg(GM_INPUT_HR, false); // hr
            }
            if (s_input_open_count[GM_INPUT_GYRO_52] || s_input_open_count[GM_INPUT_ACC_52] || s_input_open_count[GM_INPUT_MAG_50])
            {
                if (s_input_open_count[GM_INPUT_MAG_50])
                {
                    int32_t ret = qmc6308_disable_sensor();
                    if (ret == RT_EOK)
                    {
                        ALGO_COMP_LOG_E("QMC6308 sensor dis");
                    }
                    s_input_open_count[GM_INPUT_MAG_50]--;
                    lib_gomore_input_chg(GM_INPUT_MAG_50, false); // 地磁
                }
                if (s_input_open_count[GM_INPUT_GYRO_52])
                {
                    s_input_open_count[GM_INPUT_GYRO_52]--;
                    lib_gomore_input_chg(GM_INPUT_GYRO_52, false); // 陀螺仪
                }
                if (s_input_open_count[GM_INPUT_ACC_52])
                {
                    s_input_open_count[GM_INPUT_ACC_52]--;
                    lib_gomore_input_chg(GM_INPUT_ACC_52, false); // acc
                }
                // 解决wellness和fitness切换队列溢出问题
                initAccQueue(&s_acc_queue);
                initAccQueue(&g_gyro52_queue);
                initAccQueue(&g_mag50_queue);
                if (s_input_open_count[GM_INPUT_ACC_26])
                {
                    optional_config_t config = { .sampling_rate = 1,};
                    config.sampling_rate = 26; // acc采样率
                    lib_gomore_sub_chg(false, DATA_ID_RAW_ACC, algo_acc26hz_in_callback, &config);; // acc
                }
            }
            else
            {
                if (s_input_open_count[GM_INPUT_GYRO_26])
                {
                    s_input_open_count[GM_INPUT_GYRO_26]--;
                    lib_gomore_input_chg(GM_INPUT_GYRO_26, false); // 陀螺仪
                }
                if (s_input_open_count[GM_INPUT_ACC_26])
                {
                    s_input_open_count[GM_INPUT_ACC_26]--;
                    lib_gomore_input_chg(GM_INPUT_ACC_26, false); // acc
                }
            }
            s_altitude = -999;
            s_sys_speed = -1;
            s_cycling_power = -1;
            s_cycling_cadence = -1;
            break;
        case GM_CALORIES:
        case GM_INTENSE_TIME:
            if (s_input_open_count[GM_INPUT_ACC_26])
            {
                s_input_open_count[GM_INPUT_ACC_26]--;
                lib_gomore_input_chg(GM_INPUT_ACC_26, false); // acc
            }
            if (s_input_open_count[GM_INPUT_HR])
            {
                s_input_open_count[GM_INPUT_HR]--;
                lib_gomore_input_chg(GM_INPUT_HR, false); // hr
            }
            break;
        case GM_SLEEP_STAGE:
            if (s_input_open_count[GM_INPUT_ACC_26])
            {
                s_input_open_count[GM_INPUT_ACC_26]--;
                lib_gomore_input_chg(GM_INPUT_ACC_26, false); // acc
            }
            if (s_input_open_count[GM_INPUT_HR])
            {
                s_input_open_count[GM_INPUT_HR]--;
                lib_gomore_input_chg(GM_INPUT_HR, false); // hr
            }
            if (s_input_open_count[GM_INPUT_PPG])
            {
                s_input_open_count[GM_INPUT_PPG]--;
                lib_gomore_input_chg(GM_INPUT_PPG, false); // ppg
            }
            break;
        case GM_RESTING_HR:
            if (s_input_open_count[GM_INPUT_PPG])
            {
                s_input_open_count[GM_INPUT_PPG]--;
                // 强制关闭ppg
                optional_config_t config = { .sampling_rate = 25,}; // ppg采样率
                lib_gomore_sub_chg(true, DATA_ID_RAW_PPG_HR, algo_ppg_in_callback, &config); // ppg
            }
            if (s_input_open_count[GM_INPUT_HR])
            {
                s_input_open_count[GM_INPUT_HR]--;
                lib_gomore_input_chg(GM_INPUT_HR, false); // hr
            }
            lib_storage_ppg_data_reset();
            break;
        case GM_ALLDAYSTAMINA:
        case GM_STRESS_SCORE:
            if (s_input_open_count[GM_INPUT_PPI])
            {
                s_input_open_count[GM_INPUT_PPI]--;
                lib_gomore_input_chg(GM_INPUT_PPI, false); // ppi
            }
            if (s_input_open_count[GM_INPUT_HR])
            {
                s_input_open_count[GM_INPUT_HR]--;
                lib_gomore_input_chg(GM_INPUT_HR, false); // hr
            }
            break;
        case GM_HRV_TIME:
            if (s_input_open_count[GM_INPUT_PPI])
            {
                s_input_open_count[GM_INPUT_PPI]--;
                lib_gomore_input_chg(GM_INPUT_PPI, false); // ppi
            }
            break;
        case GM_RESTING_HR_QW:
            break;
        default:
            break;
    }
}

/**
 * @brief 打印函数
 *
 * @param fmt 格式化字符串
 * @param ... 可变参数
 * @return int32_t 成功:0 失败:-1
 */
static int32_t lib_common_printf_func(const char *fmt, ...)
{
    va_list args;
    va_start(args, fmt); // 初始化args
    qw_vsyslog(QW_LOG_INFO, "gm_sdk", fmt, args);
    va_end(args); // 清理args
    return 0;
}

/**
 * @brief 时间同步复位订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_sync_time_reset_callback(const void *in, uint32_t len)
{
    ALGO_COMP_LOG_I("lib_gm:rcv last_flag:%d", s_hcpu_reset_ok);
    s_hcpu_reset_ok = true;

    algo_svr_head_t head;
    if (s_call_cbk[GM_STEP_COUNT])
    {
        head.algo_type = ALGO_TYPE_STEP_COUNT;
    }
    else if (s_call_cbk[GM_CALORIES])
    {
        head.algo_type = ALGO_TYPE_CALORIES;
    }
    else if (s_call_cbk[GM_INTENSE_TIME])
    {
        head.algo_type = ALGO_TYPE_ACTIVITY_INTENSE;
    }
    else if (s_call_cbk[GM_SLEEP_STAGE])
    {
        head.algo_type = ALGO_TYPE_SLEEP_STAGE;
    }
    else if (s_call_cbk[GM_ALLDAYSTAMINA])
    {
        head.algo_type = ALGO_TYPE_ALLDAYSTAMINA;
    }
    else if (s_call_cbk[GM_STRESS_SCORE])
    {
        head.algo_type = ALGO_TYPE_STRESS_SCORE;
    }
    else if (s_call_cbk[GM_HRV_TIME])
    {
        head.algo_type = ALGO_TYPE_HRV_EVALUATE;
    }
    else if (s_call_cbk[GM_FITNESS])
    {
        head.algo_type = ALGO_TYPE_FITNESS;
    }
    else
    {
        ALGO_COMP_LOG_E("lib_gm:acc26 no support cbk");
        return;
    }

    head.cid = ALGO_CMD_ID_FEED;
    head.input_type = DATA_ID_EVENT_TIME_SYNC;
    uint8_t data = 0; // 传一个默认值，不允许为空
    if (send_msg_to_algo_fwk(head, &data, sizeof(uint8_t)) != 0)
    {
        ALGO_COMP_LOG_E("lib_gm:time sync msg");
    }
}

/**
 * @brief 事件定时器事件订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_event_timer_in_callback(const void *in, uint32_t len)
{
    // 运动中，不处理
    if (s_workout_type != 0)
    {
        return;
    }

    // 每隔30min保存一下previousData
    algo_timer_event_pub_t *in_data = (algo_timer_event_pub_t *)in;
    if (in_data->event_type == TIMER_EVENT_NEW_30MIN || in_data->event_type == TIMER_EVENT_NEW_HOUR)
    {
        lib_save_previousdata_to_sharemem();

        // 通知大核将共享内存中的previousData保存到文件
        event_gm_previous_save_t pub_data;
        pub_data.timestamp = (uint32_t)get_sec_from_rtc();
        if (qw_dataserver_publish_id(DATA_ID_EVENT_GM_PREVIOUS_SAVE, &pub_data, sizeof(event_gm_previous_save_t)) != ERRO_CODE_OK)
        {
            ALGO_COMP_LOG_E("lib_gm:event previous data save pub");
        }
        ALGO_COMP_LOG_D("lib_gm:event save pre_data time:%u", pub_data.timestamp);
    }
}

/**
 * @brief 设备上电后调用，初始化GoMore算法模块
 *
 * @return true ： 成功  false : 失败
 */
int32_t lib_gomore_init(void)
{
    if (s_lib_gm_init)
    {
        return 0;
    }
    if (!s_hcpu_reset_ok) // gomore复位状态下,输入互斥锁和gomore日志不关,传感器数据不清空,减少复位导致的数据丢失问题
    {
        // 创建输入读写互斥锁
        s_input_mutex = rt_mutex_create("gm_input_mutex", RT_IPC_FLAG_FIFO);
        if (s_input_mutex == NULL)
        {
            ALGO_COMP_LOG_E("lib_gm:init create mutex");
            return -1;
        }
        initAccQueue(&s_acc_queue);
        initAccQueue(&g_gyro52_queue);
        initAccQueue(&g_mag50_queue);
        lib_storage_ppg_data_reset();
        s_sleep_stage_sta = false;
        set_user_sleep_status(false);
        optional_config_t config = { .sampling_rate = 0,};
        int32_t ret = qw_dataserver_subscribe_id(DATA_ID_EVENT_TIME_SYNC, algo_sync_time_reset_callback, &config);
        if (ret != 0)
        {
            ALGO_COMP_LOG_E("lib_gm:init sub");
        }
        ret = qw_dataserver_subscribe_id(DATA_ID_ALGO_EVENT_TIMER, algo_event_timer_in_callback, &config);
        if (ret != 0)
        {
            ALGO_COMP_LOG_E("lib_gm:open ET");
            return -1;
        }
    }

    uint32_t prev_data_size = getPreviousDataSize();
    uint32_t frame_data_size = getMemSizeHealthFrame();

    // Allocate Memory
    if (g_sdkMem == NULL)
    {
        g_sdkMem = DATASERVER_MALLOC(frame_data_size);
    }
    memset(g_sdkMem, 0, frame_data_size);

    if (g_prevData == NULL)
    {
        g_prevData = DATASERVER_MALLOC(prev_data_size);
    }

    char* prevData = get_algo_share_mem();
    uint32_t last_time = 0;
    uint32_t sdk_size = 0;
    memcpy(&last_time, prevData, sizeof(last_time));
    memcpy(&sdk_size, prevData + sizeof(last_time), sizeof(sdk_size));
    ALGO_COMP_LOG_I("lib_gm:init last_time:%u,old:%d new:%d", last_time, sdk_size, prev_data_size);
    if (prev_data_size > ALGO_SHARE_ADDR_SIZE - sizeof(last_time) - sizeof(sdk_size))
    {
        ALGO_COMP_LOG_E("lib_gm:init buff overflow %d,%d",frame_data_size, prev_data_size);
        return -1;
    }
    if (sdk_size != prev_data_size)
    {
        memset(prevData, 0, ALGO_SHARE_ADDR_SIZE);
        memset(g_prevData, 0, prev_data_size);
    }
    else
    {
        memcpy(g_prevData, prevData + sizeof(last_time) + sizeof(sdk_size), prev_data_size);
    }

    if (g_sdkMem == NULL || g_prevData == NULL)
    {
        ALGO_COMP_LOG_E("lib_gm:init null %d,%d",frame_data_size, prev_data_size);
        return -1;
    }
    ALGO_COMP_LOG_I("lib_gm:init %d,%d",frame_data_size, prev_data_size);
    int32_t ret =  printf_register(lib_common_printf_func);
    ALGO_COMP_LOG_I("lib_gm:init log_reg ret=%d", ret);
    ret =  enable_Log(1, LOG_ERR); // gomore打开日志使能
    ALGO_COMP_LOG_I("lib_gm:init enable_Log ret=%d", ret);

    if (!init_gomore_info())
    {
        ALGO_COMP_LOG_E("lib_gm:init info");
        return -1;
    }
    s_lib_gm_init = true;
    return 0;
}

/**
 * @brief 关机前调用，退出算法模块，并进行一些清理工作
 *
 * @return true ： 成功  false : 失败
 */
static bool lib_gm_exit(void)
{
    if (!s_lib_gm_init)
    {
        return 0;
    }

    // Stop SDK
    stopHealthSession(g_sdkMem);

    // Free Memory
    // DATASERVER_FREE(g_sdkMem);
    // DATASERVER_FREE(g_prevData);
    s_lib_gm_init = false;
    return true;
}

static void lib_gm_reset_algo_config(void)
{
    int16_t ret = 0;
    for(uint8_t i = 0; i < GM_DATA_TYPE_MAX; i++)
    {
        if (LIB_GET_BIT(s_gm_map, i) == 0 || s_call_cbk[i] == NULL)
        {
            continue;
        }
        switch (i)
        {
        case GM_SLEEP_STAGE:
            // 1: module默认值
            // mode :
            // 100: PPG input on 90 seconds and off 90 seconds.
            // 101: PPG input on 90 seconds and off 180 seconds.
            // 102: PPG input on 90 seconds and off 270 seconds.
            ret = setCompetitorMode(1, 102);
            if (ret != 0) {
                ALGO_COMP_LOG_E("lib_gm:reset setCompetitorMode ret = %d", ret);
            }
            break;
        case GM_RESTING_HR:
            ret = setRestHrStartOneTimeMeasurement();
            if (ret != 0) {
                ALGO_COMP_LOG_E("lib_gm:reset setRestHrStart ret = %d", ret);
            }
            break;
        case GM_STEP_COUNT:
        case GM_CALORIES:
        case GM_INTENSE_TIME:
        case GM_FITNESS:
        case GM_ALLDAYSTAMINA:
        case GM_STRESS_SCORE:
        case GM_HRV_TIME:
        case GM_RESTING_HR_QW:
            break;
        default:
            ALGO_COMP_LOG_E("lib_gm:reset data_type:%d", i);
            break;
        }
    }
}

/**
 * @brief 用于时间戳同步重置库的状态
 *
 */
void lib_gm_reset(void)
{
    if (!s_hcpu_reset_ok)
    {
        return;
    }

    // 检查是否是否运动中,运动中不能重置
    if (0 != s_workout_type)
    {
        ALGO_COMP_LOG_E("lib_gm:not support reset %d", s_workout_type);
        return;
    }
    ALGO_COMP_LOG_W("lib_gm:reset start");

    // 释放资源或执行清理操作
    (void)lib_gm_exit();

    // 初始化库，设置初始状态或分配资源
    int32_t ret = lib_gomore_init();
    if (ret != 0) {
        ALGO_COMP_LOG_E("lib_gm:reset init ret=%d", ret);
    }

    // 重置时对各个打开的算法重新进行配置
    lib_gm_reset_algo_config();

    // 复位后通知大核将共享内存中的previousData更新到文件
    lib_save_previousdata_to_sharemem();
    event_gm_previous_save_t pub_data;
    pub_data.timestamp = (uint32_t)get_sec_from_rtc();
    if (qw_dataserver_publish_id(DATA_ID_EVENT_GM_PREVIOUS_SAVE, &pub_data, sizeof(event_gm_previous_save_t)) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("lib_gm:event previous data save pub");
    }
    ALGO_COMP_LOG_W("lib_gm:reset end");

    s_hcpu_reset_ok = false;
}

#ifdef RT_USING_FINSH

static uint32_t s_ons_second_feed_times = 60; // 模拟喂数据时1s喂gomore次数
static uint32_t s_cur_seconds = 0; // 当前时间
static uint32_t s_fitness_seconds = 0; // 需要喂入fitness数据的时间
static uint32_t s_sleep_seconds = 0; // 需要喂入睡眠数据的时间
static bool s_sleep_sim_flag = false; // 睡眠模拟标志
static bool s_manual_sleep_flag = false; // 手动睡眠标志
static uint32_t s_rtc_current_time = 1733219441; // 2024-12-31 23:59:01

// 模拟数据，用于调试
float fakeSleepAcc[25] = {0.f};
float fakeWakeAcc[25] = {
    0,994,1927,2738,3377,3804,3992,3929,3619,3082,2351,1472,501,-501,
    -1472,-2351,-3082,-3619,-3929,-3992,-3804,-3377,-2738,-1927,-994,
};
float fakeSleepPpg[25] = {
    0,994,1927,2738,3377,3804,3992,3929,3619,3082,2351,1472,501,-501,
    -1472,-2351,-3082,-3619,-3929,-3992,-3804,-3377,-2738,-1927,-994,
};
float fakeWakePpg[25] = {
    0.0,1963.615014460563,3421.4570406420257,3998.0262414629265,
    3544.814316924859,2178.556140060109,251.1620781172561,-1740.924397489308,
    -3284.596836534815,-3982.24785841232,-3654.181830570404,-2384.899499862465,
    -501.3329342572221,1511.363147273864,3134.773829303356,3950.7533623805502,
    3749.1279579675675,2581.8307508958055,749.5252583429027,-1275.837237192276,
    -2972.5793019095745,-3903.6670477549887,-3829.27799012827,-2768.5726954816296,
    -994.7595486594224,
};

uint16_t fakeSleepPpi[6][5] = {
    {100, 154, 142, 136, 4},
    {98, 126, 231, 125, 4},
    {125, 126, 154, 0, 3},
    {145, 155, 0, 0, 2},
    {132, 123, 105, 0, 3},
    {128, 162, 162, 0, 3},
};

/**
 * @brief 模拟睡眠数据
 *
 * @return true 打开睡眠模拟
 * @return false 未打开睡眠模拟
 */
static bool lib_gomore_sleep_period(void)
{
    // 未打开睡眠模拟，直接返回
    if (!s_sleep_sim_flag) {
        return false;
    }

    static bool ppg_on_off = false;
    static bool is_exit_sleep_enter_rest_hr = false; // 是否退出睡眠进入静息心率
    IndexIO gm_input = {0};
    ALGO_COMP_LOG_I("lib_gm:test slp seconds:%u,cur:%u", s_sleep_seconds, s_cur_seconds);

    // 睡眠喂ppg和acc
    if (s_cur_seconds < s_sleep_seconds) {
        gm_input.accX = fakeSleepAcc;
        gm_input.accY = fakeSleepAcc;
        gm_input.accZ = fakeSleepAcc;
        gm_input.ppg1 = fakeSleepPpg;
    } else {
        gm_input.accX = fakeWakeAcc;
        gm_input.accY = fakeWakeAcc;
        gm_input.accZ = fakeWakeAcc;
        gm_input.ppg1 = fakeWakePpg;
    }
    gm_input.timeZoneOffset = tz_get()->tz_minuteswest;
    gm_input.accLength = 25;
    s_cur_seconds++;
    for (uint8_t i = 0; i < s_ons_second_feed_times; i ++) {
        s_rtc_current_time++;
        gm_input.timestamp = s_rtc_current_time;
        if (ppg_on_off || is_exit_sleep_enter_rest_hr) {
            gm_input.ppgLength = 25;
            gm_input.ppgNumChannels = 1;
            fakeSleepPpi[i % 6][0] += rand() % 10;
            fakeSleepPpi[i % 6][1] += rand() % 10;
            fakeSleepPpi[i % 6][2] += rand() % 10;
            fakeSleepPpi[i % 6][3] += rand() % 10;
            gm_input.ppiRef = fakeSleepPpi[i % 6];
            gm_input.ppiSize = (uint8_t)fakeSleepPpi[i % 6][4];
        } else {
            gm_input.ppgLength = 0;
            gm_input.ppgNumChannels = 0;
        }
        if (s_manual_sleep_flag) {
            algo_gm_sleep_manual_in_callback();
            s_manual_sleep_flag = false;
        }

        // gomore算法trigger
        // osKernelLock();
        algo_start_statistics(ALGO_GOMORE);
        uint32_t run_tick = rt_tick_get();
        int16_t ret = updateIndex(&gm_input);
        uint32_t end_tick = rt_tick_get();
        algo_stop_statistics(ALGO_GOMORE);
        // osKernelUnlock();
        algo_save_statistics("gomore", "gomore", ALGO_GOMORE);
        algo_gomore_collect(&gm_input, end_tick - run_tick, ret);
        if (ret >= 0) {
            IndexIO *gm_ouput = &gm_input;
            // 此处算法输出数据
            ALGO_COMP_LOG_D("lib_gm:test slp time:%u slpSta:0x%x,ppgOnOff:%d",
                gm_ouput->timestamp, gm_ouput->sleepPeriodStatusOut, gm_ouput->sleepStagePpgOnOffOut);
            lib_gomore_sleep_stage(gm_ouput->timestamp, s_sleep_stage_sta, gm_ouput->sleepStagePpgOnOffOut);
            ALGO_COMP_LOG_D("lib_gm:test slp hrv:%.2f,stress:%.2f,allDayStamina:%.2f", gm_input.hrvOut, gm_input.stressOut, gm_input.allDayStaminaOut);
            if (gm_ouput->sleepPeriodStatusOut & 0x02) { // 睡眠开始
                ALGO_COMP_LOG_I("lib_gm:test slp time:%u slpSta:0x%x,ppgOnOff:%d",
                    gm_ouput->timestamp, gm_ouput->sleepPeriodStatusOut, gm_ouput->sleepStagePpgOnOffOut);
                s_sleep_stage_sta = true;
                set_user_sleep_status(true);
                algo_sleep_stage_pub_t sleep_stage = {0};
                sleep_stage.timestamp = gm_ouput->timestamp;
                sleep_stage.event_type = SLEEP_ENTER_EVENT;
                if (s_call_cbk[GM_SLEEP_STAGE] != NULL) {
                    s_call_cbk[GM_SLEEP_STAGE](&sleep_stage, sizeof(algo_sleep_stage_pub_t));
                }
            } else if (gm_ouput->sleepPeriodStatusOut & 0x04) { // 睡眠结束
                ALGO_COMP_LOG_I("lib_gm:test slp time:%u slpSta:0x%x,ppgOnOff:%d",
                    gm_ouput->timestamp, gm_ouput->sleepPeriodStatusOut, gm_ouput->sleepStagePpgOnOffOut);
                s_sleep_stage_sta = false;
                is_exit_sleep_enter_rest_hr = true;
                set_user_sleep_status(false);
                algo_sleep_stage_pub_t sleep_stage = {0};
                sleep_stage.timestamp = gm_ouput->timestamp;
                sleep_stage.event_type = SLEEP_EXIT_EVENT;
                if (s_call_cbk[GM_SLEEP_STAGE] != NULL) {
                    s_call_cbk[GM_SLEEP_STAGE](&sleep_stage, sizeof(algo_sleep_stage_pub_t));
                }
                if (gm_ouput->sleepPeriodStatusOut & 0x08) { // 睡眠周期有效,则输出睡眠周期总结数据
                    ALGO_COMP_LOG_I("lib_gm:test slp time:%u slpSta:0x%x,ppgOnOff:%d",
                        gm_ouput->timestamp, gm_ouput->sleepPeriodStatusOut, gm_ouput->sleepStagePpgOnOffOut);
                    SleepSummaryOutput sleepOutInfo = {0};
                    memset(s_sleep_stages, 0, GOMORE_STAGES_LENTH);
                    sleepOutInfo.stages = s_sleep_stages;
                    ret = getEmbeddedSleepSummary(0, &sleepOutInfo);
                    if (ret != 0)
                    {
                        ALGO_COMP_LOG_E("lib_gm:test slp ret:%d", ret);
                        return true;
                    }
                    if ((sleepOutInfo.type == GM_LONG_SLEEP && sleepOutInfo.totalSleepTime < LONG_SLEEP_VAILD_TIME) ||
                        (sleepOutInfo.type == GM_SHORT_SLEEP && sleepOutInfo.endTS - sleepOutInfo.startTS < SHORT_SLEEP_VAILD_TIME))
                    {
                        ALGO_COMP_LOG_I("lib_gm:test slp type:%d totalTime:%f, endTS:%d,startTS:%d,diif_time:%u",
                            sleepOutInfo.type, sleepOutInfo.totalSleepTime, sleepOutInfo.endTS, sleepOutInfo.startTS, sleepOutInfo.endTS - sleepOutInfo.startTS);
                        return true;
                    }
                    algo_gomore_collect_sleep_end(&sleepOutInfo);
                    ALGO_COMP_LOG_I("lib_gm:test slp sleep_type:%u startTS:%u,endTS:%u,totalTime:%f",
                        sleepOutInfo.type, sleepOutInfo.startTS, sleepOutInfo.endTS, sleepOutInfo.totalSleepTime);
                    memset(&sleep_stage, 0, sizeof(algo_sleep_stage_pub_t));
                    sleep_stage.timestamp = gm_ouput->timestamp;
                    sleep_stage.event_type = SLEEP_SUMMARY_EVENT;
                    sleep_stage.sleepStageOut = sleepOutInfo;
                    if (s_call_cbk[GM_SLEEP_STAGE] != NULL) {
                        s_call_cbk[GM_SLEEP_STAGE](&sleep_stage, sizeof(algo_sleep_stage_pub_t));
                    }
                }
            }
            if (s_sleep_stage_sta && s_call_cbk[GM_HRV_TIME] != NULL) {
                if (gm_ouput->hrvOut == 0 || gm_ouput->hrvOut > 111) { // 无效值
                    // return true;
                } else {
                    ALGO_COMP_LOG_D("lib_gm:test slp hrv:%.2f", gm_ouput->hrvOut);
                    lib_gm_hrv_evaluate_callback_proc(gm_ouput);
                }
            }
            if (!s_sleep_stage_sta && s_call_cbk[GM_RESTING_HR] != NULL) {
                ALGO_COMP_LOG_I("lib_gm:test slp hrRestEstStatus:%u,hrv:%.2f",
                    gm_ouput->hrRestEstStatusOut, gm_ouput->hrRestOut);
                lib_gm_resting_hr_callback_proc(gm_ouput);
            }
            if (!s_sleep_stage_sta) {
                ALGO_COMP_LOG_D("lib_gm:test slp hrRestEstStatus:%u,hrv:%.2f",
                    gm_ouput->hrRestEstStatusOut, gm_ouput->hrRestOut);
            }
        } else {
            ALGO_COMP_LOG_E("lib_gm:test slp %d", ret);
        }
        ppg_on_off = gm_input.sleepStagePpgOnOffOut;
    }
    return true;
}

// fitness仿真数据
static float footAccX[] = {
    -4816.341400146484, -5941.8201057278375, -8572.299256616709, 1103.273819903942, -1249.286534834882, -943.2374214639468,
    -436.2085157511179, -670.8173800487909, -484.4888181102518, -557.3947964882368, -541.1808344782614, -153.3793685387588,
    -605.4929044781875, -610.2499864539316, -1030.427932739258, -1878.6833237628546, -1543.1409952591885, -879.9858482516537,
    -1158.9412883836371, -1038.3956189058254, -2306.811858196648, -2175.5917996776348, -1527.6887854751285, -1178.1323685937991,
    -1379.231803271236, -628.7226871568329, -641.8919076724928, -623.0543000357491, -908.675310563068, -1027.5132704754262,
    -1140.5106953212191, -1417.6101879197706, -2353.0962418536733, -3638.654669936824, -4981.585054981463, -5043.943755480708,
    -4255.530843929364, -5375.477927071705, -7583.079435387433, 1350.8663907343264, -3133.1679285789014, -555.4469410253972,
    -391.48044099613065, -531.3251067181027, -377.31465028256923, -617.3869055144639, -262.54126976947975, -362.08577788605896,
    -631.3693864004962, -1001.9853358366006, -1193.6350258029236, -2227.686901481784, -962.174551827568, -814.9734808474127,
    -1069.3718657201646, -1359.9225452968053, -1746.2796581034763, -2207.015835509006, -1755.4220082808527, -1838.4782830063175,
    -1069.4786967063437, -554.460165451985, -846.9673565455826, -720.9690736264595, -872.1092574450428, -949.7450809089506,
    -1069.6397703521113, -1974.3703336131719, -3277.956125687575, -4251.889365059986, -5066.545836779536, -4532.3114200514165,
    -4128.021785191128, -3995.023688491526, -2536.391666957296, -1892.0184154899755, -1884.296767565665, -372.67385210310067,
    -107.8465690418084, -503.65372093356433, -290.0032851160788, -448.833728323179, -277.59327207293376, -23.56149225819318,
    -414.7504884369511, -759.2135546158771, -1003.2901763916009, -1780.8949996014005, -1297.622135707316, -870.2449020074332,
    -1033.2656393245775, -1172.0829204637175, -1820.0395545180875, -3549.311501639241, -1951.391648273081, -1679.3326942288147,
    -1254.7844088807506, -155.93883942585197, -692.2562852197736, -720.6948436036408
};
static float footAccY[] = {
    1963.2131226208744, 3128.880870585539, -2305.8622710558834, 613.1856198213545, 274.1875989096505, -216.40775155047976,
    187.99558707645957, -75.79776218959287, -178.10384351380043, -209.58068632349602, -255.5579311993657, -384.76660300274284,
    -216.33233099567607, -130.43225784691006, -180.70849350520567, -138.0231915688028, -184.23527114245363, -318.8592122525582,
    -903.8670403616766, -921.9996199315907, -1234.8225651955117, -1419.049749569017, -1880.9915270124154, -1792.6808960583746,
    -1705.3362009476637, -1618.8399645746972, -986.0056079163847, -729.0582364919235, -700.3544787971339, -417.6022227929572,
    -25.272086566808273, 10.848871603303651, 248.65670106848847, 747.4876520585044, 1011.0527349978066, 2065.3683798653738,
    1521.9039333109947, 2825.0463446792273, -3101.4596199502694, 699.0701908967915, 1446.526877734126, -296.19306546389254,
    168.0993985156627, -75.04602719326549, -264.86858056516354, -282.331826735516, -360.3737500249125, -322.4762605161092,
    -209.98741716754938, -162.8898309201602, -449.09297203531065, -105.99798815591069, -290.70849807894945, -526.7260609840844,
    -885.1607575708504, -440.8426187476333, -1396.842703527335, -1538.5662390261275, -2328.111064677334, -2167.187710197605,
    -1521.380482887735, -1064.3773176232155, -840.4924431625697, -792.9268369869319, -526.2734938640992, -164.16524137769426,
    -72.65910323785288, -8.907090644448267, 199.57994928165164, 815.5599710892641, 1434.8295756748744, 2010.0412174146988,
    1466.2144135455706, 2358.4643383415446, -720.3480516161152, 1317.3747549251634, -192.08850665967748, -92.38039717381672,
    143.80528488937364, 126.35270551759592, -626.541789697141, -89.03492105250449, -532.789960199468, -476.1006394211103,
    -116.84515920220733, -129.39757230330488, -304.78505699001954, -13.742881161828707, -212.69440164371358, -673.1092102673587,
    -860.7837132045202, -810.1756816007633, -789.004968137153, -1739.360653624242, -2208.1491509262373, -2205.2019469592033,
    -1571.5253791030627, -1146.2716861647025, -791.5067088847268, -768.7279545530976
};
static float footAccZ[] = {
    -4359.951797796755, -4714.979522082271, 7335.4810597945225, 2302.9957304195495, 771.5943784129862, 1097.496188416773,
    1087.771532486896, 1066.6648903671576, 1054.472339396574, 1085.5020211667427, 1153.5134607431842, 1073.0642591203955,
    907.716303455586, 654.0083593251749, 619.0581224402599, 851.1489751387613, 556.4719706165547, 395.6760241060836,
    371.120355567152, 957.8474979011384, 2821.174543731066, 2615.940989280233, 2311.6986800213253, 1987.626056281887,
    2024.6557897450978, 1566.581142191984, 1200.3266276145469, 908.6326482344649, 956.3200035873726, 1185.73692866734,
    1437.078300787478, 1319.4219628158885, 1352.3422357987386, 950.5041375452142, -951.5860032062078, -3349.563832185706,
    -3603.2027419732512, -3862.958557751711, 6028.2679966517935, 1444.2902389837682, 1239.400591169085, 1058.5116756205648,
    1135.5214410898632, 1038.857674112125, 1068.658244853116, 1209.1826419441065, 1252.6353719283115, 1113.6354329634673,
    865.997898335356, 833.5612744701133, 865.8848976602359, 757.5718821311486, 460.358512644865, 477.35376747286927,
    617.850186873454, 2329.8543813277265, 2527.6017480966993, 2445.2827220060394, 2099.901783223057, 2023.0189342888027,
    1614.271475344288, 1181.5519527513147, 1051.4337578598345, 716.7243276323591, 923.3549468371324, 1347.0202076191802,
    1336.5717907341157, 1487.3693816515843, 1399.0497589111367, 36.00333661449646, -2244.397377481266, -3256.500244140625,
    -2617.150404015373, -2364.6588033559483, 2318.8169440444526, 583.0840675198301, 1155.569271165496, 1145.8770596251172,
    1050.0539662886627, 1033.3235215167606, 977.8914159658003, 1193.1682508818944, 1032.9451463660464, 1095.3989418185527,
    1010.8018213388867, 1149.2518989407286, 685.9066632329193, 692.4775376611918, 373.7914124313706, 200.58169413586052,
    590.1474855384048, 973.9526631880781, 1936.1404496796172, 3104.4494473204404, 2178.512534316706, 1853.75875356246,
    1727.1039534588303, 1265.95382301175, 1422.8340849584463, 1112.7620813797942
};
static float footGyroX[] = { 54.012550354003906, 138.12572784423827, 440.355728149414, 230.06105804443388, 64.72706527709964,
    35.96723937988281, 0.7876141548156879, -23.314565658569343, -24.770304870605464, -39.64604263305663, -11.884596824645994,
    18.96509056091309, 24.32581062316892, -23.37518692016601, -50.11253356933594, -35.66804504394531, -122.75632324218743,
    -164.59682312011716, -121.66921234130884, -19.46188449859617, -38.922706604003906, -75.36117095947264, -52.61038970947275,
    24.67883148193354, 42.47518157958982, 59.98358917236328, 8.191492080688525, -73.729792022705, -103.67591094970703,
    -112.39735565185542, -150.81907653808594, -149.02613525390638, -101.76294708251959, -46.272687530517516, 20.187567234039147,
    -1.5375937223434448, 5.668186950682539, 111.03360443115238, 310.2445678710942, 151.63983612060443, 63.05347442626953,
    31.53888168334963, 1.1784245878458197, -34.66425781250002, -37.53607482910155, -60.5614013671875, -24.988515472412104,
    17.227587890624974, -8.876138448714759, -69.5865585327148, -53.56513595581055, -70.684822082519, -154.90499267578124,
    -144.03204650878936, -81.70232238769555, -123.56499481201172, -155.2841888427734, -143.4720977783204, -59.26838479042134,
    79.24152832031223, 107.8105926513672, 35.99755783081034, -51.32438430786078, -87.57958831787109, -90.77812042236324,
    -119.84835815429688, -140.93594665527343, -116.71986236572286, -70.44729080200229, -10.503182601928799, -17.789020538330078,
    -80.33007812499999, 15.468021392821282, 135.236759948731, 287.2586608886706, 93.76033782958984, 34.5332084655765,
    22.97405281066881, 1.5400112152099443, -30.43261184692376, -49.23394012451172, -60.16849975585929, -50.38578643798894,
    -38.78122901916523, -9.972256851195993, -72.3205337524414, -74.64806671142577, -118.02866516113245, -193.58723449707026,
    -136.98485565185578, -60.94106674194336, -57.664899444580186, -127.32897491455043, -195.51205139160186, -90.65044479370155,
    94.3036117553711, 148.9656036376954, 103.99012908935597, 0.4163948059088014, -90.87290649414051
};
static float footGyroY[] = {
    -31.43259620666504, 247.18474426269532, -147.58735961914056, 388.0778594970707, 13.57281188964852, -4.56107759475708,
    31.303749465942378, 53.29034652709961, 82.10030364990233, 126.70256652832028, 224.11839294433597, 337.8426879882812,
    443.29039306640607, 469.75341796875, 466.82347412109385, 449.1170959472656, 297.4538513183594, 266.0752807617187,
    236.04187622070313, 132.0476837158203, 59.85009384155274, -18.212661647796573, -92.03454437255851, -122.14848022460929,
    -212.06942138671843, -281.6639099121094, -319.3783264160156, -330.84593505859374, -327.4829833984375, -327.86619262695314,
    -330.5755615234375, -334.9479370117188, -329.65684814453124, -329.4685485839844, -291.0905456542971, -218.8544921875,
    30.40917816161997, 311.0757553100579, 229.18706665038917, 103.20778198241908, 4.894908905029297, -10.675095295906205,
    33.332045364379795, 64.5823867797852, 103.13256530761703, 159.12294006347656, 263.37313842773426, 385.1228332519531,
    463.47338256836014, 487.6324584960936, 470.5518493652344, 399.8756225585946, 258.66478881835934, 238.86475830078135,
    202.28032531738282, 185.4875640869141, 19.151103568076906, -80.1861099243157, -151.86562194824208, -215.86854553222634,
    -309.31988525390625, -295.17227172851574, -307.18711547851535, -316.0004821777345, -295.0997314453125, -301.707275390625,
    -316.791845703125, -318.72902221679675, -358.12495117187484, -344.2837219238283, -236.28256225585938, -116.41312866210899,
    34.18097782730984, 129.87890167236384, 205.31823730468756, -55.1871452331543, 7.308565902709752, -45.88927154541109,
    10.751376533507843, 57.550111389160065, 81.84436798095703, 137.08691864013701, 218.27670898437395, 307.27872314453055,
    415.4840698242189, 430.3795471191406, 443.4722778320312, 412.86470947265747, 291.2759826660159, 251.64756164550784,
    234.6345367431641, 261.1344421386718, 203.74194335937617, -13.595177268981036, -138.1617584228514, -178.79290771484375,
    -269.593853759765, -274.94326171875036, -284.7231140136714, -356.6670104980468
};
static float footGyroZ[] = {
    84.87508392333984, 207.73139343261718, 151.10971984863284, 91.57426452636719, 47.58504867553711, 47.58586883544922,
    24.472009277343766, 12.318043518066407, 15.637135314941414, -7.193545722961412, -5.585521221160889, 24.11605110168456,
    68.05053482055662, 102.9433578491211, 105.4063491821289, 111.4576644897461, 94.80347595214845, 118.04217987060544,
    177.94530029296865, 230.54938354492188, 224.8760681152344, 192.34732360839845, 178.78194580078124, 156.51075744628918,
    59.47478332519559, -45.92501449584961, -140.75635528564447, -221.30979003906242, -276.9084106445313, -310.4105346679687,
    -335.6433410644531, -363.1178222656249, -396.59193725585936, -387.84872436523426, -239.86008605957082, -81.31034088134766,
    -54.53753662109412, 91.43735466003393, 213.32899475097645, 127.37186126709014, 51.65127944946289, 41.86546173095707,
    22.248192214965833, 4.847907829284683, 12.29768314361575, -11.957069396972654, -0.144747734069953, 43.667740631103484,
    83.53893890380849, 92.0406402587891, 103.14440155029295, 90.41868286132825, 88.02781066894528, 139.4209197998044,
    195.10359497070303, 239.0994873046875, 245.68005371093744, 220.81333312988303, 193.4405395507814, 126.31468963623072,
    7.198023796081543, -91.82835693359395, -180.9591552734369, -249.76005859374976, -294.5493164062499, -324.5779113769531,
    -345.2831481933594, -370.04337768554666, -399.9769226074219, -336.70938110351597, -176.8495635986328, -113.20860137939448,
    -50.70048217773509, 138.72407226562416, 212.92317504882797, 131.20759582519528, 60.24497375488267, 37.66574783325211,
    21.101990127563532, 2.8332813024521086, 12.174825668334961, -6.931411170959502, -13.121549606323322, 18.09287796020486,
    65.66719055175774, 85.833251953125, 108.8847808837891, 99.7300216674805, 87.33237457275375, 142.76052856445295,
    201.7528533935547, 241.33271179199227, 235.36947631835952, 205.91645202636735, 191.61889648437503, 165.8468017578125,
    66.02357482910261, -33.01237621307311, -123.1817260742183, -203.29990234374986
};
static float s_cyclingPower[] = {
    0, 0, 0, 0, 0, 10, 20, 25, 32, 54, 76, 98, 120, 142, 164, 186, 208, 230, 252, 274, 296, 318, 340, 362, 384, 402, 402,
    384, 362, 340, 318, 296, 274, 252, 230, 208, 186, 164, 142, 120, 98, 76, 54, 32, 10, 0, 10, 32, 54, 76, 98, 120, 133,
    142, 164, 186, 208, 230, 252, 274, 296, 318, 340, 362, 384, 402, 384, 362, 340, 318, 296, 274, 252, 230, 208, 186,
    177, 164, 155, 142, 130, 128, 124, 121, 120, 120, 115, 115, 111, 98, 76, 54, 32, 10, 5, 0, 0, 0, 0, 0,
};

/**
 * @brief fitness test
 *
 * @return 喂入fitness仿真：true 不喂入：false
 */
static bool lib_gm_fitness_test(void)
{
    // 未打开fitness仿真，直接返回
    if (s_fitness_simulate_switch == 0) {
       return false;
    }

    IndexIO gm_input = {0};
    ALGO_COMP_LOG_I("lib_gm:test fitness seconds:%u,cur:%u", s_fitness_seconds, s_cur_seconds);
    gm_input.timeZoneOffset = tz_get()->tz_minuteswest;
    gm_input.accLength = 25;
    s_cur_seconds++;
    for (uint8_t i = 0; i < s_ons_second_feed_times; i ++) {
        s_rtc_current_time++;
        gm_input.timestamp = s_rtc_current_time;

        // 默认值，使用系统速度
        gm_input.gpsAccuracy = -1;
        gm_input.gpsSpeed = -1;
        gm_input.longitude = -999.0f;
        gm_input.latitude = -999.0f;
        gm_input.cyclingCadence = -1;
        gm_input.cyclingPower = -1;

        // 喂入模拟数据
        static uint8_t k = 0;
        static uint8_t j = 0;
        gm_input.accX = footAccX + k * 50;
        gm_input.accY = footAccY + k * 50;
        gm_input.accZ = footAccZ + k * 50;
        gm_input.gyroX = footGyroX + k * 50;
        gm_input.gyroY = footGyroY + k * 50;
        gm_input.gyroZ = footGyroZ + k * 50;
        gm_input.speedRef = (s_speed_simulate_val == 999 ? 13.2f : s_speed_simulate_val);
        gm_input.altitude = 0.0f;
        gm_input.accLength = 50;
        gm_input.gyroLength = 50;
        gm_input.hrRef = 130 + rand() % 3;
        if (s_workout_type == WORKOUT_INDOOR_CYCLING || s_workout_type == WORKOUT_INDOOR_RUNNING) {
            gm_input.speedRef = -1;
        } else if (s_workout_type == WORKOUT_OUTDOOR_CYCLING || s_workout_type == WORKOUT_OUTDOOR_RUNNING ||
            s_workout_type == WORKOUT_TRAIL_RUNNING || s_workout_type == WORKOUT_OUTDOOR_WLAKING) { // 自动暂停恢复,二期直接使用系统速度,需要去掉
            gm_input.gpsAccuracy = 1;
            gm_input.gpsSpeed = gm_input.speedRef;
        }
        if (s_workout_type == WORKOUT_INDOOR_CYCLING) {
            gm_input.cyclingCadence = 60;
            gm_input.cyclingPower = s_cyclingPower[j];
            j++;
            if (j >= sizeof(s_cyclingPower) / sizeof(s_cyclingPower[0])) {
                j = 0;
            }
        }
        k = (k + 1) % 2;

        // gomore算法trigger
        // osKernelLock();
        algo_start_statistics(ALGO_GOMORE);
        uint32_t run_tick = rt_tick_get();
        int16_t ret = updateIndex(&gm_input);
        uint32_t end_tick = rt_tick_get();
        algo_stop_statistics(ALGO_GOMORE);
        // osKernelUnlock();
        algo_save_statistics("gomore", "gomore", ALGO_GOMORE);
        algo_gomore_collect(&gm_input, end_tick - run_tick, ret);
        ALGO_COMP_LOG_D("lib_gm:test fitness cadence:%.2f,Step len:%.2f,distance:%.7f,totalStep:%d,avgSpeed:%.2f",
            gm_input.fitnessOut.workout.run.cadence, gm_input.fitnessOut.workout.run.stepLen, gm_input.fitnessOut.workout.run.distance,
            gm_input.fitnessOut.workout.run.totalStep, gm_input.fitnessOut.workout.run.avgSpeed);
        if (ret >= 0) {
            // 此处输出算法结果
            lib_gm_fitness_callback_proc(&gm_input);
        } else {
            ALGO_COMP_LOG_E("lib_gm:test fitness %d", ret);
        }
    }
    return true;
}

/**
 * @brief gm算法仿真测试
 *
 * @param argc 参数个数
 * @param argv 参数
 * @return int32_t 错误码
 */
static int32_t lib_gm_test(int32_t argc, char* argv[])
{
    int32_t ret = -1;
    ALGO_COMP_LOG_I("lib_gm:test start");
    if (argc < 2)
    {
        ALGO_COMP_LOG_E("Invalid parameter len < 2");
        return 0;
    } else if (strcmp(argv[1], "sleep_sim") == 0) { // 睡眠仿真测试
        if (strcmp(argv[2], "open") == 0) {
            s_sleep_sim_flag = true;
            s_sleep_seconds = atoi(argv[3]);
            s_ons_second_feed_times = atoi(argv[4]);
            s_sleep_seconds = s_sleep_seconds * 60 / s_ons_second_feed_times;
            s_rtc_current_time = (uint32_t)get_sec_from_rtc();
            s_cur_seconds = 0;
            ALGO_COMP_LOG_I("lib_gm:test open %u,%u", s_sleep_seconds, s_ons_second_feed_times);
        } else if (strcmp(argv[2], "close") == 0) {
            s_sleep_sim_flag = false;
            s_ons_second_feed_times = 60;
            s_sleep_seconds = 0;
            s_cur_seconds = 0;
            s_rtc_current_time = 0;
            ALGO_COMP_LOG_I("lib_gm:test close %u,%u", s_sleep_seconds, s_ons_second_feed_times);
        } else if (strcmp(argv[2], "manual") == 0) {
            s_manual_sleep_flag = true;
            ALGO_COMP_LOG_I("lib_gm:test manual %d", s_manual_sleep_flag);
        }
        ret = 0;
    } else if (strcmp(argv[1], "gm_acc_sim") == 0) {
        // acc输入仿真开关
        s_acc_simulate_switch = atoi(argv[2]);
        ALGO_COMP_LOG_I("lib_gm:test sim switch %d", s_acc_simulate_switch);
        ret = 0;
    } else if (strcmp(argv[1], "gm_hr_sim") == 0) {
        // 心率输入仿真开关
        s_hr_simulate_val = atoi(argv[2]);
        ALGO_COMP_LOG_I("lib_gm:test sim switch %u", s_hr_simulate_val);
        ret = 0;
    } else if (strcmp(argv[1], "gm_speed_sim") == 0) {
        // 速度输入仿真开关
        s_speed_simulate_val = atoi(argv[2]);
        lib_gm_sim_speed_input();
        ALGO_COMP_LOG_I("lib_gm:test sim switch %u", s_speed_simulate_val);
        ret = 0;
    } else if (strcmp(argv[1], "gm_fitness_sim") == 0) {
        // fitness输入仿真开关
        if (strcmp(argv[2], "open") == 0) {
            s_fitness_simulate_switch = true;
            s_fitness_seconds = atoi(argv[3]);
            s_ons_second_feed_times = atoi(argv[4]);
            s_fitness_seconds = s_fitness_seconds * 60 / s_ons_second_feed_times;
            s_rtc_current_time = (uint32_t)get_sec_from_rtc();
            s_cur_seconds = 0;
            ALGO_COMP_LOG_I("lib_gm:test open %u,%u", s_fitness_seconds, s_ons_second_feed_times);
        } else if (strcmp(argv[2], "close") == 0) {
            s_fitness_simulate_switch = false;
            s_ons_second_feed_times = 60;
            s_fitness_seconds = 0;
            s_cur_seconds = 0;
            s_rtc_current_time = 0;
            ALGO_COMP_LOG_I("lib_gm:test close %u,%u", s_fitness_seconds, s_ons_second_feed_times);
        }
        ALGO_COMP_LOG_I("lib_gm:test sim switch %d", s_fitness_simulate_switch);
        ret = 0;
    } else {
        ALGO_COMP_LOG_E("Invalid parameter");
    }
    ALGO_COMP_LOG_I("lib_gm:test end ret=%d", ret);
    return 0;
}
FINSH_FUNCTION_EXPORT(lib_gm_test, "gm sim test");
MSH_CMD_EXPORT(lib_gm_test, "gm sim test");

/**
 * 命令行进行测试应该发送这些
 * 打开睡眠模拟：lib_gm_test sleep_sim open 分钟数 时间加速倍数（60的公约数） 例：lib_gm_test sleep_sim open 40 10 (睡眠40分钟，加速10倍喂入) 注：最大倍数小于60
 * 手动控制获取睡眠分期模拟：lib_gm_test sleep_sim manual
 * 关闭睡眠模拟：lib_gm_test sleep_sim close
 *
 * acc输入仿真开关：lib_gm_test gm_acc_sim 0/1
 * 模拟心率测试活动强度（小核）：lib_gm_test gm_hr_sim hr_value
 * 模拟速度测试活动强度（小核）：lib_gm_test gm_speed_sim speed_value
 * 打开fitness模拟：lib_gm_test gm_fitness_sim open 分钟数 时间加速倍数（60的公约数） 例：lib_gm_test gm_fitness_sim open 40 10 (运动40分钟，加速10倍喂入) 注：最大倍数小于60
 * 关闭fitness模拟：lib_gm_test gm_fitness_sim close
 *
 */
#endif
