/************************************************************************​
*Copyright(c) 2025, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#include "alg_power_estimation.h"
#include "alg_power_estimation_port.h"

static PowerEstimator s_power_estimator = { 0 };

void alg_power_estimation_init(const alg_power_estimation_init_t *init)
{
    power_estimator_init(&s_power_estimator, (PowerEstimatorInit *)init);
}

float alg_power_estimation_exec(const alg_power_estimation_input_t *input)
{
    return power_estimator_exec(&s_power_estimator, (PowerEstimatorInput *)input);
}
