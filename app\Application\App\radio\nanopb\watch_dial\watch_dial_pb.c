/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   watch_dial_pb.c
@Time    :   2025/04/18 10:35:46
*
**************************************************************************/

#include "watch_dial_pb.h"
#include "watch_dial.pb.h"
#include "pb_decode.h"
#include "ble_nus_srv.h"
#include "pb_encode.h"
#include "crc8.h"
#include "ble_cmd_response.h"
#include "ble_peripheral.h"
#include "ble_nus.h"
#include "pb_decode_common.h"
#include "qw_log.h"
#include "ff.h"
#include "touchgfx_js/touchgfx_js_api.h"

// 定义F_OK常量
#ifndef F_OK
#define F_OK 0
#endif

// 添加文件操作相关头文件

// 定义BLE命令常量，这些应该在项目的某个头文件中定义
#define DIAL_FILE_LIST_NUM_MAX_UPLOAD 20

// 日志宏定义，在实际项目中应使用项目的日志系统
#define WATCH_DIAL_PB_TAG             "watch_dial_pb"
#define WATCH_DIAL_PB_LOG_D(fmt, ...)      QW_LOG_D(WATCH_DIAL_PB_TAG, fmt, ##__VA_ARGS__)
#define WATCH_DIAL_PB_LOG_I(fmt, ...)      QW_LOG_I(WATCH_DIAL_PB_TAG, fmt, ##__VA_ARGS__)
#define WATCH_DIAL_PB_LOG_W(fmt, ...)      QW_LOG_W(WATCH_DIAL_PB_TAG, fmt, ##__VA_ARGS__)
#define WATCH_DIAL_PB_LOG_E(fmt, ...)      QW_LOG_E(WATCH_DIAL_PB_TAG, fmt, ##__VA_ARGS__)

// 全局数据结构定义
static watch_dial_file_info g_watch_dial_file_list[MAX_WATCH_DIAL_COUNT]; // 表盘文件列表
static watch_dial_config_info config_msg;                                 // 表盘配置信息
static uint32_t g_watch_dial_count = 0;                                    // 表盘数量
static uint32_t g_current_dial_id = 0;                                    // 当前使用的表盘ID
static uint32_t g_current_data_type_num = 0;                              // 当前可编辑数据类型数量
static uint32_t g_current_color_type_num = 0;                             // 当前可编辑颜色类型数量
static uint32_t g_current_data_type_use_num = 0;                          // 当前正在使用的数据类型数量

//-------------------------------------------------------------------------------------------
// Function Name : repeated_uint32_decode
// Purpose       : uint16_t 数据解码
// Param[in]     : pb_istream_t *stream
//                 const pb_field_t *field
//                 void **arg
// Param[out]    : None
// Return type   :
// Comment       : 2019-08-16
//-------------------------------------------------------------------------------------------
static bool repeated_uint32_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    uint32_t** ptr = (uint32_t**) arg;
    uint64_t value = 0;

    if (!pb_decode_varint(stream, &value))
    {
        return false;
    }
    **ptr = (uint32_t)value;
    (*ptr) ++;

    return true;
}

//-------------------------------------------------------------------------------------------
// Function Name : watch_dial_current_get_send
// Purpose       : 发送当前使用的表盘信息
// Param[in]     : None
// Param[out]    : None
// Return type   : void
// Comment       : 2025/04/18
//-------------------------------------------------------------------------------------------
void watch_dial_current_get_send(void)
{

}

// 读取表盘配置
static bool read_watch_dial_config(void)
{
    FIL file;
    char line[64] = {0};
    uint32_t dial_count = 0;
    uint32_t using_dial_index = 0;

    // 清空当前列表
    memset(g_watch_dial_file_list, 0, sizeof(g_watch_dial_file_list));
    g_watch_dial_count = 0;

    // 打开配置文件
    if (f_open(&file, "0:/iGPSPORT/Dial/dialCommonCfg.cfg", FA_READ) != FR_OK)
    {
        WATCH_DIAL_PB_LOG_E("%s %d: Failed to open config file\n", __func__, __LINE__);
        return false;
    }

    // 读取第一行 - 当前使用的表盘索引
    if (f_gets(line, sizeof(line), &file) != NULL)
    {
        if (sscanf(line, "usingDialIndex:%d", &using_dial_index) != 1)
        {
            WATCH_DIAL_PB_LOG_E("%s %d: Invalid format for usingDialIndex\n", __func__, __LINE__);
            f_close(&file);
            return false;
        }
    }

    // 读取第二行 - 表盘总数
    if (f_gets(line, sizeof(line), &file) != NULL)
    {
        if (sscanf(line, "dialCount:%d", &dial_count) != 1)
        {
            WATCH_DIAL_PB_LOG_E("%s %d: Invalid format for dialCount\n", __func__, __LINE__);
            f_close(&file);
            return false;
        }
    }

    // 读取每个表盘信息
    for (uint32_t i = 0; i < dial_count; i++)
    {
        if (f_gets(line, sizeof(line), &file) != NULL)
        {
            uint32_t dial_index = 0;
            uint32_t dial_id = 0;

            // 解析格式为 "dialX:Y" 的行，其中X是表盘ID，Y是表盘值
            if (sscanf(line, "dial%d:%d", &dial_index, &dial_id) == 2)
            {
                g_watch_dial_file_list[g_watch_dial_count].goodsid = dial_id;
                g_watch_dial_file_list[g_watch_dial_count].file_size = 0; // 暂时不使用
                g_watch_dial_file_list[g_watch_dial_count].in_use = (dial_id == using_dial_index) ? 1 : 0; 
                g_watch_dial_file_list[g_watch_dial_count].dial_type = WATCH_DIAL_TYPE_enum_WATCH_DIAL_TYPE_SYSTEM; // 暂时默认系统表盘

                g_watch_dial_count++;
            }
            else
            {
                WATCH_DIAL_PB_LOG_W("%s %d: Invalid format for dial entry: %s\n", __func__, __LINE__, line);
            }
        }
        else
        {
            break; // 文件结束或读取错误
        }
    }

    f_close(&file);

    // 设置当前表盘ID
    g_current_dial_id = using_dial_index;

    WATCH_DIAL_PB_LOG_I("%s %d: Loaded %u dials, current dial ID: %u\n",
                       __func__, __LINE__, g_watch_dial_count, g_current_dial_id);

    return true;
}

static bool dail_file_list_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    if (stream == NULL || field == NULL || arg == NULL || g_watch_dial_count == 0)
    {
        return false;
    }

    bool status = true;

    watch_dial_file_flag_message file_msg= {0};
    memset(&file_msg, 0, sizeof(watch_dial_file_flag_message));

    for (uint8_t i = 0; i < g_watch_dial_count && i < DIAL_FILE_LIST_NUM_MAX_UPLOAD; i++)
    {
        // 填充表盘信息
        file_msg.has_goodsid = true;
        file_msg.goodsid = g_watch_dial_file_list[i].goodsid;

        file_msg.has_file_size = true;
        file_msg.file_size = g_watch_dial_file_list[i].file_size;

        file_msg.has_in_use = true;
        file_msg.in_use = g_watch_dial_file_list[i].in_use;

        file_msg.has_dial_type = true;
        file_msg.dial_type = g_watch_dial_file_list[i].dial_type;

        // 编码表盘信息
        if (!pb_encode_tag_for_field(stream, field) ||
            !pb_encode_submessage(stream, watch_dial_file_flag_message_fields, &file_msg))
        {
            WATCH_DIAL_PB_LOG_E("%s %d: Failed to encode file list message\n", __func__, __LINE__);
            status = false;
            break;
        }
    }

    return status;
}

static bool dail_config_data_support_type_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    uint8_t status = false;
    uint32_t* ptr = (uint32_t*) (*arg);

    for (uint8_t i = 0; i < g_current_data_type_num; i++)
    {
        status = pb_encode_tag_for_field(stream, field);
        status &= pb_encode_varint(stream, (uint64_t) (*ptr));

        ptr++;
    }

    return status;
}

static bool dail_config_data_cur_type_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    uint8_t status = false;
    uint32_t* ptr = (uint32_t*) (*arg);

    for (uint8_t i = 0; i < g_current_data_type_use_num; i++)
    {
        status = pb_encode_tag_for_field(stream, field);
        status &= pb_encode_varint(stream, (uint64_t) (*ptr));

        ptr++;
    }

    return status;
}

static bool dail_config_color_support_type_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    uint8_t status = false;
    uint32_t* ptr = (uint32_t*) (*arg);

    for (uint8_t i = 0; i < g_current_color_type_num; i++)
    {
        status = pb_encode_tag_for_field(stream, field);
        status &= pb_encode_varint(stream, (uint64_t) (*ptr));

        ptr++;
    }

    return status;
}

//-------------------------------------------------------------------------------------------
// Function Name : watch_dial_list_send
// Purpose       : 发送表盘列表信息
// Param[in]     : None
// Param[out]    : None
// Return type   : void
// Comment       : 2025/04/18
//-------------------------------------------------------------------------------------------
void watch_dial_list_send(void)
{
    dial_msg dail_data_message;
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    uint8_t pb_crc = 0;

    memset(&dail_data_message, 0, sizeof(dial_msg));
    memset(&config_msg, 0, sizeof(watch_dial_config_info));
    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

    read_watch_dial_config();

    dail_data_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_DIAL;
    dail_data_message.operation_type = WATCH_DIAL_OPERATION_TYPE_enum_WATCH_DIAL_OPERATION_TYPE_WATCH_DIAL_LIST_GET;

    dail_data_message.has_file_list_get = false;
    dail_data_message.has_cfg_message = false;

    dail_data_message.file_list.arg = g_watch_dial_file_list;
    dail_data_message.file_list.funcs.encode = &dail_file_list_encode;

    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    pb_encode(&encode_stream, dial_msg_fields, &dail_data_message);

    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch0( );

    pb_crc = CRC_Calc8_Table_L(data, *length);

    ble_cmd_end_tx(dail_data_message.service_type, 0, dail_data_message.operation_type,
        0, encode_stream.bytes_written, pb_crc);
}

static uint32_t dial_file_num_get(void)
{
    FRESULT res;
    DIR s_dir; 
    FILINFO s_fileinfo;
    uint32_t file_num = 0;

#if _USE_LFN
    s_fileinfo.fsize = _MAX_LFN + 1;
    memset(s_fileinfo.fname, 0, _MAX_LFN + 1);
#endif

    res = f_opendir(&s_dir, "0:/iGPSPORT/Dial"); //打开一个目录
    if (res == FR_OK)
    {
        while (1)
        {
            res = f_readdir(&s_dir, &s_fileinfo);
            if (res != FR_OK || s_fileinfo.fname[0] == 0) break; //错误了/到末尾了,退出

            // 只计数以数字命名的文件夹
            if (s_fileinfo.fattrib & AM_DIR)
            {
                // 检查是否全部为数字
                bool is_numeric = true;
                for (int i = 0; s_fileinfo.fname[i] != 0; i++)
                {
                    if (s_fileinfo.fname[i] < '0' || s_fileinfo.fname[i] > '9')
                    {
                        is_numeric = false;
                        break;
                    }
                }

                if (is_numeric && s_fileinfo.fname[0] != 0)
                {
                    file_num++;
                }
            }
        }
    }

    return file_num;
}

//-------------------------------------------------------------------------------------------
// Function Name : watch_dial_list_numt_send
// Purpose       : 发送表盘数量信息
// Param[in]     : None
// Param[out]    : None
// Return type   : void
// Comment       : 2025/04/18
//-------------------------------------------------------------------------------------------
void watch_dial_list_numt_send(void)
{
    uint8_t pb_crc = 0;
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    dial_msg dial_data_message;

    memset(&dial_data_message, 0, sizeof(dial_msg));
    ble_data_var_tx_get(&data, &length, BLE_NUS_CH2_UUID);

    //参数赋值
    dial_data_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_DIAL;
    dial_data_message.operation_type = WATCH_DIAL_OPERATION_TYPE_enum_WATCH_DIAL_OPERATION_TYPE_WATCH_DIAL_LIST_NUM_GET;

    dial_data_message.has_file_list_get = true;
    dial_data_message.file_list_get.has_file_num = true;
    dial_data_message.file_list_get.file_num = dial_file_num_get();
    dial_data_message.file_list_get.has_file_list_support_num_max = false;
    dial_data_message.file_list_get.has_file_index_start = false;
    dial_data_message.file_list_get.has_file_index_end = false;
    //编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX);
    //编码
    pb_encode(&encode_stream, dial_msg_fields, &dial_data_message);

    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch2( );

    //对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);
    // rt_hexdump("unit_config_send", 16, data, *length);
    //命令协议 发送通道2
    ble_cmd_end_tx(dial_data_message.service_type, 0, dial_data_message.operation_type, 0, encode_stream.bytes_written, pb_crc);
}

//-------------------------------------------------------------------------------------------
// Function Name : get_watch_dial_file_info
// Purpose       : 获取表盘配置信息
// Param[in]     : uint8_t goodsid           表盘id
// Param[out]    : None
// Return type   : void
// Comment       : 2025/04/18
//-------------------------------------------------------------------------------------------
void watch_dial_cfg_get(uint32_t goodsid)
{
    dial_msg dail_data_message;
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    uint8_t pb_crc = 0;

    memset(&dail_data_message, 0, sizeof(dial_msg));
    memset(&config_msg, 0, sizeof(watch_dial_config_info));
    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

    read_watch_dial_config();

    dail_data_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_DIAL;
    dail_data_message.operation_type = WATCH_DIAL_OPERATION_TYPE_enum_WATCH_DIAL_OPERATION_TYPE_WATCH_DIAL_CFG_GET;
    dail_data_message.has_file_list_get = false;

    dail_data_message.has_cfg_message = true;
    dail_data_message.cfg_message.has_goodsid = true;
    dail_data_message.cfg_message.goodsid = goodsid;
    dail_data_message.cfg_message.has_color_cur_type = true;
    dail_data_message.cfg_message.color_cur_type = get_dial_cfg_color_inuse_index_to_app(goodsid, "");

    config_msg.goodsid = goodsid;
    config_msg.color_cur_type = get_dial_cfg_color_inuse_index_to_app(goodsid, "");

    g_current_data_type_num = get_dial_edit_type_num_to_app(&goodsid, "");
    get_edit_data_type_to_app(config_msg.data_support_type, &goodsid, "");

    g_current_color_type_num = get_dial_cfg_theme_color_num_to_app(goodsid, "");
    get_edit_color_type(config_msg.color_support_type, &goodsid);

    g_current_data_type_use_num = get_dial_data_num_to_app(&goodsid, "");
    get_using_data_type_to_app(config_msg.data_cur_type, &goodsid);

    dail_data_message.cfg_message.data_support_type.arg = config_msg.data_support_type;
    dail_data_message.cfg_message.data_support_type.funcs.encode = &dail_config_data_support_type_encode;

    dail_data_message.cfg_message.data_cur_type.arg = config_msg.data_cur_type;
    dail_data_message.cfg_message.data_cur_type.funcs.encode = &dail_config_data_cur_type_encode;

    dail_data_message.cfg_message.color_support_type.arg = config_msg.color_support_type;
    dail_data_message.cfg_message.color_support_type.funcs.encode = &dail_config_color_support_type_encode;

    dail_data_message.file_list.arg = g_watch_dial_file_list;
    dail_data_message.file_list.funcs.encode = &dail_file_list_encode;

    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    pb_encode(&encode_stream, dial_msg_fields, &dail_data_message);

    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch0();

    pb_crc = CRC_Calc8_Table_L(data, *length);

    ble_cmd_end_tx(dail_data_message.service_type, 0, dail_data_message.operation_type,
        0, encode_stream.bytes_written, pb_crc);
}

// 根据颜色id获取颜色类型索引
uint32_t get_color_type_index_by_typeid(uint32_t typeid)
{
    FIL file;
    char path[128];
    uint32_t type_index = 0xFFFF;
    // 打开配置文件
    memset(path, 0, sizeof(path));
    snprintf((char *)path, sizeof(path), "0:/iGPSPORT/Dial/%u%s/config.cfg", config_msg.goodsid, "");
    if (f_open(&file, path, FA_READ) != FR_OK)
    {
        WATCH_DIAL_PB_LOG_E("Failed to open config file %s\n", path);
        return type_index;
    }

    char line[64];
    while (f_gets(line, sizeof(line), &file)) {
        uint32_t id;
        uint32_t index;
        if (sscanf(line, "color%u:%u", &index, &id) == 2) {
            if (id == typeid) {
                type_index = index;
                break;
            }
        }
    }

    f_close(&file);
    return type_index;
}

//-------------------------------------------------------------------------------------------
// Function Name : get_watch_dial_file_info
// Purpose       : 设置表盘配置信息
// Param[in]     : uint8_t goodsid           表盘id
// Param[out]    : None
// Return type   : void
// Comment       : 2025/04/18
//-------------------------------------------------------------------------------------------
void watch_dial_cfg_set(uint32_t goodsid)
{
    uint32_t data_type_index = get_color_type_index_by_typeid(config_msg.color_cur_type);
    if (data_type_index != 0xFFFF)
    {
        set_dial_cfg_color_inuse_index(goodsid, "", data_type_index);
    }

    for (int i = 0; i < g_current_data_type_use_num; i++)
    {
        set_dial_data_type_to_app(i, config_msg.data_cur_type[i], &goodsid, "");
    }
}

uint32_t get_dial_index_by_goodsid(uint32_t goodsid)
{
    FIL file;
    // 打开配置文件
    if (f_open(&file, "0:/iGPSPORT/Dial/dialCommonCfg.cfg", FA_READ) != FR_OK)
    {
        WATCH_DIAL_PB_LOG_E("%s %d: Failed to open config file\n", __func__, __LINE__);
        return false;
    }

    char line[64];
    uint32_t foundIndex = 0xFFFF;

    while (f_gets(line, sizeof(line), &file)) {
        uint32_t id;
        uint32_t index;
        if (sscanf(line, "dial%u:%u", &index, &id) == 2) {
            if (id == goodsid) {
                foundIndex = index;
                break;
            }
        }
    }

    f_close(&file);
    return foundIndex;
}

//-------------------------------------------------------------------------------------------
// Function Name : set_current_watch_dial_id
// Purpose       : 设置当前使用的表盘ID
// Param[in]     : uint32_t goodsid   表盘ID
// Param[out]    : None
// Return type   : void
// Comment       : 2025/04/18
//-------------------------------------------------------------------------------------------
void watch_dial_use_set(uint32_t goodsid)
{
    uint32_t usingDialIndexValue = get_dial_index_by_goodsid(goodsid);
    // 如果找到匹配项，设置为当前表盘
    if (usingDialIndexValue != 0xFFFF) {
        set_app_operate_type(APP_USE_DIAL);
        set_inuse_dial_index(usingDialIndexValue);
        send_msg_to_gui_thread(goodsid);
    }
}

//-------------------------------------------------------------------------------------------
// Function Name : watch_dial_pb_data_decode
// Purpose       : 表盘PB协议数据解码接口函数
// Param[in]     : uint8_t * pb_buffer     PB协议数据缓冲区
//                 uint16_t buffer_length   数据长度
//                 END_TYPE end_type        发送端类型
// Param[out]    : None
// Return type   : void
// Comment       : 2025/04/18
//-------------------------------------------------------------------------------------------
void watch_dial_pb_data_decode(uint8_t * pb_buffer, uint16_t buffer_length, END_TYPE end_type)
{
    if (pb_buffer == NULL || buffer_length == 0)
    {
        WATCH_DIAL_PB_LOG_E("%s %d: Invalid parameters\n", __func__, __LINE__);
        return;
    }
    dial_msg msg;

    memset(&msg, 0, sizeof(dial_msg));
    memset(&config_msg, 0, sizeof(watch_dial_config_info));

    msg.cfg_message.data_support_type.arg = config_msg.data_support_type;
    msg.cfg_message.data_support_type.funcs.decode = &repeated_uint32_decode;

    msg.cfg_message.data_cur_type.arg = config_msg.data_cur_type;
    msg.cfg_message.data_cur_type.funcs.decode = &repeated_uint32_decode;

    msg.cfg_message.color_support_type.arg = config_msg.color_support_type;
    msg.cfg_message.color_support_type.funcs.decode = &repeated_uint32_decode;

    pb_istream_t decode_stream = pb_istream_from_buffer(pb_buffer, buffer_length);
    if (!pb_decode(&decode_stream, dial_msg_fields, &msg))
    {
        WATCH_DIAL_PB_LOG_D("%s %d: Failed to decode dial_msg\n", __func__, __LINE__);
        return;
    }

    config_msg.goodsid = msg.cfg_message.goodsid;
    config_msg.color_cur_type = msg.cfg_message.color_cur_type;

    // 根据操作类型处理请求
    // WATCH_DIAL_PB_LOG_D("%s %d: Operation type: %d\n", __func__, __LINE__, msg.operation_type);

    switch (msg.operation_type)
    {
        case WATCH_DIAL_OPERATION_TYPE_enum_WATCH_DIAL_OPERATION_TYPE_WATCH_DIAL_CURRENT_GET:
            watch_dial_current_get_send();
            break;

        case WATCH_DIAL_OPERATION_TYPE_enum_WATCH_DIAL_OPERATION_TYPE_WATCH_DIAL_ADD:
            break;

        case WATCH_DIAL_OPERATION_TYPE_enum_WATCH_DIAL_OPERATION_TYPE_WATCH_DIAL_DELETE:
            set_app_operate_type(APP_DEL_DIAL);
            send_msg_to_gui_thread(msg.cfg_message.goodsid);
            ble_cmd_status_tx(service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_DIAL, 0,
                WATCH_DIAL_OPERATION_TYPE_enum_WATCH_DIAL_OPERATION_TYPE_WATCH_DIAL_DELETE, 0, enumSUCCESS_STATUS);
            break;

        case WATCH_DIAL_OPERATION_TYPE_enum_WATCH_DIAL_OPERATION_TYPE_WATCH_DIAL_LIST_GET:
            watch_dial_list_send();
            break;

        case WATCH_DIAL_OPERATION_TYPE_enum_WATCH_DIAL_OPERATION_TYPE_WATCH_DIAL_LIST_NUM_GET:
            watch_dial_list_numt_send();
            break;

        case WATCH_DIAL_OPERATION_TYPE_enum_WATCH_DIAL_OPERATION_TYPE_WATCH_DIAL_CFG_GET:
            watch_dial_cfg_get(msg.cfg_message.goodsid);
            break;

        case WATCH_DIAL_OPERATION_TYPE_enum_WATCH_DIAL_OPERATION_TYPE_WATCH_DIAL_CFG_SET:
            watch_dial_cfg_set(msg.cfg_message.goodsid);
            ble_cmd_status_tx(service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_DIAL, 0,
                WATCH_DIAL_OPERATION_TYPE_enum_WATCH_DIAL_OPERATION_TYPE_WATCH_DIAL_CFG_SET, 0, enumSUCCESS_STATUS);
            break;

        case WATCH_DIAL_OPERATION_TYPE_enum_WATCH_DIAL_OPERATION_TYPE_WATCH_DIAL_USE:
            watch_dial_use_set(msg.cfg_message.goodsid);
            ble_cmd_status_tx(service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_DIAL, 0,
                WATCH_DIAL_OPERATION_TYPE_enum_WATCH_DIAL_OPERATION_TYPE_WATCH_DIAL_USE, 0, enumSUCCESS_STATUS);
            break;

        default:
            WATCH_DIAL_PB_LOG_E("%s %d: Unknown operation type: %d\n", __func__, __LINE__,
                                msg.operation_type);
            break;
    }
}


