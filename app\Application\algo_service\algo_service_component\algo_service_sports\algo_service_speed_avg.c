﻿/***********************************************************
 * @file algo_service_speed_avg.c
 * <AUTHOR> (<EMAIL>)
 * @brief 平均速度算法组件实现
 * @version 0.1
 * @date 2024-11-29
 *
 * @copyright Copyright (c) 2024-2025, Wuhan Qiwu Technology Co., Ltd
 *
***********************************************************/
#include "algo_service_speed_avg.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_task.h"
#include "qw_time_util.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"

// 输入数据
typedef struct
{
    uint16_t speed_zone;             // 速度区间
    uint32_t distance;               // 100 * m,
    uint32_t distance_lap;           // 100 * m,
    uint32_t total_timer_time;       // 移动时间 1000 * s,
    uint32_t total_timer_time_lap;   //圈移动时间 1000 * s,
    saving_status_e saving_status;   //数据记录的状态
} algo_speed_avg_sub_t;

static algo_speed_avg_sub_t s_algo_in = {0};

// 发布数据
static algo_speed_avg_pub_t s_algo_out = {0};

// 中间数据
static uint32_t s_last_time_spd = 0;

// 本算法打开标记
static bool s_is_open = false;

//更新区间时间
static void update_time_in_zone_speed(uint32_t *time_in_zone, uint8_t time_in_zone_num, uint16_t spd_zone, uint32_t total_timer_time)
{
    uint16_t zone, delta_time;

    //Input check.
    if (NULL == time_in_zone || 0 == time_in_zone_num || ALGO_TIME_IN_SPEED_ZONE_COUNT < time_in_zone_num || spd_zone < 10 || spd_zone == UINT16_MAX)
    {
        return;
    }

    if (total_timer_time <= s_last_time_spd)
    {
        return;
    }

    delta_time = total_timer_time - s_last_time_spd;
    s_last_time_spd = total_timer_time;

    zone = spd_zone / 10 - 1;

    if (0 <= zone && time_in_zone_num > zone)
    {
        time_in_zone[zone] += delta_time;
    }
}

/**
 * @brief 算法处理
 *
 * @param algo_out 输出数据
 * @param algo_in 输入数据
 */
static void algo_speed_avg_deal(algo_speed_avg_pub_t *algo_out, const algo_speed_avg_sub_t *algo_in)
{
    if (enum_status_saving == algo_in->saving_status)
    {
        //平均速度  distance(100*m) * 10 / time(s)
        if (0 == algo_in->total_timer_time)
        {
            algo_out->avg_speed = 0;
        }
        else
        {
            algo_out->avg_speed = (uint64_t) algo_in->distance * 10 / (algo_in->total_timer_time / 1000);   // 1000 * m/s,total_distance / total_timer_time
        }

        if (0 == algo_in->total_timer_time_lap)
        {
            algo_out->avg_speed_lap = 0;
        }
        else
        {
            algo_out->avg_speed_lap = (uint64_t) algo_in->distance_lap * 10
                                    / (algo_in->total_timer_time_lap / 1000);   // 1000 * m/s,total_distance / total_timer_time
        }

        algo_out->avg_pace = (0 == algo_out->avg_speed) ? 0 : (uint16_t) (1000000 / algo_out->avg_speed);
        algo_out->avg_pace_lap = (0 == algo_out->avg_speed_lap) ? 0 : (uint16_t) (1000000 / algo_out->avg_speed_lap);

        update_time_in_zone_speed(algo_out->time_in_speed_zone, ALGO_TIME_IN_SPEED_ZONE_COUNT, algo_in->speed_zone, algo_in->total_timer_time);
    }
}

/**
 * @brief 算法控制
 *
 * @param algo_out 输出数据
 * @param ctrl_type 控制类型
 */
static void algo_speed_avg_ctrl(algo_speed_avg_pub_t *algo_out, ctrl_type_e ctrl_type)
{
    if (enum_ctrl_start == ctrl_type)
    {
        memset(algo_out, 0, sizeof(algo_speed_avg_pub_t));
        s_last_time_spd = 0;
    }
    else if (enum_ctrl_lap == ctrl_type)
    {
        algo_out->avg_speed_pre_lap = algo_out->avg_speed_lap;
        algo_out->avg_speed_lap = 0;
        algo_out->avg_pace_pre_lap = algo_out->avg_pace_lap;
        algo_out->avg_pace_lap = 0;
    }
}

/**
 * @brief 算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_distance_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_SPEED_AVG;
    head.input_type = DATA_ID_ALGO_DISTANCE;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_timer_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_SPEED_AVG;
    head.input_type = DATA_ID_ALGO_TIMER;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_speed_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_SPEED_AVG;
    head.input_type = DATA_ID_ALGO_SPEED;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法控制订阅处理
 *
 * @param in 控制数据
 * @param len 数据长度
 */
static void algo_sports_ctrl_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_SPEED_AVG;
    head.input_type = DATA_ID_EVENT_SPORTS_CTRL;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_speed_avg_out_callback(const void *out, uint32_t len)
{
    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_ALGO_SPEED_AVG, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("%s publish error", __FUNCTION__);
    }
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_distance",
        .topic_id = DATA_ID_ALGO_DISTANCE,
        .callback = algo_distance_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_timer",
        .topic_id = DATA_ID_ALGO_TIMER,
        .callback = algo_timer_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_speed",
        .topic_id = DATA_ID_ALGO_SPEED,
        .callback = algo_speed_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
};

/**
 * @brief 算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_speed_avg_init(void)
{
    algo_speed_avg_sub_t *algo_in = &s_algo_in;
    algo_speed_avg_pub_t *algo_out = &s_algo_out;

    memset(algo_in, 0, sizeof(algo_speed_avg_sub_t));
    memset(algo_out, 0, sizeof(algo_speed_avg_pub_t));
    s_last_time_spd = 0;
    return 0;
}

/**
 * @brief 算法open
 *
 * @return int32_t 结果
 */
static int32_t algo_speed_avg_open(void)
{
    if (s_is_open)
    {
        ALGO_COMP_LOG_W("%s already open", __FUNCTION__);
        return 0;
    }

    ALGO_COMP_LOG_D("%s open", __FUNCTION__);

    s_is_open = true;
    algo_speed_avg_init();

    return algo_topic_list_subscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
}

/**
 * @brief 算法feed
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_speed_avg_feed(uint32_t input_type, void *data, uint32_t len)
{
    algo_speed_avg_sub_t *algo_in = &s_algo_in;
    algo_speed_avg_pub_t *algo_out = &s_algo_out;

    switch (input_type)
    {
    case DATA_ID_ALGO_DISTANCE:
    {
        const algo_distance_pub_t *distance_data = (algo_distance_pub_t *) data;
        algo_in->distance = distance_data->distance;
        algo_in->distance_lap = distance_data->distance_lap;
    }
    break;
    case DATA_ID_ALGO_TIMER:
    {
        const algo_timer_pub_t *timer_data = (algo_timer_pub_t *) data;
        algo_in->total_timer_time = timer_data->timer_total.timer_time;
        algo_in->total_timer_time_lap = timer_data->timer_lap.timer_time;
    }
    break;
    case DATA_ID_ALGO_SPEED:
    {
        const algo_speed_pub_t *speed_data = (algo_speed_pub_t *) data;
        algo_in->speed_zone = speed_data->speed_zone;
    }
    break;
    case DATA_ID_EVENT_SPORTS_CTRL:
    {
        const algo_sports_ctrl_t *sports_ctrl = (algo_sports_ctrl_t *) data;
        algo_in->saving_status = sports_ctrl->saving_status;

        //算法控制
        algo_speed_avg_ctrl(algo_out, sports_ctrl->ctrl_type);

        //数据发布
        if (sports_ctrl->ctrl_type == enum_ctrl_null)
        {
            //算法处理
            algo_speed_avg_deal(algo_out, algo_in);

            //数据发布
            algo_speed_avg_out_callback(algo_out, sizeof(algo_speed_avg_pub_t));
        }
        else if (sports_ctrl->ctrl_type == enum_ctrl_lap)
        {
            //记圈后马上数据发布
            algo_speed_avg_out_callback(algo_out, sizeof(algo_speed_avg_pub_t));
        }
    }
    break;
    default:
        break;
    }
    return 0;
}

/**
 * @brief 算法close
 *
 * @return int32_t 结果
 */
static int32_t algo_speed_avg_close(void)
{
    if (!s_is_open)
    {
        ALGO_COMP_LOG_W("%s already close", __FUNCTION__);
        return 0;
    }

    algo_topic_list_unsubscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));

    s_is_open = false;
    ALGO_COMP_LOG_D("%s close", __FUNCTION__);
    return 0;
}

// 算法组件实现
static algo_compent_ops_t s_speed_avg_algo = {
    .init = algo_speed_avg_init,
    .open = algo_speed_avg_open,
    .feed = algo_speed_avg_feed,
    .close = algo_speed_avg_close,
    .ioctl = NULL,
};

/**
 * @brief 组件注册
 *
 * @return int32_t 结果
 */
int32_t register_speed_avg_algo(void)
{
    algo_compnent_register(ALGO_TYPE_SPEED_AVG, &s_speed_avg_algo);
    return 0;
}