﻿  QwMsgList.cpp
  QwPageIndicator.cpp
  QwWatchLock.cpp
  Translate.cpp
  JsApp.cpp
  PM_Base.cpp
  PageModel.cpp
  MsgAOD.cpp
  MsgAchReset.cpp
  MsgAchUpdate.cpp
  MsgAlarmReminder.cpp
  MsgAlarmTip.cpp
  MsgAlipayConfirm.cpp
E:\Snowa\qw_platform\qwos\module\gui\GUICtrl\QwMsgBox\QwMsgList.cpp(681,17): warning C4101: “slider_type”: 未引用的局部变量
  MsgAlipayStatus.cpp
  MsgAlipay.cpp
  MsgAltimterAcl.cpp
  MsgAltimterIcon.cpp
  MsgBindWatchFail.cpp
  MsgBindWatchSuccess.cpp
  MsgUnbindWatch.cpp
  MsgBpwrCalibResult.cpp
  MsgBpwrCalib.cpp
  MsgChargeRing.cpp
  MsgUnderVolOff.cpp
  MsgUnderVolTen.cpp
  MsgUnderVolTwenty.cpp
  MsgDataSync.cpp
  MsgDecodeERR.cpp
  MsgDelTrainingCourse.cpp
  MsgDrinkWater.cpp
  MsgFTPTestReminder.cpp
  MsgFecNotCon.cpp
  MsgFindWatch.cpp
  MsgDND.cpp
  MsgSleepBed.cpp
  MsgFtpUpdate.cpp
  MsgGiveUpSport.cpp
  MsgHealthGoal.cpp
  MsgHeartRate.cpp
  MsgHeartRateNotice.cpp
  MsgPressure.cpp
  MsgPressureRemind.cpp
  MsgSpo2.cpp
  MsgIncomingCall.cpp
  MsgIntelligentNotify.cpp
  MsgLoading.cpp
  MsgLtHrUpdate.cpp
  MsgMaxHrUpdate.cpp
  MsgNaviBack.cpp
  MsgDelNavi.cpp
  MsgOTAFinish.cpp
  MsgPowerReminder.cpp
  MsgPowerSave.cpp
  MsgPowerSaveUnlock.cpp
  MsgReconnectSensor.cpp
  MsgRemoveSensor.cpp
  MsgReset.cpp
  MsgSaveSport.cpp
  MsgSensorLowPower.cpp
  MsgSensorStateToast.cpp
  MsgSportGpsTip.cpp
  MsgSportOutCountDown.cpp
  MsgSportRemind.cpp
  MsgSportStartCountDown.cpp
  MsgSportingGpsState.cpp
  MsgSportingLap.cpp
  MsgTimerEnd.cpp
  MsgTipSelect.cpp
  MsgToastTip.cpp
  MsgTodayTrainTip.cpp
  MsgTriathlonTip.cpp
  MsgUnlock.cpp
  MsgVrbBackward.cpp
  MsginnerTraintip.cpp
  QwMsgBottomToast.cpp
  QwMsgFullScreenEventBase.cpp
  QwMsgFullScreenKeyTip.cpp
  QwMsgHalfScreen.cpp
  QwMsgMessageNotify.cpp
  QwMsgText.cpp
  QwMsgToastTip.cpp
  SensorConnReachedLimit.cpp
  QwAppScrollList.cpp
  QwBattery.cpp
  QwBottomToast.cpp
  QwFaBtn.cpp
  QwFullFunction.cpp
  QwFullScreen.cpp
  GridAltitude.cpp
  GridBar.cpp
  GridBunny.cpp
  GridCompass.cpp
  GridFitness.cpp
  GridHrm.cpp
  GridLap.cpp
  GridNavi.cpp
  GridText.cpp
  GridWktMonitor.cpp
  GridWorkout.cpp
  GridZone.cpp
  QwScrollGrid.cpp
  QhstIcon.cpp
  QhstImgText.cpp
  QhstNumberUnit.cpp
  QwHalfScreen.cpp
  ItemCheckBox.cpp
  ItemExercise.cpp
  ItemImageText.cpp
  ItemImgRadio.cpp
  ItemNotificationCall.cpp
  ItemNotificationNote.cpp
  ItemRadio.cpp
  ItemAddSensor.cpp
  ItemSensorCheck.cpp
  ItemSensorLinkText.cpp
  ItemSensorShow.cpp
  ItemSwitch.cpp
  ItemText.cpp
  ItemTrainingPlan.cpp
  QwMenuItem.cpp
  QwNavigationBar.cpp
  QwItemLap.cpp
  QwItemSummary.cpp
  QwToastTip.cpp
  QwTopStatus.cpp
  QwVerticalSwipeContainer.cpp
  QwWorkoutBlank.cpp
  QwWorkoutStepContainer.cpp
  QwWorkoutStepInfo.cpp
  QwDataKeyText.cpp
  MenuImageCache.cpp
  WatchLockService.cpp
  MsgBoxService.cpp
  MvcApp.cpp
  ActivityDuration.cpp
  ActivityDurationDailyPageView.cpp
  ActivityDurationModel.cpp
  ActivityDurationView.cpp
  ActivityDurationViewModel.cpp
  ActivityDurationWeekPageView.cpp
  AltimeterCalibration.cpp
  AltimeterCalibrationModel.cpp
  AltimeterCalibrationView.cpp
  AltimeterCalibrationViewModel.cpp
  AltimeterSelect.cpp
  AltimeterSelectModel.cpp
  AltimeterSelectView.cpp
  AltimeterSelectViewModel.cpp
  AlitmeterHistroyPage.cpp
  Altimeter.cpp
  AltimeterModel.cpp
  AltimeterSetting.cpp
  AltimeterView.cpp
  AltimeterViewModel.cpp
  Barometer.cpp
  BarometerModel.cpp
  BarometerView.cpp
  BarometerViewModel.cpp
  CaloriesCount.cpp
  CaloriesCountModel.cpp
  CaloriesCountView.cpp
  CaloriesCountViewModel.cpp
  CaloriesDailyPageView.cpp
  CaloriesWeekPageView.cpp
  CompassAlign.cpp
  CompassAlignModel.cpp
  CompassAlignView.cpp
  CompassAlignViewModel.cpp
  Compass.cpp
  CompassModel.cpp
  CompassView.cpp
  CompassViewModel.cpp
  HeartRate.cpp
  HeartRateDailyPageView.cpp
  HeartRateModel.cpp
  HeartRateView.cpp
  HeartRateViewModel.cpp
  HeartRateWeekPageView.cpp
  Hrv.cpp
  HrvDailyPageView.cpp
  HrvModel.cpp
  HrvView.cpp
  HrvViewModel.cpp
  HrvWeekPageView.cpp
  NotificationDetails.cpp
  NotificationDetailsModel.cpp
  NotificationDetailsView.cpp
  NotificationDetailsViewModel.cpp
  NotificationInit.cpp
  NotificationInitModel.cpp
  NotificationInitViewModel.cpp
  IntelligentNotification.cpp
  IntelligentNotificationModel.cpp
  IntelligentNotificationView.cpp
  IntelligentNotificationViewModel.cpp
  IntenseDuration.cpp
  IntenseDurationDailyPageView.cpp
  IntenseDurationModel.cpp
  IntenseDurationView.cpp
  IntenseDurationViewModel.cpp
  IntenseDurationWeekPageView.cpp
  NavigationDirection.cpp
  NavigationDirectionModel.cpp
  NavigationDirectionView.cpp
  NavigationDirectionViewModel.cpp
  NavigationMapDirection.cpp
  NavigationMapDirectionModel.cpp
  NavigationMapDirectionView.cpp
  NavigationMapDirectionViewModel.cpp
  NavigationMenus.cpp
  NavigationMenusModel.cpp
  NavigationMenusView.cpp
  NavigationMenusViewModel.cpp
  NaviAltitudeCon.cpp
  NaviTrackCon.cpp
  NavigationPreview.cpp
  NavigationPreviewModel.cpp
  NavigationPreviewView.cpp
  NavigationPreviewViewModel.cpp
  NavigationRoute.cpp
  NavigationRouteModel.cpp
  NavigationRouteView.cpp
  NavigationRouteViewModel.cpp
  NavigationSet.cpp
  NavigationSetModel.cpp
  NavigationSetView.cpp
  NavigationSetViewModel.cpp
  Pressure.cpp
  PressureDailyPageView.cpp
  PressureLevelBarChart.cpp
  PressureModel.cpp
  PressureStageView.cpp
  PressureView.cpp
  PressureViewModel.cpp
  PressureWeekPageView.cpp
  QwDailyBarChart.cpp
  QwDailyRangeChart.cpp
  QwWeekBarChart.cpp
  QwWeekPointChart.cpp
  RideAbility.cpp
  RideAbilityModel.cpp
  RideAbilityView.cpp
  RideAbilityViewModel.cpp
  RideLacticAcidPage.cpp
  RideOxygenPage.cpp
  RideRadarPage.cpp
  GradePredictionsPage.cpp
  LacticAcidPage.cpp
  OxygenUptakePage.cpp
  RunAbility.cpp
  RunAbilityModel.cpp
E:\Snowa\app\Application\App\UI\Page\AppMenu\RideAbility\RideAbilityModel.cpp(101,38): warning C4305: “=”: 从“double”到“float”截断
  RunAbilityView.cpp
  RunAbilityViewModel.cpp
  RunRadarPage.cpp
  SleepDailyPageView.cpp
  SleepInfo.cpp
  SleepInfoModel.cpp
  SleepInfoView.cpp
  SleepInfoViewModel.cpp
  SleepNapPageView.cpp
  SleepNoticePageView.cpp
E:\Snowa\app\Application\App\UI\Page\AppMenu\RunAbility\RunAbilityModel.cpp(106,38): warning C4305: “=”: 从“double”到“float”截断
  SleepStageView.cpp
  SleepWeekPageView.cpp
  SleepSpo2View.cpp
  Spo2.cpp
  Spo2DailyPageView.cpp
  Spo2Model.cpp
  Spo2View.cpp
  Spo2ViewModel.cpp
  Spo2WeekPageView.cpp
  StepsCount.cpp
  StepsCountModel.cpp
  StepsCountView.cpp
  StepsCountViewModel.cpp
  StepsDailyPageView.cpp
  StepsWeekPageView.cpp
  TodayActivitys.cpp
  TodayActivitysDailyPageView.cpp
  TodayActivitysModel.cpp
  TodayActivitysView.cpp
  TodayActivitysViewModel.cpp
  HistroyLoadPage.cpp
  HistroyTrendPage.cpp
  PhysicalRecoveryPage.cpp
  TraingStatus.cpp
  TraingStatusModel.cpp
  TraingStatusPage.cpp
  TraingStatusView.cpp
  TraingStatusViewModel.cpp
  CourseDetails.cpp
  CourseDetailsModel.cpp
  CourseDetailsView.cpp
  CourseDetailsViewModel.cpp
  TrainingCourseDel.cpp
  TrainingCourseDelModel.cpp
  TrainingCourseDelView.cpp
  TrainingCourseDelViewModel.cpp
  TrainingCourse.cpp
  TrainingCourseModel.cpp
  TrainingCourseView.cpp
  TrainingCourseViewModel.cpp
  TrainingPlan.cpp
  TrainingPlanModel.cpp
  TrainingPlanView.cpp
  TrainingPlanViewModel.cpp
  Weather.cpp
  WeatherHourPage.cpp
  WeatherModel.cpp
  WeatherNodataPage.cpp
  WeatherTodayPage.cpp
  WeatherView.cpp
  WeatherViewModel.cpp
  WeatherWeeklyPage.cpp
  BatteruChargingStatus.cpp
  BatteruChargingStatusModel.cpp
  BatteruChargingStatusView.cpp
  BatteruChargingStatusViewModel.cpp
  BuzzerTest.cpp
  BuzzerTestModel.cpp
E:\Snowa\app\Application\App\UI\Page\AppMenu\Weather\WeatherModel.cpp(206,45): warning C4305: “=”: 从“__int64”到“uint32_t”截断
E:\Snowa\app\Application\App\UI\Page\AppMenu\Weather\WeatherModel.cpp(206,45): warning C4309: “=”: 截断常量值
  BuzzerTestView.cpp
  BuzzerTestViewModel.cpp
  CompassData.cpp
  CompassDataModel.cpp
  CompassDataView.cpp
  CompassDataViewModel.cpp
  CurrentTestControl.cpp
  CurrentTestControlModel.cpp
  CurrentTestControlView.cpp
  CurrentTestControlViewModel.cpp
  DM_PPG_TEST.cpp
  DM_PPG_TESTModel.cpp
  DM_PPG_TESTView.cpp
  DM_PPG_TESTViewModel.cpp
  DeveloperMode.cpp
  DeveloperModeModel.cpp
  DeveloperModeView.cpp
  DeveloperModeViewModel.cpp
  FAT_AgingTest.cpp
  FAT_AgingTestModel.cpp
  FAT_AgingTestView.cpp
  FAT_AgingTestViewModel.cpp
  FAT_Barometer.cpp
  FAT_BarometerModel.cpp
  FAT_BarometerView.cpp
  FAT_BarometerViewModel.cpp
  FAT_GpsSignal.cpp
  FAT_GpsSignalModel.cpp
  FAT_GpsSignalView.cpp
  FAT_GpsSignalViewModel.cpp
  GpsSignalInfoCon.cpp
  GpsSignalSNRCon.cpp
  GpsSnrItem.cpp
  FAT_KeyTest.cpp
  FAT_KeyTestModel.cpp
  FAT_KeyTestView.cpp
  FAT_KeyTestViewModel.cpp
  FAT_LightSensor.cpp
  FAT_LightSensorModel.cpp
  FAT_LightSensorView.cpp
  FAT_LightSensorViewModel.cpp
  FAT_ScreenColor.cpp
  FAT_ScreenColorModel.cpp
  FAT_ScreenColorView.cpp
  FAT_ScreenColorViewModel.cpp
  FAT_VersionInfo.cpp
  FAT_VersionInfoModel.cpp
  FAT_VersionInfoView.cpp
  FAT_VersionInfoViewModel.cpp
  FactoryAddSensor.cpp
  FactoryAddSensorModel.cpp
  FactoryAddSensorView.cpp
  FactoryAddSensorViewModel.cpp
  FactoryCrank.cpp
  FactoryCrankModel.cpp
  FactoryCrankView.cpp
  FactoryCrankViewModel.cpp
  FactoryEditSensor.cpp
  FactoryEditSensorModel.cpp
  FactoryEditSensorView.cpp
  FactoryEditSensorViewModel.cpp
  FactorySensorMenu.cpp
  FactorySensorMenuModel.cpp
  FactorySensorMenuView.cpp
  FactorySensorMenuViewModel.cpp
  FactorySensors.cpp
  FactorySensorsModel.cpp
  FactorySensorsView.cpp
  FactorySensorsViewModel.cpp
  FactoryCompassAlign.cpp
  FactoryCompassAlignModel.cpp
  FactoryCompassAlignView.cpp
  FactoryCompassAlignViewModel.cpp
  FactoryGoMore.cpp
  FactoryGoMoreModel.cpp
  FactoryGoMoreView.cpp
  FactoryGoMoreViewModel.cpp
  AppScrollList.cpp
  DriversSwitchContainer.cpp
  FactoryGpsSignal.cpp
  FactoryGpsSignalModel.cpp
  FactoryGpsSignalView.cpp
  FactoryGpsSignalViewModel.cpp
  SignalDataContainer.cpp
  FactoryGpsStartTest.cpp
  FactoryGpsStartTestModel.cpp
  FactoryGpsStartTestView.cpp
  FactoryGpsStartTestViewModel.cpp
  FactoryMenu.cpp
  FactoryMenuModel.cpp
  FactoryMenuView.cpp
  FactoryMenuViewModel.cpp
  FactoryMode.cpp
  FactoryModeModel.cpp
  FactoryModeView.cpp
  FactoryModeViewModel.cpp
  FactoryQrCode.cpp
  FactoryQrCodeModel.cpp
  FactoryQrCodeView.cpp
  FactoryQrCodeViewModel.cpp
  FAT_QR_info_con.cpp
  FAT_QR_pair_con.cpp
  FactorySensorLink.cpp
  FactorySensorLinkModel.cpp
  FactorySensorLinkView.cpp
  FactorySensorLinkViewModel.cpp
  MotorTest.cpp
  MotorTestModel.cpp
  MotorTestView.cpp
  MotorTestViewModel.cpp
  RTCAccuracyTest.cpp
  RTCAccuracyTestModel.cpp
  RTCAccuracyTestView.cpp
  RTCAccuracyTestViewModel.cpp
  ScreenBrightnessTest.cpp
  ScreenBrightnessTestModel.cpp
  ScreenBrightnessTestView.cpp
  ScreenBrightnessTestViewModel.cpp
  SensorSelfCheck.cpp
  SensorSelfCheckModel.cpp
  SensorSelfCheckView.cpp
  SensorSelfCheckViewModel.cpp
  SixAxisSensorData.cpp
  SixAxisSensorDataModel.cpp
  SixAxisSensorDataView.cpp
  SixAxisSensorDataViewModel.cpp
  TouchPanelTest.cpp
  TouchPanelTestModel.cpp
  TouchPanelTestView.cpp
  TouchPanelTestViewModel.cpp
  HistoryAchCheck.cpp
  HistoryAchCheckModel.cpp
  HistoryAchCheckView.cpp
  HistoryAchCheckViewModel.cpp
  HistoryAchDeleted.cpp
  HistoryAchDeletedModel.cpp
  HistoryAchDeletedView.cpp
  HistoryAchDeletedViewModel.cpp
  HistoryAchDetails.cpp
  HistoryAchDetailsModel.cpp
  HistoryAchDetailsView.cpp
  HistoryAchDetailsViewModel.cpp
  HistoryAch.cpp
  HistoryAchModel.cpp
  HistoryAchView.cpp
  HistoryAchViewModel.cpp
  HistoryDelList.cpp
  HistoryDelListModel.cpp
  HistoryDelListView.cpp
  HistoryDelListViewModel.cpp
  HistoryList.cpp
  HistoryListModel.cpp
  HistoryListView.cpp
  HistoryListViewModel.cpp
  HistoryMenu.cpp
  HistoryMenuModel.cpp
  HistoryMenuView.cpp
  HistoryMenuViewModel.cpp
  ImageTest.cpp
  ImageTestModel.cpp
  ImageTestView.cpp
  ImageTestViewModel.cpp
  AodScreen.cpp
  Launcher.cpp
  LauncherModel.cpp
  LauncherView.cpp
  LauncherViewModel.cpp
  Logo.cpp
  LogoModel.cpp
  LogoView.cpp
  LogoViewModel.cpp
  OTACheck.cpp
  OTACheckModel.cpp
  OTACheckView.cpp
  OTACheckViewModel.cpp
  AppCard.cpp
  AchievementsCard.cpp
  ActiveHoursCard.cpp
  AltimeterCard.cpp
  BarometerCard.cpp
  CaloriesCard.cpp
  CompassCard.cpp
  CyclingCard.cpp
  DailyActivityCard.cpp
  HeartRateCard.cpp
  HistoryCard.cpp
  HrvCard.cpp
  IntenseCard.cpp
  NavigationCard.cpp
  NotificationCard.cpp
  PressureCard.cpp
  RunAbilityCard.cpp
  SettingCard.cpp
  SleepCard.cpp
  Spo2Card.cpp
  StepCountCard.cpp
  TraingStatusCard.cpp
  TrainingCoursesCard.cpp
  TrainingPlanCard.cpp
  WeatherCard.cpp
  BlankCard.cpp
  MenuCard.cpp
  MenuCardModel.cpp
  MenuCardView.cpp
  MenuCardViewModel.cpp
  PowerSaveDesk.cpp
  PowerSaveDeskModel.cpp
  PowerSaveDeskView.cpp
  PowerSaveDeskViewModel.cpp
  AutoPageSetting.cpp
  AutoPageSettingModel.cpp
  AutoPageSettingView.cpp
  AutoPageSettingViewModel.cpp
  AutoPauseSetting.cpp
  AutoPauseSettingModel.cpp
  AutoPauseSettingView.cpp
  AutoPauseSettingViewModel.cpp
  AutoRecordDataType.cpp
  AutoRecordDataTypeModel.cpp
  AutoRecordDataTypeView.cpp
  AutoRecordDataTypeViewModel.cpp
  AutoRecordLapSetting.cpp
  AutoRecordLapSettingModel.cpp
  AutoRecordLapSettingView.cpp
  AutoRecordLapSettingViewModel.cpp
  CountdownSetting.cpp
  CountdownSettingModel.cpp
  CountdownSettingView.cpp
  CountdownSettingViewModel.cpp
  MetronomeRemindMode.cpp
  MetronomeRemindModeModel.cpp
  MetronomeRemindModeView.cpp
  MetronomeRemindModeViewModel.cpp
  MetronomeSetting.cpp
  MetronomeSettingModel.cpp
  MetronomeSettingView.cpp
  MetronomeSettingViewModel.cpp
  MetronomeTipFreq.cpp
  MetronomeTipFreqModel.cpp
  MetronomeTipFreqView.cpp
  MetronomeTipFreqViewModel.cpp
  SmartTacxMenus.cpp
  SmartTacxMenusModel.cpp
  SmartTacxMenusView.cpp
  SmartTacxMenusViewModel.cpp
  SmartTacxModeSelect.cpp
  SmartTacxModeSelectModel.cpp
  SmartTacxModeSelectView.cpp
  SmartTacxModeSelectViewModel.cpp
  CadRemindEnable.cpp
  CadRemindEnableModel.cpp
  CadRemindEnableView.cpp
  CadRemindEnableViewModel.cpp
  CadRemindSetting.cpp
  CadRemindSettingModel.cpp
  CadRemindSettingView.cpp
  CadRemindSettingViewModel.cpp
  ConsumeRemindEnable.cpp
  ConsumeRemindEnableModel.cpp
  ConsumeRemindEnableView.cpp
  ConsumeRemindEnableViewModel.cpp
  DistanceRemindEnable.cpp
  DistanceRemindEnableModel.cpp
  DistanceRemindEnableView.cpp
  DistanceRemindEnableViewModel.cpp
  HrmRemindEnable.cpp
  HrmRemindEnableModel.cpp
  HrmRemindEnableView.cpp
  HrmRemindEnableViewModel.cpp
  HrmRemindSetting.cpp
  HrmRemindSettingModel.cpp
  HrmRemindSettingView.cpp
  HrmRemindSettingViewModel.cpp
  PaceRemindEnable.cpp
  PaceRemindEnableModel.cpp
  PaceRemindEnableView.cpp
  PaceRemindEnableViewModel.cpp
  PaceRemindSetting.cpp
  PaceRemindSettingModel.cpp
  PaceRemindSettingView.cpp
  PaceRemindSettingViewModel.cpp
  PwrRemindEnable.cpp
  PwrRemindEnableModel.cpp
  PwrRemindEnableView.cpp
  PwrRemindEnableViewModel.cpp
  PwrRemindSetting.cpp
  PwrRemindSettingModel.cpp
  PwrRemindSettingView.cpp
  PwrRemindSettingViewModel.cpp
  SpeedRemindEnable.cpp
  SpeedRemindEnableModel.cpp
  SpeedRemindEnableView.cpp
  SpeedRemindEnableViewModel.cpp
  SpeedRemindSetting.cpp
  SpeedRemindSettingModel.cpp
  SpeedRemindSettingView.cpp
  SpeedRemindSettingViewModel.cpp
  SportRemindSettingsMenu.cpp
  SportRemindSettingsMenuModel.cpp
  SportRemindSettingsMenuView.cpp
  SportRemindSettingsMenuViewModel.cpp
  StepNumRemindEnable.cpp
  StepNumRemindEnableModel.cpp
  StepNumRemindEnableView.cpp
  StepNumRemindEnableViewModel.cpp
  StepRateRemindEnable.cpp
  StepRateRemindEnableModel.cpp
  StepRateRemindEnableView.cpp
  StepRateRemindEnableViewModel.cpp
  SupplyRemindEnable.cpp
  SupplyRemindEnableModel.cpp
  SupplyRemindEnableView.cpp
  SupplyRemindEnableViewModel.cpp
  TimeRemindEnable.cpp
  TimeRemindEnableModel.cpp
  TimeRemindEnableView.cpp
  TimeRemindEnableViewModel.cpp
  SportSettingsMore.cpp
  SportSettingsMoreModel.cpp
  SportSettingsMoreView.cpp
  SportSettingsMoreViewModel.cpp
  SportSettings.cpp
  SportSettingsModel.cpp
  SportSettingsView.cpp
  SportSettingsViewModel.cpp
  SportStart.cpp
  SportStartModel.cpp
  SportStartView.cpp
  SportStartViewModel.cpp
  SportStopMenus.cpp
  SportStopMenusModel.cpp
  SportStopMenusView.cpp
  SportStopMenusViewModel.cpp
  SportsMenu.cpp
  SportsMenuModel.cpp
  SportsMenuView.cpp
  SportsMenuViewModel.cpp
  SwimLaneSetting.cpp
  SwimLaneSettingModel.cpp
  SwimLaneSettingView.cpp
  SwimLaneSettingViewModel.cpp
  TrackSetting.cpp
  TrackSettingModel.cpp
  TrackSettingView.cpp
  TrackSettingViewModel.cpp
  TodayTraining.cpp
  TodayTrainingModel.cpp
  TodayTrainingView.cpp
  TodayTrainingViewModel.cpp
  IntervalCustomMenus.cpp
  IntervalCustomMenusModel.cpp
  IntervalCustomMenusView.cpp
  IntervalCustomMenusViewModel.cpp
  IntervalCustomRest.cpp
  IntervalCustomRestModel.cpp
  IntervalCustomRestView.cpp
  IntervalCustomRestViewModel.cpp
  IntervalCustomTrain.cpp
  IntervalCustomTrainModel.cpp
  IntervalCustomTrainView.cpp
  IntervalCustomTrainViewModel.cpp
  TrainInterval.cpp
  TrainIntervalModel.cpp
  TrainIntervalView.cpp
  TrainIntervalViewModel.cpp
  TrainMenus.cpp
  TrainMenusModel.cpp
  TrainMenusView.cpp
  TrainMenusViewModel.cpp
  TrainFeel.cpp
  TrainFeelModel.cpp
  TrainFeelView.cpp
  TrainFeelViewModel.cpp
  TriathlonTypeSel.cpp
  TriathlonTypeSelModel.cpp
  TriathlonTypeSelView.cpp
  TriathlonTypeSelViewModel.cpp
  TriathlonTypeSetting.cpp
  TriathlonTypeSettingModel.cpp
  TriathlonTypeSettingView.cpp
  TriathlonTypeSettingViewModel.cpp
  VirtualRabbitSetting.cpp
  VirtualRabbitSettingModel.cpp
  VirtualRabbitSettingView.cpp
  VirtualRabbitSettingViewModel.cpp
  SummaryChartContainer.cpp
  SummaryDetailContainer.cpp
  DetailsMenuContainer.cpp
  ItervalTrainItem.cpp
  SummaryIntervalTrainCon.cpp
  SummaryLap.cpp
  SummaryLapContainer.cpp
  SummaryLapModel.cpp
  SummaryLapView.cpp
  SummaryLapViewModel.cpp
  SummarySportContainer.cpp
  RecoveryTimeCon.cpp
  SummaryVo2max.cpp
  SummaryWorkoutRet.cpp
  SummaryZoneContainer.cpp
  Summary.cpp
  SummaryModel.cpp
  SummaryView.cpp
  SummaryViewModel.cpp
  AccessibilityMenus.cpp
  AccessibilityMenusModel.cpp
  AccessibilityMenusView.cpp
  AccessibilityMenusViewModel.cpp
  AutoLockSetting.cpp
  AutoLockSettingModel.cpp
  AutoLockSettingView.cpp
  AutoLockSettingViewModel.cpp
  ShortKeyMenu.cpp
  ShortKeyMenuModel.cpp
  ShortKeyMenuView.cpp
  ShortKeyMenuViewModel.cpp
  ShortKeySetting.cpp
  ShortKeySettingModel.cpp
  ShortKeySettingView.cpp
  ShortKeySettingViewModel.cpp
  WearHandSetting.cpp
  WearHandSettingModel.cpp
  WearHandSettingView.cpp
  WearHandSettingViewModel.cpp
  BrightnessSettings.cpp
  BrightnessSettingsModel.cpp
  BrightnessSettingsView.cpp
  BrightnessSettingsViewModel.cpp
  DisplaySettingsMenu.cpp
  DisplaySettingsMenuModel.cpp
  DisplaySettingsMenuView.cpp
  DisplaySettingsMenuViewModel.cpp
  ManualBrightness.cpp
  ManualBrightnessModel.cpp
  ManualBrightnessView.cpp
  ManualBrightnessViewModel.cpp
  ScreenTimeSetting.cpp
  ScreenTimeSettingModel.cpp
  ScreenTimeSettingView.cpp
  ScreenTimeSettingViewModel.cpp
  DNDMode.cpp
  DNDModeModel.cpp
  DNDModeView.cpp
  DNDModeViewModel.cpp
  FocusOnModeMenus.cpp
  FocusOnModeMenusModel.cpp
  FocusOnModeMenusView.cpp
  FocusOnModeMenusViewModel.cpp
  SleepMode.cpp
  SleepModeModel.cpp
  SleepModeView.cpp
  SleepModeViewModel.cpp
  GPSSetting.cpp
  GPSSettingModel.cpp
  GPSSettingView.cpp
  GPSSettingViewModel.cpp
  HRReminder.cpp
  HRReminderModel.cpp
  HRReminderView.cpp
  HRReminderViewModel.cpp
  HRSetting.cpp
  HRSettingModel.cpp
  HRSettingView.cpp
  HRSettingViewModel.cpp
  HealthMonitoring.cpp
  HealthMonitoringModel.cpp
  HealthMonitoringView.cpp
  HealthMonitoringViewModel.cpp
  Spo2Measure.cpp
  Spo2MeasureModel.cpp
  Spo2MeasureView.cpp
  Spo2MeasureViewModel.cpp
  StressReminder.cpp
  StressReminderModel.cpp
  StressReminderView.cpp
  StressReminderViewModel.cpp
  StressSetting.cpp
  StressSettingModel.cpp
  StressSettingView.cpp
  StressSettingViewModel.cpp
  EditPersonalProfile.cpp
  EditPersonalProfileModel.cpp
  EditPersonalProfileView.cpp
  EditPersonalProfileViewModel.cpp
  PersonalProfile.cpp
  PersonalProfileModel.cpp
  PersonalProfileView.cpp
  PersonalProfileViewModel.cpp
  Personalization.cpp
  PersonalizationModel.cpp
  PersonalizationView.cpp
  PersonalizationViewModel.cpp
  ThemeColor.cpp
  ThemeColorModel.cpp
  ThemeColorView.cpp
  ThemeColorViewModel.cpp
  AddSensor.cpp
  AddSensorModel.cpp
  AddSensorView.cpp
  AddSensorViewModel.cpp
  Crank.cpp
  CrankModel.cpp
  CrankView.cpp
  CrankViewModel.cpp
  EditSensor.cpp
  EditSensorModel.cpp
  EditSensorView.cpp
  EditSensorViewModel.cpp
  SensorMenu.cpp
  SensorMenuModel.cpp
  SensorMenuView.cpp
  SensorMenuViewModel.cpp
  Sensors.cpp
  SensorsModel.cpp
  SensorsView.cpp
  SensorsViewModel.cpp
  PromptSoundSetting.cpp
  PromptSoundSettingModel.cpp
  PromptSoundSettingView.cpp
  PromptSoundSettingViewModel.cpp
  SoundSetting.cpp
  SoundSettingModel.cpp
  SoundSettingView.cpp
  SoundSettingViewModel.cpp
  SystemSettingsMenu.cpp
  SystemSettingsMenuModel.cpp
  SystemSettingsMenuView.cpp
  SystemSettingsMenuViewModel.cpp
  About.cpp
  AboutModel.cpp
  AboutView.cpp
  AboutViewModel.cpp
  DateStyle.cpp
  DateStyleModel.cpp
  DateStyleView.cpp
  DateStyleViewModel.cpp
  DateTime.cpp
  DateTimeModel.cpp
  DateTimeView.cpp
  DateTimeViewModel.cpp
  TimeStyle.cpp
  TimeStyleModel.cpp
  TimeStyleView.cpp
  TimeStyleViewModel.cpp
  LanguageSelectionMenu.cpp
  LanguageSelectionMenuModel.cpp
  LanguageSelectionMenuView.cpp
  LanguageSelectionMenuViewModel.cpp
  ReSet.cpp
  ReSetModel.cpp
  ReSetView.cpp
  ReSetViewModel.cpp
  Restart.cpp
  RestartModel.cpp
  RestartView.cpp
  RestartViewModel.cpp
  ShutdownAnimation.cpp
  ShutdownAnimationModel.cpp
  ShutdownAnimationView.cpp
  ShutdownAnimationViewModel.cpp
  Shutdown.cpp
  ShutdownModel.cpp
  ShutdownView.cpp
  ShutdownViewModel.cpp
  Pairing.cpp
  PairingModel.cpp
  PairingView.cpp
  PairingViewModel.cpp
  QRCodepairing.cpp
  QRCodepairingModel.cpp
  QRCodepairingView.cpp
  QRCodepairingViewModel.cpp
  RequestPairing.cpp
  RequestPairingModel.cpp
  RequestPairingView.cpp
  RequestPairingViewModel.cpp
  UniversalSettingsMenu.cpp
  UniversalSettingsMenuModel.cpp
  UniversalSettingsMenuView.cpp
  UniversalSettingsMenuViewModel.cpp
  PromptVibrationSetting.cpp
  PromptVibrationSettingModel.cpp
  PromptVibrationSettingView.cpp
  PromptVibrationSettingViewModel.cpp
  VibrationSetting.cpp
  VibrationSettingModel.cpp
  VibrationSettingView.cpp
  VibrationSettingViewModel.cpp
  AlarmMenu.cpp
  AlarmMenuModel.cpp
  AlarmMenuView.cpp
  AlarmMenuViewModel.cpp
  AlarmRepeat.cpp
  AlarmRepeatModel.cpp
  AlarmRepeatView.cpp
  AlarmRepeatViewModel.cpp
  AlarmSetting.cpp
  AlarmSettingModel.cpp
  AlarmSettingView.cpp
  AlarmSettingViewModel.cpp
  AlarmWeekly.cpp
  AlarmWeeklyModel.cpp
  AlarmWeeklyView.cpp
  AlarmWeeklyViewModel.cpp
  AlipayBindingCode.cpp
  AlipayBindingCodeModel.cpp
  AlipayBindingCodeView.cpp
  AlipayBindingCodeViewModel.cpp
  AlipayHelpCode.cpp
  AlipayHelpCodeModel.cpp
  AlipayHelpCodeView.cpp
  AlipayHelpCodeViewModel.cpp
  AlipayMenu.cpp
  AlipayMenuModel.cpp
  AlipayMenuView.cpp
  AlipayMenuViewModel.cpp
  AlipayPassword.cpp
  AlipayPasswordModel.cpp
  AlipayPasswordView.cpp
  AlipayPasswordViewModel.cpp
  AlipayBarCodePage.cpp
  AlipayPaymentCode.cpp
  AlipayPaymentCodeModel.cpp
  AlipayPaymentCodeView.cpp
  AlipayPaymentCodeViewModel.cpp
  AlipayQRCodePage.cpp
  AlipaySettings.cpp
  AlipaySettingsModel.cpp
  AlipaySettingsView.cpp
  AlipaySettingsViewModel.cpp
  AlipayTransitCode.cpp
  AlipayTransitCodeModel.cpp
  AlipayTransitCodeView.cpp
  AlipayTransitCodeViewModel.cpp
  AlipayTransitList.cpp
  AlipayTransitListModel.cpp
  AlipayTransitListView.cpp
  AlipayTransitListViewModel.cpp
  FindPhoneStart.cpp
  FindPhoneStartModel.cpp
  FindPhoneStartView.cpp
  FindPhoneStartViewModel.cpp
  FindingPhone.cpp
  FindingPhoneModel.cpp
  FindingPhoneView.cpp
  FindingPhoneViewModel.cpp
  FlashLight.cpp
  FlashLightModel.cpp
  FlashLightView.cpp
  FlashLightViewModel.cpp
  HeartRatePush.cpp
  HeartRatePushModel.cpp
  HeartRatePushView.cpp
  HeartRatePushViewModel.cpp
  MusicControlAPP.cpp
  MusicControlAPPModel.cpp
  MusicControlAPPView.cpp
  MusicControlAPPViewModel.cpp
  PowerSaveModeSetting.cpp
  PowerSaveModeSettingModel.cpp
  PowerSaveModeSettingView.cpp
  PowerSaveModeSettingViewModel.cpp
  StopWatchRecord.cpp
  StopWatchRecordModel.cpp
  StopWatchRecordView.cpp
  StopWatchRecordViewModel.cpp
  StopWatchShow.cpp
  StopWatchShowModel.cpp
  StopWatchShowView.cpp
  StopWatchShowViewModel.cpp
  TimerAppReady.cpp
  TimerAppReadyModel.cpp
  TimerAppReadyView.cpp
  TimerAppReadyViewModel.cpp
  TimerAppSetting.cpp
  TimerAppSettingModel.cpp
  TimerAppSettingView.cpp
  TimerAppSettingViewModel.cpp
  TimerAppShow.cpp
  TimerAppShowModel.cpp
  TimerAppShowView.cpp
  TimerAppShowViewModel.cpp
  ToolMetronomeRemindMode.cpp
  ToolMetronomeRemindModeModel.cpp
  ToolMetronomeRemindModeView.cpp
  ToolMetronomeRemindModeViewModel.cpp
  ToolMetronomeSetting.cpp
  ToolMetronomeSettingModel.cpp
  ToolMetronomeSettingView.cpp
  ToolMetronomeSettingViewModel.cpp
  ToolMetronomeStart.cpp
  ToolMetronomeStartModel.cpp
  ToolMetronomeStartView.cpp
  ToolMetronomeStartViewModel.cpp
  ToolMetronomeTipFreq.cpp
  ToolMetronomeTipFreqModel.cpp
  ToolMetronomeTipFreqView.cpp
  ToolMetronomeTipFreqViewModel.cpp
  BreathTrainEnd.cpp
  BreathTrainEndModel.cpp
  BreathTrainEndView.cpp
  BreathTrainEndViewModel.cpp
  BreathTrainSetting.cpp
  BreathTrainSettingModel.cpp
  BreathTrainSettingView.cpp
  BreathTrainSettingViewModel.cpp
  BreathTraining.cpp
  BreathTrainingModel.cpp
  BreathTrainingView.cpp
  BreathTrainingViewModel.cpp
  ToolsMenu.cpp
  ToolsMenuModel.cpp
  ToolsMenuView.cpp
  ToolsMenuViewModel.cpp
  DialLoading.cpp
  DialLoadingModel.cpp
  DialLoadingView.cpp
  DialLoadingViewModel.cpp
  DialDataSelect.cpp
  DialDataSelectModel.cpp
  DialDataSelectView.cpp
  DialDataSelectViewModel.cpp
  EditDialDataComponent.cpp
  EditDialDataComponentModel.cpp
  EditDialDataComponentView.cpp
  EditDialDataComponentViewModel.cpp
  EditDialTheme.cpp
  EditDialThemeModel.cpp
  EditDialThemeView.cpp
  EditDialThemeViewModel.cpp
  SelectDial.cpp
  SelectDialModel.cpp
  SelectDialView.cpp
  SelectDialViewModel.cpp
  WebGrid.cpp
  WebGridModel.cpp
  WebGridView.cpp
  WebGridViewModel.cpp
  global_button.cpp
  hr_push.cpp
  gui_event_service.cpp
common_config.obj : warning LNK4075: 忽略“/EDITANDCONTINUE”(由于“/SAFESEH”规范)
  project.vcxproj -> E:\Snowa\app\project\WR02_Win32\project\simulator\Debug\WR02_Win.exe
