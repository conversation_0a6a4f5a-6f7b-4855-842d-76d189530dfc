/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   backlight_module.cpp
@Time    :   2024/12/10 10:55:17
*
**************************************************************************/

#include "backlight_module.h"
#include "gui_event_service.h"
#include "gui_thread.h"
#include "qw_time_util.h"
#include "qw_timer.h"
#include "cfg_header_def.h"
#include "alg_auto_light.h"
#include "algo_service_sport_status.h"
#include "qw_log.h"
#include "focus_mode_srv/focus_mode_srv.h"
#include "service_wristup.h"
#include "qw_general.h"
#ifndef SIMULATOR
#include "subscribe_service.h"
#include "subscribe_data_protocol.h"
#include "lv_lcd.h"
#include "qw_system_params.h"
#else
#include "activity_record/activity_fit_app.h"
#endif

// log定义
#define BACK_LIGHT_LVL               LOG_LVL_DBG
#define BACK_LIGHT_TAG               "BACK_LIGHT"

#if (BACK_LIGHT_LVL >= LOG_LVL_DBG) && !defined(SIMULATOR)
    #define BACK_LIGHT_LOG_D(...)        QW_LOG_D(BACK_LIGHT_TAG, __VA_ARGS__)
#else
    #define BACK_LIGHT_LOG_D(...)
#endif

#if (BACK_LIGHT_LVL >= LOG_LVL_INFO)
    #define BACK_LIGHT_LOG_I(...)        QW_LOG_I(BACK_LIGHT_TAG, __VA_ARGS__)
#else
    #define BACK_LIGHT_LOG_I(...)
#endif

#if (BACK_LIGHT_LVL >= LOG_LVL_WARNING)
    #define BACK_LIGHT_LOG_W(...)        QW_LOG_W(BACK_LIGHT_TAG, __VA_ARGS__)
#else
    #define BACK_LIGHT_LOG_W(...)
#endif

#if (BACK_LIGHT_LVL >= LOG_LVL_ERROR)
    #define BACK_LIGHT_LOG_E(...)        QW_LOG_E(BACK_LIGHT_TAG, __VA_ARGS__)
#else
    #define BACK_LIGHT_LOG_E(...)
#endif

// 宏/枚举/结构体定义
#define MIN_BACKLIGHT_PCT     10     // 最小背光百分比
#define POWERSAVE_PCT         40     // 省电模式下亮度下降百分比
#define AOD_PCT               20     // AOD模式下的背光百分比
#define FORCE_EVT_PCT         50     // AOD模式下的背光百分比

/**
 * @brief 背光设置的运行状态
 */
typedef enum {
    BK_SETTING_INVALID,   // 处于正常模式
    BK_SETTING_NORMAL,    // 用户正在设置背光
    BK_SETTING_HBM,       // 硬测正在设置背光hbm
} BK_SETTING_MODE;

/**
 * @brief 背光模块运行时参数
 */
typedef struct _light_cgf_about_t
{
    BK_STATUS status;                   // 当前背光状态
    BK_SETTING_MODE setting_flag;       // 当前运行模式
    uint8_t setting_pct;                // 设置百分比(背光设置时生效)

    BK_FORCE_SENCE force;               // 强制亮屏
    bool msg_force;                     // 弹窗时亮屏
}light_cgf_about_t;

/**
 * @brief 背光操作参数
 */
typedef struct _light_check_ret_t
{
    uint8_t light_pct;              // 当前亮度等级
    uint8_t light_timer;            // 当前亮屏时间
    bool need_auto_light;
    uint8_t auto_light_hbm_value;
}light_check_ret_t;


// static 定义
static bool g_backlight_module_inited = false;      // 背光模块是否初始化
static qw_timer g_backlight_evt_timer;              // 背光逻辑处理定时器ID
static light_cgf_about_t g_bk_sys_param = {         // 运行时参数
    .status = BK_STATUS_ON,
    .setting_flag = BK_SETTING_INVALID,
    .setting_pct = 100,
    .force = BK_FORCE_DISABLE,
    .msg_force = false,
};
#ifndef SIMULATOR
static algo_bk_status_pub_t g_algo_bk_status_pub = {0};    // 算法背光状态回调
#endif
static light_check_ret_t g_last_bkinfo;
static time_t g_last_bkinfo_tick;

// static 函数

static void backlight_status_publish()
{
#ifndef SIMULATOR
    g_algo_bk_status_pub.status_on = (g_bk_sys_param.status == BK_STATUS_ON ? 1 : 0);
    if (qw_dataserver_publish_id(DATA_ID_EVENT_BACKLIGHT_STATUS,
            &g_algo_bk_status_pub, sizeof(algo_bk_status_pub_t)) != ERRO_CODE_OK)
    {
        BACK_LIGHT_LOG_E("%s erro", __FUNCTION__);
    }
#endif
}

/**
 * @brief 背光条件融合
 */
static void sync_backlight_ctrl_status(light_check_ret_t* out_info)
{
    if (out_info != NULL)
    {
        out_info->need_auto_light = false;
        uint8_t light_timer = get_light_time();         // 获取亮屏时间
        uint8_t light_pct = get_light_value();          // 获取亮度值

        light_pct = light_pct > 100 ? 100 : light_pct;
        if (light_timer < 5 || light_timer > 60)
        {
            light_timer = 5;
        }

        // 非强制亮屏状态 亮度逻辑优先级:
        // 1. 运动中 使用当前设置亮度 自动亮度生效
        // 2. 省电模式 当前设置40%亮度
        // 4. 睡眠中 当前设置40%亮度 自动亮度生效
        // 5. 正常模式 使用当前设置的亮度 自动亮度生效

        if (g_bk_sys_param.setting_flag != BK_SETTING_INVALID && g_bk_sys_param.setting_pct <= 100)
        {
            // 设置逻辑
            light_pct = g_bk_sys_param.setting_pct;
        }
        else if (g_bk_sys_param.force == BK_FORCE_EVT)
        {
#if IMG_TESTER_ENABLE == 1
            light_pct = 100;
#else
            light_pct = FORCE_EVT_PCT;
#endif
        }
#ifndef SIMULATOR
        else if (get_sport_status())
#else
        else if (get_simulator_sport_status())
#endif
        {
            if(get_light_type())
            {
                out_info->need_auto_light = true;
                out_info->auto_light_hbm_value = alg_auto_light_get();
                // 自动亮度
                // light_pct = alg_auto_light_get();
            }
            else
            {
                // 运动中 light_pct不变
            }
        }
        else if (get_power_save_setting() != POWER_SAVE_NO)
        {
            // 省电中
            if(get_light_type())
            {
                out_info->need_auto_light = true;
                out_info->auto_light_hbm_value = alg_auto_light_get();
            }
            else
            {
                light_pct = (uint8_t)((uint32_t)light_pct * POWERSAVE_PCT / 100);
            }
        }
        else if (get_sleep_state(true))
        {
            // 睡眠中
            if(get_light_type())
            {
                out_info->need_auto_light = true;
                out_info->auto_light_hbm_value = alg_auto_light_get();
                // 自动亮度
                // light_pct = alg_auto_light_get();
            }
            else
            {
                light_pct = (uint8_t)((uint32_t)light_pct * POWERSAVE_PCT / 100);
            }
        }
        else
        {
            if(get_light_type())
            {
                out_info->need_auto_light = true;
                out_info->auto_light_hbm_value = alg_auto_light_get();
                // 自动亮度
                // light_pct = alg_auto_light_get();
            }
            else
            {
                // 正常模式 light_pct不变
            }
        }

        // 非强制亮屏状态 亮屏时间逻辑优先级:
        // 1. 运动中 使用当前设置亮屏时间
        // 2. 省电模式 亮屏时间为5s
        // 3. 正常模式 使用当前设置亮屏时间
        if (g_bk_sys_param.force != BK_FORCE_DISABLE || g_bk_sys_param.msg_force)
        {
            // 强制亮屏状态 使用当前设置的亮度 关闭计时器
            light_timer = 0;
        }
#ifndef SIMULATOR
        else if (get_sport_status())
#else
        else if (get_simulator_sport_status())
#endif
        {
            // 运动中常量屏需要计时进入aod 所以light_timer不变
        }
        else if (get_power_save_setting() != POWER_SAVE_NO)
        {
            // 省电中
            light_timer = 5;
        }
        else
        {
            // 正常模式常亮屏需要计时进入aod 所以light_timer不变
        }

        out_info->light_pct = light_pct;
        out_info->light_timer = light_timer;
    }
}

/**
 * @brief GUI关闭屏幕事件
 */
static void gui_off_evt(void)
{
    BACK_LIGHT_LOG_I("backlight_screen_off");
    g_bk_sys_param.status = BK_STATUS_OFF;
    submit_gui_event(GUI_EVT_SERVICE_GUI_SLEEP, 0, NULL);   // 发送关闭屏幕事件

    backlight_status_publish();
}

/**
 * @brief 进入aod模式
 */
static void gui_aod_evt(void)
{
    BACK_LIGHT_LOG_I("backlight_screen_aod");
    g_bk_sys_param.status = BK_STATUS_AOD;
    submit_gui_event(GUI_EVT_SERVICE_LCD_SET, AOD_PCT, NULL);
    submit_gui_event(GUI_EVT_SERVICE_GUI_AOD, 0, NULL);   // 发送进入AOD

    backlight_status_publish();
}

/**
 * @brief 背光定时器处理函数
 */
static void backlight_timer_handler(void *p)
{
    BACK_LIGHT_LOG_I("backlight timer called");
    qw_timer_stop(&g_backlight_evt_timer); // 解除定时器

    if (get_sleep_state(true))
    {
        // 睡眠模式正常灭屏
        gui_off_evt();
    }
#ifndef SIMULATOR
    else if (get_sport_status() > enum_status_ready)
#else
    else if (get_simulator_sport_status() > ACTIVITY_FIT_READY)
#endif
    {
        if (get_always_sporting())
        {
            // 运动模式下常亮屏
            // 常亮屏且灭屏计时timeout 进入aod
            gui_aod_evt();
        }
        else
        {
            // 睡眠模式正常灭屏
            gui_off_evt();
        }
    }
    else
    {
        if (get_always_on())
        {
            // 常亮屏且灭屏计时timeout 进入aod
            gui_aod_evt();
        }
        else
        {
            // 睡眠模式正常灭屏
            gui_off_evt();
        }
    }
    unsubscribe_backlight_algo(BK_ALGO_AUTO_LIGHT); // 取消订阅背光算法
}

// 函数

/**
 * @brief lcd背光初始化
 */
void backlight_init_app(void)
{
    if (!g_backlight_module_inited)
    {
#ifndef SIMULATOR
        g_algo_bk_status_pub.status_on = false;
#endif

        qw_timer_init(&g_backlight_evt_timer, QW_TIMER_FLAG_ONE_SHOT | QW_TIMER_FLAG_SOFT_TIMER,
                      backlight_timer_handler);

        g_backlight_module_inited = true;

        // 初始化订阅算法输入
        // subscribe_backlight_algo(BK_ALGO_WRISTUP);
        subscribe_backlight_algo(BK_ALGO_AUTO_LIGHT);
        if(get_light_type())
        {
            alg_set_auto_light_stop_reason(ALG_AUTO_LIGHT_OFF_OTHER);
        }

        g_last_bkinfo_tick = get_boot_sec();
        memset(&g_last_bkinfo, 0, sizeof(g_last_bkinfo));
    }
}

/**
 * @brief 抬腕亮屏初始化
 */
void backlight_wrist_init_app(void)
{

        // 初始化抬腕订阅算法输入
        subscribe_backlight_algo(BK_ALGO_WRISTUP);

}

/**
 * @brief 触发背光,按照当前场景重置背光时间和亮度
 */
void backlight_open_app()
{
    if(!g_backlight_module_inited)
    {
        BACK_LIGHT_LOG_E("backlight_module is uninit error");
        return;
    }

    light_check_ret_t info;

    sync_backlight_ctrl_status(&info); // 计算背光状态 拿背光时间和状态

    if(memcmp(&g_last_bkinfo, &info, sizeof(g_last_bkinfo)) == 0)
    {
        time_t now = get_boot_sec();
        if (g_last_bkinfo_tick == now)
        {
            return; // 相同时间点，不重复触发背光
        }
        else
        {
            g_last_bkinfo_tick = now;
        }
    }

    memcpy(&g_last_bkinfo, &info, sizeof(g_last_bkinfo));

    if (info.light_timer > 0)
    {
        qw_tick_t bk_time_ms = info.light_timer * 1000;   ///< 背光时间(ms)
        qw_timer_start(&g_backlight_evt_timer, bk_time_ms, NULL, "bklight");   ///< 启动定时器
        // BACK_LIGHT_LOG_D("start a backlight timer, in after %d s", info.light_timer);
    }
    else
    {
        qw_timer_stop(&g_backlight_evt_timer);   ///< 停止定时器 保持当前亮度到再次触发调整
        // BACK_LIGHT_LOG_D("stop backlight timer");
    }

    if (g_bk_sys_param.status != BK_STATUS_ON)
    {
        // 唤醒屏幕
        g_bk_sys_param.status = BK_STATUS_ON;
        submit_gui_event(GUI_EVT_SERVICE_GUI_WEAKUP, 0, NULL);
        backlight_status_publish();
        subscribe_backlight_algo(BK_ALGO_AUTO_LIGHT); // 订阅背光算法
    }

    sync_backlight_ctrl_status(&info); // 计算背光状态 拿亮度值
    if(!info.need_auto_light)
    {
        submit_gui_event(GUI_EVT_SERVICE_LCD_SET, info.light_pct, NULL);
    }
    else
    {
        submit_gui_event(GUI_EVT_SERVICE_LCD_SET_HBM, info.auto_light_hbm_value, NULL);
    }
}

/**
 * @brief 自动背光回调
 */
void backlight_auto_check_app()
{
    if(!g_backlight_module_inited)
    {
        BACK_LIGHT_LOG_E("backlight_module is uninit error");
        return;
    }

    if (g_bk_sys_param.status == BK_STATUS_ON)
    {
        light_check_ret_t info;

        sync_backlight_ctrl_status(&info); // 计算背光状态
        if(!info.need_auto_light)
        {
            submit_gui_event(GUI_EVT_SERVICE_LCD_SET, info.light_pct, NULL);
        }
        else
        {
            submit_gui_event(GUI_EVT_SERVICE_LCD_SET_HBM, info.auto_light_hbm_value, NULL);
        }
    }
}

/**
 * @brief 关闭背光 中断计时立刻关闭背光 由按键和落腕动作触发
 */
void backlight_close_app()
{
    if(!g_backlight_module_inited)
    {
        BACK_LIGHT_LOG_E("backlight_module is uninit error");
        return;
    }

    if (g_bk_sys_param.force == BK_FORCE_DISABLE && !g_bk_sys_param.msg_force)
    {
        backlight_timer_handler(NULL);
        if(get_light_type())
        {
            alg_set_auto_light_stop_reason(ALG_AUTO_LIGHT_OFF_SCREEN);
        }
    }
    else
    {
        BACK_LIGHT_LOG_I("close failed force:%d msg_force:%d",g_bk_sys_param.force , g_bk_sys_param.msg_force);
    }
}

//倒计时结束后息屏，不关注是否睡眠或AOD
void backlight_close_app_by_power_off(void)
{
    if(!g_backlight_module_inited)
    {
        BACK_LIGHT_LOG_E("backlight_module is uninit error");
        return;
    }

    if (g_bk_sys_param.force == BK_FORCE_DISABLE && !g_bk_sys_param.msg_force)
    {
        qw_timer_stop(&g_backlight_evt_timer); // 解除定时器
        gui_off_evt();
    }
}




// 定义表结构，b值已根据pct(v) = v * 100 / 800计算
typedef struct {
    uint8_t a;
    uint8_t b;
} LookupTableEntry;

// 查表函数
uint8_t lookup_y(uint8_t x) {
    // 定义查表数组
    static const LookupTableEntry table[] = {
        {0, 10 * 100 / 800},
        {5, 20 * 100 / 800},
        {10, 35 * 100 / 800},
        {15, 50 * 100 / 800},
        {20, 70 * 100 / 800},
        {25, 90 * 100 / 800},
        {30, 110 * 100 / 800},
        {35, 130 * 100 / 800},
        {40, 155 * 100 / 800},
        {45, 180 * 100 / 800},
        {50, 205 * 100 / 800},
        {55, 235 * 100 / 800},
        {60, 270 * 100 / 800},
        {65, 310 * 100 / 800},
        {70, 355 * 100 / 800},
        {75, 405 * 100 / 800},
        {80, 460 * 100 / 800},
        {85, 520 * 100 / 800},
        {90, 585 * 100 / 800},
        {95, 685 * 100 / 800},
        {100, 800 * 100 / 800}
    };
    const size_t table_size = sizeof(table) / sizeof(table[0]);

    // 遍历表，找到第一个a > x的项
    for (size_t i = 0; i < table_size; i++) {
        if (x <= table[i].a) {
            // rt_kprintf("lookup_y x:%d a:%d b:%d\n",x,table[i].a,table[i].b);
            return table[i].b;
        }
    }

    // 如果x大于所有a值，返回最后一个b值（或根据需求调整）
    return table[table_size - 1].b;
}







/**
 * @brief 应用调用 设置背光亮度
 * @param pct 背光百分比
 * @param hbm 模式
 */
void backlight_set_lcd_pct(uint8_t pct, bool hbm)
{
    // 记录上一次设置值
    static BK_SETTING_MODE s_last_setting_flag = BK_SETTING_INVALID;
    static uint8_t s_last_light_pct = 0;

    if (pct <= 100)
    {
        // 设置背光亮度
        if (hbm)
        {
            if (s_last_setting_flag != BK_SETTING_HBM || s_last_light_pct != pct) // 判断是否需要设置
            {
#ifndef SIMULATOR
                submit_lcd_event(LCD_EVT_SERVICE_SET_HBM_BRIGHTNESS, pct>100?100:pct, NULL);
                // lv_lcd_set_hbm_brightness(pct);
#endif
                s_last_setting_flag = BK_SETTING_HBM;
                s_last_light_pct = pct;
            }
        }
        else
        {
            if (s_last_setting_flag == BK_SETTING_HBM || s_last_light_pct != pct) // 判断是否需要设置
            {
#ifndef SIMULATOR

                uint8_t drv_pct = (uint8_t)(MIN_BACKLIGHT_PCT +
                    (uint16_t) pct * (100 - MIN_BACKLIGHT_PCT) / 100);   //自动转换百分比到 MIN_BACKLIGHT_PCT% ~ 100%
                submit_lcd_event(LCD_EVT_SERVICE_SET_BRIGHTNESS, lookup_y(drv_pct), NULL);
                // lv_lcd_set_brightness(drv_pct);
#endif
                s_last_setting_flag = BK_SETTING_INVALID;
                s_last_light_pct = pct;
            }
        }
    }
}

/**
 * @brief 强制背光开启
 * @param enable 开启/关闭
 */
void set_backlight_force_on(BK_FORCE_SENCE status)
{
    if (g_bk_sys_param.force != status)
    {
        g_bk_sys_param.force = status;
        BACK_LIGHT_LOG_I("BACK_LIGHT FORCE:%d ",status);
        backlight_open_app();
    }
}

/**
 * @brief 弹窗背光开启
 * @param status 状态
 */
void set_backlight_msg_on(bool status)
{
    if (g_bk_sys_param.msg_force != status)
    {
        g_bk_sys_param.msg_force = status;
        backlight_open_app();
    }
}

/**
 * @brief debug 设置背光亮度,
 * 同时打开背光设置状态, 忽略任何背光指令, 传入0xff关闭设置状态, 恢复原有背光亮度
 * @param percent 0 - 100 / 0xff关闭设置状态
 */
void backlight_percent_set_app(uint8_t percent)
{
    if(!g_backlight_module_inited)
    {
        BACK_LIGHT_LOG_E("backlight_module is uninit error");
        return;
    }

    if (0xff == percent)
    {
        g_bk_sys_param.setting_flag = BK_SETTING_INVALID;   // 关闭背光设置标志
        g_bk_sys_param.setting_pct = percent;       // 设置背光百分比
        backlight_open_app();       // 激活背光
    }
    else if (percent <= 100)
    {
        g_bk_sys_param.setting_flag = BK_SETTING_NORMAL;    // 开启背光设置标志
        g_bk_sys_param.setting_pct = percent;       // 设置背光百分比
        backlight_set_lcd_pct(percent, false);
    }
}

/**
 * @brief debug 设置HBM背光亮度,
 * 同时打开背光设置状态, 忽略任何背光指令, 传入0xff关闭设置状态, 恢复原有背光亮度
 * @param percent 0 - 100 / 0xff关闭设置状态
 */
void backlight_hbm_percent_set_app(uint8_t percent)
{
    if(!g_backlight_module_inited)
    {
        BACK_LIGHT_LOG_E("backlight_module is uninit error");
        return;
    }

    if (0xff == percent)
    {
        g_bk_sys_param.setting_flag = BK_SETTING_INVALID;   // 关闭背光设置标志
        g_bk_sys_param.setting_pct = percent;       // 设置背光百分比
        backlight_open_app();       // 激活背光
    }
    else if (percent <= 100)
    {
        g_bk_sys_param.setting_flag = BK_SETTING_HBM;    // 开启背光设置标志
        g_bk_sys_param.setting_pct = percent;       // 设置背光百分比
        backlight_set_lcd_pct(percent, true);
    }
}

/**
 * @brief 获取背光模块初始化状态
 * @return 初始化状态
 */
bool get_backlight_is_inited()
{
    return g_backlight_module_inited;
}

/**
 * @brief 获取背光状态
 * @return 状态
 */
BK_STATUS get_backlight_status()
{
    return g_bk_sys_param.status;
}

/**
 * @brief 订阅背光相关的算法
 * @param type 算法类型
 */
void subscribe_backlight_algo(BK_ALGO_TYPE type)
{
    if(!g_backlight_module_inited)
    {
        BACK_LIGHT_LOG_E("backlight_module is uninit error");
        return;
    }

    switch (type)
    {
    case BK_ALGO_WRISTUP:
    {
        if (get_lift_wrist() && !get_sleep_state(true))
        {
#ifndef SIMULATOR
            service_wristup_subscribe();
#endif
        }
        break;
    }
    case BK_ALGO_AUTO_LIGHT:
    {
        if(get_light_type())
        {
            alg_auto_light_start();
        }
        break;
    }

    default:
        break;
    }
}

/**
 * @brief 取消订阅背光相关的算法
 * @param type 算法类型
 */
void unsubscribe_backlight_algo(BK_ALGO_TYPE type)
{
    if(!g_backlight_module_inited)
    {
        BACK_LIGHT_LOG_E("backlight_module is uninit error");
        return;
    }

    switch (type)
    {
    case BK_ALGO_WRISTUP:
    {
#ifndef SIMULATOR
        service_wristup_unsubscribe();
#endif
        break;
    }
    case BK_ALGO_AUTO_LIGHT:
    {
        alg_auto_light_stop();
        break;
    }

    default:
        break;
    }
}

#if TEMP_SHOW_GPS_SNR

static void bk_gps_data_cb(const void *in, uint32_t len);   ///< GPS数据回调函数
static gps_pub_t gps_data_;                                 ///< GPS数据结构体

/// @brief GPS数据回调函数
/// @param in 输入数据
/// @param len 数据长度
static void bk_gps_data_cb(const void *in, uint32_t len)
{
    if (in == NULL)
    {
        return;                        ///< 如果输入数据为空，返回
    }
    gps_data_ = *((gps_pub_t *) in);   ///< 解析GPS数据
    for (int i = 0; i < 10; i++)
    {
        gfx_printf("[GPS_SNR] gps_snr[%d]: l1id:%d,l1snr%d,l5id%d,l5snr:%d\n", i,
                   gps_data_.snr.gps_snr.nr_l1[i], gps_data_.snr.gps_snr.snr_l1[i],
                   gps_data_.snr.gps_snr.nr_l5[i], gps_data_.snr.gps_snr.snr_l5[i]);
        gfx_printf("[GPS_SNR] bd_snr[%d]: l1id:%d,l1snr%d,l5id%d,l5snr:%d\n", i,
                   gps_data_.snr.bd_snr.nr_l1[i], gps_data_.snr.bd_snr.snr_l1[i],
                   gps_data_.snr.bd_snr.nr_l5[i], gps_data_.snr.bd_snr.snr_l5[i]);
        swprintf("[GPS_SNR] gps_snr[%d]: l1id:%d,l1snr%d,l5id%d,l5snr:%d\n", i,
                 gps_data_.snr.gps_snr.nr_l1[i], gps_data_.snr.gps_snr.snr_l1[i],
                 gps_data_.snr.gps_snr.nr_l5[i], gps_data_.snr.gps_snr.snr_l5[i]);
        swprintf("[GPS_SNR] bd_snr[%d]: l1id:%d,l1snr%d,l5id%d,l5snr:%d\n", i,
                 gps_data_.snr.bd_snr.nr_l1[i], gps_data_.snr.bd_snr.snr_l1[i],
                 gps_data_.snr.bd_snr.nr_l5[i], gps_data_.snr.bd_snr.snr_l5[i]);
    }
}

/// @brief 启动显示GPS信噪比
static void start_show_snr_gps(void)
{
    optional_config_t config = {.sampling_rate = 0};   ///< 配置结构体
    qw_dataserver_subscribe_id(DATA_ID_ALGO_GPS_DATA, bk_gps_data_cb, &config);   ///< 订阅GPS数据
    if (1)
    {
    }
    else
    {
        qw_dataserver_unsubscribe_id(DATA_ID_ALGO_GPS_DATA, bk_gps_data_cb);   ///< 取消订阅GPS数据
    }
}

static bool ldc_off_gui_skip = false;   ///< GUI跳过标志

/// @brief 关闭屏幕并进入省电模式
void bk_close_screen_poweroff(void)
{
    //阻塞GUI线程/POP
    cancel_task(&lp_info);   ///< 取消任务
    //show snr
    start_show_snr_gps();   ///< 启动显示GPS信噪比
    swprintf("backlight module: show gps snr on\n");
    gfx_printf("backlight module: show gps snr on\n");

    //ltr
    BSP_PMIC_Init();                                  ///< 初始化PMIC
    rt_thread_mdelay(100);                            ///< 延迟100毫秒
    BSP_PMIC_Control(PMIC_OUT_1V8_LVSW100_1, 0, 1);   ///< 关闭1.8V输出
    swprintf("backlight module: LTR 1.8 OFF\n");
    gfx_printf("backlight module: LTR 1.8 OFF\n");
    rt_thread_mdelay(1000);   ///< 延迟1000毫秒
    //tp  2
    //BSP_PMIC_Control(PMIC_OUT_1V8_LVSW100_3, 0, 1);
    rt_thread_mdelay(1000);   ///< 延迟1000毫秒
    gfx_printf("backlight module: TP 1.8 OFF\n");
    swprintf("backlight module: TP 1.8 OFF\n");
    pmic_twi_ldo_set(PMIC_LDO_3, 0, 13);   ///< 设置LDO输出
    rt_thread_mdelay(1000);                ///< 延迟1000毫秒
    gfx_printf("backlight module: TP 3.3 OFF\n");
    swprintf("backlight module: TP 3.3 OFF\n");
    //lcd
    uint8_t val = 1;                                                  ///< LCD控制值
    rt_device_control(g_lcd_device, RTGRAPHIC_CTRL_POWEROFF, &val);   ///< 关闭LCD
    gfx_printf("backlight module: LCD 1.8 OFF\n");
    swprintf("backlight module: LCD 1.8 OFF\n");
}

/// @brief 设置GUI跳过标志
/// @param skip 跳过标志
void set_ldc_off_gui_skip(bool skip)
{
    ldc_off_gui_skip = skip;   ///< 更新跳过标志
}

/// @brief 获取GUI跳过标志
/// @return true表示需要关闭，false表示不关闭
bool get_ldc_off_gui_skip(void)
{
    return ldc_off_gui_skip;   ///< 返回跳过标志
}

#endif

static void back_light(int argc, char **argv)
{
    if( argc < 2)
    {
        BACK_LIGHT_LOG_E("argc error");
    }

    if( strcmp(argv[1], "bk_status") == 0)
    {
        BACK_LIGHT_LOG_I("bk_status:%d", g_bk_sys_param.status);
    }
    else if( strcmp(argv[1], "bk_settingflag") == 0)
    {
        BACK_LIGHT_LOG_I("bk_settingflag:%d", g_bk_sys_param.setting_flag);
    }
    else if( strcmp(argv[1], "bk_settingpct") == 0)
    {
        BACK_LIGHT_LOG_I("bk_settingpct:%d", g_bk_sys_param.setting_pct);
    }
    else if( strcmp(argv[1], "bk_force") == 0)
    {
        BACK_LIGHT_LOG_I("bk_force:%d msg_force:%d", g_bk_sys_param.force, g_bk_sys_param.msg_force);
    }
    else if( strcmp(argv[1], "always_on") == 0)
    {
        BACK_LIGHT_LOG_I("always_on:%d sport_always_on", get_always_on(), get_always_sporting());
    }
    else if( strcmp(argv[1], "is_sport") == 0)
    {
#ifndef SIMULATOR
        BACK_LIGHT_LOG_I("is_sport:%d", get_sport_status());
#else
        BACK_LIGHT_LOG_I("is_sport:%d", get_simulator_sport_status());
#endif
    }
    else if( strcmp(argv[1], "light_type") == 0)
    {
        BACK_LIGHT_LOG_I("light_type:%d", get_light_type());
    }
    else if( strcmp(argv[1], "light_value") == 0)
    {
        BACK_LIGHT_LOG_I("light_value:%d", get_light_value());
    }
    else if( strcmp(argv[1], "light_time") == 0)
    {
        BACK_LIGHT_LOG_I("light_time:%d", get_light_time());
    }
    else if( strcmp(argv[1], "open") == 0)
    {
        backlight_open_app();
        BACK_LIGHT_LOG_I("tshell backlight_open_app");
    }
    else
    {
        BACK_LIGHT_LOG_I("back light no argv");
    }
}

MSH_CMD_EXPORT(back_light, back_light);
