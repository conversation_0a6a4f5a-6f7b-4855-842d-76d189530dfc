#ifndef NAVI_TSGN_H
#define NAVI_TSGN_H

#ifdef __cplusplus
extern "C" {
#endif

#include "navi_common.h"
#include "qw_fs.h"

typedef struct _TsgnReader
{
    QW_FIL *fp;
} TsgnReader;

typedef struct _TsgnRWriter
{
    QW_FIL *fp;
} TsgnRWriter;

typedef struct _NaviSignSegment
{
    Dseg dseg;
    Range range;
} NaviSignSegment;

typedef struct _NaviSignSegmentArray
{
    NaviSignSegment *segments;
    uint32_t len;
} NaviSignSegmentArray;

typedef enum _NaviSignType
{
    enumNAVI_SIGN_TYPE_STAR,                //途径点
    enumNAVI_SIGN_TYPE_SPRINT,              //冲刺点
    enumNAVI_SIGN_TYPE_CLIMB_HC,            //HC级爬坡点
    enumNAVI_SIGN_TYPE_CLIMB_L1,            //1级爬坡点
    enumNAVI_SIGN_TYPE_CLIMB_L2,            //2级爬坡点
    enumNAVI_SIGN_TYPE_CLIMB_L3,            //3级爬坡点
    enumNAVI_SIGN_TYPE_CLIMB_L4,            //4级爬坡点
    enumNAVI_SIGN_TYPE_SUPPLY,              //补给点
    enumNAVI_SIGN_TYPE_RECYCLE,             //垃圾回收区
    enumNAVI_SIGN_TYPE_TOILET,              //厕所
    enumNAVI_SIGN_TYPE_SERVICE,             //服务点
    enumNAVI_SIGN_TYPE_MEDICAL,             //医疗救护站
    enumNAVI_SIGN_TYPE_GEAR,                //装备区
    enumNAVI_SIGN_TYPE_SHOP,                //商店
    enumNAVI_SIGN_TYPE_GATHER,              //集合点
    enumNAVI_SIGN_TYPE_VIEW,                //观景台
    enumNAVI_SIGN_TYPE_HOTSPOT,             //网红打卡地
    enumNAVI_SIGN_TYPE_TUNNEL,              //隧道
    enumNAVI_SIGN_TYPE_VALLEY,              //山谷
    enumNAVI_SIGN_TYPE_DANGER,              //危险路段
    enumNAVI_SIGN_TYPE_TURN,                //急转弯
    enumNAVI_SIGN_TYPE_STEEP,               //陡坡
    enumNAVI_SIGN_TYPE_CROSS,               //交叉口
    enumNAVI_SIGN_TYPE_OTHERS,              //其他
} NaviSignType;

typedef struct _NaviSign
{
    double lng;
    double lat;
    NaviSignType type;
    float dist;
    char name[NAVI_SIGN_NAME_LEN];
} NaviSign;

typedef struct _NaviSignBuf
{
    NaviSign *buf;
    Range range;
    uint32_t capacity;
} NaviSignBuf;

//指定标记点前后的标记点
typedef struct _NaviSignNearby
{
    NaviSign *buf;
    uint32_t len;
} NaviSignNearby;

typedef struct _NaviSignCache
{
    NaviSignBuf *sign_buf;
    TsgnReader *tsgn_reader;
    uint32_t capacity;
    uint32_t len;
    uint32_t next;
} NaviSignCache;

typedef struct _NaviSignList
{
    NaviSignCache cache;
    uint32_t len;
} NaviSignList;

int tsgn_header_read(TsgnReader *self, uint8_t *header);

int tsgn_sign_segment_array_read(TsgnReader *self, NaviSignSegmentArray *seg_array);

int tsgn_sign_num_read(TsgnReader *self, uint32_t *sign_num);

int tsgn_sign_data_read(TsgnReader *self, uint32_t start, uint32_t end, uint32_t sign_num, NaviSign *sign_buf);

int tsgn_header_placeholder_write(TsgnRWriter *self);

int tsgn_header_write(TsgnRWriter *self);

int tsgn_sign_segment_array_write(TsgnRWriter *self, const NaviSignSegmentArray *seg_array);

int tsgn_sign_num_write(TsgnRWriter *self, uint32_t num);

int tsgn_sign_data_write(TsgnRWriter *self, const NaviSign *sign);

int tsgn_sign_data_read2(TsgnRWriter *self, uint32_t idx, uint32_t sign_num, NaviSign *sign);

int tsgn_sign_data_write2(TsgnRWriter *self, uint32_t idx, uint32_t sign_num, const NaviSign *sign);

void navi_sign_copy(NaviSign *self, NaviSign *sign);

int navi_sign_list_get(NaviSignList *self, uint32_t idx, NaviSign *output);

void navi_sign_buf_reset(NaviSignBuf *self);

void navi_sign_cache_reset(NaviSignCache *self);

void navi_sign_list_reset(NaviSignList *self);

#ifdef __cplusplus
}
#endif

#endif
