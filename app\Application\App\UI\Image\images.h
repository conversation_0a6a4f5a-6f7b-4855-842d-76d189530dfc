/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   images.h
@Time    :   2024/12/11 20:59:36
*
**************************************************************************/

#ifndef __UI_IMAGES_H
#define __UI_IMAGES_H

#include "../lv_engine/lvgl.h"
#include "igs_dev_config.h"

#ifdef __cplusplus
extern "C" {
#endif


#ifdef IGS_DEV
#define USE_IMG(n) ("0:/iGPSPORT/Resource/Img/"#n".bin")//模拟器上使用.c，真机上使用.bin
#else
#define USE_IMG(n) (&(n))//模拟器上使用.c，真机上使用.bin
#endif

extern char *app_select_item_img_bg_dir;
extern char *grid_map_dir_img_dir;
extern char *grid_map_dir_guide_img;
extern char *grid_map_user_img_dir;

extern const lv_img_dsc_t list_focus_menus;
extern const lv_img_dsc_t list_focus_bg;
extern const lv_img_dsc_t select_item_img_bg;
extern const lv_img_dsc_t app_select_item_img_bg;

extern const lv_img_dsc_t tool_heart_rate_push;
extern const lv_img_dsc_t tools_heart_rate_status;
extern const lv_img_dsc_t tool_select_bg;
extern const lv_img_dsc_t status_battery;
extern const lv_img_dsc_t status_ble_phone;
extern const lv_img_dsc_t status_ble_phone_off;
extern const lv_img_dsc_t tools_heart_rate_push_off;
extern const lv_img_dsc_t tools_heart_rate_push_on;

extern const lv_img_dsc_t tool_setting;                  //设置
extern const lv_img_dsc_t tool_sleep_on;                 //睡眠模式开
extern const lv_img_dsc_t tool_sleep_off;                //睡眠模式关
extern const lv_img_dsc_t tool_dnd_on;                   //勿扰模式开
extern const lv_img_dsc_t tool_dnd_off;                  //勿扰模式关
extern const lv_img_dsc_t tool_alarmclock;               //闹钟
extern const lv_img_dsc_t tool_brightness;               //亮度
extern const lv_img_dsc_t tool_camera;                   //相机
extern const lv_img_dsc_t tool_changliang_off;           //常亮关
extern const lv_img_dsc_t tool_changliang_on;            //常亮开
extern const lv_img_dsc_t tool_findphone;                //查找手机
extern const lv_img_dsc_t tool_flashlight;               //手电筒
extern const lv_img_dsc_t tool_metronome;                //节拍器
extern const lv_img_dsc_t tool_music;                    //音乐
extern const lv_img_dsc_t tool_savepower_off;            //省电模式关
extern const lv_img_dsc_t tool_savepower_on;             //省电模式开
extern const lv_img_dsc_t tool_sensor;                   //传感器
extern const lv_img_dsc_t tool_stopwatch;                //秒表
extern const lv_img_dsc_t tool_taiwan_off;               //抬腕亮屏关
extern const lv_img_dsc_t tool_taiwan_on;                //抬腕亮屏开
extern const lv_img_dsc_t tool_timer;                    //计时器
extern const lv_img_dsc_t tool_touch_lock;               //触摸锁屏-开
extern const lv_img_dsc_t tool_touch_unlock;             //触摸锁屏-关
extern const lv_img_dsc_t tool_watch_lock;               //手表锁屏-关
extern const lv_img_dsc_t tool_watch_unlock;             //手表锁屏-开
extern const lv_img_dsc_t tool_alipay;                   //支付宝
extern const lv_img_dsc_t tool_breath_training;          //呼吸训练
extern const lv_img_dsc_t tool_heartrate;                //心率推送

extern const lv_img_dsc_t sys_set_auxiliary_functions;   //辅助功能
extern const lv_img_dsc_t sys_set_personalization;       //个性化设置
extern const lv_img_dsc_t sys_set_currency;              //通用设置
extern const lv_img_dsc_t sys_set_display;               //显示设置
extern const lv_img_dsc_t sys_set_focus_mode;            //专注模式
extern const lv_img_dsc_t sys_set_gps;                   //GPS
extern const lv_img_dsc_t sys_set_health_check_set;      //健康监测
extern const lv_img_dsc_t sys_set_peripheral_sensors;    //外设传感器
extern const lv_img_dsc_t sys_set_vibration;             //振动
extern const lv_img_dsc_t sys_set_voice;                 //铃声
extern const lv_img_dsc_t sys_set_personal_data;         //个人资料

extern const lv_img_dsc_t label_img_enter;

extern const lv_img_dsc_t msg_alarm_clock;
extern const lv_img_dsc_t msg_timer_clock;
extern const lv_img_dsc_t msg_health_goal_all;
extern const lv_img_dsc_t msg_health_goal_intensity;
extern const lv_img_dsc_t msg_health_stand_reminder;
extern const lv_img_dsc_t msg_health_goal_steps;
extern const lv_img_dsc_t msg_health_goal_calories;
extern const lv_img_dsc_t health_grid_mark;
extern const lv_img_dsc_t msg_training_ends_img;
extern const lv_img_dsc_t input_fog;

extern const lv_img_dsc_t stop_watch_icon;
extern const lv_img_dsc_t normalPage;
extern const lv_img_dsc_t highlightedPage;
extern const lv_img_dsc_t blackPage;
extern const lv_img_dsc_t blacknormalPage;
extern const lv_img_dsc_t paring;

extern const lv_img_dsc_t app_calorie;
extern const lv_img_dsc_t app_compass;
extern const lv_img_dsc_t app_heart_rate;
extern const lv_img_dsc_t app_hrv;
extern const lv_img_dsc_t app_hrv_card_range;
extern const lv_img_dsc_t hrv_range;
extern const lv_img_dsc_t hrv_range_example;
extern const lv_img_dsc_t hrv_base_example;
extern const lv_img_dsc_t hrv_base_range;
extern const lv_img_dsc_t app_intelligent_notification;
extern const lv_img_dsc_t app_training_plan;
extern const lv_img_dsc_t app_navigation;
extern const lv_img_dsc_t app_sleep;
extern const lv_img_dsc_t app_step_count;
extern const lv_img_dsc_t app_today_activity;
extern const lv_img_dsc_t app_weather;
extern const lv_img_dsc_t app_blood_oxygen_saturation;
extern const lv_img_dsc_t app_historical_record;
extern const lv_img_dsc_t app_achievements;
extern const lv_img_dsc_t app_riding_ability;
extern const lv_img_dsc_t app_stress_free;
extern const lv_img_dsc_t app_stress_mild;
extern const lv_img_dsc_t app_stress_moderate;
extern const lv_img_dsc_t app_stress_weight;
extern const lv_img_dsc_t app_system_setting;
extern const lv_img_dsc_t app_training_course;
extern const lv_img_dsc_t app_training_default;
extern const lv_img_dsc_t app_training_efficient;
extern const lv_img_dsc_t app_training_fast_recovery;
extern const lv_img_dsc_t app_training_interrupt;
extern const lv_img_dsc_t app_training_low_effciency;
extern const lv_img_dsc_t app_training_maintain;
extern const lv_img_dsc_t app_training_overshoot;
extern const lv_img_dsc_t app_training_recovery;
extern const lv_img_dsc_t app_training_stateless;
extern const lv_img_dsc_t app_running_ablility;
extern const lv_img_dsc_t app_altimeter;
extern const lv_img_dsc_t app_activity_hours;
extern const lv_img_dsc_t app_barometer;
extern const lv_img_dsc_t app_intensity_activity_duration;

extern const lv_img_dsc_t app_history_icon_run;

extern const lv_img_dsc_t alarm_clock_icon;
extern const lv_img_dsc_t step_count_tips;
extern const lv_img_dsc_t current_step_bg;
extern const lv_img_dsc_t current_sleep_bg;
extern const lv_img_dsc_t current_calories_bg;
extern const lv_img_dsc_t calories_tips;
extern const lv_img_dsc_t activity_steps;
extern const lv_img_dsc_t activity_cals;
extern const lv_img_dsc_t activity_strengths;
extern const lv_img_dsc_t activity_hours_people;
extern const lv_img_dsc_t strength_duration;
extern const lv_img_dsc_t compass_hand_img;
extern const lv_img_dsc_t compass_degree;

extern const lv_img_dsc_t angle_0;
extern const lv_img_dsc_t angle_30;
extern const lv_img_dsc_t angle_60;
extern const lv_img_dsc_t angle_90;
extern const lv_img_dsc_t angle_120;
extern const lv_img_dsc_t angle_150;
extern const lv_img_dsc_t angle_180;
extern const lv_img_dsc_t angle_210;
extern const lv_img_dsc_t angle_240;
extern const lv_img_dsc_t angle_270;
extern const lv_img_dsc_t angle_300;
extern const lv_img_dsc_t angle_330;

extern const lv_img_dsc_t weather_40_00;
extern const lv_img_dsc_t weather_40_01;
extern const lv_img_dsc_t weather_40_02;
extern const lv_img_dsc_t weather_40_03;
extern const lv_img_dsc_t weather_40_04;
extern const lv_img_dsc_t weather_40_05;
extern const lv_img_dsc_t weather_40_06;
extern const lv_img_dsc_t weather_40_07;
extern const lv_img_dsc_t weather_40_08;
extern const lv_img_dsc_t weather_40_09;
extern const lv_img_dsc_t weather_40_10;
extern const lv_img_dsc_t weather_40_11;
extern const lv_img_dsc_t weather_40_12;
extern const lv_img_dsc_t weather_40_13;
extern const lv_img_dsc_t weather_40_14;
extern const lv_img_dsc_t weather_40_15;
extern const lv_img_dsc_t weather_40_16;
extern const lv_img_dsc_t weather_40_17;
extern const lv_img_dsc_t weather_40_18;
extern const lv_img_dsc_t weather_40_19;
extern const lv_img_dsc_t weather_GPS;
extern const lv_img_dsc_t weather_nodata;
extern const lv_img_dsc_t weather_rainfall;
extern const lv_img_dsc_t weather_sun_drop;
extern const lv_img_dsc_t weather_sun_up;
extern const lv_img_dsc_t weather_wind;
extern const lv_img_dsc_t weather_sun;
extern const lv_img_dsc_t weather_sun_bg;
extern const lv_img_dsc_t weather_sun_line;
extern const lv_img_dsc_t humidity_icon;

extern const lv_img_dsc_t toggle_button_close;
extern const lv_img_dsc_t toggle_button_open;
extern const lv_img_dsc_t add_56;
extern const lv_img_dsc_t add_64;
extern const lv_img_dsc_t add_76;
extern const lv_img_dsc_t repeat_time_checked;
extern const lv_img_dsc_t repeat_time_unchecked;
extern const lv_img_dsc_t custom_repeat_time_checked;
extern const lv_img_dsc_t custom_repeat_time_unchecked;
extern const lv_img_dsc_t reduce;
extern const lv_img_dsc_t brightness_bg;
extern const lv_img_dsc_t brightness_mask;
extern const lv_img_dsc_t shutdown_img;
extern const lv_img_dsc_t restart_img;
extern const lv_img_dsc_t regulatory_infomation_;

//运动图标 72x72
extern const lv_img_dsc_t sport_run;                  //跑步
extern const lv_img_dsc_t sport_indoor_running;       //室内跑步
extern const lv_img_dsc_t sport_playground_running;   //操场跑步
extern const lv_img_dsc_t sport_track_run;            //越野跑步
extern const lv_img_dsc_t sport_walk;                 //步行
extern const lv_img_dsc_t sport_indoor_walk;          //室内步行
extern const lv_img_dsc_t sport_bike;                 //骑行
extern const lv_img_dsc_t sport_indoor_bike;          //室内骑行
extern const lv_img_dsc_t sport_road_bike;            //公路骑行
extern const lv_img_dsc_t sport_mountain_bike;        //山地骑行
extern const lv_img_dsc_t sport_commuting_bike;       //通勤骑行
extern const lv_img_dsc_t sport_long_distance_bike;   //长距离骑行
extern const lv_img_dsc_t sport_pool_swim;            //泳池游泳
extern const lv_img_dsc_t sport_open_water_swim;      //公开水域游泳
extern const lv_img_dsc_t sport_strength_training;    //力量训练
extern const lv_img_dsc_t sport_indoor_aerobics;      //室内有氧运动
extern const lv_img_dsc_t sport_elliptical;           //椭圆机
extern const lv_img_dsc_t sport_rowing_machine;       //划船机
extern const lv_img_dsc_t sport_mountain_climbing;    //登山
extern const lv_img_dsc_t sport_on_foot;              //徒步
extern const lv_img_dsc_t sport_skiing;               //滑雪
extern const lv_img_dsc_t sport_outdoor_aerobics;     //户外有氧运动
extern const lv_img_dsc_t sport_jump_rope;            //跳绳
extern const lv_img_dsc_t sport_triathlon;            //铁人三项
extern const lv_img_dsc_t sport_compound_exercise;    //复合运动
extern const lv_img_dsc_t sport_exercise;             //锻炼

//运动图标 56x56
extern const lv_img_dsc_t sport_run_56;                  //跑步
extern const lv_img_dsc_t sport_indoor_running_56;       //室内跑步
extern const lv_img_dsc_t sport_playground_running_56;   //操场跑步
extern const lv_img_dsc_t sport_track_run_56;            //越野跑步
extern const lv_img_dsc_t sport_walk_56;                 //步行
extern const lv_img_dsc_t sport_indoor_walk_56;          //室内步行
extern const lv_img_dsc_t sport_bike_56;                 //骑行
extern const lv_img_dsc_t sport_indoor_bike_56;          //室内骑行
extern const lv_img_dsc_t sport_road_bike_56;            //公路骑行
extern const lv_img_dsc_t sport_mountain_bike_56;        //山地骑行
extern const lv_img_dsc_t sport_commuting_bike_56;       //通勤骑行
extern const lv_img_dsc_t sport_long_distance_bike_56;   //长距离骑行
extern const lv_img_dsc_t sport_pool_swim_56;            //泳池游泳
extern const lv_img_dsc_t sport_open_water_swim_56;      //公开水域游泳
extern const lv_img_dsc_t sport_strength_training_56;    //力量训练
extern const lv_img_dsc_t sport_indoor_aerobics_56;      //室内有氧运动
extern const lv_img_dsc_t sport_elliptical_56;           //椭圆机
extern const lv_img_dsc_t sport_rowing_machine_56;       //划船机
extern const lv_img_dsc_t sport_mountain_climbing_56;    //登山
extern const lv_img_dsc_t sport_on_foot_56;              //徒步
extern const lv_img_dsc_t sport_skiing_56;               //滑雪
extern const lv_img_dsc_t sport_outdoor_aerobics_56;     //户外有氧运动
extern const lv_img_dsc_t sport_jump_rope_56;            //跳绳
extern const lv_img_dsc_t sport_triathlon_56;            //铁人三项
extern const lv_img_dsc_t sport_compound_exercise_56;    //复合运动
extern const lv_img_dsc_t sport_exercise_56;             //锻炼

//运动图标 44x44
extern const lv_img_dsc_t sport_run_44;                   //跑步
extern const lv_img_dsc_t sport_indoor_running_44;        //室内跑步
extern const lv_img_dsc_t sport_playground_running_44;    //操场跑步
extern const lv_img_dsc_t sport_track_run_44;             //越野跑步
extern const lv_img_dsc_t sport_walk_44;                  //步行
extern const lv_img_dsc_t sport_indoor_walk_44;           //室内步行
extern const lv_img_dsc_t sport_bike_44;                  //骑行
extern const lv_img_dsc_t sport_indoor_bike_44;           //室内骑行
extern const lv_img_dsc_t sport_road_bike_44;             //公路骑行
extern const lv_img_dsc_t sport_mountain_bike_44;         //山地骑行
extern const lv_img_dsc_t sport_commuting_bike_44;        //通勤骑行
extern const lv_img_dsc_t sport_long_distance_bike_44;    //长距离骑行
extern const lv_img_dsc_t sport_pool_swim_44;             //泳池游泳
extern const lv_img_dsc_t sport_open_water_swim_44;       //公开水域游泳
extern const lv_img_dsc_t sport_strength_training_44;     //力量训练
extern const lv_img_dsc_t sport_indoor_aerobics_44;       //室内有氧运动
extern const lv_img_dsc_t sport_elliptical_44;            //椭圆机
extern const lv_img_dsc_t sport_rowing_machine_44;        //划船机
extern const lv_img_dsc_t sport_mountain_climbing_44;     //登山
extern const lv_img_dsc_t sport_on_foot_44;               //徒步
extern const lv_img_dsc_t sport_skiing_44;                //滑雪
extern const lv_img_dsc_t sport_outdoor_aerobics_44;      //户外有氧运动
extern const lv_img_dsc_t sport_jump_rope_44;             //跳绳
extern const lv_img_dsc_t sport_triathlon_44;             //铁人三项
extern const lv_img_dsc_t sport_compound_exercise_44;     //复合运动
extern const lv_img_dsc_t sport_exercise_44;              //锻炼

extern const lv_img_dsc_t s_triathlon;
extern const lv_img_dsc_t s_strength_training;
extern const lv_img_dsc_t s_sport_walking;
extern const lv_img_dsc_t s_sport_treadmill;
extern const lv_img_dsc_t s_sport_playgroundrunning;
extern const lv_img_dsc_t s_sport_poolswimming;
extern const lv_img_dsc_t s_sport_openwaterswimming;
extern const lv_img_dsc_t s_sport_outdoorcycling;
extern const lv_img_dsc_t s_sport_indoorwalking;
extern const lv_img_dsc_t s_sport_indoorrunning;
extern const lv_img_dsc_t s_skiing;
extern const lv_img_dsc_t s_rowing_machine;
extern const lv_img_dsc_t s_road_riding;
extern const lv_img_dsc_t s_outdoor_aerobic;
extern const lv_img_dsc_t s_riding;
extern const lv_img_dsc_t s_mountaineering;
extern const lv_img_dsc_t s_mountain_riding;
extern const lv_img_dsc_t s_long_distance_riding;
extern const lv_img_dsc_t s_indoor_riding;
extern const lv_img_dsc_t s_jump_rope;
extern const lv_img_dsc_t s_indoor_aerobic;
extern const lv_img_dsc_t s_hiking;
extern const lv_img_dsc_t s_exercise;
extern const lv_img_dsc_t s_elliptical_machine;
extern const lv_img_dsc_t s_commuting_bybike;
extern const lv_img_dsc_t s_compound_motion;

//运动概览页面图标
extern const lv_img_dsc_t summarize_indoorrunning;
extern const lv_img_dsc_t summarize_treadmill;
extern const lv_img_dsc_t summarize_playgroundrunning;
extern const lv_img_dsc_t summarize_outdoorcycling;
extern const lv_img_dsc_t summarize_walking;
extern const lv_img_dsc_t summarize_indoorwalking;
extern const lv_img_dsc_t summarize_riding;
extern const lv_img_dsc_t summarize_indoor_riding;
extern const lv_img_dsc_t summarize_road_riding;
extern const lv_img_dsc_t summarize_mountain_riding;
extern const lv_img_dsc_t summarize_commuting_bybike;
extern const lv_img_dsc_t summarize_long_distance_riding;
extern const lv_img_dsc_t summarize_poolswimming;
extern const lv_img_dsc_t summarize_openwaterswimming;
extern const lv_img_dsc_t summarize_strength_training;
extern const lv_img_dsc_t summarize_indoor_aerobic;
extern const lv_img_dsc_t summarize_elliptical_machine;
extern const lv_img_dsc_t summarize_rowing_machine;
extern const lv_img_dsc_t summarize_mountaineering;
extern const lv_img_dsc_t summarize_hiking;
extern const lv_img_dsc_t summarize_skiing;
extern const lv_img_dsc_t summarize_outdoor_aerobic;
extern const lv_img_dsc_t summarize_jump_rope;
extern const lv_img_dsc_t summarize_triathlon;
extern const lv_img_dsc_t summarize_compound_motion;
extern const lv_img_dsc_t summarize_exercise;

extern const lv_img_dsc_t sport_start_prompt;

extern const lv_img_dsc_t gps_lost;
extern const lv_img_dsc_t gps_located;
extern const lv_img_dsc_t low_battery;
extern const lv_img_dsc_t battery_bg;
extern const lv_img_dsc_t charge_img;
extern const lv_img_dsc_t signal_full;
extern const lv_img_dsc_t signal_low;
extern const lv_img_dsc_t signal_normal;
extern const lv_img_dsc_t sensor_link;

extern const lv_img_dsc_t sensor_cadence;
extern const lv_img_dsc_t sensor_external_heart;
extern const lv_img_dsc_t sensor_frontlight;
extern const lv_img_dsc_t sensor_heart;
extern const lv_img_dsc_t sensor_power;
extern const lv_img_dsc_t sensor_radar;
extern const lv_img_dsc_t sensor_rideplatform;
extern const lv_img_dsc_t sensor_runbeans;
extern const lv_img_dsc_t sensor_speed;
extern const lv_img_dsc_t sensor_speed_cadence;
extern const lv_img_dsc_t sensor_taillight;

extern const lv_img_dsc_t sensor_cadence_44;
extern const lv_img_dsc_t sensor_external_heart_44;
extern const lv_img_dsc_t sensor_frontlight_44;
extern const lv_img_dsc_t sensor_heart_44;
extern const lv_img_dsc_t sensor_power_44;
extern const lv_img_dsc_t sensor_radar_44;
extern const lv_img_dsc_t sensor_rideplatform_44;
extern const lv_img_dsc_t sensor_runbeans_44;
extern const lv_img_dsc_t sensor_speed_44;
extern const lv_img_dsc_t sensor_speed_cadence_44;
extern const lv_img_dsc_t sensor_taillight_44;

extern const lv_img_dsc_t sensor_cadence_44_r;
extern const lv_img_dsc_t sensor_external_heart_44_r;
extern const lv_img_dsc_t sensor_frontlight_44_r;
extern const lv_img_dsc_t sensor_heart_44_r;
extern const lv_img_dsc_t sensor_power_44_r;
extern const lv_img_dsc_t sensor_radar_44_r;
extern const lv_img_dsc_t sensor_rideplatform_44_r;
extern const lv_img_dsc_t sensor_runbeans_44_r;
extern const lv_img_dsc_t sensor_speed_44_r;
extern const lv_img_dsc_t sensor_speed_cadence_44_r;
extern const lv_img_dsc_t sensor_taillight_44_r;

extern const lv_img_dsc_t sensor_cadence_44_g;
extern const lv_img_dsc_t sensor_external_heart_44_g;
extern const lv_img_dsc_t sensor_frontlight_44_g;
extern const lv_img_dsc_t sensor_heart_44_g;
extern const lv_img_dsc_t sensor_power_44_g;
extern const lv_img_dsc_t sensor_radar_44_g;
extern const lv_img_dsc_t sensor_rideplatform_44_g;
extern const lv_img_dsc_t sensor_runbeans_44_g;
extern const lv_img_dsc_t sensor_speed_44_g;
extern const lv_img_dsc_t sensor_speed_cadence_44_g;
extern const lv_img_dsc_t sensor_taillight_44_g;

extern const lv_img_dsc_t sensor_cadence_44_y;
extern const lv_img_dsc_t sensor_external_heart_44_y;
extern const lv_img_dsc_t sensor_frontlight_44_y;
extern const lv_img_dsc_t sensor_heart_44_y;
extern const lv_img_dsc_t sensor_power_44_y;
extern const lv_img_dsc_t sensor_radar_44_y;
extern const lv_img_dsc_t sensor_rideplatform_44_y;
extern const lv_img_dsc_t sensor_runbeans_44_y;
extern const lv_img_dsc_t sensor_speed_44_y;
extern const lv_img_dsc_t sensor_speed_cadence_44_y;
extern const lv_img_dsc_t sensor_taillight_44_y;

extern const lv_img_dsc_t sensor_cadence_128_r;
extern const lv_img_dsc_t sensor_external_heart_128_r;
extern const lv_img_dsc_t sensor_frontlight_128_r;
extern const lv_img_dsc_t sensor_heart_128_r;
extern const lv_img_dsc_t sensor_power_128_r;
extern const lv_img_dsc_t sensor_radar_128_r;
extern const lv_img_dsc_t sensor_rideplatform_128_r;
extern const lv_img_dsc_t sensor_runbeans_128_r;
extern const lv_img_dsc_t sensor_speed_128_r;
extern const lv_img_dsc_t sensor_speed_cadence_128_r;
extern const lv_img_dsc_t sensor_taillight_128_r;

extern const lv_img_dsc_t feel_very_relaxed;
extern const lv_img_dsc_t feel_relaxed;
extern const lv_img_dsc_t feel_moderate;
extern const lv_img_dsc_t feel_tired;
extern const lv_img_dsc_t feel_very_tired;

extern const lv_img_dsc_t gps_signal_weak;
extern const lv_img_dsc_t gps_signal_strong;
extern const lv_img_dsc_t gps_signal_no;
extern const lv_img_dsc_t gps_signal_medium;
extern const lv_img_dsc_t gps_signal_close;
extern const lv_img_dsc_t power_border;

extern const lv_img_dsc_t start_sport_img;
extern const lv_img_dsc_t manual_pause_img;
extern const lv_img_dsc_t auto_pause_img;
extern const lv_img_dsc_t give_up_select_sport_img;
extern const lv_img_dsc_t save_select_sport_img;
extern const lv_img_dsc_t gps_not_located;

extern const lv_img_dsc_t btn_bind_img;
extern const lv_img_dsc_t btn_cancel_img;
extern const lv_img_dsc_t btn_delay_snap_img;
extern const lv_img_dsc_t btn_details_img;
extern const lv_img_dsc_t btn_disable_delay_snap_img;
extern const lv_img_dsc_t btn_disable_img;
extern const lv_img_dsc_t btn_disable_snap_img;
extern const lv_img_dsc_t btn_edit_img;
extern const lv_img_dsc_t btn_end_img;
extern const lv_img_dsc_t btn_go_img;
extern const lv_img_dsc_t btn_hang_up_img;
extern const lv_img_dsc_t btn_lap_img;
extern const lv_img_dsc_t btn_list_img;
extern const lv_img_dsc_t btn_mute_img;
extern const lv_img_dsc_t btn_mute_img_y;
extern const lv_img_dsc_t btn_ok_img;
extern const lv_img_dsc_t btn_pause_img;
extern const lv_img_dsc_t btn_refresh_img;
extern const lv_img_dsc_t btn_seting_img;
extern const lv_img_dsc_t btn_sleep_on_img;
extern const lv_img_dsc_t btn_snap_img;
extern const lv_img_dsc_t btn_start_img;
extern const lv_img_dsc_t btn_start_disable_img;
extern const lv_img_dsc_t btn_unbind_img;
extern const lv_img_dsc_t btn_stop_img;
extern const lv_img_dsc_t btn_restore_img;
extern const lv_img_dsc_t btn_disable_restore_img;

#ifdef SIMULATOR
extern const lv_img_dsc_t music_cd_player_ing;
extern const lv_img_dsc_t music_stylus_start_img;
extern const lv_img_dsc_t music_stylus_stop_img;
extern const lv_img_dsc_t music_volume_add_img;
extern const lv_img_dsc_t music_volume_sub_img;
extern const lv_img_dsc_t music_stylus_img;
#endif // SIMULATOR
extern const lv_img_dsc_t music_btn_back_img;
extern const lv_img_dsc_t music_btn_next_img;
extern const lv_img_dsc_t music_btn_start_img;
extern const lv_img_dsc_t music_btn_pause_img;
extern const lv_img_dsc_t music_volume_bk_img;

//无轨迹时图标
extern const lv_img_dsc_t trajectory_indoor_running;

extern const lv_img_dsc_t max_img_40;
extern const lv_img_dsc_t avg_img_40;
extern const lv_img_dsc_t up_img_40;
extern const lv_img_dsc_t down_img_40;

extern const lv_img_dsc_t charging_img;
extern const lv_img_dsc_t charging_running;
extern const lv_img_dsc_t super_low_battery;

extern const lv_img_dsc_t grid_bg_line_horiz;
extern const lv_img_dsc_t grid_bg_line_vert_2_up;
extern const lv_img_dsc_t grid_bg_line_vert;
extern const lv_img_dsc_t grid_bg_line_vert_2_down;
extern const lv_img_dsc_t grid_bg_line_vert_3_mid;
extern const lv_img_dsc_t grid_bg_line_vert_3_upmid;
extern const lv_img_dsc_t grid_bg_line_vert_4_mid;
extern const lv_img_dsc_t grid_bg_line_vert_4_mid2;
extern const lv_img_dsc_t grid_altitude_arrow;
extern const lv_img_dsc_t grid_map_arrow;
extern const lv_img_dsc_t grid_map_dir_guide;
extern const lv_img_dsc_t grid_map_dir;
extern const lv_img_dsc_t grid_map_user_gpsoff;
extern const lv_img_dsc_t grid_map_user;
extern const lv_img_dsc_t grid_bunny_run;
extern const lv_img_dsc_t grid_bunny_run_gray;
extern const lv_img_dsc_t grid_bunny_run_pause;
extern const lv_img_dsc_t grid_bunny_ride;
extern const lv_img_dsc_t grid_bunny_ride_gray;
extern const lv_img_dsc_t grid_bunny_ride_pause;
extern const lv_img_dsc_t grid_bunny_hrm;

extern const lv_img_dsc_t watch_lock_img;
extern const lv_img_dsc_t watch_unlock_img;

extern const lv_img_dsc_t navi_track_start;
extern const lv_img_dsc_t navi_track_end;
#ifdef SIMULATOR
extern const lv_img_dsc_t drink_water_img;
extern const lv_img_dsc_t navi_prv_trak_mask;
extern const lv_img_dsc_t time_to_sleep_img;
extern const lv_img_dsc_t navi_no_route_img;
extern const lv_img_dsc_t app_train_course_img;
extern const lv_img_dsc_t about_line_img;

#endif

extern const lv_img_dsc_t im_grid_arrow;
extern const lv_img_dsc_t activity_hours_tips;
extern const lv_img_dsc_t current_activity_hours_bg;
extern const lv_img_dsc_t intensity_activity_duration_tips;
extern const lv_img_dsc_t current_intensity_activity_duration_bg;
extern const lv_img_dsc_t climbing_floors_tips;
extern const lv_img_dsc_t current_climbing_floors_bg;
extern const lv_img_dsc_t app_sleep_no_data;
extern const lv_img_dsc_t app_sleep_no_nap_data;
extern const lv_img_dsc_t sleep_tips;
extern const lv_img_dsc_t app_sleep_nap;

extern const lv_img_dsc_t alipay_help;
extern const lv_img_dsc_t alipay_payment_code;
extern const lv_img_dsc_t alipay_ride_code;
extern const lv_img_dsc_t alipay_seting;
extern const lv_img_dsc_t alipay_qrcode_fail;
extern const lv_img_dsc_t alipay_barcode_fail;
extern const lv_img_dsc_t fail_logo_128;
extern const lv_img_dsc_t success_logo_128;
extern const lv_img_dsc_t activity_hours_progress_bg;

extern const lv_img_dsc_t summary_chart_hrm;
extern const lv_img_dsc_t summary_chart_pace;
extern const lv_img_dsc_t summary_chart_spd;
extern const lv_img_dsc_t summary_chart_pwr;
extern const lv_img_dsc_t summary_chart_stepfreq;
extern const lv_img_dsc_t summary_chart_steplen;
extern const lv_img_dsc_t summary_chart_cad;
extern const lv_img_dsc_t summary_chart_alt;
extern const lv_img_dsc_t heart_rate_hight;
extern const lv_img_dsc_t heart_rate_low;
extern const lv_img_dsc_t app_10_height_max;
extern const lv_img_dsc_t app_10_height_min;
extern const lv_img_dsc_t msg_heart_rate;
extern const lv_img_dsc_t msg_heart_rate_hight;
extern const lv_img_dsc_t msg_heart_rate_low;
extern const lv_img_dsc_t msg_pressure;
extern const lv_img_dsc_t msg_pressure_remind;
extern const lv_img_dsc_t msg_spo2;

extern const lv_img_dsc_t battery_charging;
extern const lv_img_dsc_t battery_charging_over;
extern const lv_img_dsc_t altitude_acl_succ;
extern const lv_img_dsc_t altitude_acl_failed;
extern const lv_img_dsc_t altitude_acl_ing;
extern const lv_img_dsc_t arrow_down;
extern const lv_img_dsc_t activity_no_record;
extern const lv_img_dsc_t run_radar;
extern const lv_img_dsc_t run_grade_bg;
extern const lv_img_dsc_t rotate_pointer;
extern const lv_img_dsc_t app_traing_down;
extern const lv_img_dsc_t app_traing_keep;
extern const lv_img_dsc_t app_traing_quick_rise;
extern const lv_img_dsc_t app_traing_rise;
extern const lv_img_dsc_t power_consume_reminder;
extern const lv_img_dsc_t ride_ability_reminder;
extern const lv_img_dsc_t run_ability_reminder;

extern const lv_img_dsc_t vrb_ride_backward;   //使用场景-虚拟兔子落后弹窗
extern const lv_img_dsc_t vrb_run_backward;

//训练课程详情页
extern const lv_img_dsc_t course_target_free;
extern const lv_img_dsc_t course_target_power;
extern const lv_img_dsc_t course_target_heart_rate;
extern const lv_img_dsc_t course_target_speed;
extern const lv_img_dsc_t course_target_cadence;

//智能通知
extern const lv_img_dsc_t notification_app;
extern const lv_img_dsc_t notification_app_40;
extern const lv_img_dsc_t notification_app_56;
extern const lv_img_dsc_t notification_app_76;
extern const lv_img_dsc_t notification_call;
extern const lv_img_dsc_t notification_message;
extern const lv_img_dsc_t notification_call_40;
extern const lv_img_dsc_t notification_message_40;
extern const lv_img_dsc_t notification_note_40;
extern const lv_img_dsc_t notification_call_56;
extern const lv_img_dsc_t notification_message_56;
extern const lv_img_dsc_t notification_note_56;
extern const lv_img_dsc_t notification_call_76;
extern const lv_img_dsc_t notification_message_76;
extern const lv_img_dsc_t notification_note_76;
extern const lv_img_dsc_t notification_no_message_128;

//导航转向
extern const lv_img_dsc_t turn_left;
extern const lv_img_dsc_t turn_right;
extern const lv_img_dsc_t right_sharp_turn;
extern const lv_img_dsc_t left_sharp_turn;
extern const lv_img_dsc_t turn_around_left;
extern const lv_img_dsc_t turn_around_right;
extern const lv_img_dsc_t keep_on_left;
extern const lv_img_dsc_t keep_on_right;

extern const lv_img_dsc_t edit_dial;
extern const lv_img_dsc_t dial_select;

extern const lv_img_dsc_t metronome_down_part;
extern const lv_img_dsc_t metronome_pointer;
extern const lv_img_dsc_t metronome_up_part;

extern const lv_img_dsc_t red;
extern const lv_img_dsc_t green;
extern const lv_img_dsc_t yellow;

extern const lv_img_dsc_t training_plan_today_g;
extern const lv_img_dsc_t training_plan_today_w;

extern const lv_img_dsc_t ota_check_update;

extern const lv_img_dsc_t find_phone;
// 移至外部的图片
#ifndef IGS_DEV
extern const lv_img_dsc_t input_focus_bg_418_96;
extern const lv_img_dsc_t input_focus_bg_350_96;
extern const lv_img_dsc_t input_focus_bg_186_96;
extern const lv_img_dsc_t input_focus_bg_164_96;
extern const lv_img_dsc_t input_focus_bg_120_96;
extern const lv_img_dsc_t input_focus_bg_108_96;
extern const lv_img_dsc_t input_focus_bg_80_96;
extern const lv_img_dsc_t input_focus_bg_62_96;

extern const lv_img_dsc_t today_avtivity_bg;
extern const lv_img_dsc_t compass_en_img;
extern const lv_img_dsc_t compass_zn_img;
extern const lv_img_dsc_t compass_img;
extern const lv_img_dsc_t compass_bg;
extern const lv_img_dsc_t compass_bg1;

extern const lv_img_dsc_t breath_training;
extern const lv_img_dsc_t breath_train_end;

extern const lv_img_dsc_t sport_normal_200;               //普通
extern const lv_img_dsc_t sport_run_200;                  //跑步
extern const lv_img_dsc_t sport_indoor_running_200;       //室内跑步
extern const lv_img_dsc_t sport_playground_running_200;   //操场跑步
extern const lv_img_dsc_t sport_track_run_200;            //越野跑
extern const lv_img_dsc_t sport_walk_200;                 //步行
extern const lv_img_dsc_t sport_indoor_walk_200;          //室内步行
extern const lv_img_dsc_t sport_bike_200;                 //骑行
extern const lv_img_dsc_t sport_indoor_bike_200;          //室内骑行
extern const lv_img_dsc_t sport_commuting_bike_200;       //通勤骑行
extern const lv_img_dsc_t sport_long_distance_bike_200;   //长距离骑行
extern const lv_img_dsc_t sport_pool_swim_200;            //游泳池游泳
extern const lv_img_dsc_t sport_open_water_swim_200;      //公开水域游泳
extern const lv_img_dsc_t sport_strength_training_200;    //力量训练
extern const lv_img_dsc_t sport_indoor_aerobics_200;      //室内有氧
extern const lv_img_dsc_t sport_elliptical_200;           //椭圆机
extern const lv_img_dsc_t sport_rowing_machine_200;       //划船机
extern const lv_img_dsc_t sport_mountain_climbing_200;    //登山
extern const lv_img_dsc_t sport_on_foot_200;              //徒步
extern const lv_img_dsc_t sport_skiing_200;               //滑雪
extern const lv_img_dsc_t sport_outdoor_aerobics_200;     //户外有氧
extern const lv_img_dsc_t sport_jump_rope_200;            //跳绳
extern const lv_img_dsc_t sport_triathlon_200;            //铁人三项
extern const lv_img_dsc_t sport_compound_exercise_200;    //复合运动
extern const lv_img_dsc_t sport_exercise_200;             //锻炼

extern const lv_img_dsc_t ach_longest_time;            //最长时间
extern const lv_img_dsc_t ach_longest_ride;            //最长骑行
extern const lv_img_dsc_t ach_furthest_run;            //最长跑步
extern const lv_img_dsc_t ach_fastest_average_speed;   //最快平均速度
extern const lv_img_dsc_t ach_fastest_average_pace;    //最快平均配速
extern const lv_img_dsc_t ach_highest_climb;           //最高爬升
extern const lv_img_dsc_t ach_best_ride_power;         //最佳骑行功率
extern const lv_img_dsc_t ach_longest_swim;            //最长游泳
extern const lv_img_dsc_t ach_most_jumps;              //最多跳跃
extern const lv_img_dsc_t ach_run_5km;                 //最佳5公里
extern const lv_img_dsc_t ach_run_10km;                //最佳10公里
extern const lv_img_dsc_t ach_run_21km;                //最佳半马
extern const lv_img_dsc_t ach_run_42km;                //最佳全马

extern const lv_img_dsc_t weather00;
extern const lv_img_dsc_t weather01;
extern const lv_img_dsc_t weather02;
extern const lv_img_dsc_t weather03;
extern const lv_img_dsc_t weather04;
extern const lv_img_dsc_t weather05;
extern const lv_img_dsc_t weather06;
extern const lv_img_dsc_t weather07;
extern const lv_img_dsc_t weather08;
extern const lv_img_dsc_t weather09;
extern const lv_img_dsc_t weather10;
extern const lv_img_dsc_t weather11;
extern const lv_img_dsc_t weather12;
extern const lv_img_dsc_t weather13;
extern const lv_img_dsc_t weather14;
extern const lv_img_dsc_t weather15;
extern const lv_img_dsc_t weather16;
extern const lv_img_dsc_t weather17;
extern const lv_img_dsc_t weather18;
extern const lv_img_dsc_t weather19;
#endif
///////////////////////////////////////////////

#define IM_H(img) ((img).header.h)
#define IM_W(img) ((img).header.w)

#ifdef __cplusplus
}
#endif

#endif   //__UI_IMAGES_H
