﻿/*************************************************************************
 * @file algo_service_distance.c
 * <AUTHOR> (<EMAIL>)
 * @brief 距离算法组件实现
 * @version 0.1
 * @date 2024-11-27
 *
 * @copyright Copyright (c) 2024-2025, Wuhan Qiwu Technology Co., Ltd
 *
 ************************************************************************/
#include "algo_service_distance.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_task.h"
#include "cfg_header_def.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"

// 输入数据
typedef struct
{
    uint32_t distance_delta;         //前后两个点之间的距离差 100 * m, 可以来自ANT也可来自GPS
    saving_status_e saving_status;   //数据记录的状态
} algo_distance_sub_t;

static algo_distance_sub_t s_algo_in = {0};

// 发布数据
static algo_distance_pub_t s_algo_out = {0};

// 本算法打开标记
static bool s_is_open = false;

//距离警示报警检测
static void auto_alert_dist_check(sports_alert_t *p_alert, uint32_t distance)
{
    uint32_t distMoving = distance / 100;   //单位m
    SPORTTYPE sport_type = get_current_sport_mode();
    int dist_alert_used = get_sport_remind_en(sport_type, SPORT_REMIND_DISTANCE, MAIN_EN);
    static uint32_t alertDist = 0;
    uint32_t dist_threshold = get_sport_remind_value(sport_type, SPORT_REMIND_DISTANCE, true);   //警示阈值
    sports_alert_t alert_msg = {0, 0, 0, 0};

    //TEST
    // dist_alert_used = true;
    // dist_threshold = 200;

    if (NULL == p_alert)
    {
        return;
    }

    if (dist_alert_used && dist_threshold > 0)   //当前报警开启
    {
        if (distMoving >= dist_threshold)
        {
            alert_msg.high_alert_status = true;
        }
        else
        {
            alert_msg.high_alert_status = false;
        }

        //如果阈值变化, 记录的基准值立即向下调整到阈值的整数倍, 如果计算后当前值大于
        if (0 < (alertDist % dist_threshold))
        {
            alertDist = alertDist / dist_threshold * dist_threshold;
        }

        //基准值总是等于阈值的整数倍
        if (distMoving >= dist_threshold + alertDist)
        {
            alertDist += (distMoving - alertDist) / dist_threshold * dist_threshold;
            alert_msg.high_alert_event = true;
        }
        else
        {
            alert_msg.high_alert_event = false;
        }
    }
    else
    {
        alert_msg.high_alert_event = false;
    }

    memcpy(p_alert, &alert_msg, sizeof(sports_alert_t));
}

/**
 * @brief 算法处理
 *
 * @param algo_out 输出数据
 * @param algo_in 输入数据
 */
static void algo_distance_deal(algo_distance_pub_t *algo_out, const algo_distance_sub_t *algo_in)
{
    if (enum_status_saving == algo_in->saving_status)
    {
        algo_out->distance += algo_in->distance_delta;
        algo_out->distance_lap += algo_in->distance_delta;

        // 运动警示
        auto_alert_dist_check(&algo_out->alert, algo_out->distance);
    }
}

/**
 * @brief 算法控制
 *
 * @param algo_out 输出数据
 * @param ctrl_type 控制类型
 */
static void algo_distance_ctrl(algo_distance_pub_t *algo_out, const algo_sports_ctrl_t *ctrl)
{
    ctrl_type_e ctrl_type = ctrl->ctrl_type;
    evt_lap_type_e lap_type = ctrl->lap_type;
    SPORTTYPE sport_type = get_current_sport_mode();
    uint32_t auto_lap_distance = get_auto_record_lap_value(sport_type, AUTO_RECORD_LAP_DISTANCE);   //m

    if (enum_ctrl_start == ctrl_type)
    {
        algo_out->distance = 0;
        algo_out->distance_lap = 0;
        algo_out->distance_pre_lap = 0;
    }
    else if (enum_ctrl_lap == ctrl_type)
    {
        algo_out->distance_pre_lap = algo_out->distance_lap;
        algo_out->distance_lap = 0;

        // 自动计圈距离修正
        if (enum_lap_distance == lap_type)
        {
            if (auto_lap_distance * 100 < algo_out->distance_pre_lap)
            {
                algo_out->distance_lap = algo_out->distance_pre_lap - auto_lap_distance * 100;
            }

            algo_out->distance_pre_lap = auto_lap_distance * 100;
        }
    }
}

/**
 * @brief 算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_speed_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_DISTANCE;
    head.input_type = DATA_ID_ALGO_SPEED;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法控制订阅处理
 *
 * @param in 控制数据
 * @param len 数据长度
 */
static void algo_sports_ctrl_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_DISTANCE;
    head.input_type = DATA_ID_EVENT_SPORTS_CTRL;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_distance_out_callback(const void *out, uint32_t len)
{
    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_ALGO_DISTANCE, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("%s publish error", __FUNCTION__);
    }
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_speed",
        .topic_id = DATA_ID_ALGO_SPEED,
        .callback = algo_speed_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
};

/**
 * @brief 算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_distance_init(void)
{
    algo_distance_sub_t *algo_in = &s_algo_in;
    algo_distance_pub_t *algo_out = &s_algo_out;

    memset(algo_in, 0, sizeof(algo_distance_sub_t));
    memset(algo_out, 0, sizeof(algo_distance_pub_t));
    return 0;
}

/**
 * @brief 算法open
 *
 * @return int32_t 结果
 */
static int32_t algo_distance_open(void)
{
    if (s_is_open)
    {
        ALGO_COMP_LOG_W("%s already open", __FUNCTION__);
        return 0;
    }

    ALGO_COMP_LOG_D("%s open", __FUNCTION__);

    s_is_open = true;

    return algo_topic_list_subscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
}

/**
 * @brief 算法feed
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_distance_feed(uint32_t input_type, void *data, uint32_t len)
{
    algo_distance_sub_t *algo_in = &s_algo_in;
    algo_distance_pub_t *algo_out = &s_algo_out;

    switch (input_type)
    {
    case DATA_ID_ALGO_SPEED:
    {
        const algo_speed_pub_t *speed_data = (algo_speed_pub_t *) data;
        algo_in->distance_delta = speed_data->distance_delta;   //与速度算法发布频率一致才可直接赋值

        //算法处理
        algo_distance_deal(algo_out, algo_in);
    }
    break;
    case DATA_ID_EVENT_SPORTS_CTRL:
    {
        const algo_sports_ctrl_t *sports_ctrl = (algo_sports_ctrl_t *) data;
        algo_in->saving_status = sports_ctrl->saving_status;

        //算法控制
        algo_distance_ctrl(algo_out, sports_ctrl);
    }
    break;
    default:
        break;
    }

    //数据发布
    algo_distance_out_callback(algo_out, sizeof(algo_distance_pub_t));
    return 0;
}

/**
 * @brief 算法close
 *
 * @return int32_t 结果
 */
static int32_t algo_distance_close(void)
{
    if (!s_is_open)
    {
        ALGO_COMP_LOG_W("%s already close", __FUNCTION__);
        return 0;
    }

    algo_topic_list_unsubscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));

    s_is_open = false;
    ALGO_COMP_LOG_D("%s close", __FUNCTION__);
    return 0;
}

// 算法组件实现
static algo_compent_ops_t s_distance_algo = {
    .init = algo_distance_init,
    .open = algo_distance_open,
    .feed = algo_distance_feed,
    .close = algo_distance_close,
    .ioctl = NULL,
};

/**
 * @brief 组件注册
 *
 * @return int32_t 结果
 */
int32_t register_distance_algo(void)
{
    algo_compnent_register(ALGO_TYPE_DISTANCE, &s_distance_algo);
    return 0;
}