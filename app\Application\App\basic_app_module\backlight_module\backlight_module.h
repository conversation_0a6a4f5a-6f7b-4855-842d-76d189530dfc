/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   backlight_module.h
@Time    :   2024/12/10 10:56:18
*
**************************************************************************/

#ifndef __BACKLIGHT_MODULE_H
#define __BACKLIGHT_MODULE_H

#include <stdbool.h>
#include <stdint.h>

#define TEMP_SHOW_GPS_SNR 0

#ifdef __cplusplus
extern "C"
{
#endif

    /**
     * @brief 背光设置的运行状态
     */
    typedef enum {
        BK_STATUS_OFF,        // 处于息屏
        BK_STATUS_AOD,        // 处于AOD
        BK_STATUS_ON,         // 处于亮屏
    } BK_STATUS;

    /**
     * @brief 背光设置的运行状态
     */
    typedef enum {
        BK_ALGO_WRISTUP,        // 抬腕亮屏
        BK_ALGO_AUTO_LIGHT,     // 自动亮度
    } BK_ALGO_TYPE;

    /**
     * @brief 强制亮屏状态
     */
    typedef enum {
        BK_FORCE_DISABLE,       // 没有强制亮屏
        BK_FORCE_PAGE,          // 强制亮屏 (自动亮度有效)
        BK_FORCE_EVT,           // 强制亮屏 (50%亮度)
    } BK_FORCE_SENCE;

    /**
     * @brief lcd背光初始化
     */
    void backlight_init_app(void);

    /**
    * @brief 抬腕亮屏初始化
    */
    void backlight_wrist_init_app(void);

    /**
     * @brief 触发背光,按照当前场景重置背光时间和亮度
     */
    void backlight_open_app();

    /**
     * @brief 自动背光回调
     */
    void backlight_auto_check_app();

    /**
     * @brief 关闭背光 中断计时立刻关闭背光 由按键和落腕动作触发
     */
    void backlight_close_app();

    /**
     * @brief 由电源关闭触发关闭背光,
     *
     */
    void backlight_close_app_by_power_off(void);

    /**
     * @brief 应用调用 设置背光亮度
     * @param pct 背光百分比
     * @param hbm 模式
     */
    void backlight_set_lcd_pct(uint8_t pct, bool hbm);

    /**
     * @brief 强制背光开启
     * @param status 状态
     */
    void set_backlight_force_on(BK_FORCE_SENCE status);

    /**
     * @brief 弹窗背光开启
     * @param status 状态
     */
    void set_backlight_msg_on(bool status);

    /**
     * @brief debug 设置背光亮度,
     * 同时打开背光设置状态, 忽略任何背光指令, 传入0xff关闭设置状态, 恢复原有背光亮度
     * @param percent 0 - 100 / 0xff关闭设置状态
     */
    void backlight_percent_set_app(uint8_t percent);

    /**
     * @brief debug 设置HBM背光亮度,
     * 同时打开背光设置状态, 忽略任何背光指令, 传入0xff关闭设置状态, 恢复原有背光亮度
     * @param percent 0 - 100 / 0xff关闭设置状态
     */
    void backlight_hbm_percent_set_app(uint8_t percent);

    /**
     * @brief 获取背光模块初始化状态
     * @return 初始化状态
     */
    bool get_backlight_is_inited();

    /**
     * @brief 获取背光状态
     * @return 状态
     */
    BK_STATUS get_backlight_status();

    /**
     * @brief 订阅背光相关的算法
     * @param type 算法类型
     */
    void subscribe_backlight_algo(BK_ALGO_TYPE type);

    /**
     * @brief 取消订阅背光相关的算法
     * @param type 算法类型
     */
    void unsubscribe_backlight_algo(BK_ALGO_TYPE type);

#if TEMP_SHOW_GPS_SNR
    //关闭屏幕电源
    void bk_close_screen_poweroff(void);
    bool get_ldc_off_gui_skip(void);
    void set_ldc_off_gui_skip(bool skip);
#endif

#ifdef __cplusplus
}
#endif

#endif   // __BACKLIGHT_MODULE_H
