/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   DialDataSelectView.cpp
@Time    :   2024/12/13 16:16:33
* 
**************************************************************************/
#include "DialDataSelectView.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QwGUITheme.h"
#include "Image/images.h"
#include "GUI/QwGUIKey.h"
#include "../../../DialCommon.h"

static const char g_edit_component[] = "_edit_component";
TM_KEY(g_edit_component)

static const char g_step_count[] = "_step_count";
TM_KEY(g_step_count)
static const char g_battery_p[] = "_battery_p";
TM_KEY(g_battery_p)
static const char g_elevation[] = "_elevation";
TM_KEY(g_elevation)
static const char g_timer[] = "_timer";
TM_KEY(g_timer)
static const char g_calories[] = "_calories";
TM_KEY(g_calories)
static const char g_stopwatch[] = "_stopwatch";
TM_KEY(g_stopwatch)
static const char g_alarm_clock[] = "_alarm_clock";
TM_KEY(g_alarm_clock)
static const char g_run_vo_2_max[] = "_run_vo_2_max";
TM_KEY(g_run_vo_2_max)
static const char g_clcling_vo_2_max[] = "_clcling_vo_2_max";
TM_KEY(g_clcling_vo_2_max)
static const char g_air_pressure[] = "_air_pressure";
TM_KEY(g_air_pressure)
static const char g_weather[] = "_weather";
TM_KEY(g_weather)
static const char g_heart_rate_broadcast[] = "_heart_rate_broadcast";
TM_KEY(g_heart_rate_broadcast)
static const char g_stress[] = "_stress";
TM_KEY(g_stress)
static const char g_alipay[] = "_alipay";
TM_KEY(g_alipay)
static const char g_time_of_intense_activity[] = "_time_of_intense_activity";
TM_KEY(g_time_of_intense_activity)
static const char g_electronic_compass[] = "_electronic_compass";
TM_KEY(g_electronic_compass)
static const char g_heart_rate[] = "_heart_rate";
TM_KEY(g_heart_rate)
static const char g_sleep[] = "_sleep";
TM_KEY(g_sleep)
static const char g_recovery_time[] = "_recovery_time";
TM_KEY(g_recovery_time)
static const char g_physical_strength[] = "_physical_strength";
TM_KEY(g_physical_strength)


// 中文，按照拼音排序
menu_info dialDataChineseInfo_[DATA_TYPE_MAX] = {
    DATA_TYPE_STEP, 			g_step_count,				//步数
	DATA_TYPE_BATTERY,	 		g_battery_p,				//电量
	DATA_TYPE_ALTITUDE, 		g_elevation,				//海拔
	DATA_TYPE_RECOVERY_TIME,	g_recovery_time,			//恢复时间	
	DATA_TYPE_TIMER,			g_timer,					//计时器
	DATA_TYPE_CAL,				g_calories,					//卡路里
	DATA_TYPE_STOPWATCH,		g_stopwatch,				//秒表	
	DATA_TYPE_ALARM,			g_alarm_clock,				//闹钟
	DATA_TYPE_RUN_VO2MAX,		g_run_vo_2_max,				//跑步摄氧量
	DATA_TYPE_BAROMETRIC,		g_air_pressure,				//气压
	DATA_TYPE_RIDE_VO2MAX,		g_clcling_vo_2_max,			//骑行摄氧量
	DATA_TYPE_INTENSE,			g_time_of_intense_activity,	//强度活动时长
	DATA_TYPE_SLEEP,			g_sleep,					//睡眠
	DATA_TYPE_PHYSICAL_STRENGTH,g_physical_strength,		//体力恢复
	DATA_TYPE_HR,				g_heart_rate,				//心率
	DATA_TYPE_WEATHER,			g_weather,					//天气
	DATA_TYPE_HR_PUSH,			g_heart_rate_broadcast,		//心率推送
	DATA_TYPE_STRESS,			g_stress,					//压力
	DATA_TYPE_ALIPAY,			g_alipay,					//支付宝
	DATA_TYPE_COMPASS,			g_electronic_compass,		//指南针
};

// 英文，按照首字母排序
menu_info dialDataEnglishInfo_[DATA_TYPE_MAX] = {
	DATA_TYPE_BAROMETRIC,		g_air_pressure,				//气压
	DATA_TYPE_ALARM,			g_alarm_clock,				//闹钟
	DATA_TYPE_ALIPAY,			g_alipay,					//支付宝
	DATA_TYPE_BATTERY,	 		g_battery_p,				//电量
	DATA_TYPE_HR_PUSH,			g_heart_rate_broadcast,		//心率推送
	DATA_TYPE_CAL,				g_calories,					//卡路里
	DATA_TYPE_RIDE_VO2MAX,		g_clcling_vo_2_max,			//骑行摄氧量
	DATA_TYPE_COMPASS,			g_electronic_compass,		//指南针
	DATA_TYPE_ALTITUDE, 		g_elevation,				//海拔	
	DATA_TYPE_HR,				g_heart_rate,				//心率
	DATA_TYPE_PHYSICAL_STRENGTH,g_physical_strength,		//体力恢复
	DATA_TYPE_RECOVERY_TIME,	g_recovery_time,			//恢复时间
	DATA_TYPE_INTENSE,			g_time_of_intense_activity,	//强度活动时长
	DATA_TYPE_RUN_VO2MAX,		g_run_vo_2_max,				//跑步摄氧量
	DATA_TYPE_SLEEP,			g_sleep,					//睡眠
    DATA_TYPE_STEP, 			g_step_count,				//步数
	DATA_TYPE_STOPWATCH,		g_stopwatch,				//秒表	
	DATA_TYPE_STRESS,			g_stress,					//压力
	DATA_TYPE_TIMER,			g_timer,					//计时器
	DATA_TYPE_WEATHER,			g_weather,					//天气	
};

// 交换两个元素的值
static void swap(int* a, int* b) noexcept
{
    int t = *a;
    *a = *b;
    *b = t;
}

// 快速排序的分区函数
static  int partition(int arr[], int low, int high) {
    int pivot = arr[high];  // 选择最后一个元素作为基准
    int i = (low - 1);      // 小于基准的元素的索引

    for (int j = low; j <= high - 1; j++) {
        // 如果当前元素小于或等于基准
        if (arr[j] <= pivot) {
            i++;  // 增加小于基准的元素的索引
            swap(&arr[i], &arr[j]);
        }
    }
    swap(&arr[i + 1], &arr[high]);
    return (i + 1);
}

// 快速排序函数
static void quickSort(int arr[], int low, int high) {
    if (low < high) {
        // 分区索引，arr[p] 现在已排序
        int pi = partition(arr, low, high);

        // 分别对基准前后的元素进行排序
        quickSort(arr, low, pi - 1);
        quickSort(arr, pi + 1, high);
    }
}



DialDataSelectView::DialDataSelectView(PageManager* manager) :
	QwMenuView(manager),
	p_select_data_type_(nullptr),
	update_select_data_type_(this, &DialDataSelectView::update_select_data_type_from_model),
	p_set_select_data_type_(nullptr),
	on_set_select_data_type_(this, &DialDataSelectView::set_select_data_type_to_model),
	select_item_pos_(this , &DialDataSelectView::select_item_pos),
	dialDataInfo_{0},cacheIndex_{0},nowFocusIndex_{0}
{
    // 默认构造
}

DialDataSelectView::~DialDataSelectView()
{
    // 默认析构
}

void DialDataSelectView::setup()
{
	uint32_t nowEditDialType = get_dial_data_type_by_index(get_dial_edit_data_index());

	// 1.判断当前语言类型，加载对应语言表
	bool isChinese = get_language_type() == LANGUAGE_CHI;
    langTable_ = isChinese ? dialDataChineseInfo_ : dialDataEnglishInfo_;

	// 2.获取当前表盘可编辑数据个数，并获取当前表盘数据类型列表
	uint32_t dataTypeArry[DATA_TYPE_MAX];
	uint8_t editTypeNum = get_dial_edit_type_num();
	get_edit_data_type(dataTypeArry, NULL);

	// 3.1 根据当前表盘可编辑数据个数遍历
	for(int i = 0; i < editTypeNum; i++)
	{
		// 3.2 根据当前表盘数据类型列表，获取当前表盘数据类型在语言配置表中的索引,记录到cacheIndex_数组中
		for(int j = 0; j < DATA_TYPE_MAX; j++)
		{
			// 表盘配置表中的数据类型和语言配置表匹配
			if(langTable_[j].type == dataTypeArry[i])
			{
				// DIAL_LOG_D("@@@ dataType:%d name:%s map to index:%d\n", dataTypeArry[i], langTable_[j].name, j);
				cacheIndex_[i] = j;
				// 3.3适配当前正在编辑的数类型在语言配置表中的索引
				if(nowEditDialType == langTable_[j].type)
				{
					nowFocusIndex_ = j;
				}
				break;
			}
		}
	}

	// 4.对语言配置表中的数据索引进行排序，生成新菜单，方便后续的界面显示
	quickSort(cacheIndex_, 0, editTypeNum - 1);
	for(int i = 0; i < editTypeNum; i++)
	{
		// DIAL_LOG_D("@@@over quickSort-cacheIndex[%d]:%d\n", i, cacheIndex_[i]);
		dialDataInfo_[i].type = (const int)ITEM_TYPES::ITEM_RADIO;
		dialDataInfo_[i].name = langTable_[cacheIndex_[i]].name;
	}

    // 5. 设置当前的数据在新菜单的第几项
	for(int i = 0; i < DATA_TYPE_MAX; i++)
	{
		if(nowFocusIndex_ == cacheIndex_[i])
		{
			nowFocusIndex_ = i;
			break;
		}
	}
	// 1.创建menu菜单 获取数据初始类型	
    QwMenuView::show_menu(editTypeNum , nowFocusIndex_, _TM(g_edit_component));

}

void DialDataSelectView::quit()
{
	//The method is an intentionally-blank override.
}

void DialDataSelectView::handleTickEvent()
{
    QwMenuView::list_.handleTickEvent();
}

void DialDataSelectView::handleKeyEvent(uint8_t c)
{
    QwMenuView::list_.handleKeyEvent(c);
	if (c == KEY_CLK_BACK)
	{
		manager_->push("EditDialDataComponent");
	}else if (c == KEY_CLK_START)
	{
		uint8_t dataType =  get_select_app();
		// DIAL_LOG_D("@@dataType:%d\n", dataType);
		set_select_data_type_to_model(dataType);
		manager_->push("EditDialDataComponent");
	}
}

void DialDataSelectView::handleClickEvent(const ClickEvent& evt)
{
    QwMenuView::list_.handleClickEvent(evt);
}

void DialDataSelectView::handleDragEvent(const DragEvent& evt)
{
    QwMenuView::list_.handleDragEvent(evt);
}

void DialDataSelectView::handleGestureEvent(const GestureEvent& evt)
{
	QwMenuView::list_.handleGestureEvent(evt);
    if (evt.getType() == GestureEvent::GestureEventType::SWIPE_HORIZONTAL)
    {
        if (evt.getVelocity() > GESTURE_EXIT_ACCURACY)
        {
			manager_->push("EditDialDataComponent");
        }
    }
}

// Notification Callback function 设置数据到model
void DialDataSelectView::bind_set_select_data_type_to_model(Notification<uint8_t>* command)
{
	p_set_select_data_type_ = command;
}

void DialDataSelectView::set_select_data_type_to_model(uint8_t t1)
{
	if (p_set_select_data_type_ != nullptr)
	{	
		// 通过设置列表的第几个数据，遍历这个数据在语言表中哪个位置	
		for(int i = 0; i < DATA_TYPE_MAX; i++)
		{
		    if(cacheIndex_[t1] == i) break;
		}
		// DIAL_LOG_D("@@@langTable_[i].type:%d\n", langTable_[cacheIndex_[t1]].type);
		// langTable_[cacheIndex_[t1]].type 是在语言表中的数据类型
		p_set_select_data_type_->notify(langTable_[cacheIndex_[t1]].type);
		
	}
}

// ObserverDrawable Callback function 从model获取数据
void DialDataSelectView::set_select_data_type_to_view(ObserverDrawable<Drawable, uint8_t, 1>* observer)
{
	// if (observer != nullptr)
	// {
	// 	p_select_data_type_ = observer;
	// 	observer->bind_ctrl(0, QwMenuView::list_);
	// 	observer->bind_notify(update_select_data_type_);
	// }
}

void DialDataSelectView::update_select_data_type_from_model(Drawable* ctrl, Parameters<uint8_t>* data, int idx)
{
	//8 刷新ui界面
	// if (ctrl != nullptr && data != nullptr)
	// {
	// 	bool status  = true;
	// 	for(int i = 0; i < DATA_TYPE_MAX; i++)
	// 	{
	// 		i == data->get_val() ? status = true : status = false;
	// 		// DIAL_LOG_D("i:%d status:%d\n",i,status);
	// 		//item_update_show(i, ITEM_UPDATE_TYPES::ITEM_UPDATE_RIGHT_IMAGE,&status);
	// 	}
	// 	QwMenuView::list_.invalidate();
	// }
}

// custom function
// custom function
void DialDataSelectView::set_item_info(item_info_t* info, int index)
{
    // 2.设置menu菜单子信息
    if (info == nullptr || index >= DATA_TYPE_MAX)
    {
    	assert(false && "[DialDataSelectView:8001]set_item_info index is out");
		return;
    }

    info->item_index = index;
	info->type = (ITEM_TYPES)dialDataInfo_[info->item_index].type;
    memset(info->title, 0, sizeof(info->title));
    memcpy(info->title, _TM(dialDataInfo_[info->item_index].name), strlen(_TM(dialDataInfo_[info->item_index].name)));
    memset(info->subtitle, 0, sizeof(info->subtitle));

	// 3.判断是否选中
	if(nowFocusIndex_ == info->item_index)
	{
	    info->is_selected = true;
	}
	else
	{
	    info->is_selected = false;
	}

    info->item_text_info.text_align = 1;
    QwMenuView::list_.invalidate();
}

// 4.设置menu菜单子信息回调函数
void DialDataSelectView::set_item_notify(QwMenuItem* item, int index)
{
   item->set_select_pos_handle(select_item_pos_);
}

// 5.menu菜单子信息回调函数实现
void DialDataSelectView::select_item_pos(void* item, int x, int y)
{
	if (item == nullptr)
	{
		assert("[SportsMenuView:8001]custom_on_sports_select item is nullptr");
		return;
	}
	QwMenuItem* item_ = dynamic_cast<QwMenuItem*>((ItemBaseCtrl*)item);
	if (item_ == nullptr)
	{
		assert("[SportsMenuView:8001]custom_on_sports_select item_ not is QwMenuItem");
		return;
	}
    // 6.获取子菜单在当前menu中的index
    int dataType = (int)item_->get_user_data();
	if (dataType >= DATA_TYPE_HR && dataType <= DATA_TYPE_HR_PUSH)
    {
        if (x < 346 || x > 442)
        {
            return;
        }
    }
	// DIAL_LOG_D("@@@dataType:%d\n",dataType);
	// 7.设置选中index
	set_select_data_type_to_model(dataType);
    // 8.返回上一级
    manager_->push("EditDialDataComponent");


}
