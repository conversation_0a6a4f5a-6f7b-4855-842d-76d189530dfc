/************************************************************************
* 
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   dev_status_pb.c
@Time    :   2025/03/25 15:23:45
<AUTHOR>   lxin
* 
**************************************************************************/
#include "ble_cmd_response.h"
#include "ble_cmd_common.h"
#include "ble_nus_srv.h"

#include "pb.h"
#include "pb_encode.h"
#include "pb_decode.h"
#include "pb_decode_common.h"
#include "pb_encode_common.h"

#include "dev_status.pb.h"
#include "dev_status_pb.h"
#include "dev_status_data_inf.h"

#include "crc8.h"
#include "igs_global.h"
#include "gps_dec.h"
#include "test_impl.h"
#include "navi_location.h"
#include "point_name.h"
#include "view_data_srv_cb.h"

static DEV_STATUS_OPERATE_TYPE* dev_op_type = NULL;

//-------------------------------------------------------------------------------------------
// Function Name : ride_status_change_notice
// Purpose       : 码表状态变化时，通知APP
// Param[in]     : None
// Param[out]    : None
// Return type   : 
// Comment       : 2019-08-20
//-------------------------------------------------------------------------------------------
void ride_status_change_notice()
{
    static  DEV_CYCLING_STATUS status =  DEV_CYCLING_STATUS_DEV_CYCLING_STATUS_FREE;

    if ((!view_api_fit_decode_get_flag()) && 
        (status != (DEV_CYCLING_STATUS)g_device_get_show_savedata_status()))
    {
        status = (DEV_CYCLING_STATUS)g_device_get_show_savedata_status();
        ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_DEV_STATUS, 0,
            DEV_STATUS_OPERATE_TYPE_enum_DEV_STATUS_OPERATE_TYPE_SEND, 0xff, status);
    }
}

static bool position_info_repeated_submsg_encode(pb_ostream_t *stream, const pb_field_t *field, void * const *arg)
{
    uint8_t status = false;
    dev_position_point position_point;
    navi_location_data_t *navi_location = get_navi_location_data();
    uint16_t num = navi_location->num;

    position_point.has_latitude = true;
    position_point.has_longitude = true;
    
    char *point_name = NULL;
    for(uint8_t i = 0; i < num; i ++)
    {
        position_point.latitude = navi_location->location[i].lat;
        position_point.longitude = navi_location->location[i].lon;

        point_name = point_name_info_get_by_index(i);
        if(point_name == NULL)
        {
            point_name = "";
        }
        
        position_point.pointname.arg = point_name;
        position_point.pointname.funcs.encode = encode_string;
        position_point.point_index = i + 1;
        
        status = pb_encode_tag_for_field(stream, field);
        status &= pb_encode_submessage(stream, dev_position_point_fields, &position_point);
    }

    return status;
}

//-------------------------------------------------------------------------------------------
// Function Name : dev_status_send
// Purpose       : 向APP发送设备状态
// Param[in]     : None
// Param[out]    : None
// Return type   : 
// Comment       : 2019-08-16
//-------------------------------------------------------------------------------------------
static void dev_status_send()
{  
    uint8_t pb_crc = 0;
    uint8_t *data = NULL;
    uint16_t *length = NULL;	
    dev_status_msg dev_status_message;

    memset(&dev_status_message, 0, sizeof(dev_status_msg));
    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);
    
    //参数赋值
    dev_status_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_DEV_STATUS;
    dev_status_message.op_type = DEV_STATUS_OPERATE_TYPE_enum_DEV_STATUS_OPERATE_TYPE_SEND;
    
    dev_status_message.has_dev_cycling_status_msg = true;

	//骑行信息：状态、开始时间
    dev_status_message.dev_cycling_status_msg.has_dev_cycling_status = true;
    if (LOCAL_STATUS_PAUSE_AUTO == g_device_get_show_savedata_status() || LOCAL_STATUS_PAUSE_MANUAL == g_device_get_show_savedata_status())
    {
        dev_status_message.dev_cycling_status_msg.dev_cycling_status = DEV_CYCLING_STATUS_DEV_CYCLING_STATUS_PAUSE;    //与DEV_CYCLING_STATUS结构体对应
    }
    else
    {
        dev_status_message.dev_cycling_status_msg.dev_cycling_status = (DEV_CYCLING_STATUS)g_device_get_show_savedata_status();    //与DEV_CYCLING_STATUS结构体对应
    }
    
    dev_status_message.dev_cycling_status_msg.has_cycling_start_time = true;
    if (g_device_get_show_savedata_status() != LOCAL_STATUS_FREE)
    {
        dev_status_message.dev_cycling_status_msg.cycling_start_time = activity_start_time_get();              //相对于1989年的时间
    }
    else
    {
        dev_status_message.dev_cycling_status_msg.cycling_start_time = 0;
    }

	//GPS信息
    dev_status_message.has_dev_gps_msg = true;
    dev_status_message.dev_gps_msg.has_latitude = true;
    dev_status_message.dev_gps_msg.has_longitude = true;
    if (g_device_get_show_gps_status() != 0)       //与结构体DEVICE_GPS_STATUS对应
    {
        gps_position_get(&dev_status_message.dev_gps_msg.longitude, &dev_status_message.dev_gps_msg.latitude);
    }
    else
    {
        dev_status_message.dev_gps_msg.longitude = 0;
        dev_status_message.dev_gps_msg.latitude = 0;
    }

    test_impl_t *p_test_impl = test_impl_get();
    if (test_item_check(p_test_impl, enumTEST_NAVI_SIM) == true || test_item_check(p_test_impl, enumTEST_NAVI_LOOP) == true)
    {
        test_navi_upload_gps_position_to_app(&dev_status_message.dev_gps_msg.longitude, &dev_status_message.dev_gps_msg.latitude);
    }

    //WIFI状态
    dev_status_message.has_wifi_status = false;

	//导航状态
    dev_status_message.has_navi_status = false;
    dev_status_message.navi_status = (DEV_NAVI_STATUS)0;//is_route_navi();

	//骑行数据   
    if (g_device_get_show_savedata_status() != LOCAL_STATUS_FREE)
    {
        dev_status_message.has_rt_data_msg = true;
        dev_status_message.rt_data_msg.has_real_time_hrm= true;
        dev_status_message.rt_data_msg.real_time_hrm = g_device_get_hrmdata_hrm();
        dev_status_message.rt_data_msg.has_avg_hrm = true;
        dev_status_message.rt_data_msg.avg_hrm = g_device_get_hrmdata_hrm_avg();
        dev_status_message.rt_data_msg.has_real_time_speed = true;
        dev_status_message.rt_data_msg.real_time_speed = g_device_get_spdata_spd();
        dev_status_message.rt_data_msg.has_avg_speed= true;
        dev_status_message.rt_data_msg.avg_speed = g_device_get_spdata_spd_avg();
        dev_status_message.rt_data_msg.has_avg_rise = true;
        dev_status_message.rt_data_msg.avg_rise = g_device_get_altdata_avg_pos_ver_spd();
        dev_status_message.rt_data_msg.has_cur_height= true;
        dev_status_message.rt_data_msg.cur_height = g_device_get_altdata_alt();
        dev_status_message.rt_data_msg.has_total_height= true;
        dev_status_message.rt_data_msg.total_height = g_device_get_altdata_total_ascent();
        dev_status_message.rt_data_msg.has_cur_slope = true;
        dev_status_message.rt_data_msg.cur_slope = g_device_get_altdata_total_grade();
        dev_status_message.rt_data_msg.has_real_time_cad = true;
        dev_status_message.rt_data_msg.real_time_cad = g_device_get_cadata_cad();
        dev_status_message.rt_data_msg.has_real_time_power = true; 
        dev_status_message.rt_data_msg.real_time_power = g_device_get_powerdata_power();
        dev_status_message.rt_data_msg.has_riding_distance = true;
        dev_status_message.rt_data_msg.riding_distance = g_device_get_distdata_dist_trip();
        dev_status_message.rt_data_msg.has_riding_time = true;
        dev_status_message.rt_data_msg.riding_time = g_device_get_timedata_total_moving();
        dev_status_message.rt_data_msg.has_max_speed = true;
        dev_status_message.rt_data_msg.max_speed = g_device_get_spdata_spd_max();
        dev_status_message.rt_data_msg.has_max_hrm = true;
        dev_status_message.rt_data_msg.max_hrm= g_device_get_hrmdata_hrm_max();
        dev_status_message.rt_data_msg.has_avg_cad = true;
        dev_status_message.rt_data_msg.avg_cad = g_device_get_cadata_cad_avg();
        dev_status_message.rt_data_msg.has_max_cad = true;
        dev_status_message.rt_data_msg.max_cad = g_device_get_cadata_cad_max();
        dev_status_message.rt_data_msg.has_avg_power = true;
        dev_status_message.rt_data_msg.avg_power = g_device_get_powerdata_power_avg();
        dev_status_message.rt_data_msg.has_max_power = true;
        dev_status_message.rt_data_msg.max_power = g_device_get_powerdata_power_max();
        dev_status_message.rt_data_msg.has_course = true;
        dev_status_message.rt_data_msg.course = g_device_get_course();
    }

    if(navi_location_num_get())
    {
        dev_status_message.has_position_point_manager = true;
        dev_status_message.position_point_manager.has_position_point_num = true;
        dev_status_message.position_point_manager.position_point_num = navi_location_num_get();
        dev_status_message.position_point_manager.position_info.funcs.encode = position_info_repeated_submsg_encode;
    }

    //编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    //编码
    pb_encode(&encode_stream, dev_status_msg_fields, &dev_status_message);
    
   *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch0( );
    
    //对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);
    
    //命令协议 发送通道2
    ble_cmd_end_tx(dev_status_message.service_type, 0, dev_status_message.op_type, 0, encode_stream.bytes_written, pb_crc);
}

//-------------------------------------------------------------------------------------------
// Function Name : dev_status_handle
// Purpose       : 设备状态通信状态处理接口函数
// Param[in]     : uint8_t *buf  
// Param[out]    : None
// Return type   : 
// Comment       : 2019-08-16
//-------------------------------------------------------------------------------------------
void dev_status_handle(uint8_t *buf)
{
    ble_status_cmd_st *ble_status_cmd_s = (ble_status_cmd_st *)buf;
    uint8_t status = ble_status_cmd_s->status;

    if (enmuDATA_ERR_STATUS == status)
    {
        switch (ble_status_cmd_s->sub_op_type)
        {
            case DEV_STATUS_OPERATE_TYPE_enum_DEV_STATUS_OPERATE_TYPE_SEND:
                dev_status_send();
                break;
            default:
                break;                   
        }
    }
}        

bool position_info_message_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    dev_position_point position_point = {0};
    navi_location_data_t* ptr = (navi_location_data_t*) (*arg);
    uint8_t point_name[POINT_NAME_MAX] = {0};
    uint8_t index = 0;

    position_point.pointname.arg = point_name;
    position_point.pointname.funcs.decode = decode_string;

    if (!pb_decode(stream, dev_position_point_fields, &position_point))
    {
        return false;
    }
    //rt_kprintf("msg_decode index = %d, op =%d\n", position_point.point_index - 1, *dev_op_type);
    if (position_point.has_point_index)
    {
        index = position_point.point_index - 1;
        if (*dev_op_type == DEV_STATUS_OPERATE_TYPE_enum_DEV_STATUS_OPERATE_TYPE_ADD_POSITION_POINT)
        {
            if (position_point.has_latitude && position_point.has_longitude) //add point
            {
                if (ptr->num < MAX_NAVI_LOCATION)
                {
                    ptr->location[index].lat = position_point.latitude;
                    ptr->location[index].lon = position_point.longitude;
                    ptr->location[index].name_num = index;
                    point_name_info_set_by_index(index, point_name, strlen(point_name) + 1);
                }
            }
        }
        else if (*dev_op_type == DEV_STATUS_OPERATE_TYPE_enum_DEV_STATUS_OPERATE_TYPE_DELETE_POSITION_POINT)
        {
            ptr->location[index].lat = 0xFFFFFFFF;
            ptr->location[index].lon = 0xFFFFFFFF;
            ptr->location[index].name_num = 0xFFFFFFFF;
            point_name_info_delete_by_index(index);
        }
        else if (*dev_op_type == DEV_STATUS_OPERATE_TYPE_enum_DEV_STATUS_OPERATE_TYPE_MODIFY_POSITION_POINT) //modify point
        {
            //rt_kprintf("modify point index = %d\n", index);
            if (position_point.has_latitude && position_point.has_longitude)
            {
                //rt_kprintf("modify point old = [%d,%d]]\n", ptr->location[index].lat, ptr->location[index].lon);
                ptr->location[index].lat = position_point.latitude;
                ptr->location[index].lon = position_point.longitude;
                ptr->location[index].name_num = index;
                //rt_kprintf("modify point new = [%d,%d]]\n", ptr->location[index].lat, ptr->location[index].lon);
                point_name_info_set_by_index(index, point_name, strlen(point_name) + 1);
            }
        }
    }

    return true;
}

static void navi_location_data_delete_resort(navi_location_data_t *navi_location)
{
    uint16_t i = 0, j = 0;
    uint32_t name_num_index = 0;

    navi_location_data_t tmp_location;
    memset(&tmp_location, 0, sizeof(navi_location_data_t));

    for(i = 0; i < navi_location->num; i ++)
    {
        name_num_index = navi_location->location[i].name_num;
        if(name_num_index != 0xFFFFFFFF)
        {
            memcpy(&tmp_location.location[j], &navi_location->location[i], sizeof(navi_location_point_t));
            tmp_location.location[j].name_num = j + 1;
            j ++;
        }
    }
    memcpy(navi_location, &tmp_location, sizeof(navi_location_data_t));
    navi_location->num = j;
}

//-------------------------------------------------------------------------------------------
// Function Name : dev_status_decode
// Purpose       : 设备状态PB解码接口函数
// Param[in]     : uint8_t * pb_buffer     
//                 uint16_t buffer_length  
//                 END_TYPE end_type       
// Param[out]    : None
// Return type   : 
// Comment       : 2019-08-16
//-------------------------------------------------------------------------------------------
void dev_status_decode(uint8_t * pb_buffer, uint16_t buffer_length, END_TYPE end_type)
{  
    uint8_t status = false;
    dev_status_msg dev_status_message;
    navi_location_data_t navi_location_data;    

    memset(&dev_status_message, 0, sizeof(dev_status_msg));
    navi_location_data_t *navi_location = get_navi_location_data();
    memcpy(&navi_location_data, navi_location, sizeof(navi_location_data_t));

    pb_istream_t decode_stream = pb_istream_from_buffer(pb_buffer, buffer_length);

    dev_status_message.position_point_manager.position_info.arg = &navi_location_data;
    dev_status_message.position_point_manager.position_info.funcs.decode = position_info_message_decode;

    dev_op_type = &dev_status_message.op_type;

    status = pb_decode(&decode_stream, dev_status_msg_fields, &dev_status_message);

    //rt_kprintf("decode %d, %d\n", status, dev_status_message.op_type);
    if (true == status)
    {
        switch (dev_status_message.op_type)
        {
        case DEV_STATUS_OPERATE_TYPE_enum_DEV_STATUS_OPERATE_TYPE_GET:
            dev_status_send();
            break;
        case DEV_STATUS_OPERATE_TYPE_enum_DEV_STATUS_OPERATE_TYPE_ADD_POSITION_POINT:
            //navi_location_data.num = dev_status_message.position_point_manager.position_point_num;
            if (navi_location->num < MAX_NAVI_LOCATION)
            {
                navi_location_data.num += 1;
                memcpy(navi_location, &navi_location_data, sizeof(navi_location_data_t));
                navi_location_save();
                point_name_file_store();
                ble_cmd_success_status_tx(dev_status_message.service_type, 0, dev_status_message.op_type, 0);
                gui_command_submit(enumGUICMD_RESET_SCREEN, NULL);
            }
            else
            {
                ble_cmd_status_tx(dev_status_message.service_type, 0, dev_status_message.op_type, 0, enmuNUM_FULL);
            }
            break;
        case DEV_STATUS_OPERATE_TYPE_enum_DEV_STATUS_OPERATE_TYPE_DELETE_POSITION_POINT:
            navi_location_data_delete_resort(&navi_location_data);
            memcpy(navi_location, &navi_location_data, sizeof(navi_location_data_t));
            navi_location_save();
            point_name_file_store();
            ble_cmd_success_status_tx(dev_status_message.service_type, 0, dev_status_message.op_type, 0);
            gui_command_submit(enumGUICMD_RESET_SCREEN, NULL);
            break;
        case DEV_STATUS_OPERATE_TYPE_enum_DEV_STATUS_OPERATE_TYPE_MODIFY_POSITION_POINT:
            memcpy(navi_location, &navi_location_data, sizeof(navi_location_data_t));
            navi_location_save();
            point_name_file_store();
            ble_cmd_success_status_tx(dev_status_message.service_type, 0, dev_status_message.op_type, 0);
            gui_command_submit(enumGUICMD_RESET_SCREEN, NULL);
            break;
        default:
            ble_cmd_err_status_tx(dev_status_message.service_type, 0, dev_status_message.op_type, 0);
            break;
        }
    }
    else
    {
        ble_cmd_err_status_tx(dev_status_message.service_type, 0, dev_status_message.op_type, 0);
    }
}
