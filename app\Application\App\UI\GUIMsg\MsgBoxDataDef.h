/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   MsgBoxService.h
@Time    :   2024/12/13 16:08:23
*
**************************************************************************/

#ifndef __MSG_BOX_DATA_DEF_H
#define __MSG_BOX_DATA_DEF_H

#include "stdint.h"
#include "stdbool.h"

#if defined(__cplusplus)
extern "C" {
#endif

/**
 * @brief MsgTipSelect 的显示文本类型
 */
typedef enum _MSG_TIP_SELECT_TYPE
{
    MSG_TIP_SELECT_DELETE_FIT = 0,      // 删除fit历史记录
    MSG_TIP_SELECT_RESET,               // 重置参数
    MSG_TIP_SELECT_FACTORY,             // 恢复出厂
    MSG_TIP_SELECT_WHEEL_PERIMETER,     // 轮径更新
    MSG_TIP_SELECT_FTP_TRAIN_COURSE,    // FTP训练课程
    MSG_TIP_SELECT_SENSOR_CONNECT_FEC_POWER_TIP, // 传感器连接提醒，骑行台和功率计
    MSG_TIP_SELECT_FTP_TEST_REMINDER,   // FTP测试提醒
    MSG_TIP_SELECT_FTP_TEST_FAILED, // FTP测试失败
    MSG_TIP_UNBIND,                    // 解绑设备
    MSG_TIP_FACTORY_RESTORE_ID_CHANGE,           // 恢复出厂提示
}MSG_TIP_SELECT_TYPE;

typedef enum _MSG_LOADING_TYPE
{
    MSG_LOADING_TYPE_DECODE,            // 加载文件
    MSG_LOADING_TYPE_FACTORY,           // 恢复出厂
    MSG_LOADING_TYPE_BPWR_CALIB,        // 功率校准
    MSG_LOADING_TYPE_ALIPAY_BINDING,    // 支付宝绑定
    MSG_LOADING_TYPE_ALT_CALIB,         // 高度校准
}MSG_LOADING_TYPE;

typedef enum _MSG_ALIPAY_TYPE
{
    MSG_ALIPAY_TYPE_BINDING_SUCCESS,        // 支付宝绑定成功
    MSG_ALIPAY_TYPE_BINDING_FAILED,          //支付宝绑定失败
    MSG_ALIPAY_TYPE_UNBINDING_SUCCESS,       //解绑成功
    MSG_ALIPAY_TYPE_WRONG_PASSWORD,          //支付宝密码错误 
    MSG_ALIPAY_TYPE_INIT_ENV_CHECK,          //初始环境检查
    MSG_ALIPAY_TYPE_BINDING_CODE_FAILED,     //绑定码获取失败
    // MSG_ALIPAY_TYPE_BINDING,                 //支付宝开始绑定
    MSG_ALIPAY_TYPE_BINDING_CODE_EXIT_REMIND, //绑定码退出提醒，未完成绑定
    MSG_ALIPAY_TYPE_PAYMENT_SUCCESS,         //付款结果-成功
    MSG_ALIPAY_TYPE_PAYMENT_FAILED_PAYMENT_CODE_CLOSED, //付款结果-失败-付款码关闭
    MSG_ALIPAY_TYPE_PAYMENT_RESULT_FAILED_UNBIND, //付款结果-失败-已经解绑
    MSG_ALIPAY_TYPE_PAYMENT_FAILED_OTHER_ERROR, //付款结果-失败-其他错误
    MSG_ALIPAY_TYPE_FIRST_ENTER_TRANSIT_CODE, //第一次进入乘车码
    MSG_ALIPAY_TYPE_TRANSIT_FAILED_UPDATE_TRY_AGAIN, //乘车码生码失败-更新后再尝试
    MSG_ALIPAY_TYPE_TRANSIT_FAILED_DETAIL, //乘车码生码失败-详情
    MSG_ALIPAY_TYPE_TRANSIT_FAILED_ENCODE_LIMIT, //乘车码生码失败-生码受限
    MSG_ALIPAY_TYPE_TRANSIT_FAILED_CONTACT_MANUFACTURER, //乘车码生码失败-联系厂商
    MSG_ALIPAY_TYPE_TRANSIT_FAILED_PAYMENT_CODE_NOT_SUPPORTED, //乘车码生码失败-乘车码不支持
    MSG_ALIPAY_TYPE_TRANSIT_FAILED_UNBIND, //乘车码生码失败-已经解绑
    MSG_ALIPAY_TYPE_TRANSIT_FAILED_SERVICE_ERROR, //乘车码生码失败-服务端报错
    MSG_ALIPAY_TYPE_LIST_DATA_GET_FAILED_CONTACT_MANUFACTURER, //乘车码列表数据获取失败-联系厂商
    MSG_ALIPAY_TYPE_LIST_DATA_GET_FAILED_TOO_MANY, //乘车码列表数据获取失败-数量太多
    MSG_ALIPAY_TYPE_LIST_DATA_GET_FAILED_UNBIND, //乘车码列表数据获取失败-已经解绑
    MSG_ALIPAY_TYPE_SETTING_UNBIND_CONFIRM, //设置-解绑确认
    MSG_ALIPAY_TYPE_SETTING_UNBIND_REMIND, //设置-解绑后提醒
}MSG_ALIPAY_TYPE;
/**
 * @brief MsgTipSelect 的弹框配置
 */
typedef struct _msg_tip_select_t
{
    MSG_TIP_SELECT_TYPE type;           // 显示文本类型
    union
    {
        uint32_t fit_idx;              // fit历史记录索引(MSG_TIP_SELECT_DELETE_FIT)
        uint32_t wheel_perimeter;      // 轮径(MSG_TIP_SELECT_WHEEL_PERIMETER)
    };
}msg_tip_select_t;

typedef struct _msg_loading_t
{
    MSG_LOADING_TYPE type;           // 显示文本类型
    const char *page_name;                //  跳转页面名称
}msg_loading_t;

#if defined(__cplusplus)
}
#endif

#endif //__MSG_BOX_DATA_DEF_H
