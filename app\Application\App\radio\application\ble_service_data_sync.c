/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ble_service_data_sync.c
@Time    :   2025/04/10 11:00:35
*
**************************************************************************/

#include "ble_service_data_sync.h"
#include "rtthread.h"
#include "subscribe_service.h"

#include "ble_lb55x_hub.h"

#include "qw_log.h"

#define BLE_DATA_SYNC_LVL   LOG_LVL_INFO
#define BLE_DATA_SYNC_TAG   "BLE_DATA_SYNC"

#if (BLE_DATA_SYNC_LVL >= LOG_LVL_DBG)
    #define BLE_DATA_SYNC_LOG_D(fmt, ...)       QW_LOG_D(BLE_DATA_SYNC_TAG, fmt, ##__VA_ARGS__)
#else
    #define BLE_DATA_SYNC_LOG_D(fmt, ...)
#endif

#if (BLE_DATA_SYNC_LVL >= LOG_LVL_INFO)
    #define BLE_DATA_SYNC_LOG_I(fmt, ...)       QW_LOG_I(BLE_DATA_SYNC_TAG, fmt, ##__VA_ARGS__)
#else
    #define BLE_DATA_SYNC_LOG_I(fmt, ...)
#endif

#if (BLE_DATA_SYNC_LVL >= LOG_LVL_WARNING)
    #define BLE_DATA_SYNC_LOG_W(fmt, ...)       QW_LOG_W(BLE_DATA_SYNC_TAG, fmt, ##__VA_ARGS__)
#else
    #define BLE_DATA_SYNC_LOG_W(fmt, ...)
#endif

#if (BLE_DATA_SYNC_LVL >= LOG_LVL_ERROR)
    #define BLE_DATA_SYNC_LOG_E(fmt, ...)       QW_LOG_E(BLE_DATA_SYNC_TAG, fmt, ##__VA_ARGS__)
#else
    #define BLE_DATA_SYNC_LOG_E(fmt, ...)
#endif

#ifdef BF0_LCPU
#include "algo_service_component_common.h"
#include "qw_time_util.h"

#define WEATHER_DATA_TIMEOUT                (43200) // 天气数据超时时间: 12h   60 * 60 * 12
#define WEATHER_DATA_SYNC_INTERVAL          (7200)  // 天气数据同步时间间隔: 2h   60 * 60 * 2
#define WEATHER_DATA_SYNC_REQUEST_INTERVAL  (1800)  // 天气数据同步失败后，下一次请求时间间隔: 30min  60 * 30
#define BLE_DATA_SYNC_DELAY_TIME            (5000)     // 延迟同步时间: 5s
uint8_t ble_health_data_sync_minute = 0;  //健康数据同步分钟数
time_t ble_weather_data_sync_time = 0;  //前一次天气数据的同步时间  //TODO: 需从大核获取上次同步时间来解决中途重启问题
time_t ble_weather_data_sync_request_time = 0;  //上一次天气数据同步请求时间
rt_timer_t ble_data_sync_delay_timer = NULL;

// 跨核健康数据同步请求
static int32_t health_data_sync_request(void)
{
    int32_t ret = 0;
    ret = ble_app_lcpu_event_notify(HEALTH_DATA_SYNC_REQUEST, BLE_APP_FUNC_NO_RSP, 0, NULL);

    return ret;
}

// 跨核天气数据同步请求
// flag 1: 同时需要同步时间, 2: 仅同步天气
static int32_t weather_data_sync_request(uint8_t flag)
{
    int32_t ret = 0;
    uint8_t data = flag;
    ret = ble_app_lcpu_event_notify(WEATHER_DATA_SYNC_REQUEST, BLE_APP_FUNC_NO_RSP, sizeof(data), &data);

    return ret;
}

static void ble_data_sync_1min_in_callback(const void *in, uint32_t len)
{
    algo_timer_event_pub_t *sub_data = (algo_timer_event_pub_t *)in;
    time_t real_time = 0;
    uint8_t minute_val = 0;

    // 检查是否达到健康同步的分钟数
    real_time = get_sec_from_rtc();
    minute_val = (uint8_t)((real_time / 60) % 60);

    if (minute_val == ble_health_data_sync_minute)
    {
        health_data_sync_request();
    }
    BLE_DATA_SYNC_LOG_D("get 1 min callback, minute_val:%d, sync_minute:%d", minute_val, ble_health_data_sync_minute);
    // 检查与上一次天气数据同步时间，或上一次天气数据同步请求时间，之间的时间间隔是否达到需要的天气同步时间间隔（2h）
    if ((ble_weather_data_sync_time != 0) || (ble_weather_data_sync_request_time != 0))
    {
        if ((real_time - ble_weather_data_sync_time >= WEATHER_DATA_SYNC_INTERVAL) &&
            (real_time - ble_weather_data_sync_request_time >= WEATHER_DATA_SYNC_REQUEST_INTERVAL))
        {
            weather_data_sync_request(0);
            ble_weather_data_sync_request_time = real_time;
        }
    }

    // BLE_DATA_SYNC_LOG_D("get 1 min callback, sub_data->event_type:%d, sub_data->event_data.minute_val:%d, \
    //                     sub_data->event_data.hour_val:%d, sub_data->event_data.day_val:%d", sub_data->event_type, \
    //                     sub_data->event_data.minute_val, sub_data->event_data.hour_val, sub_data->event_data.day_val);
    BLE_DATA_SYNC_LOG_D("get 1 min callback, real_time:%ld, sync_time:%ld, request_time:%ld, interval:%d", \
                        real_time, ble_weather_data_sync_time, ble_weather_data_sync_request_time, WEATHER_DATA_SYNC_INTERVAL);
}

static void ble_data_sync_delay_callback(void *parameter)
{
    BLE_DATA_SYNC_LOG_I("ble_data_sync_delay_callback!!!");

    time_t real_time = get_sec_from_rtc();

    if ((ble_weather_data_sync_time == 0) ||
        (real_time - ble_weather_data_sync_time >= WEATHER_DATA_SYNC_INTERVAL))
    {
        weather_data_sync_request(BLE_DATA_SYNC_BOTH_TIME_WEATHER);
        ble_weather_data_sync_request_time = real_time;
    }
    else
    {
        weather_data_sync_request(BLE_DATA_SYNC_ONLY_TIME);
    }
}

//订阅1min事件，用以提醒ble同步信息
static int32_t ble_data_sync_timer_open(void)
{
    optional_config_t config = {.sampling_rate = 1};
    int32_t ret = 0;

    ble_data_sync_delay_timer = rt_timer_create("ble_data_sync_delay_timer", ble_data_sync_delay_callback, RT_NULL, BLE_DATA_SYNC_DELAY_TIME, RT_TIMER_FLAG_ONE_SHOT | RT_TIMER_FLAG_SOFT_TIMER);

    config.sampling_rate = 0;
    ret = qw_dataserver_subscribe_id(DATA_ID_EVENT_ONE_MIN_REACH, ble_data_sync_1min_in_callback,&config);
    if (ret != 0)
    {
        BLE_DATA_SYNC_LOG_E("ble_weather_data_sync_timer_open error ret:%d", ret);
        return ret;
    }
    return ret;
}

static int32_t ble_data_sync_timer_close(void)
{
    int32_t ret = 0;
    ret = qw_dataserver_unsubscribe_id(DATA_ID_EVENT_ONE_MIN_REACH, ble_data_sync_1min_in_callback);

    if (ret != 0)
    {
        BLE_DATA_SYNC_LOG_E("ble_weather_data_sync_timer_close error ret:%d", ret);
        return ret;
    }
    return ret;
}

INIT_ENV_EXPORT(ble_data_sync_timer_open);

/************************************************************************
 *@function:void ble_weather_update_sync_time(void)
 *@brief:同步天气数据更新时间
 *@param: null
 *@return:null
*************************************************************************/
void ble_weather_update_sync_time(void)
{
    ble_weather_data_sync_time = get_sec_from_rtc();
    ble_weather_data_sync_request_time = ble_weather_data_sync_time;
}

/************************************************************************
 *@function:void ble_weather_update_sync_request_time(void)
 *@brief:同步天气数据更新请求时间
 *@param: null
 *@return:null
*************************************************************************/
void ble_weather_update_sync_request_time(void)
{
    ble_weather_data_sync_request_time = get_sec_from_rtc();
}

/************************************************************************
 *@function:void ble_health_update_sync_minute(uint8_t minute_val)
 *@brief:同步健康数据同步分钟数
 *@param: null
 *@return:null
*************************************************************************/
void ble_health_update_sync_minute(uint8_t minute_val)
{
    ble_health_data_sync_minute = minute_val;
}

/************************************************************************
 *@function:void process_pb_connect_event(uint8_t state)
 *@brief:ble连接状态检查
 *@param: uint8_t state: 连接状态
 *@return:null
*************************************************************************/
void process_pb_connect_event(uint8_t state)
{
    BLE_DATA_SYNC_LOG_D("process_pb_connect_event state:%d", state);

    if (state == BLE_CONNECT_STATE_PB_PROTOCOL_READY)
    {
        rt_timer_start(ble_data_sync_delay_timer);
    }
    else
    {

    }
}

static void msh_ble_service_send_msg_to_app(int32_t argc, char **argv)
{
    BLE_DATA_SYNC_LOG_D("CMD: send ble msg to app!");

    if (0 == strcmp(argv[1], "health_request"))
    {
        health_data_sync_request();
    }
    else if (0 == strcmp(argv[1], "weather_request"))
    {
        weather_data_sync_request(BLE_DATA_SYNC_ONLY_WEATHER);
    }
}
MSH_CMD_EXPORT_ALIAS(msh_ble_service_send_msg_to_app, ble_l2h, ble_l2h: send ble msg to app);

#elif defined(BF0_HCPU)

/************************************************************************
 *@function:int32_t health_data_sync_time_send(uint8_t minute_val);
 *@brief:向小核发送健康数据同步时间
 *@param: minute_val: 健康数据同步分钟数
 *@return:0: 成功，其他：失败
*************************************************************************/
int32_t health_data_sync_time_send(uint8_t minute_val)
{
    //TODO: 当ble配对后/或上电后，需将随机生成的健康数据同步时间发送给小核，小核每隔该时间段，需上报小核请求健康数据同步
    int32_t ret = 0;
    ret = ble_app_hcpu_event_notify(HEALTH_DATA_SYNC_MINUTE_SET, BLE_APP_FUNC_NO_RSP, sizeof(minute_val), &minute_val);
    return ret;
}

/************************************************************************
 *@function:int32_t weather_data_sync_succ_send(void);
 *@brief:向小核发送天气数据同步成功
 *@param: null
 *@return:0: 成功，其他：失败
*************************************************************************/
int32_t weather_data_sync_succ_send(void)
{
    int32_t ret = 0;
    ret = ble_app_hcpu_event_notify(WEATHER_DATA_SYNC_SUCCESS, BLE_APP_FUNC_NO_RSP, 0, NULL);
    return ret;
}

/************************************************************************
 *@function:int32_t weather_data_sync_request_succ_send(void);
 *@brief:向小核发送天气数据已向app请求更新
 *@param: null
 *@return:0: 成功，其他：失败
*************************************************************************/
int32_t weather_data_sync_request_succ_send(void)
{
    int32_t ret = 0;
    ret = ble_app_hcpu_event_notify(WEATHER_DATA_SYNC_REQUEST_SUCCESS, BLE_APP_FUNC_NO_RSP, 0, NULL);
    return ret;
}

static uint8_t ble_connect_state_mask = 0; // 连接状态掩码 connecting | connected | pb_protocol_ready | goodix_ready | disconnecting
/************************************************************************
 *@function:int32_t ble_connect_state_notify(BLE_CONNECT_STATE_E state);
 *@brief:向小核发送ble连接状态更新
 *@param: state see BLE_CONNECT_STATE_E
 *@return:0: 成功，其他：失败
*************************************************************************/
int32_t ble_connect_state_notify(BLE_CONNECT_STATE_E state)
{
    int32_t ret = 0;
    if (state >= BLE_CONNECT_STATE_MAX)
    {
        BLE_DATA_SYNC_LOG_E("ble_connect_state:%d out of range", state);
        return -1;
    }

    if (BLE_CONNECT_STATE_DISCONNECTED == state || BLE_CONNECT_STATE_IDLE == state)
    {
        ble_connect_state_mask = 0;
    }
    else
    {
        ble_connect_state_mask |= (1 << (state - 1));
    }

    ret = ble_app_hcpu_event_notify(BLE_APP_CONNECT_STATE, BLE_APP_FUNC_NO_RSP, sizeof(state), &state);
    return ret;
}

/************************************************************************
 *@function:uint8_t ble_connect_state_get(void)
 *@brief:获取连接状态掩码
 *@return:连接状态掩码
*************************************************************************/
uint8_t ble_connect_state_get(void)
{
    return ble_connect_state_mask;
}

static void msh_ble_service_send_msg_to_lcpu(int32_t argc, char **argv)
{
    BLE_DATA_SYNC_LOG_D("CMD: send ble msg to lcpu!");

    if (0 == strcmp(argv[1], "health_time_set"))
    {
        uint8_t minute_val = atoi(argv[2]);
        health_data_sync_time_send(minute_val);
    }
    else if (0 == strcmp(argv[1], "weather_succ_set"))
    {
        weather_data_sync_succ_send();
    }
}
MSH_CMD_EXPORT_ALIAS(msh_ble_service_send_msg_to_lcpu, ble_h2l, ble_h2l: send ble msg to lcpu);

#endif
