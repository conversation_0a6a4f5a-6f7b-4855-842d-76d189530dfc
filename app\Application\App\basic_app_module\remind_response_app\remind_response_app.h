/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   remind_response_app.h
@Time    :   2025/02/07 11:09:51
*
**************************************************************************/

#ifndef __REMIND_RESPONSE_APP_H__
#define __REMIND_RESPONSE_APP_H__

#include <stdint.h>
#include <stdbool.h>
#include "GUIMsg/MsgBoxService.h"
#ifndef SIMULATOR
#include "beep.h"
#include "motor.h"
#include "rtdef.h"
//#include "multicore_vibration.h"
#endif   // SIMULATOR
#include "qw_log.h"

#ifdef __cplusplus
extern "C" {
#endif

#define REMIND_RESPONSE_LVL               LOG_LVL_INFO
#define REMIND_RESPONSE_TAG               "REMIND"

#if (REMIND_RESPONSE_LVL >= LOG_LVL_DBG)
    #define REMIND_RESPONSE_LOG_D(...)        QW_LOG_D(REMIND_RESPONSE_TAG, __VA_ARGS__)
#else
    #define REMIND_RESPONSE_LOG_D(...)
#endif

#if (REMIND_RESPONSE_LVL >= LOG_LVL_INFO)
    #define REMIND_RESPONSE_LOG_I(...)        QW_LOG_I(REMIND_RESPONSE_TAG, __VA_ARGS__)
#else
    #define REMIND_RESPONSE_LOG_I(...)
#endif

#if (REMIND_RESPONSE_LVL >= LOG_LVL_WARNING)
    #define REMIND_RESPONSE_LOG_W(...)        QW_LOG_W(REMIND_RESPONSE_TAG, __VA_ARGS__)
#else
    #define REMIND_RESPONSE_LOG_W(...)
#endif

#if (REMIND_RESPONSE_LVL >= LOG_LVL_ERROR)
    #define REMIND_RESPONSE_LOG_E(...)        QW_LOG_E(REMIND_RESPONSE_TAG, __VA_ARGS__)
#else
    #define REMIND_RESPONSE_LOG_E(...)
#endif

    typedef enum
    {
        SOUND_NONE,
        SOUND_RS01,
        SOUND_RS02,
        SOUND_RS03,
        SOUND_RS04,
        SOUND_RS05,
        SOUND_RS06,
        SOUND_RS07,
        SOUND_RS08,
        SOUND_RS09,
        SOUND_RS10,
        SOUND_RS11,
        SOUND_RS12,
        SOUND_RS13,
        SOUND_RS14,
        SOUND_RS15,
        SOUND_RS16,
        SOUND_RS17,
        SOUND_RS18,
        SOUND_RS19,
        SOUND_RS20,
        SOUND_RS21,
        SOUND_RS22,
        SOUND_RS23,
        SOUND_RS24,
        SOUND_RS25,
        SOUND_RM01,
        SOUND_RM02,
        SOUND_RM03,
        SOUND_RM04,
        SOUND_RM05,
        SOUND_RM06,
        SOUND_RM07,
        SOUND_RM08,
        SOUND_RM09,
        SOUND_RM10,
        SOUND_RC01,
        SOUND_RC02,
        SOUND_RC03,
        SOUND_MAX,
    }SOUND_TYPE;

    typedef enum
    {
        VIB_NONE,
        VIB_S01,
        VIB_S02,
        VIB_S03,
        VIB_M01,
        VIB_M02,
        VIB_M03,
        VIB_L01,
        VIB_L02,
        VIB_C01,
        VIB_C02,
        VIB_C03,
        VIB_MAX,
    }VIB_TYPE;

    typedef enum {
        PRIO_MSG_BOX = 6,       // 消息盒子（最高） //遵循弹窗的优先级
        PRIO_SYS_OPERATE = 5,   // 系统操作         

        PRIO_EMERGENCY_HEALTH = 4,   // 健康紧急事件           心率异常、跌倒检测等     未使用
        PRIO_SYSTEM_CRITICAL  = 3,   // 系统关键操作           自动关机、低电压警告等   未使用
        PRIO_SPORT_FEEDBACK   = 2,   // 运动状态反馈           运动倒计时、自动暂停等   未使用
        PRIO_GENERAL_NOTIFY   = 1,   // 普通通知               消息通知、天气提醒等     未使用
        PRIO_USER_INTERACTION = 0,   // 用户主动交互           物理按键、长按触摸等     未使用
    } RemindPriority;

    // 响应配置（8字节）
    typedef struct {
        RemindPriority priority; // 优先级
        uint8_t vibrate_id;      // 振动类型 0-15
        uint8_t sound_id;        // 声音类型 0-11
        uint8_t repeat_count;    // 是否重复 0：不重复 0xFF：无限重复 1-255：重复次数
        uint16_t interval_time;  // 间隔时间
    } ResponseMapping;

    #define RRT_BASE 0x80000000U

    // 统一场景标识（32位）
    typedef union {
        struct {
            uint32_t type : 31; // 场景类型
            uint32_t src  : 1;  // 来源标识 0:REMIND_TYPE, 1:GUI_MSGBOX
        } bits;
        uint32_t value;
    } UnifiedSceneID;

    typedef enum
    {
        // 基础操作类 (Base operations)
        RRT_B_BTN = RRT_BASE + 1,       //基础-按键(短按、长按)
        RRT_B_KNOB,                     //基础-旋钮
        RRT_B_HOLD_TOUCH_DIAL,          //基础-触摸长按表盘

        // 系统操作类 (System operations)
        RRT_S_POWER_ON,                 //系统-开机
        RRT_S_MANUAL_SHUTDOWN,          //系统-手动关机
        RRT_S_AUTO_SHUTDOWN,            //系统-自动关机
        RRT_S_REBOOT,                   //系统-重启
        RRT_S_BIND_REQUEST,             //系统-绑定请求 
        RRT_S_OTA_REQUEST,              //系统-OTA请求
        RRT_S_SHUTDOWN_COUNTDOWN,       //系统-关机倒计时

        // 应用卡片提醒类 (Application card reminder)
        RRT_A_HEART_OUT,                //应用-心率出值
        RRT_A_SPO2_OUT,                 //应用-血氧出值
        RRT_A_STRESS_OUT,               //应用-压力出值

        // 运动类页面提醒 (Sport page reminder)
        RRT_S_START_COUNTDOWN,          //运动-开始倒计时
        RRT_S_OUT_COUNT_DOWN,           //运动-退出运动倒计时
        RRT_S_START,                    //运动-开始
        RRT_S_AUTO_PAUSE,               //运动-自动暂停
        RRT_S_RESUME,                   //运动-继续
        RRT_S_MANUAL_PAUSE,             //运动-手动暂停
        RRT_S_GPS_LOACL_SUC,            //运动-GPS定位成功

        //训练相关
        RRT_S_TRAIN_STEP_END,           //运动-每个训练步骤结束
        RRT_S_TRIATHLON_CHANGE,         //运动-铁人三项切换

        //特殊提醒 (Special reminder)
        RRT_METRONOME,                  //节拍器
        RRT_TOOL_METRONOME,             //工具中心节拍器
        RRT_TOOL_BREATH_TRAIN,          //工具中心呼吸训练
        RRT_BREATH_TRAIN_OVER,          //工具中心呼吸训练完成
        RRT_PHOTO_COUNTDOWN,            //拍照倒计时
        RRT_PHOTO_SHUTTER,              //拍照快门
        RRT_DIAL_UPGRADE,               //表盘升级
        
        RRT_MAX,                        //最大值
    }REMIND_RESPONSE_TYPE;

    /************************************************************************
     *@function:  触发提醒（振动和铃声）
     *@brief: 场景触发
     *@param: scene: 场景标识REMIND_RESPONSE_TYPE或GUI_MSGBOX
     *@param: state: true:触发，false:停止
    *************************************************************************/
    void remind_trigger(uint32_t type, bool state);

    /************************************************************************
     * @function:  初始化
     * @brief:
     * *************************************************************************/
    void remind_response_init(void);

    /************************************************************************
     * @function:  停止所有提醒
     * @brief:
     * *************************************************************************/
    void remind_response_stop_all(void);

    /************************************************************************
     * @function:  反初始化
     * @brief:
     * *************************************************************************/
    void remind_response_deinit(void);

    /************************************************************************
     * @function:  设置工具节拍器回调
     * @brief:
     * *************************************************************************/
    void set_tool_metronome_callback(void (*callback)(void));

#ifdef __cplusplus
}
#endif

#endif	// __REMIND_RESPONSE_APP_H__
