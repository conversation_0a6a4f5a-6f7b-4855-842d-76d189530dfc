#include "dial_common_api.h"
#include "dial_app_api.h"
#include "dial_config_api.h"
#include "qw_fs.h"
#include "qwos.h"
#include "rtthread.h"
#include "igs_dev_config.h"

static int8_t s_edit_data_index = 0;//当前表盘数据索引
static int8_t s_prv_edit_data_index = 0;// 上一个表盘编辑数据索引
static uint32_t s_current_dial_goodsId = 0;
static EDIT_MODE_E s_dial_edit_type = 0; // 进入编译页面的方式
static APP_OPERATE_E s_app_operate_type = APP_DO_NOTHING; // app操作类型
static uint32_t s_app_operate_goodsId = 0; // 同步商品id
static bool s_is_from_launcher = false;
static char dial_config_path[50] = {0};
static char dial_js_path[50] = {0};

// 获取从何进入表盘编辑模式
EDIT_MODE_E get_enter_dial_edit_type(void)
{
    return s_dial_edit_type;
}

// 设置进入表盘编辑页面ID
void set_enter_dial_edit_type(EDIT_MODE_E enter_type)
{
    s_dial_edit_type = enter_type;
}

void init_dial_edit_data_index()
{
    s_edit_data_index = 0;
}

uint32_t get_dial_edit_data_index()
{
    if((int8_t)get_dial_data_num(NULL) <= s_edit_data_index) {
        s_edit_data_index = 0;
    }else if(s_edit_data_index < 0) {
        s_edit_data_index = get_dial_data_num(NULL)-1;
    }
    return s_edit_data_index;
}
void set_dial_edit_data_index_increase(void)
{
    s_edit_data_index++;
}
void set_dial_edit_data_index_decrease(void)
{
    s_edit_data_index--;
}

void init_dial_prv_edit_data_index()
{
    s_prv_edit_data_index = 0;
}
int8_t get_dial_prv_edit_data_index(void)
{
    return s_prv_edit_data_index;
}

void set_dial_prv_edit_data_index(int8_t index)
{
    s_prv_edit_data_index = index;
}

static void set_current_dial_goodsid(uint32_t goodsId) {
    s_current_dial_goodsId = goodsId;
}

uint32_t get_current_dial_goodsid(void) {
    return s_current_dial_goodsId;
}

void set_app_operate_type(APP_OPERATE_E type) {
    s_app_operate_type = type;
}
APP_OPERATE_E get_app_operate_type(void) {
    return s_app_operate_type;
}

void set_is_from_launcher_flag(bool flag)
{
    s_is_from_launcher = flag;
}

bool get_is_from_launcher_flag()
{
    return s_is_from_launcher;
}
// 读取对应关键字后面的数
bool get_dial_value_from_cfg(const char* file_path, const char* parse_key, uint32_t* parse_value, const char **str)
{
	if (file_path == NULL || parse_key == NULL || parse_value == NULL)
	{
		return false;
	}

	bool ret = false;
	QW_FIL *fp = NULL;
	char *p_buffer = NULL;
	char *p_key = NULL;
	char *p_value = NULL;

	do
	{
		p_buffer = (char *)qwos_malloc(100);
		if(NULL == p_buffer)
		{
			return ret;
		}

		if (QW_OK != qw_f_open(&fp, file_path, QW_FA_READ))
		{
			break;
		}
		else
		{
			//按行读取参数, 取int
			while (NULL != qw_f_gets(p_buffer, 100, fp))
			{
				p_buffer[100 - 1] = '\0';
				p_key = p_buffer;

				p_value = strchr(p_key, ':');

				if (p_value != NULL)
				{
					*p_value = '\0';
					p_value++;

					if (0 == strcmp(parse_key, p_key))
					{
						// *parse_value = atoi(p_value);
                        char *endptr;
                        errno  = 0;
                        long value = strtol(p_value,&endptr, 0);
                        if (endptr == p_value) 
                        {
                            if(str != NULL)
                            {
                                // 去除字符串末尾的换行符
                                char *end = p_value + strlen(p_value) - 1;
                                while (end >= p_value && (*end == '\n' || *end == '\r' || *end == ' ' || *end == '\t'))
                                {
                                    *end = '\0';
                                    end--;
                                }
                                *str = p_value;
                                break;
                            }
                        }
                        if (errno == ERANGE || value < 0 || value > UINT32_MAX)
                        {
                            assert("get dial data out of rang");
                            break;
                        }
                        *parse_value = (uint32_t)value;
						ret = true;
						break;
					}
				}

				memset(p_buffer, 0, 100);
			}
			qw_f_close(fp);
		}
	} while (0);

	qwos_free(p_buffer);

	return ret;
}
// 修改配置文件中指定关键字对应的值
static bool set_dial_value_in_cfg(const char* file_path, const char* parse_key, uint32_t new_value, OPERATION_TYPE_E operation)
{
    // rt_kprintf("set_dial_value_in_cfg, parse_key:%s new_value:%d\n", parse_key, new_value);
    if (file_path == NULL || parse_key == NULL)
    {
        return false;
    }

    bool ret = false;
    QW_FIL *fp = NULL;
    QW_FIL *temp_fp = NULL;
    char *p_buffer = NULL;
    char temp_file_path[256];

    // 生成临时文件路径
    snprintf(temp_file_path, sizeof(temp_file_path), "%s.tmp", file_path);

    p_buffer = (char *)qwos_malloc(1024); // 增大缓冲区大小
    if (NULL == p_buffer)
    {
        return ret;
    }

    // 打开原文件进行读取
    if (QW_OK != qw_f_open(&fp, file_path, QW_FA_READ))
    {
        goto cleanup;
    }

    // 打开临时文件进行写入
    if (QW_OK != qw_f_open(&temp_fp, temp_file_path, QW_FA_WRITE | QW_FA_CREATE_ALWAYS))
    {
        qw_f_close(fp);
        goto cleanup;
    }

    bool found_key = false;
    // 按行读取原文件内容
    while (NULL != qw_f_gets(p_buffer, 1024, fp))
    {
        p_buffer[strcspn(p_buffer, "\n")] = '\0'; // 去除换行符
        if (strstr(p_buffer, parse_key) == p_buffer) // 检查行是否以关键字开头
        {
            char *colon_pos = strchr(p_buffer, ':');
            if (colon_pos != NULL)
            {
                switch (operation)
                {
                    case OPERATION_MODIFY:
                    {
                        // 找到冒号，在冒号后写入新值
                        *colon_pos = '\0';
                        {
                            char new_line[1024];
                            snprintf(new_line, sizeof(new_line), "%s:%u\n", p_buffer, new_value);
                            qw_f_puts(new_line, temp_fp);
                        }
                        ret = true;
                        found_key = true;
                        break;
                    }
                    case OPERATION_DELETE:
                    {
                        // 删除该行，不写入临时文件
                        ret = true;
                        found_key = true;
                        break;
                    }
                    case OPERATION_ADD:
                    {
                        // 原样写入当前行
                        qw_f_puts(p_buffer, temp_fp);
                        qw_f_puts("\n", temp_fp);
                        // 新增一行
                        {
                            char new_line[1024];
                            snprintf(new_line, sizeof(new_line), "%s:%u\n", parse_key, new_value);
                            qw_f_puts(new_line, temp_fp);
                        }
                        ret = true;
                        found_key = true;
                        break;
                    }
                    default:
                        break;
                }
            }
            else
            {
                // 未找到冒号，原样写入
                qw_f_puts(p_buffer, temp_fp);
                qw_f_puts("\n", temp_fp);
            }
        }
        else
        {
            // 未匹配到关键字，原样写入临时文件
            qw_f_puts(p_buffer, temp_fp);
            qw_f_puts("\n", temp_fp);
        }
    }

    if (operation == OPERATION_ADD && !found_key)
    {
        // 如果是新增操作且未找到关键字，在文件末尾新增一行
        char new_line[1024];
        snprintf(new_line, sizeof(new_line), "%s:%u\n", parse_key, new_value);
        qw_f_puts(new_line, temp_fp);
        ret = true;
    }

    qw_f_close(fp);
    qw_f_close(temp_fp);

    if (ret)
    {
        // 删除原文件
        if (qw_f_unlink(file_path) != 0)
        {
            ret = false;
            goto cleanup;
        }

        // 将临时文件重命名为原文件
        if (qw_f_rename(temp_file_path, file_path) != 0)
        {
            ret = false;
        }
    }

cleanup:
    qwos_free(p_buffer);
    return ret;
}

// 删除备份文件
bool delete_backup_config_file()
{
    char bak_file_path[80] = {0};
	sprintf_array(bak_file_path, "%s.bak", get_dial_config_path());
    int ret = qw_f_unlink(bak_file_path);
    if (ret != QW_OK)
    {
        rt_kprintf("%s:delete backup file fail:%d\n", __FUNCTION__,ret);
        return false;
    }

    return true;
}

// 恢复配置文件
bool restore_backup_config_file()
{
    QW_FIL *bak_fp = NULL;
    char bak_file_path[256];
    snprintf(bak_file_path, sizeof(bak_file_path), "%s.bak", get_dial_config_path());

    // 如果备份文件不存在
    int ret = qw_f_open(&bak_fp, bak_file_path, QW_FA_READ | QW_FA_OPEN_EXISTING);
    if (QW_OK != ret)
    {
        rt_kprintf("%s:bak_file not exit:%d\n",__FUNCTION__, ret);
        return false;
    }
    qw_f_close(bak_fp);

    // 删除配置文件
    ret = qw_f_unlink(get_dial_config_path());
    if (ret != QW_OK)
    {
        rt_kprintf("%s:delete config file fail:%d\n",__FUNCTION__,ret);
        return false;
    }

    // 将备份配置文件重命名为配置文件
    ret = qw_f_rename(bak_file_path, get_dial_config_path());
    if (ret != QW_OK)
    {
        rt_kprintf("%s:rename bak_file fail:%d\n",__FUNCTION__, ret);
        return false;
    }
    return true;
}

// 备份配置文件
bool backup_cfg_file()
{
    char bak_file_path[256];
    char *p_buffer = NULL;
    QW_FIL *fp = NULL;
    QW_FIL *bak_fp = NULL;

    // 生成备份文件路径
    snprintf(bak_file_path, sizeof(bak_file_path), "%s.bak", get_dial_config_path());
    // 如果备份文件存在，不进行备份
    int ret = qw_f_open(&bak_fp, bak_file_path, QW_FA_READ | QW_FA_OPEN_EXISTING);
    if (QW_OK == ret)
    {
        qw_f_close(bak_fp);
        return true;
    }

    // 打开原文件进行读取
    ret = qw_f_open(&fp, get_dial_config_path(), QW_FA_READ);
    if (QW_OK != ret)
    {
        rt_kprintf("%s:open config file fail:%d\n", __FUNCTION__, ret);
        qw_f_close(fp);
        return false;
    }

    // 打开备份文件进行写入
    ret = qw_f_open(&bak_fp, bak_file_path, QW_FA_WRITE | QW_FA_CREATE_ALWAYS);
    if (QW_OK != ret)
    {
        rt_kprintf("%s:open bak_file fail:%d\n",__FUNCTION__,ret);
        return false;
    }

    p_buffer = (char *)qwos_malloc(100);
    if (NULL == p_buffer)
    {
        rt_kprintf("%s:malloc fail\n",__FUNCTION__);
        return false;
    }

    while (NULL != qw_f_gets(p_buffer, 100, fp))
    {
        p_buffer[100 - 1] = '\0';
        qw_f_puts(p_buffer, bak_fp);
        memset(p_buffer, 0, 100);
    }
    qw_f_close(fp);
    qw_f_close(bak_fp);
    qwos_free(p_buffer);

    return true;
}


void add_dial(uint32_t goodsId, void* buff)
{
    uint32_t dialCount = 0;
    get_dial_value_from_cfg(DIAL_COMMON_CFG_PATH, "dialCount", &dialCount, NULL);

    // 检查数组是否已满
    if (dialCount >= MAX_DIALS) {
        rt_kprintf("dial have been full\n");
        return;
    }
    for(int i = 0; i < dialCount; i++)
    {
        if(goodsId == get_cfg_dial_goodsid_by_index(i))
        {
            rt_kprintf("dial have been exist\n");
            set_app_operate_type(APP_DO_NOTHING);
            return;
        }
    }
    rt_kprintf("dialCount:%d GoodsId%u\n",dialCount, goodsId);
    char dialNumber[10] = {0};
    sprintf_array(dialNumber, "dial%u", dialCount);
    set_dial_value_in_cfg(DIAL_COMMON_CFG_PATH, dialNumber, goodsId, OPERATION_ADD);
    set_dial_value_in_cfg(DIAL_COMMON_CFG_PATH, "dialCount", dialCount + 1, OPERATION_MODIFY);
    set_dial_value_in_cfg(DIAL_COMMON_CFG_PATH, "usingDialIndex", dialCount, OPERATION_MODIFY);
    create_dial_snapshot(goodsId, buff, false, dialCount); 
    set_app_operate_type(APP_DO_NOTHING);
}

// 删除指定表盘
void delete_dial(uint32_t goodsId)
{
    bool found = false;
    uint32_t dialCount = 0;
    char dialNumber[32] = {0};

    get_dial_value_from_cfg(DIAL_COMMON_CFG_PATH, "dialCount", &dialCount, NULL);
    // 最少保留一个表盘
    if(dialCount <= 1)
    {
        rt_kprintf("dial have been empty\n");
        set_app_operate_type(APP_DO_NOTHING);
        return;
    }
    int i = 0;
    for(; i < dialCount; i++)
    {
        uint32_t tempGoodsId = 0;
        sprintf_array(dialNumber, "dial%d", i);
        get_dial_value_from_cfg(DIAL_COMMON_CFG_PATH, dialNumber, &tempGoodsId, NULL);
        rt_kprintf("tempGoodsId:%u\n", tempGoodsId);
        // 删除对应的表盘
        if(goodsId == tempGoodsId)
        {
            found = true;
            // 将后面的元素依次往前移动
            for (int j = i; j < dialCount-1; j++)
            {
                // 获取下一个表盘的值
                sprintf_array(dialNumber, "dial%d", j+1);
                get_dial_value_from_cfg(DIAL_COMMON_CFG_PATH, dialNumber, &tempGoodsId, NULL);
                // 将下一个表盘的值赋给当前表盘
                sprintf_array(dialNumber, "dial%d", j);
                set_dial_value_in_cfg(DIAL_COMMON_CFG_PATH, dialNumber, tempGoodsId, OPERATION_MODIFY);
            }
            break;
        }
    }

    if(found){
        sprintf_array(dialNumber, "dial%d", (int)(dialCount-1));
        set_dial_value_in_cfg(DIAL_COMMON_CFG_PATH, dialNumber, dialCount - 1, OPERATION_DELETE);
        set_dial_value_in_cfg(DIAL_COMMON_CFG_PATH, "dialCount", dialCount - 1, OPERATION_MODIFY);
        uint8_t usingDialIndex = get_using_dial_index();
        // 删除的表盘索引大于等于当前正在使用的表盘索引，则当前使用的表盘索引减1
        if(usingDialIndex >= i){
            set_dial_value_in_cfg(DIAL_COMMON_CFG_PATH, "usingDialIndex", usingDialIndex - 1, OPERATION_MODIFY);
        }
        js_destory_dial();
        delete_dial_snapshot(goodsId);
        // TODO删除对应的表盘文件
        dial_path_clear(goodsId);
    }
    set_app_operate_type(APP_DO_NOTHING);
}
/// @brief 清理文件夹内所有文件
/// @param path
static void dial_path_clear(uint32_t goodsId)
{
    char path[128] = {0};
    const char *dialType = get_cfg_dial_type(get_using_dial_index());
    #ifdef SIMULATOR
        sprintf_array(path, "./Dial/%u%s", goodsId, dialType);
    #else
        sprintf_array(path, "%s/Dial/%u%s", BASE_PATH, goodsId, dialType);
    #endif

    QW_DIR scan_dir;
    QW_FILINFO fileinfo;
    char scan_file_path[128] = {0};

    if (QW_OK == qw_f_opendir(&scan_dir, path))
    {
        while (true)
        {
            //扫描目录文件名
#ifdef SIMULATOR
            if (QW_OK == qw_f_readdir(&scan_dir, &fileinfo) && fileinfo.cFileName[0] != 0)
            {
                if (fileinfo.cFileName[0] == '.')
                {
                    continue;   //忽略上级目录
                }
                else
                {
                    sprintf_array(scan_file_path, "%s/%s", path, fileinfo.cFileName);
                    qw_f_unlink(scan_file_path);
                }
            }
            else
            {
                break;
            }
#else
            if (QW_OK == qw_f_readdir(&scan_dir, &fileinfo) && fileinfo.fname[0] != 0)
            {
                if (fileinfo.fname[0] == '.')
                {
                    continue;   //忽略上级目录
                }
                else
                {
                    sprintf_array(scan_file_path, "%s/%s", path, fileinfo.fname);
                    qw_f_unlink(scan_file_path);
                }
            }
            else
            {
                break;
            }
#endif
        }
        qw_f_closedir(&scan_dir);

    }
    qw_f_unlink(path);
}
// 删除表盘快照
bool delete_dial_snapshot(uint32_t goodsId)
{

    char path[50] = {0};
    sprintf_array(path, "%s/%u_dialSnapshot.bin", DIAL_CACHE_PATH, goodsId);
    QW_FIL *fp = NULL;
    // 如果文件存在，则删除文件
    if (QW_OK != qw_f_open(&fp, path, QW_FA_READ | QW_FA_OPEN_EXISTING))
    {
        rt_kprintf("[%s]open SnapShot bin fail\n", __FUNCTION__);
        return false;
    }
    qw_f_close(fp);
    rt_kprintf("delete SnapShot bin path:%s\n", path);
    qw_f_unlink(path);
    return true;

}

bool create_dial_snapshot(uint32_t goodsId, void *buf, bool isRecover, uint32_t index)
{
    if(isRecover)
    {
        static dial_info_t dialInfo = {0};
        void *dialbuff = get_dial_cache_buf();
		memcpy(buf, dialbuff, 466*466*3);
        dialInfo.buf = buf;
        dialInfo.goodsid = goodsId;
        js_recover_dial_snapshot(&dialInfo);
    }
    else
    {
        js_create_new_dial_snapshot(goodsId, buf, index);
    }
    return true;
}
bool is_dial_snapshot_exist(const char *path)
{
    QW_FIL *fp = NULL;
    if (QW_OK != qw_f_open(&fp, path, QW_FA_READ | QW_FA_OPEN_EXISTING))
    {
        return false;
    }
    qw_f_close(fp);
    return true;
}

void send_msg_to_gui_thread(uint32_t goodsId)
{
    s_app_operate_goodsId = goodsId;
    submit_gui_event(GUI_EVT_SYNC_DIAL, 0, (void *)goodsId);

}
uint32_t get_app_operate_goodsId()
{
    return s_app_operate_goodsId;
}

char *get_dial_config_path()
{
    return dial_config_path;
}
char *get_dial_js_path()
{
    return dial_js_path;
}

void set_dial_config_path(void)
{
    memset(dial_config_path, 0, 50);
    char cfgPath[50] = {0};
    char dialJsPath[50] = {0};
    const char *dialType = get_cfg_dial_type(get_using_dial_index());
    uint32_t goodsId = get_cfg_current_using_dial_goodsid();
#ifdef SIMULATOR
	snprintf(cfgPath, 50, "./Dial/%u%s/config.cfg", goodsId, dialType);
	snprintf(dialJsPath, 50, "./Dial/%u%s/touchx.js", goodsId, dialType);
#else
	snprintf(cfgPath, 50, "%sDial/%u%s/config.cfg", BASE_PATH, goodsId, dialType);
    snprintf(dialJsPath, 50, "%sDial/%u%s/touchx.js", BASE_PATH, goodsId, dialType);
#endif
    sprintf_array(dial_config_path, "%s",cfgPath);
    sprintf_array(dial_js_path, "%s",dialJsPath);

}

void show_js_dial_theme_color_preview_view(const char* jsPath, uint32_t index)
{
    uint32_t themeColor = get_dial_them_color(index);
    qjs_touchgfx_set_theme_color(jsPath, themeColor, index);
}

void restore_all_js_edit_data_preview_view(const char* jsPath, uint32_t editDataIndex)
{
    qjs_touchgfx_restore_all_edit_data_preview_view(jsPath, editDataIndex);
}
void refresh_js_dial_pview(const char* jsPath)
{
    qjs_touchgfx_refresh_dial_view(jsPath);
}

void set_js_edit_data_preview(const char* jsPath, uint32_t editDataIndex)
{
    qjs_touchgfx_update_edit_data_preview(jsPath, editDataIndex);
}
void set_js_aod_mode(const char* jsPath, bool isAodMode)
{
    qjs_touchgfx_set_aod_mode(jsPath, isAodMode);
}