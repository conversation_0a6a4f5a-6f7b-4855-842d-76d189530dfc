#include "lvgl.h" 

#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP ALIGN(4)
#endif

#ifndef eZIP_RGBARGB888A
#define eZIP_RGBARGB888A
#endif


LV_ATTRIBUTE_MEM_ALIGN_EZIP const  uint8_t sys_set_health_check_set_map[] SECTION(".ROM3_IMG_EZIP.sys_set_health_check_set") = { 
    0x00,0x00,0x06,0x8c,0x46,0x08,0x20,0x00,0x00,0x48,0x00,0x48,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0x03,0x00,0x00,0x00,0x90,0x00,0x00,0x03,0x34,0x00,0x00,0x05,0x9c,
    0x3d,0xeb,0x89,0x8d,0xa2,0x8c,0xe2,0xef,0xcd,0x7c,0x4b,0x0b,0x1e,0x89,0xb8,0x36,0x14,0xda,0xdd,0xc6,0x43,0x39,0x60,0x42,0x90,0x56,0xbc,0x08,0xc8,0x45,0x83,0x89,
    0x04,0xaa,0x44,0x6e,0x52,0x95,0x84,0xb4,0x0b,0x21,0x31,0x8a,0x90,0x00,0x91,0x0b,0xee,0xb2,0x18,0x11,0x6b,0x62,0x88,0x02,0x09,0xc5,0x8b,0x1c,0x38,0x48,0x49,0xb8,
    0x34,0xdd,0x46,0x7a,0x50,0x0c,0x21,0xb1,0x5d,0xda,0x42,0x1a,0x2c,0x26,0xdc,0x68,0xe9,0xce,0xcc,0xf3,0x7d,0x5b,0xbb,0xb4,0xdb,0x9d,0xd9,0x9d,0xd9,0x99,0xd9,0x3f,
    0xdd,0x5f,0x18,0x66,0xa6,0xb3,0xdf,0x9f,0xf9,0xcd,0x7b,0xbf,0xf7,0xbe,0xf7,0x01,0x44,0x28,0x23,0x1c,0x1e,0xaf,0x0b,0x1b,0x86,0xb6,0xde,0x30,0xb0,0x15,0x00,0x5b,
    0x80,0x68,0x0d,0x28,0x10,0x04,0x82,0x95,0xfc,0x78,0x95,0xfc,0x0d,0xf1,0x3f,0x05,0xe0,0x19,0x01,0x3e,0xe2,0xe7,0x13,0x80,0x38,0xc2,0xe7,0xbb,0x06,0xc2,0xd0,0x54,
    0xbd,0xd6,0xdf,0xd3,0x00,0x4f,0xdd,0x9c,0x53,0x49,0x09,0x8a,0x0c,0xd7,0xb5,0x80,0x30,0xb6,0x83,0x41,0x6f,0xf2,0xed,0x1b,0x7c,0x04,0x5d,0xe8,0xb6,0x8f,0xc9,0xbb,
    0x46,0x90,0xea,0x8d,0x87,0xe0,0x9f,0x8a,0x23,0xa8,0xfb,0x3e,0x34,0x29,0x20,0x3a,0xd8,0x14,0x76,0xb2,0x35,0x6c,0xf4,0x76,0x34,0xfc,0x19,0x11,0xce,0x45,0x9b,0x53,
    0x89,0xb2,0x27,0xa8,0x7b,0x44,0x7d,0x1b,0x11,0xf7,0xf1,0xe5,0xbb,0x7e,0x7f,0x14,0xb6,0xa8,0x2b,0xaa,0x8a,0xc7,0xbf,0x5e,0x3b,0x73,0xb7,0xec,0x08,0x3a,0x74,0x5f,
    0xfd,0xc0,0x20,0xec,0xe6,0xcb,0xd7,0xca,0x40,0xe6,0xbe,0x8c,0x85,0xb4,0x93,0x65,0x41,0xd0,0xc1,0x11,0xf5,0x1d,0x52,0xf0,0x73,0xfe,0x7c,0xed,0xe5,0x14,0x08,0x78,
    0x3e,0x37,0x41,0x17,0x9d,0xb1,0x57,0xa6,0x93,0x25,0x21,0x28,0x32,0x56,0x1f,0x22,0x5d,0xff,0x0a,0x81,0x3a,0xa0,0x7c,0x31,0x69,0x00,0x7c,0x18,0x0f,0x69,0x37,0x7c,
    0x25,0xa8,0x7b,0x38,0x70,0x00,0x15,0x3a,0xcd,0x97,0xcb,0xa0,0x22,0x40,0x1d,0xb1,0x90,0xde,0xeb,0x39,0x41,0x07,0xfe,0x86,0x17,0x85,0x2a,0xbe,0xe3,0xcb,0x9d,0x50,
    0x71,0xb0,0x26,0xa9,0x68,0x82,0xba,0x47,0xc5,0x16,0x34,0xf0,0x47,0x1e,0x68,0x2d,0x54,0x28,0xd8,0xdd,0xb6,0x9b,0xb9,0x5b,0x51,0x04,0x1d,0x4a,0x06,0x3e,0x32,0x80,
    0x7e,0x80,0xca,0xc7,0x24,0x68,0xa2,0x3d,0x97,0x70,0x2b,0x8e,0xa3,0x54,0x52,0x7c,0x51,0x25,0xe4,0x48,0xac,0x02,0x55,0xeb,0xc9,0xf5,0xc0,0x11,0x41,0x91,0x11,0x71,
    0x8a,0xb3,0xe0,0x93,0x55,0x42,0xce,0x9c,0x2f,0x6d,0x8d,0x24,0xc5,0x91,0xa2,0x5d,0x4c,0x92,0xc3,0xad,0x3e,0x83,0x2a,0x85,0xa2,0x2a,0xeb,0xe6,0x67,0xdc,0x8a,0x5d,
    0xb7,0xaa,0x66,0x72,0x24,0x74,0x9d,0x8e,0x3a,0xb2,0xa0,0x2a,0x12,0xe4,0xfc,0xde,0x86,0xd8,0x3e,0xb7,0xc0,0x15,0x85,0x86,0x72,0xc3,0xf0,0x96,0x9c,0x87,0x09,0x82,
    0x07,0x83,0x94,0xb9,0x6f,0xdc,0x84,0xb0,0xba,0xad,0x34,0xd5,0x18,0x22,0xd8,0xcf,0xa7,0x44,0x41,0x16,0x34,0x9b,0x04,0x06,0x7e,0xf7,0x3a,0xcf,0x91,0x04,0x5d,0xdd,
    0xa3,0x67,0xee,0xdb,0xba,0x14,0x68,0xef,0x72,0x1c,0x64,0x5d,0xc8,0x8d,0xb4,0xa0,0xac,0x27,0xe5,0x9d,0xc1,0x6c,0x86,0x5c,0xb9,0x49,0xa0,0xf3,0xa0,0x16,0xd8,0x9d,
    0x57,0xa4,0xe5,0xda,0xaa,0x32,0x97,0x0f,0x6e,0x10,0x44,0x3b,0x2c,0x35,0x48,0xae,0xca,0x41,0xd7,0x4e,0xbb,0xe1,0x3a,0x85,0x60,0xbe,0xfe,0x38,0xed,0xc3,0x65,0xcd,
    0xda,0xd6,0x39,0x01,0x2b,0x4c,0x09,0x9a,0x2d,0x59,0x38,0x5f,0x95,0x0f,0xc4,0x0d,0x48,0xf0,0x51,0x2c,0xec,0xf6,0xe3,0xa6,0x76,0x2d,0x9f,0x16,0x9b,0x15,0xb3,0x62,
    0x57,0x31,0xf5,0x1c,0xb7,0xc8,0x71,0x02,0x39,0xee,0x80,0x4b,0x63,0x2b,0x04,0x1b,0x72,0x12,0x94,0xae,0x04,0x16,0xe1,0x52,0xa5,0x22,0x67,0x3e,0x49,0x2e,0x25,0x44,
    0xad,0x22,0x77,0x0d,0xd9,0x79,0x99,0x34,0x5b,0x4b,0xa4,0xc9,0xcb,0x9c,0xa6,0x90,0x76,0x6e,0x12,0x2b,0xad,0xa8,0x68,0x57,0x23,0x0a,0x2f,0x22,0xe8,0xff,0x02,0xbb,
    0x73,0x51,0xce,0x22,0xc8,0xce,0x24,0x13,0xe5,0x97,0x52,0x37,0x28,0xd9,0x5b,0x33,0x65,0xb2,0xfb,0x50,0x2e,0xa1,0x3e,0xa8,0x64,0xad,0x41,0xf6,0xd5,0x68,0x59,0x50,
    0x69,0xac,0x13,0xf3,0x77,0x3c,0x81,0xbc,0xdd,0xd4,0x93,0x02,0x3e,0x70,0xd6,0x9e,0xce,0x48,0xfd,0x6a,0xbc,0xac,0xfa,0xa6,0x5d,0x59,0x2b,0x79,0xcc,0x10,0x84,0x86,
    0x78,0xdf,0xeb,0x6d,0x44,0xf9,0x32,0x85,0x26,0x7d,0x76,0x92,0x3f,0x2f,0xb5,0x2b,0xe3,0x62,0x88,0xf0,0x5e,0xcd,0xa9,0x72,0xac,0x45,0xd3,0xcb,0x8a,0xe1,0xba,0x16,
    0x02,0x7d,0xa3,0x1f,0x03,0x5a,0x59,0x84,0x99,0x75,0x59,0x59,0x9d,0xd5,0x12,0xc5,0x0d,0xfc,0x07,0x00,0x44,0x60,0x44,0x46,0x03,0xfb,0xc1,0xa0,0x6f,0xc1,0x05,0x5c,
    0xdd,0xa3,0xc3,0xc3,0x04,0x65,0xee,0x23,0x49,0x51,0x50,0x3b,0xd9,0x46,0xb6,0x9d,0x43,0x5b,0x97,0x02,0xed,0x7c,0x0c,0xc4,0x0d,0x48,0xf0,0x61,0x17,0x73,0xed,0x8b,
    0xc5,0x6c,0x0f,0x3a,0x6d,0x81,0x1a,0x2c,0x08,0x42,0xd8,0x5c,0xa3,0xc2,0x84,0xa0,0xc3,0xe3,0x75,0x61,0x3e,0x07,0x6b,0x54,0xe4,0x86,0x30,0x0c,0x6d,0xbd,0x34,0x21,
    0x3f,0x20,0x75,0x66,0xe0,0xac,0x3d,0x3d,0x69,0xdc,0x84,0xd0,0x78,0x59,0x35,0x7d,0xfe,0x60,0x90,0x1c,0x69,0x54,0x81,0x98,0x62,0x82,0xb0,0xd5,0xaf,0xaf,0x21,0x5f,
    0x66,0xbe,0x80,0x17,0x82,0xd5,0x6d,0xf9,0x3f,0x5e,0xc2,0xa3,0xf9,0xf2,0x4c,0x9f,0xb0,0x06,0x61,0x4b,0xcd,0x91,0x72,0x03,0x09,0x46,0x05,0x10,0xad,0xf1,0xc9,0xc3,
    0xf2,0x5a,0x84,0x99,0x75,0x59,0x59,0x9d,0xb4,0x4a,0xaf,0x40,0x80,0x63,0x82,0xe3,0x58,0x10,0xbc,0x1b,0x63,0x01,0xac,0xf2,0x92,0x74,0x1e,0x94,0xd0,0x17,0xfd,0xdd,
    0x69,0x1e,0xe4,0x4a,0x04,0x43,0x1a,0x56,0x98,0x9c,0x95,0x35,0x67,0x32,0xb1,0x20,0xa2,0x7b,0xf2,0x93,0xae,0xaa,0x51,0x61,0x06,0xf5,0x8e,0x52,0x23,0xc1,0x3c,0xc4,
    0xc7,0xc2,0x33,0x77,0x04,0xcb,0x0f,0xa1,0x4f,0x89,0x90,0xd4,0x19,0xbb,0xa2,0x2a,0xf3,0x20,0xc8,0xd2,0x2e,0x9f,0x34,0x69,0x30,0x9d,0x28,0xf2,0xd0,0xcf,0x78,0xca,
    0xf5,0x7e,0xe5,0x41,0x76,0x5f,0x4e,0x46,0xbd,0xec,0xc8,0xf7,0xd0,0x41,0x3e,0xe5,0x20,0x07,0xea,0x4f,0x0b,0x35,0x87,0xb2,0x47,0x35,0x6f,0xca,0x99,0x03,0xdd,0x9a,
    0x5d,0xac,0x12,0x4d,0xd4,0xe8,0x58,0x8c,0x58,0x58,0xeb,0x4b,0xbb,0x18,0x20,0x8e,0xb0,0x41,0xbd,0xee,0x57,0x1e,0x64,0x96,0x0b,0x65,0xd7,0x83,0x4a,0x9b,0x07,0x61,
    0xef,0xf3,0x72,0x07,0xd1,0xdd,0x9a,0xbd,0x64,0xe7,0x3f,0xc6,0xb5,0x0c,0x41,0x06,0xc2,0x90,0xab,0x4b,0x89,0x4d,0x59,0x82,0x9a,0xf0,0x29,0x4d,0xcf,0x15,0xfd,0x9c,
    0x59,0xcf,0xf8,0x99,0xb0,0x7e,0x29,0x53,0xee,0x98,0xaa,0xd7,0xfa,0x5f,0x98,0x16,0x9e,0x4d,0x34,0x97,0xdb,0x94,0xb7,0xf9,0xc0,0xa9,0x05,0xcb,0x8d,0x9e,0x06,0x78,
    0xca,0xe7,0x3e,0x37,0x75,0xa6,0x90,0x12,0x85,0xd7,0x0b,0x62,0x87,0x73,0xb8,0x1d,0x0b,0xa7,0xce,0x2f,0x2a,0xb9,0x72,0xa8,0xbf,0xe6,0xe6,0x04,0x77,0x5d,0x56,0x4b,
    0x46,0x92,0x2c,0xd6,0xef,0xb2,0x28,0xb0,0x59,0x3a,0x97,0x42,0xc7,0x16,0x55,0x14,0x67,0x09,0x4a,0xf5,0x22,0x88,0xb3,0x6e,0x93,0x64,0x27,0x73,0x36,0x4b,0xfe,0x72,
    0x65,0xd2,0x56,0xba,0xe3,0xf4,0xc3,0xf0,0xc2,0xf4,0x42,0xac,0x49,0xbf,0xbe,0x88,0xb4,0xe7,0xdb,0x33,0x81,0x9f,0xf8,0x67,0x7b,0x4b,0xe5,0x16,0x66,0xdb,0x3e,0x3e,
    0xe1,0xb1,0xb6,0x5c,0x5b,0xf7,0xcd,0xcb,0xf0,0x38,0xf7,0xae,0x86,0x64,0x0a,0xe1,0xdc,0x92,0xcd,0x9a,0x81,0x0e,0xe6,0x22,0x67,0x01,0x41,0xd1,0xe6,0x14,0x7f,0x43,
    0xbc,0xb2,0xe4,0xc8,0x41,0xfa,0x3e,0x1a,0xd2,0x2f,0x5a,0xef,0x8b,0xcd,0x55,0x3f,0x54,0x3c,0xbe,0xa4,0x12,0x42,0x80,0xa1,0x68,0x93,0xfe,0xa9,0xb5,0x75,0x65,0x21,
    0x92,0x14,0x47,0xf8,0x74,0xa2,0x54,0x3a,0x64,0x67,0x37,0xa3,0x78,0xd7,0xc2,0x57,0xa3,0xa1,0xd4,0x1f,0xb6,0x08,0x4a,0x93,0x34,0x22,0xfa,0xf8,0xc9,0xd6,0xea,0xb6,
    0x1e,0xda,0x75,0x26,0xa4,0xff,0x52,0xd8,0xd6,0x73,0x36,0x74,0xd1,0xc9,0xff,0x4f,0x56,0xaf,0xee,0x60,0xa4,0x10,0x72,0x4c,0x2d,0x48,0xa2,0x2b,0x29,0xde,0x62,0xf6,
    0x7e,0x5b,0xaa,0x96,0x63,0x6d,0x41,0x8c,0x78,0x48,0xbb,0xc1,0xdd,0x75,0x54,0x4f,0x28,0x87,0x21,0xa9,0x39,0x76,0xc8,0x91,0xf8,0x0f,0x00,0x00,0x45,0xc8,0x83,0x48,
    0x52,0xdd,0x0d,0x80,0x57,0xa0,0xa2,0x41,0x3d,0xd1,0x66,0xfd,0x13,0x44,0x20,0xbb,0x2d,0x95,0x7c,0x3f,0x88,0x85,0xf4,0x5e,0x03,0x60,0x3b,0x5f,0x4e,0x56,0x1e,0x2f,
    0xf0,0x2f,0x73,0xb2,0x97,0xdf,0xe1,0x63,0x27,0xe4,0x14,0x44,0x90,0x44,0x3c,0xa4,0xdd,0x00,0x4d,0xb4,0xf3,0x10,0x37,0x2b,0x86,0x1b,0xa2,0x0b,0x2a,0x69,0xeb,0xa2,
    0x21,0xfd,0x62,0x31,0xfd,0xa0,0xdd,0x06,0x91,0xa4,0x38,0xc2,0xa7,0x13,0x65,0xcc,0xcd,0x6d,0x54,0xe8,0x58,0xb4,0x49,0xbf,0xee,0x46,0x67,0xe8,0xa4,0xd1,0xa1,0xb1,
    0x65,0xad,0xba,0x4e,0x47,0xd9,0x6a,0x3b,0xca,0x87,0x17,0x1c,0xe7,0xb7,0x39,0x15,0x6b,0x4e,0x9d,0x77,0xb5,0xd7,0x62,0x1a,0x1f,0xbc,0x1f,0x68,0x23,0x82,0xfd,0x6c,
    0xd0,0x7b,0x4b,0x27,0x33,0x78,0x15,0xc8,0xf8,0xf5,0x4c,0x58,0xbf,0xe4,0x09,0xed,0x6e,0x74,0xd2,0x95,0x84,0x97,0x10,0x02,0xbb,0xd9,0xa2,0x76,0xf0,0xed,0x36,0x0f,
    0xf9,0x98,0xe2,0x63,0x90,0xd5,0xb6,0x1f,0x09,0x6e,0xc5,0xc2,0x5a,0x9f,0xe7,0x76,0xe9,0x76,0x87,0x9d,0x13,0xb0,0x62,0xf9,0xb4,0xd8,0xac,0x10,0x6c,0x00,0xc4,0x56,
    0x56,0xcb,0x30,0x9f,0x1b,0x98,0xbc,0x20,0x47,0xc3,0x3a,0xcc,0x3f,0xe6,0x14,0x13,0xf0,0x84,0x09,0x18,0x45,0xc0,0x31,0x40,0x1a,0x26,0x85,0xee,0x19,0x86,0xfa,0x57,
    0x3c,0x34,0xf3,0xa7,0xdf,0x16,0xfa,0x1f,0xac,0xa4,0x85,0x7b,
};


#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER ALIGN(4)
#endif
LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER const eZIP_RGBARGB888A  lv_img_dsc_t sys_set_health_check_set SECTION(".ROM3_IMG_EZIP_HEADER.sys_set_health_check_set") = { 
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .header.always_zero = 0,
  .header.w = 72,
  .header.h = 72,
  .data_size  = 1676,
  .data = sys_set_health_check_set_map
};
