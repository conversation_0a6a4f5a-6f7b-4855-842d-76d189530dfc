/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   UserButtonController.cpp
@Time    :   2024/12/10 10:57:30
*
**************************************************************************/

#include <touchgfx/hal/Types.hpp>
#include <touchgfx/hal/HAL.hpp>
#include <UserButtonController.hpp>
#include "../../../qw_platform/qwos_app/GUI/QwGUIKey.h"
#include "key_module.h"
#include "backlight_module/backlight_module.h"
#include "battery_srv_module/battery_srv.h"
#include "global_button_srv/global_button.h"
#include "algo_service_sport_status.h"
#include <stdbool.h>
#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include <string.h>
#include "drv_flash.h"
#include "app_task.h"
#include "dfs_fs.h"
#include "drivers/mmcsd_core.h"
#include <dfs_posix.h>
#include "ble_test_module/button_debug_cmd.h"
#include "key_queue.h"
#include <map>
#include <utility>
#include "watch_lock_srv/watch_lock_srv.h"

namespace touchgfx
{

//添加一个按键值局部变量
static uint8_t  t_key_value = KEY_VALUE_MAX;

static Queue t_key_queue;

void deal_key_wakeup(uint8_t& c)
{
    if(get_backlight_status() != BK_STATUS_ON)
    {
        BK_STATUS backlight_status_bak = get_backlight_status();//backlight_open_app 会改变屏幕状态，故备份。
        bool isOff = (backlight_status_bak == BK_STATUS_OFF);
        bool isAod = (backlight_status_bak == BK_STATUS_AOD);
        bool isSporting = (get_sport_status() > enum_status_ready);

        if(get_watch_lock_state())
        {
            if(c == KEY_VALUE_PRESS_DOWN_BACK || c == KEY_VALUE_PRESS_DOWN_START || c == KEY_VALUE_PRESS_DOWN_POWER || 
                ((c== KEY_VALUE_KNOB_UP || c == KEY_VALUE_KNOB_DOWN) && backlight_status_bak == BK_STATUS_AOD))
            {
                //可以由弹窗唤醒屏幕
                if(!battery_show_backlight_on())
                {
                    backlight_open_app();
                }
            }
        }
        else
        {
            if(c == KEY_VALUE_CLK_BACK || c == KEY_VALUE_CLK_START || c == KEY_VALUE_CLK_POWER ||
                c == KEY_VALUE_HOLD_BACK || c == KEY_VALUE_HOLD_START || c == KEY_VALUE_HOLD_POWER ||
                ((c== KEY_VALUE_KNOB_UP || c == KEY_VALUE_KNOB_DOWN) && backlight_status_bak == BK_STATUS_AOD))
            {
                //可以由弹窗唤醒屏幕
                if(!battery_show_backlight_on())
                {
                    backlight_open_app();
                }
            }
        }

        if(c == KEY_VALUE_CLK_POWER && get_access_short_key() == SHORT_KEY_BREATH_SCREEN)
        {
            c = KEY_VALUE_MAX;
            return;
        }

        if (isOff) {
            // OFF状态处理所有5种按键
            if (c == KEY_VALUE_CLK_BACK || c == KEY_VALUE_CLK_START || 
                c == KEY_VALUE_CLK_POWER || c == KEY_VALUE_KNOB_UP || 
                c == KEY_VALUE_KNOB_DOWN) {
                c = KEY_VALUE_MAX;
                return;
            }
        } else if (isAod) {
            if (!isSporting) {
            // AOD状态且运动状态<=ready时处理3种按键
                if (c == KEY_VALUE_CLK_BACK || c == KEY_VALUE_CLK_START || 
                    c == KEY_VALUE_CLK_POWER) {
                    c = KEY_VALUE_MAX;
                    return;
                }
            }
        }

    }
    else
    {
        backlight_open_app();
    }
    if(!isFull(&t_key_queue))
    {
        enqueue(&t_key_queue,c);
    }
}

void user_callback_1(int32_t keyval,int32_t key_event)
{
    // TRACE_KEY_LOG("knob key_event:%d,keyval:%d",key_event,keyval);

    //TO DO 根据keyval的值判断是顺时针还是逆时针转换坐标
    if(key_event == KNOB_MOTION_ROTATE_CW)
    {
        t_key_value = KEY_VALUE_KNOB_UP;
    }
    else if(key_event == KNOB_MOTION_ROTATE_CCW)
    {
        t_key_value = KEY_VALUE_KNOB_DOWN;
    }
    else
    {
        t_key_value = KEY_VALUE_MAX;
    }

    deal_key_wakeup(t_key_value);

#if defined(BUTTON_DEBUG_MODE) && (BUTTON_DEBUG_MODE == 1)
    button_report(t_key_value);
#endif
}

void user_callback_2(uint8_t key,uint8_t key_event)
{
    TRACE_KEY_LOG("key:%d,key_event:%d", key, key_event);

    // 使用查找表优化多条件判断
    static const std::map<std::pair<uint8_t, uint8_t>, uint8_t> key_mapping = {
        // KEY1 映射
        {{KEY1, SINGLE_CLICK},    KEY_VALUE_CLK_POWER},
        {{KEY1, LONG_PRESS_HOLD}, KEY_VALUE_HOLD_POWER},
        {{KEY1, PRESS_DOWN},      KEY_VALUE_PRESS_DOWN_POWER},
        {{KEY1, PRESS_UP},        KEY_VALUE_PRESS_UP_POWER},

        // KEY2 映射
        {{KEY2, SINGLE_CLICK},    KEY_VALUE_CLK_START},
        {{KEY2, LONG_PRESS_HOLD}, KEY_VALUE_HOLD_START},
        {{KEY2, PRESS_DOWN},      KEY_VALUE_PRESS_DOWN_START},
        {{KEY2, PRESS_UP},        KEY_VALUE_PRESS_UP_START},

        // KEY3 映射
        {{KEY3, SINGLE_CLICK},    KEY_VALUE_CLK_BACK},
        {{KEY3, LONG_PRESS_HOLD}, KEY_VALUE_HOLD_BACK},
        {{KEY3, PRESS_DOWN},      KEY_VALUE_PRESS_DOWN_BACK},
        {{KEY3, PRESS_UP},        KEY_VALUE_PRESS_UP_BACK},

        // 组合键映射
        {{LONG_PRESS_K1_K3, LONG_PRESS_HOLD}, KEY_VALUE_LONG_POWER_AND_BACK}
    };

    // 通过查找表获取键值
    auto it = key_mapping.find({key, key_event});
    t_key_value = (it != key_mapping.end()) ? it->second : KEY_VALUE_MAX;

    deal_key_wakeup(t_key_value);

#if defined(BUTTON_DEBUG_MODE) && (BUTTON_DEBUG_MODE == 1)
    button_report(t_key_value);
#endif
}

#if defined(BUTTON_DEBUG_MODE) && (BUTTON_DEBUG_MODE == 1)
int8_t user_callback_3(uint8_t key_value)
{
    int8_t ret = 0;

    if ((key_value != KEY_VALUE_CLK_POWER) &&
        (key_value != KEY_VALUE_CLK_START) &&
        (key_value != KEY_VALUE_CLK_BACK) &&
        (key_value != KEY_VALUE_KNOB_UP) &&
        (key_value != KEY_VALUE_KNOB_DOWN) &&
        (key_value != KEY_VALUE_HOLD_POWER) &&
        (key_value != KEY_VALUE_HOLD_START) &&
        (key_value != KEY_VALUE_HOLD_BACK) &&
        (key_value != KEY_VALUE_LONG_POWER_AND_BACK) &&
        (key_value != KEY_VALUE_PRESS_DOWN_POWER) &&
        (key_value != KEY_VALUE_PRESS_UP_POWER))
    {
        return -1;
    }

    t_key_value = key_value;
    deal_key_wakeup(t_key_value);

    return ret;
}
#endif

void UserButtonController::init()
{

    initQueue(&t_key_queue);

    int32_t ret = 0;
    //注册光旋钮回调
#ifdef BSP_USING_MT3503
    ret = knob_module_register_callback(user_callback_1);
    if (ret != 0)
    {
        rt_kprintf("knob_module_register_callback error\n");
    }
#endif
    //注册按键回调
    ret = key_module_register_callback(user_callback_2);
    if(ret != 0)
    {
        rt_kprintf("register key_module callback error\n");
    }

    //注册按键调试回调
#if defined(BUTTON_DEBUG_MODE) && (BUTTON_DEBUG_MODE == 1)
    ret = button_debug_register_callback(user_callback_3);
    if(ret != 0)
    {
        rt_kprintf("register button_debug_callback error\n");
    }
#endif
    paused = false;
}

/**
 * @brief  用户按键采样
 *
 * @param key
 * @return true
 * @return false
 */
bool UserButtonController::sample(uint8_t& key)
{
    if(!isEmpty(&t_key_queue))
    {
        uint8_t t;
        dequeue(&t_key_queue, &t);
        if(t != KEY_VALUE_MAX)
        {
            key = keysTransforType(t);
            return true;
        }
    }

    // if (t_key_value != KEY_VALUE_MAX)
    // {
    //     key = keysTransforType(t_key_value);
    //     TRACE_KEY_LOG("sample t_key_value out key: %d\n", key);
    //     t_key_value = KEY_VALUE_MAX;
    //     return true;
    // }
    return false;

//    keys_status key_value;
//    if(key_module_get_event(&key_value))
//    {
    //    uint8_t val;
    //    if(SINGLE_CLICK != key_value.key_event && key_value.key_event != LONG_PRESS_HOLD){
    //        return false;
    //    }
    //    if(key_value.key_event == LONG_PRESS_HOLD)
    //    {
    //        val = key_value.key * (key_value.key_event + 1);
    //        if(val == 0){
    //            val = 1;
    //        }
    //    }else{
    //        val = key_value.key * key_value.key_event;
    //    }
       //key = keysTransforType(t_key_value);
//        key = t_key_value;
       //return true;
//    }
//    return false;
}

void UserButtonController::pause(bool en)
{
    if(paused != en){
        paused = en;
        if(paused){
            //key_module_data_input_pause_control(true, 1);
        }else{
            //key_module_data_input_pause_control(false, 0);
        }
    }
}

} // namespace touchgfx
