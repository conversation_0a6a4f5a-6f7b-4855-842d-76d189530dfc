/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   hr_push.cpp
@Time    :   2025/01/03 19:55:34
*
**************************************************************************/
#include "hr_push.h"
#include <stdio.h>
#include <string.h>
#include "backlight_module/backlight_module.h"
#include "MvcApp.h"

static bool hr_push_en;
static char hr_push_devide[32];
static uint8_t hr_value;
#ifndef SIMULATOR
static rt_timer_t heart_rate_push_timer = RT_NULL;
#endif
// 心率推送相关
bool get_hr_push_en()
{
    return hr_push_en;
}

void set_hr_push_en(bool state)
{
    hr_push_en = state;
}

void set_hr_push_devide(char* devide)
{
    memset(hr_push_devide, 0, sizeof(hr_push_devide));
    snprintf(hr_push_devide, sizeof(hr_push_devide), "%s", devide);
}

char* get_hr_push_devide()
{
    return hr_push_devide;
}

void clear_hr_push_devide()
{
    memset(hr_push_devide, 0, sizeof(hr_push_devide));
}

void notify_hr_push_value(uint8_t value)
{
    hr_value = value;
}

uint8_t get_hr_push_value()
{
    return hr_value;
}

void heart_rate_push_callback(void *parameter)
{
    backlight_open_app(); // 激活背光
    MvcApp::get_mvc_app()->getPageManager()->push("HeartRatePush");
}

void delete_heart_rate_push_timer(void)
{
#ifndef SIMULATOR
    if (heart_rate_push_timer != RT_NULL)
    {
        rt_timer_stop(heart_rate_push_timer);
        rt_timer_delete(heart_rate_push_timer);
        heart_rate_push_timer = RT_NULL;
    }
#endif // !SIMULATOR
}
#ifndef SIMULATOR
rt_err_t update_heart_rate_push_timer(void)
{
    rt_err_t ret = RT_EOK;
    if (heart_rate_push_timer != RT_NULL)
    {
        uint32_t timeout = HEART_RATE_PUSH_DELAY_MS;
        ret = rt_timer_stop(heart_rate_push_timer);
        if (ret != RT_EOK)
        {
            return ret;
        }
        ret = rt_timer_control(heart_rate_push_timer, RT_TIMER_CTRL_SET_TIME, &timeout);
        if (ret != RT_EOK)
        {
            return ret;
        }
        ret = rt_timer_start(heart_rate_push_timer);
        if (ret != RT_EOK)
        {
            return ret;
        }
    }
    else
    {
        heart_rate_push_timer = rt_timer_create("heart_rate_push_timer",
                                    heart_rate_push_callback,
                                    &heart_rate_push_timer,
                                    HEART_RATE_PUSH_DELAY_MS,
                                    RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
        if (heart_rate_push_timer != RT_NULL)
        {
            ret = rt_timer_start(heart_rate_push_timer);
            if (ret != RT_EOK)
            {
                return ret;
            }
        }
    }
    return ret;
}
#endif // !SIMULATOR