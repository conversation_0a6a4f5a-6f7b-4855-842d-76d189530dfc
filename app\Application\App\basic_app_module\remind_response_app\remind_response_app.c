/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   remind_response_app.c
@Time    :   2025/02/07 11:11:23
*
**************************************************************************/

#include "remind_response_app.h"
#include "GUIMsg/MsgBoxService.h"
#include "cfg_header_def.h"
#include "focus_mode_srv/focus_mode_srv.h"
#include "qw_timer.h"
#ifndef SIMULATOR
#include "algo_service_sport_status.h"
#else
#include "activity_record/activity_fit_app.h"

#define ARRAY_SIZE(arr) (sizeof(arr) / sizeof((arr)[0]))
#endif

// 配置参数
#define MAX_INTERRUPT_DEPTH  5       // 最大中断嵌套深度
#define PRIO_NONE           0xFF     // 恢复专用优先级
#define LOCK_TIMEOUT_MS      50       // 互斥锁获取超时时间

// 调试宏
// #define REMIND_RESPONSE_LOG_E(fmt, ...) //rt_kprintf("@@@ERROR: " fmt "\n", ##__VA_ARGS__)  // 实现平台相关的错误日志
// #define REMIND_RESPONSE_LOG_D(fmt, ...) //rt_kprintf("@@@DEBUG: " fmt "\n", ##__VA_ARGS__)  // 实现平台相关的调试日志

// 互斥锁属性配置
// static const osMutexAttr_t mutex_attr = {
//     .name = "remind_mutex",
//     .attr_bits = osMutexRecursive,
// };
#ifndef SIMULATOR
// SOUND_RC02音调资源
static const beep_tone_t rc02_tone_data_1 = {
    .tone_mode = PLAY_TYPE_TONE,   // 音调模式：PLAY_TYPE_TONE
    .duty_cycle = 90,              // 占空比
    .beep = 300,                   // 扬声器响声时间,单位ms
    .interval = 100,               // 扬声器间隔时间,单位ms
    .freq = FREQ_DI,                  // 扬声器频率
};
static const beep_tone_t rc02_tone_data_2 = {
    .tone_mode = PLAY_TYPE_TONE,   // 音调模式：PLAY_TYPE_TONE
    .duty_cycle = 0,               // 占空比
    .beep = 0,                     // 扬声器响声时间,单位ms
    .interval = 400,               // 扬声器间隔时间,单位ms
    .freq = 0,                     // 扬声器频率
};
// 混合编排数组资源
static const voice_t rc02_voice_data[] = {
    {
        .tone = &rc02_tone_data_1,
    },
    {
        .tone = &rc02_tone_data_1,
    },
    {
        .tone = &rc02_tone_data_1,
    },
    {
        .tone = &rc02_tone_data_2,
    },
};

// 开机重启铃声
static const beep_note_t rl03_score[] = {
    {M1, TT / 4},
    {M1, TT / 4},
    {M5, TT / 4},
    {0, 0},
};
// 关机铃声
static const beep_note_t rl04_score[] = {
    {M1, TT / 4},
    {M3, TT / 4},
    {M5, TT / 4},
    {0, 0},
};

// 偏航铃声
static const beep_note_t rc03_score[] = {
    {M1, TT / 4},
    {M6, TT / 4},
    {M5, TT / 4},
    {M3, TT / 4},
    {0, 0},
};
//短振
static const beep_tone_t c03_tone_data_1 = {
    .tone_mode = PLAY_TYPE_TONE,   // 音调模式：PLAY_TYPE_TONE
    .duty_cycle = 90,              // 占空比
    .beep = 100,                   // 扬声器响声时间,单位ms
    .interval = 200,               // 扬声器间隔时间,单位ms
    .freq = FREQ_DI,               // 扬声器频率
};
// 混合编排数组资源
static const voice_t c03_voice_data[] = {
    {
        .tone = &c03_tone_data_1,
    },
    {
        .tone = &c03_tone_data_1,
    },
    {
        .tone = &c03_tone_data_1,
    },
};

// 导航结束铃声
static const beep_note_t rc04_score[] = {
    {M1, TT / 4},
    {M3, TT / 4},
    {M6, TT / 4},
    {M3, TT / 4},
    {M5, TT / 4},
    {M1, TT / 4},
    {0, 0},
};
//长振
static const beep_tone_t c04_tone_data_1 = {
    .tone_mode = PLAY_TYPE_TONE,   // 音调模式：PLAY_TYPE_TONE
    .duty_cycle = 90,              // 占空比
    .beep = 1000,                   // 扬声器响声时间,单位ms
    .interval = 200,               // 扬声器间隔时间,单位ms
    .freq = FREQ_DI,               // 扬声器频率
};

// 混合编排数组资源
static const voice_t c04_voice_data[] = {
    {
        .tone = &c04_tone_data_1,
    },
    {
        .tone = &c03_tone_data_1,
    },
    {
        .tone = &c03_tone_data_1,
    },
};

const beep_work_param_t beep_profiles[SOUND_MAX] = {

    [SOUND_RS01] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DI, .duty_cycle = 90, .beep = 100, .interval =   0, },    //  100
    [SOUND_RS02] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DI, .duty_cycle = 90, .beep = 300, .interval =   0, },    //  300
    [SOUND_RS03] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS04] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS05] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS06] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS07] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS08] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS09] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS10] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS11] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS12] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS13] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS14] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS15] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS16] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS17] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS18] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS19] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS20] = { .voltage_multiplier = 3, .repeat = 0x02, .freq = FREQ_DI,   .duty_cycle = 90, .beep = 200, .interval = 100, },  
    [SOUND_RS21] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS22] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS23] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS24] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RS25] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval =   0, },  
    [SOUND_RM01] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 500, .interval =   0, },  
    [SOUND_RM02] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 500, .interval =   0, },  
    [SOUND_RM03] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 500, .interval =   0, },  
    [SOUND_RM04] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 500, .interval =   0, },  
    [SOUND_RM05] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 500, .interval =   0, },  
    [SOUND_RM06] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 500, .interval =   0, },  
    [SOUND_RM07] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 500, .interval =   0, },  
    [SOUND_RM08] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 500, .interval =   0, },  
    [SOUND_RM09] = { .voltage_multiplier = 3, .repeat = 0x01,                                                                        // 2000  需要生成资源
                     .music_mode = PLAY_TYPE_MUSIC,                          // 曲谱模式：PLAY_TYPE_MUSIC
                     .play_speed = 10,                                       // 曲谱播放速度  play_speed *200 ms 和 TT 同意义
                     .music_data = rl03_score,                               // 曲谱地址
                     .music_size = sizeof(rl03_score) / sizeof(beep_note_t), // 曲谱数据大小
                   },  
    [SOUND_RM10] = { .voltage_multiplier = 3, .repeat = 0x01, .freq = FREQ_DING, .duty_cycle = 90, .beep = 500, .interval =   0, },  
    [SOUND_RC01] = { .voltage_multiplier = 3, .repeat = 0x11, .freq = FREQ_DING, .duty_cycle = 90, .beep = 200, .interval = 100, },  
    [SOUND_RC02] = { .voltage_multiplier = 3, .repeat = 0x09, .freq = FREQ_DING, .duty_cycle = 90, .beep = 500, .interval = 100, },  
    [SOUND_RC03] = { .voltage_multiplier = 3, .repeat = 0x0D, .freq = FREQ_DING, .duty_cycle = 90, .beep = 300, .interval = 100, },  

    // [SOUND_RL01] = { .voltage_multiplier = 3, .repeat = 0x02, .freq = FREQ_DI, .duty_cycle = 90, .beep = 200, .interval = 100, },    //  600
    // [SOUND_RL02] = { .voltage_multiplier = 3, .repeat = 0x03, .freq = FREQ_DI, .duty_cycle = 90, .beep = 200, .interval = 100, },    //  900
    // [SOUND_RL03] = { .voltage_multiplier = 3, .repeat = 0x01,                                                                        // 2000  需要生成资源
    //                  .music_mode = PLAY_TYPE_MUSIC,                          // 曲谱模式：PLAY_TYPE_MUSIC
    //                  .play_speed = 10,                                       // 曲谱播放速度  play_speed *200 ms 和 TT 同意义
    //                  .music_data = rl03_score,                               // 曲谱地址
    //                  .music_size = sizeof(rl03_score) / sizeof(beep_note_t), // 曲谱数据大小
    //                },
    // [SOUND_RL04] = { .voltage_multiplier = 3, .repeat = 0x01,                                                                        // 1500  需要生成资源
    //                  .music_mode = PLAY_TYPE_MUSIC,                          // 曲谱模式：PLAY_TYPE_MUSIC
    //                  .play_speed = 10,                                       // 曲谱播放速度  play_speed *200 ms 和 TT 同意义
    //                  .music_data = rl04_score,                               // 曲谱地址
    //                  .music_size = sizeof(rl04_score) / sizeof(beep_note_t), // 曲谱数据大小
    //                },
    // [SOUND_RC01] = { .voltage_multiplier = 3, .repeat = 0xFF, .freq = FREQ_DI, .duty_cycle = 90, .beep = 600, .interval = 300, },    //  900
    // [SOUND_RC02] = { .voltage_multiplier = 3, .repeat = 0xFF,                                                                        //  1600
    //                  .voice_mode = PLAY_TYPE_VOICE,                          // 混排模式：PLAY_TYPE_VOICE
    //                  .gap = 0,                                               // 混排混排的间隔,单位ms
    //                  .voice_data = rc02_voice_data,                          // 混合声音地址
    //                  .array_num = sizeof(rc02_voice_data)/sizeof(voice_t),   // 混排music和tone的总个数
    //                },
    // [SOUND_RC03] = { .voltage_multiplier = 3, .repeat = 0xFF,                                                                        // 2000  需要生成资源
    //                  .music_mode = PLAY_TYPE_MUSIC,                          // 曲谱模式：PLAY_TYPE_MUSIC
    //                  .play_speed = 10,                                       // 曲谱播放速度  play_speed *200 ms 和 TT 同意义
    //                  .music_data = rc03_score,                               // 曲谱地址
    //                  .music_size = sizeof(rc03_score) / sizeof(beep_note_t), // 曲谱数据大小
    //                },
    // [SOUND_RC04] = { .voltage_multiplier = 3, .repeat = 0xFF,                                                                        // 3000  需要生成资源
    //                  .music_mode = PLAY_TYPE_MUSIC,                          // 曲谱模式：PLAY_TYPE_MUSIC
    //                  .play_speed = 10,                                       // 曲谱播放速度  play_speed *200 ms 和 TT 同意义
    //                  .music_data = rc04_score,                               // 曲谱地址
    //                  .music_size = sizeof(rc04_score) / sizeof(beep_note_t), // 曲谱数据大小
    //                },
};

const motor_work_param_t motor_profiles[VIB_MAX] = {

    [VIB_S01] = { .voltage_multiplier = 2, .repeat = 0x01, .freq = FREQ_DI, .duty_cycle = 90, .beep = 100, .interval = 0, },      
    [VIB_S02] = { .voltage_multiplier = 2, .repeat = 0x01, .freq = FREQ_DI, .duty_cycle = 90, .beep = 200, .interval = 0, },      
    [VIB_S03] = { .voltage_multiplier = 2, .repeat = 0x01, .freq = FREQ_DI, .duty_cycle = 90, .beep = 300, .interval = 0, },      
    [VIB_M01] = { .voltage_multiplier = 3, .repeat = 0x02, .freq = FREQ_DI, .duty_cycle = 90, .beep = 200, .interval = 100, },    
    [VIB_M02] = { .voltage_multiplier = 3, .repeat = 0x02, .freq = FREQ_DI, .duty_cycle = 90, .beep = 400, .interval = 100, },    
    [VIB_M03] = { .voltage_multiplier = 3, .repeat = 0x03, .freq = FREQ_DI, .duty_cycle = 90, .beep = 200, .interval = 100, },      
    [VIB_L01] = { .voltage_multiplier = 2, .repeat = 0x01, .freq = FREQ_DI, .duty_cycle = 90, .beep = 500, .interval = 0, },      
    [VIB_L02] = { .voltage_multiplier = 2, .repeat = 0x01, .freq = FREQ_DI, .duty_cycle = 90, .beep = 1000, .interval = 0, },      
    [VIB_C01] = { .voltage_multiplier = 2, .repeat = 0x11, .freq = FREQ_DI, .duty_cycle = 90, .beep = 200, .interval = 100, },      
    [VIB_C02] = { .voltage_multiplier = 2, .repeat = 0x09, .freq = FREQ_DI, .duty_cycle = 90, .beep = 500, .interval = 100, },      
    [VIB_C03] = { .voltage_multiplier = 2, .repeat = 0x0D, .freq = FREQ_DI, .duty_cycle = 90, .beep = 300, .interval = 100, },      
    // [VIB_C02] = { .voltage_multiplier = 3, .repeat = 0x03,                                                                          //  1600
    //               .voice_mode = PLAY_TYPE_VOICE,                            // 混排模式：PLAY_TYPE_VOICE
    //               .gap = 0,                                                 // 混排混排的间隔,单位ms
    //               .voice_data = rc02_voice_data,                            // 混合声音地址
    //               .array_num = sizeof(rc02_voice_data)/sizeof(voice_t),     // 混排music和tone的总个数
    //             },
    // [VIB_C03] = { .voltage_multiplier = 3, .repeat = 0x03,                                                                          //   900
    //               //.music_mode = PLAY_TYPE_MUSIC,                          // 曲谱模式：PLAY_TYPE_MUSIC
    //               //.play_speed = 10,                                       // 曲谱播放速度  play_speed *200 ms 和 TT 同意义
    //               //.music_data = rc03_score,                               // 曲谱地址
    //               //.music_size = sizeof(rc03_score) / sizeof(beep_note_t), // 曲谱数据大小
    //               .voice_mode = PLAY_TYPE_VOICE,                            // 混排模式：PLAY_TYPE_VOICE
    //               .gap = 0,                                                 // 混排混排的间隔,单位ms
    //               .voice_data = c03_voice_data,                             // 混合声音地址
    //               .array_num = sizeof(c03_voice_data)/sizeof(voice_t),      // 混排music和tone的总个数
    //             },
    // [VIB_C04] = { .voltage_multiplier = 3, .repeat = 0xFF,                                                                          //  1800
    //               //.music_mode = PLAY_TYPE_MUSIC,                          // 曲谱模式：PLAY_TYPE_MUSIC
    //               //.play_speed = 10,                                       // 曲谱播放速度  play_speed *200 ms 和 TT 同意义
    //               //.music_data = rc04_score,                               // 曲谱地址
    //               //.music_size = sizeof(rc04_score) / sizeof(beep_note_t), // 曲谱数据大小
    //               .voice_mode = PLAY_TYPE_VOICE,                            // 混排模式：PLAY_TYPE_VOICE
    //               .gap = 0,                                                 // 混排混排的间隔,单位ms
    //               .voice_data = c04_voice_data,                             // 混合声音地址
    //               .array_num = sizeof(c04_voice_data)/sizeof(voice_t),      // 混排music和tone的总个数
    //             },
};

const int beep_time[SOUND_MAX] = {
    [SOUND_RS01] = 100,
    [SOUND_RS02] = 300,
    [SOUND_RS03] = 300,
    [SOUND_RS04] = 300,
    [SOUND_RS05] = 300,
    [SOUND_RS06] = 300,
    [SOUND_RS07] = 300,
    [SOUND_RS08] = 300,
    [SOUND_RS09] = 300,
    [SOUND_RS10] = 300,
    [SOUND_RS11] = 300,
    [SOUND_RS12] = 300,
    [SOUND_RS13] = 300,
    [SOUND_RS14] = 300,
    [SOUND_RS15] = 300,
    [SOUND_RS16] = 300,
    [SOUND_RS17] = 300,
    [SOUND_RS18] = 300,
    [SOUND_RS19] = 300,
    [SOUND_RS20] = 300,
    [SOUND_RS21] = 300,
    [SOUND_RS22] = 300,
    [SOUND_RS23] = 300,
    [SOUND_RS24] = 300,
    [SOUND_RS25] = 300,
    [SOUND_RM01] = 500,
    [SOUND_RM02] = 500,
    [SOUND_RM03] = 500,
    [SOUND_RM04] = 500,
    [SOUND_RM05] = 500,
    [SOUND_RM06] = 500,
    [SOUND_RM07] = 500,
    [SOUND_RM08] = 500,
    [SOUND_RM09] = 500,
    [SOUND_RM10] = 500,
    [SOUND_RC01] = ((200 + 100) * 0x11),
    [SOUND_RC02] = ((500 + 100) * 0x09),
    [SOUND_RC03] = ((300 + 100) * 0x0D),
};

const int motor_time[VIB_MAX] = {
    [VIB_S01] = 100,
    [VIB_S02] = 200,
    [VIB_S03] = 300,
    [VIB_M01] = 500,
    [VIB_M02] = 900,
    [VIB_M03] = 800,
    [VIB_L01] = 500,
    [VIB_L02] = 1000,
    [VIB_C01] = ((200 + 100) * 0x11),
    [VIB_C02] = ((500 + 100) * 0x09),
    [VIB_C03] = ((300 + 100) * 0x0D),
};

static rt_device_t beep_device = RT_NULL;   // 蜂鸣器
static rt_device_t motor_device = RT_NULL;  // 马达

#endif   // SIMULATOR

// REMIND_TYPE默认配置
const ResponseMapping g_base_mapping[] = {
    [(uint32_t)RRT_B_BTN - RRT_BASE] =                {PRIO_USER_INTERACTION, VIB_S01, SOUND_RS01, 0x01, 0},
    [(uint32_t)RRT_B_KNOB - RRT_BASE] =               {PRIO_USER_INTERACTION, VIB_S01, SOUND_NONE, 0x01, 0},
    [(uint32_t)RRT_B_HOLD_TOUCH_DIAL - RRT_BASE] =    {PRIO_USER_INTERACTION, VIB_S01, SOUND_NONE, 0x01, 0},
    [(uint32_t)RRT_S_POWER_ON - RRT_BASE] =           {PRIO_SYS_OPERATE, VIB_S01, SOUND_RM09, 0x01, 0},
    [(uint32_t)RRT_S_MANUAL_SHUTDOWN - RRT_BASE] =    {PRIO_SYS_OPERATE, VIB_S01, SOUND_RM10, 0x01, 0},
    [(uint32_t)RRT_S_AUTO_SHUTDOWN - RRT_BASE] =      {PRIO_SYS_OPERATE, VIB_S01, SOUND_NONE, 0x01, 0},
    [(uint32_t)RRT_S_REBOOT - RRT_BASE] =             {PRIO_SYS_OPERATE, VIB_S01, SOUND_RM09, 0x01, 0},
    [(uint32_t)RRT_S_BIND_REQUEST - RRT_BASE] =       {PRIO_SYS_OPERATE, VIB_S01, SOUND_NONE, 0x01, 0},
    [(uint32_t)RRT_S_OTA_REQUEST - RRT_BASE] =        {PRIO_SYS_OPERATE, VIB_S01, SOUND_RS02, 0x01, 0},
    [(uint32_t)RRT_S_SHUTDOWN_COUNTDOWN - RRT_BASE] = {PRIO_SYS_OPERATE, VIB_S01, SOUND_NONE, 0x01, 0},
    [(uint32_t)RRT_A_HEART_OUT - RRT_BASE] =          {PRIO_SYS_OPERATE, VIB_S01, SOUND_RS04, 0x01, 0},
    [(uint32_t)RRT_A_SPO2_OUT - RRT_BASE] =           {PRIO_SYS_OPERATE, VIB_S01, SOUND_RS04, 0x01, 0},
    [(uint32_t)RRT_A_STRESS_OUT - RRT_BASE] =         {PRIO_SYS_OPERATE, VIB_S01, SOUND_RS04, 0x01, 0},
    [(uint32_t)RRT_S_START_COUNTDOWN - RRT_BASE] =    {PRIO_SYS_OPERATE, VIB_S01, SOUND_RS08, 0x01, 0},
    [(uint32_t)RRT_S_OUT_COUNT_DOWN - RRT_BASE] =     {PRIO_SYS_OPERATE, VIB_S01, SOUND_RS08, 0x01, 0},
    [(uint32_t)RRT_S_START - RRT_BASE] =              {PRIO_SYS_OPERATE, VIB_L02, SOUND_RS20, 0x01, 1000},
    [(uint32_t)RRT_S_AUTO_PAUSE - RRT_BASE] =         {PRIO_SYS_OPERATE, VIB_L02, SOUND_RS21, 0x01, 0},
    [(uint32_t)RRT_S_RESUME - RRT_BASE] =             {PRIO_SYS_OPERATE, VIB_L02, SOUND_RS20, 0x01, 0},
    [(uint32_t)RRT_S_MANUAL_PAUSE - RRT_BASE] =       {PRIO_SYS_OPERATE, VIB_L02, SOUND_RS21, 0x01, 0},
    [(uint32_t)RRT_S_GPS_LOACL_SUC - RRT_BASE] =      {PRIO_SYS_OPERATE, VIB_S02, SOUND_RS18, 0x01, 0},
    [(uint32_t)RRT_S_TRAIN_STEP_END - RRT_BASE] =     {PRIO_SYS_OPERATE, VIB_L01, SOUND_RS22, 0x01, 0},
    [(uint32_t)RRT_S_TRIATHLON_CHANGE - RRT_BASE] =   {PRIO_SYS_OPERATE, VIB_L01, SOUND_RS22, 0x01, 0},
    [(uint32_t)RRT_METRONOME - RRT_BASE] =            {PRIO_SYS_OPERATE, VIB_S01, SOUND_RS10, 0xFF, 0},
    [(uint32_t)RRT_TOOL_METRONOME - RRT_BASE] =       {PRIO_SYS_OPERATE, VIB_S01, SOUND_RS10, 0xFF, 0},
    [(uint32_t)RRT_TOOL_BREATH_TRAIN - RRT_BASE] =    {PRIO_SYS_OPERATE, VIB_S01, SOUND_NONE, 0x01, 0},
    [(uint32_t)RRT_BREATH_TRAIN_OVER - RRT_BASE] =    {PRIO_SYS_OPERATE, VIB_L02, SOUND_RS24, 0x01, 0},
    [(uint32_t)RRT_PHOTO_COUNTDOWN - RRT_BASE] =      {PRIO_SYS_OPERATE, VIB_S01, SOUND_RS08, 0x01, 0},
    [(uint32_t)RRT_PHOTO_SHUTTER - RRT_BASE] =        {PRIO_SYS_OPERATE, VIB_S01, SOUND_RS09, 0x01, 0},
    [(uint32_t)RRT_DIAL_UPGRADE - RRT_BASE] =         {PRIO_SYS_OPERATE, VIB_S01, SOUND_RS04, 0x01, 0},
};

// GUI_MSGBOX默认配置
const ResponseMapping g_msgbox_mapping[] = {
    [enumPOPUP_ALARM_CLOCK] = {PRIO_MSG_BOX, VIB_C02, SOUND_RC02, 0xFF, 0},
    [enumPOPUP_TIMER_CLOCK] = {PRIO_MSG_BOX, VIB_C01, SOUND_RC01, 0xFF, 0},
    [enumPOPUP_HEALTH_GOAL] = {PRIO_MSG_BOX, VIB_M01, SOUND_RS05, 0x01, 0},
    [enumPOPUP_HEALTH_SEDENTARY] = {PRIO_MSG_BOX, VIB_S01, SOUND_RS04, 0x01, 0},
    [enumPOPUP_SENSOR_CONNECT_NOT_IN_MOTION] = {PRIO_MSG_BOX, VIB_S01, SOUND_RS13, 0x01, 0},
    [enumPOPUP_SENSOR_DISCONNECT_NOT_IN_MOTION] = {PRIO_MSG_BOX, VIB_S03, SOUND_RS14, 0x01, 0},
    [enumPOPUP_SENSOR_CONNECT_IN_MOTION] = {PRIO_MSG_BOX, VIB_S01, SOUND_RS13, 0x01, 0},
    [enumPOPUP_SENSOR_DISCONNECT_IN_MOTION] = {PRIO_MSG_BOX, VIB_S03, SOUND_RS15, 0x01, 0},
    [enumPOPUP_SPORT_REMIND] = {PRIO_MSG_BOX, VIB_M02, SOUND_RM06, 0x01, 0},
    [enumPOPUP_GIVE_UP_SPORT] = {PRIO_MSG_BOX, VIB_L02, SOUND_RS23, 0x01, 0},
    [enumPOPUP_SPORTING_LAP] = {PRIO_MSG_BOX, VIB_L01, SOUND_RS22, 0x01, 0},
    [enumPOPUP_CHARGE_REMINDER] = {PRIO_MSG_BOX, VIB_NONE, SOUND_RS02, 0x01, 0},
    [enumPOPUP_UNDER_VOLTAGE_TEN] = {PRIO_MSG_BOX, VIB_M01, SOUND_RS03, 0x01, 0},
    [enumPOPUP_UNDER_VOLTAGE_TWENTY] = {PRIO_MSG_BOX, VIB_M01, SOUND_RS03, 0x01, 0},
    [enumPOPUP_UNDER_VOLTAGE_OFF] = {PRIO_MSG_BOX, VIB_M01, SOUND_NONE, 0x01, 0},
    [enumPOPUP_SAVE_RECORD] = {PRIO_MSG_BOX, VIB_L02, SOUND_RS24, 0x01, 0},
    [enumPOPUP_SENSOR_LOW_POWER] = {PRIO_MSG_BOX, VIB_S02, SOUND_RS16, 0x01, 0},
    [enumPOPUP_SENSOR_CONN_REACHED_LIMIT] = {PRIO_MSG_BOX, VIB_S02, SOUND_RS17, 0x01, 0},
    [enumPOPUP_HEART_RATE] = {PRIO_MSG_BOX, VIB_S03, SOUND_RS06, 0x01, 0},
    [enumPOPUP_HEART_RATE_NOTICE] = {PRIO_MSG_BOX, VIB_M03, SOUND_RS07, 0x01, 0},
    [enumPOPUP_SPO2] = {PRIO_MSG_BOX, VIB_S03, SOUND_RS06, 0x01, 0},
    [enumPOPUP_PRESSURE] = {PRIO_MSG_BOX, VIB_S03, SOUND_RS06, 0x01, 0},
    [enumPOPUP_PRESSURE_REMIND] = {PRIO_MSG_BOX, VIB_M03, SOUND_RS07, 0x01, 0},
    [enumPOPUP_SLEEP_BED] = {PRIO_MSG_BOX, VIB_S01, SOUND_RS04, 0x01, 0},
    [enumPOPUP_INTELLIGENT_NOTIFY] = {PRIO_MSG_BOX, VIB_S02, SOUND_RS25, 0x01, 0},
    [enumPOPUP_INCOMING_CALL] = {PRIO_MSG_BOX, VIB_C03, SOUND_RC03, 0x04, 0},
    [enumPOPUP_SPORTING_GPS_LOCATE] = {PRIO_MSG_BOX, VIB_S02, SOUND_RS18, 0x01, 0},
    [enumPOPUP_SPORTING_GPS_LOSS] = {PRIO_MSG_BOX, VIB_S02, SOUND_RS19, 0x01, 0},
    [enumPOPUP_BIND_WATCH_SUCC] = {PRIO_MSG_BOX, VIB_S01, SOUND_NONE, 0x01, 0},
    [enumPOPUP_BIND_WATCH_FAILED] = {PRIO_MSG_BOX, VIB_S03, SOUND_NONE, 0x01, 0},
    [enumPOPUP_OTA_FINISH_SUCCESS] = {PRIO_MSG_BOX, VIB_S01, SOUND_RS02, 0x01, 0},
    [enumPOPUP_OTA_FINISH_FAILED] = {PRIO_MSG_BOX, VIB_S03, SOUND_NONE, 0x01, 0},
    [enumPOPUP_FIND_WATCH] = {PRIO_MSG_BOX, VIB_C03, SOUND_RC03, 0xFF, 0},
    [enumPOPUP_DRINK_WATER] = {PRIO_MSG_BOX, VIB_S01, SOUND_RS04, 0x01, 0},
    [enumPOPUP_DATA_SYNC] = {PRIO_MSG_BOX, VIB_S01, SOUND_RS04, 0x01, 0},
    [enumPOPUP_DIAL_UPGRADE_FAILED] = {PRIO_MSG_BOX, VIB_S03, SOUND_RS12, 0x01, 0},
    [enumPOPUP_NAVI_START] = {PRIO_MSG_BOX, VIB_L02, SOUND_RM01, 0x01, 0},
    [enumPOPUP_NAVI_YAW] = {PRIO_MSG_BOX, VIB_M02, SOUND_RM03, 0x01, 0},
    [enumPOPUP_NAVI_RESTORE_ROUTE] = {PRIO_MSG_BOX, VIB_M02, SOUND_RM04, 0x01, 0},
    [enumPOPUP_NAVI_CHANGE_DIR] = {PRIO_MSG_BOX, VIB_M03, SOUND_RM05, 0x01, 0},
    [enumPOPUP_NAVI_END] = {PRIO_MSG_BOX, VIB_L02, SOUND_RM02, 0x01, 0},
    [enumPOPUP_PAY_SUCCESS] = {PRIO_MSG_BOX, VIB_M01, SOUND_RS11, 0x01, 0},
    [enumPOPUP_PAY_FAILED] = {PRIO_MSG_BOX, VIB_S03, SOUND_RS12, 0x01, 0},
    [enumPOPUP_SENSOR_DISCONNECTION] = {PRIO_MSG_BOX, VIB_S03, SOUND_RS15, 0x01, 0},
    [enumPOPUP_MAX_HR_UPDATE] = {PRIO_MSG_BOX, VIB_S01, SOUND_RS04, 0x01, 0},
    [enumPOPUP_LT_HR_UPDATE] = {PRIO_MSG_BOX, VIB_S01, SOUND_RS04, 0x01, 0},
    [enumPOPUP_FTP_UPDATE] = {PRIO_MSG_BOX, VIB_S01, SOUND_RS04, 0x01, 0},
    [enumPOPUP_ACH_UPDATE] = {PRIO_MSG_BOX, VIB_M01, SOUND_RM07, 0x01, 0},
    [enumPOPUP_TRAIN_COMPLETED] = {PRIO_MSG_BOX, VIB_M01, SOUND_RM05, 0x01, 0},

};

// 播放上下文结构
typedef struct {
    UnifiedSceneID scene_id;     // 场景ID
    ResponseMapping config;      // 当前配置
    int64_t time_stamp;         // 时间戳
    uint8_t repeat_counter;      // 剩余重复次数
    bool is_active;              // 激活状态标志
} PlaybackContext;

// 全局状态管理
static struct {
    PlaybackContext current;                // 当前播放上下文
    PlaybackContext interrupt_stack[MAX_INTERRUPT_DEPTH]; // 中断栈
    uint8_t stack_top;                      // 栈顶指针
    qw_timer timer;                         // 重复定时器
    osMutexId_t mutex;                      // 互斥锁
} playback_state = {0};

static void (*tool_metronome_callback)(void) = NULL;

/* 内部函数声明 */
static void repeat_timer_callback(void* parameter);
static bool lock_playback(void);
static void unlock_playback(void);
static void stop_all_output_locked(void);
static bool save_context(PlaybackContext* info);
static bool restore_previous_context(void);
static void restore_playback_logic(void);
void buzzer_vibrator_play(UnifiedSceneID scene_id, ResponseMapping* info);

const char* os_error_to_str(osStatus_t status)
{
    switch(status) {
        case osOK: return "OK";
        case osErrorTimeout: return "Timeout";
        case osErrorResource: return "Resource not available";
        case osErrorParameter: return "Invalid parameter";
        // ...其他错误码...
        default: return "Unknown error";
    }
}

/**
 * @brief 初始化提醒响应模块
 */
void remind_response_init(void)
{
    // 初始化状态
    memset(&playback_state, 0, sizeof(playback_state));
    playback_state.current.is_active = false;

    // 创建互斥锁
    // osMutexAttr_t mutex_attr_ = {0};
    // mutex_attr_.name = "remind_mutex";
    // mutex_attr_.attr_bits = osMutexRecursive;
    // playback_state.mutex = osMutexNew(&mutex_attr_);
    // if(!playback_state.mutex)
    // {
    //     REMIND_RESPONSE_LOG_E("Mutex creation failed! ");
    //     return;
    // }

    // 创建定时器
    qw_timer_init(&playback_state.timer, QW_TIMER_FLAG_ONE_SHOT | QW_TIMER_FLAG_SOFT_TIMER, repeat_timer_callback);

#ifndef SIMULATOR
    // 查找设备
    beep_device = rt_device_find("beep");
    if(beep_device)
    {
        rt_err_t ret = rt_device_open(beep_device, RT_DEVICE_FLAG_WRONLY);
        if ((ret == RT_EOK)||(ret == (-RT_EBUSY)))
        {
            REMIND_RESPONSE_LOG_I("rt_device_open beep ok...\n");
        }
        else
        {
            REMIND_RESPONSE_LOG_E("rt_device_open beep failed...\n");
        }
    }

    motor_device = rt_device_find("motor");
    if(motor_device)
    {
        rt_err_t ret = rt_device_open(motor_device, RT_DEVICE_FLAG_WRONLY);
        if ((ret == RT_EOK)||(ret == (-RT_EBUSY)))
        {
            REMIND_RESPONSE_LOG_I("rt_device_open motor ok...\n");
        }
        else
        {
            REMIND_RESPONSE_LOG_E("rt_device_open motor failed...\n");
        }
    }
#endif

    REMIND_RESPONSE_LOG_D("Remind response module initialized");
}

/**
 * @brief 带锁保护的停止输出
 */
static void stop_all_output_locked(void)
{
#ifndef SIMULATOR
    // 停止播放
    if(beep_device)
    {
        if(RT_EOK != rt_device_control(beep_device, RT_DEVICE_CTRL_STOP, NULL))
        {
            REMIND_RESPONSE_LOG_E("rt_device_control failed...\n");
        }
    }
    if(motor_device)
    {
        if(RT_EOK != rt_device_control(motor_device, RT_DEVICE_CTRL_STOP, NULL))
        {
            REMIND_RESPONSE_LOG_E("rt_device_control failed...\n");
        }
    }
#endif
    qw_timer_stop(&playback_state.timer);
    playback_state.current.is_active = false;
}

/**
 * @brief 获取互斥锁（带超时和错误处理）
 */
static bool lock_playback(void)
{
    // osStatus_t status = osMutexAcquire(playback_state.mutex, osWaitForever);
    // if(status != osOK)
    // {
    //     REMIND_RESPONSE_LOG_E("Lock failed: %d", status);
    //     return false;
    // }
    return true;
}

/**
 * @brief 释放互斥锁
 */
static void unlock_playback(void)
{
    // osMutexRelease(playback_state.mutex);
}

/**
 * @brief 保存当前上下文到中断栈
 */
static bool save_context(PlaybackContext* info)
{
    if(info == NULL)
    {
        REMIND_RESPONSE_LOG_E("save info is NULL");
        return false;
    }

    // 查找插入位置：高优先级在前，同优先级时直接覆盖
    int insert_pos = 0;
    while(insert_pos < playback_state.stack_top &&
          playback_state.interrupt_stack[insert_pos].config.priority > info->config.priority)
    {
        insert_pos++;
    }

    // 栈未满时插入
    if (playback_state.stack_top < MAX_INTERRUPT_DEPTH)
    {
        if (insert_pos < playback_state.stack_top)
        {
            memmove(&playback_state.interrupt_stack[insert_pos + 1],
                    &playback_state.interrupt_stack[insert_pos],
                    (playback_state.stack_top - insert_pos) * sizeof(PlaybackContext));
        }
        if(info->repeat_counter == 0xFF)
        {
            info->time_stamp = get_boot_msec();
        }
        else
        {
            info->time_stamp = 0;
        }
        memcpy(&playback_state.interrupt_stack[insert_pos], info, sizeof(PlaybackContext));
        playback_state.stack_top++;
    }
    // 栈已满时，只有新消息优先级不低于栈中某消息时才插入
    else if (insert_pos < MAX_INTERRUPT_DEPTH)
    {
        memmove(&playback_state.interrupt_stack[insert_pos + 1],
                &playback_state.interrupt_stack[insert_pos],
                (MAX_INTERRUPT_DEPTH - insert_pos - 1) * sizeof(PlaybackContext));
        if(info->repeat_counter == 0xFF)
        {
            info->time_stamp = get_boot_msec();
        }
        else
        {
            info->time_stamp = 0;
        }
        memcpy(&playback_state.interrupt_stack[insert_pos], info, sizeof(PlaybackContext));
    }
    else
    {
        REMIND_RESPONSE_LOG_D("Stack full, priority too low");
        return false;
    }

    REMIND_RESPONSE_LOG_I("saved at pos:%d, stack depth:%d", insert_pos, playback_state.stack_top);
    return true;
}

/**
 * @brief 从栈恢复上一个上下文
 */
static bool restore_previous_context(void)
{
    if(playback_state.stack_top == 0)
    {
        REMIND_RESPONSE_LOG_E("restore interrupt stack empty");
        return false;
    }

    // 查找最高优先级的上下文
    uint8_t highest_index = 0;
    uint8_t highest_prio = playback_state.interrupt_stack[0].config.priority;

    for(int i = 1; i < playback_state.stack_top; i++) {
        if(playback_state.interrupt_stack[i].config.priority > highest_prio) {
            highest_prio = playback_state.interrupt_stack[i].config.priority;
            highest_index = i;
        }
    }

    // 取出最高优先级的上下文
    memcpy(&playback_state.current,
          &playback_state.interrupt_stack[highest_index],
          sizeof(PlaybackContext));

    // 压缩栈空间
    if(highest_index != playback_state.stack_top - 1) {
        memmove(&playback_state.interrupt_stack[highest_index],
                &playback_state.interrupt_stack[highest_index+1],
                (playback_state.stack_top - highest_index - 1) * sizeof(PlaybackContext));
    }
    // 清除最后一个元素
    memset(&playback_state.interrupt_stack[playback_state.stack_top - 1], 0, sizeof(PlaybackContext));
    playback_state.stack_top--;

    REMIND_RESPONSE_LOG_I("restored prio:%d from index:%d", highest_prio, highest_index);
    return true;
}

static bool deal_repeat_remind(EventData* eventData)
{
    if(!lock_playback()) return false;

    // 更新计数器（无限循环除外）
    if(playback_state.current.config.repeat_count != 0xFF)
    {
        if(playback_state.current.repeat_counter > 0)
        {
            playback_state.current.repeat_counter--;
        }
    }

    // 检查是否需要继续播放
    if(playback_state.current.is_active &&
      ((playback_state.current.repeat_counter > 0) ||
       (playback_state.current.config.repeat_count == 0xFF)))
    {
        // 执行重复播放
        if(playback_state.current.scene_id.value == RRT_TOOL_METRONOME && tool_metronome_callback)
        {
            tool_metronome_callback();
        }
#ifndef SIMULATOR
        if((playback_state.current.config.sound_id != SOUND_NONE)&&(playback_state.current.config.sound_id < SOUND_MAX))
        {
            // 开始播放
            if(beep_device)
            {
                if(RT_EOK != rt_device_control(beep_device, RT_DEVICE_CTRL_START, (void *)&beep_profiles[playback_state.current.config.sound_id]))
                {
                    REMIND_RESPONSE_LOG_E("rt_device_control failed...\n");
                }
            }
        }
        if((playback_state.current.config.vibrate_id != VIB_NONE)&&(playback_state.current.config.vibrate_id < VIB_MAX))
        {
            // 开始播放
            if(motor_device)
            {
                if(RT_EOK != rt_device_control(motor_device, RT_DEVICE_CTRL_START, (void *)&motor_profiles[playback_state.current.config.vibrate_id]))
                {
                    REMIND_RESPONSE_LOG_E("rt_device_control failed...\n");
                }
            }
        }
#endif
        // 重启定时器
        qw_timer_start(&playback_state.timer,
                       playback_state.current.config.interval_time, NULL, "remind_response_app");
    }
    else
    {
        REMIND_RESPONSE_LOG_I("repeat timer expired");
        // 标记播放结束
        playback_state.current.is_active = false;

        // 触发恢复检查（需要先解锁）
        unlock_playback();
        ResponseMapping restore_cmd = {.priority = PRIO_NONE};
        buzzer_vibrator_play((UnifiedSceneID){0}, &restore_cmd);
        return true;
    }

    return true;
    unlock_playback();
}

/**
 * @brief 定时器回调（带锁保护）
 */
static void repeat_timer_callback(void* parameter)
{
    EventData* eventData = message_allocEventData();
    message_eventDataInit(eventData, 0, 0, NULL);
    submitwork_to_system_queue(deal_repeat_remind, eventData);
}

/**
 * @brief 执行恢复播放逻辑（需在持有锁时调用）
 */
static void restore_playback_logic(void)
{
    // 恢复逻辑
    if(restore_previous_context())
    {
        REMIND_RESPONSE_LOG_I("restore suc id:%d  sound:%d  vibrate:%d", playback_state.current.scene_id.value,
            playback_state.current.config.sound_id, playback_state.current.config.vibrate_id);
        playback_state.current.is_active = true;

        if(playback_state.current.config.repeat_count == 0xFF &&
           playback_state.current.config.interval_time > 0)
        {
            int64_t time_tmp = get_boot_msec();
            int64_t remainder_tmp = ((time_tmp - playback_state.current.time_stamp) %
                                    playback_state.current.config.interval_time);
            if(remainder_tmp != 0)
            {
                uint32_t time_offset = playback_state.current.config.interval_time - remainder_tmp;
                REMIND_RESPONSE_LOG_D("Time alignment: tmp=%llu stamp=%lld offset=%u interval_time %d",
                    time_tmp, playback_state.current.time_stamp, time_offset,
                    playback_state.current.config.interval_time);
                qw_timer_start(&playback_state.timer, time_offset, NULL, "remind_response_app");
                return;
            }
        }

        if(playback_state.current.scene_id.value == RRT_TOOL_METRONOME && tool_metronome_callback)
        {
            tool_metronome_callback();
        }
#ifndef SIMULATOR
        if((playback_state.current.config.sound_id != SOUND_NONE)&&(playback_state.current.config.sound_id < SOUND_MAX))
        {
            // 开始播放
            if(beep_device)
            {
                if(RT_EOK != rt_device_control(beep_device, RT_DEVICE_CTRL_START, (void *)&beep_profiles[playback_state.current.config.sound_id]))
                {
                    REMIND_RESPONSE_LOG_E("rt_device_control failed...\n");
                }
            }
        }
        if((playback_state.current.config.vibrate_id != VIB_NONE)&&(playback_state.current.config.vibrate_id < VIB_MAX))
        {
            // 开始播放
            if(motor_device)
            {
                if(RT_EOK != rt_device_control(motor_device, RT_DEVICE_CTRL_START, (void *)&motor_profiles[playback_state.current.config.vibrate_id]))
                {
                    REMIND_RESPONSE_LOG_E("rt_device_control failed...\n");
                }
            }
        }
#endif

        if(playback_state.current.config.repeat_count != 0xFF)
        {
            if(playback_state.current.repeat_counter > 0)
            {
                playback_state.current.repeat_counter--;
            }
        }

        if(playback_state.current.config.repeat_count > 0 &&
           playback_state.current.config.interval_time > 0)
        {
            qw_timer_start(&playback_state.timer,
                           playback_state.current.config.interval_time, NULL, "remind_response_app");
        }
        else
        {
            stop_all_output_locked();
        }
    }
    else
    {
        stop_all_output_locked();
    }
}

/**
 * @brief 主播放控制函数（线程安全）
 */
void buzzer_vibrator_play(UnifiedSceneID scene_id, ResponseMapping* info)
{
    if(!lock_playback()) return;

    // 空优先级处理（恢复逻辑）
    if(info->priority == PRIO_NONE)
    {
        restore_playback_logic();
        unlock_playback();
        return;
    }

    // 有效性检查
    if ((SOUND_TYPE) info->sound_id >= SOUND_MAX || (VIB_TYPE) info->vibrate_id >= VIB_MAX)
    {
        REMIND_RESPONSE_LOG_E("Invalid config! sound:%d vib:%d",
                info->sound_id, info->vibrate_id);
        unlock_playback();
        return;
    }

    // 优先级判断
    // REMIND_RESPONSE_LOG_D("is_active:%d (repeat_count:%d)",
    //                          playback_state.current.is_active, playback_state.current.repeat_counter);
    if(playback_state.current.is_active && scene_id.value != playback_state.current.scene_id.value)
    {
        // REMIND_RESPONSE_LOG_D("Ignore low-prio req:%d (current:%d)", info->priority, playback_state.current.config.priority);
        if(info->priority < playback_state.current.config.priority)
        {
            if(info->repeat_count != 0xFF)
            {
                REMIND_RESPONSE_LOG_I("low-prio not repeat req:%d (current:%d)",
                        info->priority,
                        playback_state.current.config.priority);

                unlock_playback();
            }
            else // 低优先级的循环提醒入栈
            {
                REMIND_RESPONSE_LOG_I("save low-prio req:%d (current:%d)",
                        info->priority,
                        playback_state.current.config.priority);

                // 保存当前上下文
                PlaybackContext back_context;
                memcpy(&back_context.config, info, sizeof(ResponseMapping));
                back_context.repeat_counter = info->repeat_count;
                back_context.is_active = true;
                back_context.scene_id.value = scene_id.value;
                if(!save_context(&back_context))
                {
                    unlock_playback();
                    return;
                }
            }
            return;
        }
        else
        {
            if(playback_state.current.config.repeat_count == 0xFF)
            {
                REMIND_RESPONSE_LOG_I("stop and save cur");
                // 保存当前上下文
                if(!save_context(&playback_state.current))
                {
                    unlock_playback();
                    return;
                }
            }
        }
    }

    if(playback_state.current.is_active)
    {
        REMIND_RESPONSE_LOG_D("Interrupt current output");
        // 停止当前输出
        stop_all_output_locked();
    }

    // 加载新配置
    memcpy(&playback_state.current.config, info, sizeof(ResponseMapping));
    playback_state.current.repeat_counter = info->repeat_count;
    playback_state.current.is_active = true;
    playback_state.current.scene_id.value = scene_id.value;

    // 执行新播放
    if(playback_state.current.scene_id.value == RRT_TOOL_METRONOME && tool_metronome_callback)
    {
        tool_metronome_callback();
    }
#ifndef SIMULATOR
    if((info->sound_id != SOUND_NONE)&&(info->sound_id < SOUND_MAX))
    {
        REMIND_RESPONSE_LOG_D("ID:0x%08x,Play sound:%d", scene_id.value, info->sound_id);
        // 开始播放
        if(beep_device)
        {
            if(RT_EOK != rt_device_control(beep_device, RT_DEVICE_CTRL_START, (void *)&beep_profiles[info->sound_id]))
            {
                REMIND_RESPONSE_LOG_E("rt_device_control failed...\n");
            }
        }
    }
    if((info->vibrate_id != VIB_NONE)&&(info->vibrate_id < VIB_MAX))
    {
        REMIND_RESPONSE_LOG_D("ID:0x%08x,Play vibration:%d", scene_id.value, info->vibrate_id);
        // 开始播放
        if(motor_device)
        {
            if(RT_EOK != rt_device_control(motor_device, RT_DEVICE_CTRL_START, (void *)&motor_profiles[info->vibrate_id]))
            {
                REMIND_RESPONSE_LOG_E("rt_device_control failed...\n");
            }
        }
    }
#endif

    if(playback_state.current.config.repeat_count != 0xFF)
    {
        if(playback_state.current.repeat_counter > 0)
        {
            playback_state.current.repeat_counter--;
        }
    }

    // 设置重复定时器
    if(playback_state.current.config.interval_time > 0 && 
        ((playback_state.current.repeat_counter > 0 && (info->sound_id != SOUND_NONE || info->vibrate_id != VIB_NONE)) || 
        playback_state.stack_top != 0))
    {
        // REMIND_RESPONSE_LOG_D("Start repeat timer:%d", info->interval_time);
        qw_timer_start(&playback_state.timer,
                       info->interval_time, NULL, "remind_response_app");
        // REMIND_RESPONSE_LOG_D("qw_timer_start end");
    }
    else
    {
        playback_state.current.is_active = false;
    }

    unlock_playback();
}

/**
 * @brief 强制停止所有提醒
 */
void remind_response_stop_all(void)
{
    if(!lock_playback()) return;

    stop_all_output_locked();
    playback_state.stack_top = 0;
    memset(&playback_state.current, 0, sizeof(PlaybackContext));

    unlock_playback();
}

/**
 * @brief 模块卸载清理
 */
void remind_response_deinit(void)
{
    remind_response_stop_all();

    qw_timer_detach(&playback_state.timer);

    if(playback_state.mutex) {
        osMutexDelete(playback_state.mutex);
        playback_state.mutex = NULL;
    }

#ifndef SIMULATOR
    // 关闭设备
    if(beep_device)
    {
        rt_err_t ret = rt_device_close(beep_device);
        if(ret != RT_EOK)
        {
            REMIND_RESPONSE_LOG_E("rt_device_close beep failed...\n");
        }
        beep_device = RT_NULL;
    }
    if(motor_device)
    {
        rt_err_t ret = rt_device_close(motor_device);
        if(ret != RT_EOK)
        {
            REMIND_RESPONSE_LOG_E("rt_device_close motor failed...\n");
        }
        motor_device = RT_NULL;
    }
#endif
}

static void deal_sys_state(UnifiedSceneID scene_id, uint8_t* s_state, uint8_t* v_state)
{
    bool focus_state = get_sleep_state(true) || get_dnd_state(true);
    bool power_save_state = get_power_save_setting() > 0 ? true : false;
    *s_state = UINT8_MAX;
    *v_state = UINT8_MAX;
    if(scene_id.bits.src == 0 && (scene_id.bits.type == enumPOPUP_TIMER_CLOCK))
    {
        if(get_timer_remind_type() == TIMER_REMIND_NONE)
        {
            *s_state = *s_state & 0;
            *v_state = *v_state & 0;
        }
        else if(get_timer_remind_type() == TIMER_REMIND_VIBRATION)
        {
            *s_state = *s_state & 0;
            *v_state = *v_state & UINT8_MAX;
        }
        else
        {
            *s_state = *s_state & UINT8_MAX;
            *v_state = *v_state & UINT8_MAX;
        }
    }

    if(power_save_state)
    {
        REMIND_RESPONSE_LOG_D("power_save_state");
        *s_state = *s_state & 0;
        *v_state = *v_state & 0;
    }

    if(focus_state)
    {
        REMIND_RESPONSE_LOG_D("focus_state");
        if(scene_id.bits.src == 0 && (scene_id.bits.type == enumPOPUP_ALARM_CLOCK ||
            scene_id.bits.type == enumPOPUP_TIMER_CLOCK))
        {
            *s_state = *s_state & UINT8_MAX;
            *v_state = *v_state & UINT8_MAX;
        }
        else
        {
            *s_state = *s_state & 0;
            *v_state = *v_state & 0;
        }
    }
}

static void get_metronome_info(UnifiedSceneID scene_id, ResponseMapping* info)
{
    if(scene_id.value == RRT_METRONOME)
    {
        SPORTTYPE sport_type = get_current_sport_mode();
        if(get_sport_metronome_en(sport_type) == false)
        {
            info->sound_id = SOUND_NONE;
            info->vibrate_id = VIB_NONE;

            REMIND_RESPONSE_LOG_E("metronome is disable");
            return;
        }

        info->interval_time = 60000 / (get_sport_metronome_pace(sport_type) / (get_sport_metronome_tip_freq(sport_type) + 1));

        uint8_t tmp = get_sport_metronome_remind_mode(sport_type);
        if(tmp == 1)
        {
            info->sound_id = SOUND_NONE;
        }
        else if(tmp == 2)
        {
            info->vibrate_id = VIB_NONE;
        }
        info->repeat_count = 0xFF;
    }
    else if(scene_id.value == RRT_TOOL_METRONOME)
    {
        info->interval_time = 60000 / (get_tool_metronome_pace() / (get_tool_metronome_tip_freq() + 1));

        uint8_t tmp = get_tool_metronome_remind_mode();
        if(tmp == 1)
        {
            info->sound_id = SOUND_NONE;
        }
        else if(tmp == 2)
        {
            info->vibrate_id = VIB_NONE;
        }
        info->repeat_count = 0xFF;
    }
    REMIND_RESPONSE_LOG_D("get_metronome_info: interval_time=%d, sound_id=%d, vibrate_id=%d, repeat_count=%d",
                            info->interval_time, info->sound_id, info->vibrate_id, info->repeat_count);
}

static bool deal_remind_type(EventData* eventData)//EventData* eventData
{
    uint32_t type = (uint32_t)eventData->arg1;

    const ResponseMapping* mapping = NULL;
    ResponseMapping info = {0};

    uint8_t s_state = UINT8_MAX;
    uint8_t v_state = UINT8_MAX;

    UnifiedSceneID scene_id;
    scene_id.value = type;

    if(scene_id.bits.src == 1 && (scene_id.value == RRT_METRONOME || scene_id.value == RRT_TOOL_METRONOME))
    {
        memcpy(&info, &g_base_mapping[scene_id.bits.type], sizeof(ResponseMapping));
        get_metronome_info(scene_id, &info);
        buzzer_vibrator_play(scene_id, &info);
        return true;
    }

    deal_sys_state(scene_id, &s_state, &v_state);

    // 基础操作类型处理
    if(type >= RRT_B_BTN && type <= RRT_B_KNOB)
    {
        if(scene_id.bits.type < ARRAY_SIZE(g_base_mapping))
        {
            mapping = &g_base_mapping[scene_id.bits.type];

            // 按钮类特殊处理
            if(get_btn_sound())
            {
                info.sound_id = mapping->sound_id & s_state;
            }
            bool enable = false;
            if(get_button_vibration(&enable) && enable)
            {
                info.vibrate_id = mapping->vibrate_id & v_state;
            }
            info.repeat_count = mapping->repeat_count;
            info.priority = mapping->priority;
#ifndef SIMULATOR
            info.interval_time = mapping->interval_time ? mapping->interval_time :
                                    MAX(beep_time[info.sound_id], motor_time[info.vibrate_id]);
#else
            info.interval_time = 100;
#endif
        }
        else
        {
            return true;
        }
    }
    // 弹窗类型处理
    else
    {
        if(scene_id.bits.src == 0)
        {
            if(scene_id.bits.type < ARRAY_SIZE(g_msgbox_mapping))
            {
                mapping = &g_msgbox_mapping[scene_id.bits.type];
            }
        }
        else
        {
            if(scene_id.bits.type < ARRAY_SIZE(g_base_mapping))
            {
                mapping = &g_base_mapping[scene_id.bits.type];
            }
        }

        if (!mapping)
        {
            return true;
        }

#ifndef SIMULATOR
        // 声音触发条件
        if(get_sound_remind() == 1 ||
          (get_sound_remind() == 2 && get_sport_status() > enum_status_ready)) {
            info.sound_id = mapping->sound_id & s_state;
        }

        // 振动触发条件
        uint8_t mode = 0; // 0:不触发 1:触发
        if (get_vibration_remind(&mode))
        {
            if(mode == 1 || (mode == 2 && get_sport_status() > enum_status_ready)) {
                info.vibrate_id = mapping->vibrate_id & v_state;
            }
        }
        info.repeat_count = mapping->repeat_count;
        info.priority = mapping->priority;
        info.interval_time = mapping->interval_time ? mapping->interval_time :
                                    MAX(beep_time[info.sound_id], motor_time[info.vibrate_id]);
#else
        // 声音触发条件
        if(get_sound_remind() == 1 ||
          (get_sound_remind() == 2 && get_simulator_sport_status() > ACTIVITY_FIT_READY)) {
            info.sound_id = mapping->sound_id & s_state;
        }

        // 振动触发条件
        uint8_t mode = 0; // 0:不触发 1:触发
        if (get_vibration_remind(&mode))
        {
            if(mode == 1 || (mode == 2 && get_simulator_sport_status() > ACTIVITY_FIT_READY)) {
                info.vibrate_id = mapping->vibrate_id & v_state;
            }
        }
        info.repeat_count = mapping->repeat_count;
        info.priority = mapping->priority;
        info.interval_time = 100;
#endif
        REMIND_RESPONSE_LOG_I("deal type: type=0x%08x, s_state=%d, v_state=%d, repeat_count=%d",
                                type, info.sound_id, info.vibrate_id, info.repeat_count);
    }

    buzzer_vibrator_play(scene_id, &info);
    return true;
}

static bool deal_stop_remind(EventData* eventData)//EventData* eventData
{
    uint32_t type = (uint32_t)eventData->arg1;
    UnifiedSceneID scene_id;
    scene_id.value = type;

    if(!lock_playback()) return false;

    if(playback_state.current.scene_id.value == scene_id.value)
    {
        REMIND_RESPONSE_LOG_I("Stop current playback");
        stop_all_output_locked();
        unlock_playback();
        ResponseMapping restore_cmd = {.priority = PRIO_NONE};
        buzzer_vibrator_play((UnifiedSceneID){0}, &restore_cmd);
        return true;
    }
    else if(playback_state.stack_top != 0)
    {
        REMIND_RESPONSE_LOG_I("Stop playback in stack");
        for(int i = 0; i < playback_state.stack_top; i++)
        {
            // 查找匹配的中断上下文
            if(playback_state.interrupt_stack[i].scene_id.value == scene_id.value)
            {
                REMIND_RESPONSE_LOG_D("Found matching context at stack pos:%d", i);

                if(i != playback_state.stack_top - 1)
                {
                    REMIND_RESPONSE_LOG_D("Move interrupt stack ");
                    // 移除匹配项并压缩栈
                    memmove(&playback_state.interrupt_stack[i],
                            &playback_state.interrupt_stack[i+1],
                            (playback_state.stack_top - i - 1) * sizeof(PlaybackContext));
                }
                else
                {
                    REMIND_RESPONSE_LOG_D("Remove interrupt stack");
                    // 移除栈顶
                    memset(&playback_state.interrupt_stack[i], 0, sizeof(PlaybackContext));
                }

                playback_state.stack_top--;

                break; // 找到后立即退出循环
            }
        }
    }

    unlock_playback();

    return true;
}

void remind_trigger(uint32_t type, bool state)
{
    EventData* eventData = message_allocEventData();
    if(state)
    {
        REMIND_RESPONSE_LOG_D("Remind trigger: 0x%08x", type);
        // deal_remind_type(type);
        message_eventDataInit(eventData, type, 0, NULL);
        submitwork_to_system_queue(deal_remind_type, eventData);

    }
    else
    {
        REMIND_RESPONSE_LOG_D("Remind stop: 0x%08x ", type);
        // deal_stop_remind(type);
        message_eventDataInit(eventData, type, 0, NULL);
        submitwork_to_system_queue(deal_stop_remind, eventData);
    }
}

void set_tool_metronome_callback(void (*callback)(void))
{
    tool_metronome_callback = callback;
}
