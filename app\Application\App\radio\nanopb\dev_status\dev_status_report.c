/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   dev_status_report.c
@Time    :   2025/05/07 
* 
**************************************************************************/

#include "dev_status_report.h"
#include "ble_data_inf.h"
#include "dev_status.pb.h"
#include "ble_cmd_response.h"

//-------------------------------------------------------------------------------------------
// Function Name : sport_status_change_notice
// Purpose       : 手表运动状态变化时，通知APP
// Param[in]     : saving_status_e status 运动状态
// Param[out]    : None
// Return type   : 
// Comment       : 2025-05-06
//-------------------------------------------------------------------------------------------
void sport_status_change_notice(saving_status_e status)
{
    static DEV_CYCLING_STATUS last_dev_status =  DEV_CYCLING_STATUS_DEV_CYCLING_STATUS_FREE;
    DEV_CYCLING_STATUS new_dev_status = DEV_CYCLING_STATUS_DEV_CYCLING_STATUS_FREE;

    switch (status)
    {
        case enum_status_free:
        case enum_status_ready:
        {
            new_dev_status = DEV_CYCLING_STATUS_DEV_CYCLING_STATUS_FREE;
        }
        break;

        case enum_status_saving:
        {
            new_dev_status = DEV_CYCLING_STATUS_DEV_CYCLING_STATUS_DOING;
        }
        break;

        case enum_status_pause_auto:
        case enum_status_pause_manul:
        {
            new_dev_status = DEV_CYCLING_STATUS_DEV_CYCLING_STATUS_PAUSE;
        }
        break;

        default:
            break;
    }

    // if (last_dev_status != new_dev_status)
    // {
    //     last_dev_status = new_dev_status;
        if (g_device_get_ble_connect_status())
        {
            ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_DEV_STATUS, 0,
                DEV_STATUS_OPERATE_TYPE_enum_DEV_STATUS_OPERATE_TYPE_SEND, 0xff, last_dev_status);
        }
    // }
}
