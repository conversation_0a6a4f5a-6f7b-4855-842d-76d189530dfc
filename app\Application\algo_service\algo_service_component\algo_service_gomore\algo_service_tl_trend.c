/**
 * @file algo_service_tl_trend.c
 * <AUTHOR>
 * @brief 负荷趋势算法接口
 * @version 0.1
 * @date 2025-8-14
 *
 * @copyright Copyright (c) 2024-2025, <PERSON>han <PERSON>wu Technology Co., Ltd
 *
 */

#include "algo_service_tl_trend.h"
#include "algo_service_adapter.h"
#include "lib_gm_common.h"
#include "subscribe_service.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "subscribe_data_protocol.h"
#include "qw_time_api.h"
#include "qw_time_util.h"

// 本算法打开标记
static bool s_is_tl_trend_open = false;

/**
 * @brief tl_trend算法初始化,上电运行
 *
 * @return int32_t 初始化结果,成功返回0,失败返回-1
 */
static int32_t algo_tl_trend_init(void)
{
    ALGO_COMP_LOG_I("tl_trend:init ok");
    return 0;
}

/**
 * @brief tl_trend算法回调
 *
 * @param in 输入数据
 * @param len 输入数据长度
 * @return int32_t 结果
 */
static void algo_tl_trend_in_callback(const void *in, uint32_t len)
{
    const algo_timer_event_pub_t *pub_data = (algo_timer_event_pub_t *)in;
    if (len != sizeof(algo_timer_event_pub_t))
    {
        ALGO_COMP_LOG_E("tl_trend:callback len:%u", len);
        return;
    }

    // 每天的00:01分触发一次
    if(pub_data->event_type != TIMER_EVENT_0H_1MIN)
    {
        ALGO_COMP_LOG_I("tl_trend:no 00:01");
        return;
    }
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_TL_TREND;
    head.input_type = DATA_ID_ALGO_EVENT_TIMER;
    uint8_t data = 0; // 传一个默认值，不允许为空

    // 发送消息到算法线程
    if (send_msg_to_algo_fwk(head, &data, sizeof(data)) != 0)
    {
        ALGO_COMP_LOG_E("tl_trend:callback msg error");
        return;
    }
    ALGO_COMP_LOG_I("tl_trend:callback 00:01");
}

/**
 * @brief 打开负荷趋势算法
 *
 * @return int16_t 成功open返回0, 失败返回-1
 */
static int32_t algo_tl_trend_open()
{
    int32_t ret = lib_gomore_init();
    if(ret != 0)
    {
        ALGO_COMP_LOG_E("algo_tl_trend_open not init");
        return -1;
    }
    if (s_is_tl_trend_open)
    {
        ALGO_COMP_LOG_I("tl_trend:open already");
        return 0;
    }

    optional_config_t config = {.sampling_rate = 0};
    ret = qw_dataserver_subscribe_id(DATA_ID_ALGO_EVENT_TIMER, algo_tl_trend_in_callback, &config);
    if (ret != 0)
    {
        ALGO_COMP_LOG_E("tl_trend:open ET failed");
        return -1;
    }
    s_is_tl_trend_open = true;
    ALGO_COMP_LOG_I("tl_trend:open ok");
    return 0;
}

/**
 * @brief 负荷趋势算法feed数据
 *
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 成功feed返回0, 失败返回-1
 */
static int32_t algo_tl_trend_feed(uint32_t input_type, void *data, uint32_t len)
{
    if (!s_is_tl_trend_open)
    {
        ALGO_COMP_LOG_I("tl_trend:feed not open");
        return 0;
    }

    if(input_type != DATA_ID_ALGO_EVENT_TIMER)
    {
        ALGO_COMP_LOG_E("tl_trend:feed input_type error:%u", input_type);
        return -1;
    }

    //对运动模式下进行异常值输出，在非运动模式下输出负荷趋势数据
    algo_tl_trend_pub_t algo_data;
    int32_t ret = lib_get_tl_trend(&algo_data.tl_trend);
    if (ret != 0)
    {
        ALGO_COMP_LOG_E("tl_trend:not get");
        return ret;
    }

    //发布 tlTrend 数据
    if (qw_dataserver_publish_id(DATA_ID_ALGO_TL_TREND, &algo_data, sizeof(algo_data)) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("tl_trend: pub error");
        return -1;
    }
    ALGO_COMP_LOG_I("tl_trend: tl_trend=%f", algo_data.tl_trend);
    return 0;
}

/**
 * @brief 关闭负荷趋势算法
 *
 * @return int32_t 成功close返回0, 失败返回-1
 */
static int32_t algo_tl_trend_close(void)
{
    if (!s_is_tl_trend_open)
    {
        ALGO_COMP_LOG_I("tl_trend:close already");
        return 0;
    }
    // 取消订阅事件定时器
    int32_t ret = qw_dataserver_unsubscribe_id(DATA_ID_ALGO_EVENT_TIMER, algo_tl_trend_in_callback);
    if (ret != 0)
    {
        ALGO_COMP_LOG_E("tl_trend:close ET");
        return -1;
    }
    s_is_tl_trend_open = false;
    ALGO_COMP_LOG_I("tl_trend:close ok");
    return 0;
}

// 负荷趋势算法组件实现
static algo_compent_ops_t s_tl_trend_algo =
{
    .init = algo_tl_trend_init,
    .open = algo_tl_trend_open,
    .feed = algo_tl_trend_feed,
    .close = algo_tl_trend_close,
    .ioctl = NULL, // 负荷趋势算法不需要配置操作
};

/**
 * @brief 负荷趋势组件注册
 *
 * @return int32_t 结果
 */
int32_t register_tl_trend_algo(void)
{
    // 注册负荷趋势算法组件
    return algo_compnent_register(ALGO_TYPE_TL_TREND, &s_tl_trend_algo);
}