/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   algo_service_task.c
@Time    :   2024/12/17 14:10:28
*
**************************************************************************/

/**
 * @file algo_service_task.c
 * <AUTHOR> (<EMAIL>)
 * @brief 算法适配层接口
 * @version 0.1
 * @date 2024-11-09
 *
 * @copyright Copyright (c) 2024-2025, Wuhan Qiwu Technology Co., Ltd
 *
 */
#include "algo_service_task.h"
#include "algo_service_fwk_log.h"
#include "subscribe_commu.h"
#include "algo_service_config.h"
#include "algo_service_adapter.h"
#ifdef SOC_BF0_LCPU
#include "algo_service_step_count.h"
#include "algo_service_calories.h"
#include "algo_service_active_intense.h"
#include "algo_service_hr.h"
#include "algo_service_spo2.h"
#include "algo_service_daily_accum.h"
#include "algo_service_daily_increase.h"
#include "algo_service_event_timer.h"
#include "algo_service_sleep_stage.h"
#include "algo_service_rest_hr.h"
#include "algo_service_fitness.h"
#include "algo_service_raise_wrist.h"
#include "algo_service_alldaystamina.h"
#include "algo_service_stress_score.h"
#include "algo_service_hrv_evaluate.h"
#include "algo_service_stress.h"
#include "algo_service_hrv.h"
#include "algo_service_rhr.h"
#include "algo_service_altitude/algo_service_altitude.h"
#include "algo_service_altitude/algo_service_altitude_dot.h"
#include "algo_service_heart_rate.h"
#include "algo_service_tl_trend.h"
#else
#include "algo_service_gps/algo_service_gps_data.h"
#include "algo_service_altitude_avg.h"
#include "algo_service_chart_altitude.h"
#include "algo_service_lap_count.h"
#include "algo_service_timer.h"
#include "algo_service_speed.h"
#include "algo_service_speed_max.h"
#include "algo_service_distance.h"
#include "algo_service_odometer.h"
#include "algo_service_speed_avg.h"
#include "algo_service_heart_rate_avg.h"
#include "algo_service_cadence.h"
#include "algo_service_cadence_avg.h"
#include "algo_service_power.h"
#include "algo_service_power_avg.h"
#include "algo_service_grade.h"
#include "algo_service_grade_avg.h"
#include "algo_service_position.h"
#include "algo_service_sports_calories.h"
#include "algo_service_move_status.h"
#include "algo_service_auto_sports_ctrl.h"
#include "algo_service_running_dynamics.h"
#include "algo_service_rd_avg.h"
#include "algo_service_bunny.h"
#include "algo_service_jump_rope.h"
#include "algo_service_statistics.h"
#include "algo_service_pool_swim.h"
#include "qw_fs.h"
#endif
#include "qwos_task_def.h"

// 算法线程消息队列数目
#define ALGO_MQ_NUM 100

// 算法线程消息队列已满,预留20%空间
#define ALGO_MQ_FULL_NUM (ALGO_MQ_NUM * 0.8)

// 算法线程栈大小
#define ALGO_THREAD_STACK (20 * 1024)

// 性能算法线程消息队列数目
#define ALGO_PERFORMANCE_MQ_NUM 20

// 性能算法线程消息队列已满,预留20%空间
#define ALGO_PERFORMANCE_MQ_FULL_NUM (ALGO_PERFORMANCE_MQ_NUM * 0.8)

// 性能算法线程栈大小
#define ALGO_PERFORMANCE_THREAD_STACK (5 * 1024)

static void algo_service_thread_entry(void *parameter);
#if (CONFIG_ALGO_TASK_HIGH_PERFORMANCE == 1)
static void algo_service_per_thread_entry(void *parameter);
#endif

// 算法消息队列大小
const algo_task_config_t s_algo_task_config[ALGO_TASK_MAX] = {
    {
        .task_name = "algo_nor",
        .mq_name = "algo_nor_mq",
        .entry = algo_service_thread_entry,
        .priority = NORMAL_ALGO_TASK_PRIORITY,
        .time_slice = RT_THREAD_TICK_DEFAULT,
        .stack_size = ALGO_THREAD_STACK,
        .mq_size = ALGO_MQ_NUM,
        .mq_water_size = ALGO_MQ_FULL_NUM,
    },
#if (CONFIG_ALGO_TASK_HIGH_PERFORMANCE == 1)
    {
        .task_name = "algo_per",
        .mq_name = "algo_per_mq",
        .entry = algo_service_per_thread_entry,
        .priority = HIGH_ALGO_TASK_PRIORITY_L0,
        .time_slice = RT_THREAD_TICK_DEFAULT,
        .stack_size = ALGO_PERFORMANCE_THREAD_STACK,
        .mq_size = ALGO_PERFORMANCE_MQ_NUM,
        .mq_water_size = ALGO_PERFORMANCE_MQ_FULL_NUM,
    },
#endif
};

// 算法线程消息队列
static rt_mq_t s_algo_mq[ALGO_TASK_MAX] = {0};

// 算法服务线程
static rt_thread_t s_algo_thread[ALGO_TASK_MAX] = {0};

// 性能算法服务线程初始化标志
static bool s_algo_thread_init_flag = false;

#ifdef SOC_BF0_HCPU
/**
 * @brief 初始化算法数据采集文件夹
 *
 * @param path 文件夹路径
 */
static void algo_check_capture_dir(const char* path)
    {
    if (path == NULL) {
        return;
    }

    QW_DIR dir;
    QW_FRESULT ret = qw_f_opendir(&dir, path);
    if (QW_OK != ret) {
        if (QW_ERROR == ret) {
            ALGO_FWK_LOG_E("algo_svr check mkdir");
        } else {
            ret = qw_f_mkdir(path);
            if (QW_OK != ret)
            {
                ALGO_FWK_LOG_E("algo_svr check f_mkdir=%d", ret);
            }
        }
        return;
    }
    qw_f_closedir(&dir);
}
#endif

/**
 * @brief 算法框架通知
 *
 * @param adap_head 数据头
 * @param adap_data 数据
 * @param data_len 数据长度
 * @return int32_t ERRO_CODE_FAILED ：失败 ERRO_CODE_OK ：成功
 */
int32_t send_msg_to_algo_fwk(algo_svr_head_t adap_head, const void *adap_data, uint32_t data_len)
{
    if (!s_algo_thread_init_flag)
    {
        ALGO_FWK_LOG_E("algo_svr msg thread not init");
        return ERRO_CODE_FAILED;
    }

    if (NULL == adap_data)
    {
        ALGO_FWK_LOG_E("algo_svr msg adap null %u", adap_head.cid);
        return ERRO_CODE_FAILED;
    }
    if (data_len > ALGO_SVR_MSG_DATA_LEN) // 超过有效数据长度
    {
        ALGO_FWK_LOG_E("algo_svr msg algo_cmd:%u adap data_len:%u>512", adap_head.cid, data_len);
        return ERRO_CODE_FAILED;
    }
    ALGO_FWK_LOG_D("algo_svr msg data_len:%u", data_len);
    uint32_t start_tick = rt_tick_get();  // 添加性能监控
    algo_mq_msg_t msg;
    msg.cid = adap_head.cid;
    msg.algo_type = adap_head.algo_type;
    msg.input_type = adap_head.input_type;
    msg.len = data_len;
    memset(&msg.data, 0, sizeof(msg.data));
    (void)memcpy(msg.data, adap_data, data_len);
    rt_size_t size = sizeof(algo_mq_msg_t) - ALGO_SVR_MSG_DATA_LEN + data_len;
    const algo_compent_ops_t* algo_ops = algo_compnent_get(adap_head.algo_type);
    if (algo_ops == NULL) {
        ALGO_FWK_LOG_E("algo_svr msg algo_ops null algo_type:%u,input_type:%u",
            adap_head.algo_type, adap_head.input_type);
        return ERRO_CODE_FAILED;
    }
    if (algo_ops->task_id >= ALGO_TASK_MAX) {
        ALGO_FWK_LOG_E("algo_svr msg algo_ops task_id:%u,algo_type:%u,input_type:%u",
            algo_ops->task_id, adap_head.algo_type, adap_head.input_type);
        return ERRO_CODE_FAILED;
    }
    rt_mq_t algo_mq = s_algo_mq[algo_ops->task_id];
    uint32_t mq_water_size = s_algo_task_config[algo_ops->task_id].mq_water_size;
    rt_err_t ret = rt_mq_send(algo_mq, &msg, size);
    if (ret != RT_EOK)
    {
        ALGO_FWK_LOG_E("algo_svr msg send algo_cmd:%u,algo_type:%u, ret:%d",
            adap_head.cid, adap_head.algo_type, ret);
        return ERRO_CODE_FAILED;
    }
    if (algo_mq->entry >= mq_water_size)
    {
        ALGO_FWK_LOG_W("algo_svr msg algo_mq entry:%u, mq_water_size:%u", algo_mq->entry, mq_water_size);
    }

    // 性能监控：记录发送消息的耗时
    uint32_t cost_time = rt_tick_get() - start_tick;
    if (cost_time > 20)
    {   // 超过10ms记录警告
        ALGO_FWK_LOG_W("msg_to_algo_fwk :%u, %u, cost:%u ms, entry:%u",
            adap_head.algo_type, adap_head.input_type, cost_time, algo_mq->entry);
    }
    return ERRO_CODE_OK;
}

/**
 * @brief 算法处理入口
 *
 * @param msg 消息体
 */
static void algo_service_thread_proc(const algo_mq_msg_t *msg)
{
    if (ALGO_CMD_ID_FEED != msg->cid)
    {
        ALGO_FWK_LOG_D("algo_svr proc algo_cmd:%u,len:%u", msg->cid, msg->len);
    }
    algo_adapter_ops_t *adap = get_algo_adapter_entity();
    if (adap == NULL)
    {
        ALGO_FWK_LOG_E("algo_svr proc para null");
        return;
    }
    switch (msg->cid)
    {
        case ALGO_CMD_ID_SUB:
            adap->adapter_subscribe(msg);
            break;
        case ALGO_CMD_ID_UNSUB:
            adap->adapter_unsubscribe(msg);
            break;
        case ALGO_CMD_ID_CONFIG:
            adap->adapter_config(msg);
            break;
        case ALGO_CMD_ID_FEED:
            adap->adapter_feed(msg);
            break;
        default:
            break;
    }
}

/**
 * @brief 算法服务线程实体
 *
 * @param parameter 未使用
 */
static void algo_service_thread_entry(void *parameter)
{
    rt_err_t result;
    while (1)
    {
        algo_mq_msg_t cmd_msg;
        rt_err_t ret = rt_mq_recv(s_algo_mq[ALGO_TASK_NORMAL], &cmd_msg, sizeof(algo_mq_msg_t), RT_WAITING_FOREVER);
        if (RT_EOK == ret)
        {
            if (cmd_msg.len == 0)
            {
                ALGO_FWK_LOG_E("algo_svr proc data null, cid:%u, algo_type:%u, input_type:%u",
                    cmd_msg.cid, cmd_msg.algo_type, cmd_msg.input_type);
                return;
            }
            algo_service_thread_proc(&cmd_msg);
        }
        else
        {
            ALGO_FWK_LOG_E("algo_svr proc ret:%d", ret);
        }
    }
}

#if (CONFIG_ALGO_TASK_HIGH_PERFORMANCE == 1)
/**
 * @brief 性能算法服务线程实体
 *
 * @param parameter 未使用
 */
static void algo_service_per_thread_entry(void *parameter)
{
    rt_err_t result;
    while (1)
    {
        algo_mq_msg_t cmd_msg;
        rt_err_t ret = rt_mq_recv(s_algo_mq[ALGO_TASK_PERFORMANCE], &cmd_msg, sizeof(algo_mq_msg_t), RT_WAITING_FOREVER);
        if (RT_EOK == ret)
        {
            if (cmd_msg.len == 0)
            {
                ALGO_FWK_LOG_E("algo_svr per data null, cid:%u, algo_type:%u, input_type:%u",
                    cmd_msg.cid, cmd_msg.algo_type, cmd_msg.input_type);
                return;
            }
            algo_service_thread_proc(&cmd_msg);
        }
        else
        {
            ALGO_FWK_LOG_E("algo_svr per ret:%d", ret);
        }
    }
}
#endif

/**
 * @brief  初始化算法服务线程
 *
 * @return int32_t ERRO_CODE_FAILED ：失败 ERRO_CODE_OK ：成功
 */
int32_t qw_algo_service_init(void)
{
    // 组件注册,需要在算法服务初始化之前
#ifdef SOC_BF0_LCPU
    // 小核算法组件注册
    // 低阶算法
    register_event_timer_algo();
    register_step_count_algo();
    register_calories_algo();
    register_active_intense_algo();
    register_sleep_stage_algo();
    register_rest_hr_algo();
    register_hr_algo();
    register_spo2_algo();
    register_fitness_algo();
    register_raise_wrist_algo();
    register_alldaystamina_algo();
    register_stress_score_algo();
    register_hrv_evaluate_algo();
    register_stress_algo();
    register_hrv_algo();
    register_rhr_algo();
    register_tl_trend_algo();


    // 高阶算法
    register_altitude_algo();
    register_altitude_dot_algo();
    register_daily_accum_algo();
    register_daily_increase_algo();
    register_heart_rate_algo();
#else
    // 大核算法组件注册
    // 低阶算法
    // register_hrm_priority_algo();
    // 高阶算法
    register_gps_data_algo();
    register_altitude_avg_algo();
    register_chart_altitude_algo();
    register_lap_count_algo();
    register_timer_algo();
    register_speed_algo();
    register_speed_max_algo();
    register_distance_algo();
    register_odometer_algo();
    register_speed_avg_algo();
    register_heart_rate_avg_algo();
    register_cadence_algo();
    register_cadence_avg_algo();
    register_power_algo();
    register_power_avg_algo();
    register_grade_algo();
    register_grade_avg_algo();
    register_position_algo();
    register_sports_calories_algo();
    register_move_status_algo();
    register_auto_sports_ctrl_algo();
    register_running_dynamics_algo();
    register_rd_avg_algo();
    register_bunny_algo();
    register_jump_rope_algo();
    register_pool_swim_algo();
#endif

    // 算法初始化
    algo_adapter_ops_t *adap =  get_algo_adapter_entity();
    if (adap == NULL)
    {
        ALGO_FWK_LOG_E("algo_svr init para null");
        return ERRO_CODE_FAILED;
    }
    adap->adapter_init_all();

    // 创建算法服务线程消息队列
    for (uint8_t i = 0; i < ALGO_TASK_MAX; i++)
    {
        s_algo_mq[i] = rt_mq_create(s_algo_task_config[i].mq_name, sizeof(algo_mq_msg_t),
            s_algo_task_config[i].mq_size, RT_IPC_FLAG_FIFO);
        if (s_algo_mq[i] == RT_NULL)
        {
            ALGO_FWK_LOG_E("algo_svr init create mq,i=%d", i);
            return ERRO_CODE_FAILED;
        }
    }

    // 创建算法服务线程
    for (uint8_t i = 0; i < ALGO_TASK_MAX; i++) {
        s_algo_thread[i] = rt_thread_create(s_algo_task_config[i].task_name, s_algo_task_config[i].entry, RT_NULL,
            s_algo_task_config[i].stack_size, s_algo_task_config[i].priority, s_algo_task_config[i].time_slice);
        if (s_algo_thread[i] == RT_NULL)
        {
            ALGO_FWK_LOG_E("algo_svr init create thread,i=%d", i);
            return ERRO_CODE_FAILED;
        }
        rt_thread_startup(s_algo_thread[i]);
    }
    s_algo_thread_init_flag = true;
#ifdef SOC_BF0_HCPU
    algo_check_capture_dir(CAPTURE_PATH);
#endif
    ALGO_FWK_LOG_I("algo_svr init ok");
    return ERRO_CODE_OK;
}
INIT_ENV_EXPORT(qw_algo_service_init);