/****************************************Copyright (c)****************************************
* <PERSON><PERSON>wu Technology Co., Ltd
*
*---------------------------------------File Info--------------------------------------------
* File path :
* Created by : Lxin
* LastEditors: Lxin
* Descriptions :
*--------------------------------------------------------------------------------------------
* History :
* 2023-04-28 16:06:01: Lxin 原始版本
*
*********************************************************************************************/

#ifndef __QW_PAGE_INDICATOR_H
#define __QW_PAGE_INDICATOR_H

#include "../module/gui/GUICtrl/touchgfx.h"

class TiledImageArc : 
    public TiledImage
{
public:
    virtual void draw(const Rect& invalidatedArea) const;

public:
    static const int16_t INDICATOR_SPACE = 6;
};

class QwPageIndicator : 
    public Container
{
public:
    QwPageIndicator();
	virtual ~QwPageIndicator() {};

    void setNumberOfPages(uint8_t size);
    void setBitmaps(const Bitmap& normalPage, const Bitmap& highlightedPage);
    void goUp();
    void goDown();
    void setCurrentPage(uint8_t page);
    uint8_t getNumberOfPages() const;
    uint8_t getCurrentPage() const;

private:
    TiledImageArc unselectedPages;
    TiledImageArc selectedPage;
    uint8_t numberOfPages;
    uint8_t currentPage;
};

#endif //__QW_PAGE_INDICATOR_H