/**
 * @file algo_service_event_timer.c
 * <AUTHOR> (<EMAIL>)
 * @brief 算法整天、整时、整分事件定时器
 * @version 0.1
 * @date 2024-12-05
 *
 * @copyright Copyright (c) 2024-2025, <PERSON>han Qiwu Technology Co., Ltd
 *
 */
#include "algo_service_event_timer.h"
#include "rtthread.h"
#include "algo_service_component_log.h"
#include "algo_service_component_common.h"
#include "algo_service_task.h"
#include "algo_service_adapter.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"
#include "qw_time_api.h"
#include "qw_time_util.h"
#include "algo_service_hr.h"

// 本算法打开标记
static bool s_is_event_timer_open = false;

// 提醒定时器
rt_timer_t s_event_timer = NULL;

// 秒转毫秒
#define M_TO_MS 1000

// 定时器周期:1分钟
#define ALGO_EVENT_TIMER_PERIODIC 60

// 定时器超时时间:50分钟
#define ALGO_EVENT_TIMER_50MIN 50

// 定时器周期:1分钟
#define ALGO_EVENT_TIMER_TIMEOUT_PERIODIC (ALGO_EVENT_TIMER_PERIODIC * M_TO_MS)

/**
 * @brief 事件定时器（整天、整时）算法输出发布处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_event_timer_out_callback(const void *out, uint32_t len)
{
    const algo_timer_event_pub_t *pub_data = (algo_timer_event_pub_t *)out;
    ALGO_COMP_LOG_D("ET:out type:%u,data:%u,len:%u",
        pub_data->event_type, pub_data->event_data.hour_val, len);

    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_ALGO_EVENT_TIMER, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("ET:out");
    }
}

/**
 * @brief 事件定时器（整分）算法输出发布处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_event_timer_1min_out_callback(const void *out, uint32_t len)
{
    const algo_timer_event_pub_t *pub_data = (algo_timer_event_pub_t *)out;
    ALGO_COMP_LOG_D("ET:out 1min type:%u,data:%u,len:%u",
        pub_data->event_type, pub_data->event_data.minute_val, len);

    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_EVENT_ONE_MIN_REACH, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("ET:out 1min");
    }
}

/**
 * @brief 事件定时器所有数据发布处理
 *
 * @param datetime 时间
 */
static void algo_event_timer_all_out_callback(qw_tm_t *datetime)
{
    algo_timer_event_pub_t pub_data = {0};

    // 整1分钟到发布
    pub_data.event_type = TIMER_EVENT_NEW_MIN; // (0到23)时(0到59)分0秒
    pub_data.event_data.minute_val = datetime->tm_min;
    algo_event_timer_1min_out_callback(&pub_data, sizeof(algo_timer_event_pub_t));

    // 其他整时、整天发布
    if (datetime->tm_min == ALGO_EVENT_TIMER_50MIN) // 整50min:(0到23)时50分0秒
    {
        pub_data.event_type = TIMER_EVENT_NEW_50MIN;
        pub_data.event_data.hour_val = datetime->tm_hour;
        algo_event_timer_out_callback(&pub_data, sizeof(algo_timer_event_pub_t));
    }
    else if (datetime->tm_min == 0) // 整小时：(0到23)时0分0秒
    {
        pub_data.event_type = TIMER_EVENT_NEW_HOUR;
        pub_data.event_data.hour_val = datetime->tm_hour;
        algo_event_timer_out_callback(&pub_data, sizeof(algo_timer_event_pub_t));
        ALGO_COMP_LOG_E("algo timer pub hour: %d", pub_data.event_data.hour_val);
        if (datetime->tm_hour == 0) // 整天：0时0分0秒
        {
            pub_data.event_type = TIMER_EVENT_NEW_DAY;
            pub_data.event_data.day_val = datetime->tm_mday;
            algo_event_timer_out_callback(&pub_data, sizeof(algo_timer_event_pub_t));
        }
    }
    else if (datetime->tm_min == 30) // 整30min:(0到23)时30分0秒
    {
        pub_data.event_type = TIMER_EVENT_NEW_30MIN;
        pub_data.event_data.hour_val = datetime->tm_hour;
        algo_event_timer_out_callback(&pub_data, sizeof(algo_timer_event_pub_t));
    }
    else if (datetime->tm_min == 1 && datetime->tm_hour == 0) // 0时1分0秒
    {
        pub_data.event_type = TIMER_EVENT_0H_1MIN;
        algo_event_timer_out_callback(&pub_data, sizeof(algo_timer_event_pub_t));
    }
}

/**
 * @brief 定时器校准重置定时周期
 *
 * @param timeout 定时周期
 * @return int32_t 错误码
 */
static int32_t algo_event_timer_reset_periodic(uint32_t timeout)
{
    if (s_event_timer == RT_NULL)
    {
        ALGO_COMP_LOG_E("ET:reset timer is NULL");
        return -RT_ERROR;
    }
    rt_err_t ret = rt_timer_stop(s_event_timer);
    if (ret != RT_EOK)
    {
        ALGO_COMP_LOG_E("ET:reset stop %d", ret);
        return ret;
    }
    ret = rt_timer_control(s_event_timer, RT_TIMER_CTRL_SET_TIME, &timeout);
    if (ret != RT_EOK)
    {
        ALGO_COMP_LOG_E("ET:reset control %d", ret);
        return ret;
    }
    ret = rt_timer_start(s_event_timer);
    if (ret != RT_EOK)
    {
        ALGO_COMP_LOG_E("ET:reset start %d", ret);
    }
    return ret;
}

/**
 * @brief 事件定时器处理函数
 *
 * @param parameter 定时器handle输入参数
 */
static void algo_event_timer_proc(void* parameter)
{
    // 输入数据发布后通知算法线程
    ALGO_COMP_LOG_D("ET:cbk");
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_EVENT_TIMER;
    head.input_type = DATA_ID_ALGO_EVENT_TIMER;
    uint8_t data = 0; // 传一个默认值，不允许为空
    if (send_msg_to_algo_fwk(head, &data, sizeof(uint8_t)) != 0)
    {
        ALGO_COMP_LOG_E("ET:cbk msg");
    }
}

/**
 * @brief 事件定时器初始化
 *
 */
static int32_t algo_event_timer_init(void)
{
    // 初始化定时器
    if (s_event_timer == NULL)
    {
        s_event_timer = rt_timer_create("algo_event_timer", algo_event_timer_proc, RT_NULL,
            ALGO_EVENT_TIMER_TIMEOUT_PERIODIC, RT_TIMER_FLAG_PERIODIC | RT_TIMER_FLAG_SOFT_TIMER);
        if (s_event_timer == NULL)
        {
            ALGO_COMP_LOG_E("ET:init");
            return -1;
        }
    }
    ALGO_COMP_LOG_I("ET:init ok");
    return 0;
}

/**
 * @brief 事件定时器启动
 *
 * @return int32_t 结果
 */
static int32_t algo_event_timer_open(void)
{
    if (s_is_event_timer_open)
    {
        ALGO_COMP_LOG_I("ET:open already");
        return 0;
    }

    // 启动定时器：第一次超时时间需要根据当前时间计算
    qw_tm_t datetime = {0};
    utc_to_localtime(get_sec_from_rtc(), &datetime);
    uint32_t first_timeout = (ALGO_EVENT_TIMER_PERIODIC - datetime.tm_sec % ALGO_EVENT_TIMER_PERIODIC) * M_TO_MS;
    s_event_timer->init_tick = first_timeout;
    ALGO_COMP_LOG_I("ET:open %d %d %d %d %d %d,first_timeout:%u",
        datetime.tm_year + 1900, datetime.tm_mon + 1, datetime.tm_mday,
        datetime.tm_hour, datetime.tm_min, datetime.tm_sec, first_timeout);
    rt_err_t ret = rt_timer_start(s_event_timer);
    if (ret != RT_EOK)
    {
        ALGO_COMP_LOG_E("ET:open %d", ret);
        return -1;
    }
    s_is_event_timer_open = true;
    ALGO_COMP_LOG_I("ET:open ok");
    return 0;
}

/**
 * @brief feed事件定时器算法
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_event_timer_feed(uint32_t input_type, void *data, uint32_t len)
{
    // 计算发布时间事件
    qw_tm_t datetime = {0};
    int64_t now = get_msec_from_rtc();
    uint32_t cail_utc = (uint32_t)get_sec_from_rtc(); // 获取当前时间
    uint32_t timeout = 0;

    // 59秒,做分钟事件自动校准加1,防止软定时器误差导致分钟事件错位
    uint16_t real_msec = cail_utc % ALGO_EVENT_TIMER_PERIODIC;
    if (real_msec == 59)
    {
        cail_utc += 1;
        utc_to_localtime(cail_utc, &datetime);
        ALGO_COMP_LOG_W("ET:feed cail %d %d %d %d %d %d",
            datetime.tm_year + 1900, datetime.tm_mon + 1, datetime.tm_mday,
            datetime.tm_hour, datetime.tm_min, datetime.tm_sec);
        timeout += ALGO_EVENT_TIMER_PERIODIC * M_TO_MS; // 加上周期
    }
    else
    {
        utc_to_localtime(cail_utc, &datetime);
        ALGO_COMP_LOG_I("ET:feed nomal %d %d %d %d %d %d",
            datetime.tm_year + 1900, datetime.tm_mon + 1, datetime.tm_mday,
            datetime.tm_hour, datetime.tm_min, datetime.tm_sec);
    }
    algo_event_timer_all_out_callback(&datetime);

    // 校准一次
    timeout += (ALGO_EVENT_TIMER_PERIODIC * M_TO_MS - (uint32_t)(now % (ALGO_EVENT_TIMER_PERIODIC * M_TO_MS)));
    algo_event_timer_reset_periodic(timeout);
    ALGO_COMP_LOG_I("ET:feed reset now:%u.%u timeout:%u", (uint32_t)(now / M_TO_MS), (uint32_t)(now % M_TO_MS), timeout);
    return 0;
}

/**
 * @brief 事件定时器关闭
 *
 * @return int32_t 结果
 */
static int32_t algo_event_timer_close(void)
{
    if (!s_is_event_timer_open)
    {
        ALGO_COMP_LOG_I("ET:close already");
        return 0;
    }
    rt_err_t ret = rt_timer_stop(s_event_timer);
    if (ret != RT_EOK)
    {
        ALGO_COMP_LOG_E("ET:close %d", ret);
        return -1;
    }
    s_is_event_timer_open = false;
    ALGO_COMP_LOG_I("ET:close ok");
    return 0;
}

/**
 * @brief 事件定时器操作
 *
 * @return int32_t 结果
 */
static int32_t algo_event_timer_ioctl(algo_config_t *config)
{
    if (config->type != ALGO_CONFIG_CAILBRA)
    {
        ALGO_COMP_LOG_I("ET:ioctl type:%d not support", config->type);
        return 0;
    }

    // 校准定时器：第一次超时时间需要根据当前时间计算
    if (!s_is_event_timer_open) // 校准只在开启分钟定时器后生效
    {
        return 0;
    }
    qw_tm_t datetime = {0};
    utc_to_localtime(get_sec_from_rtc(), &datetime);
    ALGO_COMP_LOG_I("ET:ioctl %d %d %d %d %d %d",
        datetime.tm_year + 1900, datetime.tm_mon + 1, datetime.tm_mday,
        datetime.tm_hour, datetime.tm_min, datetime.tm_sec);

    uint32_t cail_timeout = ALGO_EVENT_TIMER_PERIODIC * M_TO_MS - (uint32_t)(get_msec_from_rtc() % (ALGO_EVENT_TIMER_PERIODIC * M_TO_MS));
    if (RT_EOK != algo_event_timer_reset_periodic(cail_timeout))
    {
        ALGO_COMP_LOG_E("ET:ioctl reset");
        return -1;
    }

    ALGO_COMP_LOG_I("ET:ioctl ok");
    return 0;
}

// 事件定时器算法组件实现
static algo_compent_ops_t s_event_timer_algo =
{
    .init = algo_event_timer_init,
    .open = algo_event_timer_open,
    .feed = algo_event_timer_feed,
    .close = algo_event_timer_close,
    .ioctl = algo_event_timer_ioctl,
};

/**
 * @brief 事件定时器数据组件注册
 *
 * @return int32_t 结果
 */
void register_event_timer_algo(void)
{
    algo_compnent_register(ALGO_TYPE_EVENT_TIMER, &s_event_timer_algo);
}
