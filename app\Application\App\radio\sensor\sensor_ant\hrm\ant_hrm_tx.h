/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ant_hrm_tx.h
@Time    :   2024/12/24 9:22:41
<AUTHOR>   txy
*
**************************************************************************/

#ifndef ANT_HRM_TX_H
#define ANT_HRM_TX_H

#include "qw_log.h"
#include "stdbool.h"

#ifdef __cplusplus
extern "C"
{
#endif

#define ANT_HRM_TX_LVL              LOG_LVL_INFO
#define ANT_HRM_TX_TAG              "ant.hrm.tx"

#if (ANT_HRM_TX_LVL >= LOG_LVL_DBG)
    #define ANT_HRM_TX_LOG_D(fmt, ...)        QW_LOG_D(ANT_HRM_TX_TAG, fmt, ##__VA_ARGS__)
#else
    #define ANT_HRM_TX_LOG_D(fmt, ...)
#endif

#if (ANT_HRM_TX_LVL >= LOG_LVL_INFO)
    #define ANT_HRM_TX_LOG_I(fmt, ...)        QW_LOG_I(ANT_HRM_TX_TAG, fmt, ##__VA_ARGS__)
#else
    #define ANT_HRM_TX_LOG_I(fmt, ...)
#endif

#if (ANT_HRM_TX_LVL >= LOG_LVL_WARNING)
    #define ANT_HRM_TX_LOG_W(fmt, ...)        QW_LOG_W(ANT_HRM_TX_TAG, fmt, ##__VA_ARGS__)
#else
    #define ANT_HRM_TX_LOG_W(fmt, ...)
#endif

#if (ANT_HRM_TX_LVL >= LOG_LVL_ERROR)
    #define ANT_HRM_TX_LOG_E(fmt, ...)        QW_LOG_E(ANT_HRM_TX_TAG, fmt, ##__VA_ARGS__)
#else
    #define ANT_HRM_TX_LOG_E(fmt, ...)
#endif

/************************************************************************
 *@function:void ant_hrm_tx_profile_setup(void);
 *@brief:ANT心率主机配置
 *@param:null
 *@return:null
*************************************************************************/
void ant_hrm_tx_profile_setup(void);

/************************************************************************
 *@function:uint32_t ant_hr_open(void);
 *@brief:开启并订阅ppg心率数据（后续ble心率推送完成时，统一接口）
 *@param: 0 - 成功，-1 - 订阅失败
 *@return:null
*************************************************************************/
uint32_t ant_hr_open(void);

/************************************************************************
 *@function:void ant_hrm_tx_open(void);
 *@brief:开启心率推送
 *@param:null
 *@return:null
*************************************************************************/
void ant_hrm_tx_open(void);

/************************************************************************
 *@function:uint32_t ant_hr_open(void);
 *@brief:关闭并取消订阅ppg心率数据（后续ble心率推送完成时，统一接口）
 *@param: 0 - 成功，-1 - 订阅失败
 *@return:null
*************************************************************************/
uint32_t ant_hr_close(void);

/************************************************************************
 *@function:void ant_hrm_tx_close(void);
 *@brief:关闭心率推送
 *@param:null
 *@return:null
*************************************************************************/
void ant_hrm_tx_close(void);

/************************************************************************
 *@function:bool sim_hr_tx_status_set(bool flag);
 *@brief: 设置模拟心率推送标志状态
 *@param: false - 不开启模拟推送，true - 开启模拟推送
 *@return: false - 设置失败，true - 设置成功
*************************************************************************/
bool sim_hr_tx_status_set(bool flag);

#ifdef __cplusplus
}
#endif

#endif //ANT_HRM_TX_H
