/**
 * @file algo_service_statistics.c
 * <AUTHOR> (<EMAIL>)
 * @brief 算法数据统计
 * @version 0.1
 * @date 2024-11-22
 *
 * @copyright Copyright (c) 2024-2025, <PERSON>han <PERSON>wu Technology Co., Ltd
 *
 */
#include "algo_service_statistics.h"
#include "algo_service_component_common.h"
#ifdef STATISTICS_SAVE_TO_FILE
#include "multicore_filesys.h"
#endif
#include "algo_service_fwk_log.h"
#include "qw_time_util.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"
#include "developer_mode.h"
#include "cmsis_os2.h"
#include "qw_time_api.h"
#include "qw_time_util.h"
#include "sensor_hub_app_service.h"
#include "algo_service_rhr.h"
#include "mem_section.h"

// 文件名长度
#define ALGO_STATISTICS_FILE_LEN 128

// 统计数据长度
#define ALGO_STATISTICS_DATA_LEN 128

// 统计数据保存文件路径
#define FS_ALGO_STATISTICS

// 刷屏日志重采样
#define STATISTICS_LOG_DEBUG_RESAMPLE (10) // 10s

// 算法统计数据
static algo_statistics_t s_statistics[ALGO_MAX] = {0};

extern uint32_t HAL_GetTick(void);

#ifdef STATISTICS_SAVE_TO_FILE
/**
 * @brief 记录统计信息到文件
 *
 * @param file_name 文件名
 * @param buf 统计数据
 * @param lenth 统计数据长度
 */
static void algo_statistics_save(const char* file_name, char *buf, uint32_t lenth)
{
    uint32_t br;
    int32_t ret;
    char path_buf[ALGO_STATISTICS_FILE_LEN] = {0};
    if (snprintf(path_buf, ALGO_STATISTICS_FILE_LEN, "%s/%s.csv", CAPTURE_PATH, file_name) < 0)
    {
        ALGO_FWK_LOG_E("statistics save snprintf");
        return;
    }
    int32_t fp = qw_mf_open(path_buf, QW_FA_OPEN_ALWAYS | QW_FA_WRITE | QW_FA_APPEND);
    if (fp == RT_NULL)
    {
        ALGO_FWK_LOG_E("statistics save open");
        return;
    }
    ret = qw_mf_write(fp, buf, lenth, &br);
    if (ret != 0 || br != lenth)
    {
        ALGO_FWK_LOG_E("statistics save write");
    }
    qw_mf_close(fp);
}
#endif

/**
 * @brief 统计算法耗时算法启动
 *
 * @param algo_type 算法统计类型 ALGO_STATISTICS_TYPE
 */
void algo_start_statistics(uint32_t algo_type)
{
    if (algo_type >= ALGO_MAX)
    {
        ALGO_FWK_LOG_E("statistics start algo_type:%d", algo_type);
        return;
    }
    if (!s_statistics[algo_type].is_open)
    {
        return;
    }
    s_statistics[algo_type].cost_start = HAL_GetTick();
}

/**
 * @brief 统计算法耗时算法停止
 *
 * @param algo_type 算法统计类型 ALGO_STATISTICS_TYPE
 */
void algo_stop_statistics(uint32_t algo_type)
{
    if (algo_type >= ALGO_MAX)
    {
        ALGO_FWK_LOG_E("statistics stop algo_type:%d", algo_type);
        return;
    }
    if (!s_statistics[algo_type].is_open)
    {
        return;
    }
    s_statistics[algo_type].count++;
    uint32_t cost = HAL_GetTick() - s_statistics[algo_type].cost_start;
    s_statistics[algo_type].cost_sum += cost;
    s_statistics[algo_type].cost_max = cost > s_statistics[algo_type].cost_max ? cost : s_statistics[algo_type].cost_max;

    if (s_statistics[algo_type].cost_min == 0)
    {
        s_statistics[algo_type].cost_min = cost;
    }
    else
    {
        s_statistics[algo_type].cost_min = cost < s_statistics[algo_type].cost_min ? cost : s_statistics[algo_type].cost_min;
    }
    s_statistics[algo_type].cost_ave = s_statistics[algo_type].cost_sum / s_statistics[algo_type].count;
    s_statistics[algo_type].utc = (uint32_t)get_sec_from_rtc();
}

/**
 * @brief 统计算法耗时保存
 *
 * @param file_name 文件名
 * @param tag 标签
 * @param algo_type 算法统计类型 ALGO_STATISTICS_TYPE
 */
void algo_save_statistics(const char* file_name, const char* tag, uint32_t algo_type)
{
    if (algo_type >= ALGO_MAX)
    {
        ALGO_FWK_LOG_E("statistics save algo_type:%d", algo_type);
        return;
    }
    if (!s_statistics[algo_type].is_open)
    {
        return;
    }
    if (s_statistics[algo_type].utc - s_statistics[algo_type].last_utc >= STATISTICS_LOG_DEBUG_RESAMPLE)
    {
        s_statistics[algo_type].last_utc = s_statistics[algo_type].utc;
#ifdef STATISTICS_SAVE_TO_FILE
        char buff[ALGO_STATISTICS_DATA_LEN] = {0};
        int32_t ret = snprintf(buff, ALGO_STATISTICS_DATA_LEN, "%s,count:%u,sum:%u,max:%u,min:%u,ave:%u,utc:%u\n",
                tag, s_statistics[algo_type].count, s_statistics[algo_type].cost_sum, s_statistics[algo_type].cost_max,
                s_statistics[algo_type].cost_min, s_statistics[algo_type].cost_ave, s_statistics[algo_type].utc);
        if (ret < 0)
        {
            ALGO_FWK_LOG_E("statistics save ret:%d", ret);
        }
        algo_statistics_save(file_name, buff, ret);
#else
        ALGO_FWK_LOG_I("%s_alg_cost,count:%u,sum:%u,max:%u,min:%u,ave:%u,utc:%u",
            tag, s_statistics[algo_type].count, s_statistics[algo_type].cost_sum, s_statistics[algo_type].cost_max,
            s_statistics[algo_type].cost_min, s_statistics[algo_type].cost_ave, s_statistics[algo_type].utc);
#endif // STATISTICS_SAVE_TO_FILE
    }
}

// 数据采集
static osMutexId_t s_collect_mutex = NULL;
static ALGO_DATA_COLLECT_TYPE s_collect_type = ALGO_COLLECT_TYPE_MAX;
static bool s_is_save_head[ALGO_COLLECT_TYPE_MAX] = {0};
static bool s_data_collect_open[ALGO_COLLECT_TYPE_MAX] = {0}; // 是否开始采集数据
static int32_t s_data_collect_fp[ALGO_COLLECT_TYPE_MAX] = {0}; // 采集数据文件句柄

// 缓冲区控制

L2_RET_BSS_SECT_BEGIN(gm_collect)
ALIGN(4) static algo_collect_buffer_t s_collect_buffers[ALGO_COLLECT_TYPE_MEAN_MAX] = {0};
L2_RET_BSS_SECT_END


static uint8_t s_collect_count = 0; // 采集数据计数
const char* s_data_collect_tag[ALGO_COLLECT_TYPE_MAX] = {
    "Raise_Wrist",  // ALGO_COLLECT_RAISE_WRIST
    "Daily", // ALGO_COLLECT_DEILY
    "Walk", // ALGO_COLLECT_WALK
    "Run", // ALGO_COLLECT_RUN
    "Cycle", // ALGO_COLLECT_CYCLE
    "Swim", // ALGO_COLLECT_SWIM
    "Jump_Rope", // ALGO_COLLECT_JUMP_ROPE
    "Dumbbell", // ALGO_COLLECT_DUMBBELL
    "Elliptical", // ALGO_COLLECT_ELLIPTICAL
    "Rowing", // ALGO_COLLECT_ROWING
    "Mountain_Climb", // ALGO_COLLECT_MOUNTAIN_CLIMBING
    "Skiing",// ALGO_COLLECT_SKI
    "Others", // ALGO_COLLECT_OTHERS
};

/**
 * @brief 根据类型获取缓冲区索引
 *
 * @param algo_type 算法统计类型 ALGO_DATA_COLLECT_TYPE
 * @return 缓冲区索引
 */
static int8_t algo_get_collect_buffer_by_type(uint32_t algo_type)
{
    for (int i = 0; i < ALGO_COLLECT_TYPE_MEAN_MAX; i++)
    {
        if (s_collect_buffers[i].collect_type == algo_type)
        {
            return i;
        }
    }
    return -1;
}

/**
 * @brief 根据缓冲区索引获取类型
 *
 * @param index 缓冲区索引
 * @return 算法统计类型 ALGO_DATA_COLLECT_TYPE
 */
static uint32_t algo_get_collect_type_by_index(uint32_t index)
{
    if (index >= ALGO_COLLECT_TYPE_MEAN_MAX)
    {
        return -1;
    }
    return s_collect_buffers[index].collect_type;
}

/**
 * @brief 异步数据采集输出到所有文件
 *
 * @param algo_type 算法统计类型 ALGO_DATA_COLLECT_TYPE
 * @param data 数据
 * @param data_len 数据长度
 */
static void algo_collect_output_to_file(uint32_t algo_type, const char *data, uint32_t data_len)
{
    if (algo_type >= ALGO_COLLECT_TYPE_MAX || !s_data_collect_open[algo_type])
    {
        return;
    }

    // 加锁保护文件写入操作
    osMutexAcquire(s_collect_mutex, osWaitForever);

    uint32_t offset = 0;

    while (offset < data_len)
    {
        // 可以调节每次写入多少，暂时保留
        uint32_t write_len = (data_len - offset > ALGO_COLLECT_CHUNK_SIZE) ? ALGO_COLLECT_CHUNK_SIZE : (data_len - offset);
        uint32_t br;
        int32_t ret = qw_mf_write(s_data_collect_fp[algo_type], data + offset, write_len, &br);

        if (ret != 0 || br != write_len)
        {
            ALGO_FWK_LOG_E("collect outfile write ret:%d,br:%d,len:%d,offset:%d", ret, br, write_len, offset);

            // 如果写入失败，等待一段时间后重试一次
            rt_thread_mdelay(20);
            ret = qw_mf_write(s_data_collect_fp[algo_type], data + offset, write_len, &br);
            if (ret != 0 || br != write_len)
            {
                ALGO_FWK_LOG_E("collect outfile write retry ret:%d,br:%d,len:%d,offset:%d", ret, br, write_len, offset);
                break; // 重试失败，跳出循环避免无限重试
            }
        }

        offset += write_len;
    }

    osMutexRelease(s_collect_mutex);
}

/**
 * @brief 刷新指定类型的缓冲区到文件
 * @param index 缓冲区索引
 */
static void flush_buffer_to_file(int8_t index)
{
    if (index >= ALGO_COLLECT_TYPE_MEAN_MAX)
        return;

    algo_collect_buffer_t *buffer = &s_collect_buffers[index];

    if (buffer->offset == 0)
        return;

    uint32_t written_len = 0;
    uint32_t algo_type = algo_get_collect_type_by_index(index);
    while (buffer->offset > 0 && algo_type != -1)
    {
        uint32_t write_len = (buffer->offset > ALGO_COLLECT_DATA_LEN) ? ALGO_COLLECT_DATA_LEN : buffer->offset;
        algo_collect_output_to_file(algo_type, buffer->buffer + written_len, write_len);
        written_len += write_len;
        buffer->offset -= write_len;
    }
    memset(buffer->buffer, 0, ALGO_COLLECT_BUFFER_SIZE);
}

/**
 * @brief 采集数据开始
 *
 * @param file_name_tag 文件名标签
 * @param algo_type 算法统计类型 ALGO_DATA_COLLECT_TYPE
 */
static void algo_collect_start(const char* file_name_tag, uint32_t algo_type)
{
    if (file_name_tag == NULL || algo_type >= ALGO_COLLECT_TYPE_MAX)
    {
        ALGO_FWK_LOG_E("collect start algo_type:%u", algo_type);
        return;
    }
    osMutexAcquire(s_collect_mutex, osWaitForever);
    if (s_data_collect_open[algo_type])
    {
        ALGO_FWK_LOG_E("collect start algo_type:%u already open", algo_type);
        osMutexRelease(s_collect_mutex);
        return;
    }

    // 打开文件、创建文件
    char path_buf[ALGO_STATISTICS_FILE_LEN] = {0};
    qw_tm_t tm_time = {0};
    utc_to_localtime(get_sec_from_rtc(), &tm_time);
    if (snprintf(path_buf, ALGO_STATISTICS_FILE_LEN, "%s/%s_%04d-%02d-%02d-%02d-%02d-%02d.csv",
        CAPTURE_PATH, file_name_tag, (1900 + tm_time.tm_year),
        (tm_time.tm_mon + 1), tm_time.tm_mday, tm_time.tm_hour, tm_time.tm_min, tm_time.tm_sec) < 0)
    {
        ALGO_FWK_LOG_E("collect start snprintf");
        osMutexRelease(s_collect_mutex);
        return;
    }
    s_data_collect_fp[algo_type] = qw_mf_open(path_buf, QW_FA_OPEN_ALWAYS | QW_FA_WRITE | QW_FA_APPEND);
    if (s_data_collect_fp[algo_type] == NULL || s_data_collect_fp[algo_type] == -1)
    {
        ALGO_FWK_LOG_E("collect start ret:%d", s_data_collect_fp[algo_type]);
        osMutexRelease(s_collect_mutex);
        return;
    }
    for (int i = 0; i < ALGO_COLLECT_TYPE_MEAN_MAX; i++)
    {
        if (s_collect_buffers[i].collect_type == -1) {
            s_collect_buffers[i].collect_type = algo_type;
            s_collect_buffers[i].offset = 0;
            memset(s_collect_buffers[i].buffer, 0, ALGO_COLLECT_BUFFER_SIZE);
            break;
        }

        if (i == ALGO_COLLECT_TYPE_MEAN_MAX - 1) {
            ALGO_FWK_LOG_E("collect start algo_type:%u buffer full", algo_type);
            osMutexRelease(s_collect_mutex);
            return;
        }
    }
    s_data_collect_open[algo_type] = true;
    s_collect_type = algo_type;
    osMutexRelease(s_collect_mutex);
    ALGO_FWK_LOG_I("collect start algo_type:%u,file_name:%s ok", algo_type, path_buf);
}

/**
 * @brief 采集数据停止
 *
 * @param algo_type 算法统计类型 ALGO_DATA_COLLECT_TYPE
 */
static void algo_collect_stop(uint32_t algo_type)
{
    osMutexAcquire(s_collect_mutex, osWaitForever);
    if (algo_type >= ALGO_COLLECT_TYPE_MAX)
    {
        ALGO_FWK_LOG_E("collect stop algo_type:%u invalid", algo_type);
        osMutexRelease(s_collect_mutex);
        return;
    }
    if (!s_data_collect_open[algo_type])
    {
        ALGO_FWK_LOG_E("collect stop algo_type:%u already closed", algo_type);
        osMutexRelease(s_collect_mutex);
        return;
    }

    // 清空对应的缓冲区
    int8_t index = algo_get_collect_buffer_by_type(algo_type);
    if (index == -1)
    {
        ALGO_FWK_LOG_E("collect stop buffer not found type:%u", algo_type);
    }
    else
    {
        flush_buffer_to_file(index);
        s_collect_buffers[index].offset = 0;
        s_collect_buffers[index].collect_type = -1;
        memset(s_collect_buffers[index].buffer, 0, ALGO_COLLECT_BUFFER_SIZE);
    }

    // 关闭文件
    int32_t ret = qw_mf_close(s_data_collect_fp[algo_type]);
    if (ret != 0)
    {
        ALGO_FWK_LOG_E("collect stop close ret:%d", ret);
        osMutexRelease(s_collect_mutex);
        return;
    }
    s_data_collect_open[algo_type] = false;
    s_is_save_head[algo_type] = false;
    s_data_collect_fp[algo_type] = 0;
    s_collect_type = ALGO_COLLECT_TYPE_MAX;
    osMutexRelease(s_collect_mutex);
    ALGO_FWK_LOG_I("collect stop algo_type:%u ok", algo_type);
}

/**
 * @brief 采集数据开发者模式处理
 *
 * @param algo_type 算法统计类型 ALGO_DATA_COLLECT_TYPE
 * @param on_off 开关状态
 */
static void algo_collect_dm_proc(uint32_t algo_type, bool on_off)
{
    if (algo_type >= ALGO_COLLECT_TYPE_MAX)
    {
        ALGO_FWK_LOG_E("dm algo_type:%u is invalid", algo_type);
        return;
    }
    if (on_off)
    {
        algo_collect_start(s_data_collect_tag[algo_type], algo_type);
    }
    else
    {
        algo_collect_stop(algo_type);
    }
}

/**
 * @brief 采集数据保存
 *
 * @param algo_type 算法统计类型 ALGO_STATISTICS_TYPE
 * @param format 格式化数据
 */
static void collect_save_data(uint32_t algo_type, const char *format,...)
{
    va_list ap;
    va_start(ap, format);
    char buf[ALGO_COLLECT_DATA_LEN] = {0};
    int len = vsnprintf(buf, ALGO_COLLECT_DATA_LEN, format, ap);
    va_end(ap);

    if (len <= 0 || len >= ALGO_COLLECT_DATA_LEN)
    {
        ALGO_FWK_LOG_E("collect save input len invalid: %d", len);
        return;
    }

    if (algo_type >= ALGO_COLLECT_TYPE_MAX)
    {
        ALGO_FWK_LOG_E("collect save invalid type:%u", algo_type);
        return;
    }
    int8_t index = algo_get_collect_buffer_by_type(algo_type);
    if (index == -1)
    {
        ALGO_FWK_LOG_E("collect save buffer not found type:%u", algo_type);
        return;
    }

    algo_collect_buffer_t *buffer = &s_collect_buffers[index];

    // 如果数据会超就先flush
    if (buffer->offset + len >= ALGO_COLLECT_BUFFER_SIZE)
    {
        flush_buffer_to_file(index);
    }

    memcpy(buffer->buffer + buffer->offset, buf, len);
    buffer->offset += len;

    // 检查是否达到写入阈值
    if (buffer->offset >= ALGO_COLLECT_WRITE_THRESHOLD)
    {
        flush_buffer_to_file(index);
    }
}

#if (ALGO_GM_DEBUG_ENABLE)
// gomore debug日志开关
static bool s_gm_debug_open = false;

/**
 * @brief 检查GM调试服务是否开启
 *
 * @return true GM调试服务开启
 * @return false GM调试服务关闭
 */
bool algo_service_gm_debug_is_open(void)
{
    return s_gm_debug_open;
}
#endif

/**
 * @brief 开发者选项数据订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void developer_optional_callback(const void *in, uint32_t len)
{
    // 输入数据发布后通知算法线程
    dev_mode_pub_t *pub_data = (dev_mode_pub_t *)in;
    ALGO_FWK_LOG_I("dev len:%u,mode:%u,mode_sta:%u", len, pub_data->dev_mode_type, pub_data->dev_mode_sta);
    if (pub_data->dev_mode_type == DM_GOMORE_CAST)
    {
        if (pub_data->dev_mode_sta == 0) {
            memset(&s_statistics[ALGO_GOMORE], 0, sizeof(algo_statistics_t));
        }
        s_statistics[ALGO_GOMORE].is_open = pub_data->dev_mode_sta;
    } else if (pub_data->dev_mode_type == DM_COODIX_CAST) {
        if (pub_data->dev_mode_sta == 0) {
            memset(&s_statistics[ALGO_COODIX], 0, sizeof(algo_statistics_t));
        }
        s_statistics[ALGO_COODIX].is_open = pub_data->dev_mode_sta;
    } else if (pub_data->dev_mode_type == DM_RAISE_WRIST) {
        algo_collect_dm_proc(ALGO_COLLECT_RAISE_WRIST, pub_data->dev_mode_sta);
    } else if (pub_data->dev_mode_type == DM_COLLECT_DEILY) {
        algo_collect_dm_proc(ALGO_COLLECT_DEILY, pub_data->dev_mode_sta);
    } else if (pub_data->dev_mode_type == DM_COLLECT_WALK) {
        algo_collect_dm_proc(ALGO_COLLECT_WALK, pub_data->dev_mode_sta);
    } else if (pub_data->dev_mode_type == DM_COLLECT_RUN) {
        algo_collect_dm_proc(ALGO_COLLECT_RUN, pub_data->dev_mode_sta);
    } else if (pub_data->dev_mode_type == DM_COLLECT_CYCLE) {
        algo_collect_dm_proc(ALGO_COLLECT_CYCLE, pub_data->dev_mode_sta);
    } else if (pub_data->dev_mode_type == DM_COLLECT_SWIM) {
        algo_collect_dm_proc(ALGO_COLLECT_SWIM, pub_data->dev_mode_sta);
    } else if (pub_data->dev_mode_type == DM_COLLECT_JUMP_ROPE) {
        algo_collect_dm_proc(ALGO_COLLECT_JUMP_ROPE, pub_data->dev_mode_sta);
    } else if (pub_data->dev_mode_type == DM_COLLECT_DUMBBELL) {
        algo_collect_dm_proc(ALGO_COLLECT_DUMBBELL, pub_data->dev_mode_sta);
    } else if (pub_data->dev_mode_type == DM_COLLECT_ELLIPTICAL) {
        algo_collect_dm_proc(ALGO_COLLECT_ELLIPTICAL, pub_data->dev_mode_sta);
    } else if (pub_data->dev_mode_type == DM_COLLECT_ROWING) {
        algo_collect_dm_proc(ALGO_COLLECT_ROWING, pub_data->dev_mode_sta);
    } else if (pub_data->dev_mode_type == DM_COLLECT_MOUNTAIN_CLIMBING) {
        algo_collect_dm_proc(ALGO_COLLECT_MOUNTAIN_CLIMBING, pub_data->dev_mode_sta);
    } else if (pub_data->dev_mode_type == DM_COLLECT_SKI) {
        algo_collect_dm_proc(ALGO_COLLECT_SKI, pub_data->dev_mode_sta);
    } else if (pub_data->dev_mode_type == DM_COLLECT_OTHERS) {
        algo_collect_dm_proc(ALGO_COLLECT_OTHERS, pub_data->dev_mode_sta);
    } else if (pub_data->dev_mode_type == DM_GOMORE_DEBUG) {
        s_gm_debug_open = pub_data->dev_mode_sta;
    }
}

/**
 * @brief 算法统计初始化
 *
 */
void algo_statistics_init(void)
{
    osMutexAttr_t attr = {0};
    attr.name = "algo_collect_mutex";
    attr.attr_bits = osMutexRecursive | osMutexPrioInherit | osMutexRobust;
    s_collect_mutex = osMutexNew(NULL);

    // 初始化缓冲区
    for (int i = 0; i < ALGO_COLLECT_TYPE_MEAN_MAX; i++)
    {
        s_collect_buffers[i].offset = 0;
        s_collect_buffers[i].collect_type = -1;
        memset(s_collect_buffers[i].buffer, 0, ALGO_COLLECT_BUFFER_SIZE);
    }

    optional_config_t config = {.sampling_rate = 0};
    int32_t ret = qw_dataserver_subscribe_id(DATA_ID_EVENT_DEVELOPER_CHG, developer_optional_callback, &config);
    if (ret != 0)
    {
        ALGO_FWK_LOG_E("statistics init ret:%d", ret);
    }
}

/**
 * @brief 抬腕亮屏数据采集
 *
 * @param mIn 抬腕亮屏数据
 * @param bk_sta 屏幕亮灭状态
 * @param ret 算法返回值
 */
void algo_raise_wrist_collect(const IndexIOWristUp *mIn, uint8_t bk_sta, int16_t ret)
{
    // osMutexAcquire(s_collect_mutex, osWaitForever);
    if (s_data_collect_open[ALGO_COLLECT_RAISE_WRIST] == false)
    {
        ALGO_FWK_LOG_D("collect raise open:%u invalid", s_data_collect_open[ALGO_COLLECT_RAISE_WRIST]);
        // osMutexRelease(s_collect_mutex);
        return;
    }
    if (!s_is_save_head[ALGO_COLLECT_RAISE_WRIST])
    {
        s_is_save_head[ALGO_COLLECT_RAISE_WRIST] = true;
        // 用户信息
        lib_gm_user_info_t *user_info = lib_gm_get_user_info();
        collect_save_data(s_collect_type,"age,sex,height,weight,hrmax,hrrest,vo2max,workout_type\n");
        collect_save_data(s_collect_type,"%f,%f,%f,%f,%f,%f,%f,%u\n",user_info->age, user_info->sex, user_info->height,
            user_info->weight, user_info->hrmax, user_info->hrrest, user_info->vo2max, lib_gm_get_workout_type());
        collect_save_data(ALGO_COLLECT_RAISE_WRIST, "timestamp,accX,accY,accZ,accLength,event,mode,ret\n");
    }

    // 抬腕亮屏算法输入数据
    char *tmp0 = ",",*tmp1 = "\"", *tmp2 = "\",";

    // 填充 timestamp
    collect_save_data(ALGO_COLLECT_RAISE_WRIST, "%llu", mIn->timestamp);
    collect_save_data(ALGO_COLLECT_RAISE_WRIST, tmp0);

    // 填充 accX
    collect_save_data(ALGO_COLLECT_RAISE_WRIST, tmp1);
    for (uint32_t k = 0; k < mIn->accLength; k++)
    {
        collect_save_data(ALGO_COLLECT_RAISE_WRIST, "%f", mIn->accX[k]);
        if(k != mIn->accLength - 1)
            collect_save_data(ALGO_COLLECT_RAISE_WRIST, tmp0);
    }
    collect_save_data(ALGO_COLLECT_RAISE_WRIST, tmp2);

    // 填充 accY
    collect_save_data(ALGO_COLLECT_RAISE_WRIST, tmp1);
    for (uint32_t k = 0; k < mIn->accLength; k++)
    {
        collect_save_data(ALGO_COLLECT_RAISE_WRIST, "%f", mIn->accY[k]);
        if(k != mIn->accLength - 1)
            collect_save_data(ALGO_COLLECT_RAISE_WRIST, tmp0);
    }
    collect_save_data(ALGO_COLLECT_RAISE_WRIST, tmp2);

    // 填充 accZ
    collect_save_data(ALGO_COLLECT_RAISE_WRIST, tmp1);
    for (uint32_t k = 0; k < mIn->accLength; k++)
    {
        collect_save_data(ALGO_COLLECT_RAISE_WRIST, "%f", mIn->accZ[k]);
        if(k != mIn->accLength - 1)
            collect_save_data(ALGO_COLLECT_RAISE_WRIST, tmp0);
    }
    collect_save_data(ALGO_COLLECT_RAISE_WRIST, tmp2);

    // 填充 acc长度/event/屏幕状态/算法返回值
    collect_save_data(ALGO_COLLECT_RAISE_WRIST, "%u,%u,%u,%d\n", mIn->accLength, mIn->event, bk_sta, ret);
    // osMutexRelease(s_collect_mutex);
}

/**
 * @brief gomore数据采集文件头
 *
 */
static void algo_gomore_collect_title(void)
{
    int32_t relVer[4] = {0}; // 0主版本号 1次版本号 2维护版本号 3日期
    getReleaseVersion(relVer);
    collect_save_data(s_collect_type, "Gomore version:%d:%d:%d:%d\n", relVer[0], relVer[1], relVer[2], relVer[3]);

    // 用户信息
    lib_gm_user_info_t *user_info = lib_gm_get_user_info();
    cfg_user_information_t* u_info = sensor_core_get_user_info(); // 获取用户信息
    collect_save_data(s_collect_type,"age,sex,height,weight,hrmax,hrrest,vo2max,handFlag,workout_type\n");
    collect_save_data(s_collect_type,"%f,%f,%f,%f,%f,%f,%f,%u,%u\n",user_info->age, user_info->sex, user_info->height,
        user_info->weight, user_info->hrmax, user_info->hrrest, user_info->vo2max, !u_info->hand_habit, lib_gm_get_workout_type());

    // 实时输入项：公有项
    if (s_collect_type == ALGO_COLLECT_SWIM)
    {
        collect_save_data(s_collect_type, "timestamp,timeZoneOffset,ppg1,ppiRef,accX,accY,accZ,gyroX,gyroY,gyroZ,magX,magY,magZ,ppgNumChannels,"
            "ppgLength,ppiSize,accLength,gyroLength,magLength,longitude,latitude,altitude,gpsSpeed,gpsAccuracy,incline,hrRef,"
            "speedRef,cyclingCadence,cyclingPower,wristOff,");
    }
    else
    {
        collect_save_data(s_collect_type, "timestamp,timeZoneOffset,ppg1,ppiRef,accX,accY,accZ,gyroX,gyroY,gyroZ,ppgNumChannels,"
            "ppgLength,ppiSize,accLength,gyroLength,longitude,latitude,altitude,gpsSpeed,gpsAccuracy,incline,hrRef,"
            "speedRef,cyclingCadence,cyclingPower,wristOff,");
    }

    // 实时输出项：公有项
    collect_save_data(s_collect_type, "paceOut,speedOut,stepCountOut,stressOut,allDayStaminaOut,kcalOut,fatOut,carbOut,"
        "METsOut[2],METsOut[3],METsOut[4],activityKcalOut,hrRestOut,hrRestEstStatusOut,hrvOut,speedStatusOut,speedSourceOut,");
    collect_save_data(s_collect_type,
        "activeTypeOut,cadenceOut,sleepStageOut,sleepStageStatusOut,sleepStagePpgOnOffOut,sleepPeriodStatusOut,");

    // fitness实时输出(UpdateWorkoutOutput)：私有项
    if (s_collect_type == ALGO_COLLECT_DEILY) { // 日常：日常计步、卡路里、强度活动时长、睡眠、压力、hrv
        // 在共有项默认输出
    }
    else if (s_collect_type == ALGO_COLLECT_WALK) // fitness：步行运动
    {
        collect_save_data(s_collect_type, "cadence,stepCnt,stance,flight,staFlightRatio,staBalance,"
            "verticalOscillation,runPowerWatt,stepLen,strideLen,");
        collect_save_data(s_collect_type, "totalStep,pace,maxPace,minPace,avgPace,avgSpeed,avgCadence,distance,");
    }
    else if (s_collect_type == ALGO_COLLECT_RUN) // fitness:跑步运动
    {
        collect_save_data(s_collect_type, "cadence,stepCnt,stance,flight,staFlightRatio,staBalance,"
            "verticalOscillation,runPowerWatt,stepLen,strideLen,");
        collect_save_data(s_collect_type, "totalStep,pace,maxPace,minPace,avgPace,avgSpeed,avgCadence,distance,");
    }
    else if (s_collect_type == ALGO_COLLECT_CYCLE) // fitness：骑行运动
    {
        // 此项没有私有数据
    }
    else if (s_collect_type == ALGO_COLLECT_SWIM) // fitness：游泳运动
    {
        collect_save_data(s_collect_type, "startIndex,endIndex,realtimeLapCnt,cntStroke,updateType,frontcrawlPerc,breaststrokePerc,"
            "backstrokePerc,butterflyPerc,updateStatus,");
    }
    else if (s_collect_type == ALGO_COLLECT_JUMP_ROPE) // fitness：跳绳运动
    {
        collect_save_data(s_collect_type, "totalCount,tripCount,sessionCount,");
    }
    else if (s_collect_type == ALGO_COLLECT_DUMBBELL) // fitness：力量运动
    {
        collect_save_data(s_collect_type, "dumbbellCount,");
    }
    else if (s_collect_type == ALGO_COLLECT_ELLIPTICAL) // fitness：椭圆机运动
    {
        collect_save_data(s_collect_type, "cadence,steps,");
    }
    else if (s_collect_type == ALGO_COLLECT_ROWING) // fitness：划船机运动
    {
        collect_save_data(s_collect_type, "cadence,rowCnt,");
    }
    else if (s_collect_type == ALGO_COLLECT_MOUNTAIN_CLIMBING) // fitness：徒步登山运动
    {
        collect_save_data(s_collect_type, "cadence,stepCnt,stance,flight,staFlightRatio,staBalance,"
            "verticalOscillation,runPowerWatt,stepLen,strideLen,");
        collect_save_data(s_collect_type, "totalStep,pace,maxPace,minPace,avgPace,avgSpeed,avgCadence,distance,");
    }
    else if (s_collect_type == ALGO_COLLECT_SKI) // fitness：滑雪运动
    {
        collect_save_data(s_collect_type, "avgSpeed,maxSpeed,distance,");
    }
    else if (s_collect_type == ALGO_COLLECT_OTHERS) // fitness：其他运动
    {
        // 此项无
    } else {
        ALGO_FWK_LOG_E("collect GM title type:%u is invalid", s_collect_type);
    }

    // fitness实时输出(mFitnessUpdate)：公有项
    if (s_collect_type >= ALGO_COLLECT_WALK && s_collect_type <= ALGO_COLLECT_OTHERS)
    {
        collect_save_data(s_collect_type, "aerobic,anaerobic,stamina,teAerobic,teAnaerobic,teStamina,trainingLoad,fitnessStatus,fitnessNotifierGPSOut,");
    }

    // 实时输出项：公有项
    collect_save_data(s_collect_type, "ppiRefConfidence,gm_run_tick,ret\n");
}

/**
 * @brief gomore数据采集文件数据
 *
 * @param mInput gomore数据
 * @param run_tick 单次执行时间
 * @param ret 算法返回值
 */
static void algo_gomore_collect_data(const IndexIO *mInput, uint32_t run_tick, int16_t ret)
{
    // gomore共有算法输入数据
    char *tmp0 = "\",\"",*tmp1 = "\"", *tmp2 = "\",", *tmp3 = ",";

    // 实时输入项：公有项
    collect_save_data(s_collect_type,"%u ,%d ,", mInput->timestamp, mInput->timeZoneOffset);

    collect_save_data(s_collect_type, tmp1);
    for (int k = 0; k < mInput->ppgLength; k++)
    {
        collect_save_data(s_collect_type, "%f", mInput->ppg1[k]);
        if(k != mInput->ppgLength - 1)
            collect_save_data(s_collect_type, tmp3);
    }
    collect_save_data(s_collect_type, tmp0);
    for (int k = 0; k < mInput->ppiSize; k++)                     // 填充 ppiRef
    {
        collect_save_data(s_collect_type, "%u", mInput->ppiRef[k]);
        if(k != mInput->ppiSize - 1)
            collect_save_data(s_collect_type, tmp3);
    }
    collect_save_data(s_collect_type, tmp0);
    for (int k = 0; k < mInput->accLength; k++)                     // 填充 accX
    {
        collect_save_data(s_collect_type, "%f", mInput->accX[k]);
        if(k != mInput->accLength - 1)
            collect_save_data(s_collect_type, tmp3);
    }
    collect_save_data(s_collect_type, tmp0);
    for (int k = 0; k < mInput->accLength; k++)                     // 填充 accY
    {
        collect_save_data(s_collect_type, "%f", mInput->accY[k]);
        if(k != mInput->accLength - 1)
            collect_save_data(s_collect_type, tmp3);
    }
    collect_save_data(s_collect_type, tmp0);
    for (int k = 0; k < mInput->accLength; k++)                    // 填充 accZ
    {
        collect_save_data(s_collect_type, "%f", mInput->accZ[k]);
        if(k != mInput->accLength - 1)
            collect_save_data(s_collect_type, tmp3);
    }
    collect_save_data(s_collect_type, tmp0);
    for (int k = 0; k < mInput->gyroLength; k++)                     // 填充 gyroX
    {
        collect_save_data(s_collect_type, "%f", mInput->gyroX[k]);
        if(k != mInput->gyroLength - 1)
            collect_save_data(s_collect_type, tmp3);
    }
    collect_save_data(s_collect_type, tmp0);
    for (int k = 0; k < mInput->gyroLength; k++)                    // 填充 gyroY
    {
        collect_save_data(s_collect_type, "%f", mInput->gyroY[k]);
        if(k != mInput->gyroLength - 1)
            collect_save_data(s_collect_type, tmp3);
    }
    collect_save_data(s_collect_type, tmp0);
    for (int k = 0; k < mInput->gyroLength; k++)                    // 填充 gyroZ
    {
        collect_save_data(s_collect_type, "%f", mInput->gyroZ[k]);
        if(k != mInput->gyroLength - 1)
            collect_save_data(s_collect_type, tmp3);
    }
    if (s_collect_type == ALGO_COLLECT_SWIM)
    {
        collect_save_data(s_collect_type, tmp0);
        for (int k = 0; k < mInput->magLength; k++)                   // 填充magX
        {
            collect_save_data(s_collect_type, "%f", mInput->magX[k]);
            if(k != mInput->magLength - 1)
                collect_save_data(s_collect_type, tmp3);
        }
        collect_save_data(s_collect_type, tmp0);
        for (int k = 0; k < mInput->magLength; k++)                    // 填充 magY
        {
            collect_save_data(s_collect_type, "%f", mInput->magY[k]);
            if(k != mInput->magLength - 1)
                collect_save_data(s_collect_type, tmp3);
        }
        collect_save_data(s_collect_type, tmp0);
        for (int k = 0; k < mInput->magLength; k++)                    // 填充 magZ
        {
            collect_save_data(s_collect_type, "%f", mInput->magZ[k]);
            if(k != mInput->magLength - 1)
                collect_save_data(s_collect_type, tmp3);
        }
    }
    collect_save_data(s_collect_type, tmp2);
    if (s_collect_type == ALGO_COLLECT_SWIM)
    {
        collect_save_data(s_collect_type, "%u,%u,%u,%u,%u,%u,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%u,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%u,%f,%d,%d,%d,%u,%d,%d,%d,%u,",
            mInput->ppgNumChannels, mInput->ppgLength, mInput->ppiSize, mInput->accLength, mInput->gyroLength, mInput->magLength,
            mInput->longitude, mInput->latitude, mInput->altitude, mInput->gpsSpeed, mInput->gpsAccuracy, mInput->incline,
            mInput->hrRef, mInput->speedRef, mInput->cyclingCadence, mInput->cyclingPower, mInput->wristOff,
            mInput->paceOut, mInput->speedOut, mInput->stepCountOut, mInput->stressOut, mInput->allDayStaminaOut, mInput->kcalOut,
            mInput->fatOut, mInput->carbOut, mInput->METsOut[2], mInput->METsOut[3], mInput->METsOut[4], mInput->activityKcalOut, get_rhr_window_value(),
            mInput->hrRestEstStatusOut, mInput->hrvOut, mInput->speedStatusOut, mInput->speedSourceOut, mInput->activeTypeOut, mInput->cadenceOut,
            mInput->sleepStageOut, mInput->sleepStageStatusOut, mInput->sleepStagePpgOnOffOut, mInput->sleepPeriodStatusOut);
    }
    else
    {
        collect_save_data(s_collect_type, "%u,%u,%u,%u,%u,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%u,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%u,%f,%d,%d,%d,%u,%d,%d,%d,%u,",
            mInput->ppgNumChannels, mInput->ppgLength, mInput->ppiSize, mInput->accLength, mInput->gyroLength,
            mInput->longitude, mInput->latitude, mInput->altitude, mInput->gpsSpeed, mInput->gpsAccuracy, mInput->incline,
            mInput->hrRef, mInput->speedRef, mInput->cyclingCadence, mInput->cyclingPower, mInput->wristOff,
            mInput->paceOut, mInput->speedOut, mInput->stepCountOut, mInput->stressOut, mInput->allDayStaminaOut, mInput->kcalOut,
            mInput->fatOut, mInput->carbOut, mInput->METsOut[2], mInput->METsOut[3], mInput->METsOut[4], mInput->activityKcalOut, get_rhr_window_value(),
            mInput->hrRestEstStatusOut, mInput->hrvOut, mInput->speedStatusOut, mInput->speedSourceOut, mInput->activeTypeOut, mInput->cadenceOut,
            mInput->sleepStageOut, mInput->sleepStageStatusOut, mInput->sleepStagePpgOnOffOut, mInput->sleepPeriodStatusOut);
    }

    // fitness实时输出(UpdateWorkoutOutput)：私有项
    if (s_collect_type == ALGO_COLLECT_DEILY) { // 日常：日常计步、卡路里、强度活动时长、睡眠、压力、hrv
        // 在共有项默认输出
    }
    else if (s_collect_type == ALGO_COLLECT_WALK) // fitness：步行运动
    {
        collect_save_data(s_collect_type, "%f,%d,%f,%f,%f,%f,%f,%f,%f,%f,%d,%f,%f,%f,%f,%f,%f,%f,",
            mInput->fitnessOut.workout.run.cadence, mInput->fitnessOut.workout.run.stepCnt,
            mInput->fitnessOut.workout.run.stance, mInput->fitnessOut.workout.run.flight,
            mInput->fitnessOut.workout.run.staFlightRatio, mInput->fitnessOut.workout.run.staBalance,
            mInput->fitnessOut.workout.run.verticalOscillation, mInput->fitnessOut.workout.run.runPowerWatt,
            mInput->fitnessOut.workout.run.stepLen, mInput->fitnessOut.workout.run.strideLen,
            mInput->fitnessOut.workout.run.totalStep, mInput->fitnessOut.workout.run.pace,
            mInput->fitnessOut.workout.run.maxPace, mInput->fitnessOut.workout.run.minPace,
            mInput->fitnessOut.workout.run.avgPace, mInput->fitnessOut.workout.run.avgSpeed,
            mInput->fitnessOut.workout.run.avgCadence, mInput->fitnessOut.workout.run.distance);
    }
    else if (s_collect_type == ALGO_COLLECT_RUN) // fitness:跑步运动
    {
        collect_save_data(s_collect_type, "%f,%d,%f,%f,%f,%f,%f,%f,%f,%f,%d,%f,%f,%f,%f,%f,%f,%f,",
            mInput->fitnessOut.workout.run.cadence, mInput->fitnessOut.workout.run.stepCnt,
            mInput->fitnessOut.workout.run.stance, mInput->fitnessOut.workout.run.flight,
            mInput->fitnessOut.workout.run.staFlightRatio, mInput->fitnessOut.workout.run.staBalance,
            mInput->fitnessOut.workout.run.verticalOscillation, mInput->fitnessOut.workout.run.runPowerWatt,
            mInput->fitnessOut.workout.run.stepLen, mInput->fitnessOut.workout.run.strideLen,
            mInput->fitnessOut.workout.run.totalStep, mInput->fitnessOut.workout.run.pace,
            mInput->fitnessOut.workout.run.maxPace, mInput->fitnessOut.workout.run.minPace,
            mInput->fitnessOut.workout.run.avgPace, mInput->fitnessOut.workout.run.avgSpeed,
            mInput->fitnessOut.workout.run.avgCadence, mInput->fitnessOut.workout.run.distance);
    }
    else if (s_collect_type == ALGO_COLLECT_CYCLE) // fitness：骑行运动
    {
        // 此项没有私有数据
    }
    else if (s_collect_type == ALGO_COLLECT_SWIM) // fitness：游泳运动
    {
        collect_save_data(s_collect_type, "%d,%d,%u,%u,%u,%u,%u,%u,%u,%u,",
            mInput->fitnessOut.workout.swim.startIndex, mInput->fitnessOut.workout.swim.endIndex,
            mInput->fitnessOut.workout.swim.realtimeLapCnt, mInput->fitnessOut.workout.swim.cntStroke,
            mInput->fitnessOut.workout.swim.updateType, mInput->fitnessOut.workout.swim.frontcrawlPerc,
            mInput->fitnessOut.workout.swim.breaststrokePerc, mInput->fitnessOut.workout.swim.backstrokePerc,
            mInput->fitnessOut.workout.swim.butterflyPerc, mInput->fitnessOut.workout.swim.updateStatus);
    }
    else if (s_collect_type == ALGO_COLLECT_JUMP_ROPE) // fitness：跳绳运动
    {
        collect_save_data(s_collect_type, "%d,%d,%d,",
            mInput->fitnessOut.workout.jumpRope.totalCount, mInput->fitnessOut.workout.jumpRope.tripCount,
            mInput->fitnessOut.workout.jumpRope.sessionCount);
    }
    else if (s_collect_type == ALGO_COLLECT_DUMBBELL) // fitness：力量训练运动
    {
        collect_save_data(s_collect_type, "%d,", mInput->fitnessOut.workout.dumbbell.dumbbellCount);
    }
    else if (s_collect_type == ALGO_COLLECT_ELLIPTICAL) // fitness：椭圆机运动
    {
        collect_save_data(s_collect_type, "%f,%d,",
            mInput->fitnessOut.workout.elliptical.cadence, mInput->fitnessOut.workout.elliptical.steps);
    }
    else if (s_collect_type == ALGO_COLLECT_ROWING) // fitness：划船机运动
    {
        collect_save_data(s_collect_type, "%f,%u,",
            mInput->fitnessOut.workout.row.cadence, mInput->fitnessOut.workout.row.rowCnt);
    }
    else if (s_collect_type == ALGO_COLLECT_MOUNTAIN_CLIMBING) // fitness：徒步登山运动
    {
        collect_save_data(s_collect_type, "%f,%d,%f,%f,%f,%f,%f,%f,%f,%f,%d,%f,%f,%f,%f,%f,%f,%f,",
            mInput->fitnessOut.workout.run.cadence, mInput->fitnessOut.workout.run.stepCnt,
            mInput->fitnessOut.workout.run.stance, mInput->fitnessOut.workout.run.flight,
            mInput->fitnessOut.workout.run.staFlightRatio, mInput->fitnessOut.workout.run.staBalance,
            mInput->fitnessOut.workout.run.verticalOscillation, mInput->fitnessOut.workout.run.runPowerWatt,
            mInput->fitnessOut.workout.run.stepLen, mInput->fitnessOut.workout.run.strideLen,
            mInput->fitnessOut.workout.run.totalStep, mInput->fitnessOut.workout.run.pace,
            mInput->fitnessOut.workout.run.maxPace, mInput->fitnessOut.workout.run.minPace,
            mInput->fitnessOut.workout.run.avgPace, mInput->fitnessOut.workout.run.avgSpeed,
            mInput->fitnessOut.workout.run.avgCadence, mInput->fitnessOut.workout.run.distance);
    }
    else if (s_collect_type == ALGO_COLLECT_SKI) // fitness：滑雪运动
    {
        collect_save_data(s_collect_type, "%f,%f,%f,",
            mInput->fitnessOut.workout.skiing.avgSpeed, mInput->fitnessOut.workout.skiing.maxSpeed,
            mInput->fitnessOut.workout.skiing.distance);
    }
    else if (s_collect_type == ALGO_COLLECT_OTHERS) // fitness：其他运动
    {
        // 此项无
    }
    else
    {
        ALGO_FWK_LOG_E("collect GM data type:%u is invalid", s_collect_type);
    }

    // fitness实时输出(mFitnessUpdate)：公有项
    if (s_collect_type >= ALGO_COLLECT_WALK && s_collect_type <= ALGO_COLLECT_OTHERS)
    {
        collect_save_data(s_collect_type, "%f,%f,%f,%f,%f,%f,%u,%d,%u,",
            mInput->fitnessOut.fitness.aerobic, mInput->fitnessOut.fitness.anaerobic,
            mInput->fitnessOut.fitness.stamina, mInput->fitnessOut.fitness.teAerobic,
            mInput->fitnessOut.fitness.teAnaerobic, mInput->fitnessOut.fitness.teStamina,
            mInput->fitnessOut.fitness.trainingLoad, mInput->fitnessOut.fitness.fitnessStatus,
            mInput->fitnessNotifierGPSOut);
    }

    // 填充 ppiRef置信度/算法返回值/执行时间
    collect_save_data(s_collect_type, "%u,%u,%d\n", lib_storage_ppi_confidence_get(), run_tick, ret);
}

/**
 * @brief gomore数据采集
 *
 * @param mInput gomore数据
 * @param run_tick 单次执行时间
 * @param ret 算法返回值
 */
void algo_gomore_collect(const IndexIO *mInput, uint32_t run_tick, int16_t ret)
{
    osMutexAcquire(s_collect_mutex, osWaitForever);
    if (s_collect_type >= ALGO_COLLECT_TYPE_MAX || s_collect_type <= ALGO_COLLECT_RAISE_WRIST) {
        ALGO_FWK_LOG_D("collect GM data type:%u is invalid", s_collect_type);
        osMutexRelease(s_collect_mutex);
        return;
    }
    if (s_data_collect_open[s_collect_type] == false) {
        ALGO_FWK_LOG_D("collect GM data type:%u close", s_collect_type);
        osMutexRelease(s_collect_mutex);
        return;
    }

    if ((s_collect_type == ALGO_COLLECT_DEILY) ||
        (s_collect_type != ALGO_COLLECT_DEILY && lib_gm_get_workout_type() != 0))
    {
        if (!s_is_save_head[s_collect_type]) {
            s_is_save_head[s_collect_type] = true;
            algo_gomore_collect_title();
        }
        algo_gomore_collect_data(mInput, run_tick, ret);
        if (s_collect_count++ >= 30) {
            s_collect_count = 0;
            int8_t index = algo_get_collect_buffer_by_type(s_collect_type);
            if (index != -1) {
                flush_buffer_to_file(index);
                if (s_collect_type >= ALGO_COLLECT_RAISE_WRIST && s_collect_type < ALGO_COLLECT_TYPE_MAX && s_data_collect_fp[s_collect_type])
                {
                    qw_mf_sync(s_data_collect_fp[s_collect_type]);
                }
            } else {
                ALGO_FWK_LOG_E("collect GM data flush index:%u is invalid", s_collect_type);
            }
        }
    }
    osMutexRelease(s_collect_mutex);
}

/**
 * @brief 保存运动总结数据
 * @param workout_type 运动类型
 * @param end_out 运动结束数据
 */
void algo_gomore_collect_fitness_end(int32_t workout_type, struct EndFitnessOutput *end_out)
{
    osMutexAcquire(s_collect_mutex, osWaitForever);
    if (s_collect_type >= ALGO_COLLECT_TYPE_MAX || s_collect_type <= ALGO_COLLECT_RAISE_WRIST) {
        ALGO_FWK_LOG_D("collect GM end type:%u invalid", s_collect_type);
        osMutexRelease(s_collect_mutex);
        return;
    }
    if (s_data_collect_open[s_collect_type] == false) {
        ALGO_FWK_LOG_D("collect GM end type:%u close", s_collect_type);
        osMutexRelease(s_collect_mutex);
        return;
    }

    collect_save_data(s_collect_type, "fitness type[%d] end summary switch[%d]:\n", workout_type, s_collect_type);

    // 总结数据:EndWorkoutOutput
    if (s_collect_type == ALGO_COLLECT_DEILY) { // 日常：日常计步、卡路里、强度活动时长、睡眠、压力、hrv
        // 在共有项默认输出
    }
    else if (s_collect_type == ALGO_COLLECT_WALK) // fitness：步行运动
    {
        collect_save_data(s_collect_type, "meanCadence,meanStrideLen,meanStance,meanFlight,meanPower,totalStepCount\n");
        collect_save_data(s_collect_type, "%f,%f,%f,%f,%f,%d\n",
            end_out->workout.runFormHand.meanCadence, end_out->workout.runFormHand.meanStrideLen,
            end_out->workout.runFormHand.meanStance, end_out->workout.runFormHand.meanFlight,
            end_out->workout.runFormHand.meanPower, end_out->workout.runFormHand.totalStepCount);
    }
    else if (s_collect_type == ALGO_COLLECT_RUN) // fitness:跑步运动
    {
        collect_save_data(s_collect_type, "meanCadence,meanStrideLen,meanStance,meanFlight,meanPower,totalStepCount\n");
        collect_save_data(s_collect_type, "%f,%f,%f,%f,%f,%d\n",
            end_out->workout.runFormHand.meanCadence, end_out->workout.runFormHand.meanStrideLen,
            end_out->workout.runFormHand.meanStance, end_out->workout.runFormHand.meanFlight,
            end_out->workout.runFormHand.meanPower, end_out->workout.runFormHand.totalStepCount);
    }
    else if (s_collect_type == ALGO_COLLECT_CYCLE) // fitness：骑行运动
    {
        collect_save_data(s_collect_type, "meanCadence,maxCadence\n");
        collect_save_data(s_collect_type, "%f,%f\n",
            end_out->workout.cyclingForm.meanCadence, end_out->workout.cyclingForm.maxCadence);
    }
    else if (s_collect_type == ALGO_COLLECT_SWIM) // fitness：游泳运动
    {
        collect_save_data(s_collect_type, "startIndex,endIndex,realtimeLapCnt,cntStroke,type,frontcrawlPerc,breaststrokePerc,"
            "backstrokePerc,butterflyPerc,status,strokeCnt,strokePerMin,strokePerLap,swolf,cntLap,swimDur\n");
        collect_save_data(s_collect_type, "%d,%d,%u,%u,%u,%u,%u,%u,%u,%u,%u,%f,%f,%f,%u,%u\n",
            end_out->workout.swim.startIndex, end_out->workout.swim.endIndex, end_out->workout.swim.realtimeLapCnt,
            end_out->workout.swim.cntStroke, end_out->workout.swim.type,
            end_out->workout.swim.frontcrawlPerc, end_out->workout.swim.breaststrokePerc,
            end_out->workout.swim.backstrokePerc, end_out->workout.swim.butterflyPerc,
            end_out->workout.swim.status, end_out->workout.swim.strokeCnt,
            end_out->workout.swim.strokePerMin, end_out->workout.swim.strokePerLap,
            end_out->workout.swim.swolf, end_out->workout.swim.cntLap,
            end_out->workout.swim.swimDur);
    }
    else if (s_collect_type == ALGO_COLLECT_JUMP_ROPE) // fitness：跳绳运动
    {
        // 此项无
    }
    else if (s_collect_type == ALGO_COLLECT_DUMBBELL) // fitness：力量训练运动
    {
        // 此项无
    }
    else if (s_collect_type == ALGO_COLLECT_ELLIPTICAL) // fitness：椭圆机运动
    {
        // 此项无
    }
    else if (s_collect_type == ALGO_COLLECT_ROWING) // fitness：划船机运动
    {
        // 此项无
    }
    else if (s_collect_type == ALGO_COLLECT_MOUNTAIN_CLIMBING) // fitness：徒步登山运动
    {
        collect_save_data(s_collect_type, "meanCadence,meanStrideLen,meanStance,meanFlight,meanPower,totalStepCount\n");
        collect_save_data(s_collect_type, "%f,%f,%f,%f,%f,%d\n",
            end_out->workout.runFormHand.meanCadence, end_out->workout.runFormHand.meanStrideLen,
            end_out->workout.runFormHand.meanStance, end_out->workout.runFormHand.meanFlight,
            end_out->workout.runFormHand.meanPower, end_out->workout.runFormHand.totalStepCount);
    }
    else if (s_collect_type == ALGO_COLLECT_SKI) // fitness：滑雪运动
    {
        // 此项无
    }
    else if (s_collect_type == ALGO_COLLECT_OTHERS) // fitness：其他运动
    {
        // 此项无
    }
    else
    {
        ALGO_FWK_LOG_E("collect GM end type:%u invalid", s_collect_type);
    }

    // 总结数据：mFitnessSummary
    collect_save_data(s_collect_type, "runLevelOut,vValueOut,pace,cyclingLevelOut,cValueOut,cyclingIndex,efficiency,recoveryTime,"
        "personalZone,bestRunTime,trainingLoad,tlTrend,trainingStatus,hrEstorOut,trainChecksum,trainChecksumLen\n");
    char *tmp0 = "\",\"", *tmp1 = "\"", *tmp2 = "\",", *tmp3 = ",";
    mFitnessSummary *mFit = &end_out->fitness;

    collect_save_data(s_collect_type, tmp1);

    collect_save_data(s_collect_type, "%f,%f,%f,%f,%f,%f,%f,%f,%f", mFit->runLevelOut[0], mFit->runLevelOut[1],
        mFit->runLevelOut[2], mFit->runLevelOut[3], mFit->runLevelOut[4], mFit->runLevelOut[5], mFit->runLevelOut[6],
        mFit->runLevelOut[7], mFit->runLevelOut[8]);
    collect_save_data(s_collect_type, tmp0);

    collect_save_data(s_collect_type, "%f,%d", mFit->vValueOut[0], mFit->vValueOut[1]);
    collect_save_data(s_collect_type, tmp0);

    collect_save_data(s_collect_type, "%f,%f", mFit->pace[0], mFit->pace[1]);
    collect_save_data(s_collect_type, tmp0);

    collect_save_data(s_collect_type, "%f,%f,%f,%f,%f,%f,%f,%f,%f", mFit->cyclingLevelOut[0],
        mFit->cyclingLevelOut[1], mFit->cyclingLevelOut[2], mFit->cyclingLevelOut[3],
        mFit->cyclingLevelOut[4], mFit->cyclingLevelOut[5], mFit->cyclingLevelOut[6],
        mFit->cyclingLevelOut[7], mFit->cyclingLevelOut[8]);

    collect_save_data(s_collect_type, tmp0);

    collect_save_data(s_collect_type, "%f,%f", mFit->cValueOut[0], mFit->cValueOut[1]);
    collect_save_data(s_collect_type, tmp0);

    collect_save_data(s_collect_type, "%f,%f", mFit->cyclingIndex[0], mFit->cyclingIndex[1]);
    collect_save_data(s_collect_type, tmp0);

    collect_save_data(s_collect_type, "%f,%f", mFit->efficiency[0], mFit->efficiency[1]);
    collect_save_data(s_collect_type, tmp2);

    collect_save_data(s_collect_type, "%d,", mFit->recoveryTime[0]);

    collect_save_data(s_collect_type, tmp1);

    collect_save_data(s_collect_type, "%f,%f,%f,%f,%f,%f,%f", mFit->personalZone[0],
        mFit->personalZone[1], mFit->personalZone[2], mFit->personalZone[3],
        mFit->personalZone[4], mFit->personalZone[5], mFit->personalZone[6]);
    collect_save_data(s_collect_type, tmp0);

    collect_save_data(s_collect_type, "%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d", mFit->bestRunTime[0],
        mFit->bestRunTime[1], mFit->bestRunTime[2], mFit->bestRunTime[3], mFit->bestRunTime[4],
        mFit->bestRunTime[5], mFit->bestRunTime[6], mFit->bestRunTime[7], mFit->bestRunTime[8],
        mFit->bestRunTime[9], mFit->bestRunTime[10]);
    collect_save_data(s_collect_type, tmp2);

    collect_save_data(s_collect_type, "%u,%f,%d,%f,", mFit->trainingLoad[0], mFit->tlTrend[0],
        mFit->trainingStatus[0], mFit->hrEstorOut[0]);

    collect_save_data(s_collect_type, tmp1);
    for (int k = 0; k < 128; k++)
    {
        collect_save_data(s_collect_type, "%d", mFit->trainChecksum[k]);
        if(k != 127)
            collect_save_data(s_collect_type, tmp3);
    }
    collect_save_data(s_collect_type, tmp2);

    collect_save_data(s_collect_type, "%u\n", mFit->trainChecksumLen[0]);
    osMutexRelease(s_collect_mutex);
}

/**
 * @brief 保存睡眠总结数据
 * @param outinfo 睡眠总结数据
 */
void algo_gomore_collect_sleep_end(struct SleepSummaryOutput *outinfo)
{
    osMutexAcquire(s_collect_mutex, osWaitForever);
    if (s_collect_type != ALGO_COLLECT_DEILY) {
        ALGO_FWK_LOG_D("collect GM slp end type:%u invalid", s_collect_type);
        osMutexRelease(s_collect_mutex);
        return;
    }
    if (s_data_collect_open[s_collect_type] == false) {
        ALGO_FWK_LOG_D("collect GM slp end type:%u is close", s_collect_type);
        osMutexRelease(s_collect_mutex);
        return;
    }

    // 总结数据:SleepSummaryOutput
    collect_save_data(s_collect_type, "startTS,endTS,numEpochs,totalSleepTime,waso,sleepPeriod,efficiency,wakeRatio,"
        "remRatio,lightRatio,deepRatio,wakeNumMinutes,remNumMinutes,lightNumMinutes,deepNumMinutes,score,type\n");
    collect_save_data(s_collect_type, "%u,%u,%d,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%f,%u\n",
        outinfo->startTS, outinfo->endTS, outinfo->numEpochs, outinfo->totalSleepTime, outinfo->waso,
        outinfo->sleepPeriod, outinfo->efficiency, outinfo->wakeRatio, outinfo->remRatio,
        outinfo->lightRatio, outinfo->deepRatio, outinfo->wakeNumMinutes, outinfo->remNumMinutes,
        outinfo->lightNumMinutes, outinfo->deepNumMinutes, outinfo->score, outinfo->type);

    char *tmp1 = "\"", *tmp2 = "\",", *tmp3 = ",";
    collect_save_data(s_collect_type, tmp1);
    for (int k = 0; k < GOMORE_STAGES_LENTH; k++)
    {
        collect_save_data(s_collect_type, "%d", outinfo->stages[k]);
        if(k != GOMORE_STAGES_LENTH - 1)
            collect_save_data(s_collect_type, tmp3);
    }
    collect_save_data(s_collect_type, tmp2);
    collect_save_data(s_collect_type, "\n");
    osMutexRelease(s_collect_mutex);
}
