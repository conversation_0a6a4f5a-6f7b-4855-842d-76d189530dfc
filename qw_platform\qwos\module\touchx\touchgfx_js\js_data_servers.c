/************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   js_data_servers.c
@Time    :   2025/01/18 15:14:02
*
************************************************************/
#include "js_data_servers.h"
#include "touchgfx_js.h"
#include "qw_data_type.h"
#include "sys_time.h"
#include "view_page_model.h"
#include "touchgfx_js_api.h"
#ifndef SIMULATOR
#include "service_gui_health.h"
#include "service_datetime.h"
#include "battery_srv.h"
#include "service_gui_daily_activity.h"
#include "../sport_ability/service_gui_sport_ability.h"
#include "ble_data_inf.h"
#include "../weather/service_weather.h"
#else
#include "qw_time_service.h"
#endif
#include "cfg_header_def.h"
#include "qw_time_util.h"
#include "qwos.h"

static int version = 1;

const JST_protos touchgfx_data_servers_protos[] = {
    {
        .name = "getVersion",
        .return_type = JS_TYPE_INT
    },
    {
        .name = "setVersion",
        .param_type = {JS_TYPE_INT},
        .return_type = JS_TYPE_NONE
    },
    {
        .name = "getIntByString",
        .param_type = {JS_TYPE_STRING},
        .return_type = JS_TYPE_INT
    },
    {
        .name = "getSysTime",
        .param_type = {JS_TYPE_INT},
        .return_type = JS_TYPE_STRING
    },
    {
        .name = "getHeartBeat",
        .return_type = JS_TYPE_STRING
    },
    {
        .name = "getSetpCount",
        .return_type = JS_TYPE_STRING
    },
    {
        .name = "getCalorie",
        .return_type = JS_TYPE_STRING
    },
    {
        .name = "getTemperature",
        .return_type = JS_TYPE_STRING
    },
    {
        .name = "getFloors",
        .return_type = JS_TYPE_STRING
    },
    {
        .name = "getBatteryData",
        .return_type = JS_TYPE_STRING
    },
    {
        .name = "getThemeColor",
        .return_type = JS_TYPE_INT
    },
    {
        .name = "getDialTypeData",
        .param_type = {JS_TYPE_INT},
        .return_type = JS_TYPE_STRING
    },
    {
        .name = "getDialTypeDataGoal",
        .param_type = {JS_TYPE_INT},
        .return_type = JS_TYPE_STRING
    },
    {
        .name = "getDialEditDataType",
        .param_type = {JS_TYPE_INT},
        .return_type = JS_TYPE_INT
    },
    {
        .name = "setEditDataPosition",
        .param_type = {JS_TYPE_INT, JS_TYPE_INT, JS_TYPE_INT, JS_TYPE_INT, JS_TYPE_INT},
        .return_type = JS_TYPE_NONE
    },
    {
        .name = "getChargeStatus",
        .return_type = JS_TYPE_INT
    },
    {
        .name = "getBootMsec",
        .return_type = JS_TYPE_INT
    },
    {
        .name = "getBleConnectStatus",
        .return_type = JS_TYPE_INT
    },
    {
        .name = "getWeatherType",
        .return_type = JS_TYPE_INT
    },
    {
        .name = "getIsWeatherVaild",
        .return_type = JS_TYPE_INT
    },
    {
        .name = "getLanguageType",
        .return_type = JS_TYPE_INT
    }

};

static JSValue js_data_servers_method_process(JSContext* ctx, JSValueConst this_val,
    int argc, JSValueConst* argv, int magic)
{
    return qjs_touchgfx_process(ctx, this_val, argc, argv, magic, &(touchgfx_data_servers_protos[magic - 1]), JS_OBJ_TYPE_DATASERVERS);
}

const JSCFunctionListEntry js_data_servers_methods[] = {
    JS_STDFUNC_MAGIC_DEF("getVersion", 0, js_data_servers_method_process, JST_DSERVERS_GET_VER),
    JS_STDFUNC_MAGIC_DEF("setVersion", 1, js_data_servers_method_process, JST_DSERVERS_SET_VER),
    JS_STDFUNC_MAGIC_DEF("getIntByString", 1, js_data_servers_method_process, JST_DSERVERS_STRING_TO_INT),
    JS_STDFUNC_MAGIC_DEF("getSysTime", 1, js_data_servers_method_process, JST_DSERVERS_GET_SYS_TIME),//获取系统时间年月日周时分秒
    JS_STDFUNC_MAGIC_DEF("getHeartBeat", 0, js_data_servers_method_process, JST_DSERVERS_GET_HEART_BEAT),//获取心率
    JS_STDFUNC_MAGIC_DEF("getSetpCount", 0, js_data_servers_method_process, JST_DSERVERS_GET_SETP_COUNT),//获取步数
    JS_STDFUNC_MAGIC_DEF("getCalorie", 0, js_data_servers_method_process, JST_DSERVERS_GET_CALORIE),//获取卡路里
    JS_STDFUNC_MAGIC_DEF("getTemperature", 0, js_data_servers_method_process, JST_DSERVERS_GET_TEMPERATURE),//获取温度
    JS_STDFUNC_MAGIC_DEF("getFloors", 0, js_data_servers_method_process, JST_DSERVERS_GET_FLOORS),
    JS_STDFUNC_MAGIC_DEF("getBatteryData", 0, js_data_servers_method_process, JST_DSERVERS_GET_BATTERYDATA),
    JS_STDFUNC_MAGIC_DEF("getThemeColor", 0, js_data_servers_method_process, JST_DSERVERS_GET_THEME_COLOR),
    JS_STDFUNC_MAGIC_DEF("getDialTypeData", 1, js_data_servers_method_process, JST_DSERVERS_GET_DIAL_TYPE_DATA),
    JS_STDFUNC_MAGIC_DEF("getDialTypeDataGoal", 1, js_data_servers_method_process, JST_DSERVERS_GET_DIAL_TYPE_DATA_GOAL),
    JS_STDFUNC_MAGIC_DEF("getDialEditDataType", 1, js_data_servers_method_process, JST_DSERVERS_GET_DIAL_DATA_TYPE),
    JS_STDFUNC_MAGIC_DEF("setEditDataPosition", 5, js_data_servers_method_process, JST_DSERVERS_SET_EDIT_DATA_POSITION),
    JS_STDFUNC_MAGIC_DEF("getChargeStatus", 0, js_data_servers_method_process, JST_DATASERS_GET_CHARGING_STATUS),
    JS_STDFUNC_MAGIC_DEF("getBootMsec", 0, js_data_servers_method_process, JST_DATASERS_GET_BOOTMSEC),
    JS_STDFUNC_MAGIC_DEF("getBleConnectStatus", 0, js_data_servers_method_process, JST_DATASERS_GET_BLE_CONNECT_STATUS),
    JS_STDFUNC_MAGIC_DEF("getWeatherType", 0, js_data_servers_method_process, JST_DSERVERS_GET_WEATHER_TYPE),
    JS_STDFUNC_MAGIC_DEF("getIsWeatherVaild", 0, js_data_servers_method_process, JST_DSERVERS_GET_IS_WEATHER_VAILD),
    JS_STDFUNC_MAGIC_DEF("getLanguageType", 0, js_data_servers_method_process, JST_DSERVERS_GET_LANGUAGE_TYPE),

};

int js_touchgfx_init_data_servers(JSContext* ctx, JSModuleDef* m)
{
    JS_SetModuleExportList(ctx, m, js_data_servers_methods, JST_countof(js_data_servers_methods));
    return 0;
}

void js_touchgfx_add_data_servers_module(JSContext* ctx)
{
    JSModuleDef* m;

    m = JS_NewCModule(ctx, "data_servers", js_touchgfx_init_data_servers);
    if (!m) {
        return;
    }
    JS_AddModuleExportList(ctx, m, js_data_servers_methods, JST_countof(js_data_servers_methods));
}

JSValue touchgfx_data_servers_call_method(JSContext* ctx, JSValueConst this_val, int magic, JSValueConst param[])
{
    JSValue  r = JS_UNDEFINED;
    switch (magic) {
    case JST_DSERVERS_GET_VER:
    {
        int r_in = (int)version;
        r = JS_NewInt32(ctx, r_in);
        break;
    }
    case JST_DSERVERS_SET_VER:
    {
        version = (int)param[0];
        break;
    }
    case JST_DSERVERS_STRING_TO_INT:
    {
        int r_in = atoi((char*)param[0]);
        r = JS_NewInt32(ctx, r_in);
        break;
    }
    case JST_DSERVERS_GET_SYS_TIME:
    {
        qw_tm_t tm_time = { 0 };
        char sys_time[50] = { 0 };

#ifndef SIMULATOR
        service_datetime_get(&tm_time);
        if(get_time_style() == TIMTSTYLE_TIME12 && tm_time.tm_hour > 12)
        {
            tm_time.tm_hour = tm_time.tm_hour-12;
        }
#else
        uint32_t gmt = 0;
        service_get_rtctime(&tm_time);
        gmt = service_rtctime_2_sec(&tm_time);
        gmt += 28800;
        service_sec_2_rtctime(gmt, &tm_time);
        if(get_time_style() == TIMTSTYLE_TIME12 && tm_time.tm_hour > 12)
        {
            tm_time.tm_hour = tm_time.tm_hour-12;
        }
#endif
        const int time_arr[] = { tm_time.tm_year, tm_time.tm_mon, tm_time.tm_mday, tm_time.tm_wday, tm_time.tm_hour, tm_time.tm_min, tm_time.tm_sec };
        // rt_kprintf("time_arr[0]:%d time_arr[1]:%d time_arr[2]:%d time_arr[3]:%d time_arr[4]:%d time_arr[5]:%d time_arr[6]:%d\n", time_arr[0], time_arr[1], time_arr[2], time_arr[3], time_arr[4], time_arr[5], time_arr[6]);
        sprintf_array(sys_time, "%02d", time_arr[(int)param[0]]);
        r = JS_NewString(ctx, sys_time);
        break;
    }
    case JST_DSERVERS_GET_HEART_BEAT:
    {
        char buf[12] = {0};
#ifndef SIMULATOR
       snprintf(buf,12,"%d",service_gui_get_heartrate());
#else
        snprintf(buf,12,"%d",66);
#endif
        r = JS_NewString(ctx, buf);
        break;
    }
    case JST_DSERVERS_GET_SETP_COUNT:
    {
        char buf[12] = {0};
    #ifndef SIMULATOR
        snprintf(buf,12,"%d",gui_get_today_steps());
    #else
        snprintf(buf,12,"%d", 120);
    #endif
        r = JS_NewString(ctx, buf);
        break;
    }
    case JST_DSERVERS_GET_CALORIE:
    {
        char buf[12] = {0};
    #ifndef SIMULATOR
        snprintf(buf,12,"%d",gui_get_today_calories());
    #else
        snprintf(buf,12,"%d", 120);
    #endif
        r = JS_NewString(ctx, buf);
        break;
    }
    case JST_DSERVERS_GET_BATTERYDATA:
    {
        char buf[12] = {0};
        snprintf(buf,12,"%d",battery_get_level());
        r = JS_NewString(ctx, buf);
        break;
    }
    case JST_DSERVERS_GET_THEME_COLOR:
    {
        // 通过索引获取主题色
        uint32_t index = get_dial_cfg_color_inuse_index();
        uint32_t color = get_dial_them_color(index);
        r = JS_NewInt32(ctx, color);
        break;
    }
    case JST_DSERVERS_GET_DIAL_TYPE_DATA:
    {
        int type = (int)param[0];
        char buf[64] = {0};
        jsapp_get_dial_type_data(type, buf);
        r = JS_NewString(ctx, buf);
        break;
    }
    case JST_DSERVERS_GET_DIAL_TYPE_DATA_GOAL:
    {
        int type = (int)param[0];
        char buf[64] = {0};
        jsapp_get_dial_type_data_goal(type, buf);
        r = JS_NewString(ctx, buf);
        break;
    }
    case JST_DSERVERS_GET_DIAL_DATA_TYPE:
    {
        int r_in = get_dial_data_type_by_index((int)param[0]);
        r = JS_NewInt32(ctx, r_in);
        break;
    }
    case JST_DSERVERS_SET_EDIT_DATA_POSITION:
    {
        // rt_kprintf("set_edit_data_positon index:%d x:%d y:%d w:%d h:%d\n", (int)param[0], (int)param[1], (int)param[2], (int)param[3], (int)param[4]);
        // 设置编辑数据位置
        set_edit_data_positon((int)param[0], (int)param[1], (int)param[2], (int)param[3], (int)param[4]);
        break;
    }
    case JST_DATASERS_GET_CHARGING_STATUS:
    {
        // 获取充电状态
        r = JS_NewInt32(ctx, battery_get_status());
        break;
    }
    case JST_DATASERS_GET_BOOTMSEC:
    {
        // 获取开机时间
        uint32_t boot_sec = (uint32_t)get_boot_msec();
        r = JS_NewInt32(ctx, boot_sec);
        break;
    }
    case JST_DATASERS_GET_BLE_CONNECT_STATUS:
    {
        // 获取连接状态
        uint8_t connect_status = 0;
    #ifndef SIMULATOR
        connect_status = g_device_get_ble_connect_status();
    #else
        connect_status = 1;
    #endif
        r = JS_NewInt32(ctx, connect_status);
        break;
    }
    case JST_DSERVERS_GET_WEATHER_TYPE:
    {
        // 获取连接状态
        uint8_t weather_type = 0;
    #ifndef SIMULATOR
        weather_type = get_weather_card_type();
    #else
        weather_type = 1;
    #endif
        r = JS_NewInt32(ctx, weather_type);
        break;
    }
    case JST_DSERVERS_GET_IS_WEATHER_VAILD:
    {
        bool is_vaild = get_weather_data_valid();
        r = JS_NewInt32(ctx, is_vaild);
        break;
    }
    case JST_DSERVERS_GET_LANGUAGE_TYPE:
    {
        bool isChinese = get_language_type() == LANGUAGE_CHI;
        r = JS_NewInt32(ctx, isChinese);
        break;
    }
    default:
    {
        rt_kprintf("Data base undefined method\n");
        break;
    }
    }
    return r;
}

static void jsapp_get_dial_type_data(int type, char* buf)
{
    switch(type){
       case DATA_TYPE_HR:
        {
            #ifndef SIMULATOR
            snprintf(buf,64,"%d",service_gui_get_heartrate());
            #else
            snprintf(buf,64,"%d",66);
            #endif
            break;
        }
        case DATA_TYPE_STRESS:
        {
            #ifndef SIMULATOR
            stress_t stress = {0};
            service_gui_get_last_stress(&stress);
            snprintf(buf,64,"%d",stress.stress);
            #else
            snprintf(buf,64,"%d",66);
            #endif
            break;
        }
        case DATA_TYPE_STEP:
        {
        #ifndef SIMULATOR
            snprintf(buf,12,"%d",gui_get_today_steps());
        #else
            snprintf(buf,12,"%d", 8218);
        #endif
            break;
        }
        case DATA_TYPE_CAL:
        {
    #ifndef SIMULATOR
        snprintf(buf,12,"%d",gui_get_today_calories());
    #else
        snprintf(buf,12,"%d", 588);
    #endif
            break;
        }
        case DATA_TYPE_INTENSE:
        {
    #ifndef SIMULATOR
        snprintf(buf,12,"%d",gui_get_today_intense_time());
    #else
        snprintf(buf,12,"%d", 20);
    #endif
            break;
        }
        case DATA_TYPE_RUN_VO2MAX:
        {
            uint16_t voxMax = 0;
        #ifndef SIMULATOR
            if (get_run_vo2max() == 0xffff) {
                voxMax = 0;
            }else{
                voxMax = get_run_vo2max();
            }
        #else
            voxMax = 0;
        #endif
            snprintf(buf,64,"%d",voxMax);
            break;
        }
        case DATA_TYPE_RIDE_VO2MAX:
        {
            uint16_t voxMax = 0;
        #ifndef SIMULATOR
            if (get_ride_vo2max() == 0xffff) {
                voxMax = 0;
            }else{
                voxMax = get_ride_vo2max();
            }
        #else
            voxMax = 10;
        #endif
            snprintf(buf,64,"%d",voxMax);
            break;
        }
        case DATA_TYPE_BATTERY:
        {
            uint8_t battery = 0;
        #ifndef SIMULATOR
            battery = battery_get_level();
        #else
            battery = 32;
        #endif
            snprintf(buf,64,"%d",battery);
            break;
        }
        case DATA_TYPE_ALTITUDE:
        {
            snprintf(buf,64,"%s","altitude");
            break;
        }
        case DATA_TYPE_BAROMETRIC:
        {
            snprintf(buf,64,"%s","barometric");
            break;
        }
        case DATA_TYPE_WEATHER:
        {
        #ifndef SIMULATOR
            cur_weather_st data = {0};
            get_weather_today_data(&data);
            snprintf(buf,64,"%d",data.cur_temperature);
        #else
            snprintf(buf,64,"%d",-20);
        #endif
            break;
        }
        case DATA_TYPE_ALIPAY:
        {
            snprintf(buf,64,"%s","alipay");
            break;
        }
        case DATA_TYPE_COMPASS:
        {
            snprintf(buf,64,"%s","compass");
            break;
        }
        case DATA_TYPE_ALARM:
        {
            snprintf(buf,64,"%s","alarm");
            break;
        }
        case DATA_TYPE_STOPWATCH:
        {
            snprintf(buf,64,"%s","stopwatch");
            break;
        }
        case DATA_TYPE_TIMER:
        {
            snprintf(buf,64,"%s","timer");
            break;
        }
        case DATA_TYPE_HR_PUSH:
        {
            snprintf(buf,64,"%s","hr_push");
            break;
        }
        case DATA_TYPE_SLEEP:
        {
        #ifndef SIMULATOR
            gui_sleep_period_day_t longSleep_info = {0};
            service_gui_get_sleep_period_data(&longSleep_info);
            snprintf(buf,64,"%d",longSleep_info.total_time);
        #else
            snprintf(buf,64,"%d", 120);
        #endif
            break;
        }
        case DATA_TYPE_RECOVERY_TIME:
        {
            break;
        }
        case DATA_TYPE_PHYSICAL_STRENGTH:
        {
            snprintf(buf,64,"%d", 89);
            break;
        }
        default:
            break;
    }
}

static void jsapp_get_dial_type_data_goal(int type, char* buf)
{
    switch(type){
        case DATA_TYPE_STEP:
        {
        #ifndef SIMULATOR
            snprintf(buf,12,"%d",gui_get_today_steps_goal());
        #else
            snprintf(buf,12,"%d", 5000);
        #endif
            break;
        }
        case DATA_TYPE_CAL:
        {
    #ifndef SIMULATOR
        snprintf(buf,12,"%d",gui_get_today_calories_goal());
    #else
        snprintf(buf,12,"%d", 300);
    #endif
            break;
        }
        case DATA_TYPE_INTENSE:
        {
    #ifndef SIMULATOR
        snprintf(buf,12,"%d",gui_get_today_intense_time_goal());
    #else
        snprintf(buf,12,"%d", 30);
    #endif
            break;
        }
        case DATA_TYPE_BATTERY:
        {
            snprintf(buf,64,"%d",100);
            break;
        }
        case DATA_TYPE_PHYSICAL_STRENGTH:
        {
    #ifndef SIMULATOR
        lib_gm_recovery_status_t rs;
        if (lib_gm_recovery_status_get(&rs) != -1) {
            snprintf(buf,64,"%d",rs.cur_stamina);
        }else{
            snprintf(buf,64,"%d", 0);
        }
    #else
            snprintf(buf,64,"%d",100);
    #endif
            break;
        }
        default:
            break;
    }
}
static edit_data_positon_t s_edit_data_positon[MAX_EDIT_DATA_NUM] = {0};
void init_edit_data_positon()
{
    memset(s_edit_data_positon,0, sizeof(s_edit_data_positon));
}
void get_edit_data_positon(uint32_t index, edit_data_positon_t* edit_data_positon)
{
    *edit_data_positon = s_edit_data_positon[index];

}
void set_edit_data_positon(uint32_t index, uint32_t x, uint32_t y, uint32_t width, uint32_t height)
{
    s_edit_data_positon[index].x = x;
    s_edit_data_positon[index].y = y;
    s_edit_data_positon[index].width = width;
    s_edit_data_positon[index].height = height;
}

/************************************************************************
 *@function: get_data_name
 *@brief: 根据数据类型索引返回对应的数据名称字符串，未匹配返回NULL
 *@param: uint32_t index - 数据类型索引
 *@return: const char* - 对应的数据名称字符串，未匹配返回NULL
*************************************************************************/
const char* get_edit_data_name(uint32_t index)
{
    switch(index)
    {
        case DATA_TYPE_ALIPAY:
            return "AlipayMenu";
        case DATA_TYPE_COMPASS:
            return "Compass";
        case DATA_TYPE_ALARM:
            return "AlarmMenu";
        case DATA_TYPE_STOPWATCH:
            return "StopWatchRecord";
        case DATA_TYPE_TIMER:
            return "TimerAppReady";
        case DATA_TYPE_HR_PUSH:
            return "HeartRatePush";
        default:
            return NULL;
    }
}
