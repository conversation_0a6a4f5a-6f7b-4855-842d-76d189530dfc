/**
 * @file algo_service_tl_trend.h
 * <AUTHOR>
 * @brief 负荷趋势算法接口
 * @version 0.1
 * @date 2025-8-14
 *
 * @copyright Copyright (c) 2024-2025, <PERSON>han <PERSON>wu Technology Co., Ltd
 *
 */

#ifndef __ALGO_SERVICE_TL_TREND__
#define __ALGO_SERVICE_TL_TREND__

#include "basictype.h"
#include "rtconfig.h"

#ifdef __cplusplus
extern "C"
{
#endif

/**
 * @brief 负荷趋势组件注册
 *
 * @return int32_t 结果
 */
int32_t register_tl_trend_algo(void);

#ifdef __cplusplus
}
#endif

#endif //__ALGO_SERVICE_TL_TREND__