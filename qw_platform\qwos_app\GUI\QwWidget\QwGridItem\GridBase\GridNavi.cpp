/****************************************Copyright (c)****************************************
* <PERSON><PERSON>wu Technology Co., Ltd
*
*---------------------------------------File Info--------------------------------------------
* File path :
* Created by : Lxin
* LastEditors: Lxin
* Descriptions :
*--------------------------------------------------------------------------------------------
* History :
* 2023-04-28 16:06:01: Lxin 原始版本
*
*********************************************************************************************/

#include "qw_os_gui.h"
#include "QwAppCtrlInc.h"
#include "../../qwos_app/sports_data/sports_data_show.h"
#include "../../qwos_app/GUI/Translate/QwDataKeyText.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QwGUITheme.h"
#include "Image/images.h"
#include "navi_port.h"
#include "map_input.h"
#include "track_ride_port.h"
#include "navi_util.h"
#include "utility_geometry.h"

// #define OUTER_LINE_NOT_PASSED_COLOR 0x038700 //未经过的线路（轨迹）外边色
#define INNER_LINE_NOT_PASSED_COLOR 0x09FF03 //未经过的线路（轨迹）内部色
// #define OUTER_LINE_NOT_PASSED_COLOR_NIGHT 0xE6FFE6 //未经过的线路（轨迹）外边色 夜晚
#define INNER_LINE_NOT_PASSED_COLOR_NIGHT 0x09FF03 //未经过的线路（轨迹）内部色 夜晚
#define CILMP_MAP_ROUTE_LINE_COLOR 0xF71BFF  //CILMP地图路线颜色

//导航线路已经过颜色
// #define OUTER_LINE_PASSED_COLOR 0x3A4042 //已经过的线路（轨迹）外边色
#define INNER_LINE_PASSED_COLOR 0x6B6962 //已经过的线路（轨迹）内部色
// #define OUTER_LINE_PASSED_COLOR_NIGHT 0xFFFFFF //已经过的线路（轨迹）外边色 夜晚
#define INNER_LINE_PASSED_COLOR_NIGHT 0xFFFFFF //已经过的线路（轨迹）内部色 夜晚
// #define OUTER_LINE_SIZE 8 //线路外边宽度
#define INNER_LINE_SIZE 6 //线路内部宽度

#define ROUTE_COURSE_ARROW_NUM      3

void GridNavi::setup()
{
    add(canvas_);
    canvas_.setPosition(0, 0, getWidth(), getHeight());
    canvas_.set_draw_func(draw_content_cb_);
    canvas_.setForceRefreshBG(true);
}

void GridNavi::on_notify()
{
    canvas_.invalidate();
}

void GridNavi::on_focus(bool focus)
{
	//The method is an intentionally-blank override.
}

void GridNavi::draw_content()
{
#if __NAVI
// #if __NAVI_ROUTE_SIM
//     static int s_inited = false;
//     map_input_gps_t *p_navi_gps = get_navi_gps();

//     if (false == s_inited)
//     {
//         s_inited = true;
// #ifdef SIMULATOR
//         route_use("E:/WR02_Backup/app/project/WR02_Win32/project/simulator/WR02_Disk/iGPSPORT/Courses/1627235.cnx", false);
// #else
//         route_use("0:/iGPSPORT/Courses/1627235.cnx", false);
// #endif
//         navi_route_lat_lng_init();
//     }
// #endif
    map_canvas_calc();
#endif //__NAVI
    setup_axis();
#if __NAVI
    setup_router();
#endif
    setup_items();
}

void GridNavi::setup_axis()
{
    // 坐标网格
    float axis_size = getWidth() / 8.0f;
    qw_gui_set_lv_color(lv_color_hex(0x666666));
    qw_gui_set_pen_size(1);
    for (float i = getWidth() - axis_size; i >= 2; i = i - axis_size)
    {
        for (float j = getHeight() - axis_size; j >= 2; j = j - axis_size)
        {
            int x = (int)(i + 0.5f);
            int y = (int)(j + 0.5f);
            qw_gui_draw_line(0, y, getWidth(), y);
            qw_gui_draw_line(x, 0, x, getHeight() - 1);
        }
    }
}

#if 1
static void show_no_gps_signal_way_line(const NaviWaypointDc *wpdc, uint32_t len, bool is_map)
{
	if (len < 2)
	{
		return;
	}
	struct minmea_float lat = { 0, MINMEA_SCALE }, lon = { 0, MINMEA_SCALE };
	point16_t point0, point1;
	int valid0, valid1;
	qw_gui_set_pen_size(INNER_LINE_SIZE);

	lon.value = (int_least32_t)(wpdc[0].lng * MINMEA_SCALE);
	lat.value = (int_least32_t)(wpdc[0].lat * MINMEA_SCALE);
	valid0 = position_to_pixel(&point0, &lat, &lon);

	if (is_map)
	{
		qw_gui_set_color(get_daylight() ? INNER_LINE_NOT_PASSED_COLOR : INNER_LINE_NOT_PASSED_COLOR_NIGHT);
	}
	else
	{
		qw_gui_set_color(CILMP_MAP_ROUTE_LINE_COLOR);
	}

	for (uint32_t j = 1; j < len; j++)
	{
		lon.value = (int_least32_t)(wpdc[j].lng * MINMEA_SCALE);
		lat.value = (int_least32_t)(wpdc[j].lat * MINMEA_SCALE);
		valid1 = position_to_pixel(&point1, &lat, &lon);

		if ((point0.x != point1.x || point0.y != point1.y) && (canvas_line_check(&point0, &point1) == true))
		{
			qw_gui_draw_line(point0.x, point0.y, point1.x, point1.y);
		}

		memcpy(&point0, &point1, sizeof(point16_t));
		valid0 = valid1;
	}
}

//绘制导航线路起终点
static void draw_start_finish(const NaviWaypointDc* start, const NaviWaypointDc* end)
{
	if (start == NULL || end == NULL)
	{
		return;
	}

	struct minmea_float lat = { 0, MINMEA_SCALE }, lon = { 0, MINMEA_SCALE };
	point16_t point0, point1;
	int valid0, valid1;
    const int16_t radius = 10;

	lon.value = (int_least32_t)(start->lng * MINMEA_SCALE);
	lat.value = (int_least32_t)(start->lat * MINMEA_SCALE);
	valid0 = position_to_pixel(&point0, &lat, &lon);

	lon.value = (int_least32_t)(end->lng * MINMEA_SCALE);
	lat.value = (int_least32_t)(end->lat * MINMEA_SCALE);
	valid1 = position_to_pixel(&point1, &lat, &lon);

	if (valid0)
	{
        qw_gui_set_color(GUI_GREEN);
        qw_gui_fill_circle(point0.x, point0.y, radius);
	}

	if (valid1)
	{
		qw_gui_set_color(GUI_RED);
        qw_gui_fill_circle(point1.x, point1.y, radius);
	}
}

static void show_track_line(Trackpoint* p_track, uint32_t len)
{
	if(NULL == p_track || len < 1)
	{
		return;
	}
	map_input_gps_t* p_cur_gps = get_navi_gps();

	struct minmea_float lat = { 0, MINMEA_SCALE }, lon = { 0, MINMEA_SCALE };
	point16_t point0, point1;
	int valid0, valid1;

	lon.value = (int_least32_t)(p_track[0].lng * MINMEA_SCALE);
	lat.value = (int_least32_t)(p_track[0].lat * MINMEA_SCALE);
	valid0 = position_to_pixel(&point0, &lat, &lon);

	for (int j = 1; j < len; j++)
	{
		lon.value = (int_least32_t)(p_track[j].lng * MINMEA_SCALE);
		lat.value = (int_least32_t)(p_track[j].lat * MINMEA_SCALE);
		valid1 = position_to_pixel(&point1, &lat, &lon);
		if ((point0.x != point1.x || point0.y != point1.y) && (canvas_line_check(&point0, &point1) == true))
		{
			qw_gui_draw_line(point0.x, point0.y, point1.x, point1.y);
		}

		memcpy(&point0, &point1, sizeof(point16_t));
		valid0 = valid1;
	}

	valid1 = position_to_pixel(&point1, &p_cur_gps->lat, &p_cur_gps->lon);
	if ((point0.x != point1.x || point0.y != point1.y) && (canvas_line_check(&point0, &point1) == true))
	{
		qw_gui_draw_line(point0.x, point0.y, point1.x, point1.y);
	}
}

//骑行轨迹绘制
static void show_track(void)
{
	const TrackSample* p_sample = track_sample_get();
	const TrackNearby* p_nearby = track_nearby_get();

	if (p_sample == NULL || p_nearby == NULL)
	{
		return;
	}

	if (p_sample->len < 1 || p_nearby->len < 1)
	{
		return;
	}
	//轨迹
	qw_gui_set_pen_shape(QW_GUI_PS_ROUND); //使用圆形画笔

	struct minmea_float lat = { 0, MINMEA_SCALE }, lon = { 0, MINMEA_SCALE };
	point16_t point0, point1;
	int start = 0, end = 0;

	for (int i = 0; i < p_sample->len; i++)
	{
		if (p_sample->buf[i].dist < p_nearby->buf[0].dist && p_sample->buf[i + 1].dist >= p_nearby->buf[0].dist)
		{
			start = i;
		}
	}

	qw_gui_set_pen_size(6);
	qw_gui_set_color(0x00FFFFF);

	if (0 == get_last_zoom())
	{
		show_track_line(p_sample->buf, p_sample->len);
	}
	else
	{
		if (start > 0)
		{
			lon.value = (int_least32_t)(p_sample->buf[0].lng * MINMEA_SCALE);
			lat.value = (int_least32_t)(p_sample->buf[0].lat * MINMEA_SCALE);
			position_to_pixel(&point0, &lat, &lon);
			for (int i = 1; i <= start; i++)
			{
				lon.value = (int_least32_t)(p_sample->buf[i].lng * MINMEA_SCALE);
				lat.value = (int_least32_t)(p_sample->buf[i].lat * MINMEA_SCALE);
				position_to_pixel(&point1, &lat, &lon);

				if ((point0.x != point1.x || point0.y != point1.y) && (canvas_line_check(&point0, &point1) == true))
				{
					qw_gui_draw_line(point0.x, point0.y, point1.x, point1.y);
				}
				memcpy(&point0, &point1, sizeof(point16_t));
			}
		}
		show_track_line(p_nearby->buf, p_nearby->len);
	}
}

//绘制导航线路实现
static void show_route_line(const NaviWaypointDc* wpdc, uint32_t len, float dist, bool is_map)
{
	if (len < 2)
	{
		return;
	}

	struct minmea_float lat = { 0, MINMEA_SCALE }, lon = { 0, MINMEA_SCALE };
	point16_t point0, point1, inner;
	int valid0, valid1;

	uint32_t idx = 0;

	//查找刚经过的路点
	for (uint32_t i = 0; i < len; i++)
	{
		if (dist >= wpdc[i].dist)
		{
			idx = i;
		}
		else
		{
			break;
		}
	}

	//尚未经过的那个路点
	const uint32_t idx_next = idx + 1;

	qw_gui_set_pen_size(INNER_LINE_SIZE);

	lon.value = (int_least32_t)(wpdc[0].lng * MINMEA_SCALE);
	lat.value = (int_least32_t)(wpdc[0].lat * MINMEA_SCALE);
	valid0 = position_to_pixel(&point0, &lat, &lon);

	// 绘制已经过的路点
	qw_gui_set_color(get_daylight() ? INNER_LINE_PASSED_COLOR : INNER_LINE_PASSED_COLOR_NIGHT);
	qw_gui_set_pen_shape(QW_GUI_PS_FLAT);
	qw_gui_set_area_opa(LV_OPA_30);

	for (uint32_t j = 1; j <= idx; j++)
	{
		lon.value = (int_least32_t)(wpdc[j].lng * MINMEA_SCALE);
		lat.value = (int_least32_t)(wpdc[j].lat * MINMEA_SCALE);
		valid1 = position_to_pixel(&point1, &lat, &lon);

		if ((point0.x != point1.x || point0.y != point1.y) && (canvas_line_check(&point0, &point1) == true))
		{
			qw_gui_draw_line(point0.x, point0.y, point1.x, point1.y);
		}

		memcpy(&point0, &point1, sizeof(point16_t));
		valid0 = valid1;
	}

	// 绘制正在经过的路点
	if (idx_next > len - 1)
	{
		return;
	}

	lon.value = (int_least32_t)(wpdc[idx_next].lng * MINMEA_SCALE);
	lat.value = (int_least32_t)(wpdc[idx_next].lat * MINMEA_SCALE);
	valid1 = position_to_pixel(&point1, &lat, &lon);

	float ratio = (dist - wpdc[idx].dist) / (wpdc[idx_next].dist - wpdc[idx].dist);
	if (ratio < 0.0f)
	{
		ratio = 0.0f;
	}
	if (ratio > 1.0f)
	{
		ratio = 1.0f;
	}

	const int16_t dx = point1.x - point0.x;
	const int16_t dy = point1.y - point0.y;

	inner.x = point0.x + (int16_t)((float)dx * ratio + 0.5f);
	inner.y = point0.y + (int16_t)((float)dy * ratio + 0.5f);

	if ((point0.x != inner.x || point0.y != inner.y) && (canvas_line_check(&point0, &point1) == true))
	{
		qw_gui_draw_line(point0.x, point0.y, inner.x, inner.y);
	}

	// 绘制未经过的路点
	if (is_map)
	{
		qw_gui_set_color(get_daylight() ? INNER_LINE_NOT_PASSED_COLOR : INNER_LINE_NOT_PASSED_COLOR_NIGHT);
	}
	else
	{
		qw_gui_set_color(CILMP_MAP_ROUTE_LINE_COLOR);
	}
	qw_gui_set_pen_shape(QW_GUI_PS_ROUND);
	qw_gui_set_area_opa(LV_OPA_100);

	if ((inner.x != point1.x || inner.y != point1.y) && (canvas_line_check(&point0, &point1) == true))
	{
		qw_gui_draw_line(inner.x, inner.y, point1.x, point1.y);
	}

	memcpy(&point0, &point1, sizeof(point16_t));
	valid0 = valid1;

	for (uint32_t j = idx_next + 1; j < len; j++)
	{
		lon.value = (int_least32_t)(wpdc[j].lng * MINMEA_SCALE);
		lat.value = (int_least32_t)(wpdc[j].lat * MINMEA_SCALE);
		valid1 = position_to_pixel(&point1, &lat, &lon);

		if ((point0.x != point1.x || point0.y != point1.y) && (canvas_line_check(&point0, &point1) == true))
		{
			qw_gui_draw_line(point0.x, point0.y, point1.x, point1.y);
		}

		memcpy(&point0, &point1, sizeof(point16_t));
		valid0 = valid1;
	}
}

static void calc_route_course_position(const NaviRouteWpNearby* wp_nearby, const NaviRouteWpSample* wp_sample, const navi_progress_t* progress, point16_t * course_position, int * course_angle)
{
	navi_data_t* p_navi_data = get_navi_data();

	if (wp_nearby == NULL || wp_sample == NULL || progress == NULL)
	{
		return;
	}

	if (wp_nearby->len < 2 || wp_sample->len < 2)
	{
		return;
	}

	const float dist = progress->dist;
	const float route_dist = wp_sample->wpdc_buf[wp_sample->len - 1].dist;

	//要绘制的指向所在路段的起点和终点
	NaviWaypointDc start = { 0 };
	NaviWaypointDc end = { 0 };

	//绘制距离为1.5倍比例尺长度，且不少于300m，共绘制6个指示符（箭头）
	//e.g. 比例尺长度为200m，则绘制距离为300m，则绘制间隔为300/6=50m
	const float interval = p_navi_data->distance_scale * 40 / 100;

	if (dist + interval >= route_dist)
	{
		//即将到达终点，不必绘制
		return;
	}

	//绘制结束距离
	float end_dist = dist + interval * ROUTE_COURSE_ARROW_NUM;

	if (end_dist > route_dist)
	{
		end_dist = route_dist;
	}

	//要绘制指向的距离范围在附近路点和全局路点中的区间，后续只需在区间中搜索，可以减少搜索时间
	Range nearby_range = { 0, wp_nearby->len };
	Range sample_range = { 0, wp_sample->len };

	//附近路点之后的第一个全局路点，即衔接点
	uint32_t join_idx = 0;

	//查找附近路点区间
	for (uint32_t i = 0; i < wp_nearby->len; i++)
	{
		if (wp_nearby->buf[i].dist <= dist)
		{
			nearby_range.start = i;
		}

		if (wp_nearby->buf[i].dist >= end_dist)
		{
			//range不包括右端点
			nearby_range.end = i + 1;
			break;
		}
	}

	//查找全局路点区间
	for (uint32_t i = 0; i < wp_sample->len; i++)
	{
		if (wp_sample->wpdc_buf[i].dist <= dist)
		{
			sample_range.start = i;
		}

		if (wp_sample->wpdc_buf[i].dist >= end_dist)
		{
			//range不包括右端点
			sample_range.end = i + 1;
			break;
		}
	}

	//查找衔接点
	for (uint32_t i = 0; i < wp_sample->len; i++)
	{
		if (wp_sample->wpdc_buf[i].dist > wp_nearby->buf[wp_nearby->len - 1].dist)
		{
			join_idx = i;
			break;
		}
	}

	//要绘制的指向的距离
	float course_dist = dist;

	//设置颜色
	//TODO 根据实际指定
	qw_gui_set_color(0xFF1326);

	//绘制各个指向
	for (uint32_t i = 0; i < ROUTE_COURSE_ARROW_NUM; i++)
	{
		course_dist += interval;

		//超出线路范围，停止绘制
		if (course_dist >= route_dist)
		{
			break;
		}

		//是否找到要绘制的指向所在的路段
		uint8_t is_find = false;

		//首先从附近路点中查找
		for (uint32_t j = nearby_range.start + 1; j < nearby_range.end; j++)
		{
			if (wp_nearby->buf[j].dist >= course_dist)
			{
				is_find = true;
				navi_waypoint_dc_copy(&start, &wp_nearby->buf[j - 1]);
				navi_waypoint_dc_copy(&end, &wp_nearby->buf[j]);
				break;
			}
		}

		//附近路点中没有找到，则需确认指向是否在附近路点和全局路点衔接段
		if (is_find == false)
		{
			if (course_dist > wp_nearby->buf[wp_nearby->len - 1].dist && course_dist <= wp_sample->wpdc_buf[join_idx].dist)
			{
				is_find = true;
				navi_waypoint_dc_copy(&start, &wp_nearby->buf[wp_nearby->len - 1]);
				navi_waypoint_dc_copy(&end, &wp_sample->wpdc_buf[join_idx]);
			}
		}

		//printf("angle = %d point0.x = %d point0.y = %d point1.x = %d point1.y = %d\n", angle, point0.x, point0.y, point1.x, point1.y);

		//也不在衔接段，则从全局路点中查找
		if (is_find == false)
		{
			for (uint32_t j = sample_range.start + 1; j < sample_range.end; j++)
			{
				if (wp_sample->wpdc_buf[j].dist >= course_dist)
				{
					is_find = true;
					navi_waypoint_dc_copy(&start, &wp_sample->wpdc_buf[j - 1]);
					navi_waypoint_dc_copy(&end, &wp_sample->wpdc_buf[j]);
					break;
				}
			}
		}

		if (is_find == true)
		{
			const float seg_dist = navi_util_seg_dist_calc(start.lng, start.lat, end.lng, end.lat);

			const float dd = course_dist - start.dist;
			float ratio = dd / seg_dist;

			if (ratio < 0.0f) { ratio = 0.0f; }
			if (ratio > 1.0f) { ratio = 1.0f; }

			//下面是两种绘制方法
#if 1
			//第一种是先将地理坐标转换为屏幕坐标，然后计算指向坐标
			//这种方法误差稍小（尤其在两点相距较远时），但需要两次转换，计算量可能较大

			struct minmea_float lat = { 0, MINMEA_SCALE }, lon = { 0, MINMEA_SCALE };
			point16_t point0, point1, inner;
			int valid0, valid1;

			float bearing = navi_util_course_calc(start.lng, start.lat, end.lng, end.lat);
			int32_t angle = 0;
			int32_t diff_angle = 0;
			if (p_navi_data->rotate)
			{
				map_input_gps_t *p_navi_gps = get_navi_gps();
				diff_angle = ((int32_t)bearing + 360) % 360 - p_navi_gps->course;
				if (diff_angle < 0)
				{
					diff_angle += 360;
				}

				angle = diff_angle;
			}
			else
			{
				angle = (int32_t)bearing;
			}

			lon.value = (int_least32_t)(start.lng * MINMEA_SCALE);
			lat.value = (int_least32_t)(start.lat * MINMEA_SCALE);
			valid0 = position_to_pixel(&point0, &lat, &lon);

			lon.value = (int_least32_t)(end.lng * MINMEA_SCALE);
			lat.value = (int_least32_t)(end.lat * MINMEA_SCALE);
			valid1 = position_to_pixel(&point1, &lat, &lon);

			const int16_t dx = point1.x - point0.x;
			const int16_t dy = point1.y - point0.y;

			inner.x = point0.x + (int16_t)((float)dx * ratio + 0.5f);
			inner.y = point0.y + (int16_t)((float)dy * ratio + 0.5f);

			if ((point0.x != point1.x || point0.y != point1.y) && (canvas_line_check(&point0, &point1) == true))
			{
				course_position[i].x = inner.x;
				course_position[i].y = inner.y;
				course_angle[i] = angle;
			}
#else
			//第二种是先计算指向的地理坐标，然后直接转换为屏幕坐标即可
			//由于计算使用线性插值，两点相距较远时会有误差，但只需一次转换即可

			const double d_lng = end.lng - start.lng;
			const double d_lat = end.lat - start.lat;

			const double inner_lng = start.lng + d_lng * (double)ratio;
			const double inner_lat = start.lat + d_lat * (double)ratio;

			struct minmea_float lat = { 0, MINMEA_SCALE }, lon = { 0, MINMEA_SCALE };
			point16_t inner = { 0 };

			lon.value = (int_least32_t)(inner_lng * MINMEA_SCALE);
			lat.value = (int_least32_t)(inner_lat * MINMEA_SCALE);

			float bearing = navi_util_course_calc(start.lng, start.lat, end.lng, end.lat);
			int32_t angle = 0;
			int32_t diff_angle = 0;
			if (p_navi_data->rotate)
			{
				map_input_gps_t *p_navi_gps = get_navi_gps();
				diff_angle = ((int32_t)bearing + 360) % 360 - p_navi_gps->course;
				if (diff_angle < 0)
				{
					diff_angle += 360;
				}

				angle = diff_angle;
			}
			else
			{
				angle = (int32_t)bearing;
			}

			if (position_to_pixel(&inner, &lat, &lon) == true)
			{
				course_position[i].x = inner.x;
				course_position[i].y = inner.y;
				course_angle[i] = angle;
			}
#endif
		}
	}
}

static void draw_arrow2(const point16_t *p_arrow_point, int16_t angle, uint32_t color, uint8_t pen_size)
{
    float bearing = 0.0f;
    point16_t point_wing[2][2];
    point16_t point_tip[2];
#if defined(IGS_DEV) || defined(SIMULATOR)
	uint8_t width = 18;
#else
    uint8_t width = OUTER_LINE_SIZE - 2;
#endif
    uint8_t height = 8;
    uint8_t inter = 10;

    // Input check.
    if (NULL == p_arrow_point)
    {
        return;
    }

    point_tip[0].x = p_arrow_point->x;
    point_tip[0].y = p_arrow_point->y - inter;
    point_tip[1].x = p_arrow_point->x;
    point_tip[1].y = p_arrow_point->y;

    // Point rotate.
    point_wing[0][0].x = point_tip[0].x - width / 2;
    point_wing[0][1].x = point_tip[0].x + width / 2;
    point_wing[0][0].y = point_tip[0].y + height;
    point_wing[0][1].y = point_wing[0][0].y;
    qw_position_rotate(&point_wing[0][0], &point_wing[0][0], p_arrow_point, -angle);
    qw_position_rotate(&point_wing[0][1], &point_wing[0][1], p_arrow_point, -angle);

    point_wing[1][0].x = point_tip[1].x - width / 2;
    point_wing[1][1].x = point_tip[1].x + width / 2;
    point_wing[1][0].y = point_tip[1].y + height;
    point_wing[1][1].y = point_wing[1][0].y;
    qw_position_rotate(&point_wing[1][0], &point_wing[1][0], p_arrow_point, -angle);
    qw_position_rotate(&point_wing[1][1], &point_wing[1][1], p_arrow_point, -angle);

    qw_position_rotate(&point_tip[0], &point_tip[0], p_arrow_point, -angle);
    qw_position_rotate(&point_tip[1], &point_tip[1], p_arrow_point, -angle);

    // Draw arrow.
     qw_gui_set_pen_shape(QW_GUI_PS_ROUND); //使用圆形画笔
    qw_gui_set_color(color);
    qw_gui_set_pen_size(pen_size);
    qw_gui_draw_line(point_tip[0].x, point_tip[0].y, point_wing[0][0].x, point_wing[0][0].y);
    qw_gui_draw_line(point_tip[0].x, point_tip[0].y, point_wing[0][1].x, point_wing[0][1].y);
    qw_gui_draw_line(point_tip[1].x, point_tip[1].y, point_wing[1][0].x, point_wing[1][0].y);
    qw_gui_draw_line(point_tip[1].x, point_tip[1].y, point_wing[1][1].x, point_wing[1][1].y);
    qw_gui_set_pen_shape(QW_GUI_PS_FLAT); //恢复平头画笔
}

static void show_route_course(point16_t * course_position, int32_t * course_angle, bool is_map)
{
	for (uint32_t i = 0; i < ROUTE_COURSE_ARROW_NUM; i++)
	{
		if (course_position[i].x != 0 && course_position[i].y != 0)
		{

				// qw_gui_draw_img(course_position[i].x - 12, course_position[i].y - 12, get_daylight() ? &im_navi_route_arrow : &im_navi_route_arrow_w,
				// 								course_angle[i] * 10, 256);
			    draw_arrow2(&course_position[i], (int16_t)course_angle[i], GUI_WHITE, 4);
		}
	}
}

static void show_route(void)
{
	// navi_data_t* p_navi_data = get_navi_data();
	// const NaviRouteWpNearby* wp_nearby = (p_navi_data->route_to_location ? router_navi_route_wp_nearby_get() : navi_route_wp_nearby_get());
	// const NaviRouteWpSample* wp_sample = (p_navi_data->route_to_location ? router_navi_route_wp_sample_get() : navi_route_wp_sample_get());
	// const navi_progress_t* progress = (p_navi_data->route_to_location ? router_navi_progress_get() : navi_progress_get());
	const NaviRouteWpNearby* wp_nearby = navi_route_wp_nearby_get();
	const NaviRouteWpSample* wp_sample = navi_route_wp_sample_get();
	const navi_progress_t* progress = navi_progress_get();
	map_input_data_t* p_map_input = get_map_input();
	map_input_gps_t *p_navi_gps = get_navi_gps();

	point16_t course_position[ROUTE_COURSE_ARROW_NUM] = { 0 };
	int course_angle[ROUTE_COURSE_ARROW_NUM] = { 0 };

	if (!p_navi_gps->status)
	{
		if (wp_sample != NULL)
		{
			if (wp_sample->len < 2)
			{
				return;
			}
			qw_gui_set_pen_shape(QW_GUI_PS_ROUND); //使用圆形画笔
			show_no_gps_signal_way_line(wp_sample->wpdc_buf, wp_sample->len, p_map_input->cur_page);
			qw_gui_set_pen_shape(QW_GUI_PS_FLAT); // 恢复平头画笔

			// Start & finish points.
			draw_start_finish(&wp_sample->wpdc_buf[0], &wp_sample->wpdc_buf[wp_sample->len - 1]);
			return;
		}
	}

	if (wp_nearby == NULL || wp_sample == NULL || progress == NULL)
	{
		return;
	}

	if (wp_nearby->len < 2 || wp_sample->len < 2)
	{
		return;
	}

	calc_route_course_position(wp_nearby, wp_sample, progress, course_position, course_angle);

	//用于衔接全局路点和附近路点
	NaviWaypointDc join[2] = { 0 };

	//线路
	qw_gui_set_pen_shape(QW_GUI_PS_ROUND); //使用圆形画笔

	// 画线路
	if (0 == get_last_zoom())
	{
		show_route_line(wp_sample->wpdc_buf, wp_sample->len, progress->dist, p_map_input->cur_page);
	}
	else
	{
		uint32_t cnt = 0;

		// 寻找附近路点之前的全局路点
		for (uint32_t i = 0; i < wp_sample->len; i++)
		{
			if (wp_sample->wpdc_buf[i].dist < wp_nearby->buf[0].dist)
			{
				cnt += 1;
			}
			else
			{
				break;
			}
		}

		// 绘制附近路点之前的全局路点
		if (cnt > 1)
		{
			show_route_line(wp_sample->wpdc_buf, cnt, progress->dist, p_map_input->cur_page);
		}

		// 绘制全局路点到附近路点之前衔接的路段
		if (cnt > 0)
		{
			navi_waypoint_dc_copy(&join[0], &wp_sample->wpdc_buf[cnt - 1]);
			navi_waypoint_dc_copy(&join[1], &wp_nearby->buf[0]);

			show_route_line(join, 2, progress->dist, p_map_input->cur_page);
		}

		// 绘制附近路点
		show_route_line(wp_nearby->buf, wp_nearby->len, progress->dist, p_map_input->cur_page);

		cnt = 0;

		// 寻找附近路点之后的全局路点
		for (uint32_t i = wp_sample->len - 1; i > 0; i--)
		{
			if (wp_sample->wpdc_buf[i].dist > wp_nearby->buf[wp_nearby->len - 1].dist)
			{
				cnt += 1;
			}
			else
			{
				break;
			}
		}

		// 绘制附近路点到其后的全局路点的衔接路段
		if (cnt > 0)
		{
			navi_waypoint_dc_copy(&join[0], &wp_nearby->buf[wp_nearby->len - 1]);
			navi_waypoint_dc_copy(&join[1], &wp_sample->wpdc_buf[wp_sample->len - cnt]);

			show_route_line(join, 2, progress->dist, p_map_input->cur_page);
		}

		// 绘制附近路点之后的全局路点
		if (cnt > 1)
		{
			show_route_line(wp_sample->wpdc_buf + wp_sample->len - cnt, cnt, progress->dist, p_map_input->cur_page);
		}
	}
	show_route_course(course_position, course_angle, p_map_input->cur_page);  //显示线路航向
	qw_gui_set_pen_shape(QW_GUI_PS_FLAT); // 恢复平头画笔

	// Start & finish points.
	draw_start_finish(&wp_sample->wpdc_buf[0], &wp_sample->wpdc_buf[wp_sample->len - 1]);
}
#endif

void GridNavi::setup_router()
{
    //画轨迹
	show_track();

	//画路线
	show_route();
}

static void show_scale(int metric)
{
    map_input_data_t* p_map_input = get_map_input();
    navi_data_t* p_navi_data = get_navi_data();
    char text_temp[30] = { 0 };
    sports_data_show_t ride_temp = { 0 };
    sports_data_t temp_sport_data = { 0 };

    int16_t length = p_navi_data->scale_len;
    int16_t x0 = p_map_input->map_origin.x + p_map_input->map_rang_x - 58 - length; // p_map_input->map_origin.x + 5;
    int16_t y0 = p_map_input->map_origin.y + p_map_input->map_rang_y - 6;
    uint8_t pen = 2;

    qw_gui_set_color(GUI_WHITE);

    qw_gui_set_pen_size(pen); //设置画笔

    memset(&ride_temp, 0, sizeof(sports_data_show_t));
    invaild_sports_data(&temp_sport_data);
    temp_sport_data.value = get_last_zoom_distance() / 100;
    temp_sport_data.decimal = get_last_zoom_distance() % 100;
    get_dist_string(&ride_temp, &temp_sport_data, metric); // 序列化比例尺

    if (0 == get_last_zoom())
    {
        sprintf(text_temp, "%s%s", ride_temp.data_full, ride_temp.unit);
    }
    else
    {
        // if (atoi(ride_temp.data_front) == 0)
        {
            sprintf(text_temp, "%s%s", ride_temp.data_short, ride_temp.unit);
        }
        // else
        // {
        //     sprintf(text_temp, "%s%s", ride_temp.data_front, ride_temp.unit);
        // }
    }

#if defined(IGS_DEV) || defined(SIMULATOR)
    qw_gui_set_text_font(&PUBLIC_NO_20_R_FONT); //设置字体
#endif
    qw_gui_set_text_range(length, 0);                            //宽度
    qw_gui_set_text_align(QW_GUI_TA_CENTER, QW_GUI_TA_UP, true); //对齐方式
    qw_gui_draw_text(text_temp, x0, y0 - 28);

    //横线
    qw_gui_draw_line(x0, y0, x0 + length, y0);
    //竖线
    qw_gui_draw_line(x0, y0 + pen - 1, x0, y0 - 4);
    qw_gui_draw_line(x0 + length, y0 + pen - 1, x0 + length, y0 - 4);
}

static void show_zoom_in(int16_t x, int16_t y) // ‘+’
{
    const static int16_t radius = 20;

    qw_gui_set_color(GUI_WHITE);
    qw_gui_fill_circle(x, y, radius);
    qw_gui_set_color(GUI_BLACK);
    qw_gui_set_pen_size(4);
    qw_gui_draw_line(x - radius / 2, y, x + radius / 2, y);
    qw_gui_draw_line(x, y - radius / 2, x, y + radius / 2);
}

static void show_zoom_out(int16_t x, int16_t y) // ‘-’
{
    const static int16_t radius = 20;

    qw_gui_set_color(GUI_WHITE);
    qw_gui_fill_circle(x, y, radius);
    qw_gui_set_color(GUI_BLACK);
    qw_gui_set_pen_size(4);
    qw_gui_draw_line(x - radius / 2, y, x + radius / 2, y);
}

static void show_course_location(void)
{
    map_input_data_t* p_map_input = get_map_input();
    navi_data_t *p_navi_data = get_navi_data();
    const navi_progress_t* progress = navi_progress_get();
    point16_t point = {0, 0};
    point16_t point_o = {0, 0};
    point16_t p_point_course = {0, 0};
    struct minmea_float lat = { 0, MINMEA_SCALE }, lon = { 0, MINMEA_SCALE };
    float bearing = 0.0f;
    int16_t angle = 0;
    const int16_t img_w = 56;

    if(progress == NULL)
    {
        return;
    }

    if(progress->status != enumNAVI_STATUS_OFF_COURSE)
    {
       return;
    }
    lon.value = (int_least32_t)(progress->match_point.lng * MINMEA_SCALE);
    lat.value = (int_least32_t)(progress->match_point.lat * MINMEA_SCALE);
    position_to_pixel(&p_point_course, &lat, &lon);

    // 不在画面区域内才显示指引
    // if (false == canvas_check_point(&p_point_course))
    {
        if (false != p_navi_data->rotate) // 旋转开
        {
            point_o.x = p_map_input->map_origin.x + p_map_input->map_rang_x / 2;
            point_o.y = p_map_input->map_origin.y + p_navi_data->map_real_y * 2 / 3; // 当前GPS定位点屏幕坐标(旋转开启时固定)
        }
        else // 旋转关
        {
            point_o.x = p_map_input->map_origin.x + p_map_input->map_rang_x / 2;
            point_o.y = p_map_input->map_origin.y + p_navi_data->map_real_y / 2; // 当前GPS定位点屏幕坐标(屏幕中央)
        }

        qw_position_bearing_get(&bearing, &p_point_course, &point_o);
        angle = (int16_t)round(bearing);
        point.x = point_o.x;
        point.y = point_o.y - img_w * 2 / 3;
        qw_position_rotate(&point, &point, &point_o, angle);
        draw_arrow2(&point, -angle, GUI_RED, 4);
    }
}

void GridNavi::setup_items()
{
    map_input_data_t *p_map_input = get_map_input();
    navi_data_t* p_navi_data = get_navi_data();
    map_input_gps_t* p_navi_gps = get_navi_gps();
    point16_t point0 = { 0, 0 };
    int32_t course = p_navi_gps->course;
    const int16_t img_w = 56;

    //指北针和航向指针
    if (p_navi_data->rotate) //地图旋转
    {
        qw_gui_draw_img(24, 213, grid_map_dir_img_dir, (360 - course) * 10, 256);
        qw_gui_draw_img((getWidth() - img_w) / 2, 205, grid_map_user_img_dir, 0, 256);
    }
    else //北向朝上
    {
        qw_gui_draw_img(24, 213, grid_map_dir_guide_img, 0, 256);

        if (0 == get_last_zoom())
        {
            int valid = position_to_pixel(&point0, &p_navi_gps->lat, &p_navi_gps->lon);
            if (valid)
            {
                qw_gui_draw_img(point0.x - img_w / 2,
                        point0.y - img_w / 2,
                        grid_map_user_img_dir, course * 10, 256);
            }
        }
        else
        {
            qw_gui_draw_img(p_map_input->map_origin.x + (p_map_input->map_rang_x - img_w) / 2,
                    p_map_input->map_origin.y + (p_map_input->map_rang_y - img_w) / 2,
                    grid_map_user_img_dir, course * 10, 256);
        }
    }

    //偏航时显示线路方位
    show_course_location();

    //比例尺
    // show_scale(METRIC_CHECK(metric_, GRID_METRIC_DISTANCE));
    show_scale(true); //TODO: 公英制在运行中会被设置为0

    if (false != navi_ctrl_mode_get())
    {
        show_zoom_in(getWidth() - 50, 233 - 40);
        show_zoom_out(getWidth() - 50, 233 + 40);
    }
}
