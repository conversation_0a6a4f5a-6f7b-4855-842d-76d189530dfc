/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
*@File    :   algo_hai.h
*@Time    :   2025/08/25 14:20:30
*
**************************************************************************/

#ifndef __ALGO_HAI_H__
#define __ALGO_HAI_H__

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/****************************************************************************
*Public Types
****************************************************************************/
typedef enum {

    QW_INTENSITY_OBJECT_STATIC = 0, //物体静止
    QW_INTENSITY_SUSPECTED_STATIC,  //疑似静止状态
    QW_INTENSITY_SLEEPING,          //睡眠
    QW_INTENSITY_HUMAN_STATIC,      //人体静止
    QW_INTENSITY_OFFICE,            //办公
    QW_INTENSITY_LOW,               //散步
    QW_INTENSITY_MODERATE,          //快走
    QW_INTENSITY_HIGH,              //跑
    QW_INTENSITY_OTHER              // Just for initialization
} hai_status_t;

//user_data可以传入静息心率、年龄等参数
typedef void (*qw_hai_changed_cb)(hai_status_t intensity, void *user_data);

/************************************************************************
 *@function: void *qw_activity_intensity_init(qw_hai_changed_cb hai_cb, void *user_data)
 *@brief: 初始化人体活动强度识别算法
 *@param: hai_cb: 状态变化回调函数, user_data: 用户数据指针
 *@return: 算法句柄指针，失败返回NULL
*************************************************************************/
void *qw_activity_intensity_init(qw_hai_changed_cb hai_cb, void *user_data);

/************************************************************************
 *@function: int qw_activity_intensity_process(uint64_t ts, float acc_x, float acc_y, float acc_z)
 *@brief: 输入加速度数据进行处理
 *@param: ts: 时间戳(ms), acc_x/y/z: 三轴加速度值(m/s²)
 *@return: 0:成功, -1:失败
*************************************************************************/
int qw_activity_intensity_process(uint64_t ts, float acc_x, float acc_y, float acc_z);

/************************************************************************
 *@function: const char* qw_hai_get_state_string(hai_status_t state)
 *@brief: 获取活动状态对应的字符串描述
 *@param: state: 活动状态
 *@return: 状态字符串
*************************************************************************/
const char* qw_hai_get_state_string(hai_status_t state);

#ifdef __cplusplus
}
#endif

#endif 

