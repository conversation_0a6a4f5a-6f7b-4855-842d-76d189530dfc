/****************************************Copyright (c)****************************************
* <PERSON><PERSON>wu Technology Co., Ltd
*
*---------------------------------------File Info--------------------------------------------
* File path : \radio\sensor\sensor_ant\lev\ant_lev_rx.c
* Created by : Lxin
* LastEditors: Lxin
* Descriptions :
*--------------------------------------------------------------------------------------------
* History :
* 2022-05-30 13:48:15: L<PERSON> 原始版本
*
*********************************************************************************************/
#if ANT_SENSOR_LEV_ENABLED
#include "nrf_log.h"
#include "nrf_sdh.h"
#include "nrf_sdh_ant.h"
#include "ant_lev.h"
#include "app_error.h"
#include "ant_parameters.h"
#include "ant_interface.h"
#include "basictype.h"
#include "sensor_ant_common.h"
#include "ant_lev_rx.h"
#include "rttlog.h"
#include "qw_sensor_data.h"
#include "cfg_header_def.h"

static void ant_lev_rx_evt_handler(ant_lev_profile_t * p_profile, ant_lev_evt_t event);
//--------------------------------------变量定义-------------------------------------------//
static ant_lev_profile_t m_ant_lev;
static void lev_ant_evt(ant_evt_t *p_ant_evt, void * p_context);

//--------------------------------------函数定义-------------------------------------------//

/**
 * @*********************************************************************************************
 * @description: 加载ANT ebike接收通道默认配置
 * @param {ant_channel_config_t } *p_channel_config
 * @return {*}
 * @*********************************************************************************************
 */
static void LoadChnConf_lev_rx(ant_channel_config_t  *p_channel_config)
{
    p_channel_config->channel_number    = sensor_ant_channel_num_get(SENSOR_TYPE_LEV);
    p_channel_config->channel_type      = LEV_DISP_CHANNEL_TYPE;
    p_channel_config->ext_assign        = LEV_EXT_ASSIGN;
    p_channel_config->rf_freq           = LEV_ANTPLUS_RF_FREQ;
    p_channel_config->transmission_type = CHAN_ID_TRANS_TYPE;
    p_channel_config->device_type       = LEV_DEVICE_TYPE;
    p_channel_config->channel_period    = LEV_MSG_PERIOD;
    p_channel_config->network_number    = ANTPLUS_NETWORK_NUM;
}

/**
 * @*********************************************************************************************
 * @description: ebike事件处理函数
 * @param {ant_lev_profile_t *} p_profile
 * @param {ant_lev_evt_t} event
 * @return {*}
 * @*********************************************************************************************
 */
static void ant_lev_rx_evt_handler(ant_lev_profile_t * p_profile, ant_lev_evt_t event)
{
    sensor_search_infor_t       sensor_search_infor;
    sensor_connect_infor_t      sensor_connect;
	sensor_connect_infor_get(SENSOR_TYPE_LEV, &sensor_connect);
    sensor_module_evt_handler   evt_handler             = sensor_module_evt_handler_get();
    static sensor_saved_work_t  *p_sensor_saved         = NULL;
    static sensor_work_state_t  sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    sensor_module_param_input_t *p_param                = sensor_module_param_input_get();
    sensor_original_data_t      *p_sensor_original_data = sensor_original_data_get();
    static int8_t index                   = -1;
    static uint8_t low_power_indicate_flag = FALSE;

    NRF_LOG_INFO("File: %s, Function: %s",  (uint32_t)__FILE__, (uint32_t)__func__);

	sensor_ant_leave_rx_search(SENSOR_TYPE_LEV);

    memset ((uint8_t *)&sensor_search_infor, 0x00, sizeof(sensor_search_infor_t));
    switch (event)
    {
        case ANT_LEV_PAGE_1_UPDATED:
        	p_sensor_original_data->levData.system_information.battery_temperature = p_profile->page_1.battery_temperature;
        	p_sensor_original_data->levData.system_information.motor_temperature = p_profile->page_1.motor_temperature;
        	p_sensor_original_data->levData.system_information.assist_mode = p_profile->page_1.assist_mode;
        	p_sensor_original_data->levData.system_information.regen_mode = p_profile->page_1.regen_mode;
        	p_sensor_original_data->levData.system_information.signal_right = p_profile->page_1.signal_right;
        	p_sensor_original_data->levData.system_information.signal_left = p_profile->page_1.signal_left;
        	p_sensor_original_data->levData.system_information.light_beam = p_profile->page_1.light_beam;
        	p_sensor_original_data->levData.system_information.light = p_profile->page_1.light;
        	p_sensor_original_data->levData.system_information.manual_throttle = p_profile->page_1.manual_throttle;
        	p_sensor_original_data->levData.system_information.gear_exist = p_profile->page_1.gear_exist;
        	if (p_profile->page_1.gear_exist)
        	{
            	p_sensor_original_data->levData.system_information.manual_gear = p_profile->page_1.manual_gear;
            	p_sensor_original_data->levData.system_information.front_gear = p_profile->page_1.front_gear;
            	p_sensor_original_data->levData.system_information.rear_gear = p_profile->page_1.rear_gear;
        	}
        	else
        	{
            	p_sensor_original_data->levData.system_information.manual_gear = 0;
            	p_sensor_original_data->levData.system_information.front_gear = 0;
            	p_sensor_original_data->levData.system_information.rear_gear = 0;
        	}
        	p_sensor_original_data->levData.error_message = p_profile->page_1.error_message;
        	p_sensor_original_data->levData.lev_speed = p_profile->page_1.lev_speed;

        	//连接事件
        	memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
			memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
			sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
			sensor_search_infor.sensor_type = SENSOR_TYPE_LEV;

			sensor_work_state = SENSOR_WORK_STATE_IDLE;
			p_sensor_saved    = NULL;
			index             = -1;
			// sensor_saved_work_infor_get(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state);

			if (SENSOR_CONNECT_STATE_CONNECTING == sensor_connect.state)
			{
				sensor_connect.state = SENSOR_CONNECT_STATE_CONNECTED;
                sensor_connect_infor_set(SENSOR_TYPE_LEV, &sensor_connect);

                if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
				{
                    if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
                    {
                        p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                        p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
                        cfg_mark_update(enum_cfg_ant_ble_dev);
                    }
                    else if (SENSOR_WORK_STATE_FORBIDDEN == sensor_work_state)
                    {
                        sensor_infor_t sensor_infor = {0};
                        sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                        sensor_disconnect(&sensor_infor);
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }
				else
				{
                    if(sensor_disconnect_item_check(&sensor_search_infor))
					{
						sensor_disconnect_info_remove(&sensor_search_infor);
						sensor_infor_t sensor_infor = {0};
						sensor_infor.sensor_type = sensor_search_infor.sensor_type;
						sensor_disconnect(&sensor_infor);
						return;
					}
					sensor_saved_work_infor_add(&sensor_search_infor);
					sensor_search_infor_del(&sensor_search_infor);
				}

				if (NULL != evt_handler)
				{
            		evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, TRUE);
					evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, 0);
					evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, SENSOR_TYPE_LEV, SENSOR_RADIO_TYPE_ANT, TRUE);
				}

				low_power_indicate_flag = TRUE;
			}
            else if (SENSOR_CONNECT_STATE_CONNECTED == sensor_connect.state)
            {
                if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
                {
                    if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
                    {
                        p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                        p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }
            }
    		break;
        case ANT_LEV_PAGE_2_UPDATED:
        	p_sensor_original_data->levData.odometer = p_profile->page_2.odometer;
        	p_sensor_original_data->levData.remaining_range = p_profile->page_2.remaining_range;
        	p_sensor_original_data->levData.lev_speed = p_profile->page_2.lev_speed;
    		break;
        case ANT_LEV_PAGE_3_UPDATED:
        	p_sensor_original_data->levData.system_information.battery_capacity = p_profile->page_3.battery_capacity;
        	p_sensor_original_data->levData.system_information.regen_mode = p_profile->page_3.regenerative;
        	p_sensor_original_data->levData.system_information.assist_mode = p_profile->page_3.assist;
        	p_sensor_original_data->levData.system_information.signal_right = p_profile->page_3.signal_right;
        	p_sensor_original_data->levData.system_information.signal_left = p_profile->page_3.signal_left;
        	p_sensor_original_data->levData.system_information.light_beam = p_profile->page_3.light_beam;
        	p_sensor_original_data->levData.system_information.light = p_profile->page_3.light;
        	p_sensor_original_data->levData.system_information.manual_throttle = p_profile->page_3.manual_throttle;
        	p_sensor_original_data->levData.system_information.gear_exist = p_profile->page_3.gear_exist;
        	if (p_profile->page_3.gear_exist)
        	{
            	p_sensor_original_data->levData.system_information.manual_gear = p_profile->page_3.manual_gear;
            	p_sensor_original_data->levData.system_information.front_gear = p_profile->page_3.front_gear;
            	p_sensor_original_data->levData.system_information.rear_gear = p_profile->page_3.rear_gear;
        	}
        	else
        	{
            	p_sensor_original_data->levData.system_information.manual_gear = 0;
            	p_sensor_original_data->levData.system_information.front_gear = 0;
            	p_sensor_original_data->levData.system_information.rear_gear = 0;
        	}
        	p_sensor_original_data->levData.assist_pct = p_profile->page_3.assist_pct;
        	p_sensor_original_data->levData.lev_speed = p_profile->page_3.lev_speed;

			if (p_profile->page_3.battery_capacity >= 1 && p_profile->page_3.battery_capacity <= 100)
            {
                p_sensor_original_data->battery_list.lev = p_profile->page_3.battery_capacity;
            }

        	if (low_power_indicate_flag && p_profile->page_3.battery_alert)
        	{
        		low_power_indicate_flag = FALSE;
				if (NULL != evt_handler)
				{
					evt_handler(EVENT_SENSOR_LOW_POWER, &sensor_search_infor.sensor_id, SENSOR_TYPE_LEV, SENSOR_RADIO_TYPE_ANT, 0);
				}
        	}
    		break;
        case ANT_LEV_PAGE_4_UPDATED:
        	p_sensor_original_data->levData.battery_information.charging_cycle_count = p_profile->page_4.charging_cycle_count;
        	p_sensor_original_data->levData.battery_information.fuel_consumption = p_profile->page_4.fuel_consumption;
        	p_sensor_original_data->levData.battery_information.battery_voltage = p_profile->page_4.battery_voltage;
        	p_sensor_original_data->levData.battery_information.distance_on_current_charge = p_profile->page_4.distance_on_current_charge;
    		break;
        case ANT_LEV_PAGE_5_UPDATED:
        	p_sensor_original_data->levData.capability.travel_modes_supported = p_profile->page_5.travel_modes_supported;
        	p_sensor_original_data->levData.capability.wheel_circumference = p_profile->page_5.wheel_circumference;

        	if (0 < p_profile->page_5.wheel_circumference && 4095 >= p_profile->page_5.wheel_circumference)
        	{
        		memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
				memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
				sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
				sensor_search_infor.sensor_type = SENSOR_TYPE_LEV;

				sensor_work_state = SENSOR_WORK_STATE_IDLE;
				p_sensor_saved    = NULL;
				index             = -1;
				// sensor_saved_work_infor_get(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state);

				if (SENSOR_CONNECT_STATE_CONNECTED == sensor_connect.state)
				{
                    if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
					{
					    p_sensor_saved->sensor_saved_infor->sensor_infor[index].cfg_param.wheel_size.wheel_size = p_profile->page_5.wheel_circumference;
                        sensor_saved_work_infor_release_write_lock(index);
                        cfg_mark_update(enum_cfg_ant_ble_dev);
                    }
                }
        	}
    		break;
        case ANT_LEV_PAGE_34_UPDATED:
        	p_sensor_original_data->levData.odometer = p_profile->page_34.odometer;
        	p_sensor_original_data->levData.battery_information.fuel_consumption = p_profile->page_34.fuel_consumption;
        	p_sensor_original_data->levData.lev_speed = p_profile->page_34.lev_speed;
    		break;
        case ANT_LEV_PAGE_80_UPDATED:
#if SENSOR_DEVICE_INFO_ENABLED
            memset((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
			memcpy((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
			sensor_search_infor.radio_type = SENSOR_RADIO_TYPE_ANT;
			sensor_search_infor.sensor_type = SENSOR_TYPE_LEV;

			sensor_work_state = SENSOR_WORK_STATE_IDLE;
			p_sensor_saved = NULL;
			index = -1;

            if (SENSOR_CONNECT_STATE_CONNECTED == sensor_connect.state)
			{
				if (sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
				{
                    if (p_sensor_saved->sensor_manufacturer[index].manufacturer_ant != p_profile->page_80.manufacturer_id ||
                            p_sensor_saved->sensor_hw_version[index].version_ant != p_profile->page_80.hw_revision ||
                            p_sensor_saved->sensor_model[index].model_ant != p_profile->page_80.model_number)
                    {
                        p_sensor_saved->sensor_manufacturer[index].manufacturer_ant = p_profile->page_80.manufacturer_id;
                        p_sensor_saved->sensor_hw_version[index].version_ant      = p_profile->page_80.hw_revision;
                        p_sensor_saved->sensor_model[index].model_ant        = p_profile->page_80.model_number;
                        evt_handler(EVENT_SENSOR_MANUFACTURER_RECEIVED, NULL, sensor_search_infor.sensor_type, 0, index);
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }
            }
#endif
        	p_sensor_original_data->levData.hw_revision = p_profile->page_80.hw_revision;
        	p_sensor_original_data->levData.manufacturer_id = p_profile->page_80.manufacturer_id;
        	p_sensor_original_data->levData.model_number = p_profile->page_80.model_number;
    		break;
        case ANT_LEV_PAGE_81_UPDATED:
#if SENSOR_DEVICE_INFO_ENABLED
            memset((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
			memcpy((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
			sensor_search_infor.radio_type = SENSOR_RADIO_TYPE_ANT;
			sensor_search_infor.sensor_type = SENSOR_TYPE_LEV;

			sensor_work_state = SENSOR_WORK_STATE_IDLE;
			p_sensor_saved = NULL;
			index = -1;

            if (SENSOR_CONNECT_STATE_CONNECTED == sensor_connect.state)
			{
				if (sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
				{
                    if (p_sensor_saved->sensor_serial[index].serial_ant != p_profile->page_81.serial_number ||
                            p_sensor_saved->sensor_sw_version[index].version_ant != ((((uint16_t)p_profile->page_81.sw_revision_major) << 8) | p_profile->page_81.sw_revision_minor))
                    {
                        p_sensor_saved->sensor_serial[index].serial_ant = p_profile->page_81.serial_number;
                        p_sensor_saved->sensor_sw_version[index].version_ant = ((((uint16_t)p_profile->page_81.sw_revision_major) << 8) | p_profile->page_81.sw_revision_minor);
                        evt_handler(EVENT_SENSOR_MANUFACTURER_RECEIVED, NULL, sensor_search_infor.sensor_type, 0, index);
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }
            }
#endif
        	p_sensor_original_data->levData.sw_revision_minor = p_profile->page_81.sw_revision_minor;
        	p_sensor_original_data->levData.sw_revision_major = p_profile->page_81.sw_revision_major;
        	p_sensor_original_data->levData.serial_number = p_profile->page_81.serial_number;
    		break;
        case ANT_LEV_PAGE_REQUEST_SUCCESS:
        	break;
        case ANT_LEV_PAGE_REQUEST_FAILED:
        	break;
        default:
            break;
    }
}

/**
 * @*********************************************************************************************
 * @description: ebike ANT事件处理函数
 * @param {ant_evt_t} *p_ant_evt
 * @param {void *} p_context
 * @return {*}
 * @*********************************************************************************************
 */
static void lev_ant_evt(ant_evt_t *p_ant_evt, void * p_context)
{
    sensor_search_infor_t     sensor_search_infor;
    // sensor_connect_infor_t    *p_sensor_connect       = sensor_connect_infor_get(SENSOR_TYPE_LEV);
    sensor_connect_infor_t      sensor_connect;
	sensor_connect_infor_get(SENSOR_TYPE_LEV, &sensor_connect);
    sensor_module_evt_handler evt_handler             = sensor_module_evt_handler_get();
    sensor_saved_work_t       *p_sensor_saved         = NULL;
    sensor_original_data_t    *p_sensor_original_data = sensor_original_data_get();
    sensor_work_state_t       sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    int8_t                    index                   = -1;
    ret_code_t                err_code                = NRF_SUCCESS;

    sensor_systime_update();

    memset ((uint8_t *)&sensor_search_infor, 0x00, sizeof(sensor_search_infor_t));
    ant_lev_disp_evt_handler(p_ant_evt, p_context);

    if (p_ant_evt->channel == m_ant_lev.channel_number)
    {
        switch (p_ant_evt->event)
        {
            case EVENT_CHANNEL_CLOSED:
                memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
                memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
                sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
                sensor_search_infor.sensor_type = SENSOR_TYPE_LEV;

            	sensor_ant_leave_rx_search(SENSOR_TYPE_LEV);

                err_code = sd_ant_channel_unassign(m_ant_lev.channel_number);
                APP_ERROR_CHECK(err_code);
                m_ant_lev.channel_number = 0;
                sensor_ant_channel_num_unassign(SENSOR_TYPE_LEV);

                bool forbidden_mask = sensor_connect_infor_get_forbidden_mask(SENSOR_TYPE_LEV);
                if(sensor_connect_infor_get(SENSOR_TYPE_LEV, &sensor_connect))
                {
                    sensor_connect_infor_clear(SENSOR_TYPE_LEV);

                    if (NULL != evt_handler && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)
                    {
                        evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                    }
                }

                if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
				{
                    if (SENSOR_WORK_STATE_IDLE != sensor_work_state)
                    {
                        p_sensor_saved->rssi             [index] = 0;
                        p_sensor_saved->battery_voltage  [index] = 0xff;
                        p_sensor_saved->sensor_work_state[index] = SENSOR_WORK_STATE_SAVED;
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }

                if((!forbidden_mask && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)       //连接完成后异常断连
					|| (forbidden_mask && sensor_connect.state == SENSOR_CONNECT_STATE_CLOSE_WAIT)
                    || sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)                      //连接超时
                {
                    // connected状态下断连，检索saved数组是否有同类型sensor并进行连接
                    sensor_connect_from_saved_info(sensor_search_infor.sensor_type);
                }

                if (NULL != evt_handler)
                {
                    evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                    evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                }

                memset(&p_sensor_original_data->levData, 0xff, sizeof(lev_data_t));
                p_sensor_original_data->battery_list.lev = 0;
                break;
            case EVENT_RX_FAIL_GO_TO_SEARCH:
                // sd_ant_channel_close(LEV_CHANNEL_NUMBER);
				// sensor_ant_close(SENSOR_TYPE_LEV);
            	sensor_ant_enter_rx_search(SENSOR_TYPE_LEV);
                break;
            case EVENT_RX_SEARCH_TIMEOUT:
            default:
                break;
        }
    }
}

NRF_SDH_ANT_OBSERVER(m_lev_ant_observer, ANT_LEV_ANT_OBSERVER_PRIO, lev_ant_evt, &m_ant_lev);

/**
 * @*********************************************************************************************
 * @description: 初始化ANT ebike通道
 * @param {ant_id_t} *id
 * @return {*}
 * @*********************************************************************************************
 */
void ant_lev_rx_profile_setup(ant_id_t *id)
{
    sensor_connect_infor_t      sensor_connect;
	sensor_connect_infor_get(SENSOR_TYPE_LEV, &sensor_connect);
    ant_channel_config_t channel_config;

    memcpy ((uint8_t *)&sensor_connect.sensor_id.ant_id, (uint8_t *)id, sizeof(ant_id_t));
    sensor_connect_infor_set(SENSOR_TYPE_LEV, &sensor_connect);

    /*
    //device num的组成
    //1byte   1byte    |     1byte      |      1byte                  从左到右高到低
    //   device id     | device type    |MSN:extended device number LSN:Transmission Type
    */
    uint16_t sensor_id = (uint16_t)id->id;
    uint8_t trans_type = CHAN_ID_TRANS_TYPE;
    if (id->id > 0xffff)
    {
        trans_type = id->trans_type;
    }

    //加载参数
    LoadChnConf_lev_rx(&channel_config);
    channel_config.device_number     = sensor_id;
    channel_config.transmission_type = trans_type;

    ant_lev_disp_init(&m_ant_lev, (const ant_channel_config_t *)&channel_config, ant_lev_rx_evt_handler);
}

/**
 * @*********************************************************************************************
 * @description: 开启ebike通道
 * @param {*}
 * @return {*}
 * @*********************************************************************************************
 */
void ant_lev_rx_open(void)
{
    ret_code_t             err_code          = NRF_SUCCESS;

    err_code = ant_lev_disp_open(&m_ant_lev);
    APP_ERROR_CHECK(err_code);
}

uint32_t ant_lev_get_info(uint8_t type)
{
    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_LEV, SENSOR_CONNECT_STATE_CONNECTED);
    sensor_original_data_t *p_sensor_original_data = sensor_original_data_get();
    lev_data_t *p_lev_data = &p_sensor_original_data->levData;

    if (res)
    {
    	switch (type)
    	{
    	case enum_LEV_INFO_BAT_TEMPERATURE:
    		return  p_lev_data->system_information.battery_temperature;
    	case enum_LEV_INFO_MOTOR_TEMPERATURE:
    		return  p_lev_data->system_information.motor_temperature;
    	case enum_LEV_INFO_ASSIST_LEVEL:
    		return  p_lev_data->system_information.assist_mode;
    	case enum_LEV_INFO_ASSIST_LEVEL_MAX:
    		return  p_lev_data->capability.assist_mode;
    	case enum_LEV_INFO_REGEN_LEVEL:
    		return  p_lev_data->system_information.regen_mode;
    	case enum_LEV_INFO_REGEN_LEVEL_MAX:
    		return  p_lev_data->capability.regen_mode;
    	case enum_LEV_INFO_MANUAL_THROTTLE:
    		return  p_lev_data->system_information.manual_throttle;
    	case enum_LEV_INFO_LIGHT:
    		return  p_lev_data->system_information.light;
    	case enum_LEV_INFO_LIGHT_BEAM:
    		return  p_lev_data->system_information.light_beam;
    	case enum_LEV_INFO_LIGHT_LEFT:
    		return  p_lev_data->system_information.signal_left;
    	case enum_LEV_INFO_LIGHT_RIGHT:
    		return  p_lev_data->system_information.signal_right;
    	case enum_LEV_INFO_MANUAL_GEAR:
    		if (p_lev_data->system_information.gear_exist)
    		{
        		return  p_lev_data->system_information.manual_gear;
    		}
    		else
    		{
        		return  0;
    		}
    	case enum_LEV_INFO_GEAR_FRONT:
    		if (p_lev_data->system_information.gear_exist)
    		{
        		return  p_lev_data->system_information.front_gear;
    		}
    		else
    		{
        		return  0;
    		}
    	case enum_LEV_INFO_GEAR_REAR:
    		if (p_lev_data->system_information.gear_exist)
			{
				return  p_lev_data->system_information.rear_gear;
			}
			else
			{
				return  0;
			}
    	case enum_LEV_INFO_ERROR:
			return  p_lev_data->error_message;
    	case enum_LEV_INFO_SPEED:
			return  p_lev_data->lev_speed;
    	case enum_LEV_INFO_ODOMETER:
			return  p_lev_data->odometer;
    	case enum_LEV_INFO_REMAINING_RANGE:
			return  p_lev_data->remaining_range;
    	case enum_LEV_INFO_FUEL:
			return  p_lev_data->battery_information.fuel_consumption;
    	case enum_LEV_INFO_BATTERY:
			return  p_lev_data->system_information.battery_capacity;
    	case enum_LEV_INFO_BATTERY_VOL:
			return  p_lev_data->battery_information.battery_voltage;
    	case enum_LEV_INFO_ASSIST_PCT:
			return  p_lev_data->assist_pct;
    	case enum_LEV_INFO_CHARGING_COUNT:
			return  p_lev_data->battery_information.charging_cycle_count;
    	case enum_LEV_INFO_CHARGING_DISTANCE:
			return  p_lev_data->battery_information.distance_on_current_charge;
    	case enum_LEV_INFO_WHEEL_SIZE:
			return  p_lev_data->capability.wheel_circumference;
    	case enum_LEV_INFO_MANUFACTURER:
			return  p_lev_data->manufacturer_id;
    	case enum_LEV_INFO_VERSION_H:
			return  p_lev_data->hw_revision;
    	case enum_LEV_INFO_VERSION_S:
			return (((uint16_t)p_lev_data->sw_revision_major) << 8) + p_lev_data->sw_revision_minor;
    	case enum_LEV_INFO_SN:
			return  p_lev_data->serial_number;
    	default:
    		break;
    	}
    }
    return 0;
}

void ant_lev_set_info(uint8_t type, uint16_t val)
{
    ret_code_t             err_code          = NRF_SUCCESS;
    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_LEV, SENSOR_CONNECT_STATE_CONNECTED);

    if (res)
    {
        err_code = ant_lev_disp_request_set(&m_ant_lev, (ant_lev_cmd_e)type, val);
    }

    APP_ERROR_CHECK(err_code);
}

#endif
