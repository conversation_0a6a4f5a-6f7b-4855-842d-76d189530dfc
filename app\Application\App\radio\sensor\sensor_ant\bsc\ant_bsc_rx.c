/**
 * @*************************************** Copyright (c) ***************************************
 * @                              <PERSON>han Qiwu Technology Co., Ltd
 * @*********************************************************************************************
 * @Author: Jiang<PERSON>hen
 * @Date: 2021-11-02 17:51:27
 * @LastEditTime: 2021-12-06 20:21:32
 * @LastEditors: JiangZhen
 * @FilePath: \iGS320_APP\src\radio\sensor\sensor_ant\bsc\ant_bsc_rx.c
 * @Description: ANT速度踏频二合一模块接口源文件
 * @*********************************************************************************************
 */
#include "nrf_log.h"
#include "nrf_sdh.h"
#include "nrf_sdh_ant.h"
#include "ant_bsc.h"
#include "app_error.h"
#include "ant_parameters.h"
#include "ant_interface.h"
#include "basictype.h"

#include "qw_sensor_common.h"
#include "qw_sensor_config.h"
#include "qw_sensor_data.h"
#include "sensor_ant_common.h"
#include "cbsc_rx.h"
#include "cfg_header_def.h"

static void ant_bsc_rx_evt_handler(ant_bsc_profile_t * p_profile, ant_bsc_evt_t event);
static void bsc_ant_evt(ant_evt_t *p_ant_evt, void * p_context);

//--------------------------------------变量定义-------------------------------------------//
BSC_DISP_PROFILE_CONFIG_DEF(m_ant_bsc,
                            ant_bsc_rx_evt_handler);

static ant_bsc_profile_t m_ant_bsc;

#if ANT_SENSOR_BPWR_ENABLED
extern uint8_t g_pwrCad;
extern uint8_t g_pwrSpd;
#else
static uint8_t g_pwrCad = 0;
static uint8_t g_pwrSpd = 0;
#endif


NRF_SDH_ANT_OBSERVER(m_bsc_ant_observer, ANT_BSC_ANT_OBSERVER_PRIO,
                bsc_ant_evt, &m_ant_bsc);

//--------------------------------------函数定义-------------------------------------------//
/**
 * @*********************************************************************************************
 * @description: 加载ANT速度踏频二合一接收通道默认配置
 * @param {ant_channel_config_t } *p_channel_config
 * @return {*}
 * @*********************************************************************************************
 */
static void LoadChnConf_bsc_rx(ant_channel_config_t  *p_channel_config)
{
    p_channel_config->channel_number = sensor_ant_channel_num_get(SENSOR_TYPE_CBSC);
    p_channel_config->channel_type = CHANNEL_TYPE_SLAVE;
    p_channel_config->ext_assign = BSC_EXT_ASSIGN;
    p_channel_config->rf_freq = BSC_ANTPLUS_RF_FREQ;
    p_channel_config->transmission_type = CHAN_ID_TRANS_TYPE;
    p_channel_config->device_type = BSC_COMBINED_DEVICE_TYPE;
    p_channel_config->channel_period = BSC_MSG_PERIOD_COMBINED;
    p_channel_config->network_number = ANTPLUS_NETWORK_NUM;
}

/**
 * @*********************************************************************************************
 * @description: 速度踏频二合一事件处理函数
 * @param {ant_bsc_profile_t *} p_profile
 * @param {ant_bsc_evt_t} event
 * @return {*}
 * @*********************************************************************************************
 */
static void ant_bsc_rx_evt_handler(ant_bsc_profile_t * p_profile, ant_bsc_evt_t event)
{
    sensor_search_infor_t       sensor_search_infor;
    // sensor_connect_infor_t      *p_sensor_connect       = sensor_connect_infor_get(SENSOR_TYPE_CBSC);
    sensor_connect_infor_t      sensor_connect;
    sensor_module_evt_handler   evt_handler             = sensor_module_evt_handler_get();
    sensor_saved_work_t         *p_sensor_saved         = NULL;
    uint16_t                    cadence_event_time      = 0;                                           ///< Speed or cadence event time.
    uint16_t                    cadence_rev_count       = 0;                                           ///< Speed or cadence revolution count.
    uint16_t                    speed_event_time        = 0;                                           ///< Speed event time.
    uint16_t                    speed_rev_count         = 0;
    uint8_t                     cadData                 = 0Xff;                                        ///< Speed revolution count.
    uint16_t                    wheel_speed             = 0xffff;
    uint16_t                    wheel_delta             = 0xffff;
    sensor_work_state_t         sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    sensor_module_param_input_t *p_param                = sensor_module_param_input_get();
    sensor_original_data_t      *p_sensor_original_data = sensor_original_data_get();
    int8_t                      index                   = -1;
    static uint8_t              low_power_indicate_flag = FALSE;

    sensor_ant_leave_rx_search(SENSOR_TYPE_CBSC);
    sensor_connect_infor_get(SENSOR_TYPE_CBSC, &sensor_connect);

    memset ((uint8_t *)&sensor_search_infor, 0x00, sizeof(sensor_search_infor_t));
    switch (event)
    {
        case ANT_BSC_PAGE_0_UPDATED:
        case ANT_BSC_PAGE_1_UPDATED:
            /* fall through */
        case ANT_BSC_PAGE_2_UPDATED:
            /* fall through */
        case ANT_BSC_PAGE_3_UPDATED:
            /* fall through */
        case ANT_BSC_PAGE_4_UPDATED:
            /* fall through */
        case ANT_BSC_PAGE_5_UPDATED:
            break;
        case ANT_BSC_COMB_PAGE_0_UPDATED:
            //更新数据
            cadence_event_time = p_profile->page_comb_0.cadence_event_time;
            cadence_rev_count =  p_profile->page_comb_0.cadence_rev_count;
            speed_event_time = p_profile->page_comb_0.speed_event_time;
            speed_rev_count =  p_profile->page_comb_0.speed_rev_count;
            DecodeSpd(speed_event_time, speed_rev_count, &wheel_speed, &wheel_delta);
            DecodeCad(cadence_event_time, cadence_rev_count, &cadData);

            if (0xff != cadData)
            {
                p_sensor_original_data->cbscData.cadence = cadData;
            }
            if (0xffff != wheel_speed)
            {
                p_sensor_original_data->cbscData.wheel_speed = wheel_speed;

                if (0xffff != wheel_delta)
                {
                     p_sensor_original_data->cbscData.wheel_delta += wheel_delta;
                }
            }
            memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
            memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
            sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
            sensor_search_infor.sensor_type = SENSOR_TYPE_CBSC;
            // sensor_saved_work_infor_get(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state);

            if (SENSOR_CONNECT_STATE_CONNECTING == sensor_connect.state)
            {
                sensor_connect.state = SENSOR_CONNECT_STATE_CONNECTED;
                sensor_connect_infor_set(SENSOR_TYPE_CBSC, &sensor_connect);

                if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
                {
                    if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
                    {
                        p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                        p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
                        cfg_mark_update(enum_cfg_ant_ble_dev);
                    }
                    else if (SENSOR_WORK_STATE_FORBIDDEN == sensor_work_state)
                    {
                        sensor_infor_t sensor_infor = {0};
                        sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                        sensor_disconnect(&sensor_infor);
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }
                else
                {
                    if(sensor_disconnect_item_check(&sensor_search_infor))
                    {
                        sensor_disconnect_info_remove(&sensor_search_infor);
                        sensor_infor_t sensor_infor = {0};
                        sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                        sensor_disconnect(&sensor_infor);
                        return;
                    }
                    sensor_saved_work_infor_add(&sensor_search_infor);
                    sensor_search_infor_del(&sensor_search_infor);
                }

                if (NULL != evt_handler)
                {
                    evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, TRUE);
                    evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, 0);
                    evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, SENSOR_TYPE_CBSC, SENSOR_RADIO_TYPE_ANT, TRUE);
                }

                low_power_indicate_flag = TRUE;
            }
            else if (SENSOR_CONNECT_STATE_CONNECTED == sensor_connect.state)
            {
                if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
                {
                    if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
                    {
                        p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                        p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }
            }

            p_sensor_original_data->wheel_size = sensor_saved_size_get(&sensor_search_infor);

            if (sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
            {
                if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
                {
#if SENSOR_DEVICE_INFO_ENABLED
                    if (p_sensor_saved->sensor_manufacturer[index].manufacturer_ant != p_profile->page_2.manuf_id ||
                        p_sensor_saved->sensor_serial[index].serial_ant != p_profile->page_2.serial_num ||
                        p_sensor_saved->sensor_hw_version[index].version_ant != p_profile->page_3.hw_version ||
                        p_sensor_saved->sensor_model[index].model_ant != p_profile->page_3.model_num ||
                        p_sensor_saved->sensor_sw_version[index].version_ant != p_profile->page_3.sw_version)
                    {
                        p_sensor_saved->sensor_manufacturer[index].manufacturer_ant = p_profile->page_2.manuf_id;
                        p_sensor_saved->sensor_serial[index].serial_ant       = p_profile->page_2.serial_num;
                        p_sensor_saved->sensor_hw_version[index].version_ant      = p_profile->page_3.hw_version;
                        p_sensor_saved->sensor_model[index].model_ant        = p_profile->page_3.model_num;
                        p_sensor_saved->sensor_sw_version[index].version_ant      = p_profile->page_3.sw_version;
                        if (evt_handler != NULL)
                        {
                            evt_handler(EVENT_SENSOR_MANUFACTURER_RECEIVED, NULL, sensor_search_infor.sensor_type, 0, index);
                        }
                    }
#endif
                    if (p_profile->page_4.bat_status >= BSC_BAT_STATUS_NEW && p_profile->page_4.bat_status <= BSC_BAT_STATUS_CRITICAL)
                    {
                        p_sensor_saved->battery_voltage[index] = (6 - p_profile->page_4.bat_status) * 20;    //1-5级，转换成百分比发送给APP
                    }
                }
                sensor_saved_work_infor_release_write_lock(index);
            }

            if (p_profile->page_4.bat_status >= SENSOR_BATT_NEW && p_profile->page_4.bat_status <= SENSOR_BATT_CRITICAL)
            {
                p_sensor_original_data->battery_list.cbsc = (SENSOR_BATT_MAX - p_profile->page_4.bat_status) * 20;
            }

            if (p_profile->page_4.bat_status  == BSC_BAT_STATUS_CRITICAL && TRUE == low_power_indicate_flag)
            {
                low_power_indicate_flag = FALSE;
                if (NULL != evt_handler)
                {
                    evt_handler(EVENT_SENSOR_LOW_POWER, &sensor_search_infor.sensor_id, SENSOR_TYPE_CBSC, SENSOR_RADIO_TYPE_ANT, 0);
                }
            }
            break;
        default:
            break;
    }
    if (SENSOR_CONNECT_STATE_CONNECTED == sensor_connect.state)
    {
        if (NULL != evt_handler)
        {
            evt_handler(EVENT_SENSOR_DATA_UPDATE, NULL, SENSOR_TYPE_CBSC, SENSOR_RADIO_TYPE_ANT, 0);
        }
    }
}

/**
 * @*********************************************************************************************
 * @description: 速度踏频二合一ANT事件处理函数
 * @param {ant_evt_t} *p_ant_evt
 * @param {void *} p_context
 * @return {*}
 * @*********************************************************************************************
 */
static void bsc_ant_evt(ant_evt_t *p_ant_evt, void * p_context)
{
    sensor_search_infor_t     sensor_search_infor;
    // sensor_connect_infor_t    *p_sensor_connect       = sensor_connect_infor_get(SENSOR_TYPE_CBSC);
    sensor_connect_infor_t    sensor_connect;
    sensor_module_evt_handler evt_handler             = sensor_module_evt_handler_get();
    sensor_saved_work_t       *p_sensor_saved         = NULL;
    sensor_original_data_t    *p_sensor_original_data = sensor_original_data_get();
    sensor_work_state_t       sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    int8_t                    index                   = -1;
    ret_code_t                err_code                = NRF_SUCCESS;
    bool manual_connect_status_sent = false;

    sensor_systime_update();

    sensor_connect_infor_get(SENSOR_TYPE_CBSC, &sensor_connect);
    memset ((uint8_t *)&sensor_search_infor, 0x00, sizeof(sensor_search_infor_t));
    ant_bsc_disp_evt_handler(p_ant_evt, p_context);

    if (p_ant_evt->channel == m_ant_bsc.channel_number)
    {
        switch (p_ant_evt->event)
        {
            case EVENT_CHANNEL_CLOSED:
                memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
                memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
                sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
                sensor_search_infor.sensor_type = SENSOR_TYPE_CBSC;

            	sensor_ant_leave_rx_search(SENSOR_TYPE_CBSC);

                err_code = sd_ant_channel_unassign(m_ant_bsc.channel_number);
                APP_ERROR_CHECK(err_code);
                m_ant_bsc.channel_number = 0;
                sensor_ant_channel_num_unassign(SENSOR_TYPE_CBSC);

                bool forbidden_mask = sensor_connect_infor_get_forbidden_mask(SENSOR_TYPE_CBSC);

                if(sensor_connect_infor_get(SENSOR_TYPE_CBSC, &sensor_connect))
                {
                    sensor_connect_infor_clear(SENSOR_TYPE_CBSC);
                    if (NULL != evt_handler && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)
                    {
                        manual_connect_status_sent = true;
                        evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, &sensor_connect.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                    }
                }


                if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
                {
                    if (SENSOR_WORK_STATE_IDLE != sensor_work_state)
                    {
                        p_sensor_saved->rssi             [index] = 0;
                        p_sensor_saved->battery_voltage  [index] = 0xff;
                        p_sensor_saved->sensor_work_state[index] = SENSOR_WORK_STATE_SAVED;
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }

                if((!forbidden_mask && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)       //连接完成后异常断连
                    || (forbidden_mask && sensor_connect.state == SENSOR_CONNECT_STATE_CLOSE_WAIT)
                    || sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)                      //连接超时
                {
                    // connected状态下断连，检索saved数组是否有同类型sensor并进行连接
                    sensor_connect_from_saved_info(sensor_search_infor.sensor_type);
                }

                if (NULL != evt_handler)
                {
                    evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, 0);
                    if (!manual_connect_status_sent)
                    {
                        evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, SENSOR_TYPE_CBSC, SENSOR_RADIO_TYPE_ANT, FALSE);
                    }
                }

                p_sensor_original_data->cbscData.wheel_speed =  0xffff;
                p_sensor_original_data->cbscData.wheel_delta = 0xffff;
                p_sensor_original_data->cbscData.cadence = 0xff;
                p_sensor_original_data->battery_list.cbsc = 0;
                break;
            case EVENT_RX_FAIL_GO_TO_SEARCH:
                // sd_ant_channel_close(CBSC_CHANNEL_NUMBER);
                //sensor_ant_close(SENSOR_TYPE_CBSC);
            	sensor_ant_enter_rx_search(SENSOR_TYPE_CBSC);
                break;
            case EVENT_RX_SEARCH_TIMEOUT:
            default:
                break;
        }
    }
}

/**
 * @*********************************************************************************************
 * @description: 初始化ANT速度踏频二合一通道
 * @param {ant_id_t} *id
 * @return {*}
 * @*********************************************************************************************
 */
void ant_bsc_rx_profile_setup(ant_id_t *id)
{
    ret_code_t             err_code           = NRF_SUCCESS;
    // sensor_connect_infor_t * p_sensor_connect = sensor_connect_infor_get(SENSOR_TYPE_CBSC);
    sensor_connect_infor_t sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_CBSC, &sensor_connect);
    ant_channel_config_t channel_config;

    memcpy ((uint8_t *)&sensor_connect.sensor_id.ant_id, (uint8_t *)id, sizeof(ant_id_t));

    sensor_connect_infor_set(SENSOR_TYPE_CBSC, &sensor_connect);
    /*
    //device num的组成
    //1byte   1byte    |     1byte      |      1byte                  从左到右高到低
    //   device id     | device type    |MSN:extended device number LSN:Transmission Type
    */
    uint16_t sensor_id = (uint16_t)id->id;
    uint8_t trans_type = CHAN_ID_TRANS_TYPE;
    if (id->id > 0xffff)
    {
        trans_type = id->trans_type;
    }

    //加载参数
    LoadChnConf_bsc_rx(&channel_config);
    channel_config.device_number = sensor_id;
    channel_config.transmission_type = trans_type;

    err_code = ant_bsc_disp_init(&m_ant_bsc, (const ant_channel_config_t *)&channel_config, BSC_DISP_PROFILE_CONFIG(m_ant_bsc));
    APP_ERROR_CHECK(err_code);
}

/**
 * @*********************************************************************************************
 * @description: 开启CBSC通道
 * @param {*}
 * @return {*}
 * @*********************************************************************************************
 */
void ant_bsc_rx_open(void)
{
    ret_code_t             err_code          = NRF_SUCCESS;
    err_code = ant_bsc_disp_open(&m_ant_bsc);
    APP_ERROR_CHECK(err_code);

    InitAntSpdAndCadStatus();
}


