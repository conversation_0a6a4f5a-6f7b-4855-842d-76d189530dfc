/************************************************************************
* 
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   schedule_course_manege.c
@Time    :   2025/04/24 10:36:20
<AUTHOR>   lxin
* 
**************************************************************************/
#include "../inc/schedule_course_manage.h"
#include "../inc/workout_data.h"
#include "../inc/workout_decode.h"
#include "../inc/workout_def.h"
#include "cfg_header_def.h"
#include "cmsis_os2.h"
#include "gui_event_service.h"
#include "qw_fit_api.h"
#include "qw_fs_api.h"
#include "qw_mlist.h"
#include "qw_os_adaptor.h"
#include "service_datetime.h"
#include "thread_pool.h"

#define MAX_COURSE_ID_LENGTH                20    //课程uuid最大长度
#define COURSE_NAME_MAX                     90    //课程最长名字
#define COURSES_FILE_NAME_MAX               120   //课程文件名最大长度
#define TIME_STAMP_LENGTH                   10    //时间戳长度

#define SCHEDULE_COURSES_FILES_MAX          199   //课程文件允许存在的最大个数
#define SCHEDULE_COURSES_FILES_DELETE_EXTRA 60    //额外删除的课程文件  目前为0，不可超过199

#ifdef IGS_DEV
#include "mem_section.h"
L2_RET_BSS_SECT_BEGIN(schedule_mem)
ALIGN(4) static workout_step_param_t my_schedule_course_step;
L2_RET_BSS_SECT_END
#else
static workout_step_param_t my_schedule_course_step;   //用于开始执行和预览详情的训练课程实例
#endif

//课程类型
typedef enum {
    enum_init_status_not_ok,    // 课程表未初始化
    enum_init_status_running,   // 课程表初始化中
    enum_init_status_ok,        // 课程表初始化完成
} schedule_init_status_t;

/**
 * @brief 读取课程表 线程函数
 */
typedef struct
{
    course_t course;
    uint8_t is_exsited;
} my_courses_t;

static uint8_t cur_course_cnt = 0;                             //当前初始化的天中的课程的数量
static uint32_t last_init_time = 0;                            //训练计划实际上次初始化的时间

static schedule_cache_head_t schedule_cache_head;              //训练计划缓存头
static schedules_t schedules;                                  //训练计划
static uint8_t courses_cnt[SCHEDULE_NEARBY_COURSES_NUM];       //具体每一天的课程数量
static my_courses_t my_courses[SCHEDULE_NEARBY_COURSES_NUM];   //所有计划中当天的前后若干天课程结构体,用做显示是否有训练计划
static uint8_t is_today_course_completed = 0;   //今天课程是否完成的标志,用于在连接APP的时候通知APP同步Schedules.fit文件,每次初始化训练计划文件的时候都需要重新赋值
static uint32_t done_serial_number = 0;         //完成了的课程ID
static qw_mlist_t mlist_course;
static qw_mlist_t mlist_file;
static schedule_init_status_t g_schedule_inited = enum_init_status_not_ok;

// 静态

/**
 * @brief 更新课程表头
 */
static int schedule_head_encode()
{
    QW_FIL *fp = NULL;
    uint32_t tt = 0;

    if (qw_f_open(&fp, SCHEDULE_CACHE, QW_FA_CREATE_ALWAYS | QW_FA_WRITE) != QW_OK)
    {
        return false;
    }
    qw_f_write(fp, (const void *) &schedule_cache_head, sizeof(schedule_cache_head_t), &tt);
    qw_f_close(fp);
    return true;
}

/**
 * @brief 更新课程表头
 */
static int schedule_head_decode()
{
    QW_FIL *fp = NULL;
    uint32_t tt = 0;

    if (qw_f_open(&fp, SCHEDULE_CACHE, QW_FA_READ) != QW_OK)
    {
        return false;
    }
    qw_f_read(fp, (void *) &schedule_cache_head, sizeof(schedule_cache_head_t), &tt);
    qw_f_close(fp);
    return true;
}

/**
 * @brief 输入时间判断是否经过一天
 */
static uint8_t is_crossing_one_day(uint32_t fit_time, uint32_t last_check)
{
    uint8_t ret = 0;
    qw_tm_t input_time = {0};
    qw_tm_t last_time = {0};

    service_datetime_gmt2datetime(fit_time, &input_time, 0);
    service_datetime_gmt2datetime(last_check, &last_time, 0);

    if (input_time.tm_year == last_time.tm_year)
    {
        if ((input_time.tm_yday - last_time.tm_yday) > 0)   //同年跨天
        {
            ret = 1;
        }
    }
    else
    {
        if ((input_time.tm_year - last_time.tm_year) == 1)   //跨年
        {
            ret = 1;
        }
    }
    return ret;
}

/**
 * @brief 加载训练计划到内存 初始化运行时变量
 */
static void courses_pre_init(uint32_t fit_time)
{
    uint32_t time_fit_start = 0;
    memset(my_courses, 0x00, sizeof(my_courses));
    memset(courses_cnt, 0x00, sizeof(courses_cnt));
    for (int8_t i = 0; i < SCHEDULE_NEARBY_COURSES_NUM; i++)
    {
        time_fit_start = fit_time + (i - SCHEDULE_TODAY) * FIT_TIME_ONE_DAY;
        for (uint8_t k = 0; k < schedules.schedules.courses_num; k++)
        {
            if (schedules.schedules.courses[k].scheduled_time >= time_fit_start
                && schedules.schedules.courses[k].scheduled_time < time_fit_start + FIT_TIME_ONE_DAY)
            {
                my_courses[i].is_exsited = 1;
                courses_cnt[i]++;   //记录当前星期课程个数
                memcpy(&my_courses[i].course, &schedules.schedules.courses[k], sizeof(course_t));
            }
        }
    }
    if (my_courses[SCHEDULE_TODAY].course.completed)
    {
        is_today_course_completed = 1;
        done_serial_number = my_courses[SCHEDULE_TODAY].course.serial_number;
    }
}

/**
 * @brief 初始化课程表
 */
static void find_file_path_by_index(schedule_day week_index, char *file_path, size_t size_path, char *m_file_name, size_t size_name)
{
    char file_name[COURSES_FILE_NAME_MAX];
    char courses_name[COURSE_NAME_MAX];
    uint32_t serial_number = my_courses[week_index].course.serial_number;
    uint32_t serial_number_parse = 0;

    qw_mlist_t *mnode;
    qw_mlist_init(&mlist_course);
    fs_scan_files(&mlist_course, SCHEDULE_COURSE, ".fit", 0, 0, 0, 0);
    FOR_MLIST(mnode, &mlist_course)   //mnode->mnode_data不包含'\0',包含扩展名
    {
        //课程名先转化为字符串
        char name_temp[COURSE_NAME_MAX];
        char *uuid = NULL;
        memset(name_temp, 0x00, sizeof(name_temp));
        if (mnode->data_size < COURSE_NAME_MAX)
        {
            memcpy(name_temp, mnode->mnode_data, mnode->data_size);
            name_temp[mnode->data_size] = '\0';
            uuid = strstr(name_temp, "_");
        }
        //解析第一个"_",判断"_"前的数字是时间戳还是课程id
        if (uuid)
        {
            char serial_temp[MAX_COURSE_ID_LENGTH];   //第一串数字buffer，优先解出来判断是时间戳还是课程id
            uint8_t length = 0;
            if ((uuid - name_temp) < MAX_COURSE_ID_LENGTH)
            {
                memcpy(serial_temp, name_temp, uuid - name_temp);
                serial_temp[uuid - name_temp] = '\0';
                length = strlen(serial_temp);
                serial_number_parse = atoi(serial_temp);

                if (length == TIME_STAMP_LENGTH)   //长度为10  可能是时间戳，需要进一步判断
                {
                    if (isdigit(uuid[1]))          //如果是数字,直接判定下一串字符为uuid
                    {
                        char *uuid2 = strstr(uuid + 1, "_");
                        if (uuid2)
                        {
                            char serial_temp2[MAX_COURSE_ID_LENGTH];
                            if ((uuid2 - uuid - 1) < MAX_COURSE_ID_LENGTH)
                            {
                                memcpy(serial_temp2, uuid + 1, uuid2 - uuid - 1);
                                serial_temp2[uuid2 - uuid - 1] = '\0';

                                char *name = strstr(uuid2 + 1, ".fit");
                                if (name)
                                {
                                    if ((name - uuid2 - 1) < COURSE_NAME_MAX)
                                    {
                                        memcpy(courses_name, uuid2 + 1, name - uuid2 - 1);
                                        courses_name[name - uuid2 - 1] = '\0';
                                        if (m_file_name)
                                        {
                                            size_t cpy_len = (name - uuid2 > size_name ? size_name : name - uuid2);
                                            memcpy(m_file_name, uuid2 + 1, cpy_len - 1);
                                            m_file_name[cpy_len - 1] = '\0';
                                        }
                                    }
                                    serial_number_parse = atoi(serial_temp2);
                                    if (serial_number_parse == serial_number)   //uuid匹配成功
                                    {
                                        sprintf_array(file_name, "%d_%d_%s.fit", (int) my_courses[week_index].course.scheduled_time, (int) serial_number,
                                                courses_name);   //courses_name包含.fit
                                        sprintf_ptr(file_path, size_path, "%s/%s", SCHEDULE_COURSE, file_name);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    else   //第一串数字后的第一个字符不是数字，判定serial_temp为uuid，直接开始解析课程名字
                    {
                        //正常uuid解析
                        char *name = strstr(uuid, ".fit");
                        if (name)
                        {
                            if ((name - uuid - 1) < COURSE_NAME_MAX)
                            {
                                memcpy(courses_name, uuid + 1, name - uuid - 1);
                                courses_name[name - uuid - 1] = '\0';
                                if (m_file_name)
                                {
                                    memcpy(m_file_name, uuid + 1, name - uuid - 1);
                                    m_file_name[name - uuid - 1] = '\0';
                                }
                            }
                        }
                        if (serial_number_parse == serial_number)                                 //匹配成功
                        {
                            sprintf_array(file_name, "%d_%s.fit", (int) serial_number, courses_name);   //courses_name包含.fit
                            sprintf_ptr(file_path, size_path, "%s/%s", SCHEDULE_COURSE, file_name);              //file_path包含.fit
                            break;
                        }
                    }
                }
                else   //长度不为10,第一串数字一定是课程id
                {
                    //正常uuid解析
                    char *name = strstr(uuid, ".fit");
                    if (name)
                    {
                        if ((name - uuid - 1) < COURSE_NAME_MAX)
                        {
                            memcpy(courses_name, uuid + 1, name - uuid - 1);
                            courses_name[name - uuid - 1] = '\0';
                            if (m_file_name)
                            {
                                memcpy(m_file_name, uuid + 1, name - uuid - 1);
                                m_file_name[name - uuid - 1] = '\0';
                            }
                        }
                    }
                    if (serial_number_parse == serial_number)                                 //匹配成功
                    {
                        sprintf_array(file_name, "%d_%s.fit", (int) serial_number, courses_name);   //courses_name包含.fit
                        sprintf_ptr(file_path, size_path, "%s/%s", SCHEDULE_COURSE, file_name);     //拼接文件路径
                        break;
                    }
                }
            }
        }
    }
    qw_mlist_uninit(&mlist_course);
}

/**
 * @brief 创建训练计划临时文件
 */
static int schedule_course_cache()
{
    workout_step_param_t temp_schedule_course_step;
    schedule_course_cache_t course_cacha;
    char file_path[180];
    QW_FIL *fp = NULL;
    uint32_t tt = 0;

    if (qw_f_open(&fp, SCHEDULE_COURSE_CACHE, QW_FA_CREATE_ALWAYS | QW_FA_WRITE) != QW_OK)
    {
        return -1;
    }
    for (uint8_t week_index = 0; week_index < SCHEDULE_NEARBY_COURSES_NUM; week_index++)
    {
        memset(file_path, 0x00, sizeof(file_path));
        memset(&course_cacha, 0x00, sizeof(schedule_course_cache_t));
        find_file_path_by_index(week_index, file_path, sizeof(file_path), (char *) course_cacha.name, sizeof(course_cacha.name));
        if (workout_fit_load(file_path, &temp_schedule_course_step) == LIB_ERROR)
        {
            memset(&course_cacha, 0x00, sizeof(schedule_course_cache_t));
            qw_f_write(fp, (const void *) &course_cacha, sizeof(schedule_course_cache_t), &tt);
        }
        else
        {
            course_cacha.step_cnt = temp_schedule_course_step.total_step_num;
            for (uint8_t k = 0; k < course_cacha.step_cnt; k++)
            {
                if (enumDurationOfTime == temp_schedule_course_step.workout_step[k].duration_type)
                {
                    course_cacha.time_total += temp_schedule_course_step.workout_step[k].duration_val;
                }
                else
                {
                    course_cacha.time_total = 0;
                    break;
                }
            }
            course_cacha.week_index = week_index;
            course_cacha.complete = my_courses[week_index].course.completed;
            course_cacha.is_valid = 1;
            course_cacha.sport_type = temp_schedule_course_step.sport_type;
            qw_f_write(fp, (const void *) &course_cacha, sizeof(schedule_course_cache_t), &tt);
        }
    }
    qw_f_close(fp);
    return 0;
}

/**
 * @brief 读取课程表 线程函数
 */
static bool schedule_reinit_cb(const thread_pool_task *task_info)
{
    // 初始化
    memset(&schedules, 0x00, sizeof(schedules_t));

    uint32_t fit_time = service_datetime_get_fit_time();           // 获取当前时间
    fit_time = (fit_time / FIT_TIME_ONE_DAY) * FIT_TIME_ONE_DAY;   // 获取当天0点时间

    schedule_head_decode();                                        // 读取课程表头

    fit_schedules_loadupdate(fit_time, &schedules, NULL);          // 加载课程

    courses_pre_init(fit_time);                                    // 初始化课程运行时数据

    schedule_course_cache();                                       // 创建缓存

    if (is_crossing_one_day(fit_time, schedule_cache_head.last_check))
    {
        //跨天处理 清空当时完成数据
        schedule_cache_head.last_check = fit_time;
        schedule_cache_head.complete = 0;
        schedule_cache_head.today_fit = 0;
        schedule_head_encode();   // 保存课程表头
    }

    last_init_time = fit_time;   // 记录最近初始化时间

    g_schedule_inited = enum_init_status_ok;

    schedule_course_refresh_page();   // 发送指令刷新页面-由页面层判断是否刷新

    return true;
}

/**
 * @brief 冒泡排序
 */
static void bubble_sort(uint32_t *buf, int size)
{
    int i, j;
    uint32_t temp;
    for (i = 0; i < size; i++)
    {
        for (j = i; j < size; j++)
        {
            if (buf[i] > buf[j])
            {
                temp = buf[i];
                buf[i] = buf[j];
                buf[j] = temp;
            }
        }
    }
}

/**
 * @brief 课程id解析
 */
static void course_id_parse(char *src, char *buf)
{
    if (src)
    {
        char *temp1 = strstr(src, "_");
        if (temp1)
        {
            char serial_temp[MAX_COURSE_ID_LENGTH];   //第一串数字先解出来判断是时间戳还是课程id
            uint8_t length = 0;
            if ((temp1 - src) < MAX_COURSE_ID_LENGTH)
            {
                memcpy(serial_temp, src, temp1 - src);
                serial_temp[temp1 - src] = '\0';
                length = strlen(serial_temp);

                if (length == TIME_STAMP_LENGTH)   //长度为10  可能是时间戳，需要进一步判断
                {
                    if (isdigit(temp1[1]))         //如果是数字,直接判定下一串字符为uuid
                    {
                        char *uuid = strstr(temp1 + 1, "_");
                        if (uuid)
                        {
                            if ((uuid - temp1 - 1) < MAX_COURSE_ID_LENGTH)
                            {
                                memcpy(buf, temp1 + 1, uuid - temp1 - 1);
                                buf[uuid - temp1 - 1] = '\0';
                            }
                        }
                    }
                    else   //不是数字 serial_temp就是uuid
                    {
                        memcpy(buf, serial_temp, MAX_COURSE_ID_LENGTH);
                    }
                }
                else   //长度不为10，一定是uuid
                {
                    memcpy(buf, serial_temp, MAX_COURSE_ID_LENGTH);
                }
            }
        }
    }
}

static bool schedules_courses_file_deal(const thread_pool_task *task_info)
{
    qw_mlist_t *mnode;
    qw_mlist_t *mnode_sort;
    qw_mlist_init(&mlist_file);
    uint32_t file_number = fs_scan_files(&mlist_file, SCHEDULE_COURSE, ".fit", 0, 0, 0, 0);
    if (file_number > SCHEDULE_COURSES_FILES_MAX)
    {
        uint32_t *course_id;
        course_id = (uint32_t *) mem_malloc(sizeof(uint32_t) * file_number);
        if (course_id)
        {
            uint32_t i = 0;
            FOR_MLIST(mnode, &mlist_file)   //mnode->mnode_data不包含'\0',包含扩展名
            {
                char id[MAX_COURSE_ID_LENGTH];
                memset(id, 0x00, sizeof(id));
                course_id_parse((char *) mnode->mnode_data, id);
                course_id[i] = (uint32_t) atoi(id);
                i++;
            }
            bubble_sort(course_id, file_number);
            for (uint8_t j = 0; j < file_number - SCHEDULE_COURSES_FILES_MAX + SCHEDULE_COURSES_FILES_DELETE_EXTRA; j++)
            {
                char file_path[180];
                memset(file_path, 0x00, sizeof(file_path));
                FOR_MLIST(mnode_sort, &mlist_file)
                {
                    char id_sort[MAX_COURSE_ID_LENGTH];
                    memset(id_sort, 0x00, sizeof(id_sort));
                    course_id_parse((char *) mnode_sort->mnode_data, id_sort);
                    if (atoi(id_sort) == course_id[j])
                    {
                        char file_name[COURSES_FILE_NAME_MAX];
                        memset(file_name, 0x00, sizeof(file_name));
                        if (mnode_sort->data_size > COURSES_FILE_NAME_MAX - 1)   //防止拷贝溢出
                        {
                            memcpy(file_name, mnode_sort->mnode_data, COURSES_FILE_NAME_MAX - 1);
                            file_name[COURSES_FILE_NAME_MAX - 1] = '\0';
                        }
                        else
                        {
                            memcpy(file_name, mnode_sort->mnode_data, mnode_sort->data_size);
                            file_name[mnode_sort->data_size] = '\0';
                        }
                        sprintf_array(file_path, "%s/%s", SCHEDULE_COURSE, file_name);
                        fs_del_files(file_path);
                    }
                }
            }
            mem_free(course_id);
            course_id = NULL;
        }
    }
    qw_mlist_uninit(&mlist_file);
    thread_pool_add_task(schedule_reinit_cb, NULL, NULL, osPriorityBelowNormal2);
    return true;
}

/**
 * @brief 更新课程表
 */
static void schedule_completed_update(schedule_day week_index, uint8_t schedule_index)
{
    // rt_kprintf("schedule_completed_update %d %d\n", week_index, schedule_index);
    my_courses[week_index].course.completed = 1;
    uint32_t serial_number = my_courses[week_index].course.serial_number;
    for (uint8_t i = 0; i < schedules.schedules.courses_num; i++)
    {
        if (serial_number == schedules.schedules.courses[i].serial_number)
        {
            // rt_kprintf("schedules.schedules.courses[i].completed %d\n", serial_number);
            schedules.schedules.courses[i].completed = 1;
        }
    }

    DEV_INFO info = {0};
    info.serial_number_h = get_serial_number_high();
    info.serial_number = get_serial_number_low();
    info.manufacturer = get_manufacturer_id();
    info.product = get_product_id();
    info.hardware_version = get_hardware_version();
    info.software_version = get_major_app_version();
    fit_schedules_save(&schedules, &info, NULL);

    schedule_course_cache();   // 创建缓存

    done_serial_number = my_courses[week_index].course.serial_number;
    is_today_course_completed = 1;
}

/**
 * @brief 更新课程表 线程函数
 */
static bool schedule_update(const thread_pool_task *task_info)
{
    schedule_cache_head_t *p_arg = (schedule_cache_head_t *) task_info->arg;

    if (p_arg->complete == 100)
    {
        schedule_completed_update(SCHEDULE_TODAY, 0);
    }
    schedule_head_encode();
    return true;
}

// 外部

void crossing_day_check()
{
    uint32_t fit_time = service_datetime_get_fit_time();           // 获取当前时间
    fit_time = (fit_time / FIT_TIME_ONE_DAY) * FIT_TIME_ONE_DAY;   // 获取当天0点时间

    if (g_schedule_inited == enum_init_status_not_ok || (g_schedule_inited == enum_init_status_ok && is_crossing_one_day(fit_time, last_init_time)))
    {
        //跨天处理
        g_schedule_inited = enum_init_status_running;
        thread_pool_add_task(schedule_reinit_cb, NULL, NULL, osPriorityBelowNormal2);
    }
}

schedule_day get_schedule_start_day(void)
{
    if (g_schedule_inited == enum_init_status_ok)
    {
        for (int8_t i = 0; i < SCHEDULE_NEARBY_COURSES_NUM; i++)
        {
            if (my_courses[i].is_exsited)
            {
                return SCHEDULE_14_DAY_AGO + i;
            }
        }
    }
    return __SCHEDULE_DAY_MAX;
}

schedule_day get_schedule_end_day(void)
{
    if (g_schedule_inited == enum_init_status_ok)
    {
        for (int8_t i = SCHEDULE_NEARBY_COURSES_NUM - 1; i >= 0; i--)
        {
            if (my_courses[i].is_exsited)
            {
                return SCHEDULE_14_DAY_AGO + i;
            }
        }
    }
    return __SCHEDULE_DAY_MAX;
}

uint8_t get_schedule_day_courses_num(schedule_day week_index)
{
    return courses_cnt[week_index];
}

int get_schedule_day_course_cache(schedule_day week_index, schedule_course_cache_t *p_arg)
{
    QW_FIL *fp = NULL;
    uint32_t tt = 0;

    if (p_arg == NULL)
    {
        return false;
    }

    if (qw_f_open(&fp, SCHEDULE_COURSE_CACHE, QW_FA_READ) != QW_OK)
    {
        return false;
    }
    qw_f_lseek(fp, week_index * sizeof(schedule_course_cache_t));
    qw_f_read(fp, (void *) p_arg, sizeof(schedule_course_cache_t), &tt);
    qw_f_close(fp);
    return true;
}

int get_schedule_day_course_info(schedule_day week_index, workout_step_param_t *p_arg)
{
    if (p_arg == NULL)
    {
        return false;
    }

    char file_path[80];
    if (schedule_day_course_update(week_index, file_path, sizeof(file_path)))
    {
        memcpy(p_arg, &my_schedule_course_step, sizeof(workout_step_param_t));
        return true;
    }
    else
    {
        return false;
    }
}

int schedule_day_course_update(schedule_day week_index, char *file_path, size_t size)
{
    // char file_path[80];
    memset(&my_schedule_course_step, 0x00, sizeof(workout_step_param_t));
    find_file_path_by_index(week_index, file_path, size, NULL, 0);
    return (workout_fit_load(file_path, &my_schedule_course_step) == LIB_NOERROR);
}

workout_step_param_t *get_schedule_course_info(void)
{
    return &my_schedule_course_step;
}

int get_is_schedule_day_completed(schedule_day week_index)
{
    return my_courses[week_index].course.completed;
}

void schedule_course_state_update(uint32_t fit_time, uint8_t pct)
{
    uint32_t cur_time = service_datetime_get_fit_time();           // 获取当前时间
    cur_time = (cur_time / FIT_TIME_ONE_DAY) * FIT_TIME_ONE_DAY;   // 获取当天0点时间

    schedule_cache_head.last_check = cur_time;
    schedule_cache_head.today_fit = fit_time;
    schedule_cache_head.complete = pct;
    thread_pool_add_task(schedule_update, (void *) &schedule_cache_head, NULL, osPriorityBelowNormal2);
}

void schedule_courses_file_manage(void)
{
    thread_pool_add_task(schedules_courses_file_deal, NULL, NULL, osPriorityBelowNormal2);
}

void set_schedule_course_name(const char *name)
{
    if (name != NULL)
    {
        strcpy_safe((char *) schedule_cache_head.name, name, sizeof(schedule_cache_head.name));
        schedule_head_encode();
    }
}

const char *get_schedule_course_name(void)
{
    return (const char *) schedule_cache_head.name;
}

uint8_t cal_schedule_course_finish_pct(int cur_step, int cur_progress)
{
    uint32_t total_time = 0;
    uint32_t cur_time = 0;
    for (uint8_t k = 0; k < my_schedule_course_step.total_step_num; k++)
    {
        if (enumDurationOfTime == my_schedule_course_step.workout_step[k].duration_type)
        {
            total_time += my_schedule_course_step.workout_step[k].duration_val;

            if (k < cur_step)
            {
                cur_time = total_time;
            }
            else if (k == cur_step)
            {
                cur_time += (cur_progress * my_schedule_course_step.workout_step[k].duration_val / 100);
            }
        }
        else
        {
            total_time = 0;
            break;
        }
    }

    if (total_time > 0)
    {
        return (cur_time * 100 / total_time);
    }
    else if (my_schedule_course_step.total_step_num > 0)
    {
        return (cur_step * 100 / my_schedule_course_step.total_step_num);
    }
    else
    {
        return 0;
    }
}

int get_schedule_today_fit_info(uint32_t *fit_time, uint8_t *pct)
{
    if (get_schedule_day_courses_num(SCHEDULE_TODAY) > 0)
    {
        *fit_time = schedule_cache_head.today_fit;
        *pct = schedule_cache_head.complete;
        return true;
    }
    else
    {
        return false;
    }
}

static gui_evt_service_page_command_t training_plan_page_command;

/**
 * @brief 发送指令刷新页面
 */
void schedule_course_refresh_page()
{
    memset(&training_plan_page_command, 0x00, sizeof(gui_evt_service_page_command_t));
    training_plan_page_command.page = "TrainingPlan";
    training_plan_page_command.cmd = 1;   //TrainingPlan_Cmd::SET_REFRESH
    training_plan_page_command.user_data = (void *) 1;
    submit_gui_event(GUI_EVT_SERVICE_PAGE_COMMAND, 0, &training_plan_page_command);
}
