/************************************************************************
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
*@File : ble_pm_manager.c
*<AUTHOR> 
*@Date : 2025/01/15
*@Description : Software uart driver header file
************************************************************************/
#include <rtthread.h>
#include "ble_pm_manager.h"
#include "thread_pool.h"
#include "qw_sensor_common.h"
#include "qw_sensor_common_low.h"
#include "ble_central.h"
#include "ble_peripheral.h"
#include "system_utils.h"
#include "ser_user_data_def.h"
#include "nrf_sdh_freertos.h"
#include "power_ctl.h"
#include "ant_ble_sensor_ctl.h"
#include "stm32_phy_spi.h"
#include "bf0_hal.h"
#include "drv_io.h"
#include "pm_manager.h"
#include "nrf_sdm.h"
#include "sensor_ble.h"
#include <rtdevice.h>

static bool ble_ant_stack_reboot_request = false;

extern bool ble_ant_task_status_get(void);
extern void ble_ant_sensor_module_init(void);


bool ble_ant_suspend_prepare(const void *task_info)
{
    if(ble_ant_task_status_get()){
        // sensor_all_close(SENSOR_CLOSE_MODE_SLEEP);
        rt_pm_request(PM_SLEEP_MODE_IDLE);
        sensor_ble_periph_all_colse();
        ant_sensor_check_timer_suspend();
        ant_ble_sensor_operation_cancel();

        ble_center_timer_stop();
        // advertising_stop();
        // ser_user_settings_master_sleep(true);
        // ser_user_settings_sys_poweroff();
        nrf_sdh_freertos_suspend();
        nrf_sdh_thread_set_idle();
        
        // stm32_phy_req_rdy_pin_deinit();
        // stm32_phy_spi_deinit();
        // sd_softdevice_disable();

        power_control_ble(false);
        rt_pm_release(PM_SLEEP_MODE_IDLE);
    }
    return true;
}

bool ble_ant_resume_prepare(const void *task_info)
{
	if(ble_ant_task_status_get()){
        power_control_ble(true);
        HAL_Delay(10);
        
        ble_ant_sensor_module_init();
        sensor_ant_search_channel_parameter_reinit();

        ser_user_master_sleep_status_clear();
        nrf_sdh_softdevice_resume();
        // sensor_search_enter(SENSOR_SEARCH_MODE_BACKGROUND_ALL);
        // advertising_start();
        // ble_center_timer_start();
        // ant_sensor_check_timer_resume();
        nrf_sdh_freertos_resume();
    }
    return true;
}
static bool ble_ant_sdh_reboot_task(const void *task_info)
{
	ble_ant_suspend_prepare(task_info);
	ble_ant_resume_prepare(task_info);
	rt_kprintf("ble_sdh_hot_restart end\n");
	ble_ant_stack_reboot_request = false;
	return true;
}

void ble_sdh_hot_restart(void)
{
	if(!ble_ant_stack_reboot_request){
		ble_ant_stack_reboot_request = true;
		rt_kprintf("ble_sdh_hot_restart\n");
		thread_pool_add_task((task_process)ble_ant_sdh_reboot_task,NULL,NULL,osPriorityRealtime);
	}
}

int ble_pm_control(bool on_off)
{
	if(!on_off){
		thread_pool_add_task((task_process)ble_ant_suspend_prepare,NULL,NULL,osPriorityRealtime);
	}else{
		thread_pool_add_task((task_process)ble_ant_resume_prepare,NULL,NULL,osPriorityRealtime);
	}
	return 0;
}
MSH_CMD_EXPORT(ble_pm_control, ble_pm_control);

static void pm_status_notify(uint32_t sta, void *data)
{
	if (PM_SLEEP_ENTER == sta){
		ble_ant_suspend_prepare(NULL);
	}else if (PM_SLEEP_EXIT == sta){
		ble_ant_resume_prepare(NULL);
	}
}
DECLARE_PM_EVENT_LISTENER(pm_ble_module, pm_status_notify, PM_LEVEL_DEVICE);

#ifndef BSP_BLE_DTM_ENABLE
int pm_ble_ant_init(void)
{
	app_register_pm_event_listener(&pm_ble_module);
    return 0;
}
// INIT_COMPONENT_EXPORT(pm_ble_ant_init);
#endif
