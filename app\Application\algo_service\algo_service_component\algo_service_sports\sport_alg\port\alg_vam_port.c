/************************************************************************​
*Copyright(c) 2025, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#include "alg_vam.h"
#include "alg_vam_port.h"

static float s_alg_vam_buf[ALG_VAM_BUF_SIZE] = { 0 };

#ifdef IGS_DEV
static VamCalculator s_vam_calculator;
#else
static VamCalculator s_vam_calculator = {
    .buf = s_alg_vam_buf,
    .capacity = ALG_VAM_BUF_SIZE,
};
#endif

void alg_vam_init(void)
{
#ifdef IGS_DEV
    s_vam_calculator.buf = s_alg_vam_buf;
    s_vam_calculator.capacity = ALG_VAM_BUF_SIZE;
#endif

    vam_calculator_reset(&s_vam_calculator);
}

int alg_vam_exec(const alg_vam_input_t *input, alg_vam_output_t *output)
{
    return vam_calculator_exec(&s_vam_calculator, (VamCalcInput *)input, (VamCalcOutput *)output);
}
