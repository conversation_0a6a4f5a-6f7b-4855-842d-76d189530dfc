{"core.repositoryformatversion": ["1"], "core.filemode": ["false"], "core.symlinks": ["false"], "core.ignorecase": ["true"], "extensions.worktreeconfig": ["true"], "filter.lfs.smudge": ["git-lfs smudge --skip -- %f"], "filter.lfs.process": ["git-lfs filter-process --skip"], "remote.origin.url": ["ssh://yanxu<PERSON><PERSON>@********:29418/qw_algo/navigation"], "remote.origin.review": ["http://********:8081"], "remote.origin.projectname": ["qw_algo/navigation"], "remote.origin.fetch": ["+refs/heads/*:refs/remotes/origin/*"]}