/************************************************************************​
*Copyright(c) 2024, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#include <string.h>
#include <stddef.h>
#include <stdbool.h>
#include <math.h>
#include "navi_port.h"
#include "navi_route_port.h"
#include "navi_turn_port.h"
#include "navi_climb_port.h"
#include "navi_sign_port.h"
#include "navi_util.h"
#include "cnx.h"
#include "gpx.h"
#include "tcx.h"
#include "fit_route.h"
#include "trck.h"
//#include "decode_cache.h"
#include "data_check.h"
//#include "ride_data_simulate.h"
#include "igs_dev_config.h"
#include "qw_fs.h"
#include "map_input.h"

//导航模块状态
typedef enum _navi_state
{
    enumNAVI_STATE_UNINITED,                    //未初始化，上电后默认状态
    enumNAVI_STATE_READY,                       //就绪
    enumNAVI_STATE_PROCESSING,                  //路书解析和处理中
    enumNAVI_STATE_PREVIEWING,                  //路书解析完成，正在预览中
    enumNAVI_STATE_MATCHING,                    //路书解析完成，实时匹配中
} navi_state_t;

//导航线路处理结果
typedef enum _navi_process_status
{
    enumNAVI_PROCESS_NEVER,                     //尚未启用任何一条导航线路
    enumNAVI_PROCESS_SUCCESS,                   //导航线路处理成功，可以获取线路数据
    enumNAVI_PROCESS_FAILED,                    //导航线路处理失败，不能获取线路数据
} navi_process_status_t;

//GPS坐标
typedef struct _gps_coord
{
    struct minmea_float longitude;
    struct minmea_float latitude;
} gps_coord_t;

//导航模块运行所使用的数据
typedef struct _navi
{
    navi_state_t state;
    navi_process_status_t status;
    char *fpath;                                //启用/预览的路书路径
    float route_dist;                           //线路距离（m）
    float match_dist;                           //最后一次匹配到线路的对应距离（m）
    float off_course_dist;                      //偏航刚开始时对应的线路距离（m）
    gps_coord_t last_fixed;                     //上一次GPS坐标
    navi_cbs_t cbs;                             //导航模块回调函数
    uint32_t fixed_cnt;                         //有效GPS坐标计数
    uint8_t is_processing;                      //是否处理中，true - 处理中，false - 未处理中
    uint8_t is_reverse;                         //线路是否反转，true - 返程导航，false - 正常导航
    uint8_t is_matched;                         //是否已经匹配到线路，true - 已匹配，false - 未匹配
    uint8_t is_off_coursed;                     //是否已经偏航，true - 已偏航，false - 未偏航
} navi_t;

typedef int (*decode_route_one_point_f)(QW_FIL *fp, struct minmea_float *lat, struct minmea_float *lon, uint32_t *dis, int32_t *alt, void *user_data);
typedef int (*decode_route_next_sign_f)(QW_FIL *fp, struct minmea_float *lat, struct minmea_float *lon, char *text, sign_type_e *type);
typedef int (*decode_route_over_f)(void);

static char s_fpath[100] = { 0 };

#ifdef IGS_DEV
static navi_t s_navi;
#else
static navi_t s_navi = {
    .state = enumNAVI_STATE_UNINITED,
    .status = enumNAVI_PROCESS_NEVER,
    .fpath = s_fpath,
    .is_processing = false,
};
#endif

static navi_calc_input_t s_navi_input = { 0 };
static navi_progress_t s_navi_progress = { 0 };

static NaviRouteWpFilter s_navi_route_wp_filter = { 0 };

//上电后调用，初始化导航模块
int navi_init(const navi_cbs_t *cbs)
{
#ifdef IGS_DEV
    s_navi.state = enumNAVI_STATE_UNINITED;
    s_navi.status = enumNAVI_PROCESS_NEVER;
    s_navi.fpath = s_fpath;
    s_navi.is_processing = false;
#else
    if (s_navi.state != enumNAVI_STATE_UNINITED)
    {
        return -1;
    }
#endif

    navi_route_init();
    navi_turn_init();
    navi_climb_init();
    navi_sign_init();

    if (cbs != NULL)
    {
        s_navi.cbs.route_use_cb = cbs->route_use_cb;
        s_navi.cbs.route_stop_cb = cbs->route_stop_cb;
        s_navi.cbs.route_arrive_cb = cbs->route_arrive_cb;
    }
    else
    {
        s_navi.cbs.route_use_cb = NULL;
        s_navi.cbs.route_stop_cb = NULL;
        s_navi.cbs.route_arrive_cb = NULL;
    }

    s_navi.state = enumNAVI_STATE_READY;

    return 0;
}

//关机前调用，做一些清理工作
void navi_uninit(void)
{
    if (s_navi.state == enumNAVI_STATE_PROCESSING)
    {
        navi_route_process_terminate();
        navi_turn_process_terminate();
        navi_climb_process_terminate();
        navi_sign_process_terminate();
        s_navi.state = enumNAVI_STATE_READY;
    }
    else if (s_navi.state == enumNAVI_STATE_MATCHING)
    {
        stop_course();

#if RIDE_DATA_SIMULATE_ENABLE
        ride_data_simulator_stop();
#endif
    }
    else if (s_navi.state == enumNAVI_STATE_PREVIEWING)
    {
        stop_preview();
    }

    s_navi.cbs.route_use_cb = NULL;
    s_navi.cbs.route_stop_cb = NULL;
    s_navi.cbs.route_arrive_cb = NULL;

    navi_route_uninit();
    navi_turn_uninit();
    navi_climb_uninit();
    navi_sign_uninit();
}

//获取导航计算和匹配所需的输入数据
void navi_calc_input(const navi_calc_input_t *input)
{
    if (input != NULL)
    {
        memcpy(&s_navi_input, input, sizeof(navi_calc_input_t));
    }
}

//处理导航线路
static int navi_route_process_exec(const char *path)
{
    if (path == NULL)
    {
        return -1;
    }

    const uint32_t len = strlen(path);

    if (len > MAX_ROUTE_PATHNAME_LEN || len < 5)
    {
        return -1;
    }

    //检查导航线路对应的相关文件是否都有效，都有效则直接加载即可
    if (navi_route_is_tcnx_valid(path) == true &&
        navi_turn_is_tnav_valid(path) == true &&
        navi_climb_is_tclm_valid(path) == true &&
        navi_sign_is_tsgn_valid(path) == true)
    {
        if (navi_route_tcnx_load(path) != 0)
        {
            return -1;
        }

        if (navi_turn_tnav_load(path) != 0)
        {
            return -1;
        }

        if (navi_climb_tclm_load(path) != 0)
        {
            return -1;
        }

        if (navi_sign_tsgn_load(path) != 0)
        {
            return -1;
        }

        return 0;
    }

    if (navi_route_process_start(path) != 0)
    {
        return -1;
    }

    if (navi_turn_process_start(path) != 0)
    {
        navi_route_process_terminate();
        return -1;
    }

    if (navi_climb_process_start(path) != 0)
    {
        navi_route_process_terminate();
        navi_turn_process_terminate();
        return -1;
    }

    QW_FIL *fp = NULL;
    if(qw_f_open(&fp, path, QW_FA_READ) != QW_OK)
    {
        navi_route_process_terminate();
        navi_turn_process_terminate();
        navi_climb_process_terminate();
        return -1;
    }

    struct minmea_float lat = {0x7fffffff, 1};
    struct minmea_float lon = {0x7fffffff, 1};
    int32_t alt = 0x7fffffff;
    uint32_t dis_tmp = 0xffffffff;
    void *user_data = NULL;
    // DECODE_RESULT *decode_result = NULL;
    // uint8_t is_route_fit = false;

    NaviWaypointA wpa = { 0 };
    NaviWaypointAdc wpadc = { 0 };

    NaviSign sign = { 0 };
    NaviRouteProgress progress = { 0 };

    decode_route_one_point_f decode_route_one_point = NULL;
    decode_route_next_sign_f decode_route_next_sign = NULL;
    decode_route_over_f decode_route_over = NULL;

    if (strcmp(path+len-4, ".cnx") == 0)
    {
        decode_route_one_point = decode_cnx_one_point;
        decode_route_next_sign = decode_cnx_next_sign;
    }
    else if (strcmp(path+len-4, ".gpx") == 0)
    {
        decode_route_one_point = decode_gpx_one_point;
    }
    else if (strcmp(path+len-4, ".tcx") == 0)
    {
        decode_route_one_point = decode_tcx_one_point;
    }
    else if (strcmp(path+len-4, ".fit") == 0)
    {
        // decode_result = Dec_RequestResultBuffer();
        // if(!decode_result)
        // {
        //     goto err_handler1;
        // }
        // user_data = DEC_InitDecoder(decode_result);
        // if(!user_data)
        // {
        //     DEC_ReleaseResultBuffer(decode_result);
        //     goto err_handler1;
        // }
        decode_route_one_point = decode_fit_one_point;
        decode_route_over = decode_fit_over;
        // is_route_fit = true;
    }
    else if (strcmp(path+len-5, ".trck") == 0)
    {
        if (trck_is_file_valid(fp) == false || trck_trackpoint_decode_start(fp) != 0)
        {
            goto err_handler1;
        }
        decode_route_one_point = trck_decode_one_point;
    }
    else
    {
        goto err_handler1;
    }

    navi_route_wp_filter_reset(&s_navi_route_wp_filter);

    while(!qw_f_eof(fp))
    {
        if(decode_route_one_point(fp, &lat, &lon, &dis_tmp, &alt, user_data) != -1)
        {
            if(position_invalid_check(&lat) == true || position_invalid_check(&lon) == true)
            {
                continue;
            }

            wpa.lng = (double)lon.value / (double)lon.scale;
            wpa.lat = (double)lat.value / (double)lat.scale;
            wpa.alt = (float)alt / 100.0f;

            if (navi_route_wp_filter_exec(&s_navi_route_wp_filter, &wpa, &wpadc) != 0)
            {
                continue;
            }

            if (navi_route_process(&wpadc) != 0)
            {
                navi_turn_process_terminate();
                navi_climb_process_terminate();
                goto err_handler2;
            }

            if (navi_turn_process(&wpadc) != 0)
            {
                navi_route_process_terminate();
                navi_climb_process_terminate();
                goto err_handler2;
            }

            if (navi_climb_process(&wpadc) != 0)
            {
                navi_route_process_terminate();
                navi_turn_process_terminate();
                goto err_handler2;
            }
        }
    }

    if (navi_route_wp_filter_the_last_get(&s_navi_route_wp_filter, &wpadc) != 0)
    {
        navi_route_process_terminate();
        navi_turn_process_terminate();
        navi_climb_process_terminate();
        goto err_handler2;
    }

    if (navi_route_process_end(&wpadc) != 0)
    {
        navi_turn_process_terminate();
        navi_climb_process_terminate();
        goto err_handler2;
    }

    if (navi_turn_process_end(&wpadc) != 0)
    {
        navi_route_process_exit();
        navi_climb_process_terminate();
        goto err_handler2;
    }

    if (navi_climb_process_end(&wpadc) != 0)
    {
        navi_route_process_exit();
        navi_turn_process_exit();
        goto err_handler2;
    }

    if (decode_route_next_sign != NULL)
    {
        if (qw_f_lseek(fp, 0) != QW_OK)
        {
            goto err_handler3;
        }

        if (navi_sign_process_start(path) != 0)
        {
            goto err_handler3;
        }

        while(!qw_f_eof(fp))
        {
            if(true == decode_route_next_sign(fp, &lat, &lon, sign.name, (sign_type_e *)(&sign.type)))
            {
                break; //返回true表示已解析完
            }

            sign.lng = (double)lon.value / (double)lon.scale;
            sign.lat = (double)lat.value / (double)lat.scale;

            if (navi_route_find(sign.lng, sign.lat, false, &progress) != 0)
            {
                goto err_handler3;
            }

            sign.dist = progress.dist;

            if (navi_sign_process(&sign) != 0)
            {
                goto err_handler3;
            }
        }

        if (navi_sign_process_end(wpadc.dist) != 0)
        {
            goto err_handler3;
        }
    }
    else
    {
        if (navi_sign_process_start(path) != 0)
        {
            goto err_handler3;
        }

        if (navi_sign_process_end(wpadc.dist) != 0)
        {
            goto err_handler3;
        }
    }

    if (decode_route_over != NULL)
    {
        decode_route_over();
    }
    qw_f_close(fp);
    // if (is_route_fit == true)
    // {
    //     DEC_EndDecoder(user_data);
    //     DEC_ReleaseResultBuffer(decode_result);
    // }

    return 0;

err_handler1:
    navi_route_process_terminate();
    navi_turn_process_terminate();
    navi_climb_process_terminate();
    if (decode_route_over != NULL)
    {
        decode_route_over();
    }
    qw_f_close(fp);
    return -1;

err_handler2:
    if (decode_route_over != NULL)
    {
        decode_route_over();
    }
    qw_f_close(fp);
    // if (is_route_fit == true)
    // {
    //     DEC_EndDecoder(user_data);
    //     DEC_ReleaseResultBuffer(decode_result);
    // }
    return -1;

err_handler3:
    navi_route_process_exit();
    navi_turn_process_exit();
    navi_climb_process_exit();
    if (decode_route_over != NULL)
    {
        decode_route_over();
    }
    qw_f_close(fp);
    // if (is_route_fit == true)
    // {
    //     DEC_EndDecoder(user_data);
    //     DEC_ReleaseResultBuffer(decode_result);
    // }
    return -1;
}

//启用一条通常的导航线路
//注意：需要关闭规划路书（比如规划到位置点的路书）
int route_use(const char *path, uint8_t is_reverse)
{
    s_navi.is_processing = true;

    if (s_navi.state == enumNAVI_STATE_MATCHING)
    {
        stop_course();

#if RIDE_DATA_SIMULATE_ENABLE
        ride_data_simulator_stop();
#endif
    }

    if (s_navi.state == enumNAVI_STATE_PREVIEWING)
    {
        stop_preview();
    }

    if (s_navi.state != enumNAVI_STATE_READY)
    {
        s_navi.is_processing = false;
        return -1;
    }

    if (path == NULL)
    {
        s_navi.is_processing = false;
        return -1;
    }

    const uint32_t len = strlen(path);

    if (len > MAX_ROUTE_PATHNAME_LEN || len < 5)
    {
        s_navi.is_processing = false;
        return -1;
    }

    strcpy(s_navi.fpath, path);

    s_navi.state = enumNAVI_STATE_PROCESSING;

    int ret = navi_route_process_exec(path);

    if (ret == 0)
    {
        s_navi.status = enumNAVI_PROCESS_SUCCESS;
        s_navi.state = enumNAVI_STATE_MATCHING;
        s_navi.fixed_cnt = 0;

        s_navi.is_reverse = is_reverse;
        if (s_navi.is_reverse == true)
        {
            navi_route_reverse();
            navi_climb_reverse();
        }

        s_navi.is_matched = false;
        s_navi.is_off_coursed = false;

        //获取线路距离，用于到达终点判定
        const NaviRouteData *route_data = navi_route_data_get();
        if (route_data != NULL)
        {
            s_navi.route_dist = route_data->dist;
        }

        //初始化导航进度输出
        s_navi_progress.type = enumNAVI_TYPE_ROUTE;
        s_navi_progress.status = enumNAVI_STATUS_STILL;
        s_navi_progress.turn.is_valid = false;
        s_navi_progress.climb.is_valid = false;
        s_navi_progress.climb.d2climb = -1.0f;
        s_navi_progress.sign.is_valid = false;
        s_navi_progress.dist = 0.0f;
        s_navi_progress.dist2dest = s_navi.route_dist;

        const NaviClimbEnsembleData *climb_ensemble = navi_climb_ensemble_data_get();
        if (climb_ensemble != NULL)
        {
            s_navi_progress.climb.d_residual = climb_ensemble->dist;
            s_navi_progress.climb.h_residual = climb_ensemble->h;
            s_navi_progress.climb.idx = 0xFFFFFFFF;
            s_navi_progress.climb.idx_next = 0xFFFFFFFF;
            s_navi_progress.climb.climb_remain = 0xFFFFFFFF;
        }

        if (s_navi.cbs.route_use_cb != NULL)
        {
            s_navi.cbs.route_use_cb();
        }
    }
    else
    {
        s_navi.status = enumNAVI_PROCESS_FAILED;
        s_navi.state = enumNAVI_STATE_READY;
    }

    s_navi.is_processing = false;

    return ret;
}

//预览一条导航线路
int route_preview(const char *path)
{
    s_navi.is_processing = true;

    if (s_navi.state == enumNAVI_STATE_MATCHING)
    {
        stop_course();

#if RIDE_DATA_SIMULATE_ENABLE
        ride_data_simulator_stop();
#endif
    }

    if (s_navi.state == enumNAVI_STATE_PREVIEWING)
    {
        stop_preview();
    }

    if (s_navi.state != enumNAVI_STATE_READY)
    {
        s_navi.is_processing = false;
        return -1;
    }

    if (path == NULL)
    {
        s_navi.is_processing = false;
        return -1;
    }

    const uint32_t len = strlen(path);

    if (len > 94 || len < 5)
    {
        s_navi.is_processing = false;
        return -1;
    }

    strcpy(s_navi.fpath, path);

    s_navi.state = enumNAVI_STATE_PROCESSING;

    int ret = navi_route_process_exec(path);

    if (ret == 0)
    {
        s_navi.status = enumNAVI_PROCESS_SUCCESS;
        s_navi.state = enumNAVI_STATE_PREVIEWING;
    }
    else
    {
        s_navi.status = enumNAVI_PROCESS_FAILED;
        s_navi.state = enumNAVI_STATE_READY;
    }

    s_navi.is_processing = false;

    return ret;
}

static navi_turn_type_t navi_turn_type_calc(float angle)
{
    navi_turn_type_t turn_type = enumNAVI_TURN_GO_STRAIGHT;

    if (angle < 0.0f)
    {
        if (angle <= -NAVI_TURN_SHARPLY_ANGLE_THRES)
        {
            turn_type = enumNAVI_TURN_LEFT_ROUND;
        }
        else if (angle <= -NAVI_TURN_NORMALLY_ANGLE_THRES)
        {
            turn_type = enumNAVI_TURN_LEFT_SHARPLY;
        }
        else if (angle <= -NAVI_TURN_SLIGHTLY_ANGLE_THRES)
        {
            turn_type = enumNAVI_TURN_LEFT_NORMALLY;
        }
        else if (angle <= -NAVI_TURN_GO_STRAIGHT_ANGLE_THRES)
        {
            turn_type = enumNAVI_TURN_LEFT_SLIGHTLY;
        }
    }
    else
    {
        if (angle >= NAVI_TURN_SHARPLY_ANGLE_THRES)
        {
            turn_type = enumNAVI_TRUN_RIGHT_ROUND;
        }
        else if (angle >= NAVI_TURN_NORMALLY_ANGLE_THRES)
        {
            turn_type = enumNAVI_TURN_RIGHT_SHARPLY;
        }
        else if (angle >= NAVI_TURN_SLIGHTLY_ANGLE_THRES)
        {
            turn_type = enumNAVI_TURN_RIGHT_NORMALLY;
        }
        else if (angle >= NAVI_TURN_GO_STRAIGHT_ANGLE_THRES)
        {
            turn_type = enumNAVI_TURN_RIGHT_SLIGHTLY;
        }
    }

    return turn_type;
}

//导航计算和匹配
static int navi_route_update_exec(navi_t *navi, navi_calc_input_t *input, navi_progress_t *progress)
{
    if (navi == NULL || input == NULL || progress == NULL)
    {
        return -1;
    }

    if (position_invalid_check(&input->longitude) == true || position_invalid_check(&input->latitude) == true)
    {
        navi->fixed_cnt = 0;
        progress->status = enumNAVI_STATUS_NOT_FIXED;
        progress->turn.is_valid = false;
        progress->climb.is_valid = false;
        progress->climb.idx = 0xFFFFFFFF;
        progress->climb.idx_next = 0xFFFFFFFF;
        progress->climb.climb_remain = 0xFFFFFFFF;
        progress->climb.d2climb = -1.0f;
        progress->sign.is_valid = false;
        return -1;
    }

    NaviRouteProgress route_progress = { 0 };

    if (navi->fixed_cnt == 0)
    {
        memcpy(&navi->last_fixed.longitude, &input->longitude, sizeof(struct minmea_float));
        memcpy(&navi->last_fixed.latitude, &input->latitude, sizeof(struct minmea_float));

        progress->status = enumNAVI_STATUS_STILL;
        progress->turn.is_valid = false;
        progress->climb.is_valid = false;
        progress->climb.idx = 0xFFFFFFFF;
        progress->climb.idx_next = 0xFFFFFFFF;
        progress->climb.climb_remain = 0xFFFFFFFF;
        progress->climb.d2climb = -1.0f;
        progress->sign.is_valid = false;
        navi->fixed_cnt += 1;

        const double lng = (double)input->longitude.value / (double)input->longitude.scale;
        const double lat = (double)input->latitude.value / (double)input->latitude.scale;

        if (navi_route_find(lng, lat, s_navi.is_reverse, &route_progress) == 0)
        {
            //当前经纬度到线路匹配的点的距离
            const float p2d_dist = navi_util_seg_dist_calc(lng, lat, route_progress.lng, route_progress.lat);

            progress->dist = route_progress.dist;
            progress->dist2dest = navi->route_dist - route_progress.dist + p2d_dist;

            progress->match_point.lng = route_progress.lng;
            progress->match_point.lat = route_progress.lat;
            navi_route_wp_load(s_navi.is_reverse, route_progress.idx);
            navi_route_cp_load(route_progress.dist, s_navi.is_reverse);
        }
        return 1;
    }

    navi->fixed_cnt += 1;

    const double last_lng = (double)navi->last_fixed.longitude.value / (double)navi->last_fixed.longitude.scale;
    const double last_lat = (double)navi->last_fixed.latitude.value / (double)navi->last_fixed.latitude.scale;
    const double lng = (double)input->longitude.value / (double)input->longitude.scale;
    const double lat = (double)input->latitude.value / (double)input->latitude.scale;

    //距离变化过小时不计算
    const float dist = navi_util_seg_dist_calc(last_lng, last_lat, lng, lat);
    if (dist < 4.0f)
    {
        return 0;
    }

    const float course = navi_util_course_calc(last_lng, last_lat, lng, lat);

    NaviTurn navi_turn = { 0 };
    NaviClimbProgress climb_progress = { 0 };
    NaviSign navi_sign = { 0 };

    uint32_t idx = 0;

    int ret = navi_route_match(lng, lat, course, s_navi.is_reverse, &route_progress);

    if (ret == 0)
    {
        if (route_progress.dist < navi->route_dist - 20.0f)
        {
            progress->status = enumNAVI_STATUS_NORMAL;
            progress->dist = route_progress.dist;
            progress->dist2dest = navi->route_dist - route_progress.dist;

            if (navi_turn_match(route_progress.dist, s_navi.is_reverse, &navi_turn, &idx) == 0)
            {
                progress->turn.is_valid = true;
                progress->turn.type = navi_turn_type_calc(navi_turn.angle);
                progress->turn.dist = navi_turn.start_dist - route_progress.dist;
                progress->turn.idx = idx;
                if (s_navi.is_reverse == true)
                {
                    memcpy(progress->turn.wayname, navi_turn.wayname2, NAVI_TURN_WAYNAME_LEN);
                }
                else
                {
                    memcpy(progress->turn.wayname, navi_turn.wayname, NAVI_TURN_WAYNAME_LEN);
                }
                progress->turn.wayname[NAVI_TURN_WAYNAME_LEN] = '\0';
            }
            else
            {
                progress->turn.is_valid = false;
            }

            if (navi_climb_match(route_progress.dist, s_navi.is_reverse, &climb_progress) == 0)
            {
                progress->climb.is_valid = true;
                progress->climb.idx = climb_progress.idx;
                progress->climb.idx_next = climb_progress.idx_next;
                progress->climb.climb_remain = climb_progress.climb_remain;
                progress->climb.dist_remain = climb_progress.dist_remain;
                progress->climb.h_remain = climb_progress.h_remain;
                progress->climb.d_residual = climb_progress.d_residual;
                progress->climb.h_residual = climb_progress.h_residual;
                progress->climb.d2climb = climb_progress.d2climb;
            }
            else
            {
                progress->climb.is_valid = false;
                progress->climb.idx = climb_progress.idx;
                progress->climb.idx_next = climb_progress.idx_next;
                progress->climb.climb_remain = climb_progress.climb_remain;
                progress->climb.d2climb = climb_progress.d2climb;
            }

            if (navi_sign_match(route_progress.dist, s_navi.is_reverse, &navi_sign, &idx) == 0)
            {
                progress->sign.is_valid = true;
                progress->sign.idx = idx;
                progress->sign.dist = navi_sign.dist - route_progress.dist;
                memcpy(progress->sign.name, navi_sign.name, NAVI_SIGN_NAME_LEN);
                progress->sign.name[NAVI_SIGN_NAME_LEN] = '\0';
                navi_sign_load(s_navi.is_reverse, idx);
            }
            else
            {
                progress->sign.is_valid = false;
            }
        }
        else
        {
            progress->status = enumNAVI_STATUS_ARRIVED;
            progress->dist = route_progress.dist;
            progress->dist2dest = 0.0f;
        }

        progress->match_point.lng = route_progress.lng;
        progress->match_point.lat = route_progress.lat;
        navi_route_wp_load(s_navi.is_reverse, route_progress.idx);
        navi_route_cp_load(route_progress.dist, s_navi.is_reverse);

        s_navi.is_matched = true;
        s_navi.is_off_coursed = false;
        s_navi.match_dist = route_progress.dist;
    }
    else if (ret == 1)
    {
        progress->status = enumNAVI_STATUS_BACKWARD;
        progress->turn.is_valid = false;
        progress->climb.is_valid = false;
        progress->climb.idx = 0xFFFFFFFF;
        progress->climb.idx_next = 0xFFFFFFFF;
        progress->climb.climb_remain = 0xFFFFFFFF;
        progress->climb.d2climb = -1.0f;
        progress->sign.is_valid = false;

        progress->match_point.lng = route_progress.lng;
        progress->match_point.lat = route_progress.lat;
        navi_route_wp_load(s_navi.is_reverse, route_progress.idx);
        navi_route_cp_load(route_progress.dist, s_navi.is_reverse);

        s_navi.is_matched = true;
        s_navi.is_off_coursed = false;
        s_navi.match_dist = route_progress.dist;
    }
    else if (ret == 2)
    {
        progress->status = enumNAVI_STATUS_OFF_COURSE;
        progress->turn.is_valid = false;
        progress->climb.is_valid = false;
        progress->climb.idx = 0xFFFFFFFF;
        progress->climb.idx_next = 0xFFFFFFFF;
        progress->climb.climb_remain = 0xFFFFFFFF;
        progress->climb.d2climb = -1.0f;
        progress->sign.is_valid = false;

        if (navi_route_find(lng, lat, s_navi.is_reverse, &route_progress) == 0)
        {
            //当前经纬度到线路匹配的点的距离
            const float p2d_dist = navi_util_seg_dist_calc(lng, lat, route_progress.lng, route_progress.lat);

            progress->dist = route_progress.dist;
            progress->dist2dest = navi->route_dist - route_progress.dist + p2d_dist;

            progress->match_point.lng = route_progress.lng;
            progress->match_point.lat = route_progress.lat;
            navi_route_wp_load(s_navi.is_reverse, route_progress.idx);
            navi_route_cp_load(route_progress.dist, s_navi.is_reverse);

            if (s_navi.is_off_coursed == false)
            {
                s_navi.off_course_dist = route_progress.dist;
                s_navi.is_off_coursed = true;
            }

            float target_dist = 0.0f;
            const float forward_dist = 2000.0f;

            if (s_navi.is_matched == true)
            {
                target_dist = s_navi.match_dist + fabsf(route_progress.dist - s_navi.off_course_dist) + forward_dist;
            }
            else
            {
                target_dist = route_progress.dist + forward_dist;
            }

            if (target_dist > s_navi.route_dist)
            {
                target_dist = s_navi.route_dist;
            }

            if (navi_route_wp_find(target_dist, s_navi.is_reverse, &progress->target_point.lng, &progress->target_point.lat) != 0)
            {
                progress->target_point.lng = progress->match_point.lng;
                progress->target_point.lat = progress->match_point.lat;
            }
        }
    }
    else
    {
        //TODO err handler
    }

    memcpy(&navi->last_fixed.longitude, &input->longitude, sizeof(struct minmea_float));
    memcpy(&navi->last_fixed.latitude, &input->latitude, sizeof(struct minmea_float));

    return 0;
}

#if __NAVI_ROUTE_SIM
//导航线路模拟
#include "map_input.h"
#include "track_ride_port.h"

static int s_route_sim = false;

int navi_route_sim_get(void)
{
    return s_route_sim;
}

void navi_route_sim_set(int route_sim)
{
    s_route_sim = route_sim;
}

static void navi_route_sim(void)
{
	const NaviRouteWpSample* wp_sample = navi_route_wp_sample_get();

    if (false == navi_route_sim_get() || NULL == wp_sample)
    {
        return;
    }

	map_input_data_t* p_map_input = get_map_input();
	map_input_gps_t *p_navi_gps = get_navi_gps();
    static uint32_t sim_i = 0;
    float bearing = 0.0;
    struct minmea_float tmp_lat, tmp_lat1;
    struct minmea_float tmp_lon, tmp_lon1;
    int32_t lat, lon, lat1, lon1;
    static uint8_t i = 0;

    p_navi_gps->status = 1;
    p_navi_gps->lat.scale = 10000000;
    p_navi_gps->lon.scale = 10000000;
    p_navi_gps->lat.value = wp_sample->wpdc_buf[sim_i].lat * 10000000;
    p_navi_gps->lon.value = wp_sample->wpdc_buf[sim_i].lng * 10000000;

    if (0 < sim_i)
    {
        lat = wp_sample->wpdc_buf[sim_i - 1].lat * 10000000;
        lon = wp_sample->wpdc_buf[sim_i - 1].lng * 10000000;
        lat1 = wp_sample->wpdc_buf[sim_i].lat * 10000000;
        lon1 = wp_sample->wpdc_buf[sim_i].lng * 10000000;
        int_to_minmea(&tmp_lat, lat, MINMEA_SCALE);
        int_to_minmea(&tmp_lon, lon, MINMEA_SCALE);
        int_to_minmea(&tmp_lat1, lat1, MINMEA_SCALE);
        int_to_minmea(&tmp_lon1, lon1, MINMEA_SCALE);
        util_pos_bearing_get(&bearing, &tmp_lat, &tmp_lon, &tmp_lat1, &tmp_lon1);
        p_navi_gps->course = (int32_t)bearing;
        p_map_input->course = (int32_t)bearing;

        p_navi_gps->lat.value = lat + (lat1 - lat) / 3 * i;// + rand() % 10000;
        p_navi_gps->lon.value = lon + (lon1 - lon) / 3 * i;// + rand() % 10000;
    }

    const navi_calc_input_t c_navi_calc_input = {
        .longitude = {
            .value = p_navi_gps->lon.value,
            .scale = p_navi_gps->lon.scale,
        },
        .latitude = {
            .value = p_navi_gps->lat.value,
            .scale = p_navi_gps->lat.scale,
        },
    };
    const track_ride_input_t c_track_ride_input = {
        .longitude = {
            .value = p_navi_gps->lon.value,
            .scale = p_navi_gps->lon.scale,
        },
        .latitude = {
            .value = p_navi_gps->lat.value,
            .scale = p_navi_gps->lat.scale,
        },
        .altitude = 0,
    };
    navi_calc_input(&c_navi_calc_input);
    track_ride_input(&c_track_ride_input);
    p_map_input->gps_lat.value = p_navi_gps->lat.value;
    p_map_input->gps_lon.value = p_navi_gps->lon.value;
    p_map_input->gps_lat.scale = p_navi_gps->lat.scale;
    p_map_input->gps_lon.scale = p_navi_gps->lon.scale;
    p_map_input->status = 1;

    if (sim_i + 1 < wp_sample->len)
    {
        sim_i++;
    }
}
#endif

//更新导航计算和匹配
void navi_route_update(void)
{
    if (s_navi.state == enumNAVI_STATE_MATCHING)
    {
#if __NAVI_ROUTE_SIM
        navi_route_sim(); //线路模拟
#endif

        navi_route_update_exec(&s_navi, &s_navi_input, &s_navi_progress);

        //到达终点后自动停止导航
        if (s_navi_progress.status == enumNAVI_STATUS_ARRIVED)
        {
            stop_course();

            if (s_navi.cbs.route_arrive_cb != NULL)
            {
                s_navi.cbs.route_arrive_cb();
            }

#if RIDE_DATA_SIMULATE_ENABLE
            ride_data_simulator_stop();
#endif
        }
    }
}

//停止一条线路
void stop_course(void)
{
    if (s_navi.state == enumNAVI_STATE_MATCHING)
    {
        navi_route_match_stop();
        navi_turn_match_stop();
        navi_climb_match_stop();
        navi_sign_match_stop();
        s_navi.status = enumNAVI_PROCESS_NEVER;
        s_navi.state = enumNAVI_STATE_READY;

        if (s_navi.cbs.route_stop_cb != NULL)
        {
            s_navi.cbs.route_stop_cb();
        }
    }
}

//停止预览导航线路
void stop_preview(void)
{
    if (s_navi.state == enumNAVI_STATE_PREVIEWING)
    {
        navi_route_match_stop();
        navi_turn_match_stop();
        navi_climb_match_stop();
        navi_sign_match_stop();
        s_navi.status = enumNAVI_PROCESS_NEVER;
        s_navi.state = enumNAVI_STATE_READY;
    }
}

//返回起点
int back_to_start(void)
{
    int ret = route_use(TRACK_RIDE_PATH, true);

#if RIDE_DATA_SIMULATE_ENABLE
    ride_data_simulator_start(TRACK_RIDE_PATH);
#endif

    return ret;
}

//导航进行中获取导航结果
const navi_progress_t* navi_progress_get(void)
{
    if (s_navi.state == enumNAVI_STATE_MATCHING)
    {
        return &s_navi_progress;
    }
    else
    {
        return NULL;
    }
}

//导航进行中时获取附近的路点
const NaviRouteWpNearby* navi_route_wp_nearby_get(void)
{
    if (s_navi.state == enumNAVI_STATE_MATCHING)
    {
        return navi_route_wp_nearby_get_impl();
    }
    else
    {
        return NULL;
    }
}

//获取导航线路数据
const NaviRouteData* navi_route_data_get(void)
{
    if (s_navi.status == enumNAVI_PROCESS_SUCCESS)
    {
        return navi_route_data_get_impl();
    }
    else
    {
        return NULL;
    }
}

//获取导航线路采样
const NaviRouteWpSample* navi_route_wp_sample_get(void)
{
    if (s_navi.status == enumNAVI_PROCESS_SUCCESS)
    {
        return navi_route_wp_sample_get_impl();
    }
    else
    {
        return NULL;
    }
}

//获取爬坡总体数据
const NaviClimbEnsembleData* navi_climb_ensemble_data_get(void)
{
    if (s_navi.status == enumNAVI_PROCESS_SUCCESS)
    {
        return navi_climb_ensemble_data_get_impl(s_navi.is_reverse);
    }
    else
    {
        return NULL;
    }
}

//获取线路海拔采样
const NaviClimbSample* navi_climb_sample_get(void)
{
    if (s_navi.status == enumNAVI_PROCESS_SUCCESS)
    {
        return navi_climb_sample_get_impl();
    }
    else
    {
        return NULL;
    }
}

//获取指定爬坡信息
const NaviClimbData* navi_climb_data_get(uint32_t idx)
{
    if (s_navi.status == enumNAVI_PROCESS_SUCCESS)
    {
        return navi_climb_data_get_impl(idx, s_navi.is_reverse);
    }
    else
    {
        return NULL;
    }
}

//导航进行中时获取附近的爬坡点
const NaviRouteCpNearby* navi_route_cp_nearby_get(void)
{
    if (s_navi.state == enumNAVI_STATE_MATCHING)
    {
        return navi_route_cp_nearby_get_impl();
    }
    else
    {
        return NULL;
    }
}

//导航进行中时获取附近的标记点
const NaviSignNearby* navi_sign_nearby_get(void)
{
    if (s_navi.state == enumNAVI_STATE_MATCHING)
    {
        return navi_sign_nearby_get_impl();
    }
    else
    {
        return NULL;
    }
}

//获取导航线路转向数量
uint32_t navi_turn_num_get(void)
{
    if (s_navi.status == enumNAVI_PROCESS_SUCCESS)
    {
        return navi_turn_num_get_impl();
    }
    else
    {
        return 0;
    }
}

//获取指定索引的转向
const navi_turn_t* navi_turn_get(uint32_t idx)
{
    static NaviTurn navi_turn = { 0 };
    static navi_turn_t turn = { 0 };

    if (s_navi.status == enumNAVI_PROCESS_SUCCESS)
    {
        if (navi_turn_get_impl(idx, s_navi.is_reverse, &navi_turn) == 0)
        {
            turn.type = navi_turn_type_calc(navi_turn.angle);
            turn.dist = navi_turn.start_dist;
            turn.idx = idx;
            if (s_navi.is_reverse == true)
            {
                memcpy(turn.wayname, navi_turn.wayname2, NAVI_TURN_WAYNAME_LEN);
            }
            else
            {
                memcpy(turn.wayname, navi_turn.wayname, NAVI_TURN_WAYNAME_LEN);
            }
            turn.wayname[NAVI_TURN_WAYNAME_LEN] = '\0';
            return &turn;
        }
        else
        {
            return NULL;
        }
    }
    else
    {
        return NULL;
    }
}

//启用/预览一条导航线路时先调用，用于获取处理状态
void navi_route_process_enter(void)
{
    s_navi.is_processing = true;
}

//导航线路是否正在处理
uint8_t navi_route_is_processing(void)
{
    return s_navi.is_processing;
}

//检查指定路径的路书文件是否正在使用中
//true - 正在使用中
//false - 未使用
//注意：严禁删除正在使用中的文件！
uint8_t navi_is_using(const char *path)
{
    if (path == NULL)
    {
        return false;
    }

    if (s_navi.state == enumNAVI_STATE_PROCESSING || s_navi.state == enumNAVI_STATE_PREVIEWING || s_navi.state == enumNAVI_STATE_MATCHING)
    {
        if (strcmp(s_navi.fpath, path) == 0)
        {
            return true;
        }
    }

    return false;
}

//检查指定名称的路书文件是否正在使用中
//true - 正在使用中
//false - 未使用
//注意：严禁删除正在使用中的文件！
uint8_t navi_is_using2(const char *name)
{
    if (name == NULL)
    {
        return false;
    }

    if (s_navi.state == enumNAVI_STATE_PROCESSING || s_navi.state == enumNAVI_STATE_PREVIEWING || s_navi.state == enumNAVI_STATE_MATCHING)
    {
        if (strstr(s_navi.fpath, name) != NULL)
        {
            return true;
        }
    }

    return false;
}

void navi_ctrl_mode_set(int ctrl_mode)
{
    map_input_data_t *p_map_input = get_map_input();
    p_map_input->ctrl_mode = ctrl_mode;
}

int navi_ctrl_mode_get(void)
{
    map_input_data_t *p_map_input = get_map_input();
    return p_map_input->ctrl_mode;
}
