/** 
 * @*************************************** Copyright (c) ***************************************
 * @                              <PERSON>han <PERSON>wu Technology Co., Ltd
 * @*********************************************************************************************
 * @Author: Sun
 * @Date: 2024-07-09 10:01:30
 * @LastEditTime: 2024-07-09 17:02:03
 * @LastEditors: Sun
 * @FilePath: \iGS630_App\Application\App\radio\sensor\sensor_ble\hrm\ble_hrm_cfg.c
 * @Description: 
 * @*********************************************************************************************
 */
#include "ble_hrm_cfg.h"
#include "qw_sensor_scan.h"
#include "qw_sensor_data.h"
#include "ble_central.h"
#include "cfg_header_def.h"

BLE_HRS_C_DEF(m_hrs_c);                                             /**< Structure used to identify the heart rate client module. */
static uint16_t m_conn_handle_hrs_c  = BLE_CONN_HANDLE_INVALID;  /**< Connection handle for the HRS central application */

/**@brief Handles events coming from the Heart Rate central module. 
 */
static void hrs_c_evt_handler(ble_hrs_c_t * p_hrs_c, ble_hrs_c_evt_t * p_hrs_c_evt)
{
    sensor_search_infor_t       sensor_search_infor;
    sensor_original_data_t      *p_sensor_original_data = sensor_original_data_get();
    sensor_module_evt_handler   evt_handler             = sensor_module_evt_handler_get();
    sensor_saved_work_t         *p_sensor_saved         = NULL;
    sensor_module_param_input_t *p_param                = sensor_module_param_input_get();
    sensor_work_state_t         sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    int8_t                      index                   = -1;
    ret_code_t                  err_code                = 0;
    
    sensor_connect_infor_t      sensor_connect;
    bool found = sensor_connect_infor_get(SENSOR_TYPE_HRM, &sensor_connect);

    switch (p_hrs_c_evt->evt_type)
    {
        case BLE_HRS_C_EVT_DISCOVERY_COMPLETE:
        {
            err_code = ble_hrs_c_handles_assign(p_hrs_c,
                                                p_hrs_c_evt->conn_handle,
                                                &p_hrs_c_evt->params.peer_db);
            APP_ERROR_CHECK(err_code);

            // Heart rate service discovered. Enable notification of Heart Rate Measurement.
            err_code = ble_hrs_c_hrm_notif_enable(p_hrs_c);
            APP_ERROR_CHECK(err_code);
            
            if (SENSOR_CONNECT_STATE_CONNECTED == sensor_connect.state)
            {
                ble_hrs_conn_handle_set(p_hrs_c_evt->conn_handle); 
                ble_gap_connect_release();
                
                sensor_connect.radio_type = SENSOR_RADIO_TYPE_BLE;
                sensor_connect.state = SENSOR_CONNECT_STATE_DISCOVERY;
                sensor_connect_infor_set(SENSOR_TYPE_HRM, &sensor_connect);

                memset (&sensor_search_infor, 0x00, sizeof(sensor_search_infor_t));
                memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
                sensor_ble_search_infor_get(sensor_connect.sensor_id.ble_mac_addr, &sensor_search_infor);
                
                sensor_search_infor.radio_type = SENSOR_RADIO_TYPE_BLE;
                sensor_search_infor.sensor_type = SENSOR_TYPE_HRM;

                if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
                {
                    if (SENSOR_WORK_STATE_FORBIDDEN == sensor_work_state)
                    {
                        sensor_infor_t sensor_infor = {0};
                        sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                        sensor_disconnect(&sensor_infor);
                    }
                    else
                    {
                        p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                        p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
                        cfg_mark_update(enum_cfg_ant_ble_dev);
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }
                else
                {
                    sensor_search_infor_del(&sensor_search_infor); 
                    if(sensor_disconnect_item_check(&sensor_search_infor))
                    {
                        sensor_disconnect_info_remove(&sensor_search_infor);
                        sensor_infor_t sensor_infor = {0};
                        sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                        sensor_disconnect(&sensor_infor);
                        return;
                    }
                    sensor_saved_work_infor_add(&sensor_search_infor);
                }

                if (NULL != evt_handler)
                {
                    evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_BLE, TRUE);
                    evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, sensor_search_infor.sensor_type, sensor_search_infor.radio_type, 0);
                    evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_connect.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_BLE, TRUE);
                }

                sensor_ant_scan_start();
            }
            if (found && SENSOR_CONNECT_STATE_DISCOVERY == sensor_connect.state)
            {
                
                if (NULL != evt_handler)
                {
                    evt_handler(EVENT_SENSOR_DATA_UPDATE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_BLE, 0);
                }
            }
        }
        break; // BLE_HRS_C_EVT_DISCOVERY_COMPLETE

        case BLE_HRS_C_EVT_HRM_NOTIFICATION:
        { 
            p_sensor_original_data->hrData.heart_rate = p_hrs_c_evt->params.hrm.hr_value;         					        
            NRF_LOG_INFO("Heart Rate = %d", p_hrs_c_evt->params.hrm.hr_value);

            memset (&sensor_search_infor, 0x00, sizeof(sensor_search_infor_t));
            memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
            sensor_ble_search_infor_get(sensor_connect.sensor_id.ble_mac_addr, &sensor_search_infor);
            
            sensor_search_infor.radio_type = SENSOR_RADIO_TYPE_BLE;
            sensor_search_infor.sensor_type = SENSOR_TYPE_HRM;

            if (sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
            {
                if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
                {
                    p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                    p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
                }
                sensor_saved_work_infor_release_write_lock(index);
            }
            if (NULL != evt_handler)
            {
                evt_handler(EVENT_SENSOR_DATA_UPDATE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_BLE, 0);
            }
        } 
        break; // BLE_HRS_C_EVT_HRM_NOTIFICATION

        default:
            // No implementation needed.
            break;
    }
}
////////////////////////////////////////////////////////////////////
void ble_central_hrs_disc_handler(ble_db_discovery_evt_t * p_evt)
{
    ble_hrs_on_db_disc_evt(&m_hrs_c, p_evt);
}

uint16_t ble_hrs_conn_handle_get()
{
    return m_conn_handle_hrs_c;
}
void ble_hrs_conn_handle_set(uint16_t handle)
{
    m_conn_handle_hrs_c = handle;
}


////////////////////////////////////////////////////////////////////
/**@brief Heart Rate Collector initialization.
 */
void hrs_c_init(void)
{
    ret_code_t       err_code;
    ble_hrs_c_init_t hrs_c_init_obj;

    hrs_c_init_obj.evt_handler = hrs_c_evt_handler;

    err_code = ble_hrs_c_init(&m_hrs_c, &hrs_c_init_obj);
    APP_ERROR_CHECK(err_code);
}
