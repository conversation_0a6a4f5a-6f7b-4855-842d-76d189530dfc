{
    "terminal.integrated.profiles.windows": {

        "PowerShell": {
            "source": "PowerShell",
            "icon": "terminal-powershell"
        },
        "Command Prompt": {
            "path": [
                "${env:windir}\\Sysnative\\cmd.exe",
                "${env:windir}\\System32\\cmd.exe"
            ],
            "args": [],
            "icon": "terminal-cmd"
        },
        "GitBash": {
            "path":"C:\\Program Files\\Git\\bin\\bash.exe"
        }
    },
    "files.encoding":"utf8",
    "terminal.integrated.defaultProfile.windows": "GitBash",
    "remote.SSH.remotePlatform": {
        "yanxuqiang.tpddns.cn": "linux"
    },
    "editor.defaultFormatter": "xaver.clang-format",
    "editor.formatOnType": false,
    "security.workspace.trust.untrustedFiles": "open",
    "C_Cpp.errorSquiggles": "disabled",
    
    "search.exclude": {
        "**/node_modules": true,
        "**/bower_components": true,
        "**/*.code-search": true,
        "**/*.map": true,
        "**/*.o": true,
        "**/*.mak": true,
        "**/*.pp": true,
        "**/*.ppp": true,
        "**/*.txt": true,
        "**/tavor/":true
    },
    "files.exclude": {
        "**/.git": false,
        "**/.svn": false,
        "**/.hg": true,
        "**/CVS": true,
        "**/.DS_Store": true,
        "**/Thumbs.db": true,
        "**/build/": true,
        "**/lv_phone/": true,
        "**/out/": true,
        "**/product/cranem_modem/": true,
        "**/product/cranem_modem/atatest/": true,
        "**/product/pc_simulator/": true,
        "**/releasepack/": true,
        "**/tools/": true
    },/*
    "files.associations": {
        "lv_watch.h": "c",
        "xun_features.h": "c",
        "demo_test.h": "c",
        "array": "c",
        "bitset": "c",
        "string_view": "c",
        "rope": "c",
        "slist": "c",
        "initializer_list": "c",
        "regex": "c",
        "utility": "c",
        "valarray": "c",
        "__hash_table": "c",
        "__split_buffer": "c",
        "__tree": "c",
        "deque": "c",
        "iterator": "c",
        "list": "c",
        "map": "c",
        "queue": "c",
        "random": "c",
        "set": "c",
        "stack": "c",
        "string": "c",
        "unordered_map": "c",
        "unordered_set": "c",
        "vector": "c",
        "algorithm": "c",
        "xun_mem_pool_ctrl.h": "c",
        "xun_mem_debug_record.h": "c",
        "__bit_reference": "c",
        "chrono": "c",
        "xun_contact.h": "c",
        "xun_gui_mem.h": "c",
        "xun_popup_windows.h": "c"
    },*/
    
    "editor.tokenColorCustomizations": {
        "types": "#00a2ff",
        "functions": "#4a8dd4", //函数名
        "variables": "#59c97e", //变量
        "numbers": "#d43939", //数字
        "keywords": "#3b48f7", //关键字
        "strings": "#ff8c2e", //字符串
    },
    "editor.semanticTokenColorCustomizations": {
        "enabled": true,
        "rules": {
            "enumMember": { //枚举
                "foreground": "#e60101",
                "fontStyle": "italic"
            },
            "macro": { //宏
                "foreground": "#e60101",
                //"fontStyle": "bold"
            },
            "function": { //函数
                "foreground": "#3f77cb",
                "fontStyle": "bold"
            },
            "variable.global": { //全局变量
                "foreground": "#c040ba",
                "fontStyle": "italic"
            },
            "variable.local": { //局部变量
                "foreground": "#32acb4",
                //"fontStyle": "bold"
            },
            "parameter": { //参数
                "foreground": "#32acb4",
                //"fontStyle": "bold"
            },
            "property": { //属性
                "foreground": "#2fbd2f",
                "fontStyle": "bold"
            },
            "type": { //类型
                "foreground": "#b5e4e7",
                //"fontStyle": "bold"
            },
        }
    },
    "editor.formatOnPaste": false,
    "editor.find.autoFindInSelection": "multiline",
    "editor.renderWhitespace": "none",
    "editor.renderControlCharacters": false,
    "workbench.colorCustomizations": {
        "editor.selectionBackground": "#d1d1c6b0",
        "editor.selectionHighlightBackground": "#c5293e8e",
        //目标树缩进参考线颜色
        "tree.indentGuidesStroke": "#942ba1",
        //编辑器缩进参考线颜色
        "editorIndentGuide.activeBackground": "#86ff67",
        //匹配括号的边框色
        "editorBracketMatch.border": "#33ff0056",
        //匹配括号的背景色
        "editorBracketMatch.background": "#33ff0063",
    },
    "files.associations": {
        "*.cpp": "cpp",
        "*.embeddedhtml": "html",
        "lv_watch.h": "c",
        "xun_video_voice_layout.h": "c",
        "lv_watch_obj.h": "c",
        "lv_lang.h": "c",
        "ui_textid.h": "c",
        "xun_app_list_layout.h": "c",
        "xun_features.h": "c",
        "xun_layout_includes.h": "c",
        "xun_app_at_cmds.h": "c",
        "ui_inter_msg_interface.h": "c",
        "factory_mode.h": "c",
        "xun_lv_test_fs.h": "c",
        "__hash_table": "c",
        "__split_buffer": "c",
        "__tree": "c",
        "array": "c",
        "bitset": "c",
        "deque": "c",
        "initializer_list": "c",
        "iterator": "c",
        "list": "c",
        "map": "c",
        "queue": "c",
        "random": "c",
        "regex": "c",
        "set": "c",
        "stack": "c",
        "string": "c",
        "string_view": "c",
        "unordered_map": "c",
        "unordered_set": "c",
        "utility": "c",
        "valarray": "c",
        "vector": "c",
        "rope": "c",
        "slist": "c",
        "__bit_reference": "c",
        "__functional_base": "c",
        "__node_handle": "c",
        "algorithm": "c",
        "atomic": "c",
        "chrono": "c",
        "__memory": "c",
        "filesystem": "c",
        "functional": "c",
        "limits": "c",
        "locale": "c",
        "memory": "c",
        "optional": "c",
        "ratio": "c",
        "scoped_allocator": "c",
        "system_error": "c",
        "tuple": "c",
        "type_traits": "c",
        "*.inc": "c",
        "istream": "c",
        "ostream": "c",
        "lv_debug.h": "c",
        "gc9a01_3line2data.h": "c",
        "__locale": "c",
        "__string": "c",
        "lv_barcode.h": "c",
        "hal_ble.h": "c",
        "hal_wlan.h": "c",
        "hal_http.h": "c",
        "lang_en.h": "c",
        "cstddef": "c",
        "ios": "c",
        "new": "c",
        "condition_variable": "c",
        "xun_net_framework.h": "c",
        "xun_net_send_list.h": "c",
        "xun_setting_layout.h": "c",
        "setting.h": "c",
        "xun_setting_alert_type.h": "c",
        "xun_setting_guard_listen.h": "c",
        "xun_list_theme.h": "c",
        "watch_slidepage.h": "c",
        "*.def": "c",
        "xiosbase": "c",
        "iosfwd": "c",
        "xlocbuf": "c",
        "mmidropdownwin_export.h": "c",
        "mmk_type.h": "c",
        "quickjs.h": "c",
        "quickjs-libc.h": "c",
        "touchgfx_js.h": "c",
        "touchgfx_js_data_servers.h": "c",
        "stdint.h": "c",
        "string.h": "c",
        "touchgfx_js_api.h": "c",
        "js_container.h": "c",
        "js_app.h": "c",
        "js_box.h": "c",
        "js_img.h": "c",
        "js_textarea.h": "c",
        "js_data_servers.h": "c",
        "quickjs_user.h": "c",
        "lv_fs.h": "c",
        "lv_mem.h": "c",
        "cutils.h": "c",
        "unistd_pc.h": "c",
        "future": "cpp",
        "sports_data.h": "c",
        "sports_data_show.h": "c",
        "stdlib.h": "c",
        "sys_time.h": "c",
        "igs_global.h": "c",
        "*.tpp": "cpp",
        "gomorelib.h": "c",
        "gomorelibstruct.h": "c",
        "igs_dev_config.h": "c",
        "mytime.h": "c",
        "nrf_dfu_serial.h": "c",
        "app_scheduler.h": "c",
        "sqlite_service.h": "c",
        "qw_fs.h": "c",
        "algo_service_altitude.h": "c",
        "multicoregps.h": "c",
        "subscribe_service.h": "c",
        "subscribe_data_protocol.h": "c",
        "sports_data_callback.h": "c",
        "lps28dfw_reg.h": "c",
        "tx_list.h": "c",
        "qwos.h": "c",
        "memory_manager.h": "c",
        "ble_data_inf.h": "c",
        "cmath": "cpp",
        "battery_srv.h": "c",
        "*.rh": "cpp",
        "basic_app.h": "c",
        "cfg_header_def.h": "c",
        "service_weather.h": "c",
        "gui_event_service.h": "c",
        "remind_response_app.h": "c",
        "task_thread_helper.h": "c",
        "qw_system_params.h": "c",
        "alg_grade.h": "c",
        "types.h": "c"
    },
    "[cpp]": {
        "editor.defaultFormatter": "ms-vscode.cpptools"
    },
    "[c]": {
        "editor.defaultFormatter": "ms-vscode.cpptools"
    }
}