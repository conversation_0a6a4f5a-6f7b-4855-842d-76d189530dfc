/****************************************Copyright (c)****************************************
* <PERSON><PERSON>wu Technology Co., Ltd
*
*---------------------------------------File Info--------------------------------------------
* File path :
* Created by : Lxin
* LastEditors: Lxin
* Descriptions :
*--------------------------------------------------------------------------------------------
* History :
* 2023-04-28 16:06:01: Lxin 原始版本
*
*********************************************************************************************/

#ifndef __QW_PAGE_INDICATOR_H
#define __QW_PAGE_INDICATOR_H

#include "../module/gui/GUICtrl/touchgfx.h"

class TiledImageArc :
    public TiledImage
{
public:
    virtual void draw(const Rect& invalidatedArea) const;

    // Support multiple unselected bitmaps (used cyclically)
    void setBitmapList(const Bitmap* list, uint8_t count);
    void clearBitmapList();
    bool hasBitmapList() const;
    Bitmap getFirstBitmapFromList() const;

    // 新增：设置动态位图选择的回调
    void setDynamicBitmapSelector(const Bitmap* normalList, uint8_t normalCount,
                                  const Bitmap& highlighted, uint8_t currentPageIndex);

public:
    static const int16_t INDICATOR_SPACE = 6;

private:
    const Bitmap* bitmapList = nullptr;
    uint8_t bitmapListCount = 0;

    // 动态位图选择
    const Bitmap* dynamicNormalList = nullptr;
    uint8_t dynamicNormalCount = 0;
    Bitmap dynamicHighlighted;
    uint8_t dynamicCurrentPage = 0;
};

class QwPageIndicator : 
    public Container
{
public:
    QwPageIndicator();
	virtual ~QwPageIndicator() {};

    void setNumberOfPages(uint8_t size);
    // Original API: one normal + one highlighted
    void setBitmaps(const Bitmap& normalPage, const Bitmap& highlightedPage);
    // New API: multiple normals + one highlighted; normals are applied cyclically
    void setBitmaps(const Bitmap* normalPages, uint8_t normalCount, const Bitmap& highlightedPage);

    void goUp();
    void goDown();
    void setCurrentPage(uint8_t page);
    uint8_t getNumberOfPages() const;
    uint8_t getCurrentPage() const;

private:
    TiledImageArc unselectedPages;
    TiledImageArc selectedPage;  // 保留但不使用，避免破坏现有接口
    uint8_t numberOfPages;
    uint8_t currentPage;

    // 新增：存储位图用于动态切换
    Bitmap normalBitmap;
    Bitmap highlightedBitmap;
    const Bitmap* normalBitmapList;
    uint8_t normalBitmapCount;
};

#endif //__QW_PAGE_INDICATOR_H