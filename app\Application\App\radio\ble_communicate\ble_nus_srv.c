/***************************************Copyright (c)****************************************/
//                              <PERSON>han Qiwu Technology Co., Ltd
//
//---------------------------------------File Info--------------------------------------------
// File name         : ble_nus_srv.c
// Created by        : jiangzhen
// Descriptions      : 定义NUS服务相关的数据接收与发送接口
//--------------------------------------------------------------------------------------------
// History           :
// 2020-05-13        :原始版本
/*********************************************************************************************/

//通道0，为控制命令传输通道
//通道1，回复确认及特殊状态提示通道
//通道2，文件类数据上行通道，如骑行活动上传、实时追踪等
//通道3，文件类数据下行通道，如线路下发、AGPS下发等

#include "ble_nus.h"
#include "ble_nus_srv.h"
#include "ble_peripheral.h"
#include "common.pb.h"
#include "back.pb.h"
#include "ble_cmd_common.h"
#include "cycling_data.pb.h"
#include "app_timer.h"
#include "nrf_queue.h"
#include "ble_interflow_single.h"
#include "ble_ant_module.h"
#include "watch_health_data.pb.h"

static uint8_t data_tx_ch0[DATA_LENGTH_CH1 + ONE_FRAME_DATA_LENGTH_MAX_CH0] = {0};

static uint8_t data_tx_ch2[DATA_LENGTH_CH1 + 4+ ONE_FRAME_DATA_LENGTH_MAX_CH2_TX] = {0};

static uint8_t data_tx_ch3[DATA_LENGTH_CH1 + ONE_FRAME_DATA_LENGTH_MAX] = {0};

static uint16_t data_length_tx_ch0 = 0;
static uint16_t data_length_tx_ch2 = 0;
static uint16_t data_length_tx_ch3 = 0;

static uint8_t busy_flag_tx_ch0 = false;
static uint8_t busy_flag_tx_ch2 = false;
static uint8_t busy_flag_tx_ch3 = false;

uint8_t data_tx_ch1_ch0_flag = false;
uint8_t data_tx_ch1_ch2_flag = false;
uint8_t data_tx_ch1_ch3_flag = false;

uint8_t data_tx_ch1_ch0[DATA_LENGTH_CH1] = {0};
uint8_t data_tx_ch1_ch2[DATA_LENGTH_CH1] = {0};
uint8_t data_tx_ch1_ch3[DATA_LENGTH_CH1] = {0};

#undef BLE_NUS_MAX_DATA_LEN
extern uint8_t dynamic_mtu_size;
#define 	BLE_NUS_MAX_DATA_LEN 	dynamic_mtu_size

#define NOTICE_STATUS_NOTIFY_QUEUE_SIZE		16

NRF_QUEUE_DEF(ble_status_cmd_st,
              m_notice_and_status_notify_mailbox,
			  NOTICE_STATUS_NOTIFY_QUEUE_SIZE,
			  NRF_QUEUE_MODE_NO_OVERFLOW);

extern void ble_data_deal_notify(void);

uint8_t ble_nus_channel_sta = 0;
void ble_nus_channel_status_set(enum_channel_status sta)
{
	ble_nus_channel_sta |= sta;
	if(ble_nus_channel_sta == (enum_CHANNEL0|enum_CHANNEL1|enum_CHANNEL2|enum_CHANNEL3)){
		ble_data_deal_notify();
	}
}

void ble_nus_channel_status_clear(enum_channel_status sta)
{
	ble_nus_channel_sta &= ~sta;
if(ble_nus_channel_sta == 0){
		ble_data_deal_notify();
	}
}

bool ble_nus_channel_all_is_start(void)
{
	return (ble_nus_channel_sta == (enum_CHANNEL0|enum_CHANNEL1|enum_CHANNEL2|enum_CHANNEL3));
}

void ble_nus_channel_status_clear_all(void)
{
	ble_nus_channel_sta = 0x00;
}

static ble_nus_channel_uuid_t ble_nus_data_tx_get_channel(uint8_t const *data)
{
	ble_nus_channel_uuid_t channel_uuid = (ble_nus_channel_uuid_t)0;
	if(enum_BLE_NOTICE_CMD != data[0]){
		switch(data[1]){
			case service_type_index_enum_SERVICE_TYPE_INDEX_CYCLING_DATA:
            case service_type_index_enum_SERVICE_TYPE_INDEX_LOG:
                channel_uuid = BLE_NUS_CH2_UUID;
			  	break;
			case service_type_index_enum_SERVICE_TYPE_INDEX_BACK:
			case service_type_index_enum_SERVICE_TYPE_INDEX_ROUTE_PLAN:
			case service_type_index_enum_SERVICE_TYPE_INDEX_TRAINING:
			case service_type_index_enum_SERVICE_TYPE_INDEX_LANGUAGE:
			case service_type_index_enum_SERVICE_TYPE_INDEX_THEME:
			case service_type_index_enum_SERVICE_TYPE_INDEX_MAP_NEW:
			case service_type_index_enum_SERVICE_TYPE_INDEX_FILE_OPERATION:
			case service_type_index_enum_SERVICE_TYPE_INDEX_ROUTE_BOOK:
            case service_type_index_enum_SERVICE_TYPE_INDEX_STAGE:
            case service_type_index_enum_SERVICE_TYPE_INDEX_TRAINING_PLAN:
            case service_type_index_enum_SERVICE_TYPE_INDEX_ALG_GOMORE:
                channel_uuid = BLE_NUS_CH3_UUID;
			break;
		  default:
                if ((data[1] == service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_HEALTH) &&
                    ((data[4] == WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_HISTORY_FILE_GET) || 
                     (data[4] == WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_HISTORY_LIST_GET)))
                {
                    channel_uuid = BLE_NUS_CH2_UUID;
                }
                else
                {
			  	    channel_uuid = BLE_NUS_CH0_UUID;
                }
			break;
		}
	}else{
		channel_uuid = BLE_NUS_CH1_UUID;
	}
	return channel_uuid;
}

static void notice_and_status_queue_notify_app_push(const void *data,bool send_now)
{
	nrf_queue_push(&m_notice_and_status_notify_mailbox, data);
	if(send_now){
		ble_data_deal_notify();
	}
}

void notice_and_status_queue_notify_app_pop(void)
{
	uint8_t cnt = 0;
	while(!nrf_queue_is_empty(&m_notice_and_status_notify_mailbox) && (cnt < (NOTICE_STATUS_NOTIFY_QUEUE_SIZE>>2))){
		ble_status_cmd_st item = {0};
		if (NRF_SUCCESS == nrf_queue_pop(&m_notice_and_status_notify_mailbox, &item))
		{
			uint16_t data_length = DATA_LENGTH_CH1;
			ble_nus_channel_uuid_t channel_uuid = ble_nus_data_tx_get_channel((uint8_t*)&item);

			if(NRF_SUCCESS != nus_data_send((uint8_t*)&item, &data_length, (BLE_APP_NUS_CHANNLE_UUID)channel_uuid))
            // if(NRF_SUCCESS != ble_platform_nus_data_send((uint8_t*)&item, &data_length, channel_uuid))
			{
				notice_and_status_queue_notify_app_push((void *)&item,false);
				break;
			}else{
				cnt ++;
			}
		}
	}
}

void notice_and_status_queue_clear(void)
{
	nrf_queue_reset(&m_notice_and_status_notify_mailbox);
}
//-------------------------------------------------------------------------------------------
// Function Name : ble_nus_data_rx_ch0
// Purpose       : 通道0 接收数据处理函数
// Param[in]     : uint8_t const *data  
//                 uint16_t const length      
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-13
//-------------------------------------------------------------------------------------------
void ble_nus_data_rx_ch0(uint8_t const *data, uint16_t const length)
{
	ble_interflow_single_insert_packet(SINGLE_CHANNEL_0,data,length);
	ble_data_deal_notify();
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_nus_data_rx_ch1
// Purpose       : 通道1 接收数据处理函数
// Param[in]     : uint8_t const *data    
//                 uint16_t const length  
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-13
//-------------------------------------------------------------------------------------------
void ble_nus_data_rx_ch1(uint8_t const *data, uint16_t const length)
{
    ble_interflow_single_insert_packet(SINGLE_CHANNEL_1,data,length);
    ble_data_deal_notify();
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_nus_data_rx_ch3
// Purpose       : 通道2 接收数据处理函数
// Param[in]     : uint8_t const *data    
//                 uint16_t const length  
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-13
//-------------------------------------------------------------------------------------------
void ble_nus_data_rx_ch2(uint8_t const *data, uint16_t const length)
{
	ble_interflow_single_insert_packet(SINGLE_CHANNEL_2,data,length);
	ble_data_deal_notify();
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_nus_data_rx_ch3
// Purpose       : 通道3 接收数据处理函数
// Param[in]     : uint8_t const *data    
//                 uint16_t const length  
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-13
//-------------------------------------------------------------------------------------------
void ble_nus_data_rx_ch3(uint8_t const *data, uint16_t const length)
{	
	ble_interflow_single_insert_packet(SINGLE_CHANNEL_3,data,length);
	ble_data_deal_notify();
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_nus_data_tx_ch0
// Purpose       : 通道0 发送数据处理函数
// Param[in]     : None
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-13
//-------------------------------------------------------------------------------------------
ret_code_t ble_nus_data_tx_ch0(void)
{
	if (true == busy_flag_tx_ch0)
	{
		return NRF_ERROR_BUSY;
	}
	else
	{
		busy_flag_tx_ch0 = true;
		return NRF_SUCCESS;
	}
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_nus_data_tx_ch0
// Purpose       : 通道1 发送数据处理函数
// Param[in]     : None
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-13
//-------------------------------------------------------------------------------------------
void ble_nus_data_tx_protocol_header(uint8_t const *data)
{
	if(!ble_nus_channel_all_is_start())return;
	
	ret_code_t err_code = NRF_SUCCESS;
	uint16_t data_length = 0;
    uint8_t cmd_type = data[0];
    ble_nus_channel_uuid_t channel_uuid = ble_nus_data_tx_get_channel(data);

    if(enum_BLE_END_CMD != cmd_type){
		data_length = DATA_LENGTH_CH1;
		err_code = nus_data_send(data, &data_length, (BLE_APP_NUS_CHANNLE_UUID)channel_uuid);
        // err_code = ble_platform_nus_data_send(data, &data_length, channel_uuid);
        if (NRF_SUCCESS != err_code){
        	notice_and_status_queue_notify_app_push(data,true);
        }
    }else{
		switch(channel_uuid)
		{
			case BLE_NUS_CH0_UUID:
				memcpy (data_tx_ch1_ch0, data, DATA_LENGTH_CH1);
				data_tx_ch1_ch0_flag = true;
				break;
			case BLE_NUS_CH2_UUID:
				memcpy (data_tx_ch1_ch2, data, DATA_LENGTH_CH1);
				data_tx_ch1_ch2_flag = true;
				break;
			case BLE_NUS_CH3_UUID:
				memcpy (data_tx_ch1_ch3, data, DATA_LENGTH_CH1);
				data_tx_ch1_ch3_flag = true;
				break;
			default:
				break;
		}
    }
}
//-------------------------------------------------------------------------------------------
// Function Name : ble_nus_data_tx_ch0
// Purpose       : 通道2 发送数据处理函数
// Param[in]     : None
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-13
//-------------------------------------------------------------------------------------------
int ble_nus_data_tx_ch2(void)
{
	if (true == busy_flag_tx_ch2)
	{
		return NRF_ERROR_BUSY;
	}
	else
	{
		busy_flag_tx_ch2 = true;
        return NRF_SUCCESS;
	}
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_nus_data_tx_ch3
// Purpose       : 通道3 发送数据处理函数
// Param[in]     : None
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-13
//-------------------------------------------------------------------------------------------
int ble_nus_data_tx_ch3(void)
{	
	if (true == busy_flag_tx_ch3)
	{
		return NRF_ERROR_BUSY;
	}
	else
	{
		busy_flag_tx_ch3 = true;
        return NRF_SUCCESS;
	}
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_data_var_tx_get
// Purpose       : 根据通道，获取发送数据存储的数据
// Param[in]     : uint8_t *data                            
//                 uint16_t *length                         
//                 ble_nus_channel_uuid_t nus_channel_uuid  
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-14
//-------------------------------------------------------------------------------------------
ret_code_t _ble_data_var_tx_get(uint8_t **data, uint16_t **length, ble_nus_channel_uuid_t nus_channel_uuid)
{	
	ret_code_t            err_code = NRF_SUCCESS;
	
	switch (nus_channel_uuid)
	{
		case BLE_NUS_CH0_UUID:
			if (true == busy_flag_tx_ch0)
			{
				err_code = NRF_ERROR_BUSY;
			}
			else
			{
				*data = &data_tx_ch0[DATA_LENGTH_CH1];
				*length = &data_length_tx_ch0;
			}
			break;		
		case BLE_NUS_CH2_UUID:
			if (true == busy_flag_tx_ch2)
			{
				err_code = NRF_ERROR_BUSY;
			}
			else
			{
				*data = &data_tx_ch2[DATA_LENGTH_CH1];
				*length = &data_length_tx_ch2;
			}
			break;
		case BLE_NUS_CH3_UUID:
			if (true == busy_flag_tx_ch3)
			{
				err_code = NRF_ERROR_BUSY;
			}
			else
			{
				*data = &data_tx_ch3[DATA_LENGTH_CH1];
				*length = &data_length_tx_ch3;
			}
			break;	
			
		case BLE_NUS_CH1_UUID:	
		default:
			err_code = NRF_ERROR_INVALID_PARAM;
			break;
	}	

	return err_code;
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_data_tx_ch0
// Purpose       : 发送通道0数据
// Param[in]     : void  
// Param[out]    : None
// Return type   : static
// Comment       : 2020-07-18
//-------------------------------------------------------------------------------------------
static void ble_data_tx_ch0(bool is_clear_buf)
{
	static uint16_t offset = 0;	
	uint16_t data_length = 0;
	static uint8_t flag = 0;
	ret_code_t err_code = NRF_SUCCESS;
	
	if(is_clear_buf){
		offset = 0;
		flag = 0;
		busy_flag_tx_ch0 = false;
		memset (data_tx_ch0, 0, data_length_tx_ch0);
		data_length_tx_ch0 = 0;
		return;
	}

	if(offset == 0 && 0 == flag){
		if(data_tx_ch1_ch0_flag){
			data_tx_ch1_ch0_flag = false;
			memcpy(data_tx_ch0,data_tx_ch1_ch0,DATA_LENGTH_CH1);
			data_length_tx_ch0 += DATA_LENGTH_CH1;
		}else{
			data_length_tx_ch0 += DATA_LENGTH_CH1;
			offset = DATA_LENGTH_CH1;
		}
		flag = 1;
	}

	if (offset < data_length_tx_ch0)
	{
		if (BLE_NUS_MAX_DATA_LEN >= (data_length_tx_ch0 - offset)){
			data_length = data_length_tx_ch0 - offset;
			err_code = nus_data_send(&data_tx_ch0[offset], &data_length, (BLE_APP_NUS_CHANNLE_UUID)BLE_NUS_CH0_UUID);
            // err_code = ble_platform_nus_data_send(&data_tx_ch0[offset], &data_length, BLE_NUS_CH0_UUID);
		}
		else{
			data_length = BLE_NUS_MAX_DATA_LEN;
			err_code = nus_data_send(&data_tx_ch0[offset], &data_length, (BLE_APP_NUS_CHANNLE_UUID)BLE_NUS_CH0_UUID);			
            // err_code = ble_platform_nus_data_send(&data_tx_ch0[offset], &data_length, BLE_NUS_CH0_UUID);			
		}

		if (NRF_SUCCESS == err_code){
			offset += data_length;
		}
	}

	if (offset >= data_length_tx_ch0){
		offset = 0;
		flag = 0;
		busy_flag_tx_ch0 = false;
		memset (data_tx_ch0, 0, data_length_tx_ch0);
		data_length_tx_ch0 = 0;
	}
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_data_tx_ch2
// Purpose       : 发送通道2的数据
// Param[in]     : void  
// Param[out]    : None
// Return type   : 
// Comment       : 2020-07-18
//-------------------------------------------------------------------------------------------
void ble_data_tx_ch2(bool is_clear_buf)
{
	static uint16_t offset = 0;	
	uint16_t data_length = 0;
	ret_code_t err_code = NRF_SUCCESS;
	static uint8_t flag = 0;

	if(is_clear_buf){
		offset = 0;
		flag = 0;
		busy_flag_tx_ch2 = false;
		memset (data_tx_ch2, 0, data_length_tx_ch2);
		data_length_tx_ch2 = 0;
		return;
	}

	if(offset == 0 && 0 == flag){
		if(data_tx_ch1_ch2_flag){
			data_tx_ch1_ch2_flag = false;
			memcpy(data_tx_ch2,data_tx_ch1_ch2,DATA_LENGTH_CH1);
			data_length_tx_ch2 += DATA_LENGTH_CH1;
		}else{
			data_length_tx_ch2 += DATA_LENGTH_CH1;
			offset = DATA_LENGTH_CH1;
		}

		flag = 1;
	}

	if (offset < data_length_tx_ch2){
		if (BLE_NUS_MAX_DATA_LEN >= (data_length_tx_ch2 - offset)){
			data_length = data_length_tx_ch2 - offset;
			err_code = nus_data_send(&data_tx_ch2[offset], &data_length, (BLE_APP_NUS_CHANNLE_UUID)BLE_NUS_CH2_UUID);
            // err_code = ble_platform_nus_data_send(&data_tx_ch2[offset], &data_length, BLE_NUS_CH2_UUID);
		}
		else{
			data_length = BLE_NUS_MAX_DATA_LEN;
			err_code = nus_data_send(&data_tx_ch2[offset], &data_length, (BLE_APP_NUS_CHANNLE_UUID)BLE_NUS_CH2_UUID);
            // err_code = ble_platform_nus_data_send(&data_tx_ch2[offset], &data_length, BLE_NUS_CH2_UUID);
		}

		if (NRF_SUCCESS == err_code){
			offset += data_length;
		}
	}
	if (offset >= data_length_tx_ch2){
		offset = 0;
		flag = 0;
		busy_flag_tx_ch2 = false;
		memset (data_tx_ch2, 0, data_length_tx_ch2);
		data_length_tx_ch2 = 0;
	}
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_data_tx_ch3
// Purpose       : 发送通道3的数据
// Param[in]     : void  
// Param[out]    : None
// Return type   : 
// Comment       : 2020-07-18
//-------------------------------------------------------------------------------------------
void ble_data_tx_ch3(bool is_clear_buf)
{
	static uint16_t offset = 0;	
	uint16_t data_length = 0;
	ret_code_t err_code = NRF_SUCCESS;
	static uint8_t flag = 0;
	
	if(is_clear_buf){
		offset = 0;
		flag = 0;
		busy_flag_tx_ch3 = false;
		memset (data_tx_ch3, 0, data_length_tx_ch3);
		data_length_tx_ch3 = 0;
		return;
	}

	if(offset == 0 && 0 == flag){
		if(data_tx_ch1_ch3_flag){
			data_tx_ch1_ch3_flag = false;
			memcpy(data_tx_ch3,data_tx_ch1_ch3,DATA_LENGTH_CH1);
			data_length_tx_ch3 += DATA_LENGTH_CH1;
		}else{
			data_length_tx_ch3 += DATA_LENGTH_CH1;
			offset = DATA_LENGTH_CH1;
		}

		flag = 1;
	}

	if (offset < data_length_tx_ch3)
	{
		if (BLE_NUS_MAX_DATA_LEN >= (data_length_tx_ch3 - offset)){
			data_length = data_length_tx_ch3 - offset;
			err_code = nus_data_send(&data_tx_ch3[offset], &data_length, (BLE_APP_NUS_CHANNLE_UUID)BLE_NUS_CH3_UUID);
            // err_code = ble_platform_nus_data_send(&data_tx_ch3[offset], &data_length, BLE_NUS_CH3_UUID);
		}else{
			data_length = BLE_NUS_MAX_DATA_LEN;
			err_code = nus_data_send(&data_tx_ch3[offset], &data_length, (BLE_APP_NUS_CHANNLE_UUID)BLE_NUS_CH3_UUID);			
            // err_code = ble_platform_nus_data_send(&data_tx_ch3[offset], &data_length, BLE_NUS_CH3_UUID);			
		}

		if (NRF_SUCCESS == err_code){
			offset += data_length;
		}
	}

	if (offset >= data_length_tx_ch3){
		offset = 0;
		flag = 0;
		busy_flag_tx_ch3 = false;

		memset (data_tx_ch3, 0, data_length_tx_ch3);
		data_length_tx_ch3 = 0;
	}
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_data_tx
// Purpose       : 发送数据
// Param[in]     : void  
// Param[out]    : None
// Return type   : 
// Comment       : 2020-07-18
//-------------------------------------------------------------------------------------------
void ble_data_tx(void)
{
	//确保2通道在上传数据时，别的通道数据也能及时响应
	ble_data_tx_ch0(false);
	ble_data_tx_ch3(false);

	ble_data_tx_ch2(false);
}

void ble_data_tx_buf_clear(void)
{
	ble_data_tx_ch0(true);
	ble_data_tx_ch2(true);
	ble_data_tx_ch3(true);
}

bool ble_more_data_need_tx(void)
{
	if(busy_flag_tx_ch0 ||
		busy_flag_tx_ch2 ||
		busy_flag_tx_ch3)
		return true;
	return false;
}

bool ble_tx_channel_is_free(ble_nus_channel_uuid_t nus_channel_uuid)
{
	bool is_free = false;
	switch(nus_channel_uuid){
	case BLE_NUS_CH0_UUID:
		is_free = !busy_flag_tx_ch0;
		break;
	case BLE_NUS_CH2_UUID:
		is_free = !busy_flag_tx_ch2;
		break;
	case BLE_NUS_CH3_UUID:
		is_free = !busy_flag_tx_ch3;
		break;
	default:
		break;
	}
	return is_free;
}

