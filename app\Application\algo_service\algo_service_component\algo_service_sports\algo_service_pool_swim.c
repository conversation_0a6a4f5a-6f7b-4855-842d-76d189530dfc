﻿/*************************************************************************
 * @file algo_service_pool_swim.c
 * <AUTHOR> (<EMAIL>)
 * @brief  泳池游泳算法组件实现
 * @version 0.1
 * @date 2025-05-19
 *
 * @copyright Copyright (c) 2024-2025, <PERSON>han <PERSON>wu Technology Co., Ltd
 *
 ************************************************************************/
#include "algo_service_pool_swim.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_task.h"
#include "cfg_header_def.h"
#include "gui_event_service.h"
#include "max_min.h"
#include "qw_data_type.h"
#include "qw_time_util.h"
#include "service_datetime.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"
#include "utility_add.h"

#define MIN_ALERT_TIME_DIFF     60 //最小的报警时间间隔,单位s
#define IDLE_LENGTH_TIME_THRESHOLD 8000 // 空闲趟时间阈值ms
#define IDLE_LENGTH_STROKES_THRESHOLD 5 // 空闲趟划水次数阈值
#define WINDOW_SIZE 7 //实时划频窗口周期:s

// 输入数据
typedef struct
{
    uint32_t timestamp;              //时间戳
    uint32_t startIndex;             //本趟开始ms数
    uint32_t endIndex;               //本趟结束ms数,未结束时为0
    uint32_t realtimeLapCnt;         //趟数
    uint32_t cntStroke;              //本趟划次
    uint16_t updateType;             //本趟泳姿,1自,2蛙,3仰,4蝶,5混,本趟未结束时为0
    uint16_t frontcrawlPerc;         //本趟自由泳占比,本趟未结束时为0
    uint16_t breaststrokePerc;       //本趟蛙泳占比,本趟未结束时为0
    uint16_t backstrokePerc;         //本趟仰泳占比,本趟未结束时为0
    uint16_t butterflyPerc;          //本趟蝶泳占比,本趟未结束时为0
    uint16_t updateStatus;           //更新标志,本趟未结束时为0?
    saving_status_e saving_status;   //数据记录的状态
    uint8_t auto_lap_lengths;       //自动计圈趟数
} algo_pool_swim_sub_t;

static algo_pool_swim_sub_t s_algo_in;

// 发布数据
static algo_pool_swim_pub_t s_algo_out;

// 中间数据
static uint32_t s_total_strokes_base = 0;       //本趟之前的总划水次数
static uint32_t s_total_strokes_lap_base = 0;   //本趟之前的组划水次数
static uint32_t s_num_lengths_base = 0;         //本组之前的趟数
static uint32_t s_pre_length_end_ms = 0;        //前趟结束ms数
static max_min_uint16_t s_max_min_speed = {0};
static max_min_uint16_t s_max_min_speed_lap = {0};
static uint32_t s_window_cnt[WINDOW_SIZE];   // 实时划频窗口内的次数
static int64_t s_window_ms[WINDOW_SIZE];     // 实时划频窗口内的ms
static uint8_t s_window_index = 0;           // 实时划频窗口索引（当前输入点）
static uint8_t s_window_valid = 0;           // 实时划频窗口内的次数有效个数
static uint32_t s_timer_time_length = 0;
static uint32_t s_num_lap_lengths = 0; // 由于外部计圈截断而增加的趟数
static uint32_t s_num_lap_length_strokes = 0; // 外部计圈截断趟的划水次数
static uint32_t s_last_realtimeLapCnt = 0;         //上一次的趟数
static uint32_t s_last_cntStroke = 0;              //上一帧的划次
// 本算法打开标记
static bool s_is_open = false;

/* 从1自,2蛙,3仰,4蝶,5混,本趟未结束时为0
转换到
0：FREESTYLE 自由泳
1：BACKSTROKE 仰泳(缩写Back)
2：BREASTSTROKE 蛙泳(缩写Brst)
3：BUTTERFLY 蝶泳
4：DRILL 潜泳
5：MIXED 混合泳（统称）
6：IM 混合泳（Interval MIXED，有相关规则的，正式比赛项目中的一种混合泳） */
static uint8_t swim_stroke_get(uint16_t updateType)
{
    uint8_t stroke = 0xff;
    switch (updateType)
    {
    case 1:
        stroke = 0;
        break;
    case 2:
        stroke = 2;
        break;
    case 3:
        stroke = 1;
        break;
    case 4:
        stroke = 3;
        break;
    case 5:
        stroke = 5;
        break;
    default:
        break;
    }
    return stroke;
}

//Init lap.
static void alg_pool_swim_lap_init(void)
{
    algo_pool_swim_pub_t *algo_out = &s_algo_out;
    algo_out->total_distance_lap = 0;
    algo_out->avg_speed_lap = 0;
    algo_out->max_speed_lap = 0;
    algo_out->total_strokes_lap = 0;
    algo_out->avg_strokes_lap = 0;
    algo_out->avg_strokes_length_lap = 0;
    algo_out->avg_cadence_lap = 0;
    algo_out->num_lengths_lap = 0;
    algo_out->avg_swolf_lap = 0;
    algo_out->swim_stroke_lap = 0;
    algo_out->total_moving_time_lap = 0;
    algo_out->total_timer_time_lap = 0;
    algo_out->total_elapsed_time_lap = 0;
    algo_out->start_time_lap = service_datetime_get_fit_time();
    algo_out->start_time_length = service_datetime_get_fit_time();
    algo_out->swim_stroke_lap = 0xff; //泳姿

    s_total_strokes_lap_base = 0;
    s_num_lengths_base = algo_out->num_lengths;

    max_min_uint16_init(&s_max_min_speed_lap);
    // max_min_uint16_init(&s_max_min_balance_lap);
    // max_min_uint8_init(&s_max_min_left_effect_lap);
    // max_min_uint8_init(&s_max_min_right_effect_lap);
    // max_min_uint8_init(&s_max_min_left_smooth_lap);
    // max_min_uint8_init(&s_max_min_right_smooth_lap);
}

//Init session.
static void alg_pool_swim_session_init(void)
{
    algo_pool_swim_pub_t *algo_out = &s_algo_out;

    memset(algo_out, 0, sizeof(algo_pool_swim_pub_t));
    algo_out->start_time = service_datetime_get_fit_time();
    algo_out->swim_stroke = 0xff;          //泳姿
    algo_out->swim_stroke_lap = 0xff;      //泳姿
    algo_out->swim_stroke_length = 0xff;   //泳姿
    algo_out->swim_status = enum_swim_status_active;

    s_total_strokes_base = 0;
    s_num_lengths_base = 0;
    s_pre_length_end_ms = 0;
    s_timer_time_length = 0;
    s_num_lap_lengths = 0;
    s_last_realtimeLapCnt = 0;
    s_last_cntStroke = 0;
    alg_pool_swim_lap_init();

    max_min_uint16_init(&s_max_min_speed);
    // max_min_uint16_init(&s_max_min_balance);
    // max_min_uint8_init(&s_max_min_left_effect);
    // max_min_uint8_init(&s_max_min_right_effect);
    // max_min_uint8_init(&s_max_min_left_smooth);
    // max_min_uint8_init(&s_max_min_right_smooth);
    // max_min_uint16_init(&s_max_min_lap);
    // max_min_uint16_init(&s_max_min_balance_lap);
    // max_min_uint8_init(&s_max_min_left_effect_lap);
    // max_min_uint8_init(&s_max_min_right_effect_lap);
    // max_min_uint8_init(&s_max_min_left_smooth_lap);
    // max_min_uint8_init(&s_max_min_right_smooth_lap);

    memset(&s_window_cnt, 0, sizeof(s_window_cnt));
    memset(&s_window_ms, 0, sizeof(s_window_ms));
    s_window_index = 0;
    s_window_valid = 0;
}

//计趟时的处理
static void alg_pool_swim_length_end(algo_pool_swim_pub_t *algo_out, algo_pool_swim_sub_t *algo_in)
{
    uint32_t pool_length = (uint32_t) (get_sport_swim_value() * 100);   //泳池长度cm
    static uint32_t last_time = 0;
    uint32_t systime = service_datetime_get_fit_time();
    uint32_t length_inc = 0; //趟数增量
    length_inc = algo_in->realtimeLapCnt - s_last_realtimeLapCnt; //本趟划次增量
    //判断是否为空闲趟（GM自动计趟不应是空闲趟，这里只判断由于外部计圈截断而增加的趟是否是空闲趟）
    if (0 == algo_in->endIndex && (algo_out->start_time_length + IDLE_LENGTH_TIME_THRESHOLD > systime || IDLE_LENGTH_STROKES_THRESHOLD > algo_out->total_strokes_length)) //空闲趟
    {
        //空闲趟索引
        if (0 < algo_out->num_idle_lengths + algo_out->num_lengths)
        {
            algo_out->idle_length_index = algo_out->num_idle_lengths + algo_out->num_lengths - 1;
        }
        else
        {
            algo_out->idle_length_index = 0;
        }

        algo_out->length_index = algo_out->idle_length_index + 1; //趟索引
        algo_out->idle_length_time = (systime - algo_out->start_time_length) * 1000; //空闲趟时间ms
        algo_out->idle_length_start_time = algo_out->start_time_length; //空闲趟开始时间s
        algo_out->idle_length_timestamp = systime; //空闲趟时间戳
        algo_out->num_idle_lengths++; //空闲趟数加1
        // 空闲趟时间累加到总记录时间和圈记录时间
        algo_out->total_timer_time += algo_out->idle_length_time;
        algo_out->total_timer_time_lap += algo_out->idle_length_time;
#if __POOL_SWIM_AUTO_REST //自动休息功能。Garmin自动休息开启时，休息时间被计为lap
        algo_out->num_laps++; //用一个lap包裹一个空闲length
#endif
        // 遇到空闲趟时，清零本组距离

        algo_out->total_distance_lap = 0;
        algo_out->num_lengths_lap = 0;
        algo_out->total_strokes_lap = 0;
        algo_out->total_moving_time_lap = 0;
        algo_out->total_timer_time_lap = 0;
        algo_out->total_elapsed_time_lap = 0;
        algo_out->avg_speed_lap = 0;
        algo_out->max_speed_lap = 0;
        algo_out->avg_swolf_lap = 0;
        algo_out->avg_strokes_lap = 0;
        algo_out->avg_strokes_length_lap = 0;
        algo_out->avg_cadence_lap = 0;
        algo_out->swim_stroke_lap = 0xff;
        algo_out->total_elapsed_time = 0;
        max_min_uint16_init(&s_max_min_speed_lap);
        //下一趟数据初始化
        algo_out->start_time_length = systime; //趟开始时间s
        algo_out->total_timer_time_length = 0; //活动趟的记录时间
        algo_out->total_elapsed_time_length = 0; //活动趟的总时间（总时间与记录时间总是相同（Garmin））
        algo_out->total_moving_time_length = 0; //活动趟游泳时间
        algo_out->total_strokes_length = 0;
    }
    else //活动趟
    {
        //趟结束时更新的数据
        algo_out->num_lengths += length_inc;
        algo_out->num_lengths_lap += length_inc;// 本组趟数累加
        algo_out->total_distance = pool_length * algo_out->num_lengths;
        algo_out->total_distance_lap = pool_length * algo_out->num_lengths_lap;
        algo_out->start_time_pre_length = algo_out->start_time_length;

        if (0 < algo_out->num_idle_lengths + algo_out->num_lengths)
        {
            algo_out->length_index = algo_out->num_idle_lengths + algo_out->num_lengths - 1;
        }

        //趟时间
        if (0 < algo_in->endIndex) //GM自动计趟
        {
            algo_out->total_elapsed_time_length = algo_in->endIndex - s_pre_length_end_ms;
            algo_out->total_timer_time_length = algo_in->endIndex - s_pre_length_end_ms;
            algo_out->total_moving_time_length = algo_in->endIndex - algo_in->startIndex;
            s_num_lap_length_strokes = 0;
        }
        else //由于外部计圈截断而增加的趟
        {
            algo_out->total_elapsed_time_length = (systime - algo_out->start_time_length) * 1000;
            algo_out->total_timer_time_length = (systime - algo_out->start_time_length) * 1000;
            algo_out->total_moving_time_length = (systime - algo_out->start_time_length) * 1000;
            // s_num_lap_length_strokes = algo_in->cntStroke;
        }

        //如果趟记录时间比累加时间长，可能是中途暂停，则用累加时间
        if (algo_out->total_timer_time_length > s_timer_time_length + 2000)
        {
            algo_out->total_timer_time_length = s_timer_time_length;
        }

        //中途暂停情况的游泳时间
        if (algo_out->total_moving_time_length > algo_out->total_timer_time_length)
        {
            algo_out->total_moving_time_length = algo_out->total_timer_time_length;
        }

//         // 游泳时间与记录时间差值较大, 存一个空闲的length（自动休息开启时，还要存一个lap）
//         if (algo_out->total_moving_time_length + IDLE_LENGTH_TIME_THRESHOLD < algo_out->total_timer_time_length)
//         {
//             //空闲趟索引
//             if (0 < algo_out->num_idle_lengths + algo_out->num_lengths)
//             {
//                 algo_out->idle_length_index = algo_out->num_idle_lengths + algo_out->num_lengths - 1;
//             }
//             else
//             {
//                 algo_out->idle_length_index = 0;
//             }

//             algo_out->length_index = algo_out->idle_length_index + 1; //趟索引
//             algo_out->idle_length_time = algo_out->total_timer_time_length - algo_out->total_moving_time_length; //空闲趟时间ms
//             algo_out->idle_length_start_time = algo_out->start_time_length; //空闲趟开始时间s
//             algo_out->start_time_length += algo_out->idle_length_time / 1000; //趟开始时间s
//             algo_out->idle_length_timestamp = algo_out->start_time_length; //空闲趟时间戳
//             algo_out->total_timer_time_length = algo_out->total_moving_time_length; //活动趟的记录时间
//             algo_out->total_elapsed_time_length = algo_out->total_timer_time_length; //活动趟的总时间（总时间与记录时间总是相同（Garmin））
//             algo_out->num_idle_lengths++; //空闲趟数加1
// #if __POOL_SWIM_AUTO_REST //自动休息功能。Garmin自动休息开启时，休息时间被计为lap
//             algo_out->num_laps++; //用一个lap包裹一个空闲length
// #endif

//             //空闲趟时间加到记录时间
//             // algo_out->total_timer_time += algo_out->idle_length_time;
//             // algo_out->total_timer_time_lap += algo_out->idle_length_time;
//         }
//         else
//         {
//             algo_out->idle_length_time = 0;                                           //空闲趟时间为0, fit不存空闲趟, 直接存活动趟
//             algo_out->total_moving_time_length = algo_out->total_timer_time_length;   //活动趟的游泳时间
//         }

        //趟时间加到记录时间
        // algo_out->total_timer_time += algo_out->total_timer_time_length;
        // algo_out->total_timer_time_lap += algo_out->total_timer_time_length;

        algo_out->idle_length_time = 0;                                           //空闲趟时间为0, fit不存空闲趟, 直接存活动趟
        algo_out->total_moving_time_length = algo_out->total_timer_time_length;   //活动趟的游泳时间
        //游泳时间
        algo_out->total_moving_time += algo_out->total_moving_time_length;
        algo_out->total_moving_time_lap += algo_out->total_moving_time_length;

        //总时间
        // algo_out->total_elapsed_time = (systime - algo_out->start_time) * 1000;
        // algo_out->total_elapsed_time_lap = (systime - algo_out->start_time_lap) * 1000;

        //平均速度
        if (0 < algo_out->total_moving_time && 0 < 0xffffffff != algo_out->total_distance)
        {
            algo_out->avg_speed = algo_out->total_distance * 10000 / algo_out->total_moving_time;
        }

        if (0 < algo_out->total_moving_time_length && 0 < 0xffffffff != pool_length)
        {
            algo_out->avg_speed_pre_length = pool_length * 10000 / algo_out->total_moving_time_length;
        }

        if (0 < algo_out->total_moving_time_lap && 0 < 0xffffffff != algo_out->total_distance_lap)
        {
            algo_out->avg_speed_lap = algo_out->total_distance_lap * 10000 / algo_out->total_moving_time_lap;
        }

        //最大速度
        max_min_uint16_update(&s_max_min_speed, algo_out->avg_speed_pre_length);
        algo_out->max_speed = s_max_min_speed.max;
        max_min_uint16_update(&s_max_min_speed_lap, algo_out->avg_speed_pre_length);
        algo_out->max_speed_lap = s_max_min_speed_lap.max;

        //趟均划次
        if (0 < algo_out->num_lengths)
        {
            algo_out->avg_strokes_length = algo_out->total_strokes / algo_out->num_lengths;
        }

        //组趟均划次
        if (0 < algo_out->num_lengths_lap)
        {
            algo_out->avg_strokes_length_lap = algo_out->total_strokes_lap / algo_out->num_lengths_lap;
        }

        //组均划次
        if (0 < algo_out->num_laps)
        {
            algo_out->avg_strokes_lap = algo_out->total_strokes_lap / algo_out->num_laps;
        }

        //划水频率
        if (0 < algo_out->total_moving_time)
        {
            algo_out->avg_cadence = (uint8_t) (algo_out->total_strokes * 60000 / algo_out->total_moving_time);
        }

        //组划水频率
        if (0 < algo_out->total_moving_time_lap)
        {
            algo_out->avg_cadence_lap = (uint8_t) (algo_out->total_strokes_lap * 60000 / algo_out->total_moving_time_lap);
        }

        //趟划水频率
        if (0 < algo_out->total_moving_time_length)
        {
            algo_out->avg_cadence_length = (uint8_t) (algo_out->total_strokes_length * 60000 / algo_out->total_moving_time_length);
        }

        //SWOLF
        algo_out->swolf = algo_out->total_moving_time_length / 1000 + algo_out->total_strokes_length;

        if (0 < algo_out->num_lengths)
        {
            algo_out->avg_swolf = (algo_out->total_moving_time / 1000 + algo_out->total_strokes) / algo_out->num_lengths;
        }

        if (0 < algo_out->num_lengths_lap)
        {
            algo_out->avg_swolf_lap = (algo_out->total_moving_time_lap / 1000 + algo_out->total_strokes_lap) / algo_out->num_lengths_lap;
        }

        //泳姿
        algo_out->swim_stroke_length = swim_stroke_get(algo_in->updateType);
        algo_out->length_type = 1;   //活动

        if (0xff == algo_out->swim_stroke)
        {
            algo_out->swim_stroke = algo_out->swim_stroke_length;
        }
        else if (algo_out->swim_stroke != algo_out->swim_stroke_length && 0xff != algo_out->swim_stroke_length)
        {
            algo_out->swim_stroke = 5;   //混合泳
        }

        if (0xff == algo_out->swim_stroke_lap)
        {
            algo_out->swim_stroke_lap = algo_out->swim_stroke_length;
        }
        else if (algo_out->swim_stroke_lap != algo_out->swim_stroke_length && 0xff != algo_out->swim_stroke_length)
        {
            algo_out->swim_stroke_lap = 5;   //混合泳
        }

        // //下一趟初始化
        // s_total_strokes_base = algo_out->total_strokes;
        // s_total_strokes_lap_base = algo_out->total_strokes_lap;
        // s_pre_length_end_ms = 0 < algo_in->endIndex ? algo_in->endIndex : (systime - algo_out->start_time) * 1000;
        // algo_out->start_time_length = systime;
        // algo_out->total_strokes_pre_length = algo_out->total_strokes_length;
        // s_timer_time_length = 0;
        s_pre_length_end_ms = algo_in->endIndex;
        algo_out->start_time_length = systime;
        algo_out->total_strokes_pre_length = algo_out->total_strokes_length;
        s_timer_time_length = 0;
    }
    s_last_realtimeLapCnt = algo_in->realtimeLapCnt; //更新趟数
}

//计圈时的处理
static void alg_pool_swim_lap_end(algo_pool_swim_pub_t *algo_out)
{
    //Copy to pre lap.
    algo_out->total_distance_pre_lap = algo_out->total_distance_lap;
    algo_out->avg_speed_pre_lap = algo_out->avg_speed_lap;
    algo_out->max_speed_pre_lap = algo_out->max_speed_lap;
    algo_out->total_strokes_pre_lap = algo_out->total_strokes_lap;
    algo_out->num_lengths_pre_lap = algo_out->num_lengths_lap;
    algo_out->avg_swolf_pre_lap = algo_out->avg_swolf_lap;
    algo_out->first_length_index = algo_out->num_lengths;
    algo_out->num_laps++;

    algo_out->timestamp_pre_lap = algo_out->start_time_length;
    algo_out->start_time_pre_lap = algo_out->start_time_lap;
    algo_out->total_elapsed_time_pre_lap = (algo_out->timestamp_pre_lap - algo_out->start_time_pre_lap) * 1000;
    algo_out->total_timer_time_pre_lap = algo_out->total_elapsed_time_pre_lap;
    algo_out->total_moving_time_pre_lap = algo_out->total_moving_time_lap;

    algo_out->avg_strokes_length_pre_lap = algo_out->avg_strokes_length_lap; //组单趟平均划次
    algo_out->avg_cadence_pre_lap = algo_out->avg_cadence_lap; //组平均划频(划水率)

    //单圈均时
    algo_out->avg_lap_time = algo_out->num_idle_lengths < algo_out->num_laps
                                ? algo_out->total_timer_time / (algo_out->num_laps - algo_out->num_idle_lengths)
                                : 0;
        // lap写出后，清零本组距离
    algo_out->total_distance_lap = 0;
    algo_out->total_strokes_lap = 0;
    algo_out->num_lengths_lap = 0;
    algo_out->total_moving_time_lap = 0;
    algo_out->total_timer_time_lap = 0;
    algo_out->total_elapsed_time_lap = 0;
    algo_out->avg_speed_lap = 0;
    algo_out->max_speed_lap = 0;
    algo_out->avg_swolf_lap = 0;
    algo_out->avg_strokes_lap = 0;
    algo_out->avg_strokes_length_lap = 0;
    algo_out->avg_cadence_lap = 0;
    algo_out->swim_stroke_lap = 0xff;

    //Init lap.
    alg_pool_swim_lap_init();
}

/**
 * @brief 算法处理
 *
 * @param algo_out 输出数据
 * @param algo_in 输入数据
 */
static void algo_pool_swim_deal(algo_pool_swim_pub_t* algo_out, algo_pool_swim_sub_t* algo_in)
{
    uint32_t pool_length = (uint32_t)(get_sport_swim_value() * 100); // 泳池长度 cm
	uint32_t systime_s = service_datetime_get_fit_time();          // 日历时间/fit时间（单位s）
	int64_t  boot_msec = get_boot_msec();                          // 算法启动时间戳（单位ms）

    // 先用输入时间戳占位
    algo_out->timestamp = algo_in->timestamp;

    // 内部状态机
    static uint32_t s_last_endIndex = 0;
    static uint32_t s_idle_start_ms = 0;         // 当前这一段空闲的起始时间ms（进入空闲时设定）
  

    // 时间累计(沿用原 diff_s 逻辑)
    static uint32_t last_systime_s = 0;
    uint32_t diff_s = (systime_s > last_systime_s && (systime_s - last_systime_s) < 10)
        ? (systime_s - last_systime_s) : 0;
    last_systime_s = systime_s;

    if (enum_status_saving <= algo_in->saving_status)
    {
        algo_out->total_elapsed_time += diff_s * 1000;
        algo_out->total_elapsed_time_lap += diff_s * 1000;
        if (enum_status_saving == algo_in->saving_status)
        {
            s_timer_time_length += diff_s * 1000;
            algo_out->total_timer_time += diff_s * 1000;
            algo_out->total_timer_time_lap += diff_s * 1000;
        }
    }

    //length time++
    if (enum_swim_status_active == algo_out->swim_status)
    {
        if (systime_s > algo_out->start_time_length)
        {
			algo_out->total_elapsed_time_length = (systime_s - algo_out->start_time_length) * 1000;
			algo_out->total_timer_time_length = (systime_s - algo_out->start_time_length) * 1000;
		}
    }
    else //idle
    {
        if (algo_out->idle_length_start_time > 0 && systime_s >= algo_out->idle_length_start_time)
        {
			algo_out->idle_length_time = (systime_s - algo_out->idle_length_start_time) * 1000;
        }
			
    }

    algo_out->timestamp = systime_s;

    // 划次增量
    uint32_t stroke_inc = 0;
    if (algo_in->cntStroke >= s_last_cntStroke)
        stroke_inc = algo_in->cntStroke - s_last_cntStroke;
    else
    {
        // 设备内部重置 => 新趟内计数重开
        stroke_inc = algo_in->cntStroke;
        algo_out->total_strokes_length = 0;
    }
    s_last_cntStroke = algo_in->cntStroke;

    // 累加划次
    algo_out->total_strokes += stroke_inc;
    algo_out->total_strokes_lap += stroke_inc;
    algo_out->total_strokes_length += stroke_inc;

    // 实时划频窗口
    if (WINDOW_SIZE > s_window_valid)
    {
        s_window_cnt[s_window_index] = algo_out->total_strokes;
        s_window_ms[s_window_index] = boot_msec;
        s_window_index = QW_RING_ADD(s_window_index, 1, WINDOW_SIZE);
        s_window_valid++;
    }
    else
    {
        int next = QW_RING_ADD(s_window_index, 1, WINDOW_SIZE);
        if (boot_msec > s_window_ms[next])
        {
            algo_out->cadence = (uint8_t)(((int64_t)algo_out->total_strokes - s_window_cnt[s_window_index]) * 60000
                / (boot_msec - s_window_ms[next]));
            s_window_cnt[s_window_index] = algo_out->total_strokes;
            s_window_ms[s_window_index] = boot_msec;
            s_window_index = QW_RING_ADD(s_window_index, 1, WINDOW_SIZE);
        }
    }

    // 状态判定
    bool stroke_is_zero = (algo_in->cntStroke == 0);
    bool stroke_reach_active = (algo_in->cntStroke >= IDLE_LENGTH_STROKES_THRESHOLD);
	uint32_t since_active_ms = (systime_s - algo_out->start_time - s_last_endIndex / 1000) * 1000;

    if (stroke_is_zero)
    {
        // 达到进入空闲判定
        if (since_active_ms > IDLE_LENGTH_TIME_THRESHOLD)
        {
            if (algo_out->swim_status != enum_swim_status_idle)
            {
                // 进入空闲
                algo_out->swim_status = enum_swim_status_idle;

                // 首次进入空闲时生成空闲趟基础信息
                algo_out->idle_length_start_time = (systime_s - (since_active_ms / 1000));
                algo_out->idle_length_time = since_active_ms; // 已经累积的这段
                //algo_out->idle_length_timestamp = systime_s;

                if (algo_out->num_idle_lengths + algo_out->num_lengths > 0)
                    algo_out->idle_length_index = algo_out->num_idle_lengths + algo_out->num_lengths - 1;
                else
                    algo_out->idle_length_index = 0;
                algo_out->length_index = algo_out->idle_length_index + 1;
                //algo_out->num_idle_lengths++;

#if __POOL_SWIM_AUTO_REST
                if (0 < algo_out->num_lengths)
                {
				    alg_pool_swim_lap_end(algo_out);
                }
#endif
            }
        }
        // else 尚未达到阈值：保持活动状态/过渡，不改变现有状态
    }
    else  // 有划水
    {
        if (stroke_reach_active && algo_out->swim_status == enum_swim_status_idle)
        {
            // 空闲 -> 活动
            algo_out->swim_status = enum_swim_status_active;

            //加一个空闲趟结算（时间，趟数）
            //algo_out->idle_length_start_time = (systime_s - (since_active_ms / 1000));
            algo_out->idle_length_time = algo_in->startIndex - s_last_endIndex; // 已经累积的这段
            algo_out->idle_length_timestamp = algo_out->start_time + algo_in->startIndex / 1000;
			s_pre_length_end_ms = algo_in->startIndex; // 作为下一趟的起点

#if __POOL_SWIM_AUTO_REST
            algo_out->start_time_lap = algo_out->idle_length_timestamp;
#endif

            /*if (algo_out->num_idle_lengths + algo_out->num_lengths > 0)
                algo_out->idle_length_index = algo_out->num_idle_lengths + algo_out->num_lengths - 1;
            else
                algo_out->idle_length_index = 0;
            algo_out->length_index = algo_out->idle_length_index + 1;*/
            algo_out->num_idle_lengths++;

            // 开始新活动趟
            algo_out->start_time_length = algo_out->start_time + algo_in->startIndex / 1000;
            algo_out->total_timer_time_length = (systime_s - algo_out->start_time_length) * 1000;
            algo_out->total_elapsed_time_length = (systime_s - algo_out->start_time_length) * 1000;
            //algo_out->total_moving_time_length = (systime_s - algo_out->start_time_length) * 1000;
            //algo_out->total_strokes_length = algo_in->cntStroke;
        }
    }

    // endIndex -> 活动趟结束
    if (algo_in->endIndex > 0 && algo_in->endIndex != s_last_endIndex)
    {
        s_last_endIndex = algo_in->endIndex;

        alg_pool_swim_length_end(algo_out, algo_in);

        // 结束时间戳（start_time_length 已更新为下一趟开始时间 = 本趟结束时间）
        algo_out->timestamp_lap = systime_s; //?
    }
}

/**
 * @brief 算法控制
 *
 * @param algo_out 输出数据
 * @param ctrl_type 控制类型
 */
static void algo_pool_swim_ctrl(algo_pool_swim_pub_t *algo_out, ctrl_type_e ctrl_type)
{
    uint32_t systime = service_datetime_get_fit_time();

    if (enum_ctrl_start == ctrl_type)
    {
        alg_pool_swim_session_init();
    }
    else if (enum_ctrl_lap == ctrl_type)
    {
                //Copy to pre lap.
        algo_out->total_distance_pre_lap = algo_out->total_distance_lap;
        algo_out->avg_speed_pre_lap = algo_out->avg_speed_lap;
        algo_out->max_speed_pre_lap = algo_out->max_speed_lap;
        algo_out->total_strokes_pre_lap = algo_out->total_strokes_lap;
        algo_out->num_lengths_pre_lap = algo_out->num_lengths_lap;
        algo_out->avg_swolf_pre_lap = algo_out->avg_swolf_lap;
        algo_out->first_length_index = algo_out->num_lengths;
        algo_out->num_laps++;

        //单圈均时
        algo_out->avg_lap_time = algo_out->num_idle_lengths < algo_out->num_laps ? algo_out->total_timer_time / (algo_out->num_laps - algo_out->num_idle_lengths) : 0;

        //Init lap.
        alg_pool_swim_lap_init();

        // 重置组划次历史值
        //s_last_total_strokes_lap = 0;
    

        // 计趟时的处理（外部计圈截断趟）
        alg_pool_swim_length_end(algo_out, &s_algo_in);

        //计圈时的处理
        alg_pool_swim_lap_end(algo_out);
        
        submit_gui_event(GUI_EVT_RESET_PAGE, true, NULL);
    }
    else if (enum_ctrl_rest_resume == ctrl_type)
    {
        submit_gui_event(GUI_EVT_RESET_PAGE, false, NULL);
    }
}

/**
 * @brief 算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_pool_swim_drv_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_POOL_SWIM;
    head.input_type = DATA_ID_EVENT_POOL_SWIM_DRV;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_sports_ctrl_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_POOL_SWIM;
    head.input_type = DATA_ID_EVENT_SPORTS_CTRL;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_pool_swim_out_callback(const void *out, uint32_t len)
{
    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_ALGO_POOL_SWIM, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("%s publish error", __FUNCTION__);
    }
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_POOL_SWIM_DRV,
        .topic_id = DATA_ID_EVENT_POOL_SWIM_DRV,
        .callback = algo_pool_swim_drv_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
};

/**
 * @brief 算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_pool_swim_init(void)
{
    return 0;
}

/**
 * @brief 打开算法
 *
 * @return int32_t 结果
 */
static int32_t algo_pool_swim_open(void)
{
    if (s_is_open)
    {
        ALGO_COMP_LOG_W("%s already open", __FUNCTION__);
        return 0;
    }

    ALGO_COMP_LOG_D("%s open", __FUNCTION__);

    s_is_open = true;

    memset(&s_algo_in, 0, sizeof(s_algo_in));
    s_algo_in.saving_status = enum_status_saving;
    alg_pool_swim_session_init();

    return algo_topic_list_subscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
}

/**
 * @brief feed算法
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_pool_swim_feed(uint32_t input_type, void *data, uint32_t len)
{
    algo_pool_swim_sub_t *algo_in = &s_algo_in;
    algo_pool_swim_pub_t *algo_out = &s_algo_out;
    AUTO_RECORD_LAP_TYPE auto_lap_type = AUTO_RECORD_LAP_NUMS;
    uint32_t auto_lap_threshold = 0;   // 阈值
    uint8_t auto_lap_enable = 0;       // 0:关闭 1:开启

    switch (input_type)
    {
    case DATA_ID_EVENT_POOL_SWIM_DRV:
    {
        const pool_swim_drv_pub_t *drv_data = (pool_swim_drv_pub_t *) data;
        algo_in->timestamp = drv_data->timestamp;
        algo_in->startIndex = drv_data->startIndex;
        algo_in->endIndex = drv_data->endIndex;
        algo_in->realtimeLapCnt = drv_data->realtimeLapCnt;
        algo_in->cntStroke = drv_data->cntStroke;
        algo_in->updateType = drv_data->updateType;
        algo_in->frontcrawlPerc = drv_data->frontcrawlPerc;
        algo_in->breaststrokePerc = drv_data->breaststrokePerc;
        algo_in->backstrokePerc = drv_data->backstrokePerc;
        algo_in->butterflyPerc = drv_data->butterflyPerc;
        algo_in->updateStatus = drv_data->updateStatus;

        //TEST SWIM
        // rt_kprintf("==========%s:%d,IN:Lengths%u,Strokes%u=========\n", __FUNCTION__, __LINE__,algo_in->realtimeLapCnt,algo_in->cntStroke);
    }
    break;
    case DATA_ID_EVENT_SPORTS_CTRL:
    {
        const algo_sports_ctrl_t *sports_ctrl = (algo_sports_ctrl_t *) data;
        algo_in->saving_status = sports_ctrl->saving_status;

        //算法控制
        algo_pool_swim_ctrl(algo_out, sports_ctrl->ctrl_type);

        //数据发布
        if (sports_ctrl->ctrl_type == enum_ctrl_null)
        {
            auto_lap_enable = get_auto_record_lap(get_current_sport_mode());                                      // 是否启用自动记圈
            auto_lap_type = get_auto_record_lap_type(get_current_sport_mode());                                   // 自动记圈类型
            auto_lap_threshold = get_auto_record_lap_value(get_current_sport_mode(), auto_lap_type);   // 自动记圈阈值

            if (auto_lap_enable && auto_lap_type == AUTO_RECORD_LAP_TRIPS)
            {
                algo_in->auto_lap_lengths = (uint8_t)auto_lap_threshold; //自动记圈趟数
            }
            else
            {
                algo_in->auto_lap_lengths = 0;
            }

            //算法处理
            algo_pool_swim_deal(algo_out, algo_in);

            //数据发布
            algo_pool_swim_out_callback(algo_out, sizeof(algo_pool_swim_pub_t));
        }
        else if (sports_ctrl->ctrl_type == enum_ctrl_lap)
        {
            //记圈后马上数据发布
            algo_pool_swim_out_callback(algo_out, sizeof(algo_pool_swim_pub_t));
        }
    }
    break;
    default:
        break;
    }
    return 0;
}

/**
 * @brief 关闭算法
 *
 * @return int32_t 结果
 */
static int32_t algo_pool_swim_close(void)
{
    if (!s_is_open)
    {
        ALGO_COMP_LOG_W("%s already close", __FUNCTION__);
        return 0;
    }

    algo_topic_list_unsubscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));

    s_is_open = false;
    ALGO_COMP_LOG_D("%s close", __FUNCTION__);
    return 0;
}

// 算法组件实现
static algo_compent_ops_t s_pool_swim_algo = {
    .init = algo_pool_swim_init,
    .open = algo_pool_swim_open,
    .feed = algo_pool_swim_feed,
    .close = algo_pool_swim_close,
    .ioctl = NULL,
};

/**
 * @brief 组件注册
 *
 * @return int32_t 结果
 */
int32_t register_pool_swim_algo(void)
{
    algo_compnent_register(ALGO_TYPE_POOL_SWIM, &s_pool_swim_algo);
    return 0;
}
