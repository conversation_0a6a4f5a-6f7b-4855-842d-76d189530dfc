/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   watch_config_pb.c
@Time    :   2025/03/10 17:34:38
*
**************************************************************************/

#include "watch_config_pb.h"
#include "watch_config.pb.h"
#include "pb_decode.h"
#include "cfg_header_def.h"
#include "ble_nus_srv.h"
#include "pb_encode.h"
#include "crc8.h"
#include "ble_cmd_response.h"
#include "ble_cmd_common.h"
#include "ble_peripheral.h"
#include "ble_nus.h"
#include "pb_decode_common.h"
#include "qw_log.h"
#include "algo_service_sport_status.h"
#include "gui_event_service.h"
#include "cfg_sportspage.h"

#define WATCH_CONFIG_PB_LVL             LOG_LVL_WARNING
#define WATCH_CONFIG_PB_TAG             "watch_config_pb"

#if (WATCH_CONFIG_PB_LVL >= LOG_LVL_DBG)
    #define WATCH_CONFIG_PB_LOG_D(...)          QW_LOG_D(WATCH_CONFIG_PB_TAG, __VA_ARGS__)
#else
    #define WATCH_CONFIG_PB_LOG_D(...)
#endif

#if (WATCH_CONFIG_PB_LVL >= LOG_LVL_INFO)
    #define WATCH_CONFIG_PB_LOG_I(...)          QW_LOG_I(WATCH_CONFIG_PB_TAG, __VA_ARGS__)
#else
    #define WATCH_CONFIG_PB_LOG_I(...)
#endif

#if (WATCH_CONFIG_PB_LVL >= LOG_LVL_WARNING)    
    #define WATCH_CONFIG_PB_LOG_W(...)          QW_LOG_W(WATCH_CONFIG_PB_TAG, __VA_ARGS__)
#else
    #define WATCH_CONFIG_PB_LOG_W(...)
#endif

#if (WATCH_CONFIG_PB_LVL >= LOG_LVL_ERROR)
    #define WATCH_CONFIG_PB_LOG_E(...)          QW_LOG_E(WATCH_CONFIG_PB_TAG, __VA_ARGS__)
#else
    #define WATCH_CONFIG_PB_LOG_E(...)
#endif

#define MAX_UNIT_ITEM_NUM               6

typedef struct
{
    WATCH_UNIT_ITEM unit_item;
    WATCH_UNIT_TYPE unit_type;
}unit_config_st;

static unit_config_st unit_config[MAX_UNIT_ITEM_NUM];
static uint8_t unit_set_index = 0;
static uint8_t cur_operate_mode = 0;
static uint8_t auto_set_index = 0;
static uint8_t data_grid_page_index = 0; // 数据网格页面索引
// 全局页面配置结构体，用于存储解码后的页面配置数据
static watch_page_msg page_config[GRAPH_PAGE_ALL_TOTAL] = {0};
static uint32_t page_data[GRAPH_PAGE_ALL_TOTAL][MAX_PAGE_DATA_ITEMS] = {0};
static watch_alarm_msg alarm_message;
static watch_alarm_select_info alarm_select_info[_WATCH_ALARM_SELECT_ARRAYSIZE] = {0};
static watch_alarm_params_set_msg alarm_params_set_message[_WATCH_ALARM_TYPE_ARRAYSIZE] = {0};
static watch_auto_set_msg auto_set_message_info[_WATCH_AUTO_TYPE_ARRAYSIZE] = {0};
static watch_menu_list_msg menu_list_message = {0};
static watch_menu_item_msg menu_item_message[CFG_CUSTOM_RSV_NUM] = {0};
static uint8_t alarm_index = 0;         // 警示提醒索引
static uint8_t alarm_select_index = 0;  // 警示提醒选择索引
static uint8_t sport_page_index = 0;
static uint8_t current_data_count = 0;  // 当前页面的数据项数量
static uint8_t user_info_update_flag = 0;

//-------------------------------------------------------------------------------------------
// Function Name : repeated_uint32_decode
// Purpose       : uint16_t 数据解码
// Param[in]     : pb_istream_t *stream
//                 const pb_field_t *field
//                 void **arg
// Param[out]    : None
// Return type   :
// Comment       : 2019-08-16
//-------------------------------------------------------------------------------------------
static bool repeated_uint32_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    uint32_t** ptr = (uint32_t**) arg;
    uint64_t value = 0;

    if (!pb_decode_varint(stream, &value))
    {
        return false;
    }
    **ptr = (uint32_t)value;
    (*ptr) ++;

    return true;
}

//-------------------------------------------------------------------------------------------
// Function Name : page_message_decode
// Purpose       : 页面设置子消息解码接口
// Param[in]     : pb_istream_t *stream
//                 const pb_field_t *field
//                 void **arg
// Param[out]    : None
// Return type   : static bool
// Comment       : 2025-03-20
//-------------------------------------------------------------------------------------------
static bool page_message_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    if (stream == NULL || field == NULL || arg == NULL)
    {
        return false;
    }

    watch_page_msg *page_msg_st = (watch_page_msg *)*arg;
 
    if (sport_page_index >= GRAPH_PAGE_ALL_TOTAL)
    {
        WATCH_CONFIG_PB_LOG_E("%s %d: Invalid page index: %d\n", __func__, __LINE__, sport_page_index);
        return false;
    }

    // 清空当前页面配置
    memset(&page_config[sport_page_index], 0, sizeof(watch_page_msg));

    // 设置数据解码回调
    page_config[sport_page_index].data.arg = page_data[sport_page_index];
    page_config[sport_page_index].data.funcs.decode = read_repeated_varint;

    // 解码页面消息
    if (!pb_decode(stream, watch_page_msg_fields, &page_config[sport_page_index]))
    {
        WATCH_CONFIG_PB_LOG_E("%s %d: Failed to decode page message\n", __func__, __LINE__);
        return false;
    }

    sport_page_index ++;
    return true;
}

//-------------------------------------------------------------------------------------------
// Function Name : unit_message_decode
// Purpose       : 单位设置子消息解码接口
// Param[in]     : pb_istream_t *stream
//                 const pb_field_t *field
//                 void **arg
// Param[out]    : None
// Return type   : static
// Comment       : 2020-05-28
//-------------------------------------------------------------------------------------------
static bool unit_message_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    watch_unit_msg submsg;
    memset (&submsg, 0, sizeof(watch_unit_msg));

    if (!pb_decode(stream, watch_unit_msg_fields, &submsg))
    {
        return false;
    }

    if (submsg.has_unit_item)
    {
        unit_config[unit_set_index].unit_item = submsg.unit_item;
    }
    if (submsg.has_unit_type)
    {
        unit_config[unit_set_index].unit_type = submsg.unit_type;
    }

    unit_set_index ++;

    return true;
}

//-------------------------------------------------------------------------------------------
// Function Name : unit_config_set
// Purpose       : 码表单位设置
// Param[in]     : void
// Param[out]    : None
// Return type   : static
// Comment       : 2020-05-28
//-------------------------------------------------------------------------------------------
static void unit_config_set(void)
{
    for (uint8_t i = 0; i < MAX_UNIT_ITEM_NUM; i ++)
    {
        if (WATCH_UNIT_ITEM_enum_WATCH_UNIT_ITEM_INVALID == unit_config[i].unit_item || WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_INVALID == unit_config[i].unit_type)
        {
            continue;
        }
        WATCH_CONFIG_PB_LOG_D("%s %d index:%d unit_item:%d\n", __func__,__LINE__,i,unit_config[i].unit_item);
        switch(unit_config[i].unit_item)
        {
            case WATCH_UNIT_ITEM_enum_WATCH_UNIT_ITEM_DISTANCE:
                if (WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_METRIC == unit_config[i].unit_type)
                {
                    set_unit_distance(1);
                }
                else
                {
                    set_unit_distance(0);
                }
                break;
            case WATCH_UNIT_ITEM_enum_WATCH_UNIT_ITEM_ELEVATION:
                if (WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_METRIC == unit_config[i].unit_type)
                {
                    set_unit_height(1);
                }
                else
                {
                    set_unit_height(0);
                }
                break;
            case WATCH_UNIT_ITEM_enum_WATCH_UNIT_ITEM_WEIGHT:
                if (WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_METRIC == unit_config[i].unit_type)
                {
                    set_unit_weight(1);
                }
                else
                {
                    set_unit_weight(0);
                }
                break;
            case WATCH_UNIT_ITEM_enum_WATCH_UNIT_ITEM_TEMPERATURE:
                if (WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_METRIC == unit_config[i].unit_type)
                {
                    set_unit_temperature(1);
                }
                else
                {
                    set_unit_temperature(0);
                }
                break;
            case WATCH_UNIT_ITEM_enum_WATCH_UNIT_ITEM_LENGTH:
                if (WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_METRIC == unit_config[i].unit_type)
                {
                    set_unit_length(1);
                }
                else
                {
                    set_unit_length(0);
                }
                break;
            case WATCH_UNIT_ITEM_enum_WATCH_UNIT_ITEM_POOL_LENGTH:
                if (WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_METRIC == unit_config[i].unit_type)
                {
                    set_unit_pool_length(1);
                }
                else
                {
                    set_unit_pool_length(0);
                }
                break;
            default:
                break;
        }
    }
}
//-------------------------------------------------------------------------------------------
// Function Name : unit_message_encode
// Purpose       : 单位PB编码
// Param[in]     : pb_ostream_t *stream
//                 const pb_field_t *field
//                 void *const *arg
// Param[out]    : None
// Return type   : static
// Comment       : 2020-11-27
//-------------------------------------------------------------------------------------------
static bool unit_message_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    uint8_t i = 0;
    uint8_t status = false;
    watch_unit_msg unit_message= {0};

    memset (&unit_message, 0, sizeof(watch_unit_msg));

    for (i =0; i< MAX_UNIT_ITEM_NUM; i++)
    {
        unit_message.has_unit_item = true;
        unit_message.has_unit_type = true;
        switch(i)
        {
            case 0:
                unit_message.unit_item = WATCH_UNIT_ITEM_enum_WATCH_UNIT_ITEM_DISTANCE;
                if (get_unit_distance())
                {
                    unit_message.unit_type =WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_METRIC ;
                }
                else
                {
                    unit_message.unit_type =WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_INCH ;
                }
                break;
            case 1:
                unit_message.unit_item = WATCH_UNIT_ITEM_enum_WATCH_UNIT_ITEM_ELEVATION;
                if (get_unit_height())
                {
                    unit_message.unit_type =WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_METRIC ;
                }
                else
                {
                    unit_message.unit_type =WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_INCH ;
                }
                break;
            case 2:
                unit_message.unit_item = WATCH_UNIT_ITEM_enum_WATCH_UNIT_ITEM_WEIGHT;
                if (get_unit_weight())
                {
                    unit_message.unit_type =WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_METRIC ;
                }
                else
                {
                    unit_message.unit_type =WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_INCH ;
                }
                break;
            case 3:
                unit_message.unit_item = WATCH_UNIT_ITEM_enum_WATCH_UNIT_ITEM_TEMPERATURE;
                if (get_unit_temperature())
                {
                    unit_message.unit_type =WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_METRIC ;
                }
                else
                {
                    unit_message.unit_type =WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_INCH ;
                }
                break;
            case 4:
                unit_message.unit_item = WATCH_UNIT_ITEM_enum_WATCH_UNIT_ITEM_LENGTH;
                if (get_unit_length())
                {
                    unit_message.unit_type =WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_METRIC ;
                }
                else
                {
                    unit_message.unit_type =WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_INCH ;
                }
                break;
            case 5:
                unit_message.unit_item = WATCH_UNIT_ITEM_enum_WATCH_UNIT_ITEM_POOL_LENGTH;
                if (get_unit_pool_length())
                {
                    unit_message.unit_type =WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_METRIC ;
                }
                else
                {
                    unit_message.unit_type =WATCH_UNIT_TYPE_enum_WATCH_UNIT_TYPE_INCH ;
                }
                break;
            default:
                break;
        }

        status = pb_encode_tag_for_field(stream, field);
        status &= pb_encode_submessage(stream, watch_unit_msg_fields, &unit_message);
    }

    return status;
}
//-------------------------------------------------------------------------------------------
// Function Name : unit_config_send
// Purpose       : 向APP发送单位配置信息
// Param[in]     : None
// Param[out]    : None
// Return type   : static
// Comment       : 2020-05-28
//-------------------------------------------------------------------------------------------
static void unit_config_send()
{
    watch_config_msg config_message;
    watch_unit_msg unit_message;
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    uint8_t pb_crc = 0;

    memset(&config_message, 0, sizeof(watch_config_msg));
    memset (&unit_message, 0, sizeof(watch_unit_msg));
    memset (unit_config, 0, MAX_UNIT_ITEM_NUM * sizeof(unit_config_st));

    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

    config_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_CONFIG;
    config_message.config_service_type = WATCH_CONFIG_SERVICE_TYPE_enum_WATCH_CONFIG_SERVICE_TYPE_UNIT;
    config_message.config_operate_type = WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_GET;

    config_message.has_user_data = false;

    config_message.unit_message.arg = &unit_message;
    config_message.unit_message.funcs.encode = &unit_message_encode;

    //编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data,ONE_FRAME_DATA_LENGTH_MAX_CH0);
    //编码
    pb_encode(&encode_stream, watch_config_msg_fields, &config_message);

    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch0( );

    //对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);
    // rt_hexdump("unit_config_send", 16, data, *length);
    //命令协议 发送通道2
    ble_cmd_end_tx(config_message.service_type, config_message.config_service_type, config_message.config_operate_type, 0, encode_stream.bytes_written, pb_crc);
}

//-------------------------------------------------------------------------------------------
// Function Name : repeated_uint16_decode
// Purpose       : uint16_t 数据解码
// Param[in]     : pb_istream_t *stream
//                 const pb_field_t *field
//                 void **arg
// Param[out]    : None
// Return type   :
// Comment       : 2019-08-16
//-------------------------------------------------------------------------------------------
static bool repeated_uint16_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    uint16_t** ptr = (uint16_t**) arg;
    uint64_t value = 0;

    if (!pb_decode_varint(stream, &value))
        return false;

    **ptr = (uint16_t)value;
    (*ptr) ++;

    return true;
}

//-------------------------------------------------------------------------------------------
// Function Name : repeated_uint8_decode
// Purpose       : uint8_t 数据解码
// Param[in]     : pb_istream_t *stream
//                 const pb_field_t *field
//                 void **arg
// Param[out]    : None
// Return type   :
// Comment       : 2019-08-16
//-------------------------------------------------------------------------------------------
static bool repeated_uint8_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    uint8_t** ptr = (uint8_t**)arg;
    uint64_t value = 0;

    if (!pb_decode_varint(stream, &value))
        return false;

    **ptr = (uint8_t)value;
    (*ptr) ++;

    return true;
}

static user_data s_user_data = { 0 };         //用户信息
static section_data s_sec_data = { 0 };       //区间配置

/**
 * @brief 从cfg_user_info同步用户配置数据到s_user_data
 * 
 * 该函数用于确保s_user_data中的数据与系统配置中的用户信息保持一致
 */
void sync_user_info_from_cfg(void)
{
    user_birth_t birth = {0};
    
    // 同步用户基本信息
    s_user_data.sex = get_user_info_gender();
    s_user_data.height = get_user_info_height();
    s_user_data.weight = get_user_info_weight();
    s_user_data.wear_hand = get_user_info_hand_habit();
    
    // 同步用户年龄/生日信息
    get_user_info_birth(&birth);
    s_user_data.age = birth.year * 10000 + birth.mon * 100 + birth.day; 
    
    // 同步心率相关信息
    s_user_data.run_hr_max = get_user_info_run_hrm_max();
    s_user_data.ride_hr_max = get_user_info_ride_hrm_max();
    s_user_data.swim_hr_max = get_user_info_swim_hrm_max();
    s_user_data.hr_rest = get_user_info_hrm_resting();
    s_user_data.run_hr_lactic_acid = get_user_info_run_hrm_lactic();
    s_user_data.ride_hr_lactic_acid = get_user_info_ride_hrm_lactic();
    s_user_data.swim_hr_lactic_acid = get_user_info_swim_hrm_lactic();
    
    // 同步FTP
    s_user_data.sport_ftp = get_user_info_ftp_ride();
    s_sec_data.FTP = get_user_info_ftp_ride();
    
    // 同步各种区间配置
    // 同步心率区间
    for (uint8_t i = 0; i < HR_ZONE_NUM; i++) {
        s_sec_data.run_hrm_zone[i] = get_user_info_run_hrm_zone(i);
        s_sec_data.ride_hrm_zone[i] = get_user_info_ride_hrm_zone(i);
        s_sec_data.swim_hrm_zone[i] = get_user_info_swim_hrm_zone(i);
    }
    s_sec_data.run_hrm_zone[PB_HR_ZONE_NUM - 1] = get_user_info_run_hrm_max();
    s_sec_data.ride_hrm_zone[PB_HR_ZONE_NUM - 1] = get_user_info_ride_hrm_max();
    s_sec_data.swim_hrm_zone[PB_HR_ZONE_NUM - 1] = get_user_info_swim_hrm_max();

    // 同步功率区间
    for (uint8_t i = 0; i < PWR_ZONE_NUM; i++) {
        s_sec_data.power_zone[i] = get_user_info_pwr_ride_zone(i);
    }
    
    // 同步踏频区间
    for (uint8_t i = 0; i < CAD_ZONE_NUM; i++) {
        s_sec_data.cad_zone[i] = get_user_info_cad_ride_zone(i);
    }
    
    // 同步速度区间
    for (uint8_t i = 0; i < SPD_ZONE_NUM; i++) {
        s_sec_data.spd_zone[i] = get_user_info_spd_ride_zone(i);
    }
}

/**
 * @brief 直接将s_user_data和s_sec_data配置到cfg中
 * 
 * 该函数接收用户数据和区间数据，然后直接将它们写入cfg结构体，
 * 而不通过cfg_user_info.c中的函数接口
 * 
 * @param user_data 用户数据
 * @param sec_data 区间配置数据
 */
void configure_user_info_to_cfg(const user_data *user_data, const section_data *sec_data)
{    
    if (user_data == NULL || sec_data == NULL) {
        WATCH_CONFIG_PB_LOG_E("%s %d: Invalid parameters\n", __func__, __LINE__);
        return;
    }
    
    // 配置用户基本信息
    set_user_info_gender(user_data->sex ? 1 : 0);
    if(user_data->height >= 50 && user_data->height <= 250)
    {
        set_user_info_height((uint8_t)user_data->height);
    }
    if(user_data->weight >= 100 && user_data->weight <= 2500)
    {
        set_user_info_weight((uint16_t)user_data->weight);
    }
    set_user_info_hand_habit(user_data->wear_hand ? 1 : 0);
    
    // 配置生日信息
    if (user_data->age > 0) {
        set_user_info_birth((uint16_t)(user_data->age / 10000), (uint8_t)((user_data->age % 10000) / 100), (uint8_t)(user_data->age % 100));
    }
    
    // 配置心率相关信息
    if(user_data->run_hr_max >= 40 && user_data->run_hr_max <= 220)
    {
        set_user_info_run_hrm_max((uint8_t)user_data->run_hr_max);
    }
    if(user_data->ride_hr_max >= 40 && user_data->ride_hr_max <= 220)
    {
        set_user_info_ride_hrm_max((uint8_t)user_data->ride_hr_max);
    }
    if(user_data->swim_hr_max >= 40 && user_data->swim_hr_max <= 220)
    {
        set_user_info_swim_hrm_max((uint8_t)user_data->swim_hr_max);
    }
    if(user_data->hr_rest >= 40 && user_data->hr_rest <= 220)
    {
        set_user_info_hrm_resting((uint8_t)user_data->hr_rest);
    }
    if(user_data->run_hr_lactic_acid >= 40 && user_data->run_hr_lactic_acid <= 220)
    {
        set_user_info_run_hrm_lactic((uint8_t)user_data->run_hr_lactic_acid);
    }
    if(user_data->ride_hr_lactic_acid >= 40 && user_data->ride_hr_lactic_acid <= 220)
    {
        set_user_info_ride_hrm_lactic((uint8_t)user_data->ride_hr_lactic_acid);
    }
    if(user_data->swim_hr_lactic_acid >= 40 && user_data->swim_hr_lactic_acid <= 220)
    {
        set_user_info_swim_hrm_lactic((uint8_t)user_data->swim_hr_lactic_acid);
    }
    
    // 配置各种区间
    // 心率区间
    for (uint8_t i = 0; i < HR_ZONE_NUM; i++) {
        if(sec_data->run_hrm_zone[i] >= 40 && sec_data->run_hrm_zone[i] <= 240)
        {
            set_user_info_run_hrm_zone(i, (uint8_t)sec_data->run_hrm_zone[i]);  //跑步心率区间
        }
        if(sec_data->ride_hrm_zone[i] >= 40 && sec_data->ride_hrm_zone[i] <= 240)
        {
            set_user_info_ride_hrm_zone(i, (uint8_t)sec_data->ride_hrm_zone[i]);  //骑行心率区间
        }
        if(sec_data->swim_hrm_zone[i] >= 40 && sec_data->swim_hrm_zone[i] <= 240)
        {
            set_user_info_swim_hrm_zone(i, (uint8_t)sec_data->swim_hrm_zone[i]);  //游泳心率区间
        }
    }
    
    // 功率区间
    for (uint8_t i = 0; i < PWR_ZONE_NUM; i++) {
        if(sec_data->power_zone[i] >= 100 && sec_data->power_zone[i] <= 999)
        {
            set_user_info_pwr_ride_zone(i, (uint16_t)sec_data->power_zone[i]);  //功率区间
        }
    }
    
    // 踏频区间
    for (uint8_t i = 0; i < CAD_ZONE_NUM; i++) {
        set_user_info_cad_ride_zone(i, (uint8_t)sec_data->cad_zone[i]);  //踏频区间
    }
    
    // 速度区间
    for (uint8_t i = 0; i < SPD_ZONE_NUM; i++) {
        set_user_info_spd_ride_zone(i, (uint16_t)sec_data->spd_zone[i]);
    }
    // 配置FTP
    if(sec_data->FTP >= 100 && sec_data->FTP <= 999)
    {
        set_user_info_ftp_ride((uint16_t)sec_data->FTP);  //FTP
    }
    WATCH_CONFIG_PB_LOG_D("%s %d: User info configured to cfg successfully\n", __func__, __LINE__);
}

static bool user_section_data_decode(pb_istream_t* stream, const pb_field_t* field, void** arg)
{
    watch_section_data_msg* ptr = (watch_section_data_msg*) (*arg);

    ptr->run_hrm.arg = s_sec_data.run_hrm_zone;
    ptr->run_hrm.funcs.decode = repeated_uint32_decode;

    ptr->cad.arg = s_sec_data.cad_zone;
    ptr->cad.funcs.decode = repeated_uint32_decode;

    ptr->spd.arg = s_sec_data.spd_zone;
    ptr->spd.funcs.decode = repeated_uint32_decode;

    ptr->ride_hrm.arg = s_sec_data.ride_hrm_zone;
    ptr->ride_hrm.funcs.decode = repeated_uint32_decode;

    ptr->swim_hrm.arg = s_sec_data.swim_hrm_zone;
    ptr->swim_hrm.funcs.decode = repeated_uint32_decode;

    ptr->power.arg = s_sec_data.power_zone;
    ptr->power.funcs.decode = repeated_uint32_decode;

    if (!pb_decode(stream, watch_section_data_msg_fields, &ptr))
        return false;

    if (ptr->has_FTP)
        s_sec_data.FTP = ptr->FTP;

    return true;
}

void set_user_msg(watch_user_data_msg *user_data)
{
    if (user_data->has_sex) {
        s_user_data.sex = user_data->sex;
    }
    if (user_data->has_birthday) {
        s_user_data.age = user_data->birthday;
    }
    if (user_data->has_height) {
        s_user_data.height = user_data->height;
    }
    if (user_data->has_weight) {
        s_user_data.weight = user_data->weight;
    }
    if (user_data->has_step_length) {
        s_user_data.step_length = user_data->step_length;
    }
    if (user_data->has_run_hr_max) {
        s_user_data.run_hr_max = user_data->run_hr_max;
    }
    if (user_data->has_hr_rest) {
        s_user_data.hr_rest = user_data->hr_rest;
    }
    if (user_data->has_run_hr_lactic_acid) {
        s_user_data.run_hr_lactic_acid = user_data->run_hr_lactic_acid;
    }
    if (user_data->has_wear_hand) {
        s_user_data.wear_hand = user_data->wear_hand;
    }
    if (user_data->has_ride_hr_max) {
        s_user_data.ride_hr_max = user_data->ride_hr_max;
    }
    if (user_data->has_swim_hr_max) {
        s_user_data.swim_hr_max = user_data->swim_hr_max;
    }
    if (user_data->has_ride_hr_lactic_acid) {
        s_user_data.ride_hr_lactic_acid = user_data->ride_hr_lactic_acid;
    }
    if (user_data->has_swim_hr_lactic_acid) {
        s_user_data.swim_hr_lactic_acid = user_data->swim_hr_lactic_acid;
    }

    if (user_data->section_data.has_FTP)
    {
        s_sec_data.FTP = user_data->section_data.FTP;
    }
    configure_user_info_to_cfg(&s_user_data, &s_sec_data);
    service_sync_user_info_to_algo(); // 同步用户信息到算法
    set_treadmill_calibratio_distance(INT32_MAX); //身高信息改变，重置校准跑步机距离
    menu_list_refresh_page("PersonalProfile", 0);//页面更新个人资料
    WATCH_CONFIG_PB_LOG_D("BACK_SERVICE_TYPE_USER_INFO sex = %d age = %d height = %d weight = %d\r\n",
        user_data->sex, user_data->age, user_data->height, user_data->weight);
}

static bool run_hrm_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    uint8_t status = false;
    uint32_t* ptr = (uint32_t*) (*arg);

    for (uint8_t i = 0; i < PB_HR_ZONE_NUM; i++)
    {
        status = pb_encode_tag_for_field(stream, field);
        status &= pb_encode_varint(stream, (uint64_t) (*ptr));
        ptr++;
    }

    return status;
}

static bool cad_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    uint8_t status = false;
    uint32_t* ptr = (uint32_t*) (*arg);

    // 遍历7个元素
    for (uint8_t i = 0; i < CAD_ZONE_NUM; i++)
    {
        status = pb_encode_tag_for_field(stream, field);
        status &= pb_encode_varint(stream, (uint64_t) (*ptr));

        ptr++;
    }

    return status;
}

static bool spd_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    uint8_t status = false;
    uint32_t* ptr = (uint32_t*) (*arg);

    for (uint8_t i = 0; i < SPD_ZONE_NUM; i++)
    {
        status = pb_encode_tag_for_field(stream, field);
        status &= pb_encode_varint(stream, (uint64_t) (*ptr));

        ptr++;
    }

    return status;
}

static bool power_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    uint8_t status = false;
    uint32_t* ptr = (uint32_t*) (*arg);

    for (uint8_t i = 0; i < PWR_ZONE_NUM; i++)
    {
        status = pb_encode_tag_for_field(stream, field);
        status &= pb_encode_varint(stream, (uint64_t) (*ptr));

        ptr++;
    }

    return status;
}

void send_user_msg()
{
    watch_config_msg watch_config_message;
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    uint8_t pb_crc = 0;

    memcpy(&s_user_data.section_data, &s_sec_data, sizeof(section_data));
    
    memset(&watch_config_message, 0, sizeof(watch_config_msg));
    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

    watch_config_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_CONFIG;
    watch_config_message.config_service_type = WATCH_CONFIG_SERVICE_TYPE_enum_WATCH_CONFIG_SERVICE_TYPE_USER;
    watch_config_message.config_operate_type = WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_GET;

    watch_config_message.has_user_data = true;

    watch_config_message.user_data.has_birthday = true;
    watch_config_message.user_data.birthday = s_user_data.age;
    watch_config_message.user_data.has_height = true;
    watch_config_message.user_data.height = s_user_data.height;

    watch_config_message.user_data.has_sex = true;
    watch_config_message.user_data.sex = s_user_data.sex;

    watch_config_message.user_data.has_weight = true;
    watch_config_message.user_data.weight = s_user_data.weight;

    watch_config_message.user_data.has_step_length = false;
    watch_config_message.user_data.step_length = 0;

    watch_config_message.user_data.has_run_hr_max = true;
    watch_config_message.user_data.run_hr_max = s_user_data.run_hr_max;

    watch_config_message.user_data.has_hr_rest = true;
    watch_config_message.user_data.hr_rest = s_user_data.hr_rest;

    watch_config_message.user_data.has_run_hr_lactic_acid = true;
    watch_config_message.user_data.run_hr_lactic_acid = s_user_data.run_hr_lactic_acid;

    watch_config_message.user_data.has_wear_hand = true;
    watch_config_message.user_data.wear_hand = s_user_data.wear_hand;

    watch_config_message.user_data.has_ride_hr_max = true;
    watch_config_message.user_data.ride_hr_max = s_user_data.ride_hr_max;

    watch_config_message.user_data.has_swim_hr_max = true;
    watch_config_message.user_data.swim_hr_max = s_user_data.swim_hr_max;

    watch_config_message.user_data.has_ride_hr_lactic_acid = true;
    watch_config_message.user_data.ride_hr_lactic_acid = s_user_data.ride_hr_lactic_acid;

    watch_config_message.user_data.has_swim_hr_lactic_acid = true;
    watch_config_message.user_data.swim_hr_lactic_acid = s_user_data.swim_hr_lactic_acid;

    watch_config_message.user_data.has_timestamp = false;
    watch_config_message.user_data.timestamp = 0;

    // section data
    watch_config_message.user_data.has_section_data = true;

    watch_config_message.user_data.section_data.has_FTP = true;
    watch_config_message.user_data.section_data.FTP = s_sec_data.FTP;

    // 确保将正确的值传递给编码函数
    watch_config_message.user_data.section_data.run_hrm.arg = s_sec_data.run_hrm_zone;
    watch_config_message.user_data.section_data.run_hrm.funcs.encode = &run_hrm_encode;

    watch_config_message.user_data.section_data.cad.arg = s_sec_data.cad_zone;
    watch_config_message.user_data.section_data.cad.funcs.encode = &cad_encode;

    watch_config_message.user_data.section_data.spd.arg = s_sec_data.spd_zone;
    watch_config_message.user_data.section_data.spd.funcs.encode = &spd_encode;

    watch_config_message.user_data.section_data.ride_hrm.arg = s_sec_data.ride_hrm_zone;
    watch_config_message.user_data.section_data.ride_hrm.funcs.encode = &run_hrm_encode;

    watch_config_message.user_data.section_data.swim_hrm.arg = s_sec_data.swim_hrm_zone;
    watch_config_message.user_data.section_data.swim_hrm.funcs.encode = &run_hrm_encode;

    watch_config_message.user_data.section_data.power.arg = s_sec_data.power_zone;
    watch_config_message.user_data.section_data.power.funcs.encode = &power_encode;

    watch_config_message.user_data.has_update_status = true;
    watch_config_message.user_data.update_status = user_info_update_flag;

    if (user_info_update_flag)
    {
        user_info_update_flag = false;
    }

    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    //编码
    pb_encode(&encode_stream, watch_config_msg_fields, &watch_config_message);

    *length = encode_stream.bytes_written;
        ble_nus_data_tx_ch0( );

    //对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);

    //命令协议 发送通道2
    ble_cmd_end_tx(watch_config_message.service_type, watch_config_message.config_service_type,
        watch_config_message.config_operate_type, 0, encode_stream.bytes_written, pb_crc);
}

//-------------------------------------------------------------------------------------------
// Function Name : save_page_config_to_kvdb
// Purpose       : 将页面配置保存到KVDB
// Param[in]     : void
// Param[out]    : None
// Return type   : void
// Comment       : 2025/03/25
//-------------------------------------------------------------------------------------------
static int save_page_config(watch_page_msg *page_config)
{
    SPORTTYPE sport_type = cur_operate_mode;
    uint8_t page_order[GRAPH_PAGE_ALL_TOTAL] = {0};
    uint8_t valid_page_count = 0;
    
    // 1. 转换页面布局和字段信息
    for (uint8_t i = 0; i < sport_page_index; i++)
    {
        watch_page_msg *current_page = &page_config[i];
        uint8_t page_index = 0;
        uint8_t page_type = 0;

        if (current_page->has_page_type)
        {
            page_type = current_page->page_type;
        }

        switch (page_type)
        {
            case WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_DATA:
                page_index = current_page->page_index;
                break;
            case WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_HRM:
                page_index = GRAPH_PAGE_HRM;
                break;
            case WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_ALT:
                page_index = GRAPH_PAGE_ALT;
                break;
            case WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_WKT_MONITOR:
                page_index = GRAPH_PAGE_WKT_MONITOR;
                break;
            case WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_NAVI:
                page_index = GRAPH_PAGE_NAVI;
                break;
            case WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_STAGES:
                page_index = GRAPH_PAGE_STAGE;
                break;
            case WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_WKT:
                page_index = GRAPH_PAGE_WKT;
                break;
            case WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_WKT_FEC:
                page_index = GRAPH_PAGE_WKT_FEC;
                break;
            case WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_NAVI_ALT:
                page_index = GRAPH_CONST_PAGE_NAVI_ALT;
                break;
            case WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_NAVI_INFO:
                page_index = GRAPH_CONST_PAGE_NAVI_INFO;
                break;
            case WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_COMPASS:
                page_index = GRAPH_CONST_PAGE_COMPASS;
                break;
            case WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_LAP:
                page_index = GRAPH_CONST_PAGE_LAP;
                break;
            case WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_BUNNY:
                page_index = GRAPH_CONST_PAGE_BUNNY;
                break;
            case WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_OTHER:
                page_index = GRAPH_CONST_PAGE_OTHER;
                break;
            default:
                WATCH_CONFIG_PB_LOG_E("%s %d: Invalid page type: %d\n", __func__, __LINE__, page_type);
                return -1;
        }

        data_grid_page_index++;

        if (page_index < GRAPH_PAGE_ALL_TOTAL)
        {
            // 保存页面顺序
            page_order[valid_page_count++] = page_index;

            // 设置页面状态
            if (current_page->has_status)
            {
                set_sport_page_status(sport_type, page_index, current_page->status);
            }
        
            // 设置页面布局,只有数据页才支持配置页面布局layout 
            if (page_index < MAX_DATA_PAGE_NUM)
            {
                if (current_page->has_layout_mode)
                {
                    if (current_page->layout_mode.has_data_count
                        && current_page->layout_mode.has_style)
                    {
                        if (current_page->layout_mode.data_count == 1 && current_page->layout_mode.style == 0)
                        {
                            set_sport_page_layout(sport_type, page_index, LAYOUT_1);
                        }
                        else if (current_page->layout_mode.data_count == 2 && current_page->layout_mode.style == 0)
                        {
                            set_sport_page_layout(sport_type, page_index, LAYOUT_2);
                        }
                        else if (current_page->layout_mode.data_count == 3 && current_page->layout_mode.style == 0)
                        {
                            set_sport_page_layout(sport_type, page_index, LAYOUT_3A);
                        }
                        else if (current_page->layout_mode.data_count == 3 && current_page->layout_mode.style == 1)
                        {
                            set_sport_page_layout(sport_type, page_index, LAYOUT_3B);
                        }
                        else if (current_page->layout_mode.data_count == 4 && current_page->layout_mode.style == 0)
                        {
                            set_sport_page_layout(sport_type, page_index, LAYOUT_4A);
                        }
                        else if (current_page->layout_mode.data_count == 4 && current_page->layout_mode.style == 1)
                        {
                            set_sport_page_layout(sport_type, page_index, LAYOUT_4B);
                        }
                        else if (current_page->layout_mode.data_count == 4 && current_page->layout_mode.style == 2)
                        {
                            set_sport_page_layout(sport_type, page_index, LAYOUT_4C);
                        }
                        else if (current_page->layout_mode.data_count == 5 && current_page->layout_mode.style == 0)
                        {
                            set_sport_page_layout(sport_type, page_index, LAYOUT_5);
                        }
                        else if (current_page->layout_mode.data_count == 6 && current_page->layout_mode.style == 0)
                        {
                            set_sport_page_layout(sport_type, page_index, LAYOUT_6);
                        }
                    }
                }
            }

            
            // 设置普通数据页数据
            if (page_index < MAX_DATA_PAGE_NUM)
            {
                for (uint8_t j = 0; j < MAX_PAGE_DATA_ITEMS; j++)
                {
                    if(page_index < GRAPH_PAGE_TOTAL)
                    {                    
                        set_sport_page_data_type(sport_type, page_index, j, page_data[i][j]);
                    }
                }
            }
            else if ((page_index >= GRAPH_PAGE_HRM && page_index <= GRAPH_PAGE_WKT_FEC))
            {
                set_sport_page_data_type(sport_type, page_index, 1, page_data[i][0]);
            }
            
            // 设置主页
            if (current_page->has_home_page)
            {
                if (current_page->home_page)
                {
                    set_sport_home_page(sport_type, page_index);
                }
            }
        }
    }

    // 保存页面顺序
    if (valid_page_count > 0) {
        set_sport_page_order(sport_type, page_order, valid_page_count);
    }
    
    WATCH_CONFIG_PB_LOG_D("%s %d: Save page config, sport_type=%d\n", __func__, __LINE__, sport_type);
    return 0;
}

static bool page_data_fields_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    uint8_t i = 0;
    uint8_t status = true;
    uint8_t *page_index = (uint8_t *)*arg;

    // 使用current_data_count来决定编码的数据项数量
    // 如果current_data_count为0，使用默认值以防止错误
    uint8_t data_count = (current_data_count > 0) ? current_data_count : MAX_DATA_PAGE_ITEM_NUM;

    for (i = 0; i < data_count; i++)
    {
        status = pb_encode_tag_for_field(stream, field);
        
        // 对于图表页(GRAPH_PAGE_HRM到GRAPH_PAGE_WKT_FEC)，数据存储在索引1；对于普通页，使用循环索引i
        uint8_t data_index = ((*page_index >= GRAPH_PAGE_HRM) && (*page_index <= GRAPH_PAGE_WKT_FEC)) ? 1 : i;
        status &= pb_encode_varint(stream, get_sport_page_data_type(cur_operate_mode, *page_index, data_index));
    }

    return status;
}

//-------------------------------------------------------------------------------------------
// Function Name : page_data_encode
// Purpose       : 页面配置编码
// Param[in]     : pb_ostream_t *stream     
//                 const pb_field_t *field  
//                 void *const *arg         
// Param[out]    : None 
// Return type   : bool
// Comment       : 2025/03/25   
//-------------------------------------------------------------------------------------------
static bool page_data_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    uint8_t status = false;
    SPORTTYPE sport_type = cur_operate_mode;
    watch_page_msg *p_page_config = (watch_page_msg *)*arg;

    // 遍历所有页面，按照page_order顺序填充
    for (uint8_t order_idx = 0; order_idx < GRAPH_PAGE_ALL_TOTAL; order_idx++)
    {
        // 从page_order获取页面类型
        uint8_t i = get_sport_webgrid_page_type_by_order(sport_type, order_idx);
        
        // 确保页面类型有效
        if (i >= GRAPH_PAGE_ALL_TOTAL) {
            continue;
        }

        // 检查当前运动类型是否支持该页面类型，不支持则跳过
        if (!is_sport_page_supported(sport_type, i)) {
            continue;
        }

        memset(&p_page_config[order_idx], 0, sizeof(watch_page_msg));
        // 设置页面索引
        p_page_config[order_idx].page_index = i;

        // 设置页面状态
        p_page_config[order_idx].has_status = true;
        p_page_config[order_idx].status = get_sport_page_status(sport_type, i);

        // 设置页面布局
        if (i < GRAPH_PAGE_ALL_TOTAL)
        {
            p_page_config[order_idx].has_page_type = true;
            if (i < MAX_DATA_PAGE_NUM)
            {
                p_page_config[order_idx].page_type = WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_DATA;
            }
            else
            {
                switch (i)
                {
                    case GRAPH_PAGE_HRM:
                        p_page_config[order_idx].page_type = WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_HRM;
                        break;
                    case GRAPH_PAGE_ALT:
                        p_page_config[order_idx].page_type = WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_ALT;
                        break;
                    case GRAPH_PAGE_WKT_MONITOR:
                        p_page_config[order_idx].page_type = WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_WKT_MONITOR;
                        break;
                    case GRAPH_PAGE_NAVI:
                        p_page_config[order_idx].page_type = WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_NAVI;
                        break;
                    case GRAPH_PAGE_STAGE:
                        p_page_config[order_idx].page_type = WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_STAGES;
                        break;
                    case GRAPH_PAGE_WKT:
                        p_page_config[order_idx].page_type = WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_WKT;
                        break;
                    case GRAPH_PAGE_WKT_FEC:
                        p_page_config[order_idx].page_type = WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_WKT_FEC;
                        break;
                    case GRAPH_CONST_PAGE_NAVI_ALT:
                        p_page_config[order_idx].page_type = WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_NAVI_ALT;
                        break;
                    case GRAPH_CONST_PAGE_NAVI_INFO:
                        p_page_config[order_idx].page_type = WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_NAVI_INFO;
                        break;
                    case GRAPH_CONST_PAGE_COMPASS:
                        p_page_config[order_idx].page_type = WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_COMPASS;
                        break;
                    case GRAPH_CONST_PAGE_LAP:
                        p_page_config[order_idx].page_type = WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_LAP;
                        break;
                    case GRAPH_CONST_PAGE_BUNNY:
                        p_page_config[order_idx].page_type = WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_BUNNY;
                        break;
                    case GRAPH_CONST_PAGE_OTHER:
                        p_page_config[order_idx].page_type = WATCH_PAGE_TYPE_enum_WATCH_PAGE_TYPE_OTHER;
                        break;
                    default:
                        break;
                }
            }
            p_page_config[order_idx].has_layout_mode = true;
            p_page_config[order_idx].layout_mode.has_data_count = true;
            p_page_config[order_idx].layout_mode.has_style = true;

            uint8_t layout = get_sport_page_layout(sport_type, i);
            switch (layout)
            {
                case LAYOUT_1:
                    p_page_config[order_idx].layout_mode.data_count = 1;
                    p_page_config[order_idx].layout_mode.style = 0;
                    break;
                case LAYOUT_2:
                    p_page_config[order_idx].layout_mode.data_count = 2;
                    p_page_config[order_idx].layout_mode.style = 0;
                    break;
                case LAYOUT_3A:
                    p_page_config[order_idx].layout_mode.data_count = 3;
                    p_page_config[order_idx].layout_mode.style = 0;
                    break;
                case LAYOUT_3B:
                    p_page_config[order_idx].layout_mode.data_count = 3;
                    p_page_config[order_idx].layout_mode.style = 1;
                    break;
                case LAYOUT_4A:
                    p_page_config[order_idx].layout_mode.data_count = 4;
                    p_page_config[order_idx].layout_mode.style = 0;
                    break;
                case LAYOUT_4B:
                    p_page_config[order_idx].layout_mode.data_count = 4;
                    p_page_config[order_idx].layout_mode.style = 1;
                    break;
                case LAYOUT_4C:
                    p_page_config[order_idx].layout_mode.data_count = 4;
                    p_page_config[order_idx].layout_mode.style = 2;
                    break;
                case LAYOUT_5:
                    p_page_config[order_idx].layout_mode.data_count = 5;
                    p_page_config[order_idx].layout_mode.style = 0;
                    break;
                case LAYOUT_6:
                    p_page_config[order_idx].layout_mode.data_count = 6;
                    p_page_config[order_idx].layout_mode.style = 0;
                    break;
                case LAYOUT_GRID_1://图形化样式1 3行 整行 * 2 + 整行 * 1
                    p_page_config[order_idx].layout_mode.data_count = 1;
                    p_page_config[order_idx].layout_mode.style = 0;
                    break;
                case LAYOUT_GRID_2://图形化样式2 4行 整行 * 3 + 整行 * 1
                    p_page_config[order_idx].layout_mode.data_count = 1;
                    p_page_config[order_idx].layout_mode.style = 0;
                    break;
                default:
                    break;
            }

            if ((i >= GRAPH_CONST_PAGE_NAVI_ALT) && (i <= GRAPH_CONST_PAGE_OTHER))
            {
                p_page_config[order_idx].layout_mode.data_count = 0;
                p_page_config[order_idx].layout_mode.style = 0;
            }

            // 设置数据项前，更新当前页面的数据项数量
            current_data_count = p_page_config[order_idx].layout_mode.data_count;
            if (current_data_count > 0)
            {
                p_page_config[order_idx].data.arg = &i; // 注意这里传递的是页面类型而不是顺序索引
                p_page_config[order_idx].data.funcs.encode = page_data_fields_encode;
            }
        }

        // 设置主页
        p_page_config[order_idx].has_home_page = true;
        p_page_config[order_idx].home_page = (get_sport_home_page(sport_type) == i);

        // 编码当前页面配置
        status = pb_encode_tag_for_field(stream, field);
        status &= pb_encode_submessage(stream, watch_page_msg_fields, &p_page_config[order_idx]);
    }

    return status;
}

//-------------------------------------------------------------------------------------------
// Function Name : page_data_config_send
// Purpose       : 发送页面配置请求
// Param[in]     : void
// Param[out]    : None
// Return type   : bool
// Comment       : 2025/03/25   
//-------------------------------------------------------------------------------------------
static void page_data_config_send(void)
{
    watch_config_msg config_message;
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    uint8_t pb_crc = 0;

    memset(&config_message, 0, sizeof(watch_config_msg));
    memset(&page_config, 0, sizeof(watch_page_msg) * GRAPH_PAGE_ALL_TOTAL);

    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

    config_message.page_message.arg = &page_config;
    config_message.page_message.funcs.encode = &page_data_encode;

    config_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_CONFIG;
    config_message.config_service_type = WATCH_CONFIG_SERVICE_TYPE_enum_WATCH_CONFIG_SERVICE_TYPE_PAGE;
    config_message.config_operate_type = WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_GET;

    config_message.has_cur_operate_mode = true;
    config_message.cur_operate_mode = map_sporttype_to_watch_menu_sporttype(cur_operate_mode);

    // 编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    // 编码
    pb_encode(&encode_stream, watch_config_msg_fields, &config_message);

    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch0();

    // 对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);
    // rt_hexdump("page_data_config_send", 16, data, *length);
    ble_cmd_end_tx(config_message.service_type, config_message.config_service_type, 
        config_message.config_operate_type, 0, encode_stream.bytes_written, pb_crc);
}

static bool alarm_select_info_message_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    if (stream == NULL || field == NULL || arg == NULL)
    {
        return false;
    }

    watch_alarm_select_info *alarm_select_info_st = (watch_alarm_select_info *)*arg;
    if (alarm_select_index >= _WATCH_ALARM_SELECT_ARRAYSIZE)
    {
        alarm_select_index = 0; // 重置索引以防止越界
    }

    memset(&alarm_select_info_st[alarm_select_index], 0, sizeof(watch_alarm_select_info));

    if (!pb_decode(stream, watch_alarm_select_info_fields, &alarm_select_info_st[alarm_select_index]))
    {
        return false;
    }

    alarm_select_index++;
    return true;
}

static bool alarm_set_message_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    if (stream == NULL || field == NULL || arg == NULL)
    {
        WATCH_CONFIG_PB_LOG_E("%s %d: Invalid parameters\n", __func__, __LINE__);
        return false;
    }

    watch_alarm_params_set_msg *alarm_msg_st = (watch_alarm_params_set_msg *)*arg;
    if (alarm_index >= _WATCH_ALARM_TYPE_ARRAYSIZE)
    {
        return false;
    }
    

    memset(&alarm_msg_st[alarm_index], 0, sizeof(watch_alarm_params_set_msg));
    
    alarm_msg_st[alarm_index].select_info.arg = &alarm_select_info;
    alarm_msg_st[alarm_index].select_info.funcs.decode = &alarm_select_info_message_decode;
    
    alarm_select_index = 0;

    if (!pb_decode(stream, watch_alarm_params_set_msg_fields, &alarm_msg_st[alarm_index]))
    {
        return false;
    }
    alarm_index++;
    return true;
}

static bool alarm_params_select_info_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    bool status = true;
    watch_alarm_select_info *alarm_select_info_st = (watch_alarm_select_info *)*arg;

    for (uint8_t i = 0; i < _WATCH_ALARM_SELECT_ARRAYSIZE; i++)
    {
        alarm_select_info_st[i].alarm_select = i + 1;
        if ((alarm_select_info_st[i].alarm_select != WATCH_ALARM_SELECT_WATCH_ALARM_SELECT_CALORIE)
            && (alarm_select_info_st[i].alarm_select != WATCH_ALARM_SELECT_WATCH_ALARM_SELECT_TIME))//
        {
            continue;
        }
        alarm_select_info_st[i].has_alarm_select = true;
        alarm_select_info_st[i].has_value = true;
        if (alarm_select_info_st[i].alarm_select == WATCH_ALARM_SELECT_WATCH_ALARM_SELECT_CALORIE)
        {
            alarm_select_info_st[i].value = get_sport_remind_value(cur_operate_mode, SPORT_REMIND_SUPPLY, false);//热量
        }
        else
        {
            alarm_select_info_st[i].value = get_sport_remind_value(cur_operate_mode, SPORT_REMIND_SUPPLY, true);//时间
        }
        
        // 设置状态
        alarm_select_info_st[i].has_status = true;
        if (get_sport_supply_mode(cur_operate_mode) == 0 
            && alarm_select_info_st[i].alarm_select == WATCH_ALARM_SELECT_WATCH_ALARM_SELECT_CALORIE) // 热量
        {
            alarm_select_info_st[i].status = true;
        }
        else
        {
            alarm_select_info_st[i].status = false;
        }
        
        if (get_sport_supply_mode(cur_operate_mode) == 1
            && alarm_select_info_st[i].alarm_select == WATCH_ALARM_SELECT_WATCH_ALARM_SELECT_TIME) // 时间
        {
            alarm_select_info_st[i].status = true;
        }
        else
        {
            alarm_select_info_st[i].status = false;
        }
        
        // 编码
        status &= pb_encode_tag_for_field(stream, field);
        status &= pb_encode_submessage(stream, watch_alarm_select_info_fields, &alarm_select_info_st[i]);
    }
    
    return status;
}

static bool alarm_params_set_message_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    bool status = true;
    watch_alarm_params_set_msg *alarm_params_set_msg_st = (watch_alarm_params_set_msg *)*arg;
    // 清空全局变量
    memset(alarm_select_info, 0, sizeof(alarm_select_info));
    
    for (uint8_t i = 0; i < _WATCH_ALARM_TYPE_MAX; i++)
    {
        SPORT_REMIND_TYPE remind_type = SPORT_REMIND_MAX;
        
        alarm_params_set_msg_st[i].alarm_type = i + 1;
        
        memset(&alarm_params_set_msg_st[i].select_info, 0, sizeof(alarm_params_set_msg_st[i].select_info));
        // 只有补给提醒类型才设置select_info
        if (alarm_params_set_msg_st[i].alarm_type == WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_FEED)
        {
            alarm_params_set_msg_st[i].select_info.arg = &alarm_select_info;
            alarm_params_set_msg_st[i].select_info.funcs.encode = &alarm_params_select_info_encode;
            alarm_params_set_msg_st[i].status = get_sport_remind_en(cur_operate_mode, SPORT_REMIND_SUPPLY, MAIN_EN);
        }

        
        // 根据警示提醒类型设置相应的提醒类型
        switch (alarm_params_set_msg_st[i].alarm_type)
        {
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_TIME:
                remind_type = SPORT_REMIND_TIME;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_DISTANCE:
                remind_type = SPORT_REMIND_DISTANCE;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_HRM:
                remind_type = SPORT_REMIND_HRM;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_CAD:
                remind_type = SPORT_REMIND_CAD;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_PWR:
                remind_type = SPORT_REMIND_POWER;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_CONSUME:
                remind_type = SPORT_REMIND_CONSUME;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_SPEED:
                remind_type = SPORT_REMIND_SPEED;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_PACE:
                remind_type = SPORT_REMIND_PACE;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_STEP_RATE:
                remind_type = SPORT_REMIND_STEP_RATE;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_STEP_NUM:
                remind_type = SPORT_REMIND_STEP_NUM;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_FEED:
                remind_type = SPORT_REMIND_SUPPLY;
                break;
            default:
                remind_type = SPORT_REMIND_MAX;
                break;
        }

        switch (remind_type)
        {
            case SPORT_REMIND_STEP_RATE:
            case SPORT_REMIND_HRM:
            case SPORT_REMIND_CAD:
            case SPORT_REMIND_POWER:
            case SPORT_REMIND_SPEED:
            case SPORT_REMIND_PACE:
            {
                alarm_params_set_msg_st[i].has_value_min = true;
                alarm_params_set_msg_st[i].value_min = get_sport_remind_value(cur_operate_mode, remind_type, false);
                if (remind_type != SPORT_REMIND_STEP_RATE)
                {
                    alarm_params_set_msg_st[i].has_value_min_en = true;
                    alarm_params_set_msg_st[i].value_min_en = get_sport_remind_en(cur_operate_mode, remind_type, LOW_EN);

                    alarm_params_set_msg_st[i].has_value_max_en = true;
                    alarm_params_set_msg_st[i].value_max_en = get_sport_remind_en(cur_operate_mode, remind_type, HIGH_EN);
                }
            }
            case SPORT_REMIND_STEP_NUM:
            case SPORT_REMIND_CONSUME:
            case SPORT_REMIND_TIME:
            case SPORT_REMIND_DISTANCE:
            {
                alarm_params_set_msg_st[i].has_value_max = true;
                alarm_params_set_msg_st[i].value_max = get_sport_remind_value(cur_operate_mode, remind_type, true);

                alarm_params_set_msg_st[i].status = get_sport_remind_en(cur_operate_mode, remind_type, MAIN_EN);
            }
            break;
            default:
            break;
        }
        if (remind_type != SPORT_REMIND_MAX)
        {
            status &= pb_encode_tag_for_field(stream, field);
            status &= pb_encode_submessage(stream, watch_alarm_params_set_msg_fields, &alarm_params_set_msg_st[i]);
        }
    }

    return status;
}

static bool alarm_message_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    bool status = true;
    watch_alarm_msg *alarm_msg_st = (watch_alarm_msg *)*arg;
    // 初始化alarm_msg_st指向的内存
    memset(alarm_msg_st, 0, sizeof(watch_alarm_msg));
    // 使用全局的警示提醒配置数据
    alarm_msg_st->set_msg.arg = &alarm_params_set_message;
    alarm_msg_st->set_msg.funcs.encode = &alarm_params_set_message_encode;
    alarm_msg_st->has_alarm_interval_time = true;
    // 获取实际间隔时间，而不是固定值
    alarm_msg_st->alarm_interval_time = 30; // TODO: 获取间隔时间

    status &= pb_encode_tag_for_field(stream, field);
    status &= pb_encode_submessage(stream, watch_alarm_msg_fields, alarm_msg_st);

    return status;
}

static void alarm_message_set(watch_alarm_msg *alarm_msg_st, 
                             watch_alarm_params_set_msg *alarm_params_set_msg_st,
                             watch_alarm_select_info *alarm_select_info_st)
{
    if (alarm_msg_st == NULL || alarm_params_set_msg_st == NULL || alarm_select_info_st == NULL)
    {
        WATCH_CONFIG_PB_LOG_D("%s %d: input param is NULL\n", __func__, __LINE__);
        return;
    }

    if (alarm_msg_st->has_alarm_interval_time)
    {
        //TODO: 设置同类型警示提醒间隔时间
    }

    // 遍历所有警示提醒类型
    for (uint8_t i = 0; i < alarm_index; i++)
    {
        SPORT_REMIND_TYPE remind_type = SPORT_REMIND_MAX;
        watch_alarm_params_set_msg *current_alarm = &alarm_params_set_msg_st[i];
        
        // 检查当前警示提醒是否有效
        if (current_alarm->alarm_type == WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_INVALID)
        {
            continue;
        }

        // 根据警示提醒类型设置对应的remind_type
        switch (current_alarm->alarm_type)
        {
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_TIME:
                remind_type = SPORT_REMIND_TIME;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_DISTANCE:
                remind_type = SPORT_REMIND_DISTANCE;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_HRM:
                remind_type = SPORT_REMIND_HRM;
                break;  
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_CAD:
                remind_type = SPORT_REMIND_CAD;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_PWR:
                remind_type = SPORT_REMIND_POWER;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_CONSUME:
                remind_type = SPORT_REMIND_CONSUME;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_SPEED:
                remind_type = SPORT_REMIND_SPEED;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_PACE:
                remind_type = SPORT_REMIND_PACE;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_STEP_RATE:
                remind_type = SPORT_REMIND_STEP_RATE;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_STEP_NUM:
                remind_type = SPORT_REMIND_STEP_NUM;
                break;
            case WATCH_ALARM_TYPE_WATCH_ALARM_TYPE_FEED:
                remind_type = SPORT_REMIND_SUPPLY;
                break;
            default:
                remind_type = SPORT_REMIND_MAX;
                break;
        }

        if (remind_type == SPORT_REMIND_MAX)
        {
            WATCH_CONFIG_PB_LOG_D("%s %d: remind_type is SPORT_REMIND_MAX for alarm_type %d\n", 
                __func__, __LINE__, current_alarm->alarm_type);
            continue;
        }

        WATCH_CONFIG_PB_LOG_D("%s %d: cur_operate_mode:%d, remind_type:%d\n", 
            __func__, __LINE__, cur_operate_mode, remind_type);

        // 设置警示提醒的最大值
        if (current_alarm->has_value_max)
        {
            set_sport_remind_custom_value(cur_operate_mode, remind_type, true, current_alarm->value_max);
        }


        // 设置警示提醒的最小值
        switch (remind_type)
        {
            case SPORT_REMIND_HRM://心率
            case SPORT_REMIND_PACE://配速
            case SPORT_REMIND_SPEED://速度
            case SPORT_REMIND_STEP_RATE://步频
            case SPORT_REMIND_CAD://踏频
            case SPORT_REMIND_POWER://功率
            if (current_alarm->has_value_min)
            {
                set_sport_remind_custom_value(cur_operate_mode, remind_type, false, current_alarm->value_min);
            }
            break;
        
            default:
            break;
        }

        // 设置警示提醒的主开关
        if (current_alarm->status)
        {
            set_sport_remind_en(cur_operate_mode, remind_type, MAIN_EN, true);
        }
        else
        {
            set_sport_remind_en(cur_operate_mode, remind_type, MAIN_EN, false);
        }

        // 设置警示提醒的最大值开关
        switch (remind_type)
        {
            case SPORT_REMIND_HRM://心率
            case SPORT_REMIND_PACE://配速
            case SPORT_REMIND_SPEED://速度
            case SPORT_REMIND_CAD://踏频
            case SPORT_REMIND_POWER://功率
            if (current_alarm->has_value_max_en)
            {
                set_sport_remind_en(cur_operate_mode, remind_type, HIGH_EN, current_alarm->value_max_en);
            }
            break;
            default:
            break;  
        }
        
        // 设置警示提醒的最小值开关
        switch (remind_type)
        {
            case SPORT_REMIND_HRM://心率
            case SPORT_REMIND_PACE://配速
            case SPORT_REMIND_SPEED://速度
            case SPORT_REMIND_CAD://踏频
            case SPORT_REMIND_POWER://功率
            if (current_alarm->has_value_min_en)
            {
                set_sport_remind_en(cur_operate_mode, remind_type, LOW_EN, current_alarm->value_min_en);
            }
            break;
            default:
            break;
        }

        // 处理补给提醒的特殊情况
        if (remind_type == SPORT_REMIND_SUPPLY)
        {
            // 遍历alarm_select_info数组处理补给提醒的选择信息
            for (uint8_t j = 0; j < alarm_select_index; j++)
            {
                watch_alarm_select_info *current_select = &alarm_select_info_st[j];
                
                if (!current_select->has_alarm_select)
                {
                    continue;
                }

                switch (current_select->alarm_select)
                {
                    case WATCH_ALARM_SELECT_WATCH_ALARM_SELECT_TIME://时间
                        if (current_select->has_value)
                        {
                            set_sport_remind_custom_value(cur_operate_mode, SPORT_REMIND_SUPPLY, true, current_select->value);
                        }
                        if (current_select->has_status)
                        {
                            if (current_select->status)
                            {
                                set_sport_supply_mode(cur_operate_mode, 1);
                            }
                            else
                            {
                                set_sport_supply_mode(cur_operate_mode, 0);
                            }
                        }
                        break;
                    
                    case WATCH_ALARM_SELECT_WATCH_ALARM_SELECT_CALORIE:
                        if (current_select->has_value)
                        {
                            set_sport_remind_custom_value(cur_operate_mode, SPORT_REMIND_SUPPLY, false, current_select->value);
                        }
                        if (current_select->has_status)
                        {
                            if (current_select->status)
                            {
                                set_sport_supply_mode(cur_operate_mode, 0);
                            }
                            else
                            {
                                set_sport_supply_mode(cur_operate_mode, 1);
                            }
                        }
                        break;
                    default:
                        break;
                }
            }
        }
    }
    alarm_index = 0;
    alarm_select_index = 0;
}

    //-------------------------------------------------------------------------------------------
    // Function Name : alarm_message_decode
    // Purpose       : 警示提醒解码函数
    // Param[in]     : pb_istream_t *stream
    //                 const pb_field_t *field
    //                 void **arg
    // Param[out]    : None
    // Return type   : static
    // Comment       : 2025-03-25
    //-------------------------------------------------------------------------------------------
static bool alarm_message_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    if (stream == NULL || field == NULL || arg == NULL)
    {
        return false;
    }
    watch_alarm_msg *alarm_msg_st = (watch_alarm_msg *)*arg;  

    memset(alarm_msg_st, 0, sizeof(watch_alarm_msg));
    
    // 确保重置alarm_select_index和alarm_index
    alarm_select_index = 0;
    alarm_index = 0;

    alarm_msg_st->set_msg.arg = &alarm_params_set_message;
    alarm_msg_st->set_msg.funcs.decode = &alarm_set_message_decode;

    if (!pb_decode(stream, watch_alarm_msg_fields, alarm_msg_st))
    {
        WATCH_CONFIG_PB_LOG_E("%s %d: Failed to decode alarm message\n", __func__, __LINE__);
        return false;
    }

    WATCH_CONFIG_PB_LOG_D("%s %d: Successfully decoded alarm message\n", __func__, __LINE__);
    return true;
}
//-------------------------------------------------------------------------------------------
// Function Name : alarm_message_send
// Purpose       : 警示提醒解码函数
// Param[in]     : void **arg
// Param[out]    : None
// Return type   : static
// Comment       : 2025-03-25
//-------------------------------------------------------------------------------------------
static void alarm_message_send(void)
{
    watch_config_msg config_message;
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    uint8_t pb_crc = 0;
    bool encode_success = false;

    // 初始化警示提醒数据
    memset(&alarm_message, 0, sizeof(watch_alarm_msg));
    memset(&config_message, 0, sizeof(watch_config_msg));
    
    // 填充警示提醒数据（这里需要根据实际情况添加代码）
    
    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

    config_message.alarm_message.arg = &alarm_message;
    config_message.alarm_message.funcs.encode = &alarm_message_encode;

    config_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_CONFIG;
    config_message.config_service_type = WATCH_CONFIG_SERVICE_TYPE_enum_WATCH_CONFIG_SERVICE_TYPE_ALARM;
    config_message.config_operate_type = WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_GET;

    config_message.has_cur_operate_mode = true;
    config_message.cur_operate_mode = map_sporttype_to_watch_menu_sporttype(cur_operate_mode);

    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    encode_success = pb_encode(&encode_stream, watch_config_msg_fields, &config_message);
    
    if (encode_success)
    {
        *length = encode_stream.bytes_written;
        ble_nus_data_tx_ch0();

        pb_crc = CRC_Calc8_Table_L(data, *length);
        // rt_hexdump("alarm_message_send", 16, data, *length);
        ble_cmd_end_tx(config_message.service_type, config_message.config_service_type, 
            config_message.config_operate_type, 0, encode_stream.bytes_written, pb_crc);
    }
    else
    {
        WATCH_CONFIG_PB_LOG_E("%s %d: Failed to encode alarm message\n", __func__, __LINE__);
    }
}

//-------------------------------------------------------------------------------------------
// Function Name : auto_set_message_decode
// Purpose       : 自动暂停解码函数
// Param[in]     : pb_istream_t *stream
//                 const pb_field_t *field
//                 void **arg
// Param[out]    : None
// Return type   : static
// Comment       : 2025-03-25
//-------------------------------------------------------------------------------------------
static bool auto_set_message_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    watch_auto_set_msg *auto_set_msg_st = (watch_auto_set_msg *)*arg;

    if (auto_set_index >= _WATCH_AUTO_TYPE_ARRAYSIZE)
    {
        return false;
    }

    memset(&auto_set_msg_st[auto_set_index], 0, sizeof(watch_auto_set_msg));

    if (!pb_decode(stream, watch_auto_set_msg_fields, &auto_set_msg_st[auto_set_index]))
    {
        WATCH_CONFIG_PB_LOG_E("%s %d: Failed to decode auto set message\n", __func__, __LINE__);
        return false;
    }

    auto_set_index++;
    WATCH_CONFIG_PB_LOG_D("%s %d: Decoded auto set message at index %d\n", __func__, __LINE__, auto_set_index);
    return true;
}

static void auto_set_message(watch_auto_set_msg *auto_set_message_info)
{
    if (auto_set_message_info == NULL)
    {
        WATCH_CONFIG_PB_LOG_D("%s %d: auto_set_message_info is NULL\n", __func__, __LINE__);
        return;
    }

    for (uint8_t i = 0; i < auto_set_index; i++)
    {
        watch_auto_set_msg *current_auto_set = &auto_set_message_info[i];
        
        if (current_auto_set->has_auto_type)
        {
            switch (current_auto_set->auto_type)
            {
                case WATCH_AUTO_TYPE_enum_WATCH_PAUSE:
                    // 设置自动暂停
                    if (current_auto_set->has_status)
                    {
                        set_sport_auto_pause_en(cur_operate_mode, current_auto_set->status);
                    }
                    if (current_auto_set->has_param1)
                    {
                        set_sport_auto_pause_min_value(cur_operate_mode, current_auto_set->param1);
                    }
                    if (current_auto_set->has_mode)
                    {
                        switch (current_auto_set->mode)
                        {
                            case WATCH_AUTO_STOP_MODE_WATCH_AUTO_STOP_MODE_MOTIONLESS:
                                set_sport_auto_pause_type(cur_operate_mode, STILLNESS);
                                break;
                            case WATCH_AUTO_STOP_MODE_WATCH_AUTO_STOP_MODE_SPEED:
                                set_sport_auto_pause_type(cur_operate_mode, SPEED_LESS);
                                break;
                            case WATCH_AUTO_STOP_MODE_WATCH_AUTO_STOP_MODE_PACE:
                                set_sport_auto_pause_type(cur_operate_mode, CONF_SPEED);
                                break;
                            default:
                                break;
                        }
                    }
                    break;
                case WATCH_AUTO_TYPE_enum_WATCH_PAGE_AUTO:
                    // 设置自动翻页
                    if (current_auto_set->has_status)
                    {
                        set_sport_auto_page_enable(cur_operate_mode, current_auto_set->status);
                    }
                    if (current_auto_set->has_param1)
                    {
                        switch (current_auto_set->param1)
                        {
                            case 2:
                                set_sport_auto_page_time(cur_operate_mode, AUTO_PAGE_FAST);
                                break;
                            case 4:
                                set_sport_auto_page_time(cur_operate_mode, AUTO_PAGE_MEDIUM);
                                break;
                            case 6:
                                set_sport_auto_page_time(cur_operate_mode, AUTO_PAGE_SLOW);
                                break;
                            case 255:
                                set_sport_auto_page_time(cur_operate_mode, AUTO_PAGE_MAIN_PAGE);
                                break;
                            default:
                                break;
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    }

    // 重置auto_set_index
    auto_set_index = 0;

    WATCH_CONFIG_PB_LOG_D("%s %d: Set auto stop config success\n", __func__, __LINE__);
}

static bool auto_set_message_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    uint8_t status = false;
    watch_auto_set_msg *auto_set_msg_st = (watch_auto_set_msg *)*arg;

    for (uint8_t i = 0; i < _WATCH_AUTO_TYPE_ARRAYSIZE; i++)
    {
        auto_set_msg_st[i].auto_type = i + 1;
        auto_set_msg_st[i].has_auto_type = true;
        
        if (auto_set_msg_st[i].auto_type == WATCH_AUTO_TYPE_enum_WATCH_PAUSE)
        {
            auto_set_msg_st[i].has_status = true;
            auto_set_msg_st[i].status = get_sport_auto_pause_en(cur_operate_mode);
            auto_set_msg_st[i].has_param1 = true;
            auto_set_msg_st[i].param1 = get_sport_auto_pause_min_value(cur_operate_mode);
            auto_set_msg_st[i].has_mode = true;
            switch (get_sport_auto_pause_type(cur_operate_mode))
            {
                case STILLNESS:
                    auto_set_msg_st[i].mode = WATCH_AUTO_STOP_MODE_WATCH_AUTO_STOP_MODE_MOTIONLESS;
                    break;
                case SPEED_LESS:
                    auto_set_msg_st[i].mode = WATCH_AUTO_STOP_MODE_WATCH_AUTO_STOP_MODE_SPEED;
                    break;
                case CONF_SPEED:
                    auto_set_msg_st[i].mode = WATCH_AUTO_STOP_MODE_WATCH_AUTO_STOP_MODE_PACE;
                    break;
                default:
                    break;
            }
        }
        else if (auto_set_msg_st[i].auto_type == WATCH_AUTO_TYPE_enum_WATCH_PAGE_AUTO)
        {
            auto_set_msg_st[i].has_status = true;
            auto_set_msg_st[i].status = get_sport_auto_page_enable(cur_operate_mode);
            auto_set_msg_st[i].has_param1 = true;
            auto_set_msg_st[i].param1 = get_sport_auto_page_time(cur_operate_mode);
        }

        if ((auto_set_msg_st[i].auto_type == WATCH_AUTO_TYPE_enum_WATCH_PAUSE)
            || (auto_set_msg_st[i].auto_type == WATCH_AUTO_TYPE_enum_WATCH_PAGE_AUTO))
        {
            status = pb_encode_tag_for_field(stream, field);
            status &= pb_encode_submessage(stream, watch_auto_set_msg_fields, &auto_set_msg_st[i]);
        }
    }
    return status;
}
//-------------------------------------------------------------------------------------------
// Function Name : auto_set_message_send
// Purpose       : 自动暂停解码函数
// Param[in]     : void **arg
// Param[out]    : None
// Return type   : static
// Comment       : 2025-03-25   
//-------------------------------------------------------------------------------------------
static void auto_set_message_send(void)
{
    watch_config_msg config_message;
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    uint8_t pb_crc = 0;

    memset(&config_message, 0, sizeof(watch_config_msg));
    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

    config_message.auto_set_message.arg = &auto_set_message_info;
    config_message.auto_set_message.funcs.encode = &auto_set_message_encode;

    config_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_CONFIG;
    config_message.config_service_type = WATCH_CONFIG_SERVICE_TYPE_enum_WATCH_CONFIG_SERVICE_TYPE_AUTO;
    config_message.config_operate_type = WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_GET;

    config_message.has_cur_operate_mode = true;
    config_message.cur_operate_mode = map_sporttype_to_watch_menu_sporttype(cur_operate_mode);

    // 编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    // 编码
    pb_encode(&encode_stream, watch_config_msg_fields, &config_message);

    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch0();

    // 对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);
    // rt_hexdump("auto_set_message_send", 16, data, *length);
    ble_cmd_end_tx(config_message.service_type, config_message.config_service_type, 
        config_message.config_operate_type, 0, encode_stream.bytes_written, pb_crc);
}

static void time_system_set(WATCH_TIME_SYSTEM_TYPE time_system)
{
    //TODO: 设置时间制
    if (time_system == WATCH_TIME_SYSTEM_TYPE_enum_WATCH_TIME_SYSTEM_TYPE_12HOUR)
    {
        set_phone_time_style(TIMTSTYLE_TIME12);
    }
    else if (time_system == WATCH_TIME_SYSTEM_TYPE_enum_WATCH_TIME_SYSTEM_TYPE_24HOUR)
    {
        set_phone_time_style(TIMTSTYLE_TIME24);
    }
}

//-------------------------------------------------------------------------------------------
// Function Name : menu_item_message_decode
// Purpose       : 菜单项子消息解码接口
// Param[in]     : pb_istream_t *stream
//                 const pb_field_t *field
//                 void **arg
// Param[out]    : None
// Return type   : static bool
// Comment       : 2025-03-20
//-------------------------------------------------------------------------------------------
static bool menu_item_message_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    if (stream == NULL || field == NULL || arg == NULL)
    {
        return false;
    }

    uint8_t *index = (uint8_t *)(*arg);
    
    if (*index >= CFG_CUSTOM_RSV_NUM)
    {
        WATCH_CONFIG_PB_LOG_E("%s %d: Invalid menu item index: %d\n", __func__, __LINE__, *index);
        return false;
    }

    // 清空当前菜单项配置
    memset(&menu_item_message[*index], 0, sizeof(watch_menu_item_msg));

    // 解码菜单项消息
    if (!pb_decode(stream, watch_menu_item_msg_fields, &menu_item_message[*index]))
    {
        WATCH_CONFIG_PB_LOG_E("%s %d: Failed to decode menu item message\n", __func__, __LINE__);
        return false;
    }

    (*index)++;
    return true;
}

//-------------------------------------------------------------------------------------------
// Function Name : watch_menu_list_decode
// Purpose       : 菜单列表消息解码接口
// Param[in]     : pb_istream_t *stream
//                 const pb_field_t *field
//                 void **arg
// Param[out]    : None
// Return type   : static bool
// Comment       : 2025-03-20
//-------------------------------------------------------------------------------------------
static bool watch_menu_list_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    if (stream == NULL || field == NULL || arg == NULL)
    {
        return false;
    }

    // 设置菜单项解码回调
    uint8_t item_index = 0;
    menu_list_message.menu_item.arg = &item_index;
    menu_list_message.menu_item.funcs.decode = menu_item_message_decode;

    // 解码菜单列表消息
    if (!pb_decode(stream, watch_menu_list_msg_fields, &menu_list_message))
    {
        WATCH_CONFIG_PB_LOG_E("%s %d: Failed to decode menu list message\n", __func__, __LINE__);
        return false;
    }

    // 处理菜单列表配置
    if (menu_list_message.has_menu_type)
    {
        cfg_menu_t menu_items[CFG_CUSTOM_RSV_NUM] = {0};
        uint8_t menu_count = 0;

        // 遍历所有收到的菜单项
        for (uint8_t i = 0; i < item_index; i++)
        {
            if (menu_item_message[i].has_state)
            {
                switch (menu_list_message.menu_type)
                {
                    case WATCH_MENU_TYPE_enum_WATCH_MENU_TYPE_APPS:
                        if (menu_item_message[i].has_app_type && 
                            menu_item_message[i].app_type <= _WATCH_MENU_APPTYPE_MAX)
                        {
                            menu_items[menu_count].type = (uint8_t)map_watch_menu_apptype_to_apptype(menu_item_message[i].app_type);
                            menu_items[menu_count].status = (uint8_t)menu_item_message[i].state;
                            menu_count++;
                        }
                        break;

                    case WATCH_MENU_TYPE_enum_WATCH_MENU_TYPE_SPORTS:
                        if (menu_item_message[i].has_sport_type && 
                            menu_item_message[i].sport_type <= _WATCH_MENU_SPORTTYPE_MAX)
                        {
                            menu_items[menu_count].type = (uint8_t)map_watch_menu_sporttype_to_sporttype(menu_item_message[i].sport_type);
                            menu_items[menu_count].status = (uint8_t)menu_item_message[i].state;
                            menu_count++;
                        }
                        break;

                    case WATCH_MENU_TYPE_enum_WATCH_MENU_TYPE_TOOLS:
                        if (menu_item_message[i].has_console_type && 
                            menu_item_message[i].console_type <= _WATCH_MENU_CONSOLETYPE_MAX)
                        {
                            menu_items[menu_count].type = (uint8_t)map_watch_menu_consoletype_to_consoletype(menu_item_message[i].console_type);
                            menu_items[menu_count].status = (uint8_t)menu_item_message[i].state;
                            menu_count++;
                        }
                        break;

                    default:
                        break;
                }
            }
        }

        // 更新配置
        if (menu_count > 0)
        {
            switch (menu_list_message.menu_type)
            {
                case WATCH_MENU_TYPE_enum_WATCH_MENU_TYPE_APPS:
                    set_custom_apps(menu_count, menu_items);
                    menu_list_refresh_page("MenuCard", 3);//刷新      
                    break;
                case WATCH_MENU_TYPE_enum_WATCH_MENU_TYPE_SPORTS:
                    set_custom_sports(menu_count, menu_items);
                    menu_list_refresh_page("SportsMenu", 2);//刷新      
                    break;
                case WATCH_MENU_TYPE_enum_WATCH_MENU_TYPE_TOOLS:
                    set_custom_tools(menu_count, menu_items);
                    menu_list_refresh_page("ToolsMenu", 2);//刷新      
                    break;
                default:
                    break;
            }
        }
    }

    return true;
}

//-------------------------------------------------------------------------------------------
// Function Name : menu_item_message_encode
// Purpose       : 菜单项编码，包含所有菜单项而不仅是启用的
// Param[in]     : pb_ostream_t *stream
//                 const pb_field_t *field
//                 void *const *arg
// Param[out]    : None
// Return type   : static bool
// Comment       : 2025-03-20
//-------------------------------------------------------------------------------------------
static bool menu_item_message_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    WATCH_MENU_TYPE menu_type = *(WATCH_MENU_TYPE *)(*arg);
    watch_menu_item_msg item = {0};
    uint8_t type = 0;
    uint8_t status = 0;

    switch (menu_type)
    {
        case WATCH_MENU_TYPE_enum_WATCH_MENU_TYPE_APPS:
            // 遍历所有应用菜单项，而不仅是启用的项
            for (uint8_t i = 0; i < CFG_CUSTOM_RSV_NUM; i++)
            {
                // 使用接口获取菜单项是否有效
                if (get_custom_app_item(i, &type, &status))
                {
                    memset(&item, 0, sizeof(item));
                    item.has_app_type = true;
                    item.app_type = map_apptype_to_watch_menu_apptype(type);
                    item.has_state = true;
                    item.state = status;

                    if (!pb_encode_tag_for_field(stream, field) ||
                        !pb_encode_submessage(stream, watch_menu_item_msg_fields, &item))
                    {
                        return false;
                    }
                }
            }
            break;

        case WATCH_MENU_TYPE_enum_WATCH_MENU_TYPE_SPORTS:
            // 遍历所有运动菜单项，而不仅是启用的项
            for (uint8_t i = 0; i < CFG_CUSTOM_RSV_NUM; i++)
            {
                // 使用接口获取菜单项是否有效
                if (get_custom_sports_item(i, &type, &status))
                {
                    memset(&item, 0, sizeof(item));
                    item.has_sport_type = true;
                    item.sport_type = map_sporttype_to_watch_menu_sporttype(type);
                    item.has_state = true;
                    item.state = status;

                    if (!pb_encode_tag_for_field(stream, field) ||
                        !pb_encode_submessage(stream, watch_menu_item_msg_fields, &item))
                    {
                        return false;
                    }
                }
            }
            break;

        case WATCH_MENU_TYPE_enum_WATCH_MENU_TYPE_TOOLS:
            // 遍历所有工具菜单项，而不仅是启用的项
            for (uint8_t i = 0; i < CFG_CUSTOM_RSV_NUM; i++)
            {
                // 使用接口获取菜单项是否有效
                if (get_custom_tools_item(i, &type, &status))
                {
                    memset(&item, 0, sizeof(item));
                    item.has_console_type = true;
                    item.console_type = map_consoletype_to_watch_menu_consoletype(type);
                    item.has_state = true;
                    item.state = status;

                    if (!pb_encode_tag_for_field(stream, field) ||
                        !pb_encode_submessage(stream, watch_menu_item_msg_fields, &item))
                    {
                        return false;
                    }
                }
            }
            break;

        default:
            break;
    }

    return true;
}

//-------------------------------------------------------------------------------------------
// Function Name : menu_list_message_encode
// Purpose       : 菜单列表编码
// Param[in]     : pb_ostream_t *stream
//                 const pb_field_t *field
//                 void *const *arg
// Param[out]    : None
// Return type   : static bool
// Comment       : 2025-03-20
//-------------------------------------------------------------------------------------------
static bool menu_list_message_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    WATCH_MENU_TYPE menu_type = *(WATCH_MENU_TYPE *)(*arg);
    
    watch_menu_list_msg menu_list = {0};
    menu_list.has_menu_type = true;
    menu_list.menu_type = menu_type;
    
    // 设置菜单项编码回调
    menu_list.menu_item.arg = &menu_type;
    menu_list.menu_item.funcs.encode = menu_item_message_encode;

    if (!pb_encode_tag_for_field(stream, field) ||
        !pb_encode_submessage(stream, watch_menu_list_msg_fields, &menu_list))
    {
        return false;
    }

    return true;
}

//-------------------------------------------------------------------------------------------
// Function Name : menu_list_message_send
// Purpose       : 发送菜单列表配置
// Param[in]     : WATCH_MENU_TYPE menu_type 菜单类型
// Param[out]    : None
// Return type   : static void
// Comment       : 2025-03-20
//-------------------------------------------------------------------------------------------
static void menu_list_message_send(WATCH_MENU_TYPE menu_type)
{
    watch_config_msg config_message;
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    uint8_t pb_crc = 0;

    memset(&config_message, 0, sizeof(watch_config_msg));
    memset(&menu_list_message, 0, sizeof(watch_menu_list_msg));
    memset(&menu_item_message, 0, sizeof(menu_item_message));

    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

    config_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_CONFIG;
    config_message.config_service_type = WATCH_CONFIG_SERVICE_TYPE_enum_WATCH_CONFIG_SERVICE_TYPE_MENUS;
    config_message.config_operate_type = WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_GET;

    // 设置菜单列表编码回调
    config_message.menu_list_message.arg = &menu_type;
    config_message.menu_list_message.funcs.encode = menu_list_message_encode;

    // 编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    // 编码
    pb_encode(&encode_stream, watch_config_msg_fields, &config_message);

    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch0();

    // 对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);
    // rt_hexdump("menu_list_message_send", 16, data, *length);
    // 命令协议 发送通道2
    ble_cmd_end_tx(config_message.service_type, config_message.config_service_type, 
        config_message.config_operate_type, 0, encode_stream.bytes_written, pb_crc);
}
//-------------------------------------------------------------------------------------------
// Function Name : watch_config_pb_data_decode
// Purpose       : APP配置PB解码接口函数
// Param[in]     : uint8_t * pb_buffer     
//                 uint16_t buffer_length  
//                 END_TYPE end_type       
// Param[out]    : None
// Return type   : 
// Comment       : 2025/03/10
//-------------------------------------------------------------------------------------------
void watch_config_pb_data_decode(uint8_t * pb_buffer, uint16_t buffer_length, END_TYPE end_type)
{
    uint8_t status = false;
    watch_config_msg watch_config_message;
    watch_unit_msg unit_message;

    // rt_hexdump("watch_config_pb_data before decode", 16, (rt_uint8_t *)pb_buffer, buffer_length);

    unit_set_index = 0;
    cur_operate_mode = 0;
    alarm_index = 0;  // 重置alarm_index
    auto_set_index = 0;
    sport_page_index = 0;
    alarm_select_index = 0;  // 确保重置alarm_select_index
    data_grid_page_index = 0;
    
    
    memset(&watch_config_message, 0, sizeof(watch_config_msg));
    memset(&unit_message, 0, sizeof(watch_unit_msg));
    memset(&alarm_message, 0, sizeof(watch_alarm_msg));
    memset(&alarm_select_info, 0, sizeof(watch_alarm_select_info) * _WATCH_ALARM_SELECT_ARRAYSIZE);
    memset(&auto_set_message_info, 0, sizeof(watch_auto_set_msg) * _WATCH_AUTO_TYPE_ARRAYSIZE);
    memset(&alarm_params_set_message, 0, sizeof(watch_alarm_params_set_msg) * _WATCH_ALARM_SELECT_ARRAYSIZE);
    memset(&page_data, 0, sizeof(page_data));

    memset(&menu_list_message, 0, sizeof(watch_menu_list_msg));
    memset(&menu_item_message, 0, sizeof(watch_menu_item_msg) * CFG_CUSTOM_RSV_NUM);
    memset(&s_sec_data, 0, sizeof(section_data));
    memset(&s_user_data, 0, sizeof(user_data));
    sync_user_info_from_cfg();

    //TODO: 配置解码接口和解码后值的传出变量

    watch_config_message.user_data.section_data.run_hrm.arg = s_sec_data.run_hrm_zone;
    watch_config_message.user_data.section_data.run_hrm.funcs.decode = repeated_uint32_decode;

    watch_config_message.user_data.section_data.cad.arg = s_sec_data.cad_zone;
    watch_config_message.user_data.section_data.cad.funcs.decode = repeated_uint32_decode;

    watch_config_message.user_data.section_data.spd.arg = s_sec_data.spd_zone;
    watch_config_message.user_data.section_data.spd.funcs.decode = repeated_uint32_decode;

    watch_config_message.user_data.section_data.ride_hrm.arg = s_sec_data.ride_hrm_zone;
    watch_config_message.user_data.section_data.ride_hrm.funcs.decode = repeated_uint32_decode;

    watch_config_message.user_data.section_data.swim_hrm.arg = s_sec_data.swim_hrm_zone;
    watch_config_message.user_data.section_data.swim_hrm.funcs.decode = repeated_uint32_decode;

    watch_config_message.user_data.section_data.power.arg = s_sec_data.power_zone;
    watch_config_message.user_data.section_data.power.funcs.decode = repeated_uint32_decode;

    watch_config_message.unit_message.arg = &unit_message;
    watch_config_message.unit_message.funcs.decode = &unit_message_decode;

    watch_config_message.page_message.arg = &page_config;
    watch_config_message.page_message.funcs.decode = &page_message_decode;

    watch_config_message.alarm_message.arg = &alarm_message;
    watch_config_message.alarm_message.funcs.decode = &alarm_message_decode;

    watch_config_message.auto_set_message.arg = &auto_set_message_info;
    watch_config_message.auto_set_message.funcs.decode = &auto_set_message_decode;

    watch_config_message.menu_list_message.arg = &menu_list_message;
    watch_config_message.menu_list_message.funcs.decode = &watch_menu_list_decode;

    pb_istream_t stream = pb_istream_from_buffer(pb_buffer, buffer_length);

    if (!pb_decode(&stream, watch_config_msg_fields, &watch_config_message))
    {
        WATCH_CONFIG_PB_LOG_E("%s %d: Failed to decode watch_config_message\n", __func__, __LINE__);
        return;
    }

    if(watch_config_message.has_cur_operate_mode){
        cur_operate_mode = map_watch_menu_sporttype_to_sporttype(watch_config_message.cur_operate_mode);
        WATCH_CONFIG_PB_LOG_D("%s %d: cur_operate_mode: %d\n", __func__, __LINE__, watch_config_message.cur_operate_mode);
        WATCH_CONFIG_PB_LOG_D("%s %d: sport_type: %d\n", __func__, __LINE__, cur_operate_mode);
        if (cur_operate_mode < SPORTSTYPE_RUNNING || cur_operate_mode >= SPORTSTYPE__MAX)
        {
            WATCH_CONFIG_PB_LOG_E("%s %d: cur_operate_mode is invalid\n", __func__, __LINE__);
            ble_cmd_status_tx(watch_config_message.service_type, watch_config_message.config_service_type, watch_config_message.config_operate_type, 0, enmuDATA_ERR_STATUS);
            return;
        }
    }
    switch (watch_config_message.config_service_type)
    {
        case WATCH_CONFIG_SERVICE_TYPE_enum_WATCH_CONFIG_SERVICE_TYPE_USER:
            switch (watch_config_message.config_operate_type) {
                case WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_SET: // app设置用户信息
                    set_user_msg(&watch_config_message.user_data);
                    ble_cmd_success_status_tx(watch_config_message.service_type, watch_config_message.config_service_type, watch_config_message.config_operate_type, 0);
                    break;
                case WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_GET: // app获取用户信息
                    send_user_msg();
                    break;
                default:
                    break;
            }
            break;
        case WATCH_CONFIG_SERVICE_TYPE_enum_WATCH_CONFIG_SERVICE_TYPE_UNIT:
            switch(watch_config_message.config_operate_type)
            {
                case WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_SET:
                    unit_config_set();
                    menu_list_refresh_page("EditPersonalProfile", 1);//更新个人信息页面
                    if (NULL != ble_periph_evt_handler)
                    {
                        ble_periph_evt_handler(BLE_PERIPH_UNIT_SET_EVT, NULL);
                    }
                    // gui_command_submit(enumGUICMD_RESET_SCREEN, NULL);
                    ble_cmd_success_status_tx(watch_config_message.service_type, watch_config_message.config_service_type, watch_config_message.config_operate_type, 0);
                    break;
                case WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_GET:
                    unit_config_send();
                    break;
                default:
                    ble_cmd_status_tx(watch_config_message.service_type, watch_config_message.config_service_type, watch_config_message.config_operate_type, 0, enumINVALID_CMD);
                    break;
            }
            break;
        case WATCH_CONFIG_SERVICE_TYPE_enum_WATCH_CONFIG_SERVICE_TYPE_PAGE:
            switch (watch_config_message.config_operate_type)
            {
                case WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_SET:
                    if(get_sport_status() == 0)//非运动中设置
                    {
                        int ret = save_page_config(page_config);
                        if (ret == 0)
                        {
                            WATCH_CONFIG_PB_LOG_D("%s %d: Set page config success", __func__, __LINE__);
                            ble_cmd_success_status_tx(watch_config_message.service_type, watch_config_message.config_service_type, watch_config_message.config_operate_type, 0);
                        }
                        else
                        {
                            WATCH_CONFIG_PB_LOG_D("%s %d: Set page config failed", __func__, __LINE__);
                            ble_cmd_status_tx(watch_config_message.service_type, watch_config_message.config_service_type, watch_config_message.config_operate_type, 0, enumINVALID_CMD);
                        }
                    }
                    else//运动中设置
                    {
                        WATCH_CONFIG_PB_LOG_W("%s %d: In sport mode ,do not set page config", __func__, __LINE__);
                        ble_cmd_status_tx(watch_config_message.service_type, watch_config_message.config_service_type, watch_config_message.config_operate_type, 0, enumIN_USE);
                    }
                    break;
                case WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_GET:
                    WATCH_CONFIG_PB_LOG_D("%s %d: Get page config", __func__, __LINE__);
                    page_data_config_send();
                    break;
                default:
                    ble_cmd_status_tx(watch_config_message.service_type, watch_config_message.config_service_type, watch_config_message.config_operate_type, 0, enumINVALID_CMD);
                    break;
            }
            break;    
        case WATCH_CONFIG_SERVICE_TYPE_enum_WATCH_CONFIG_SERVICE_TYPE_ALARM:
            switch (watch_config_message.config_operate_type)
            {
                case WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_SET:
                    WATCH_CONFIG_PB_LOG_D("%s %d: Set alarm config\n", __func__, __LINE__);
                    alarm_message_set(&alarm_message, alarm_params_set_message, alarm_select_info);
                    ble_cmd_success_status_tx(watch_config_message.service_type, watch_config_message.config_service_type, watch_config_message.config_operate_type, 0);
                    break;
                case WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_GET:
                    WATCH_CONFIG_PB_LOG_D("%s %d: Get alarm config\n", __func__, __LINE__);
                    alarm_message_send();
                    break;
                default:
                    ble_cmd_status_tx(watch_config_message.service_type, watch_config_message.config_service_type, watch_config_message.config_operate_type, 0, enumINVALID_CMD);
                    break;
            }
            break;
        case WATCH_CONFIG_SERVICE_TYPE_enum_WATCH_CONFIG_SERVICE_TYPE_AUTO:
            switch (watch_config_message.config_operate_type)
            {
                case WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_SET:
                    WATCH_CONFIG_PB_LOG_D("%s %d: Set auto stop config\n", __func__, __LINE__);  
                    auto_set_message(auto_set_message_info);
                    ble_cmd_success_status_tx(watch_config_message.service_type, watch_config_message.config_service_type, watch_config_message.config_operate_type, 0);
                    break;
                case WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_GET:
                    WATCH_CONFIG_PB_LOG_D("%s %d: Get auto stop config\n", __func__, __LINE__);
                    auto_set_message_send();
                    break;
                default:
                    ble_cmd_status_tx(watch_config_message.service_type, watch_config_message.config_service_type, watch_config_message.config_operate_type, 0, enumINVALID_CMD);
                    break;
            }
            break;
        case WATCH_CONFIG_SERVICE_TYPE_enum_WATCH_CONFIG_SERVICE_TYPE_TIME_SYS:
            switch (watch_config_message.config_operate_type)
            {
                case WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_SET:
                    WATCH_CONFIG_PB_LOG_D("%s %d: Set time system config\n", __func__, __LINE__);
                    if (watch_config_message.has_time_system_type)
                    {
                        time_system_set(watch_config_message.time_system_type);
                        ble_cmd_success_status_tx(watch_config_message.service_type, watch_config_message.config_service_type, watch_config_message.config_operate_type, 0);
                    }
                    else
                    {
                        ble_cmd_status_tx(watch_config_message.service_type, watch_config_message.config_service_type, watch_config_message.config_operate_type, 0, enumINVALID_CMD);
                    }
                    break;
                default:
                    ble_cmd_status_tx(watch_config_message.service_type, watch_config_message.config_service_type, watch_config_message.config_operate_type, 0, enumINVALID_CMD);
                    break;
            }
            break;
        case WATCH_CONFIG_SERVICE_TYPE_enum_WATCH_CONFIG_SERVICE_TYPE_MENUS:
            switch (watch_config_message.config_operate_type)
            {
                case WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_SET:
                    ble_cmd_success_status_tx(watch_config_message.service_type, watch_config_message.config_service_type, watch_config_message.config_operate_type, 0);
                    break;
                case WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_GET:
                    menu_list_message_send(menu_list_message.menu_type);
                    break;
                default:
                    ble_cmd_status_tx(watch_config_message.service_type, watch_config_message.config_service_type, watch_config_message.config_operate_type, 0, enumINVALID_CMD);
                    break;
            }
            break;
        default:
            break;
    }
}

/**
 * @brief 将APPTYPE映射到WATCH_MENU_APPTYPE
 * @param app_type APPTYPE枚举值
 * @return 对应的WATCH_MENU_APPTYPE枚举值
 */
WATCH_MENU_APPTYPE map_apptype_to_watch_menu_apptype(APPTYPE app_type)
{
    switch(app_type)
    {
        case APPTYPE_DAILY:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_DAILY;
        case APPTYPE_STEP_COUNT:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_STEP_COUNT;
        case APPTYPE_CALORY:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_CALORY;
        case APPTYPE_INTENSE_DURATION:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_INTENSE_DURATION;
        case APPTYPE_DURATION:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_DURATION;
        case APPTYPE_SLEEP:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_SLEEP;
        case APPTYPE_HRM:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_HRM;
        case APPTYPE_BLOOD_OXYGEN:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_BLOOD_OXYGEN;
        case APPTYPE_PRESSURE:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_PRESSURE;
        case APPTYPE_HRV:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_HRV;
        case APPTYPE_HISTORY:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_HISTORY;
        case APPTYPE_RUNNING:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_RUNNING;
        case APPTYPE_CYCLING:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_CYCLING;
        case APPTYPE_SCHEDULES:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_SCHEDULES;
        case APPTYPE_NAVIGATION:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_NAVIGATION;
        case APPTYPE_TRAINING_STATUS:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_TRAINING_STATUS;
        case APPTYPE_TRAINING_LOAD:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_TRAINING_LOAD;
        case APPTYPE_TRAINING:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_TRAINING;
        case APPTYPE_ALARM_CLOCK:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_ALARM_CLOCK;
        case APPTYPE_WEATHER:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_WEATHER;
        case APPTYPE_STAMINA:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_STAMINA;
        case APPTYPE_BREATHE:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_BREATHE;
        case APPTYPE_SUN:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_SUN;
        case APPTYPE_ALTITUDE:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_ALTITUDE;
        case APPTYPE_BAROMETER:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_BAROMETER;
        case APPTYPE_COMPASS:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_COMPASS;
        case APPTYPE_NOTIFICATION:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_NOTIFICATION;
        case APPTYPE_SETTING:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_SETTING;
        case APPTYPE_STAIRS_COUNT:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_STAIRS_COUNT;
        case APPTYPE_TRAINING_COURSES:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_TRAINING_COURSES;
        default:
            return WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_INVALID;
    }
}

/**
 * @brief 将SPORTTYPE映射到WATCH_MENU_SPORTTYPE
 * @param sport_type SPORTTYPE枚举值
 * @return 对应的WATCH_MENU_SPORTTYPE枚举值
 */
WATCH_MENU_SPORTTYPE map_sporttype_to_watch_menu_sporttype(SPORTTYPE sport_type)
{
    switch(sport_type)
    {
        case SPORTSTYPE_RUNNING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_RUNNING;
        case SPORTSTYPE_TREADMILL:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_TREADMILL;
        case SPORTSTYPE_PLAYGROUND:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_PLAYGROUND;
        case SPORTSTYPE_TRAIL_RUNNING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_TRAIL_RUNNING;
        case SPORTSTYPE_WALKING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_WALKING;
        case SPORTSTYPE_INDOOR_RUNNING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_INDOOR_RUNNING;
        case SPORTSTYPE_CYCLING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_CYCLING;
        case SPORTSTYPE_INDOOR_CYCLING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_INDOOR_CYCLING;
        case SPORTSTYPE_ROAD_CYCLING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_ROAD_CYCLING;
        case SPORTSTYPE_MOUNTAIN_CYCLING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_MOUNTAIN_CYCLING;
        case SPORTSTYPE_COMMUTING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_COMMUTING;
        case SPORTSTYPE_TRIP_CYCLING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_TRIP_CYCLING;
        case SPORTSTYPE_POOL_SWIMMING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_POOL_SWIMMING;
        case SPORTSTYPE_OPEN_WATER_SWIMMING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_OPEN_WATER_SWIMMING;
        case SPORTSTYPE_STRENGTH_TRAINING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_STRENGTH_TRAINING;
        case SPORTSTYPE_INDOOR_AEROBIC:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_INDOOR_AEROBIC;
        case SPORTSTYPE_ELLIPTICAL_MACHINE:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_ELLIPTICAL_MACHINE;
        case SPORTSTYPE_ROWING_MACHINE:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_ROWING_MACHINE;
        case SPORTSTYPE_MOUNTAINEERING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_MOUNTAINEERING;
        case SPORTSTYPE_HIKING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_HIKING;
        case SPORTSTYPE_SKIING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_SKIING;
        case SPORTSTYPE_OUTDOOR_AEROBIC:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_OUTDOOR_AEROBIC;
        case SPORTSTYPE_JUMP_ROPE:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_JUMP_ROPE;
        case SPORTSTYPE_TRIATHLON:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_TRIATHLON;
        case SPORTSTYPE_COMPOUND_MOTION:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_COMPOUND_MOTION;
        case SPORTSTYPE_EXERCISE:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_EXERCISE;
        case SPORTSTYPE_HIIT:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_HIIT;
        case SPORTSTYPE_ROWING_BOAT:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_ROWING_BOAT;
        case SPORTSTYPE_PULP_BOARD:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_PULP_BOARD;
        case SPORTSTYPE_FITNESS:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_FITNESS;
        case SPORTSTYPE_SNOWBOARDING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_SNOWBOARDING;
        case SPORTSTYPE_SNOWBOARDING_2:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_SNOWBOARDING_2;
        case SPORTSTYPE_CROSS_COUNTRY_SKIING:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_CROSS_COUNTRY_SKIING;
        case SPORTSTYPE_YOGA:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_YOGA;
        case SPORTSTYPE_PILATES:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_PILATES;
        case SPORTSTYPE_OUTDOOR_FRISBEE:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_OUTDOOR_FRISBEE;
        default:
            return WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_INVALID;
    }
}

/**
 * @brief 将CONSOLETYPE映射到WATCH_MENU_CONSOLETYPE
 * @param console_type CONSOLETYPE枚举值
 * @return 对应的WATCH_MENU_CONSOLETYPE枚举值
 */
WATCH_MENU_CONSOLETYPE map_consoletype_to_watch_menu_consoletype(CONSOLETYPE console_type)
{
    switch(console_type)
    {
        case CONSOLETYPE_FOCUS_MODE:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_FOCUS_MODE;
        case CONSOLETYPE_SLEEP_MODE:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_SLEEP_MODE;
        case CONSOLETYPE_LOCK:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_LOCK;
        case CONSOLETYPE_LOCKALL:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_LOCKALL;
        case CONSOLETYPE_BRIGHTNESS:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_BRIGHTNESS;
        case CONSOLETYPE_SENSOR:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_SENSOR;
        case CONSOLETYPE_CAMERA:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_CAMERA;
        case CONSOLETYPE_STOP_WATCH:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_STOP_WATCH;
        case CONSOLETYPE_TIMER:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_TIMER;
        case CONSOLETYPE_ALARMCLOCK:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_ALARMCLOCK;
        case CONSOLETYPE_FLASHLIGHT:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_FLASHLIGHT;
        case CONSOLETYPE_FIND_MY_PHONE:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_FIND_MY_PHONE;
        case CONSOLETYPE_HRM:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_HRM;
        case CONSOLETYPE_MUSIC:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_MUSIC;
        case CONSOLETYPE_ALIPAY:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_ALIPAY;
        case CONSOLETYPE_RAISE_AWAKE:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_RAISE_AWAKE;
        case CONSOLETYPE_ALWAYS_ON:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_ALWAYS_ON;
        case CONSOLETYPE_BATTERY_SAVE:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_BATTERY_SAVE;
        case CONSOLETYPE_SETTING:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_SETTING;
        case CONSOLETYPE_METRONOME:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_METRONOME;
        case CONSOLETYPE_BREATH_TRAINING:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_BREATH_TRAINING;
        default:
            return WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_INVALID;
    }
}

/**
 * @brief 将WATCH_MENU_APPTYPE映射到APPTYPE
 * @param watch_app_type WATCH_MENU_APPTYPE枚举值
 * @return 对应的APPTYPE枚举值
 */
APPTYPE map_watch_menu_apptype_to_apptype(WATCH_MENU_APPTYPE watch_app_type)
{
    switch(watch_app_type)
    {
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_DAILY:
            return APPTYPE_DAILY;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_STEP_COUNT:
            return APPTYPE_STEP_COUNT;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_CALORY:
            return APPTYPE_CALORY;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_INTENSE_DURATION:
            return APPTYPE_INTENSE_DURATION;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_DURATION:
            return APPTYPE_DURATION;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_SLEEP:
            return APPTYPE_SLEEP;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_HRM:
            return APPTYPE_HRM;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_BLOOD_OXYGEN:
            return APPTYPE_BLOOD_OXYGEN;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_PRESSURE:
            return APPTYPE_PRESSURE;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_HRV:
            return APPTYPE_HRV;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_HISTORY:
            return APPTYPE_HISTORY;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_RUNNING:
            return APPTYPE_RUNNING;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_CYCLING:
            return APPTYPE_CYCLING;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_SCHEDULES:
            return APPTYPE_SCHEDULES;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_NAVIGATION:
            return APPTYPE_NAVIGATION;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_TRAINING_STATUS:
            return APPTYPE_TRAINING_STATUS;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_TRAINING_LOAD:
            return APPTYPE_TRAINING_LOAD;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_TRAINING:
            return APPTYPE_TRAINING;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_ALARM_CLOCK:
            return APPTYPE_ALARM_CLOCK;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_WEATHER:
            return APPTYPE_WEATHER;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_STAMINA:
            return APPTYPE_STAMINA;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_BREATHE:
            return APPTYPE_BREATHE;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_SUN:
            return APPTYPE_SUN;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_ALTITUDE:
            return APPTYPE_ALTITUDE;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_BAROMETER:
            return APPTYPE_BAROMETER;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_COMPASS:
            return APPTYPE_COMPASS;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_NOTIFICATION:
            return APPTYPE_NOTIFICATION;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_SETTING:
            return APPTYPE_SETTING;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_STAIRS_COUNT:
            return APPTYPE_STAIRS_COUNT;
        case WATCH_MENU_APPTYPE_enum_WATCH_APPTYPE_TRAINING_COURSES:
            return APPTYPE_TRAINING_COURSES;
        default:
            return APPTYPE__MAX;
    }
}

/**
 * @brief 将WATCH_MENU_SPORTTYPE映射到SPORTTYPE
 * @param watch_sport_type WATCH_MENU_SPORTTYPE枚举值
 * @return 对应的SPORTTYPE枚举值
 */
SPORTTYPE map_watch_menu_sporttype_to_sporttype(WATCH_MENU_SPORTTYPE watch_sport_type)
{
    switch(watch_sport_type)
    {
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_RUNNING:
            return SPORTSTYPE_RUNNING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_TREADMILL:
            return SPORTSTYPE_TREADMILL;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_PLAYGROUND:
            return SPORTSTYPE_PLAYGROUND;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_TRAIL_RUNNING:
            return SPORTSTYPE_TRAIL_RUNNING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_WALKING:
            return SPORTSTYPE_WALKING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_INDOOR_RUNNING:
            return SPORTSTYPE_INDOOR_RUNNING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_CYCLING:
            return SPORTSTYPE_CYCLING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_INDOOR_CYCLING:
            return SPORTSTYPE_INDOOR_CYCLING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_ROAD_CYCLING:
            return SPORTSTYPE_ROAD_CYCLING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_MOUNTAIN_CYCLING:
            return SPORTSTYPE_MOUNTAIN_CYCLING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_COMMUTING:
            return SPORTSTYPE_COMMUTING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_TRIP_CYCLING:
            return SPORTSTYPE_TRIP_CYCLING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_POOL_SWIMMING:
            return SPORTSTYPE_POOL_SWIMMING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_OPEN_WATER_SWIMMING:
            return SPORTSTYPE_OPEN_WATER_SWIMMING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_STRENGTH_TRAINING:
            return SPORTSTYPE_STRENGTH_TRAINING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_INDOOR_AEROBIC:
            return SPORTSTYPE_INDOOR_AEROBIC;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_ELLIPTICAL_MACHINE:
            return SPORTSTYPE_ELLIPTICAL_MACHINE;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_ROWING_MACHINE:
            return SPORTSTYPE_ROWING_MACHINE;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_MOUNTAINEERING:
            return SPORTSTYPE_MOUNTAINEERING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_HIKING:
            return SPORTSTYPE_HIKING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_SKIING:
            return SPORTSTYPE_SKIING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_OUTDOOR_AEROBIC:
            return SPORTSTYPE_OUTDOOR_AEROBIC;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_JUMP_ROPE:
            return SPORTSTYPE_JUMP_ROPE;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_TRIATHLON:
            return SPORTSTYPE_TRIATHLON;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_COMPOUND_MOTION:
            return SPORTSTYPE_COMPOUND_MOTION;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_EXERCISE:
            return SPORTSTYPE_EXERCISE;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_HIIT:
            return SPORTSTYPE_HIIT;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_ROWING_BOAT:
            return SPORTSTYPE_ROWING_BOAT;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_PULP_BOARD:
            return SPORTSTYPE_PULP_BOARD;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_FITNESS:
            return SPORTSTYPE_FITNESS;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_SNOWBOARDING:
            return SPORTSTYPE_SNOWBOARDING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_SNOWBOARDING_2:
            return SPORTSTYPE_SNOWBOARDING_2;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_CROSS_COUNTRY_SKIING:
            return SPORTSTYPE_CROSS_COUNTRY_SKIING;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_YOGA:
            return SPORTSTYPE_YOGA;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_PILATES:
            return SPORTSTYPE_PILATES;
        case WATCH_MENU_SPORTTYPE_enum_WATCH_SPORTSTYPE_OUTDOOR_FRISBEE:
            return SPORTSTYPE_OUTDOOR_FRISBEE;
        default:
            return SPORTSTYPE__MAX;
    }
}

/**
 * @brief 将WATCH_MENU_CONSOLETYPE映射到CONSOLETYPE
 * @param watch_console_type WATCH_MENU_CONSOLETYPE枚举值
 * @return 对应的CONSOLETYPE枚举值
 */
CONSOLETYPE map_watch_menu_consoletype_to_consoletype(WATCH_MENU_CONSOLETYPE watch_console_type)
{
    switch(watch_console_type)
    {
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_FOCUS_MODE:
            return CONSOLETYPE_FOCUS_MODE;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_SLEEP_MODE:
            return CONSOLETYPE_SLEEP_MODE;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_LOCK:
            return CONSOLETYPE_LOCK;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_LOCKALL:
            return CONSOLETYPE_LOCKALL;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_BRIGHTNESS:
            return CONSOLETYPE_BRIGHTNESS;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_SENSOR:
            return CONSOLETYPE_SENSOR;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_CAMERA:
            return CONSOLETYPE_CAMERA;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_STOP_WATCH:
            return CONSOLETYPE_STOP_WATCH;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_TIMER:
            return CONSOLETYPE_TIMER;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_ALARMCLOCK:
            return CONSOLETYPE_ALARMCLOCK;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_FLASHLIGHT:
            return CONSOLETYPE_FLASHLIGHT;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_FIND_MY_PHONE:
            return CONSOLETYPE_FIND_MY_PHONE;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_HRM:
            return CONSOLETYPE_HRM;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_MUSIC:
            return CONSOLETYPE_MUSIC;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_ALIPAY:
            return CONSOLETYPE_ALIPAY;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_RAISE_AWAKE:
            return CONSOLETYPE_RAISE_AWAKE;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_ALWAYS_ON:
            return CONSOLETYPE_ALWAYS_ON;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_BATTERY_SAVE:
            return CONSOLETYPE_BATTERY_SAVE;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_SETTING:
            return CONSOLETYPE_SETTING;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_METRONOME:
            return CONSOLETYPE_METRONOME;
        case WATCH_MENU_CONSOLETYPE_enum_WATCH_CONSOLETYPE_BREATH_TRAINING:
            return CONSOLETYPE_BREATH_TRAINING;
        default:
            return CONSOLETYPE__MAX;
    }
}

static gui_evt_service_page_command_t menu_list_page_command;   
/**
 * @brief 发送指令刷新页面
 */
void menu_list_refresh_page(char *page_name, uint8_t cmd)
{
    memset(&menu_list_page_command, 0x00, sizeof(gui_evt_service_page_command_t));
    menu_list_page_command.page = page_name;
    menu_list_page_command.cmd = cmd;
    menu_list_page_command.user_data = (void *)1;// 0:不刷新 1:刷新
    submit_gui_event(GUI_EVT_SERVICE_PAGE_COMMAND, 0, &menu_list_page_command);
}

void user_infor_change_update(void)
{
    if(0 == user_info_update_flag)
    {
        user_info_update_flag = 1;
    }
    if (g_device_get_ble_connect_status())
    {
        ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_CONFIG, WATCH_CONFIG_SERVICE_TYPE_enum_WATCH_CONFIG_SERVICE_TYPE_USER,
                    WATCH_CONFIG_OPERATE_TYPE_enum_WATCH_CONFIG_OPERATE_TYPE_NONE, 0xff, 0);
    }
}
