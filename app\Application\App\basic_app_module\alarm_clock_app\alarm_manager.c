/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   alarm_manager.c
@Time    :   2025/02/17 11:13:09
*
**************************************************************************/
#include "alarm_manager.h"
#include <string.h>
#include "service_datetime.h"
#include "cfg_header_def.h"
#include "gui_event_service.h"
#include "remind_response_app/remind_response_app.h"
#include "focus_mode_srv/focus_mode_srv.h"
static struct {
    // rt_mutex_t lock;
    uint8_t next_id;
    struct {
        alarm_id_t id;
        rt_alarm_t alarm;
        AlarmTime alarm_time;
        rt_bool_t active;
        rt_timer_t delay_timer;  // 延迟定时器
        uint8_t delay_pending;   // 延迟等待标记
    } entries[ALARM_MAX_COUNT];
} mgr;

static gui_evt_service_page_command_t alarm_menu_page_command;

static uint32_t last_trigger_time = 0;
static uint8_t last_triggered_id = 0;

/* 初始化中间层 */
int alarm_manager_init(void)
{
    ALARM_LOG_D("%s %d", __func__,__LINE__);
    rt_memset(&mgr, 0, sizeof(mgr));
    clear_all_delay_times();
    clear_all_alarm_id();
    // rt_mutex_init(&mgr.lock, "alarm_mgr", RT_IPC_FLAG_FIFO);
    mgr.next_id = 1;

    // 加载持久化配置
    alarm_manager_load_config();  // 新增配置加载
    return RT_EOK;
}

static rt_uint32_t convert_alarm_type(uint8_t type)
{
    switch(type) {
    case ONCE:    return RT_ALARM_ONESHOT;
    case EVERYDAY:   return RT_ALARM_DAILY;
    default:             return RT_ALARM_WEEKLY;
    }
}

/* 填充时间结构 */
static void fill_wktime(struct tm *wktime, AlarmTime *t)
{
    qw_tm_t rtc_time = {0};
    uint32_t day_gmt = 0;
    uint32_t gmt = service_datetime_get_gmt_time();
    int16_t timezone = service_datetime_get_timezone();
    service_datetime_gmt2datetime(gmt, &rtc_time, timezone);
    ALARM_LOG_D("Current Time - Year: %d, Month: %d, Day: %d, Hour: %d, Minute: %d, Second: %d",
               rtc_time.tm_year, rtc_time.tm_mon, rtc_time.tm_mday,
               rtc_time.tm_hour, rtc_time.tm_min, rtc_time.tm_sec);

    // 使用传入的时间参数
    rtc_time.tm_hour = t->hour;
    rtc_time.tm_min  = t->minute;
    rtc_time.tm_sec  = t->second;

    // 如果是单次闹钟且有特定日期，则使用它
    if (t->day != RT_ALARM_TM_NOW && t->month != RT_ALARM_TM_NOW) {
        rtc_time.tm_mday = t->day;
        rtc_time.tm_mon = t->month;
        rtc_time.tm_year = t->year;
    }

    ALARM_LOG_D("alarm time: %04d-%02d-%02d %02d:%02d:%02d wday:%d",
               rtc_time.tm_year, rtc_time.tm_mon, rtc_time.tm_mday,
               rtc_time.tm_hour, rtc_time.tm_min, rtc_time.tm_sec, rtc_time.tm_wday);
    day_gmt = service_datetime_datetime2gmt(&rtc_time, timezone);

    // 新增GMT时间转换和打印
    qw_tm_t gmt_tm = {0};
    service_datetime_gmt2datetime(day_gmt, &gmt_tm, 0); // 0时区表示GMT
    ALARM_LOG_D("GMT Time - Year: %d, Month: %d, Day: %d, Hour: %d, Minute: %d, Second: %d",
               gmt_tm.tm_year, gmt_tm.tm_mon, gmt_tm.tm_mday,
               gmt_tm.tm_hour, gmt_tm.tm_min, gmt_tm.tm_sec);


    wktime->tm_hour = gmt_tm.tm_hour;
    wktime->tm_min  = gmt_tm.tm_min;
    wktime->tm_sec  = gmt_tm.tm_sec;

    // 处理特殊时间值
    wktime->tm_mday = gmt_tm.tm_mday;
    wktime->tm_mon  = gmt_tm.tm_mon - 1;
    // wktime->tm_year = (t->month == RT_ALARM_TM_NOW) ? now_tm.tm_year : (t->year); // 简化处理
    wktime->tm_year = gmt_tm.tm_year - 1900;
    wktime->tm_wday = t->wday;
    // 打印所有时间参数用于调试
    ALARM_LOG_D("wktime: %04d-%02d-%02d %02d:%02d:%02d wday:%d",
              wktime->tm_year + 1900, wktime->tm_mon + 1, wktime->tm_mday,
              wktime->tm_hour, wktime->tm_min, wktime->tm_sec, wktime->tm_wday);
}

/* 通过ID查找闹钟 */
static rt_alarm_t find_alarm(alarm_id_t id)
{
    // rt_mutex_take(&mgr.lock, RT_WAITING_FOREVER);
    for (int i = 0; i < ALARM_MAX_COUNT; i++) {
        if (mgr.entries[i].id == id && mgr.entries[i].active) {
            // rt_mutex_release(&mgr.lock);
            return mgr.entries[i].alarm;
        }
    }
    // rt_mutex_release(&mgr.lock);
    return RT_NULL;
}

// 新增辅助函数：通过rt_alarm_t查找alarm_id
static alarm_id_t find_alarm_id(rt_alarm_t alarm)
{
    for (int i = 0; i < ALARM_MAX_COUNT; i++) {
        if (mgr.entries[i].alarm == alarm && mgr.entries[i].active) {
            return mgr.entries[i].id;
        }
    }
    return 0;
}

// 修改后的回调函数参数类型
void alarm_callback(rt_alarm_t alarm, unsigned int param)
{
    ALARM_LOG_D("Alarm triggered %s %d", __func__,__LINE__);
    uint32_t current_time = get_boot_sec();

    alarm_id_t id = find_alarm_id(alarm);
    if (id == 0)
    {
        ALARM_LOG_D("Cannot find Alarm id %s %d", __func__,__LINE__);
        return;
    }

    // 检查是否是短时间内的重复触发
    if (current_time - last_trigger_time < 5 && last_triggered_id != id)
    {
        ALARM_LOG_D("Skipping duplicate alarm trigger within 5s, id:%d", id);
        return;
    }

    last_trigger_time = current_time;
    last_triggered_id = id;

    alarm_clocks* alarm_clock = get_alarm_by_id(id);
    if(alarm_clock == NULL)
    {
        //kv找不到，查看专注起床提醒
        alarm_clock = get_alarm_by_sleep_srv(id);
    }

    if (alarm_clock)
    {
        submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_ALARM_CLOCK, (void *)alarm_clock);
        // alarm_manager_update_delay_timer(id);
    }
    else
    {
        ALARM_LOG_D("Canot find Alarm clock %s %d", __func__,__LINE__);
    }
}

/* 创建闹钟 */
alarm_id_t alarm_manager_create(uint32_t trig_point, uint8_t repeat, rt_alarm_callback_t cb)
{
    struct rt_alarm_setup setup = {0};
	AlarmTime alarm_time;
    alarm_time = trig_point_to_alarmtime(trig_point,repeat,false);
    ALARM_LOG_D("[Alarm Create] Time params - h:%d m:%d s:%d day:%d month:%d year:%d wday:%d",
              alarm_time.hour, alarm_time.minute, alarm_time.second,
              alarm_time.day, alarm_time.month, alarm_time.year, alarm_time.wday);
    // 参数检查
    if (alarm_time.hour < 0 || alarm_time.hour > 23 ||
        alarm_time.minute < 0 || alarm_time.minute > 59 ||
        alarm_time.second < 0 || alarm_time.second > 59) {
        return 0;
    }

    // 增加参数有效性检查
    if (mgr.next_id == 0) {
        ALARM_LOG_D("ERROR: ID sequence overflow!");
        return 0;  // 0表示无效ID
    }

    // 转换类型
    setup.flag = convert_alarm_type(repeat);
    fill_wktime(&setup.wktime, &alarm_time);

    // 特殊处理周闹钟
    // if(setup.flag == RT_ALARM_WEEKLY) {
    //     setup.wktime.tm_mday = RT_ALARM_TM_NOW;
    //     setup.wktime.tm_mon  = RT_ALARM_TM_NOW;
    //     setup.wktime.tm_year = RT_ALARM_TM_NOW;
    // }

    // 修改ID分配逻辑
    for (int i = 0; i < ALARM_MAX_COUNT; i++) {
        if (mgr.entries[i].id == 0) {  // 0表示空闲槽位
            mgr.entries[i].id = mgr.next_id;
            if (++mgr.next_id == 0) mgr.next_id = 1;  // 跳过0值
            mgr.entries[i].alarm = rt_alarm_create(cb, &setup);
            if (!mgr.entries[i].alarm) return 0;
            mgr.entries[i].alarm_time = alarm_time;
            mgr.entries[i].active = RT_TRUE;

            rt_err_t ret = rt_alarm_start(mgr.entries[i].alarm);
            ALARM_LOG_D("%s %d add ret:%d alarm id:%d", __func__,__LINE__,ret,mgr.entries[i].id);
            return mgr.entries[i].id;
        }
    }

    return 0;
}

/* 删除闹钟 */
rt_err_t alarm_manager_delete(alarm_id_t id)
{
    if (id == 0) return -RT_ERROR;
    rt_alarm_t alarm = find_alarm(id);
    if (!alarm) return -RT_ERROR;

    // 新增：停止关联的延迟定时器
    // alarm_manager_stop_delay_timer(id);

    // 先停止闹钟，确保它不会再触发
    rt_err_t stop_result = rt_alarm_stop(alarm);
    ALARM_LOG_D("Stopping alarm id=%d before deletion, result=%d", id, stop_result);

    // 删除闹钟
    rt_err_t delete_result = rt_alarm_delete(alarm);
    ALARM_LOG_D("Deleting alarm id=%d, result=%d", id, delete_result);

    // 彻底清理管理器中的记录
    for (int i = 0; i < ALARM_MAX_COUNT; i++) {
        if (mgr.entries[i].id == id) {
            // 彻底清除所有相关字段
            mgr.entries[i].id = 0;
            mgr.entries[i].alarm = RT_NULL;
            mgr.entries[i].active = RT_FALSE;
            mgr.entries[i].delay_timer = RT_NULL;
            mgr.entries[i].delay_pending = 0;
            rt_memset(&mgr.entries[i].alarm_time, 0, sizeof(AlarmTime));

            ALARM_LOG_D("Cleared alarm entry at index %d for id=%d", i, id);
            break;
        }
    }

    return delete_result;
}

/* 启用/禁用闹钟 */
rt_err_t alarm_manager_enable(alarm_id_t id, rt_bool_t enable)
{
    rt_alarm_t alarm = find_alarm(id);
    if (!alarm) return -RT_ERROR;

    for (int i = 0; i < ALARM_MAX_COUNT; i++) {
        if (mgr.entries[i].id == id) {
            // 强制清理定时器（无论启用还是禁用）
            // alarm_manager_stop_delay_timer(id);

            // mgr.entries[i].active = enable;

            // 如果启用且需要自动启动延迟定时器（根据业务需求）
            // if (enable) {
            //     alarm_manager_create_delay_timer(id);
            // }
            break;
        }
    }

    if (enable) {
        return rt_alarm_start(alarm);
    } else {
        return rt_alarm_stop(alarm);
    }
}

/* 获取闹钟信息 */

AlarmTime trig_point_to_alarmtime(uint32_t trig_point, uint8_t repeat, bool weekly_next_day)
{
    AlarmTime alarm_time = {0};

    // 分解触发时间（trig_point是当日秒数）
    alarm_time.hour = trig_point / 3600;
    alarm_time.minute = (trig_point % 3600) / 60;
    alarm_time.second = trig_point % 60;
    qw_tm_t rtc_time = {0};
    uint32_t gmt = service_datetime_get_gmt_time();
    int16_t timezone = service_datetime_get_timezone();
    service_datetime_gmt2datetime(gmt, &rtc_time, timezone);

    // 获取当前时间的总秒数，用于比较
    uint32_t current_seconds = rtc_time.tm_hour * 3600 + rtc_time.tm_min * 60 + rtc_time.tm_sec;

    // 根据重复类型设置日期参数
    if (repeat == ONCE) {
        // 单次闹钟需要具体日期
        alarm_time.day = rtc_time.tm_mday;
        alarm_time.month = rtc_time.tm_mon; // 系统月份从0开始
        alarm_time.year = rtc_time.tm_year; // 系统年份从1900开始

        // 如果是单次闹钟且设置时间早于当前时间，则设置为明天
        if (trig_point <= current_seconds) {
            // 计算明天的日期
            qw_tm_t tomorrow = rtc_time;
            tomorrow.tm_mday += 1;
            // 处理月末日期变更
            uint32_t tomorrow_gmt = service_datetime_datetime2gmt(&tomorrow, timezone);
            service_datetime_gmt2datetime(tomorrow_gmt, &tomorrow, timezone);

            alarm_time.day = tomorrow.tm_mday;
            alarm_time.month = tomorrow.tm_mon;
            alarm_time.year = tomorrow.tm_year;
        }
    } else {
        // 重复闹钟使用特殊标记
        alarm_time.day = RT_ALARM_TM_NOW;
        alarm_time.month = RT_ALARM_TM_NOW;
    }

    // 处理周重复的星期设置
    if (repeat != ONCE && repeat != EVERYDAY) {
        const uint8_t wday_mapping[] = {0x40, 0x1, 0x2, 0x4, 0x8, 0x10, 0x20};
        alarm_time.wday = 0;
        int current_wday = rtc_time.tm_wday;

        // 新增：获取当前时间总秒数用于比较
        current_seconds = rtc_time.tm_hour * 3600 + rtc_time.tm_min * 60 + rtc_time.tm_sec;

        // 调整查找逻辑
        int next_wday = -1;
        int start_day = weekly_next_day ? 1 : 0;  // 强制跳过今天

        // 第一轮：查找未来6天（当需要跳过今天时）
        for (int i = start_day; i < 7; i++) {
            int check_day = (current_wday + i) % 7;

            // 必须满足两个条件：1.在repeat范围内 2.时间未过或不是今天
            if ((repeat & wday_mapping[check_day])) {
                // 如果是今天且不需要跳过，则检查时间
                if (i == 0 && !weekly_next_day) {
                    if (trig_point > current_seconds) {
                        next_wday = check_day;
                        break;
                    }
                }
                // 其他情况直接记录
                else {
                    next_wday = check_day;
                    break;
                }
            }
        }

        // 第二轮：完整周查找（处理跨周情况）
        if (next_wday == -1) {
            for (int i = 0; i < 7; i++) {
                int check_day = (current_wday + i) % 7;
                if ((repeat & wday_mapping[check_day])) {
                    // 如果是今天且不需要跳过，需要再次检查时间
                    if (i == 0 && !weekly_next_day) {
                        if (trig_point > current_seconds) {
                            next_wday = check_day;
                            break;
                        }
                    } else {
                        next_wday = check_day;
                        break;
                    }
                }
            }
        }

        if (next_wday != -1) {
            alarm_time.wday = next_wday;
            ALARM_LOG_D("%s %d next_wday=%d", __func__,__LINE__,next_wday);
        }
    }

    return alarm_time;
}

/* 修改闹钟参数 */
rt_err_t alarm_manager_modify(alarm_id_t id, uint32_t new_trig_point, uint8_t new_repeat, bool weekly_next_day)
{
    ALARM_LOG_D("%s %d id=%d new_trig_point=%d new_repeat=%d", __func__,__LINE__,id,new_trig_point,new_repeat);
    // 参数有效性检查
    if (id == 0 || new_trig_point >= 86400) // 86400=24*3600
    {
        ALARM_LOG_D("%s %d Invalid parameters: id=%d, trig_point=%d", __func__,__LINE__,id,new_trig_point);
        return -RT_ERROR;
    }

    // 新增：停止旧定时器
    // alarm_manager_stop_delay_timer(id);

    // 查找现有闹钟
    rt_alarm_t alarm = find_alarm(id);
    if (!alarm)
    {
        ALARM_LOG_D("%s %d Cannot find alarm id:%d.", __func__,__LINE__,id);

        // 尝试直接创建新闹钟，而不是返回错误
        ALARM_LOG_D("%s %d Attempting to create new alarm with id=%d", __func__,__LINE__,id);

        // 创建新闹钟（复用原ID）
        AlarmTime new_time = trig_point_to_alarmtime(new_trig_point, new_repeat, weekly_next_day);
        ALARM_LOG_D("[Alarm Create] Time params - h:%d m:%d s:%d day:%d month:%d year:%d wday:%d",
                  new_time.hour, new_time.minute, new_time.second,
                  new_time.day, new_time.month, new_time.year, new_time.wday);

        struct rt_alarm_setup new_setup = {
            .flag = convert_alarm_type(new_repeat)
        };
        fill_wktime(&new_setup.wktime, &new_time);

        // 查找空闲槽位并创建
        for (int i = 0; i < ALARM_MAX_COUNT; i++) {
            if (mgr.entries[i].id == 0) {
                mgr.entries[i].id = id;  // 使用指定ID
                mgr.entries[i].alarm = rt_alarm_create(alarm_callback, &new_setup);
                if (!mgr.entries[i].alarm)
                {
                    ALARM_LOG_D("%s %d Failed to create alarm with id=%d", __func__,__LINE__,id);
                    return -RT_ERROR;
                }

                mgr.entries[i].alarm_time = new_time;
                mgr.entries[i].active = RT_TRUE;

                rt_err_t ret = rt_alarm_start(mgr.entries[i].alarm);
                ALARM_LOG_D("%s %d Created and started new alarm id=%d, result=%d", __func__,__LINE__,id,ret);
                return ret;
            }
        }

        ALARM_LOG_D("%s %d No free slots available for id=%d", __func__,__LINE__,id);
        return -RT_ERROR;
    }

    // 停止并删除旧闹钟
    rt_err_t stop_result = rt_alarm_stop(alarm);
    ALARM_LOG_D("%s %d Stopped alarm id=%d, result=%d", __func__,__LINE__,id,stop_result);

    rt_err_t delete_result = rt_alarm_delete(alarm);
    ALARM_LOG_D("%s %d Deleted alarm id=%d, result=%d", __func__,__LINE__,id,delete_result);

    // 在管理记录中标记为无效，但保留ID
    int slot_index = -1;
    for (int i = 0; i < ALARM_MAX_COUNT; i++) {
        if (mgr.entries[i].id == id) {
            mgr.entries[i].active = RT_FALSE;
            mgr.entries[i].alarm = RT_NULL;
            // 不要清除ID，保留以便后续重用
            // mgr.entries[i].id = 0;
            slot_index = i;
            break;
        }
    }

    // 创建新闹钟（复用原ID）
    AlarmTime new_time = trig_point_to_alarmtime(new_trig_point, new_repeat, weekly_next_day);
    ALARM_LOG_D("[Alarm Modify] Time params - h:%d m:%d s:%d day:%d month:%d year:%d wday:%d",
              new_time.hour, new_time.minute, new_time.second,
              new_time.day, new_time.month, new_time.year, new_time.wday);

    struct rt_alarm_setup new_setup = {
        .flag = convert_alarm_type(new_repeat)
    };
    fill_wktime(&new_setup.wktime, &new_time);

    // 如果找到了原槽位，直接在原槽位重新创建
    if (slot_index >= 0) {
        mgr.entries[slot_index].alarm = rt_alarm_create(alarm_callback, &new_setup);
        if (!mgr.entries[slot_index].alarm) {
            ALARM_LOG_D("%s %d Failed to create new alarm in original slot %d", __func__,__LINE__,slot_index);
            return -RT_ERROR;
        }

        mgr.entries[slot_index].alarm_time = new_time;
        mgr.entries[slot_index].active = RT_TRUE;

        rt_err_t ret = rt_alarm_start(mgr.entries[slot_index].alarm);
        ALARM_LOG_D("%s %d Modified alarm id=%d in slot %d, result=%d", __func__,__LINE__,id,slot_index,ret);
        return ret;
    }

    // 如果没找到原槽位，查找空闲槽位并重新创建
    for (int i = 0; i < ALARM_MAX_COUNT; i++) {
        if (mgr.entries[i].id == 0) {
            mgr.entries[i].id = id;  // 复用原ID
            mgr.entries[i].alarm = rt_alarm_create(alarm_callback, &new_setup);
            if (!mgr.entries[i].alarm) {
                ALARM_LOG_D("%s %d Failed to create new alarm in new slot %d", __func__,__LINE__,i);
                return -RT_ERROR;
            }

            mgr.entries[i].alarm_time = new_time;
            mgr.entries[i].active = RT_TRUE;

            rt_err_t ret = rt_alarm_start(mgr.entries[i].alarm);
            ALARM_LOG_D("%s %d Modified alarm id=%d in new slot %d, result=%d", __func__,__LINE__,id,i,ret);
            return ret;
        }
    }

    ALARM_LOG_D("%s %d No free slots available for modified alarm id=%d", __func__,__LINE__,id);
    return -RT_ERROR;
}

// 新增延迟处理函数
rt_err_t alarm_manager_delay(alarm_clocks *p_alarm_config)
{
    if (!p_alarm_config)
    {
        return -RT_ERROR;
    }
    if (p_alarm_config->alarm_id == 0)
    {
        ALARM_LOG_D("%s %d alarm_id invalid", __func__,__LINE__);
        return -RT_ERROR;
    }
    rt_alarm_t alarm = find_alarm(p_alarm_config->alarm_id);
    if (!alarm)
    {
        ALARM_LOG_D("%s %d Cannot find alarm id:%d", __func__,__LINE__,p_alarm_config->alarm_id);
        return -RT_ERROR;
    }

    // 更新延迟次数
    if (p_alarm_config->delay_times < ALARM_MAX_DELAY_COUNT) {
        p_alarm_config->delay_times++;
    }
    ALARM_LOG_D("%s %d current_delay time:%d repeat:%d", __func__,__LINE__,p_alarm_config->delay_times, p_alarm_config->repeat);

    // 处理时间调整
    if (p_alarm_config->delay_times < ALARM_MAX_DELAY_COUNT) {
        // 推迟10分钟（单次触发）
        ALARM_LOG_D("%s %d Delaying alarm id=%d by %d minutes",
                  __func__, __LINE__, p_alarm_config->alarm_id, ALARM_DELAY_TIME / 60);

        return alarm_manager_modify(p_alarm_config->alarm_id,
                                  p_alarm_config->trig_point + ALARM_DELAY_TIME * p_alarm_config->delay_times,
                                  ONCE, false);
    } else {
        // 达到最大延迟次数
        ALARM_LOG_D("%s %d Max delay count reached for alarm id=%d", __func__, __LINE__, p_alarm_config->alarm_id);

        // 恢复原始设置
        p_alarm_config->delay_times = 0;
        return alarm_manager_modify(p_alarm_config->alarm_id,
                                  p_alarm_config->trig_point,
                                  p_alarm_config->repeat, true);
    }

    return -RT_ERROR;
}

// 在文件末尾添加配置加载函数
int alarm_manager_load_config(void)
{
    uint32_t trig_point = 0;
    uint8_t repeat = 0;
    bool open = false;
    uint8_t alarm_count = get_alarm_count(); // 获取闹钟数量
    ALARM_LOG_D("%s %d count:%d", __func__,__LINE__,alarm_count);

    // 获取当前时间
    uint32_t current_time = service_datetime_get_gmt_time();

    // 遍历所有配置项
    for (int i = 0; i < alarm_count; i++)
    {
        trig_point = 0;
        repeat = 0;
        open = false;

        get_alarm_details(i, &trig_point, &repeat, &open);

        // 新增：检查"仅一次"闹钟是否已过期
        if (repeat == ONCE && open)
        {
            // 将触发点转换为GMT时间戳进行比较
            uint32_t alarm_timestamp = convert_trigpoint_to_timestamp(trig_point);
            if (alarm_timestamp < current_time)
            {
                // 闹钟已过期，更新配置为关闭
                open = false;
                set_alarm_open(i, false);
                ALARM_LOG_D("Disabled expired once alarm: index=%d", i);
                continue; // 跳过已过期的闹钟
            }
        }

        // 创建闹钟并获取新ID
        alarm_id_t new_id = alarm_manager_create(trig_point, repeat, alarm_callback);

        if (new_id != 0) {
            // 更新配置中的alarm_id
            set_alarm_id(i, new_id);
            ALARM_LOG_D("Loaded alarm: trig_point=%d, id=%d", trig_point, new_id);
        } else {
            ALARM_LOG_D("Failed to create alarm: trig_point=%d", trig_point);
        }
        if (!open)
        {
            alarm_manager_enable(new_id, false);
        }
    }
    return RT_EOK;
}

// 新增延迟定时器回调
static void delay_timer_callback(void *parameter)
{
    alarm_id_t id = (alarm_id_t)(uintptr_t)parameter;
    alarm_clocks *p_alarm = get_alarm_by_id(id);
    if(p_alarm == NULL)
    {
        //kv找不到，查看专注起床提醒
        p_alarm = get_alarm_by_sleep_srv(id);
    }

    if (p_alarm) {
        remind_trigger(enumPOPUP_ALARM_CLOCK, false);

        // 如果是一次性闹钟且已达到最大延迟次数，则关闭闹钟
        if ((p_alarm->repeat == ONCE) && (p_alarm->delay_times >= ALARM_MAX_DELAY_COUNT - 1))
        {
            // 关闭当前闹钟
            alarm_manager_disable_alarm(p_alarm);

            // 同时关闭所有同一触发时间的一次性闹钟
            uint32_t target_time = p_alarm->trig_point + ALARM_DELAY_TIME * p_alarm->delay_times;
            disable_all_once_alarms_with_time(target_time);
        }
        else
        {
            // 正常处理延迟
            alarm_manager_delay(p_alarm);
        }
    }

    // 更新状态
    for (int i = 0; i < ALARM_MAX_COUNT; i++) {
        if (mgr.entries[i].id == id) {
            mgr.entries[i].delay_pending = 0;
            break;
        }
    }
}

// 创建延迟定时器
rt_err_t alarm_manager_create_delay_timer(alarm_id_t id)
{
    if (id == 0) return -RT_ERROR;

    for (int i = 0; i < ALARM_MAX_COUNT; i++) {
        if (mgr.entries[i].id == id) {
            // 如果已有定时器则先删除
            if (mgr.entries[i].delay_timer) {
                rt_timer_stop(mgr.entries[i].delay_timer);
                rt_timer_delete(mgr.entries[i].delay_timer);
            }

            // 创建45秒定时器
            mgr.entries[i].delay_timer = rt_timer_create("delay_tmr",
                delay_timer_callback,
                (void*)(uintptr_t)id,
                ALARM_MAX_DURATION_TIME,
                RT_TIMER_FLAG_ONE_SHOT | RT_TIMER_FLAG_SOFT_TIMER);

            if (mgr.entries[i].delay_timer) {
                mgr.entries[i].delay_pending = 1;
                return rt_timer_start(mgr.entries[i].delay_timer);
            }
            return -RT_ERROR;
        }
    }
    return -RT_ERROR;
}

// 更新延迟定时器
rt_err_t alarm_manager_update_delay_timer(alarm_id_t id)
{
    if (id == 0) return -RT_ERROR;

    for (int i = 0; i < ALARM_MAX_COUNT; i++) {
        if (mgr.entries[i].id == id) {
            // 如果定时器正在运行，立即触发延迟
            if (mgr.entries[i].delay_pending) {
                if (mgr.entries[i].delay_timer)
                {
                    rt_timer_stop(mgr.entries[i].delay_timer);
                }
                delay_timer_callback((void*)(uintptr_t)id);
            }

            // 重新创建定时器
            return alarm_manager_create_delay_timer(id);
        }
    }
    return -RT_ERROR;
}

// 停止并删除定时器
rt_err_t alarm_manager_stop_delay_timer(alarm_id_t id)
{
    if (id == 0) return -RT_ERROR;

    for (int i = 0; i < ALARM_MAX_COUNT; i++) {
        if (mgr.entries[i].id == id) {
            if (mgr.entries[i].delay_timer) {
                rt_timer_stop(mgr.entries[i].delay_timer);
                rt_timer_delete(mgr.entries[i].delay_timer);
                mgr.entries[i].delay_timer = RT_NULL;
                mgr.entries[i].delay_pending = 0;
                return RT_EOK;
            }
            return -RT_ERROR;
        }
    }
    return -RT_ERROR;
}

// 将触发点转换为GMT时间戳
uint32_t convert_trigpoint_to_timestamp(uint32_t trig_point)
{
    // 获取当前日期时间
    uint32_t current_gmt = service_datetime_get_gmt_time();
    qw_tm_t current_tm = {0};
    int16_t timezone = service_datetime_get_timezone();
    service_datetime_gmt2datetime(current_gmt, &current_tm, timezone);

    // 用触发点替换小时、分钟、秒
    current_tm.tm_hour = trig_point / 3600;
    current_tm.tm_min = (trig_point % 3600) / 60;
    current_tm.tm_sec = trig_point % 60;

    // 转回GMT时间戳
    return service_datetime_datetime2gmt(&current_tm, timezone);
}

void alarm_manager_refresh_alarm_list(void)
{
    static uint8_t user_data = 1;
    memset(&alarm_menu_page_command, 0, sizeof(gui_evt_service_page_command_t));
    alarm_menu_page_command.page = "AlarmMenu";
    alarm_menu_page_command.cmd = 1;//AlarmMenu_CMD::SET_REFRESH_FLAG
    alarm_menu_page_command.user_data = (void *)&user_data;
    submit_gui_event(GUI_EVT_SERVICE_PAGE_COMMAND, 0, &alarm_menu_page_command);
}

/**
 * @brief 关闭指定的闹钟（主要用于"仅一次"闹钟）
 *
 * @param p_alarm_config 指向闹钟配置的指针
 * @return rt_err_t 成功返回RT_EOK，失败返回-RT_ERROR
 */
rt_err_t alarm_manager_disable_alarm(alarm_clocks *p_alarm_config)
{
    if (p_alarm_config == NULL)
    {
        return -RT_ERROR;
    }

    // 更新内存中的状态
    p_alarm_config->open = false;

    // 查找索引并更新持久化存储
    for (uint8_t i = 0; i < get_alarm_count(); i++)
    {
        uint8_t alarm_id = get_alarm_id_by_index(i);
        if (alarm_id == p_alarm_config->alarm_id)
        {
            set_alarm_open(i, false);
            // 刷新闹钟列表界面
            alarm_manager_refresh_alarm_list();
            return RT_EOK;
        }
    }

    return -RT_ERROR;  // 未找到对应的闹钟
}

/**
 * @brief 关闭所有指定触发时间的一次性闹钟
 *
 * @param target_time 目标触发时间
 * @return int 关闭的闹钟数量
 */
int disable_all_once_alarms_with_time(uint32_t target_time)
{
    int disabled_count = 0;
    uint8_t alarm_count = get_alarm_count();

    // 遍历所有闹钟
    for (uint8_t i = 0; i < alarm_count; i++)
    {
        uint32_t current_trig_point = 0;
        uint8_t repeat = 0;
        bool open = false;

        // 获取闹钟详情
        get_alarm_details(i, &current_trig_point, &repeat, &open);

        // 如果是一次性闹钟且触发时间相同且处于开启状态
        if (repeat == ONCE && current_trig_point == target_time && open)
        {
            // 获取闹钟ID
            uint8_t alarm_id = get_alarm_id_by_index(i);

            // 获取闹钟配置
            alarm_clocks* p_alarm = get_alarm_by_id(alarm_id);

            if (p_alarm != NULL)
            {
                // 关闭闹钟
                alarm_manager_disable_alarm(p_alarm);
                ALARM_LOG_D("Disabled once alarm with target time: index=%d, id=%d", i, alarm_id);
                disabled_count++;
            }
        }
    }

    // 刷新闹钟列表UI
    if (disabled_count > 0)
    {
        alarm_manager_refresh_alarm_list();
    }

    return disabled_count;
}

/**
 * @brief 停止并删除所有指定触发时间的一次性闹钟
 *
 * @param target_time 目标触发时间
 * @param include_delayed 是否包括延迟的闹钟
 * @return int 处理的闹钟数量
 *
 * @note 当延迟次数达到最大值时，也会处理该闹钟
 */
int stop_and_remove_all_once_alarms_with_time(uint32_t target_time, bool include_delayed)
{
    int removed_count = 0;
    uint8_t alarm_count = get_alarm_count();

    ALARM_LOG_D("Stopping and removing one-time alarms with time %d, include_delayed=%d",
              target_time, include_delayed);

    // 第一步：找出并存储所有需要删除的一次性闹钟ID
    alarm_id_t ids_to_remove[ALARM_MAX_COUNT] = {0};
    int id_count = 0;

    // 遍历所有闹钟配置
    for (uint8_t i = 0; i < alarm_count; i++)
    {
        uint32_t current_trig_point = 0;
        uint8_t repeat = 0;
        bool open = false;

        // 获取闹钟详情
        get_alarm_details(i, &current_trig_point, &repeat, &open);

        // 检查是否是一次性闹钟，且处于开启状态
        if (repeat == ONCE && open)
        {
            bool should_remove = false;
            uint8_t alarm_id = get_alarm_id_by_index(i);
            alarm_clocks* p_alarm = get_alarm_by_id(alarm_id);

            // 检查原始触发时间
            if (current_trig_point == target_time)
            {
                ALARM_LOG_D("Found one-time alarm to remove: index=%d, id=%d, time=%d",
                          i, alarm_id, current_trig_point);
                should_remove = true;
            }
            // 如果需要包括延迟的闹钟，则检查延迟后的时间
            else if (include_delayed && p_alarm)
            {
                // 计算延迟后的触发时间
                uint32_t delayed_time = p_alarm->trig_point +
                                      ALARM_DELAY_TIME * p_alarm->delay_times;
                ALARM_LOG_D("Delayed time: %d, target time: %d", delayed_time, target_time);
                if (delayed_time == target_time)
                {
                    should_remove = true;
                }
            }

            // 检查延迟次数是否达到最大值，如果达到也需要删除
            if (p_alarm)
            {
                ALARM_LOG_D("Delay times: %d, max delay count: %d", p_alarm->delay_times, ALARM_MAX_DELAY_COUNT - 1);
                if (p_alarm->delay_times >= ALARM_MAX_DELAY_COUNT - 1)
                {
                    should_remove = true;
                    ALARM_LOG_D("Alarm reached max delay count, will be removed: id=%d, delay_times=%d",
                                    alarm_id, p_alarm->delay_times);
                }
            }

            if (should_remove)
            {
                ALARM_LOG_D("Found one-time alarm to remove: index=%d, id=%d, time=%d",
                          i, alarm_id, current_trig_point);

                ids_to_remove[id_count] = alarm_id;
                id_count++;
                // 在配置中将其标记为关闭
                set_alarm_open(i, false);

                // 清除闹钟ID，确保配置中不再引用已删除的闹钟
                set_alarm_id(i, 0);
            }
        }
    }

    // 第二步：停止并删除这些闹钟
    for (int i = 0; i < id_count; i++)
    {
        alarm_id_t id = ids_to_remove[i];

        if (id > 0)
        {
            // 停止延迟定时器
            // alarm_manager_stop_delay_timer(id);

            // 查找闹钟实例
            rt_alarm_t alarm = find_alarm(id);
            if (alarm)
            {
                // 先停止闹钟
                rt_err_t stop_result = rt_alarm_stop(alarm);
                ALARM_LOG_D("Stopped alarm id=%d, result=%d", id, stop_result);

                // 然后删除闹钟
                rt_err_t delete_result = rt_alarm_delete(alarm);
                ALARM_LOG_D("Deleted alarm id=%d, result=%d", id, delete_result);

                if (delete_result == RT_EOK)
                {
                    // 清理管理器中的记录
                    for (int j = 0; j < ALARM_MAX_COUNT; j++) {
                        if (mgr.entries[j].id == id) {
                            // 彻底清除所有相关字段
                            mgr.entries[j].id = 0;
                            mgr.entries[j].alarm = RT_NULL;
                            mgr.entries[j].active = RT_FALSE;
                            mgr.entries[j].delay_timer = RT_NULL;
                            mgr.entries[j].delay_pending = 0;
                            rt_memset(&mgr.entries[j].alarm_time, 0, sizeof(AlarmTime));

                            ALARM_LOG_D("Cleaned alarm entry, index %d, id=%d", j, id);
                            removed_count++;
                            break;
                        }
                    }
                }
                else
                {
                    ALARM_LOG_D("Failed to delete alarm id=%d, error=%d", id, delete_result);
                }
            }
            else
            {
                ALARM_LOG_D("Cannot find alarm instance id=%d", id);

                // 即使找不到闹钟实例，也清理管理器中的记录
                for (int j = 0; j < ALARM_MAX_COUNT; j++) {
                    if (mgr.entries[j].id == id) {
                        // 彻底清除所有相关字段
                        mgr.entries[j].id = 0;
                        mgr.entries[j].alarm = RT_NULL;
                        mgr.entries[j].active = RT_FALSE;
                        mgr.entries[j].delay_timer = RT_NULL;
                        mgr.entries[j].delay_pending = 0;
                        rt_memset(&mgr.entries[j].alarm_time, 0, sizeof(AlarmTime));

                        ALARM_LOG_D("Cleaned alarm entry, index %d, id=%d (instance missing)", j, id);
                        removed_count++;
                        break;
                    }
                }
            }
        }
    }

    // 如果有闹钟被处理，刷新闹钟列表UI
    if (removed_count > 0)
    {
        alarm_manager_refresh_alarm_list();
    }

    ALARM_LOG_D("Removed %d one-time alarms related to time %d", removed_count, target_time);
    return removed_count;
}
/**
 *
 * 该函数用于处理闹钟响铃后的恢复，确保非一次性闹钟（每天、每周）
 * 能够在下一个周期正常触发，同时清除任何延迟状态。
 *
 * @param target_time 目标触发时间点（当前实际触发时间，可能包含延迟）
 * @param include_delayed 是否包括延迟的闹钟
 * @return int 成功处理的闹钟数量
 */
int restore_non_once_alarms_by_time(uint32_t target_time, bool include_delayed)
{
    int restored_count = 0;
    uint8_t alarm_count = get_alarm_count();

    ALARM_LOG_D("Restoring non-once alarms with time %d, include_delayed=%d",
              target_time, include_delayed);

    // 第一步：找出并存储所有需要恢复的非一次性闹钟ID和原始触发时间
    struct {
        alarm_id_t id;
        uint32_t original_trig_point;
        uint8_t repeat;
    } alarms_to_restore[ALARM_MAX_COUNT] = {0};

    int restore_count = 0;

    // 遍历所有闹钟配置
    for (uint8_t i = 0; i < alarm_count; i++)
    {
        uint32_t current_trig_point = 0;
        uint8_t repeat = 0;
        bool open = false;

        // 获取闹钟详情
        get_alarm_details(i, &current_trig_point, &repeat, &open);

        // 检查是否是非一次性闹钟，且处于开启状态
        if (repeat != ONCE && open)
        {
            bool should_restore = false;
            uint8_t alarm_id = get_alarm_id_by_index(i);

            // 检查原始触发时间
            if (current_trig_point == target_time)
            {
                should_restore = true;
            }
            // 如果需要包括延迟的闹钟，则检查延迟后的时间
            else if (include_delayed)
            {
                alarm_clocks* p_alarm = get_alarm_by_id(alarm_id);

                if (p_alarm && p_alarm->delay_times > 0)
                {
                    // 计算延迟后的触发时间
                    uint32_t delayed_time = p_alarm->trig_point +
                                          ALARM_DELAY_TIME * p_alarm->delay_times;

                    if (delayed_time == target_time)
                    {
                        should_restore = true;
                    }
                }
            }

            if (should_restore && alarm_id > 0)
            {
                ALARM_LOG_D("Found non-once alarm to restore: index=%d, id=%d, time=%d, repeat=%d",
                          i, alarm_id, current_trig_point, repeat);

                alarms_to_restore[restore_count].id = alarm_id;
                alarms_to_restore[restore_count].original_trig_point = current_trig_point;
                alarms_to_restore[restore_count].repeat = repeat;
                restore_count++;

                // 重置延迟次数
                clear_single_delay_time(alarm_id);
            }
        }
    }

    // 第二步：恢复这些闹钟
    for (int i = 0; i < restore_count; i++)
    {
        alarm_id_t id = alarms_to_restore[i].id;
        uint32_t original_trig_point = alarms_to_restore[i].original_trig_point;
        uint8_t repeat = alarms_to_restore[i].repeat;

        if (id > 0)
        {
            // 停止当前闹钟的任何延迟定时器
            // alarm_manager_stop_delay_timer(id);

            // 查找闹钟实例
            rt_alarm_t alarm = find_alarm(id);
            if (alarm)
            {
                // 停止当前闹钟
                rt_err_t stop_result = rt_alarm_stop(alarm);
                ALARM_LOG_D("Stopping alarm id=%d before restoration, result=%d", id, stop_result);

                // 删除当前闹钟
                rt_err_t delete_result = rt_alarm_delete(alarm);
                ALARM_LOG_D("Deleting alarm id=%d before restoration, result=%d", id, delete_result);

                // 清理管理器中的记录，但保留ID以便后续重用
                for (int j = 0; j < ALARM_MAX_COUNT; j++) {
                    if (mgr.entries[j].id == id) {
                        mgr.entries[j].active = RT_FALSE;
                        mgr.entries[j].alarm = RT_NULL;
                        // 不要清除ID，保留以便后续重用
                        // mgr.entries[j].id = 0;
                        break;
                    }
                }

                // 创建新闹钟（复用原ID）
                AlarmTime new_time = trig_point_to_alarmtime(original_trig_point, repeat, true);
                ALARM_LOG_D("[Alarm Restore] Time params - h:%d m:%d s:%d day:%d month:%d year:%d wday:%d",
                          new_time.hour, new_time.minute, new_time.second,
                          new_time.day, new_time.month, new_time.year, new_time.wday);

                struct rt_alarm_setup new_setup = {
                    .flag = convert_alarm_type(repeat)
                };
                fill_wktime(&new_setup.wktime, &new_time);

                // 直接在原槽位重新创建闹钟
                for (int j = 0; j < ALARM_MAX_COUNT; j++) {
                    if (mgr.entries[j].id == id) {
                        mgr.entries[j].alarm = rt_alarm_create(alarm_callback, &new_setup);
                        if (!mgr.entries[j].alarm) {
                            ALARM_LOG_D("Failed to create new alarm for id=%d", id);
                            break;
                        }

                        mgr.entries[j].alarm_time = new_time;
                        mgr.entries[j].active = RT_TRUE;

                        rt_err_t ret = rt_alarm_start(mgr.entries[j].alarm);
                        if (ret == RT_EOK) {
                            ALARM_LOG_D("Successfully restored alarm id=%d to original time %d for next cycle",
                                      id, original_trig_point);
                            restored_count++;
                        } else {
                            ALARM_LOG_D("Failed to start restored alarm id=%d, error=%d", id, ret);
                        }
                        break;
                    }
                }
            }
            else
            {
                ALARM_LOG_D("Cannot find alarm instance for id=%d to restore", id);
            }
        }
    }

    if (restored_count > 0)
    {
        alarm_manager_refresh_alarm_list();
    }

    ALARM_LOG_D("Restored %d non-once alarms with time %d", restored_count, target_time);
    return restored_count;
}