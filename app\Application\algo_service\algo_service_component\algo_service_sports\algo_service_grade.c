﻿/**
 * @file algo_service_grade.c
 * <AUTHOR> (<EMAIL>)
 * @brief 坡度算法组件实现
 * @version 0.1
 * @date 2024-12-07
 *
 * @copyright Copyright (c) 2024-2025, <PERSON>han Qiwu Technology Co., Ltd
 *
 */
#include "algo_service_grade.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_task.h"
#include "qw_time_util.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"
#include "alg_grade_port.h"
#include "alg_vam_port.h"
#include "service_datetime.h"
#include "data_check.h"

// 输入数据
typedef struct
{
    int32_t altitude;        //当前气压海拔  100*m（相对高度）
    uint32_t speed;          //当前速度  1000*m/s
    int32_t diff_distance;   //本次的距离差,前后两次的水平距离(无论是否处于记录状态都有数据)
                             //这里不能输入当前距离,
                             //因为当前如果不处于记录状态有可能距离不增加,从而影响坡度,100*m
} algo_grade_sub_t;

static algo_grade_sub_t s_algo_in = {0xff, 0xff};

// 发布数据
static algo_grade_pub_t s_algo_out = {0xff};

// 中间数据
static int32_t s_distance = 0; //diff_distance的累加值

// 本算法打开标记
static bool s_is_open = false;

/**
 * @brief 算法处理
 *
 * @param algo_out 输出数据
 * @param algo_in 输入数据
 */
static void algo_grade_deal(algo_grade_pub_t *algo_out, algo_grade_sub_t *algo_in)
{
    //坡度相关
    s_distance += algo_in->diff_distance;

    alg_grade_input_t alg_grade_input = { 0 };
    alg_grade_input.timestamp = service_datetime_get_fit_time();
    alg_grade_input.dist = (float)s_distance / 100.0f;

    //检查海拔是否有效，该数据项仅在海拔校准后生效，否则为无效值（0x7fffffff）
    if (altitude_check(algo_in->altitude) == true)
    {
        alg_grade_input.alt = (float)algo_in->altitude / 100.0f;
    }
    else
    {
        //规定海拔小于-999.0m为无效值
        alg_grade_input.alt = -1000.0f;
    }

    alg_grade_output_t alg_grade_output = {
        .grade = 0.0f,
        .dalt = 0.0f,
    };

    alg_grade_exec(&alg_grade_input, &alg_grade_output);

    algo_out->grade = (int16_t)(alg_grade_output.grade * 10000.0f);
    algo_out->diff_altitude = (int16_t)(alg_grade_output.dalt * 100.0f);

    //VAM相关
    alg_vam_input_t alg_vam_input = { 0 };
    alg_vam_input.timestamp = service_datetime_get_fit_time();

    if (altitude_check(algo_in->altitude) == true)
    {
        alg_vam_input.alt = (float)algo_in->altitude / 100.0f;
    }
    else
    {
        //规定海拔小于-999.0m为无效值
        alg_vam_input.alt = -1000.0f;
    }

    alg_vam_output_t alg_vam_output = {
        .vam = 0.0f,
        .vam_30s = 0.0f,
    };

    alg_vam_exec(&alg_vam_input, &alg_vam_output);

    algo_out->vertical_speed = (int16_t)(alg_vam_output.vam * 1000.0f / 3600.0f);
    algo_out->vertical_speed_30s = (int16_t)(alg_vam_output.vam_30s * 1000.0f / 3600.0f);
}

/**
 * @brief 算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_altitude_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_GRADE;
    head.input_type = DATA_ID_ALGO_ALTITUDE;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_speed_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_GRADE;
    head.input_type = DATA_ID_ALGO_SPEED;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法控制订阅处理
 *
 * @param in 控制数据
 * @param len 数据长度
 */
static void algo_sports_ctrl_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_GRADE;
    head.input_type = DATA_ID_EVENT_SPORTS_CTRL;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_grade_out_callback(const void *out, uint32_t len)
{
    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_ALGO_GRADE, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("%s publish error", __FUNCTION__);
    }
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_altitude",
        .topic_id = DATA_ID_ALGO_ALTITUDE,
        .callback = algo_altitude_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_speed",
        .topic_id = DATA_ID_ALGO_SPEED,
        .callback = algo_speed_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
};

/**
 * @brief 算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_grade_init(void)
{
    return 0;
}

/**
 * @brief 打开算法
 *
 * @return int32_t 结果
 */
static int32_t algo_grade_open(void)
{
    if (s_is_open)
    {
        ALGO_COMP_LOG_W("%s already open", __FUNCTION__);
        return 0;
    }

    ALGO_COMP_LOG_D("%s open", __FUNCTION__);

    s_is_open = true;
    alg_grade_init();
    alg_vam_init();
    s_distance = 0;

    return algo_topic_list_subscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
}

/**
 * @brief feed算法
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_grade_feed(uint32_t input_type, void *data, uint32_t len)
{
    algo_grade_sub_t *algo_in = &s_algo_in;
    algo_grade_pub_t *algo_out = &s_algo_out;

    switch (input_type)
    {
    case DATA_ID_ALGO_ALTITUDE:
    {
        const algo_altitude_pub_t *altitude_data = (algo_altitude_pub_t *) data;
        algo_in->altitude = altitude_data->altitude_sensor;
    }
    break;
    case DATA_ID_ALGO_SPEED:
    {
        const algo_speed_pub_t *speed_data = (algo_speed_pub_t *) data;
        algo_in->speed = speed_data->enhanced_speed;
        algo_in->diff_distance = speed_data->distance_delta;
    }
    break;
    case DATA_ID_EVENT_SPORTS_CTRL:
    {
        const algo_sports_ctrl_t *sports_ctrl = (algo_sports_ctrl_t *) data;

        //数据发布
        if (sports_ctrl->ctrl_type == enum_ctrl_null)
        {
            //算法处理
            algo_grade_deal(algo_out, algo_in);

            //数据发布
            algo_grade_out_callback(algo_out, sizeof(algo_grade_pub_t));
        }
    }
    break;
    default:
        break;
    }
    return 0;
}

/**
 * @brief 关闭算法
 *
 * @return int32_t 结果
 */
static int32_t algo_grade_close(void)
{
    if (!s_is_open)
    {
        ALGO_COMP_LOG_W("%s already close", __FUNCTION__);
        return 0;
    }

    algo_topic_list_unsubscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));

    s_is_open = false;
    ALGO_COMP_LOG_D("%s close", __FUNCTION__);
    return 0;
}

// 算法组件实现
static algo_compent_ops_t s_grade_algo = {
    .init = algo_grade_init,
    .open = algo_grade_open,
    .feed = algo_grade_feed,
    .close = algo_grade_close,
    .ioctl = NULL,
};

/**
 * @brief 组件注册
 *
 * @return int32_t 结果
 */
int32_t register_grade_algo(void)
{
    algo_compnent_register(ALGO_TYPE_GRADE, &s_grade_algo);
    return 0;
}
