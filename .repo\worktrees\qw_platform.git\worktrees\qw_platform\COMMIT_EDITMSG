GUI: 表盘框架新增表盘类型字段

新增接口获取表盘类型,整体框架适配接口

Change-Id: I561c05004f110943b5451153fca685058f522a2f
Signed-off-by: ya<PERSON><PERSON><PERSON><PERSON> <yanxu<PERSON><PERSON>@igpsport.com>

# Please enter the commit message for your changes. Lines starting
# with '#' will be ignored, and an empty message aborts the commit.
#
# Date:      Tue Sep 9 15:44:02 2025 +0800
#
# On branch develop
# Your branch is ahead of 'origin/develop' by 1 commit.
#   (use "git push" to publish your local commits)
#
# Changes to be committed:
#	modified:   qwos/module/touchx/touchgfx_js/JsApp.cpp
#	modified:   qwos/module/touchx/touchgfx_js/JsApp.h
#	modified:   qwos/module/touchx/touchgfx_js/touchgfx_js_api.c
#	modified:   qwos/module/touchx/touchgfx_js/touchgfx_js_api.h
#
