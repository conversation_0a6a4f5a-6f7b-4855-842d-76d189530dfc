{"Version": 1, "WorkspaceRootPath": "E:\\Snowa\\app\\project\\WR02_Win32\\project\\simulator\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\dial_common_api.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\dial_config_api.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\SelectDialView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialTheme\\EditDialThemeView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\EditDialDataComponentView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\Launcher\\LauncherView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\Launcher\\AodScreen.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelect.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\transitions\\SlideTransition.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\QwWeekBarChart.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\Tools\\ToolsMenu\\ToolsMenuView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\TraingStatus\\HistroyTrendPage.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "dial_common_api.h", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\dial_common_api.h", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\dial_common_api.h", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\dial_common_api.h", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\dial_common_api.h", "ViewState": "AgIAADgAAAAAAAAAAAAswEkAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-09-10T02:49:15.266Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "dial_config_api.h", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\dial_config_api.h", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\dial_config_api.h", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\dial_config_api.h", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\dial_config_api.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-09-10T02:48:52.915Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "EditDialDataComponentView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\EditDialDataComponentView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\EditDialDataComponentView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\EditDialDataComponentView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\EditDialDataComponentView.cpp", "ViewState": "AgIAACUAAAAAAAAAAAAAwC0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-09T05:51:23.292Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "DialDataSelect.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelect.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelect.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelect.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\DialDataSelect\\DialDataSelect.cpp", "ViewState": "AgIAABMAAAAAAAAAAAAAwCMAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-06T03:47:46.834Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "LauncherView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Launcher\\LauncherView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Launcher\\LauncherView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Launcher\\LauncherView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Launcher\\LauncherView.cpp", "ViewState": "AgIAAEYAAAAAAAAAAAAAwFQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-05T02:16:13.776Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "EditDialThemeView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialTheme\\EditDialThemeView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialTheme\\EditDialThemeView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialTheme\\EditDialThemeView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialTheme\\EditDialThemeView.cpp", "ViewState": "AgIAANEAAAAAAAAAAAAAwOMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-08T03:36:39.793Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "AodScreen.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Launcher\\AodScreen.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Launcher\\AodScreen.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Launcher\\AodScreen.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Launcher\\AodScreen.cpp", "ViewState": "AgIAAB0AAAAAAAAAAAAswC4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-08T03:38:22.909Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "SlideTransition.hpp", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\transitions\\SlideTransition.hpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\transitions\\SlideTransition.hpp", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\transitions\\SlideTransition.hpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\transitions\\SlideTransition.hpp", "ViewState": "AgIAAMAAAAAAAAAAAAAAwNAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-09-05T03:38:11.403Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "SelectDialView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\SelectDialView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\SelectDialView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\SelectDialView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\SelectDialView.cpp", "ViewState": "AgIAAOUAAAAAAAAAAAAAwO4AAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-05T02:14:37.206Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "ToolsMenuView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Tools\\ToolsMenu\\ToolsMenuView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Tools\\ToolsMenu\\ToolsMenuView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Tools\\ToolsMenu\\ToolsMenuView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Tools\\ToolsMenu\\ToolsMenuView.cpp", "ViewState": "AgIAAAUBAAAAAAAAAAAAwBMBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-04T11:45:04.222Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "QwWeekBarChart.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\QwWeekBarChart.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\QwWeekBarChart.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\QwWeekBarChart.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\QwWeekBarChart.cpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-04T03:39:12.716Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "HistroyTrendPage.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\TraingStatus\\HistroyTrendPage.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\TraingStatus\\HistroyTrendPage.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\TraingStatus\\HistroyTrendPage.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\TraingStatus\\HistroyTrendPage.cpp", "ViewState": "AgIAAG8AAAAAAAAAAADwv30AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-04T03:03:32.61Z", "EditorCaption": ""}]}]}]}