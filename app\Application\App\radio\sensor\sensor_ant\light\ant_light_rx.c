﻿/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ant_light_rx.c
@Time    :   2024/12/18 10:30:36
<AUTHOR>   lxin
*
**************************************************************************/
#if ANT_SENSOR_LIGHT_ENABLED
#include "nrf_log.h"
#include "nrf_sdh.h"
#include "nrf_sdh_ant.h"
#include "ant_light.h"

#include "app_error.h"
#include "ant_parameters.h"
#include "ant_interface.h"
#include "basictype.h"
#include "sensor_ant_common.h"
#include "ant_light_rx.h"
#include "rttlog.h"
#include "cfg_header_def.h"
#include "qw_sensor_data.h"
#include "cfg_header_def.h"

#define setbit(X,Y)  X|=(1<<Y)
#define clrbit(X,Y)  X&=~(1<<Y)
static void ant_light_rx_evt_handler(ant_light_profile_t *p_profile, ant_light_evt_t event);
static void light_ant_evt(ant_evt_t *p_ant_evt, void *p_context);
static void ant_light_connect_cmd_set(light_data_t *p_light_data);
static void ant_light_connect(ant_light_profile_t *p_profile, light_data_t *p_light_data);

#define LIGHT_CTRL_INDEX        1

//--------------------------------------变量定义-------------------------------------------//
static ant_light_profile_t m_ant_light;
static uint8_t s_sequence_num = 0;
//--------------------------------------函数定义-------------------------------------------//

static bool check_cur_mode_enable(uint8_t cur_mode, one_light_data_t* light_state)
{
    if (light_state != NULL && (light_state->supported_standard > 0 || light_state->custom_mode_count > 0))
    {
        if ((cur_mode == 0) ||  //light_off
            (cur_mode < 15 && (light_state->supported_standard && (0x01 << cur_mode)) > 0) ||
            (cur_mode > 15 && (64 - cur_mode) <= light_state->custom_mode_count))
        {
            return true;
        }
    }
    return false;
}

static void update_saved_info(one_light_data_t* light_state)
{
    int8_t index = -1;
    sensor_search_infor_t sensor_search_infor;
    sensor_connect_infor_t sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_LIGHT, &sensor_connect);
    sensor_saved_work_t *p_sensor_saved = NULL;
    sensor_work_state_t sensor_work_state = SENSOR_WORK_STATE_IDLE;

    memset((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
    memcpy((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
    sensor_search_infor.radio_type = SENSOR_RADIO_TYPE_ANT;
    sensor_search_infor.sensor_type = SENSOR_TYPE_LIGHT;
    // sensor_saved_work_infor_get(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state);

    if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
    {
        if (SENSOR_WORK_STATE_SAVED == sensor_work_state && p_sensor_saved != NULL && index >= 0 && light_state != NULL)
        {
            p_sensor_saved->sensor_saved_infor->sensor_infor[index].cfg_param.light_cfg.standard = light_state->supported_standard;
            p_sensor_saved->sensor_saved_infor->sensor_infor[index].cfg_param.light_cfg.custom = light_state->custom_mode_count;

            if (p_sensor_saved->sensor_saved_infor->sensor_infor[index].cfg_param.light_cfg.setting_mode == 0xff &&
                light_state->cur_mode != 0)
            {
                p_sensor_saved->sensor_saved_infor->sensor_infor[index].cfg_param.light_cfg.setting_mode = light_state->cur_mode;
            }
            cfg_mark_update(enum_cfg_ant_ble_dev);
        }
        sensor_saved_work_infor_release_write_lock(index);
    }
}

/**
 * @*********************************************************************************************
 * @description: 加载ANT车灯接收通道默认配置
 * @param {ant_channel_config_t } *p_channel_config
 * @return {*}
 * @*********************************************************************************************
 */
static void LoadChnConf_light_rx(ant_channel_config_t *p_channel_config)
{
    p_channel_config->channel_number = sensor_ant_channel_num_get(SENSOR_TYPE_LIGHT);
    p_channel_config->channel_type = LIGHT_DISP_CHANNEL_TYPE;
    p_channel_config->ext_assign = LIGHT_EXT_ASSIGN;
    p_channel_config->rf_freq = LIGHT_ANTPLUS_RF_FREQ;
    p_channel_config->transmission_type = CHAN_ID_TRANS_TYPE;
    p_channel_config->device_type = LIGHT_DEVICE_TYPE;
    p_channel_config->channel_period = LIGHT_MSG_PERIOD;
    p_channel_config->network_number = ANTPLUS_NETWORK_NUM;
}

/**
 * @*********************************************************************************************
 * @description: 车灯事件处理函数
 * @param {ant_light_profile_t *} p_profile
 * @param {ant_light_evt_t} event
 * @return {*}
 * @*********************************************************************************************
 */

static uint8_t low_power_indicate_flag = FALSE;
static void ant_light_rx_evt_handler(ant_light_profile_t *p_profile, ant_light_evt_t event)
{
    sensor_search_infor_t sensor_search_infor;
    sensor_connect_infor_t sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_LIGHT, &sensor_connect);

    sensor_module_evt_handler evt_handler = sensor_module_evt_handler_get();
    sensor_saved_work_t *p_sensor_saved = NULL;
    sensor_work_state_t sensor_work_state = SENSOR_WORK_STATE_IDLE;
    sensor_module_param_input_t *p_param = sensor_module_param_input_get();
    sensor_original_data_t* p_sensor_original_data = sensor_original_data_get();
    light_data_t *p_light_data = light_data_get();
    int8_t index = -1;

    memset((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
    memcpy((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
    sensor_search_infor.radio_type = SENSOR_RADIO_TYPE_ANT;
    sensor_search_infor.sensor_type = SENSOR_TYPE_LIGHT;

    if (SENSOR_CONNECT_STATE_CONNECTING == sensor_connect.state)
    {
        sensor_connect.state = SENSOR_CONNECT_STATE_CONNECTED;
        sensor_connect_infor_set(SENSOR_TYPE_LIGHT, &sensor_connect);

        if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
        {
            if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
            {
                p_sensor_saved->sensor_work_state[index] = SENSOR_WORK_STATE_CONNECTED;
                p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
                p_light_data->one_light[LIGHT_CTRL_INDEX].setting_mode =
                    p_sensor_saved->sensor_saved_infor->sensor_infor[index].cfg_param.light_cfg.setting_mode;
                cfg_mark_update(enum_cfg_ant_ble_dev);
            }
            else if (SENSOR_WORK_STATE_FORBIDDEN == sensor_work_state)
            {
                sensor_infor_t sensor_infor = {0};
                sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                sensor_disconnect(&sensor_infor);
            }
            sensor_saved_work_infor_release_write_lock(index);
        }
        else
        {
            if(sensor_disconnect_item_check(&sensor_search_infor))
            {
                sensor_disconnect_info_remove(&sensor_search_infor);
                sensor_infor_t sensor_infor = {0};
                sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                sensor_disconnect(&sensor_infor);
                return;
            }
            sensor_saved_work_infor_add(&sensor_search_infor);
            sensor_search_infor_del(&sensor_search_infor);
        }

        if (NULL != evt_handler)
        {
            evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, TRUE);
            evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, 0);
            evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, TRUE);
        }

        low_power_indicate_flag = TRUE;
    }
    else if (SENSOR_CONNECT_STATE_CONNECTED == sensor_connect.state)
    {
        if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
        {
            if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
            {
                p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
            }
            sensor_saved_work_infor_release_write_lock(index);
        }
    }

    if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
    {
        if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
        {
#if SENSOR_DEVICE_INFO_ENABLED
            p_sensor_saved->sensor_manufacturer[index].manufacturer_ant = p_profile->page_80.manufacturer_id;
            p_sensor_saved->sensor_hw_version[index].version_ant = p_profile->page_80.hw_revision;
            p_sensor_saved->sensor_model[index].model_ant = p_profile->page_80.model_number;
            p_sensor_saved->sensor_serial[index].serial_ant = p_profile->page_81.serial_number;
            p_sensor_saved->sensor_sw_version[index].version_ant = ((((uint16_t)p_profile->page_81.sw_revision_major) << 8) | p_profile->page_81.sw_revision_minor);
#endif
        }
        sensor_saved_work_infor_release_write_lock(index);
    }

    app_light_rx_evt_handler(p_profile, event);

    if (p_profile != NULL && event == ANT_LIGHT_PAGE_1_UPDATED)
    {
        if (p_profile->page_1.battery_warning >= SENSOR_BATT_NEW && p_profile->page_1.battery_warning <= SENSOR_BATT_CRITICAL)
        {
            p_sensor_original_data->battery_list.light = (SENSOR_BATT_MAX - p_profile->page_1.battery_warning) * 20;
        }

        if (low_power_indicate_flag && p_profile->page_1.battery_warning == 5)
        {
            low_power_indicate_flag = FALSE;
            if (NULL != evt_handler)
            {
                evt_handler(EVENT_SENSOR_LOW_POWER, &sensor_search_infor.sensor_id, SENSOR_TYPE_LIGHT, SENSOR_RADIO_TYPE_ANT, 0);
            }
        }
    }
}

/**
 * @*********************************************************************************************
 * @description: 车灯ANT事件处理函数
 * @param {ant_evt_t} *p_ant_evt
 * @param {void *} p_context
 * @return {*}
 * @*********************************************************************************************
 */
static void light_ant_evt(ant_evt_t *p_ant_evt, void *p_context)
{
    sensor_search_infor_t sensor_search_infor;
    sensor_connect_infor_t sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_LIGHT, &sensor_connect);
    sensor_module_evt_handler evt_handler = sensor_module_evt_handler_get();
    sensor_saved_work_t *p_sensor_saved = NULL;
    sensor_original_data_t *p_sensor_original_data = sensor_original_data_get();
    sensor_work_state_t sensor_work_state = SENSOR_WORK_STATE_IDLE;
    int8_t index = -1;
    ret_code_t err_code = NRF_SUCCESS;
    bool manual_connect_status_sent = false;

    if (p_ant_evt->channel != m_ant_light.channel_number)
    {
        return;
    }

    sensor_systime_update();

    memset((uint8_t *)&sensor_search_infor, 0x00, sizeof(sensor_search_infor_t));
    ant_light_disp_evt_handler(p_ant_evt, p_context, &p_sensor_original_data->lightData);

    if (p_ant_evt->channel == m_ant_light.channel_number)
    {
        switch (p_ant_evt->event)
        {
        case EVENT_CHANNEL_CLOSED:
            memset((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
            memcpy((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
            sensor_search_infor.radio_type = SENSOR_RADIO_TYPE_ANT;
            sensor_search_infor.sensor_type = SENSOR_TYPE_LIGHT;

            err_code = sd_ant_channel_unassign(m_ant_light.channel_number);
            APP_ERROR_CHECK(err_code);
            m_ant_light.channel_number = 0;
            sensor_ant_channel_num_unassign(SENSOR_TYPE_LIGHT);

            bool forbidden_mask_radar = sensor_connect_infor_get_forbidden_mask(SENSOR_TYPE_RADAR);
            bool forbidden_mask_light = sensor_connect_infor_get_forbidden_mask(SENSOR_TYPE_LIGHT);
            if(sensor_connect_infor_get(SENSOR_TYPE_LIGHT, &sensor_connect))
            {
                sensor_connect_infor_clear(SENSOR_TYPE_LIGHT);

                if (NULL != evt_handler && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)
                {
                    manual_connect_status_sent = true;
                    evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, &sensor_connect.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                }
            }

            if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
            {
                if (SENSOR_WORK_STATE_IDLE != sensor_work_state)
                {
                    p_sensor_saved->rssi[index] = 0;
                    p_sensor_saved->battery_voltage[index] = 0xff;
                    p_sensor_saved->sensor_work_state[index] = SENSOR_WORK_STATE_SAVED;
                }
                sensor_saved_work_infor_release_write_lock(index);
            }

            if((forbidden_mask_radar == false && forbidden_mask_light == false && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)
                || sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)
            {
                //connected状态下断连，检索saved数组是否有同类型sensor并进行连接
                sensor_connect_from_saved_info(sensor_search_infor.sensor_type);
            }
            if (NULL != evt_handler)
            {
                evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                if (!manual_connect_status_sent)
                {
                    evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                }
            }
            p_sensor_original_data->battery_list.light = 0;
            break;

        case EVENT_RX_FAIL_GO_TO_SEARCH:
            err_code = sd_ant_channel_close(p_ant_evt->channel);
            APP_ERROR_CHECK(err_code);
            break;

        case EVENT_RX_SEARCH_TIMEOUT:
            break;

        default:
            break;
        }
    }
}

NRF_SDH_ANT_OBSERVER(m_light_ant_observer, ANT_LIGHT_ANT_OBSERVER_PRIO, light_ant_evt, &m_ant_light);

/**
 * @*********************************************************************************************
 * @description: 初始化ANT车灯通道
 * @param {ant_id_t} *id
 * @return {*}
 * @*********************************************************************************************
 */
void ant_light_rx_profile_setup(ant_id_t *id)
{
    ret_code_t err_code = NRF_SUCCESS;
    sensor_connect_infor_t sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_LIGHT, &sensor_connect);
    ant_channel_config_t channel_config;
    light_data_t *p_light_data = light_data_get();

    /*
    //device num的组成
    //1byte   1byte    |     1byte      |      1byte                  从左到右高到低
    //   device id     | device type    |MSN:extended device number LSN:Transmission Type
    */
    uint16_t sensor_id = (uint16_t)id->id;
    uint8_t trans_type = CHAN_ID_TRANS_TYPE;

    //Init light data.
    if (0xffffffffffffffff == p_light_data->light_connect_cmd_flag)
    {
        memset(p_light_data, 0, sizeof(light_data_t));
        for (uint8_t i = 0; i < MAX_SUPPORTED_LIGHTS; i++)
        {
            p_light_data->one_light[i].setting_mode = UINT8_MAX;
        }
    }

    memcpy((uint8_t *)&sensor_connect.sensor_id.ant_id, (uint8_t *)id, sizeof(ant_id_t));
    sensor_connect_infor_set(SENSOR_TYPE_LIGHT, &sensor_connect);

    if (id->id > 0xffff)
    {
        trans_type = id->trans_type;
    }

    // 加载参数
    LoadChnConf_light_rx(&channel_config);
    channel_config.device_number = sensor_id;
    channel_config.transmission_type = trans_type;

    err_code = ant_light_disp_init(&m_ant_light, (const ant_channel_config_t *)&channel_config, ant_light_rx_evt_handler);
    APP_ERROR_CHECK(err_code);

    if (0 == p_light_data->light_connect_cmd_flag) //No light connect yet.
    {
        p_light_data->main_light_dev_num = sensor_id;
        p_light_data->main_light_trans_type = trans_type;
    }

    ant_light_connect_cmd_set(p_light_data);
}

/**
 * @*********************************************************************************************
 * @description: 开启LIGHT通道
 * @param {*}
 * @return {*}
 * @*********************************************************************************************
 */
void ant_light_rx_open(void)
{
    ret_code_t err_code = NRF_SUCCESS;

    err_code = ant_light_disp_open(&m_ant_light);
    APP_ERROR_CHECK(err_code);
}

light_data_t* light_data_get(void)
{
    return &sensor_original_data_get()->lightData;
}

static void ant_light_connect_cmd_set(light_data_t *p_light_data)
{
    uint8_t i;

    if (NULL == p_light_data)
    {
        return;
    }

    for (i = 0; i < MAX_SUPPORTED_LIGHTS; i++)
    {
        if (0 == ((p_light_data->light_connect_cmd_flag >> i) & 1))
        {
            break;
        }
    }

    if (i < MAX_SUPPORTED_LIGHTS)
    {
        p_light_data->light_connect_cmd_flag |= 1L << i;
    }
}

static void ant_light_connect(ant_light_profile_t *p_profile, light_data_t *p_light_data)
{
    ant_light_message_layout_t light_message_layout;
    ant_light_page_data_33_t *p_page33 = NULL;
    uint8_t i;

    if (NULL == p_profile || NULL == p_light_data)
    {
        return;
    }

    p_page33 = &p_profile->page_33;
    light_message_layout.page_number = ANT_LIGHT_PAGE_33;

    if (p_light_data->light_connect_cmd_flag > p_light_data->light_connect_flag)
    {
        for (i = 0; i < MAX_SUPPORTED_LIGHTS; i++)
        {
            if (((p_light_data->light_connect_cmd_flag >> i) & 1) == 1 && ((p_light_data->light_connect_flag >> i) & 1) == 0)
            {
                //Tx page33.
                // p_page33->setting_index = i + 1;
                p_page33->setting_index = 1; //TODO目前只能一个灯
                p_page33->secondary_lights = 1;//TODO目前只能一个灯
                p_page33->controller_ID = get_serial_number_low() & 0xff; //LSB of ANT+ Controller’s Serial Number
                p_page33->sub_light_index = 0;
                p_page33->state = 0; //0 Invalid: Do not change light state.
                p_page33->type = p_profile->page_1.light_type;
                p_page33->device_number = p_light_data->main_light_dev_num;
                p_page33->transmission_type = 0x06;//p_light_data->main_light_trans_type;
                ant_light_page_33_encode(light_message_layout.page_payload, &p_profile->page_33);

                uint32_t err_code = sd_ant_acknowledge_message_tx(p_profile->channel_number,
                                                            sizeof (ant_light_message_layout_t),
                                                            (uint8_t *) &light_message_layout);
                if(NRF_ANT_ERROR_TRANSFER_IN_PROGRESS == err_code)
                {
                    break;
                }
                APP_ERROR_CHECK(err_code);
                break;
            }
        }
    }
}

void ant_light_disconnect(void)
{
    ant_light_message_layout_t light_message_layout;
    ant_light_page_data_32_t *p_page32 = NULL;
    uint8_t i;
    ant_light_profile_t *p_profile = &m_ant_light;
    light_data_t *p_light_data = light_data_get();

    if (NULL == p_profile || NULL == p_light_data)
    {
        return;
    }

    p_page32 = &p_profile->page_32;
    light_message_layout.page_number = ANT_LIGHT_PAGE_32;
    p_page32->controller_ID = get_serial_number_low() & 0xff; //LSB of ANT+ Controller’s Serial Number
    // p_page32->light_index = p_profile->page_1.light_index;
    p_page32->light_index = 1; //

    if (0 < p_page32->light_index)
    {
        ant_light_page_32_encode(light_message_layout.page_payload, &p_profile->page_32);

        uint32_t err_code = sd_ant_broadcast_message_tx(p_profile->channel_number,
                                                                sizeof (ant_light_message_layout_t),
                                                                (uint8_t *) &light_message_layout);

        if(NRF_ANT_ERROR_TRANSFER_IN_PROGRESS != err_code && 0 != err_code)
        {
            APP_ERROR_CHECK(err_code);
        }

        //p_light_data->light_connect_cmd_flag &= (~((uint64_t)1 << (p_page32->light_index - 1)));
        p_light_data->light_connect_cmd_flag = 0; //TODO目前只能一个灯
        p_light_data->light_connect_flag = 0; //TODO目前只能一个灯
    }
}

static void ant_light_page_request(ant_light_profile_t *p_profile, ant_light_page_t page_num)
{
    ant_light_message_layout_t light_message_layout;
    ant_light_page_data_70_t *p_page70 = NULL;
    uint8_t channelstate = 0;
    ret_code_t err_code = NRF_SUCCESS;

    if (NULL == p_profile)
    {
        return;
    }

    p_page70 = &p_profile->page_70;
    light_message_layout.page_number = ANT_LIGHT_PAGE_70;

    //Tx page70.
    p_page70->respond_light_index = 1;
    p_page70->respond_sub_light_index = 0;
    p_page70->respond_times = 1;
    p_page70->acknowledged_reply = false;
    p_page70->request_page = page_num;
    ant_light_page_70_encode(light_message_layout.page_payload, &p_profile->page_70);

    err_code = sd_ant_channel_status_get(m_ant_light.channel_number, &channelstate);
    if (STATUS_TRACKING_CHANNEL == channelstate)
    {
        err_code = sd_ant_acknowledge_message_tx(p_profile->channel_number,
                                                        sizeof (ant_light_message_layout_t),
                                                        (uint8_t *) &light_message_layout);

        if(NRF_ANT_ERROR_TRANSFER_IN_PROGRESS == err_code)
        {
            return;
        }
        APP_ERROR_CHECK(err_code);
    }
    else
    {
        rt_kprintf("%d-%s:err_code = %d,channelstate = %d.\n",__LINE__,__func__,err_code,channelstate);
    }
}

void app_light_rx_evt_handler(ant_light_profile_t *p_profile, ant_light_evt_t event)
{
    light_data_t *p_light_data = light_data_get();
    uint8_t light_index;

    if (NULL == p_profile)
    {
        return;
    }

    switch (event)
    {
    case ANT_LIGHT_PAGE_1_UPDATED:
        light_index = p_profile->page_1.light_index;

        if (p_profile->page_1.sequence_number == s_sequence_num)
        {
            s_sequence_num += 1;
        }

        if (0 == light_index) //A light not connect.
        {
            p_light_data->one_light[light_index].light_type = p_profile->page_1.light_type;
            if (check_cur_mode_enable(p_profile->page_1.cur_mode, &p_light_data->one_light[light_index]))
            {
                p_light_data->one_light[light_index].cur_mode = p_profile->page_1.cur_mode;

                update_saved_info(&p_light_data->one_light[light_index]);
            }
        }
        else if (MAX_SUPPORTED_LIGHTS >= light_index) //A light connected.
        {
            if (0 == ((p_light_data->light_connect_flag >> (light_index - 1)) & 1)) //light_cnt++
            {
                p_light_data->light_connect_flag |= 1L << (light_index - 1);
                p_light_data->light_cnt++;
            }

            p_light_data->one_light[light_index].light_type = p_profile->page_1.light_type;    //存一下灯的类型
            if (check_cur_mode_enable(p_profile->page_1.cur_mode, &p_light_data->one_light[light_index]))
            {
                if(p_light_data->one_light[light_index].cur_mode != p_profile->page_1.cur_mode)
                {
                    p_light_data->one_light[light_index].setting_mode = p_profile->page_1.cur_mode;
                }
                p_light_data->one_light[light_index].cur_mode = p_profile->page_1.cur_mode;

                update_saved_info(&p_light_data->one_light[light_index]);
            }
        }
        break;

     case ANT_LIGHT_PAGE_2_UPDATED:
        light_index = p_profile->page_2.light_index;

        if (MAX_SUPPORTED_LIGHTS >= light_index)
        {
            p_light_data->one_light[light_index].supported_count = p_profile->page_2.supported_count;
            p_light_data->one_light[light_index].supported_type = p_profile->page_2.supported_type;

            if (p_profile->page_2.supported_modes > 0)
            {
                p_light_data->one_light[light_index].supported_standard = p_profile->page_2.supported_standard;

                int std_mode_count = 0;
                for (uint8_t mode_bit = 0; mode_bit < enum_std_mode_end; mode_bit++)
                {
                    if ((p_light_data->one_light[light_index].supported_standard & (0x01 << mode_bit)) > 0)
                    {
                        std_mode_count++;
                    }
                }

                if(std_mode_count < p_profile->page_2.supported_modes)
                {
                    p_light_data->one_light[light_index].custom_mode_count = p_profile->page_2.supported_modes - std_mode_count;
                }
                else
                {
                    p_light_data->one_light[light_index].custom_mode_count = 0;
                }
            }
            else
            {
                p_light_data->one_light[light_index].supported_standard = 0;
                p_light_data->one_light[light_index].custom_mode_count = 0;
                p_light_data->one_light[light_index].cur_mode = 0;
            }
        }
        break;

    default:
        break;
    }
}

void ant_light_data_update(uint32_t runtime_ms)
{
    static uint8_t s_cmd_inter = 0;
    static uint8_t s_maintain_inter = LIGHT_MAINTAIN_INTER;
    light_data_t *p_light_data = light_data_get();
    ant_light_profile_t *p_profile = &m_ant_light;

    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_LIGHT, SENSOR_CONNECT_STATE_CONNECTED);
    if (!res)
    {
    	return;
    }

    //Check connect cmd.
    if (p_light_data->light_connect_cmd_flag > p_light_data->light_connect_flag)
    {
        for (uint8_t i = 0; i < MAX_SUPPORTED_LIGHTS; i++)
        {
            if (((p_light_data->light_connect_cmd_flag >> i) & 1) == 1 && ((p_light_data->light_connect_flag >> i) & 1) == 0)
            {
                if (0 == s_cmd_inter)
                {
                    ant_light_connect(p_profile, p_light_data);
                    s_cmd_inter = LIGHT_CMD_INTER;
                    return;
                }
                else
                {
                    s_cmd_inter--;
                }
                break;
            }
        }
    }

    //Periodically request page18 to maintain the session.
    if (0 == s_maintain_inter)
    {
        ant_light_page_request(p_profile, ANT_LIGHT_PAGE_18);
        s_maintain_inter = LIGHT_MAINTAIN_INTER;
        return;
    }
    else
    {
        s_maintain_inter--;
    }

    // Check light mode
    if (p_light_data->one_light[LIGHT_CTRL_INDEX].setting_mode < 64 &&
        p_light_data->one_light[LIGHT_CTRL_INDEX].cur_mode != p_light_data->one_light[LIGHT_CTRL_INDEX].setting_mode &&
        check_cur_mode_enable(p_light_data->one_light[LIGHT_CTRL_INDEX].setting_mode, &p_light_data->one_light[LIGHT_CTRL_INDEX]))
    {
        ant_light_page_data_34_t page_34 = {0};
        page_34.light_index = LIGHT_CTRL_INDEX;
        page_34.controller_ID = get_serial_number_low() & 0xff;
        page_34.sequence_number = s_sequence_num;
        page_34.sub_address = 1;
        page_34.light_state.light_beam = 0;
        page_34.light_state.light_mode = p_light_data->one_light[LIGHT_CTRL_INDEX].setting_mode;
        ant_light_mode_set_tx(p_profile, &page_34);
    }
}

/*****************************************************************************
 * Function      : ant_light_mode_set
 * Description   : 主灯ANT控制接口
 * Input         : uint8_t state
 * Output        : None
 * Return        :
 * Others        :
 * Record
 * 1.Date        : 20230606
 *   Author      : Noxstella
 *   Modification: Created function

*****************************************************************************/
uint16_t ant_light_supported_standard_mode_get()
{
    light_data_t *p_light_data = light_data_get();
    one_light_data_t* light_state = &p_light_data->one_light[LIGHT_CTRL_INDEX];
    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_LIGHT, SENSOR_CONNECT_STATE_CONNECTED);
    if (!res)
    {
    	return 0;
    }

    if ((light_state->supported_standard > 0 || light_state->custom_mode_count > 0))
    {
        return light_state->supported_standard;
    }
    return 0;
}

uint8_t ant_light_supported_custom_mode_get()
{
    light_data_t *p_light_data = light_data_get();
    one_light_data_t* light_state = &p_light_data->one_light[LIGHT_CTRL_INDEX];
    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_LIGHT, SENSOR_CONNECT_STATE_CONNECTED);
    if (!res)
    {
    	return 0;
    }

    if ((light_state->supported_standard > 0 || light_state->custom_mode_count > 0))
    {
        return light_state->custom_mode_count;
    }
    return 0;
}

void ant_light_mode_set(uint8_t mode)
{
    light_data_t *p_light_data = light_data_get();
    one_light_data_t* light_state = &p_light_data->one_light[LIGHT_CTRL_INDEX];
    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_LIGHT, SENSOR_CONNECT_STATE_CONNECTED);
    if (!res)
    {
    	return;
    }

    if ((light_state->supported_standard > 0 || light_state->custom_mode_count > 0))
    {
        if ((mode == 0) ||  //light_off
            (mode < 15 && (light_state->supported_standard && (0x01 << mode)) > 0) ||
            (mode > 15 && (64 - mode) <= light_state->custom_mode_count))
        {
            light_state->setting_mode = mode;
        }
    }
}

void ant_light_switch(uint8_t open)
{
    sensor_search_infor_t sensor_search_infor;
    sensor_connect_infor_t sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_LIGHT, &sensor_connect);
    sensor_saved_work_t *p_sensor_saved = NULL;
    sensor_work_state_t sensor_work_state = SENSOR_WORK_STATE_IDLE;
    int8_t index = -1;

    light_data_t *p_light_data = light_data_get();
    one_light_data_t* light_state = &p_light_data->one_light[LIGHT_CTRL_INDEX];

    memset((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
    memcpy((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
    sensor_search_infor.radio_type = SENSOR_RADIO_TYPE_ANT;
    sensor_search_infor.sensor_type = SENSOR_TYPE_LIGHT;
    // sensor_saved_work_infor_get(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state);

    if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
    {
        if (sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)
        {
            if (open)
            {
                p_sensor_saved->sensor_saved_infor->sensor_infor[index].cfg_param.light_cfg.setting_mode = light_state->setting_mode;
                ant_light_mode_set(p_sensor_saved->sensor_saved_infor->sensor_infor[index].cfg_param.light_cfg.setting_mode);
                cfg_mark_update(enum_cfg_ant_ble_dev);
            }
            else
            {
                ant_light_mode_set(0);
            }
        }
        sensor_saved_work_infor_release_write_lock(index);
    }
}

uint8_t ant_light_mode_get()
{
    light_data_t *p_light_data = light_data_get();
    one_light_data_t* light_state = &p_light_data->one_light[LIGHT_CTRL_INDEX];

    if ((light_state->supported_standard > 0 || light_state->custom_mode_count > 0))
    {
        return light_state->cur_mode;
    }
    else
    {
        return light_state->cur_mode;
    }
}

#endif
