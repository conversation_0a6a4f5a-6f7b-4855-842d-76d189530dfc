#ifndef NAVI_CLIMB_H
#define NAVI_CLIMB_H

#ifdef __cplusplus
extern "C" {
#endif

#include "navi_waypoint.h"
#include "navi_tclm.h"
#include "navi_tmpx.h"

//导航线路爬坡点采样器
typedef struct _NaviRouteCpSampler
{
    NaviClimbpoint *cp_buf;             //缓冲区，保存采样结果
    float *ad_buf;                      //缓冲区，用于中间处理
    uint32_t capacity;                  //缓冲区容量
    uint32_t len;                       //缓冲区数据数量
} NaviRouteCpSampler;

//导航线路爬坡点压缩器
typedef struct _NaviRouteCpCompressor
{
    NaviClimbpoint start;               //起点，作为参考点
    NaviClimbpoint last;                //上一个爬坡点
    NaviClimbpoint cur;                 //当前爬坡点
    float h_thres;                      //压缩阈值
    uint32_t cnt;                       //爬坡点计数
} NaviRouteCpCompressor;

//导航线路爬坡点分段器
typedef struct _NaviRouteCpSegmentor
{
    NaviRouteCpSegment *seg_buf;        //缓冲区，保存分段结果
    uint32_t capacity;                  //缓冲区容量
    uint32_t len;                       //已分段数量
    uint32_t interval;                  //当前分段间隔
    uint32_t INTERVAL;                  //初始分段间隔
    uint32_t cnt;                       //爬坡点计数
} NaviRouteCpSegmentor;

//用于检查一个爬坡是否有效，即是否满足规定的最小条件
typedef int (*climb_valid_check_t)(float dd, float dalt);

//爬坡合并器，用于将多个连续小的爬坡合并为一个大的爬坡
typedef struct _NaviClimbMerger
{
    NaviClimbInfo *buf;                 //缓冲区，保存每个小的爬坡的信息
    climb_valid_check_t valid_check;    //检查爬坡是否有效
    float dist_thres;                   //爬坡合并的距离阈值，两个爬坡距离小于该阈值才能合并
    uint32_t capacity;                  //缓冲区容量
    uint32_t len;                       //缓冲区中爬坡数量
} NaviClimbMerger;

//爬坡计算器，从输入的爬坡点中找出满足条件的爬坡，并输出这个爬坡的信息，用于后续计算爬坡的数据
typedef struct _NaviClimbCalculator
{
    NaviClimbMerger pos_merger;         //用于上坡爬坡合并
    NaviClimbMerger neg_merger;         //用于下坡爬坡合并
    NaviClimbInfo pos_climb_info;       //保存找到的上坡的信息
    NaviClimbInfo neg_climb_info;       //保存找到的下坡的信息
    NaviRouteCpInfo last;               //保存上一个爬坡点的信息
    NaviRouteCpInfo peak;               //保存自上一个爬坡点以来的峰值的信息
    NaviRouteCpInfo valley;             //保存自上一个爬坡点以来的谷值的信息
    float h_thres;                      //海拔阈值，一个爬坡点相对于上一个爬坡点的海拔变化大于该阈值才会计算坡度
    float score_thres;                  //爬坡评分阈值，仅当一个爬坡的评分大于该阈值该爬坡有效
    float min_grade;                    //规定的爬坡最小坡度（正值，不换算为百分比）
    uint32_t cnt;                       //爬坡点计数
    int32_t grade_dir;                  //坡度方向，0-平路，1-上坡，-1-下坡
} NaviClimbCalculator;

//爬坡分段器
typedef struct _NaviClimbSegmentor
{
    NaviClimbSegment *seg_buf;          //缓冲区，保存分段结果
    uint32_t capacity;                  //缓冲区容量
    uint32_t len;                       //缓冲区中结果数量
    uint32_t interval;                  //当前分段间隔
    uint32_t INTERVAL;                  //初始分段间隔
    uint32_t cnt;                       //爬坡计数
} NaviClimbSegmentor;

//爬坡数据计算器，根据输入的爬坡信息，从临时文件中读取属于该爬坡的爬坡点，进而计算出爬坡相关的数据项等
typedef struct _NaviClimbDataCalculator
{
    NaviRouteCpSampler sampler;         //用于爬坡点采样，采样结果用于计算剩余爬升和预览等
    NaviTmpRouteCpList cp_list;         //一个抽象列表，通过它可以从临时文件中获取任一爬坡点
} NaviClimbDataCalculator;

//爬坡总体数据计算器
typedef struct _NaviClimbEnsembleDataCalculator
{
    uint32_t num;                       //爬坡数量
    float h;                            //总爬升，即所有爬坡海拔变化量之和
    float dist;                         //总距离，即所有爬坡距离变化量之和
} NaviClimbEnsembleDataCalculator;

//爬坡进度
typedef struct _NaviClimbProgress
{
    uint32_t idx;                       //当前正在进行的爬坡的索引（0xFFFFFFFF表示无效）
    uint32_t idx_next;                  //下一个爬坡的索引（0xFFFFFFFF表示无效）
    uint32_t climb_remain;              //剩余爬坡数量（0xFFFFFFFF表示无效），包含当前爬坡
    float dist_remain;                  //当前爬坡剩余距离
    float h_remain;                     //当前爬坡剩余海拔
    float d_residual;                   //剩余的总爬坡距离
    float h_residual;                   //剩余的总爬坡爬升
    float d2climb;                      //当前没有进行爬坡时，距离接下来的那个爬坡的距离，小于0表示无效
} NaviClimbProgress;

//爬坡匹配器，根据输入的距离找到对应的爬坡，并计算爬坡进度
typedef struct _NaviClimbMatcher
{
    NaviClimbEnsembleData *ensemble;    //爬坡总体数据，用于计算剩余总爬升等数据
    NaviClimbList *climb_list;          //一个抽象列表，通过它可以获取任一爬坡的数据
    NaviClimbSegmentArray *seg_array;   //爬坡分段，便于查找当前进行的爬坡
} NaviClimbMatcher;

typedef struct _NaviRouteCpLoader
{
    NaviRouteCpList *cp_list;           //一个抽象列表，通过它可以从tclm文件中获取任一爬坡点
    NaviRouteCpSegmentArray *seg_array; //爬坡点分段
    NaviClimbpoint *buf;                //缓冲区，保存加载的爬坡点
    Range range;                        //加载的爬坡点范围
    uint32_t capacity;                  //缓冲区容量
    uint32_t len;                       //缓冲区中数据数量
    uint32_t seg_idx;                   //上一次的距离输入对应的分段索引
    float ref_dist;                     //上一次的距离输入
    uint32_t ref_idx;                   //上一次的距离输入对应的爬坡点索引
} NaviRouteCpLoader;

//NaviRouteCpSampler
int navi_route_cp_sampler_exec(NaviRouteCpSampler *self, const NaviClimbpoint *cp);

void navi_route_cp_sampler_data_get(NaviRouteCpSampler *self, NaviClimbSample *sample);

void navi_route_cp_sampler_data_copy(NaviRouteCpSampler *self, NaviClimbSample *sample);

void navi_route_cp_sampler_reset(NaviRouteCpSampler *self);

//NaviRouteCpCompressor
int navi_route_cp_compressor_exec(NaviRouteCpCompressor *self, const NaviWaypointAdc *wpadc, NaviClimbpoint *output);

void navi_route_cp_compressor_reset(NaviRouteCpCompressor *self);

//NaviRouteCpSegmentor
int navi_route_cp_segmentor_exec(NaviRouteCpSegmentor *self, const NaviClimbpoint *cp);

void navi_route_cp_segmentor_end(NaviRouteCpSegmentor *self);

void navi_route_cp_segmentor_data_get(NaviRouteCpSegmentor *self, NaviRouteCpSegmentArray *array);

void navi_route_cp_segmentor_reset(NaviRouteCpSegmentor *self);

//NaviClimbCalculator
int navi_climb_calculator_exec(NaviClimbCalculator *self, const NaviClimbpoint *cp, NaviClimbInfoList *output);

int navi_climb_calculator_end(NaviClimbCalculator *self, const NaviClimbpoint *cp, NaviClimbInfoList *output);

void navi_climb_calculator_reset(NaviClimbCalculator *self);

//NaviClimbSegmentor
int navi_climb_segmentor_exec(NaviClimbSegmentor *self, const NaviClimbInfo *ci);

void navi_climb_segmentor_end(NaviClimbSegmentor *self, float dist_max);

void navi_climb_segmentor_data_get(NaviClimbSegmentor *self, NaviClimbSegmentArray *array);

void navi_climb_segmentor_reset(NaviClimbSegmentor *self);

//NaviClimbDataCalculator
int navi_climb_data_calculator_exec(NaviClimbDataCalculator *self, const NaviClimbInfo *info, NaviClimbData *output);

void navi_climb_data_calculator_reset(NaviClimbDataCalculator *self);

//NaviClimbEnsembleDataCalculator
int navi_climb_ensemble_data_calculator_exec(NaviClimbEnsembleDataCalculator *self, const NaviClimbData *data);

void navi_climb_ensemble_data_calculator_data_get(NaviClimbEnsembleDataCalculator *self, NaviClimbEnsembleData *data);

void navi_climb_ensemble_data_calculator_reset(NaviClimbEnsembleDataCalculator *self);

//NaviClimbMatcher
int navi_climb_matcher_exec(NaviClimbMatcher *self, float dist, uint8_t is_reverse, NaviClimbProgress *output);

void navi_climb_matcher_reset(NaviClimbMatcher *self);

//NaviRouteCpLoader
int navi_route_cp_loader_exec(NaviRouteCpLoader *self, float dist, uint8_t is_reverse);

void navi_route_cp_loader_data_get(NaviRouteCpLoader *self, NaviRouteCpNearby *cp_nearby);

void navi_route_cp_loader_reset(NaviRouteCpLoader *self);

#ifdef __cplusplus
}
#endif

#endif