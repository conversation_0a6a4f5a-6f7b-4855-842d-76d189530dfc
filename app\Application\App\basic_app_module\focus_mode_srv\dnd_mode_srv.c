/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   dnd_mode_srv.c
@Time    :   2025/01/10 11:13:40
*
**************************************************************************/
#include "focus_mode_srv.h"
#include "cfg_header_def.h"
#include "algo_service_sport_status.h"
#ifdef SIMULATOR
#include "activity_record/activity_fit_app.h"
#endif
/*
手动和定时相互独立，手动生效时，定时不生效，


dnd_auto_start_usable / dnd_auto_end_usable 可用时，当时间到达后，会执行任务，否则跳过执行。
dnd_in_auto_time 被off on任务管理，被提交、取消任务管理
*/

typedef struct
{
    uint8_t dnd_state;              //勿扰模式状态
    uint8_t dnd_manual_btn_state;   //手动开关状态 0:关闭 1:手动开启 2：定时开启
    bool dnd_auto_btn_state;      //定时开关状态
    uint32_t dnd_start_time;      //定时勿扰开始时间
    uint32_t dnd_end_time;        //定时勿扰结束时间
    bool dnd_auto_start_usable;   //时间段内的开始事件是否可用
    bool dnd_auto_end_usable;     //时间段内的结束事件是否可用
    bool dnd_in_auto_time;        //当前时间是否在定时勿扰时间段内

    //接入运动逻辑
    bool dnd_sport;                 // 当前运动状态
    bool dnd_pre_sport_state;       // 运动前的勿扰模式状态
    uint8_t dnd_sport_btn;          // 运动中手动设置的勿扰状态 (0:未设置, 1:开启, 2:关闭)
    // uint8_t dnd_sport_auto_btn;     // 运动中自动设置的勿扰状态 (0:未设置, 1:开启, 2:关闭)
} dnd_srv_cgf_t;

#ifdef  IGS_DEV
static alarm_id_t alarm_id_on, alarm_id_off;
#endif

static dnd_srv_cgf_t dnd_srv_parm = {0};
static founc_evt_service_cmd_t info;

static inline bool dnd_is_in_time()
{
    qw_tm_t dnd_now_tm = {0};
    service_datetime_get(&dnd_now_tm);
    uint32_t cur_time = dnd_now_tm.tm_hour * 3600 + dnd_now_tm.tm_min * 60;
    if (cur_time >= dnd_srv_parm.dnd_start_time && cur_time < dnd_srv_parm.dnd_end_time)
    {
        return true;
    }
    else
    {
        return false;
    }
}

//更新勿扰模式状态,执行相关操作
static void dnd_srv_state_update(uint8_t state);
//创建定时勿扰任务
static void dnd_create_timer_task(void);
//取消定时勿扰任务
static void dnd_delete_timer_task(void);
//操作手动按钮执行相关操作
static void dnd_ops_manual(bool state);
//操作定时按钮执行相关操作
static void dnd_ops_auto(bool state);
//设置定时勿扰时间执行相关操作
static void dnd_ops_set_time(uint8_t type, uint16_t dnd_time);
//运动状态更新时的操作
static void dnd_ops_sport_evt(bool state);

static void dnd_srv_state_update(uint8_t state)
{
    //更新参数
    set_focus_DndStatus(state);
    dnd_srv_parm.dnd_state = state;
    //执行操作
    if (state >= 1)
    {
        // msg_msgbox_enable(false);
    }
    else
    {
        // msg_msgbox_enable(true);
    }
}
#ifdef  IGS_DEV
static void dnd_auto_task_off(rt_alarm_t alarm, time_t timestamp)
{
    dnd_srv_parm.dnd_in_auto_time = false;
    //检查本次是否应该执行
    if (!dnd_srv_parm.dnd_auto_start_usable)
    {
        dnd_srv_parm.dnd_auto_start_usable = true;
        return;
    }
    //若存在手动操作，则不执行
    if (dnd_srv_parm.dnd_manual_btn_state == 1)
    {
        //手动开启自动跳过不执行
    }
    else
    {
        // dnd_srv_state_update(false);
        info.evt = FOUCS_ON_DND;
        info.state = false;
        submit_gui_event(GUI_EVT_SERVICE_FOCUS_ON, 0, &info);
    }
}

static void dnd_auto_task_on(rt_alarm_t alarm, time_t timestamp)
{
    dnd_srv_parm.dnd_in_auto_time = true;
    //检查本次是否应该执行
    if (!dnd_srv_parm.dnd_auto_end_usable)
    {
        dnd_srv_parm.dnd_auto_end_usable = true;
        return;
    }
    //若存在手动操作，则不执行
    if (dnd_srv_parm.dnd_manual_btn_state == 1)
    {
        //手动关闭自动跳过不执行
    }
    else
    {
        // dnd_srv_state_update(true);
        info.evt = FOUCS_ON_DND;
        info.state = true;
        submit_gui_event(GUI_EVT_SERVICE_FOCUS_ON, 0, &info);
    }
}
#endif


static void dnd_create_timer_task(void)
{
#ifdef  IGS_DEV
    alarm_id_on = alarm_manager_create(dnd_srv_parm.dnd_start_time, 0x80, dnd_auto_task_on);
    alarm_id_off = alarm_manager_create(dnd_srv_parm.dnd_end_time, 0x80, dnd_auto_task_off);

    alarm_manager_enable(alarm_id_on, true);
    alarm_manager_enable(alarm_id_off, true);
#endif
}

static void dnd_delete_timer_task(void)
{
#ifdef  IGS_DEV
    alarm_manager_enable(alarm_id_on, false);
    alarm_manager_enable(alarm_id_off, false);
    alarm_manager_delete(alarm_id_on);
    alarm_manager_delete(alarm_id_off);
#endif
}

/*
操作手动按钮，
--禁用定时任务：直接更新勿扰状态，
--启用定时任务：
----段内：
------手动开启：
---------允许执行：禁止执行关闭任务，勿扰开启
---------禁止执行：？？
------手动关闭：
---------允许执行：？？
---------禁止执行：允许执行关闭任务，勿扰关闭
----段外：
------手动开启：直接开启勿扰
------手动关闭：直接关闭勿扰
*/

static void dnd_ops_manual(bool state)
{
    if (!dnd_srv_parm.dnd_auto_btn_state)
    {
        dnd_srv_state_update(state);
    }
    else
    {
        if (dnd_is_in_time())
        {
            if (state)
            {
                if (dnd_srv_parm.dnd_auto_end_usable)
                {
                    dnd_srv_parm.dnd_auto_end_usable = false;
                    dnd_srv_state_update(true);
                }
                else
                {
                }
            }
            else
            {
                dnd_srv_parm.dnd_auto_end_usable = true;
                dnd_srv_state_update(false);
            }
        }
        else
        {
            dnd_srv_state_update(state);
        }
    }
}

/*
手动操作定时勿扰开关
// --手动勿扰关闭
----启用定时任务：提交任务
------段内：开启勿扰
------段外：
----禁用定时任务：取消任务
------段内：关闭勿扰
------段外：

// --手动勿扰开启
*/

static void dnd_ops_auto(bool state)
{
    if (state)
    {
        //提交定时勿扰任务（开、关）
        dnd_create_timer_task();
        //如果当前时间在定时勿扰时间段内，则立即执行
        if (dnd_is_in_time())
        {
            dnd_srv_parm.dnd_manual_btn_state = 2;
            set_focus_DndEn(dnd_srv_parm.dnd_manual_btn_state);
            dnd_srv_state_update(true);
        }
        else
        {
        }
    }
    else
    {
        //取消定时勿扰任务（开、关）
        dnd_delete_timer_task();
        if (dnd_is_in_time())
        {
            if (dnd_srv_parm.dnd_manual_btn_state == 2)
            {
                dnd_srv_parm.dnd_manual_btn_state = 0;
                set_focus_DndEn(dnd_srv_parm.dnd_manual_btn_state);
                dnd_srv_state_update(false);
            }
        }
    }
}

//设置定时勿扰时间 type:0-start,1-end
/*
开始时间设置：
--段内
----立即执行
--段外
*/

static void dnd_ops_set_time(uint8_t type, uint16_t dnd_time)
{
    if (type == 0)
    {
        dnd_delete_timer_task();
        dnd_create_timer_task();
        set_focus_DndStartTime(dnd_srv_parm.dnd_start_time);
        if (dnd_is_in_time())
        {
            dnd_srv_parm.dnd_manual_btn_state = 2;
            set_focus_DndEn(dnd_srv_parm.dnd_manual_btn_state);
            dnd_srv_state_update(true);
        }
        else
        {
            if (dnd_srv_parm.dnd_state && dnd_srv_parm.dnd_manual_btn_state == 2)
            {
                dnd_srv_parm.dnd_manual_btn_state = 0;
                set_focus_DndEn(dnd_srv_parm.dnd_manual_btn_state);
                dnd_srv_state_update(false);
            }
        }
    }
    else
    {
        dnd_delete_timer_task();
        dnd_create_timer_task();
        set_focus_DndEndTime(dnd_srv_parm.dnd_end_time);
        if (dnd_is_in_time())
        {
            dnd_srv_parm.dnd_manual_btn_state = 2;
            set_focus_DndEn(dnd_srv_parm.dnd_manual_btn_state);
            dnd_srv_state_update(true);
        }
        else
        {
            if (dnd_srv_parm.dnd_state && dnd_srv_parm.dnd_manual_btn_state == 2)
            {
                dnd_srv_parm.dnd_manual_btn_state = 0;
                set_focus_DndEn(dnd_srv_parm.dnd_manual_btn_state);
                dnd_srv_state_update(false);
            }
        }
    }
}

static void dnd_ops_sport_evt(bool state)
{
    if(state)     //运动开启
    {
        // 备份当前勿扰状态
        dnd_srv_parm.dnd_pre_sport_state = dnd_srv_parm.dnd_state;
        if(dnd_srv_parm.dnd_state)   //如果当前勿扰开启，则关闭，运动后应该恢复成改状态
        {
            set_focus_DndEn(false);
            dnd_srv_state_update(false);
        }
    }
    else        //运动结束
    {
        //检查是否手动修改
        if(dnd_srv_parm.dnd_sport_btn!=0)
        {
            bool new_state = (dnd_srv_parm.dnd_sport_btn == 1);
            dnd_srv_state_update(new_state);
            set_focus_DndEn(new_state);

             // 更新手动按钮状态
             dnd_srv_parm.dnd_manual_btn_state = new_state ? 1 : 0;
             set_focus_DndEn(dnd_srv_parm.dnd_manual_btn_state);

             // 重置运动中设置状态
             dnd_srv_parm.dnd_sport_btn = 0;
        }
        //检查是否有备份
        else if(dnd_srv_parm.dnd_pre_sport_state)
        {
            dnd_srv_state_update(true);
            set_focus_DndEn(true);
        }

        // 检查是否自动修改  /  检查是否在定时勿扰时间段内
        if (dnd_srv_parm.dnd_auto_btn_state && dnd_is_in_time())
        {
            // 在定时勿扰时间段内，更新状态为定时开启
            dnd_srv_parm.dnd_manual_btn_state = 2;
            set_focus_DndEn(dnd_srv_parm.dnd_manual_btn_state);
            dnd_srv_state_update(true);
        }
    }
}






void dnd_sumit_work(dnd_ops_evt_t evt, void *arg1)
{
    switch (evt)
    {
    case DND_OPS_MANUAL://手动修改了勿扰状态
        if(!dnd_srv_parm.dnd_sport)//非运动中修改
        {
            dnd_srv_parm.dnd_manual_btn_state = *(uint8_t *) arg1;
            set_focus_DndEn(dnd_srv_parm.dnd_manual_btn_state);
            dnd_ops_manual(dnd_srv_parm.dnd_manual_btn_state);
        }
        else //运动中修改
        {
            dnd_srv_parm.dnd_sport_btn = *(uint8_t *) arg1;
        }
        break;
    case DND_OPS_AUTO:
        dnd_srv_parm.dnd_auto_btn_state = *(bool *) arg1;
        set_focus_DndTime(dnd_srv_parm.dnd_auto_btn_state);
        dnd_ops_auto(dnd_srv_parm.dnd_auto_btn_state);
        break;
    case DND_OPS_AUTO_START:
        dnd_srv_parm.dnd_start_time = *(uint32_t *) arg1;
        dnd_ops_set_time(0, dnd_srv_parm.dnd_start_time);
        break;
    case DND_OPS_AUTO_END:
        dnd_srv_parm.dnd_end_time = *(uint32_t *) arg1;
        dnd_ops_set_time(1, dnd_srv_parm.dnd_end_time);
        break;
    case DND_OPS_USABLE:
        break;
    case DND_OPS_UPDATE:
        dnd_srv_state_update(*(bool *) arg1);
        break;
    case DND_OPS_SPORT://改变运动状态
        {
            bool new_sport_state = *(bool *)arg1;

            // 只有当运动状态确实发生变化时才处理
            if (dnd_srv_parm.dnd_sport != new_sport_state)
            {
                dnd_srv_parm.dnd_sport = new_sport_state;
                dnd_ops_sport_evt(dnd_srv_parm.dnd_sport);
            }
        }
        break;
    default:
        break;
    }
}


void dnd_srv_init(void)
{
    dnd_srv_parm.dnd_state = (bool)get_focus_DndStatus();
    dnd_srv_parm.dnd_manual_btn_state = get_focus_DndEn();
    dnd_srv_parm.dnd_auto_btn_state = get_focus_DndTime();
    dnd_srv_parm.dnd_start_time = get_focus_DndStartTime();
    dnd_srv_parm.dnd_end_time = get_focus_DndEndTime();
    dnd_srv_parm.dnd_auto_start_usable = true;
    dnd_srv_parm.dnd_auto_end_usable = true;
    dnd_srv_parm.dnd_in_auto_time = false;
#ifndef SIMULATOR
    dnd_srv_parm.dnd_sport = get_sport_status();
#else
    dnd_srv_parm.dnd_sport = get_simulator_sport_status();
#endif
    dnd_srv_parm.dnd_sport_btn = 0;
    dnd_srv_parm.dnd_pre_sport_state = false;

    if(dnd_srv_parm.dnd_auto_btn_state)
    {
        dnd_ops_auto(dnd_srv_parm.dnd_auto_btn_state);
        if(!dnd_is_in_time())
        {
            if(dnd_srv_parm.dnd_state && dnd_srv_parm.dnd_manual_btn_state != 1)
            {
                dnd_srv_parm.dnd_manual_btn_state = 0;
                set_focus_DndEn(dnd_srv_parm.dnd_manual_btn_state);
                dnd_srv_state_update(false);
            }
        }
    }
}

void show_dnd_mode_arg()
{
    FOCUS_ON_LOG_D("DND MODE ARG:\n \
    \t state:%d \t manual_btn_state:%d \t auto_btn_state:%d \t \n \
    \t start_time:%d \t end_time:%d \t \n \
    \t auto_start_usable:%d \t auto_end_usable:%d \t\n \
    \t sport:%d \t sport_btn:%d \n",
    dnd_srv_parm.dnd_state,dnd_srv_parm.dnd_manual_btn_state,dnd_srv_parm.dnd_auto_btn_state,
    dnd_srv_parm.dnd_start_time,dnd_srv_parm.dnd_end_time,
    dnd_srv_parm.dnd_auto_start_usable,dnd_srv_parm.dnd_auto_end_usable,
    dnd_srv_parm.dnd_sport,dnd_srv_parm.dnd_sport_btn);
}


bool dnd_mode_status_refresh(void)
{
    return true;
}

bool get_dnd_manual_btn_state(void)
{
    return (bool) dnd_srv_parm.dnd_manual_btn_state;
}

bool get_dnd_auto_btn_state(void)
{
    return dnd_srv_parm.dnd_auto_btn_state;
}

uint32_t get_dnd_start_time(void)
{
    return dnd_srv_parm.dnd_start_time;
}

uint32_t get_dnd_end_time(void)
{
    return dnd_srv_parm.dnd_end_time;
}

uint8_t get_dnd_state(bool true_state)
{
    if(true_state)
    {
        return dnd_srv_parm.dnd_state;
    }
    if(dnd_srv_parm.dnd_sport)
    {
        return dnd_srv_parm.dnd_sport_btn == 1 ?true:false;
    }
    return dnd_srv_parm.dnd_state;
}

bool get_dnd_sport_btn(void)
{
    return dnd_srv_parm.dnd_sport_btn == 1 ?true:false;
}

bool get_dnd_bak_state(void)
{
    return dnd_srv_parm.dnd_pre_sport_state;
}

bool get_dnd_sport(void)
{
    return dnd_srv_parm.dnd_sport;
}


//------------------------------------------------测试函数----------------------------------------------------------------
static int focus_on(int argc, char **argv)
{
    int ret = 0;
    if (argc < 2)
    {
        FOCUS_ON_LOG_D("Usage: %s <command>\n", argv[0]);
        FOCUS_ON_LOG_D("<command>: sleep or dnd\n");
        return -1;
    }
    if (strcmp(argv[1], "sleep") == 0)
    {
        show_sleep_mode_arg();
    }
    else if (strcmp(argv[1], "dnd") == 0)
    {
        show_dnd_mode_arg();
    }
    return 0;
}

FINSH_FUNCTION_EXPORT(focus_on,focus on test);
MSH_CMD_EXPORT(focus_on,focus on test);
