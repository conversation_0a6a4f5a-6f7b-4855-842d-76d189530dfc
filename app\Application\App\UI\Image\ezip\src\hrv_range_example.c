#include "lvgl.h" 

#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP ALIGN(4)
#endif

#ifndef eZIP_RGBARGB888A
#define eZIP_RGBARGB888A
#endif


LV_ATTRIBUTE_MEM_ALIGN_EZIP const  uint8_t hrv_range_example_map[] SECTION(".ROM3_IMG_EZIP.hrv_range_example") = { 
    0x00,0x00,0x02,0x1f,0x46,0x08,0x20,0x00,0x00,0x1b,0x00,0x14,0x00,0x00,0x00,0x00,0x00,0x14,0x00,0x01,0x00,0x00,0x00,0x60,0xb5,0x7a,0x59,0x8d,0x22,0x88,0xc2,0x00,
    0xfc,0xff,0xd5,0x97,0x89,0x46,0xe2,0x46,0x31,0xe2,0x26,0x01,0x89,0x9b,0x4c,0xbb,0x4a,0x08,0xc4,0x8d,0xef,0x20,0xfa,0x80,0x3e,0x84,0x3e,0x40,0x76,0x22,0x98,0xcb,
    0x46,0x30,0x90,0x8d,0x82,0xa0,0x1b,0x17,0xc1,0xb9,0xd5,0xef,0xe9,0xee,0xc9,0xd0,0xd5,0x5d,0x25,0x13,0xb1,0x37,0x33,0x7c,0x9c,0xa9,0xaa,0xae,0x73,0x19,0x00,0x00,
    0xe5,0xab,0xc7,0xd3,0xb7,0x58,0x3e,0x53,0xf2,0xd0,0x43,0x8b,0x0d,0xe1,0x23,0x3a,0x4f,0xda,0x71,0xe8,0x85,0xc5,0x06,0x62,0x4e,0x73,0x05,0xee,0x6e,0xbe,0x78,0xea,
    0xe1,0x0c,0xd8,0x1b,0x79,0x9c,0x75,0x03,0xd2,0xce,0x07,0x33,0x70,0x6f,0xa4,0x41,0xfc,0xd2,0x35,0xf0,0xd5,0x66,0x53,0xba,0x71,0x21,0xff,0x81,0xc4,0x2c,0x3c,0x65,
    0xca,0x51,0x15,0x52,0x2c,0xbe,0x1a,0x45,0x3d,0xab,0x9a,0xcd,0xec,0x24,0xbb,0xf2,0x28,0x4b,0xf0,0x73,0x37,0x20,0xed,0xd8,0xf5,0x9e,0xe6,0x08,0x7c,0x4e,0xee,0xd4,
    0x9e,0x47,0x1d,0xa5,0x5b,0x9e,0xf2,0xb8,0xec,0x5d,0xc7,0xdf,0x9d,0xc7,0xfd,0x6b,0xaa,0x9f,0x09,0xf0,0x22,0xed,0xfe,0xdc,0xd9,0x82,0x63,0x07,0x5d,0x65,0xd4,0xb7,
    0xde,0x46,0x51,0x9f,0xd4,0xae,0xf5,0xbd,0x5e,0x27,0x17,0xcd,0xf1,0xd5,0xd9,0x55,0x3d,0x1f,0x21,0x3c,0x8d,0xa0,0x7b,0x31,0x07,0xb8,0x39,0xaf,0x9d,0x5c,0xd3,0xb5,
    0x59,0xaf,0x53,0x2c,0xdf,0xd6,0x95,0xd2,0x85,0x13,0x7f,0xf5,0x92,0x39,0x8e,0xf9,0x84,0xaa,0x5a,0xd7,0xc0,0xf3,0xa8,0xb3,0x75,0xb6,0xee,0x0a,0xe0,0x34,0x48,0x26,
    0xb8,0x3d,0x87,0xb6,0xe3,0xce,0xa1,0x93,0x8f,0x6a,0x1f,0x45,0xdd,0x05,0x5e,0x17,0x88,0x7a,0x15,0x38,0xb6,0x80,0xb3,0xb5,0x5d,0xac,0x4a,0xe8,0x3c,0xee,0x3e,0xf0,
    0x3c,0x08,0x20,0x9e,0x5a,0xfb,0x21,0x17,0x2e,0xfb,0x4e,0xc9,0x5c,0x51,0x2f,0x84,0x2f,0xa1,0x33,0xea,0xab,0xa6,0x06,0x95,0x4d,0xc1,0xa3,0xb2,0x3d,0x7d,0x27,0xf7,
    0x8b,0xc6,0x0b,0xf6,0x8a,0x88,0x70,0x69,0xc7,0x51,0xde,0x5b,0xc7,0x5e,0xcf,0xad,0x36,0x9b,0xd8,0xa4,0xc8,0xa1,0xcb,0x4c,0xf8,0x1e,0x26,0x39,0x8f,0xba,0xf5,0x5a,
    0x95,0xc3,0x47,0x1c,0x8d,0xdb,0x6f,0x42,0x07,0xda,0x09,0xe2,0xa5,0xfb,0x73,0x61,0xbf,0x14,0x83,0x24,0xa7,0xdd,0x6d,0xb5,0x9e,0x45,0x9c,0x11,0xe7,0x96,0x15,0xcc,
    0x7e,0x3b,0xae,0xec,0xf4,0x56,0xd2,0x9f,0x08,0x5d,0x87,0x77,0x9f,0xf2,0x66,0x2e,0x9e,0x12,0x7e,0xe8,0x88,0x39,0x1b,0x77,0x0b,0xf2,0xc9,0x9c,0x7e,0xc7,0x4a,0xfa,
    0xa2,0x1b,0x90,0x74,0xc1,0x5c,0x3b,0x96,0xdb,0xf3,0xa8,0x2b,0xed,0xee,0x37,0xf0,0xd2,0x76,0x3d,0xe9,0xcf,0xb3,0xa4,0x3b,0xfe,0x83,0xe3,0x64,0x55,0x8d,0xb6,0xeb,
    0x55,0xaf,0x81,0x9f,0xfd,0x1f,0xc7,0xd2,0xd5,0xb8,0xb3,0x7f,0xdf,0x77,0x61,0xe9,0xaa,0xb4,0xa1,0x7a,0x30,0x70,0xb9,0xa2,0xf6,0xbb,0xbc,0x8d,0xf3,0xe0,0x0e,0xfc,
    0xfb,0x55,0x9f,0x65,0xd0,0xcf,0x6e,0xd0,0x35,0xdc,0xeb,0xfa,0x73,0xe0,0x4e,0x6f,0x9a,0xb6,0xf3,0xb7,0x73,0x9b,0xaf,0x3f,0x6e,0xec,0x0f,0x0b,0xf3,0xa1,0x63,
};


#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER ALIGN(4)
#endif
LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER const eZIP_RGBARGB888A  lv_img_dsc_t hrv_range_example SECTION(".ROM3_IMG_EZIP_HEADER.hrv_range_example") = { 
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .header.always_zero = 0,
  .header.w = 27,
  .header.h = 20,
  .data_size  = 543,
  .data = hrv_range_example_map
};
