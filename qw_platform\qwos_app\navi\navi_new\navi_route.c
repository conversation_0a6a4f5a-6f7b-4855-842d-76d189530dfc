#include <string.h>
#include <stddef.h>
#include <stdbool.h>
#include <math.h>
#include "navi_util.h"
#include "navi_route.h"

//导航线路路点过滤
//-1 - 输入无效
//0 - 计算完成，输出有效
//1 - 输入路点被过滤，输出无效
int navi_route_wp_filter_exec(NaviRouteWpFilter *self, const NaviWaypointA *wpa, NaviWaypointAdc *output)
{
    if (self == NULL || wpa == NULL || output == NULL)
    {
        return -1;
    }

    if (navi_util_is_coord_valid(wpa->lng, wpa->lat) == false)
    {
        return -1;
    }

    if (self->cnt == 0)
    {
        self->wpadc.lng = wpa->lng;
        self->wpadc.lat = wpa->lat;
        self->wpadc.alt = wpa->alt;
        self->cnt = 1;
        return 1;
    }

    const float dist = navi_util_seg_dist_calc(self->wpadc.lng, self->wpadc.lat, wpa->lng, wpa->lat);
    if (dist < 4.0f)
    {
        return 1;
    }

    //首个有效路点必须输出，因此每次都是输出上一个有效路点
    navi_waypoint_adc_copy(output, &self->wpadc);

    const float course = navi_util_course_calc(self->wpadc.lng, self->wpadc.lat, wpa->lng, wpa->lat);
    self->wpadc.lng = wpa->lng;
    self->wpadc.lat = wpa->lat;
    self->wpadc.alt = wpa->alt;
    self->wpadc.dist += dist;
    self->wpadc.course = course;

    //首个有效路点的航向必须等到第二个有效路点到来才能计算得到
    if (self->cnt == 1)
    {
        output->course = self->wpadc.course;
    }

    self->cnt += 1;

    return 0;
}

//获取最后一个有效路点
int navi_route_wp_filter_the_last_get(NaviRouteWpFilter *self, NaviWaypointAdc *output)
{
    if (self == NULL || output == NULL)
    {
        return -1;
    }

    //线路没有有效路点或者只有一个有效路点，则导航线路无效
    if (self->cnt < 2)
    {
        return -1;
    }

    navi_waypoint_adc_copy(output, &self->wpadc);

    return 0;
}

//重置路点过滤器
void navi_route_wp_filter_reset(NaviRouteWpFilter *self)
{
    if (self != NULL)
    {
        self->cnt = 0;
        navi_waypoint_adc_reset(&self->wpadc);
    }
}

//导航线路数据计算，所有路点计算完成后，获取最终结果即可，中间结果没有意义
int navi_route_data_calculator_exec(NaviRouteDataCalculator *self, const NaviWaypointAd *wpad)
{
    if (self == NULL || wpad == NULL)
    {
        return -1;
    }

    if (self->is_first == true)
    {
        navi_waypoint_ad_copy(&self->wpad, wpad);
        bbox_update(&self->bbox, wpad->lng, wpad->lng, wpad->lat, wpad->lat);
        self->alt_min = wpad->alt;
        self->alt_max = wpad->alt;
        self->is_first = false;
        return 0;
    }

    bbox_extend(&self->bbox, wpad->lng, wpad->lat);

    self->dist = wpad->dist;

    if (wpad->alt > self->wpad.alt)
    {
        self->ascent += (wpad->alt - self->wpad.alt);
    }
    else
    {
        self->descent += (self->wpad.alt - wpad->alt);
    }

    if (wpad->alt > self->alt_max)
    {
        self->alt_max = wpad->alt;
    }

    if (wpad->alt < self->alt_min)
    {
        self->alt_min = wpad->alt;
    }

    navi_waypoint_ad_copy(&self->wpad, wpad);

    return 0;
}

//获取导航线路整体数据，应该仅在所有路点计算完成后调用
int navi_route_data_calculator_data_get(NaviRouteDataCalculator *self, NaviRouteData *output)
{
    if (self == NULL || output == NULL)
    {
        return -1;
    }

    bbox_copy(&output->bbox, &self->bbox);
    output->dist = self->dist;
    output->ascent = self->ascent;
    output->descent = self->descent;
    output->alt_min = self->alt_min;
    output->alt_max = self->alt_max;

    return 0;
}

//重置导航线路数据计算器
void navi_route_data_calculator_reset(NaviRouteDataCalculator *self)
{
    if (self != NULL)
    {
        self->dist = 0.0f;
        self->ascent = 0.0f;
        self->descent = 0.0f;
        self->alt_min = 0.0f;
        self->alt_max = 0.0f;

        self->is_first = true;
        navi_waypoint_ad_reset(&self->wpad);
    }
}

//导航线路路点采样，所有路点计算完成后，获取最终结果即可，中间结果没有意义
int navi_route_wp_sampler_exec(NaviRouteWpSampler *self, const NaviWaypointDc *wpdc)
{
    if (self == NULL || wpdc == NULL)
    {
        return -1;
    }

    //思路是：全局路点缓冲区满后，遍历所有全局路点，如果一个路点距离前后路点距离之和最小，那么剔除它

    if (self->len < self->capacity)
    {
        navi_waypoint_dc_copy(&self->wpdc_buf[self->len], wpdc);
        self->len += 1;

        //这里的缓冲区从1开始存入数据，首元素实际未使用，这样做可以对齐数据，减少不同索引数量，便于计算
        if (self->len >= 3)
        {
            self->dd_buf[self->len-2] = self->wpdc_buf[self->len-1].dist - self->wpdc_buf[self->len-3].dist;
        }

        return 0;
    }

    //处理最新到来的路点
    self->dd_buf[self->len-1] = wpdc->dist - self->wpdc_buf[self->len-2].dist;

    //寻找距离前后路点距离之和最小的路点
    uint32_t idx_min = 1;
    float dd_min = self->dd_buf[idx_min];

    for (uint32_t i = 2; i < self->len; i++)
    {
        if (self->dd_buf[i] < dd_min)
        {
            idx_min = i;
            dd_min = self->dd_buf[i];
        }
    }

    //首元素无法计算其到前一个路点的距离
    if (idx_min > 1)
    {
        if (idx_min < self->len - 1)
        {
            self->dd_buf[idx_min-1] = self->wpdc_buf[idx_min+1].dist - self->wpdc_buf[idx_min-2].dist;
        }
        else
        {
            self->dd_buf[idx_min-1] = wpdc->dist - self->wpdc_buf[idx_min-2].dist;
        }
    }

    //尾元素无法计算其到下一个路点的距离
    if (idx_min < self->len-1)
    {
        if (idx_min < self->len - 2)
        {
            self->dd_buf[idx_min+1] = self->wpdc_buf[idx_min+2].dist - self->wpdc_buf[idx_min-1].dist;
        }
        else
        {
            self->dd_buf[idx_min+1] = wpdc->dist - self->wpdc_buf[idx_min-1].dist;
        }
    }

    //剔除选定路点
    for (uint32_t i = idx_min; i < self->len-1; i++)
    {
        navi_waypoint_dc_copy(&self->wpdc_buf[i], &self->wpdc_buf[i+1]);
        self->dd_buf[i] = self->dd_buf[i+1];
    }

    navi_waypoint_dc_copy(&self->wpdc_buf[self->len-1], wpdc);

    return 0;
}

//获取全局路点，应该仅在所有路点计算完成后调用
int navi_route_wp_sampler_data_get(NaviRouteWpSampler *self, NaviRouteWpSample *output)
{
    if (self == NULL || output == NULL)
    {
        return -1;
    }

    output->len = self->len;
    output->wpdc_buf = self->wpdc_buf;

    return 0;
}

//重置路点采样器
void navi_route_wp_sampler_reset(NaviRouteWpSampler *self)
{
    if (self != NULL)
    {
        self->len = 0;
    }
}

//路点压缩
//-1 - 输入无效
//0 - 压缩完成，输出有效
//1 - 输入路点被压缩，输出无效
int navi_route_wp_compressor_exec(NaviRouteWpCompressor *self, const NaviWaypointDc *wpdc, NaviWaypointDc *output)
{
    if (self == NULL || wpdc == NULL || output == NULL)
    {
        return -1;
    }

    //实现思路：滑动窗口算法

    if (self->len < 2)
    {
        navi_waypoint_dc_copy(&self->buf[self->len], wpdc);
        self->len += 1;

        //起点必须输出
        if (self->len == 1)
        {
            navi_waypoint_dc_copy(output, &self->buf[0]);
            return 0;
        }

        return 1;
    }

    navi_waypoint_dc_copy(&self->buf[self->len], wpdc);
    self->len += 1;

    //缓冲区满，强制输出
    if (self->len >= self->capacity)
    {
        navi_waypoint_dc_copy(output, &self->buf[self->len-2]);
        //使用压缩路点，重算路点航向
        output->course = navi_util_course_calc(self->buf[0].lng, self->buf[0].lat, output->lng, output->lat);
        navi_waypoint_dc_copy(&self->buf[0], &self->buf[self->len-2]);
        navi_waypoint_dc_copy(&self->buf[1], &self->buf[self->len-1]);
        self->len = 2;
        return 0;
    }

    //计算距离压缩前后垂直距离偏差
    const float a = navi_util_seg_dist_calc(self->buf[0].lng, self->buf[0].lat, self->buf[self->len-1].lng, self->buf[self->len-1].lat);
    for (uint32_t i = 1; i < self->len-1; i++)
    {
        const float b = navi_util_seg_dist_calc(self->buf[i].lng, self->buf[i].lat, self->buf[0].lng, self->buf[0].lat);
        const float c = navi_util_seg_dist_calc(self->buf[i].lng, self->buf[i].lat, self->buf[self->len-1].lng, self->buf[self->len-1].lat);
        const float h = navi_util_triangle_h_calc(a, b, c);

        //偏差大于阈值，触发输出
        if (h > self->h_thres)
        {
            navi_waypoint_dc_copy(output, &self->buf[self->len-2]);
            //使用压缩路点，重算路点航向
            output->course = navi_util_course_calc(self->buf[0].lng, self->buf[0].lat, output->lng, output->lat);
            navi_waypoint_dc_copy(&self->buf[0], &self->buf[self->len-2]);
            navi_waypoint_dc_copy(&self->buf[1], &self->buf[self->len-1]);
            self->len = 2;
            return 0;
        }
    }

    return 1;
}

//路点压缩结束时调用，处理最后一个路点
//-1 - 输入无效
//0 - 输出有效
//1 - 输出无效
int navi_route_wp_compressor_end(NaviRouteWpCompressor *self, NaviWaypointDc *output)
{
    if (self == NULL || output == NULL)
    {
        return -1;
    }

    if (self->len < 2)
    {
        return 1;
    }

    navi_waypoint_dc_copy(output, &self->buf[self->len-1]);
    output->course = navi_util_course_calc(self->buf[0].lng, self->buf[0].lat, output->lng, output->lat);

    return 0;
}

//重置路点压缩器
void navi_route_wp_compressor_reset(NaviRouteWpCompressor *self)
{
    if (self != NULL)
    {
        self->len = 0;
    }
}

//导航线路分段，所有路点计算完成后，获取最终结果即可，中间结果没有意义
int navi_route_segmentor_exec(NaviRouteSegmentor *self, const NaviWaypointDc *wpdc)
{
    if (self == NULL || wpdc == NULL)
    {
        return -1;
    }

    if (self->cnt == 0)
    {
        self->len = 0;
        bbox_update(&self->seg_buf[self->len].bbox, wpdc->lng, wpdc->lng, wpdc->lat, wpdc->lat);
        dseg_update(&self->seg_buf[self->len].dseg, 0.0f, wpdc->dist);
        range_update(&self->seg_buf[self->len].range, self->cnt, self->cnt);
        self->cnt += 1;
        return 0;
    }

    //更新分段数据
    bbox_extend(&self->seg_buf[self->len].bbox, wpdc->lng, wpdc->lat);
    self->seg_buf[self->len].dseg.dist_max = wpdc->dist;
    self->cnt += 1;

    if (self->cnt < (self->len + 1) * self->interval)
    {
        //尚未完成一个分段，返回即可
        return 0;
    }

    //完成了一个分段
    self->seg_buf[self->len].range.end = self->cnt;
    self->len += 1;

    if (self->len < self->capacity)
    {
        //创建新的分段
        bbox_update(&self->seg_buf[self->len].bbox, wpdc->lng, wpdc->lng, wpdc->lat, wpdc->lat);
        dseg_update(&self->seg_buf[self->len].dseg, wpdc->dist, wpdc->dist);
        range_update(&self->seg_buf[self->len].range, self->cnt, self->cnt);
    }
    else
    {
        //分段数量已经达到最大数量，那么两两合并，将分段数量减半，以创建新的分段
        self->interval *= 2;
        const uint32_t merge_num = self->capacity / 2;

        //两两合并
        for (uint32_t i = 0; i < merge_num; i++)
        {
            self->seg_buf[i*2].range.end = self->seg_buf[i*2+1].range.end;
            self->seg_buf[i*2].dseg.dist_max = self->seg_buf[i*2+1].dseg.dist_max;
            bbox_merge(&self->seg_buf[i*2].bbox, &self->seg_buf[i*2+1].bbox);
        }

        //分段移动到前半部分
        for (uint32_t i = 1; i < merge_num; i++)
        {
            bbox_copy(&self->seg_buf[i].bbox, &self->seg_buf[i*2].bbox);
            dseg_copy(&self->seg_buf[i].dseg, &self->seg_buf[i*2].dseg);
            range_copy(&self->seg_buf[i].range, &self->seg_buf[i*2].range);
        }

        self->len = merge_num;

        //分段容量为偶数，两两合并后没有多余的，开始新的分段即可
        if (self->capacity % 2 == 0)
        {
            bbox_update(&self->seg_buf[self->len].bbox, wpdc->lng, wpdc->lng, wpdc->lat, wpdc->lat);
            dseg_update(&self->seg_buf[self->len].dseg, wpdc->dist, wpdc->dist);
            range_update(&self->seg_buf[self->len].range, self->cnt, self->cnt);
        }
        else
        {
            //分段数量为奇数，两两合并多出一个，基于该分段继续分段处理
            bbox_copy(&self->seg_buf[self->len].bbox, &self->seg_buf[self->capacity-1].bbox);
            dseg_copy(&self->seg_buf[self->len].dseg, &self->seg_buf[self->capacity-1].dseg);
            range_copy(&self->seg_buf[self->len].range, &self->seg_buf[self->capacity-1].range);
        }
    }

    return 0;
}

//完成分段，所有路点处理完毕后必须调用，以处理最后一个未完成分段
void navi_route_segmentor_end(NaviRouteSegmentor *self)
{
    if (self != NULL)
    {
        self->seg_buf[self->len].range.end = self->cnt;
        if (self->len > 0)
        {
            const uint32_t len = self->seg_buf[self->len].range.end - self->seg_buf[self->len].range.start;
            //最后一个分段不足一半，将最后一个未完成分段合并到上一个分段中，上一个分段必定是完成的
            if (len < self->interval / 2)
            {
                self->seg_buf[self->len-1].range.end = self->seg_buf[self->len].range.end;
                self->seg_buf[self->len-1].dseg.dist_max = self->seg_buf[self->len].dseg.dist_max;
                bbox_merge(&self->seg_buf[self->len-1].bbox, &self->seg_buf[self->len].bbox);
            }
            else
            {
                //最后一个分段已经达到一半，则创建一个新的分段
                self->len += 1;
            }
        }
        else
        {
            //如果一个分段都没有完成，那么直接创建一个分段
            self->len = 1;
        }
    }
}

//获取分段数据，应该仅在所有分段完成后调用
int navi_route_segmentor_data_get(NaviRouteSegmentor *self, NaviRouteSegmentArray *output)
{
    if (self == NULL || output == NULL)
    {
        return -1;
    }

    output->len = self->len;
    output->segments = self->seg_buf;

    return 0;
}

//重置线路分段器
void navi_route_segmentor_reset(NaviRouteSegmentor *self)
{
    if (self != NULL)
    {
        self->len = 0;
        self->cnt = 0;
        self->interval = self->INTERVAL;
    }
}

//导航线路匹配
//-1 - 输入无效
//0 - 匹配成功，正向骑行
//1 - 匹配成功，反向骑行
//2 - 匹配失败，已偏航
int navi_route_matcher_exec(NaviRouteMatcher *self, double lng, double lat, float course, uint8_t is_reverse, NaviRouteProgress *progress)
{
    if (self == NULL || progress == NULL)
    {
        return -1;
    }

    if (navi_util_is_coord_valid(lng, lat) == false || navi_util_is_course_valid(course) == false)
    {
        return -1;
    }

    double dx = 0.0;
    double dy = 0.0;

    navi_util_pos_dxdy_calc(lng, lat, 200.0f, &dx, &dy);

    Bbox nearby_bbox = {
        .lng_min = lng - dx,
        .lng_max = lng + dx,
        .lat_min = lat - dy,
        .lat_max = lat + dy,
    };

    uint8_t is_forward_find = false;                //是否找到前向匹配路点
    uint32_t forward_idx = 0;                       //找到的前向匹配路点索引
    NaviWaypointDc wpdc_forward;                    //找到的前向匹配路点
    float cost_min = 0.0f;                          //前向最小代价

    uint8_t is_backward_find = false;               //是否找到后向匹配路点
    uint32_t backward_idx = 0;                      //找到的后向匹配路点索引
    NaviWaypointDc wpdc_backward;                   //找到的后向匹配路点
    float dist_min = 0.0f;                          //后向最小距离

    NaviWaypointDc wpdc;
    uint32_t idx = 0;

    NaviWaypointDc cur_wp;
    NaviWaypointDc last_wp;
    NaviWaypointDc next_wp;

    //遍历所有分段，检查当前和哪一个分段重合
    for (uint32_t i = 0; i < self->seg_array->len; i++)
    {
        if (bbox_is_overlap(&nearby_bbox, &self->seg_array->segments[i].bbox) == false)
        {
            continue;
        }

        const uint32_t start = self->seg_array->segments[i].range.start;
        const uint32_t end = self->seg_array->segments[i].range.end;

        //和某一个分段重合，则遍历该分段下的所有路点，检查是否有匹配路点
        for (uint32_t j = start; j < end; j++)
        {
            if (navi_route_wp_list_get(self->wp_list, j, &cur_wp) != 0)
            {
                return -1;
            }

            const uint32_t last_idx = j > 0 ? j - 1 : 1;

            if (navi_route_wp_list_get(self->wp_list, last_idx, &last_wp) != 0)
            {
                return -1;
            }

            const float dist = navi_util_p2seg_dist_calc(lng, lat, cur_wp.lng, cur_wp.lat, last_wp.lng, last_wp.lat);

            //当前位置点距离线路的最小距离不得超过50m
            if (dist >= 50.0f)
            {
                continue;
            }

            if (is_reverse == true)
            {
                if (cur_wp.course >= 0.0f)
                {
                    cur_wp.course -= 180.0f;
                }
                else
                {
                    cur_wp.course += 180.0f;
                }
            }

            const float angle_diff = navi_util_course_diff_calc(course, cur_wp.course);

            //角度差值小于等于90°，则为前向骑行
            if (angle_diff <= 90.0f)
            {
                const float dist_diff = fabsf(self->ref_dist - cur_wp.dist);
                const float coeff = (1.0f + angle_diff / 30.0f) * (1.0f + dist_diff / 500.0f);
                const float cost = dist * coeff;

                if (is_forward_find == true)
                {
                    if (cost < cost_min)
                    {
                        cost_min = cost;
                        navi_waypoint_dc_copy(&wpdc_forward, &cur_wp);
                        forward_idx = j;
                    }
                }
                else
                {
                    cost_min = cost;
                    navi_waypoint_dc_copy(&wpdc_forward, &cur_wp);
                    forward_idx = j;
                    is_forward_find = true;
                }
            }
            else
            {
                //角度差值大于90°，则为反向骑行
                if (is_backward_find == true)
                {
                    if (dist < dist_min)
                    {
                        dist_min = dist;
                        navi_waypoint_dc_copy(&wpdc_backward, &cur_wp);
                        backward_idx = j;
                    }
                }
                else
                {
                    dist_min = dist;
                    navi_waypoint_dc_copy(&wpdc_backward, &cur_wp);
                    backward_idx = j;
                    is_backward_find = true;
                }
            }
        }
    }

    if (is_forward_find == true || is_backward_find == true)
    {
        if (is_forward_find == true)
        {
            self->ref_dist = wpdc_forward.dist;
            navi_waypoint_dc_copy(&wpdc, &wpdc_forward);
            idx = forward_idx;
        }
        else
        {
            self->ref_dist = 0.0f;
            navi_waypoint_dc_copy(&wpdc, &wpdc_backward);
            idx = backward_idx;
        }

        const float course2 = navi_util_course_calc(wpdc.lng, wpdc.lat, lng, lat);
        const float angle = navi_util_course_diff_calc(course2, wpdc.course);

        if (angle < 90.0f)
        {
            //已经经过匹配的路点
            const uint32_t end_idx = (is_reverse == true ? 0 : self->wp_list->len - 1);

            if (idx == end_idx)
            {
                //已经到达终点
                if (is_reverse == true)
                {
                    progress->dist = self->route_data->dist - wpdc.dist;
                }
                else
                {
                    progress->dist = wpdc.dist;
                }
                progress->lng = wpdc.lng;
                progress->lat = wpdc.lat;
            }
            else
            {
                const uint32_t next_idx = (is_reverse == true ? idx-1 : idx+1);

                if (navi_route_wp_list_get(self->wp_list, next_idx, &next_wp) != 0)
                {
                    return -1;
                }

                double lng_d = 0.0;
                double lat_d = 0.0;

                //计算当前位置到线路的投影点，投影点作为当前已经到达的线路位置
                navi_util_p2seg_coord_calc(lng, lat, wpdc.lng, wpdc.lat, next_wp.lng, next_wp.lat, &lng_d, &lat_d);

                const float ref_dist = fabsf(next_wp.dist - wpdc.dist);
                const float cur_dist = navi_util_seg_dist_calc(lng_d, lat_d, next_wp.lng, next_wp.lat);

                //投影点的距离作为匹配距离
                if (cur_dist > ref_dist)
                {
                    if (is_reverse == true)
                    {
                        progress->dist = self->route_data->dist - wpdc.dist;
                    }
                    else
                    {
                        progress->dist = wpdc.dist;
                    }
                }
                else
                {
                    if (is_reverse == true)
                    {
                        progress->dist = self->route_data->dist - next_wp.dist - cur_dist;
                    }
                    else
                    {
                        progress->dist = next_wp.dist - cur_dist;
                    }
                }

                progress->lng = lng_d;
                progress->lat = lat_d;
            }

            if (is_reverse == true)
            {
                progress->idx = self->wp_list->len - 1 - idx;
            }
            else
            {
                progress->idx = idx;
            }
        }
        else
        {
            //尚未经过匹配的路点
            const uint32_t start_idx = (is_reverse == true ? self->wp_list->len - 1 : 0);

            if (idx == start_idx)
            {
                //当前还在起点
                if (is_reverse == true)
                {
                    progress->dist = self->route_data->dist - wpdc.dist;
                }
                else
                {
                    progress->dist = wpdc.dist;
                }
                progress->lng = wpdc.lng;
                progress->lat = wpdc.lat;
            }
            else
            {
                const uint32_t last_idx = (is_reverse == true ? idx+1 : idx-1);

                if (navi_route_wp_list_get(self->wp_list, last_idx, &last_wp) != 0)
                {
                    return -1;
                }

                double lng_d = 0.0;
                double lat_d = 0.0;

                //计算当前位置到线路的投影点，投影点作为当前已经到达的线路位置
                navi_util_p2seg_coord_calc(lng, lat, wpdc.lng, wpdc.lat, last_wp.lng, last_wp.lat, &lng_d, &lat_d);

                const float ref_dist = fabsf(wpdc.dist - last_wp.dist);
                const float cur_dist = navi_util_seg_dist_calc(lng_d, lat_d, wpdc.lng, wpdc.lat);

                //投影点的距离作为匹配距离
                if (cur_dist > ref_dist)
                {
                    if (is_reverse == true)
                    {
                        progress->dist = self->route_data->dist - last_wp.dist;
                    }
                    else
                    {
                        progress->dist = last_wp.dist;
                    }
                }
                else
                {
                    if (is_reverse == true)
                    {
                        progress->dist = self->route_data->dist - wpdc.dist - cur_dist;
                    }
                    else
                    {
                        progress->dist = wpdc.dist - cur_dist;
                    }
                }

                progress->lng = lng_d;
                progress->lat = lat_d;
            }

            if (is_reverse == true)
            {
                progress->idx = idx < self->wp_list->len - 1 ? self->wp_list->len - 1 - 1 - idx : 0;
            }
            else
            {
                progress->idx = idx > 0 ? idx - 1 : idx;
            }
        }

        if (is_forward_find == true)
        {
            return 0;
        }
        else
        {
            return 1;
        }
    }
    else
    {
        //未找到匹配的路点，已偏航
        self->ref_dist = 0.0f;
        return 2;
    }
}

//重置路点匹配器
void navi_route_matcher_reset(NaviRouteMatcher *self)
{
    if (self != NULL)
    {
        self->ref_dist = 0.0f;
    }
}

//根据给定经纬度，从线路上查找对应的点
//-1 - 输入无效
//0 - 查找成功
int navi_route_finder_exec(NaviRouteFinder *self, double lng, double lat, uint8_t is_reverse, NaviRouteProgress *progress)
{
    if (self == NULL || progress == NULL)
    {
        return -1;
    }

    if (navi_util_is_coord_valid(lng, lat) == false)
    {
        return -1;
    }

    if (self->seg_array->len == 0)
    {
        return -1;
    }

    double dx = 0.0;
    double dy = 0.0;

    navi_util_pos_dxdy_calc(lng, lat, 200.0f, &dx, &dy);

    Bbox nearby_bbox = {
        .lng_min = lng - dx,
        .lng_max = lng + dx,
        .lat_min = lat - dy,
        .lat_max = lat + dy,
    };

    uint8_t is_find = false;
    uint32_t idx = 0;
    float dist_min = 0.0f;

    NaviWaypointDc wpdc;
    NaviWaypointDc cur_wp;
    NaviWaypointDc last_wp;
    NaviWaypointDc next_wp;

    for (uint32_t i = 0; i < self->seg_array->len; i++)
    {
        if (bbox_is_overlap(&nearby_bbox, &self->seg_array->segments[i].bbox) == false)
        {
            continue;
        }

        const uint32_t start = self->seg_array->segments[i].range.start;
        const uint32_t end = self->seg_array->segments[i].range.end;

        //和某一个分段重合，则遍历该分段下的所有路点，检查是否有匹配路点
        for (uint32_t j = start; j < end; j++)
        {
            if (navi_route_wp_list_get(self->wp_list, j, &cur_wp) != 0)
            {
                return -1;
            }

            const uint32_t last_idx = j > 0 ? j - 1 : 1;

            if (navi_route_wp_list_get(self->wp_list, last_idx, &last_wp) != 0)
            {
                return -1;
            }

            const float dist = navi_util_p2seg_dist_calc(lng, lat, cur_wp.lng, cur_wp.lat, last_wp.lng, last_wp.lat);

            //当前位置点距离路点的最小距离不得超过150m
            if (dist >= 150.0f)
            {
                continue;
            }

            if (is_find == true)
            {
                if (dist < dist_min)
                {
                    dist_min = dist;
                    idx = j;
                    navi_waypoint_dc_copy(&wpdc, &cur_wp);
                }
            }
            else
            {
                dist_min = dist;
                idx = j;
                navi_waypoint_dc_copy(&wpdc, &cur_wp);
                is_find = true;
            }
        }
    }

    if (is_find == false)
    {
        //从所有分段中找到距离当前位置最近的分段
        for (uint32_t i = 0; i < self->seg_array->len; i++)
        {
            //TODO 在生成分段时计算分段的质心，则此时直接使用质心即可
            const double lng_c = (self->seg_array->segments[i].bbox.lng_max + self->seg_array->segments[i].bbox.lng_min) / 2;
            const double lat_c = (self->seg_array->segments[i].bbox.lat_max + self->seg_array->segments[i].bbox.lat_min) / 2;

            const float dist = navi_util_seg_dist_calc(lng, lat, lng_c, lat_c);

            if (i == 0)
            {
                dist_min = dist;
                idx = i;
                continue;
            }

            if (dist < dist_min)
            {
                dist_min = dist;
                idx = i;
            }
        }

        const uint32_t start = self->seg_array->segments[idx].range.start;
        const uint32_t end = self->seg_array->segments[idx].range.end;

        //遍历该分段下的所有路点，寻找最近的路点
        for (uint32_t i = start; i < end; i++)
        {
            if (navi_route_wp_list_get(self->wp_list, i, &cur_wp) != 0)
            {
                return -1;
            }

            const uint32_t last_idx = i > 0 ? i - 1 : 1;

            if (navi_route_wp_list_get(self->wp_list, last_idx, &last_wp) != 0)
            {
                return -1;
            }

            const float dist = navi_util_p2seg_dist_calc(lng, lat, cur_wp.lng, cur_wp.lat, last_wp.lng, last_wp.lat);

            if (i == start)
            {
                dist_min = dist;
                idx = i;
                navi_waypoint_dc_copy(&wpdc, &cur_wp);
                continue;
            }

            if (dist < dist_min)
            {
                dist_min = dist;
                idx = i;
                navi_waypoint_dc_copy(&wpdc, &cur_wp);
            }
        }
    }

    if (is_reverse == true)
    {
        if (wpdc.course >= 0.0f)
        {
            wpdc.course -= 180.0f;
        }
        else
        {
            wpdc.course += 180.0f;
        }
    }

    //计算进度
    const float course = navi_util_course_calc(wpdc.lng, wpdc.lat, lng, lat);
    const float angle = navi_util_course_diff_calc(course, wpdc.course);

    if (angle < 90.0f)
    {
        //已经经过匹配的路点
        const uint32_t end_idx = (is_reverse == true ? 0 : self->wp_list->len - 1);

        if (idx == end_idx)
        {
            //已经到达终点
            if (is_reverse == true)
            {
                progress->dist = self->route_data->dist - wpdc.dist;
            }
            else
            {
                progress->dist = wpdc.dist;
            }
            progress->lng = wpdc.lng;
            progress->lat = wpdc.lat;
        }
        else
        {
            const uint32_t next_idx = (is_reverse == true ? idx-1 : idx+1);

            if (navi_route_wp_list_get(self->wp_list, next_idx, &next_wp) != 0)
            {
                return -1;
            }

            double lng_d = 0.0;
            double lat_d = 0.0;

            //计算当前位置到线路的投影点，投影点作为当前已经到达的线路位置
            navi_util_p2seg_coord_calc(lng, lat, wpdc.lng, wpdc.lat, next_wp.lng, next_wp.lat, &lng_d, &lat_d);

            const float ref_dist = fabsf(next_wp.dist - wpdc.dist);
            const float cur_dist = navi_util_seg_dist_calc(lng_d, lat_d, next_wp.lng, next_wp.lat);

            //投影点的距离作为匹配距离
            if (cur_dist > ref_dist)
            {
                if (is_reverse == true)
                {
                    progress->dist = self->route_data->dist - wpdc.dist;
                }
                else
                {
                    progress->dist = wpdc.dist;
                }
            }
            else
            {
                if (is_reverse == true)
                {
                    progress->dist = self->route_data->dist - next_wp.dist - cur_dist;
                }
                else
                {
                    progress->dist = next_wp.dist - cur_dist;
                }
            }

            progress->lng = lng_d;
            progress->lat = lat_d;
        }

        if (is_reverse == true)
        {
            progress->idx = self->wp_list->len - 1 - idx;
        }
        else
        {
            progress->idx = idx;
        }
    }
    else
    {
        //尚未经过匹配的路点
        const uint32_t start_idx = (is_reverse == true ? self->wp_list->len - 1 : 0);

        if (idx == start_idx)
        {
            //当前还在起点
            if (is_reverse == true)
            {
                progress->dist = self->route_data->dist - wpdc.dist;
            }
            else
            {
                progress->dist = wpdc.dist;
            }
            progress->lng = wpdc.lng;
            progress->lat = wpdc.lat;
        }
        else
        {
            const uint32_t last_idx = (is_reverse == true ? idx+1 : idx-1);

            if (navi_route_wp_list_get(self->wp_list, last_idx, &last_wp) != 0)
            {
                return -1;
            }

            double lng_d = 0.0;
            double lat_d = 0.0;

            //计算当前位置到线路的投影点，投影点作为当前已经到达的线路位置
            navi_util_p2seg_coord_calc(lng, lat, wpdc.lng, wpdc.lat, last_wp.lng, last_wp.lat, &lng_d, &lat_d);

            const float ref_dist = fabsf(wpdc.dist - last_wp.dist);
            const float cur_dist = navi_util_seg_dist_calc(lng_d, lat_d, wpdc.lng, wpdc.lat);

            //投影点的距离作为匹配距离
            if (cur_dist > ref_dist)
            {
                if (is_reverse == true)
                {
                    progress->dist = self->route_data->dist - last_wp.dist;
                }
                else
                {
                    progress->dist = last_wp.dist;
                }
            }
            else
            {
                if (is_reverse == true)
                {
                    progress->dist = self->route_data->dist - wpdc.dist - cur_dist;
                }
                else
                {
                    progress->dist = wpdc.dist - cur_dist;
                }
            }

            progress->lng = lng_d;
            progress->lat = lat_d;
        }

        if (is_reverse == true)
        {
            progress->idx = idx < self->wp_list->len - 1 ? self->wp_list->len - 1 - 1 - idx : 0;
        }
        else
        {
            progress->idx = idx > 0 ? idx - 1 : idx;
        }
    }

    return 0;
}

//根据给定的距离，从线路上查找对应的点
//-1 - 输入无效
//0 - 查找成功
int navi_route_finder_exec2(NaviRouteFinder *self, float dist, uint8_t is_reverse, double *lng, double *lat)
{
    if (self == NULL || lng == NULL || lat == NULL)
    {
        return -1;
    }

    if (dist < 0.0f || dist > self->route_data->dist)
    {
        return -1;
    }

    if (is_reverse == true)
    {
        dist = self->route_data->dist - dist;
    }

    uint8_t is_find = false;
    uint32_t idx = 0;
    NaviWaypointDc cur_wp;
    NaviWaypointDc last_wp;

    //从所有分段中找到包含给定距离的分段
    for (uint32_t i = 0; i < self->seg_array->len; i++)
    {
        if (dseg_is_contain(&self->seg_array->segments[i].dseg, dist) == true)
        {
            is_find = true;
            idx = i;
            break;
        }
    }

    if (is_find == false)
    {
        return -1;
    }

    is_find = false;

    const uint32_t start = self->seg_array->segments[idx].range.start;
    const uint32_t end = self->seg_array->segments[idx].range.end;

    //找到第一个距离大于等于给定距离的路点，该路点和其前一个路点构成的线段包含给定距离
    for (uint32_t i = start; i < end; i++)
    {
        if (navi_route_wp_list_get(self->wp_list, i, &cur_wp) != 0)
        {
            return -1;
        }

        if (cur_wp.dist >= dist)
        {
            is_find = true;
            idx = i;
            break;
        }
    }

    if (is_find == false)
    {
        return -1;
    }

    if (idx > 0)
    {
        if (navi_route_wp_list_get(self->wp_list, idx-1, &last_wp) != 0)
        {
            return -1;
        }

        //线性插值计算给定距离对应的经纬度
        const float coeff = (dist - last_wp.dist) / (cur_wp.dist - last_wp.dist);

        *lng = last_wp.lng + (cur_wp.lng - last_wp.lng) * coeff;
        *lat = last_wp.lat + (cur_wp.lat - last_wp.lat) * coeff;
    }
    else
    {
        *lng = cur_wp.lng;
        *lat = cur_wp.lat;
    }

    return 0;
}

//加载指定路点前后的路点
//-1 - 加载失败
//0 - 加载成功
int navi_route_wp_loader_exec(NaviRouteWpLoader *self, uint8_t is_reverse, uint32_t idx)
{
    if (self == NULL)
    {
        return -1;
    }

    if (idx > self->wp_list->len - 1)
    {
        return -1;
    }

    if (is_reverse == true)
    {
        idx = self->wp_list->len - 1 - idx;
    }

    const uint32_t half = self->capacity / 2;

    Range range = { 0 };

    //计算要加载的路点的范围
    if (idx >= half)
    {
        range.start = idx - half;
    }
    else
    {
        range.start = 0;
    }

    uint32_t end = 0;

    if (self->capacity % 2 == 0)
    {
        end = idx + half;
    }
    else
    {
        end = idx + half + 1;
    }

    if (end <= self->wp_list->len)
    {
        range.end = end;
    }
    else
    {
        range.end = self->wp_list->len;
    }

    const uint32_t capacity = range.end - range.start;

    //尽可能占满缓冲区
    if (capacity < self->capacity)
    {
        const uint32_t mtr = self->capacity - capacity;

        if (range.start > mtr)
        {
            range.start -= mtr;
        }
        else
        {
            range.start = 0;
        }

        if (range.end + mtr <= self->wp_list->len)
        {
            range.end += mtr;
        }
        else
        {
            range.end = self->wp_list->len;
        }
    }

    //缓冲区没有发生变化，不必进行加载
    if (self->range.start == range.start && self->range.end == range.end)
    {
        return 0;
    }

    //将已经加载的路点复制到正确的位置
    if (self->range.start < range.end && self->range.end > range.start)
    {
        //路点前移，则前向遍历，否则路点会被覆盖，导致所有数据相同
        if (range.start >= self->range.start)
        {
            for (uint32_t i = range.start; i < range.end; i++)
            {
                if (i >= self->range.start && i < self->range.end)
                {
                    if (is_reverse == true)
                    {
                        navi_waypoint_dc_copy(&self->buf[self->len-1-i+range.start], &self->buf[self->len-1-i+self->range.start]);
                    }
                    else
                    {
                        navi_waypoint_dc_copy(&self->buf[i-range.start], &self->buf[i-self->range.start]);
                    }
                }
            }
        }
        else
        {
            //路点后移，则后向遍历，否则路点会被覆盖，导致所有数据相同
            for (uint32_t i = range.end - 1; i > range.start; i--)
            {
                if (i >= self->range.start && i < self->range.end)
                {
                    if (is_reverse == true)
                    {
                        navi_waypoint_dc_copy(&self->buf[self->len-1-i+range.start], &self->buf[self->len-1-i+self->range.start]);
                    }
                    else
                    {
                        navi_waypoint_dc_copy(&self->buf[i-range.start], &self->buf[i-self->range.start]);
                    }
                }
            }
        }
    }

    const uint32_t len = range.end - range.start;

    //加载剩余的路点
    for (uint32_t i = range.start; i < range.end; i++)
    {
        if (i >= self->range.start && i < self->range.end)
        {
            continue;
        }

        NaviWaypointDc *wp = NULL;

        if (is_reverse == true)
        {
            wp = &self->buf[len-1-i+range.start];
        }
        else
        {
            wp = &self->buf[i-range.start];
        }

        if (navi_route_wp_list_get(self->wp_list, i, wp) != 0)
        {
            return -1;
        }

        if (is_reverse == true)
        {
            wp->dist = self->route_data->dist - wp->dist;
            if (wp->course >= 0.0f)
            {
                wp->course -= 180.0f;
            }
            else
            {
                wp->course += 180.0f;
            }
        }
    }

    self->range.start = range.start;
    self->range.end = range.end;
    self->len = self->range.end - self->range.start;

    return 0;
}

//获取加载的路点
void navi_route_wp_loader_data_get(NaviRouteWpLoader *self, NaviRouteWpNearby *wp_nearby)
{
    if (self != NULL && wp_nearby != NULL)
    {
        wp_nearby->len = self->len;
        wp_nearby->buf = self->buf;
    }
}

//重置路点加载器
void navi_route_wp_loader_reset(NaviRouteWpLoader *self)
{
    if (self != NULL)
    {
        self->len = 0;
        self->range.start = 0;
        self->range.end = 0;
    }
}
