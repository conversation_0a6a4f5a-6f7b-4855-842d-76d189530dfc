/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   EditDialDataComponentView.cpp
@Time    :   2024/12/13 20:02:17
* 
**************************************************************************/
#include "EditDialDataComponentView.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QwGUITheme.h"
#include "Image/images.h"
#include "GUI/QwGUIKey.h"
#include "../touchgfx_js/touchgfx_js_api.h"
#include "../touchgfx_js/js_data_servers.h"
#include "../touchgfx_js/jsApp.h"
#include "qw_time_util.h"

static const char g_edit_component[] = "_edit_component";
TM_KEY(g_edit_component)

EditDialDataComponentView::EditDialDataComponentView(PageManager* manager) :
	PageView(manager),
	dataNum_(0),
	jsAppObjectName_(nullptr),
	jsContainer_(nullptr),
	isReleaseCashBuf_(true),
	rc_(0, 0, 0, 0),
	backLauncherStatus_(BACK_LAUNCHER_STATUS_NONE),
	isRefreshFlag_(true)
{
	if(cashBuf_ == nullptr)
	{
		cashBuf_ = HAL::getScreenCachedBuffer(LV_COLOR_DEPTH/8);
	}
}

EditDialDataComponentView::~EditDialDataComponentView()
{
	if(strcmp(manager_->get_next_page(), "Launcher") == 0)
	{
		if(backLauncherStatus_ == BACK_LAUNCHER_STATUS_NONE)
		{
			create_dial_snapshot(dialGoodsId_, cashBuf_, true);
			delete_backup_config_file();
			isReleaseCashBuf_ = false;
		}
	}
	if(isReleaseCashBuf_)
	{
		if(cashBuf_ != nullptr)
		{
			HAL::freeScreenCachedBuffer(cashBuf_);
		}
	}
	js_free_dial_cache_buf();
	remove(*jsContainer_);
}

void EditDialDataComponentView::setup()
{
	dialGoodsId_ = get_cfg_current_using_dial_goodsid();
	dataNum_ = get_dial_cfg_theme_color_num(dialGoodsId_)-1;
	const char *dialType = get_cfg_dial_type(get_using_dial_index());
	get_dial_path(dialGoodsId_, dialType, jsPath_);
	backup_cfg_file();
	add(bg_);
	bg_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
	bg_.setColor(0x000000);

	bool get_dial_container(CacheableContainer **container);
	// 表盘之前已销毁需要重新创建
	if (js_create_dial(jsPath_))
    {

        get_dial_container(&jsContainer_);
		add(*jsContainer_);
		js_stop_dial_timer();
		jsContainer_->setCacheBitmapZoom(176);
		jsContainer_->setAlign(ALIGN_IN_CENTER, 0, 0);
	}
	else// 表盘之前未销毁，例如从编辑主题色跳到编辑数据，直接使用buff
	{
		get_dial_container(&jsContainer_);
		add(*jsContainer_);
		jsContainer_->setCacheBitmapZoom(176);
		
		if(strcmp(manager_->get_prev_page(), "DialDataSelect") == 0)
		{
			if(isRefreshFlag_)
			{
				create_dial_cache_buf();
				set_js_edit_data_preview(jsPath_, get_dial_prv_edit_data_index());
			}
		}
		else
		{
			if(isRefreshFlag_)
			{
				js_refresh_dial_view(jsPath_);
				isRefreshFlag_ = true;
			}
		}
		js_stop_dial_timer();
		jsContainer_->setAlign(ALIGN_IN_CENTER, 0, 0);

	}
	edit_data_positon_t position = {0};
	get_edit_data_positon(get_dial_edit_data_index(), &position);
	// 获取需要编辑的数据位置
	rc_ = {position.x, position.y, position.width, position.height};

	//添加编辑掩码
#ifdef SIMULATOR
	sprintf(maskBgPath_, "./Dial/%u/%d_maskbg.bin", dialGoodsId_,  get_dial_edit_data_index());
#else
	sprintf(maskBgPath_, "%s/Dial/%u/%d_maskbg.bin", BASE_PATH, dialGoodsId_,  get_dial_edit_data_index());
#endif 
	add(maskBg_);
	maskBg_.setWidthHeight(HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
	maskBg_.setBitmap(Bitmap(maskBgPath_));
	maskBg_.setZoom(176);
	maskBg_.setAlign(ALIGN_IN_CENTER, 0,0);

	add(title_text_);
	title_text_.setTextFont(&PUBLIC_NO_24_R_FONT);//PUBLIC_NO_38_M_FONT
    title_text_.setColor(lv_color_white());
    title_text_.setLabelAlpha(LV_OPA_TRANSP);
	title_text_.setTypedDynamicText(_TM(g_edit_component));
	uint8_t title_text_width = title_text_.getTextWidth();
	title_text_.setWidthHeight(title_text_width, 33);
	title_text_.setAlign(ALIGN_IN_TM, 0, 26);

	add(circle_);
	circle_.setWidthHeight(322, 322);
    circle_.setCircle(161, 161, 161);
    circle_.setLineWidth(2);
    circle_.setArc(-120, 240);
    circle_.setColor(0xffffff);
	circle_.setAlign(ALIGN_IN_CENTER, 0, 0);

    add(fab_);
    fab_.setup(FABTN_START::OK);

	add(edit_dial_img_);
    edit_dial_img_.setBitmap(Bitmap(&edit_dial));
    edit_dial_img_.setAlign(ALIGN_IN_RT, 0,44);
	
	bg_.invalidate();
}

void EditDialDataComponentView::quit()
{
	//The method is an intentionally-blank override.
}

void EditDialDataComponentView::handleTickEvent()
{
	//The method is an intentionally-blank override.
	if(jsContainer_->getCacheBitmapZoomValue() == 255)
	{
		isRefreshFlag_ = false;
		getRootContainer().removeAll();
		setup();
	}
}
// #include "qw_time_util.h"
void EditDialDataComponentView::handleKeyEvent(uint8_t c)
{
    if (c == KEY_CLK_BACK)
    {
		uint8_t editType = get_dial_edit_type(dialGoodsId_);
		restore_backup_config_file();
		if(editType == EDIT_TYPE_ALL)
		{
			restore_all_js_edit_data_preview_view(jsPath_, get_dial_edit_data_index());
			isReleaseCashBuf_ = false;
			create_dial_snapshot(dialGoodsId_, cashBuf_, true);
		}
		else if(editType == EDIT_TYPE_DATA)
		{
			isReleaseCashBuf_ = true;
		}
		backLauncherStatus_ = BACK_LAUNCHER_STATUS_KEY;
	    manager_->push("Launcher");
    }
	else if (c == KEY_CLK_START)
	{
		// 不能把释放qjs的代码放在析构函数中，否则会导致qjs崩溃，因为要新建缩略图
		// rt_kprintf("start_destory:%u\n", (uint32_t)get_boot_msec());

		// rt_kprintf("key start_create_dial_snapshot:%u\n", (uint32_t)get_boot_msec());
		create_dial_snapshot(dialGoodsId_, cashBuf_, true);
		// rt_kprintf("key end_create_dial_snapshot:%u\n", (uint32_t)get_boot_msec());
		delete_backup_config_file();
		// rt_kprintf("key end_del_dial_conf:%u\n", (uint32_t)get_boot_msec());
		isReleaseCashBuf_ = false;
		backLauncherStatus_ = BACK_LAUNCHER_STATUS_KEY;
	    manager_->push("Launcher");
	}
	else if(c == KEY_CLK_POWER)
	{
		isReleaseCashBuf_ = true;
        set_dial_prv_edit_data_index(get_dial_edit_data_index());
		manager_->push("DialDataSelect");
	}
	else if(c == KEY_KNOB_UP || c == KEY_KNOB_DOWN)
	{
		// 整个耗时大概80ms
		// uint32_t start = (uint32_t)get_boot_msec();
		if(c == KEY_KNOB_UP){
			set_dial_edit_data_index_decrease();
		}else {
			set_dial_edit_data_index_increase();
		}
		// 刷新遮挡
	#ifdef SIMULATOR
		sprintf(maskBgPath_, "./Dial/%u/%d_maskbg.bin", dialGoodsId_,  get_dial_edit_data_index());
	#else
		sprintf(maskBgPath_, "%s/Dial/%u/%d_maskbg.bin", BASE_PATH, dialGoodsId_,  get_dial_edit_data_index());
	#endif
		maskBg_.setBitmap(Bitmap(maskBgPath_));

		jsContainer_->updateCache();
		edit_data_positon_t position = {0};
		get_edit_data_positon(get_dial_edit_data_index(), &position);
		rc_ = {position.x, position.y, position.width, position.height};
		rt_kprintf("x:%d y:%d w:%d h:%d\n", position.x, position.y, position.width, position.height);
		// uint32_t end = (uint32_t)get_boot_msec();
	}
}
// #include "qw_time_util.h"
void EditDialDataComponentView::handleClickEvent(const ClickEvent& evt)
{
    if (fab_.get_fab_absoluteRect(FABTN_TYPE::START).intersect(evt.getX(), evt.getY()))
	{
		rt_kprintf("cli start_del_dial_snapshot:%u\n", (uint32_t)get_boot_msec());
		rt_kprintf("cli end_del_dial_snapshot:%u\n", (uint32_t)get_boot_msec());
		create_dial_snapshot(dialGoodsId_, cashBuf_, true);
		rt_kprintf("cli start_create_dial_snapshot:%u\n", (uint32_t)get_boot_msec());
		delete_backup_config_file();
		rt_kprintf("cli end_create_dial_snapshot:%u\n", (uint32_t)get_boot_msec());
		// rt_kprintf("@@@@end , mesc:%u\n", (uint32_t)get_boot_msec());
		isReleaseCashBuf_ = false;
		backLauncherStatus_ = BACK_LAUNCHER_STATUS_CLICK;
        manager_->push("Launcher");
	}else if (edit_dial_img_.getAbsoluteRect().intersect(evt.getX(), evt.getY()))
	{
		isReleaseCashBuf_ = true;
        set_dial_prv_edit_data_index(get_dial_edit_data_index());
		manager_->push("DialDataSelect");
	}
	else if (rc_.intersect(evt.getX(), evt.getY()))
	{
		isReleaseCashBuf_ = true;
        set_dial_prv_edit_data_index(get_dial_edit_data_index());
		manager_->push("DialDataSelect");
	}
}

void EditDialDataComponentView::handleDragEvent(const DragEvent& evt)
{
	//The method is an intentionally-blank override.
}


void EditDialDataComponentView::handleGestureEvent(const GestureEvent& evt)
{
	//The method is an intentionally-blank override.
}

// Notification Callback function
// ObserverDrawable Callback function
// custom function

