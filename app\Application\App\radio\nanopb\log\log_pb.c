/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   log_pb.c
@Time    :   2025/05/21 22:22:22
*
**************************************************************************/
#include "log_pb.h"
#include "log.pb.h"

#include "ble_nus_srv.h"
#include "ble_cmd_common.h"
#include "ble_cmd_response.h"

#include "pb.h"
#include "pb_encode.h"
#include "pb_decode.h"
#include "pb_decode_common.h"
#include "pb_encode_common.h"

#include "crc8.h"
#include "basictype.h"
#include "max_config.h"
#include "igs_global.h"
#include "ff.h"
#include "igs_dev_config.h"
#include "qw_time_util.h"
#include "thread_pool.h"
#include "zip.h"

#define LOG_FILE_NAME_LENGTH        50
#define FILE_READ_512_SIZE		1536   // (8*244)
#define FILE_REMIND_SIZE		416   // (8*244 - 3*512)

static uint8_t g_log_file_name[LOG_FILE_NAME_LENGTH] = {0};
static uint8_t trans_cmp = false;              //文件传输完成状态
static uint8_t open_flag = false;
static uint32_t read_file_len = 0;
static FIL *fdst;
// 日志目录基路径（全局指针）
const char* g_base_log_dir = NULL;
static bool g_debug_log_flag = false;

static bool debug_log_n_trace_zip(const thread_pool_task* task_info);
static void debug_log_n_trace_zip_finish(void *arg, bool res);
//-------------------------------------------------------------------------------------------
// Function Name : log_file_num_get
// Purpose       : 获取LOG日志文件的数量
// Param[in]     : void  
// Param[out]    : None
// Return type   : static
// Comment       : 2021-01-18
//-------------------------------------------------------------------------------------------
static uint32_t log_file_num_get(void)
{
    FRESULT res;
//    char *fn;
    DIR s_dir; 
    FILINFO s_fileinfo;
    uint32_t file_num = 0;

#if _USE_LFN
    s_fileinfo.fsize = _MAX_LFN + 1;
    memset(s_fileinfo.fname, 0, _MAX_LFN + 1);
#endif
    if (g_debug_log_flag == true)
    {
        res = f_opendir(&s_dir, "0:/iGPSPORT/System/");
        if (res == FR_OK)
        {
            while (FR_OK == f_readdir(&s_dir, &s_fileinfo) && s_fileinfo.fname[0])
            {
                if (s_fileinfo.fname[0] == 0)
                {
                    break;
                }

                if (!(s_fileinfo.fattrib & AM_DIR))
                {
                    if (strncmp(s_fileinfo.fname, "debug_log_", strlen("debug_log_")) == 0)
                    {
                        file_num++;
                    }
                }
            }
        }
    }
    else
    {
        res = f_opendir(&s_dir, (const TCHAR *)g_base_log_dir);
        if (res == FR_OK)
        {
            while (1)
            {
                res = f_readdir(&s_dir, &s_fileinfo);                                           //读取目录下的一个文件
                if (res != FR_OK || s_fileinfo.fname[0] == 0) break;        //错误了/到末尾了,退出

                if (!(s_fileinfo.fattrib & AM_DIR) && s_fileinfo.fsize > 0) {
                    // 如果不是目录，才计数为文件
                    file_num++;
                }
            }
        }   
    }
    return file_num;
}

//-------------------------------------------------------------------------------------------
// Function Name : log_file_name_get
// Purpose       : 获取需要同步的LOG文件的名称
// Param[in]     : uint8_t *file_name  
// Param[out]    : None
// Return type   : static
// Comment       : 2021-01-18
//-------------------------------------------------------------------------------------------
static bool log_file_name_get(uint8_t *file_name)
{
    FRESULT res;
    char *fn; 
    DIR s_dir; 
    FILINFO s_fileinfo;
//    uint32_t file_num = 0;

#if _USE_LFN
    s_fileinfo.fsize = _MAX_LFN + 1;
    memset(s_fileinfo.fname, 0, _MAX_LFN + 1);
#endif

    res = f_opendir(&s_dir, (const TCHAR *)g_base_log_dir); //打开一个目录
    if (res == FR_OK)
    {
        while (1)
        {
            res = f_readdir(&s_dir, &s_fileinfo);                                           //读取目录下的一个文件
            if (res != FR_OK || s_fileinfo.fname[0] == 0)                       //错误了/到末尾了,退出
            {
                return false;
            }

            // 检查是否为普通文件（非目录）
            if (!(s_fileinfo.fattrib & AM_DIR) && s_fileinfo.fsize > 0)
            {
        #if _USE_LFN
        //    fn = *s_fileinfo.fname ? s_fileinfo.fname : s_fileinfo.altname;
                fn = s_fileinfo.fname;
        #else
                fn = s_fileinfo.fname;
        #endif
                memcpy(file_name, fn, strlen((char *)fn));
                return true;
            }
        }
    }
    return false;
}

static bool debug_log_file_name_get(uint8_t *file_name)
{
        FRESULT res;
        char *fn; 
        DIR s_dir; 
        FILINFO s_fileinfo;

        res = f_opendir(&s_dir, "0:/iGPSPORT/System/"); //打开一个目录
        if (res == FR_OK)
        {
            while (1)
            {
                res = f_readdir(&s_dir, &s_fileinfo);                                           //读取目录下的一个文件
                if (res != FR_OK || s_fileinfo.fname[0] == 0)                       //错误了/到末尾了,退出
                {
                    return false;
                }

                // 检查是否为普通文件（非目录）
                if (!(s_fileinfo.fattrib & AM_DIR) && s_fileinfo.fsize > 0)
                {
                    if (strncmp(s_fileinfo.fname, "debug_log_", strlen("debug_log_")) == 0)
                    {
                        fn = s_fileinfo.fname;
                        memcpy(file_name, fn, strlen((char *)fn));
                        return true;
                    }
                }
            }
        }
        return false;
}
//-------------------------------------------------------------------------------------------
// Function Name : log_file_num_send
// Purpose       : 发送LOG文件数量
// Param[in]     : void  
// Param[out]    : None
// Return type   : static
// Comment       : 2021-01-18
//-------------------------------------------------------------------------------------------
static void log_file_num_send(void)
{
    uint8_t pb_crc = 0;
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    log_msg log_message;

    memset (&log_message, 0, sizeof(log_msg));
    ble_data_var_tx_get(&data, &length, BLE_NUS_CH2_UUID);

    //参数赋值
    log_message.service = service_type_index_enum_SERVICE_TYPE_INDEX_LOG;
    log_message.operate = SERVICE_OPERATE_TYPE_enum_SERVICE_OPERATE_TYPE_GET;
    log_message.sub_operate = LOG_SUB_OPERATE_TYPE_enum_LOG_NUMBER_GET;

    log_message.has_log_num = true;
    log_message.log_num = log_file_num_get();

    //编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX);
    //编码
    pb_encode(&encode_stream, log_msg_fields, &log_message);

    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch2( );
        
    //对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);

    //命令协议 发送通道2
    ble_cmd_end_tx(log_message.service, 0, log_message.operate, log_message.sub_operate, encode_stream.bytes_written, pb_crc);
}

////-------------------------------------------------------------------------------------------
//// Function Name : log_file_read
//// Purpose       : 读取LOG文件
//// Param[in]     : void
//// Param[out]    : None
//// Return type   : static
//// Comment       : 2021-01-18
////-------------------------------------------------------------------------------------------
//static void log_file_read(void)
//{
//    char file_path[100] = {0};
//    unsigned int tt = 1;
//    uint8_t i = 0;
//
//    memset(file_path, 0 ,100);
//
//    if (NULL == fdst)
//    {
//        fdst = QW_FIL_FD_MALLOC(sizeof(FIL));
//        if (NULL == fdst)
//        {
//            return;
//        }
//    }
//
//    sprintf(file_path, "%s/%s", g_base_log_dir,  g_log_file_name);
//
//    if (false == open_flag)
//    {
//        if (FR_OK == f_open(fdst, file_path, FA_READ))
//        {
//            open_flag = true;
//        }
//    }
//
//    if (true == open_flag)
//    {
//        for (i = 0; i < 2; i ++)
//        {
//            if(!f_eof(fdst))
//            {
//                if (read_file_len + 256 > DATA_LENGTH_MAX_512B)
//                {
//                    break;
//                }
//
//                f_read(fdst, file_content_upload + read_file_len, 256, &tt);
//                read_file_len += tt;
//                if (tt < 256)
//                {
//                    f_close(fdst);
//                    QW_FIL_FD_FREE(fdst);
//                    open_flag = false;
//                    trans_cmp = true;
//                    break;
//                }
//            }
//            else
//            {
//                f_close(fdst);
//                QW_FIL_FD_FREE(fdst);
//                open_flag = false;
//                trans_cmp = true;
//                break;
//            }
//        }
//    }
//}

//-------------------------------------------------------------------------------------------
// Function Name : log_file_encode_bytes
// Purpose       : LOG文件内容编码
// Param[in]     : pb_ostream_t *stream     
//                 const pb_field_t *field  
//                 void *const *arg         
// Param[out]    : None
// Return type   : static
// Comment       : 2021-01-18
//-------------------------------------------------------------------------------------------
static bool log_file_encode_bytes(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    if (!pb_encode_tag_for_field(stream, field))
        return false;

    return pb_encode_string(stream, (uint8_t*)(*arg), read_file_len);    
}


//-------------------------------------------------------------------------------------------
// Function Name : log_file_send
// Purpose       : 同步文件
// Param[in]     : void  
// Param[out]    : None
// Return type   : static
// Comment       : 2021-01-18
//-------------------------------------------------------------------------------------------
//static void log_file_send(bool isError)
//{
//    uint8_t pb_crc = 0;
//    uint8_t *data = NULL;
//    uint16_t *length = NULL;
//    log_msg log_message;
//
//    memset (&log_message, 0, sizeof(log_msg));
//    ble_data_var_tx_get(&data, &length, BLE_NUS_CH2_UUID);
//
//    if (!isError)
//    {
//        memset(file_content_upload, 0, DATA_LENGTH_MAX_512B);
//        log_file_read();
//    }
//
//    //参数赋值
//    log_message.service = service_type_index_enum_SERVICE_TYPE_INDEX_LOG;
//    log_message.operate = SERVICE_OPERATE_TYPE_enum_SERVICE_OPERATE_TYPE_GET;
//    log_message.sub_operate = LOG_SUB_OPERATE_TYPE_enum_LOG_GET;
//
//    log_message.has_log_num = false;
//    log_message.log_name.arg = g_log_file_name;
//    log_message.log_name.funcs.encode = &encode_string;
//    log_message.content.arg = file_content_upload;
//    log_message.content.funcs.encode = &log_file_encode_bytes;
//
//    //编码缓存
//    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX);
//    //编码
//    pb_encode(&encode_stream, log_msg_fields, &log_message);
//
//    *length = encode_stream.bytes_written;
//    ble_nus_data_tx_ch2( );
//
//    //对pb编码进行crc校验
//    pb_crc = CRC_Calc8_Table_L(data, *length);
//
//    //命令协议 发送通道2
//    if (trans_cmp == false)
//    {
//        ble_cmd_end_type_tx(log_message.service, 0, log_message.operate, log_message.sub_operate, *length, enumONE_FRAME_END, pb_crc);
//    }
//    else
//    {
//        ble_cmd_end_type_tx(log_message.service, 0, log_message.operate, log_message.sub_operate, *length, enumFILE_END, pb_crc);
//    }
//}

#include "ble_interflow_single.h"
#include "file_upload_pb_encode.h"

static uint32_t log_data_file_size_get(void)
{
	char file_path[100] = {0};
    FIL f_dst = {0};
	memset (g_log_file_name, 0, LOG_FILE_NAME_LENGTH);
    if (g_debug_log_flag == true)
    {
        if(!debug_log_file_name_get(g_log_file_name)){
            return 0;
        }
        sprintf(file_path, "0:/iGPSPORT/System/%s", g_log_file_name);
        if (FR_OK == f_open(&f_dst, file_path, FA_READ)){
            uint32_t log_data_file_size = f_dst.obj.objsize;
            f_close(&f_dst);

            return log_data_file_size;
        }
    }
    else
    {
        if(!log_file_name_get(g_log_file_name)){
            return 0;
        }
        sprintf(file_path, "%s/%s", g_base_log_dir,  g_log_file_name);
        if (FR_OK == f_open(&f_dst, file_path, FA_READ)){
            uint32_t log_data_file_size = f_dst.obj.objsize;
            f_close(&f_dst);

            return log_data_file_size;
        }
    }
    return 0;
}

static uint32_t log_file_date_size = 0;
static bool log_file_read_direct(uint8_t *buf)
{
    char file_path[100] = {0};
    unsigned int tt = 1;

    memset(file_path, 0 ,100);

    if (NULL == fdst)
    {
        fdst = QW_FIL_FD_MALLOC(sizeof(FIL));
        if (NULL == fdst)
        {
        	trans_cmp = true;
            return false;
        }
    }

    if (false == open_flag){
    	sprintf(file_path, "%s/%s", g_base_log_dir,  g_log_file_name);
        if (FR_OK == f_open(fdst, file_path, FA_READ)){
            open_flag = true;
            log_file_date_size = 0;
        }else{
        	trans_cmp = true;
        	return false;
        }
    }

    if (true == open_flag){
        if(!f_eof(fdst)){
        	f_read(fdst, buf + read_file_len, FILE_READ_512_SIZE, &tt);
            read_file_len += tt;
            log_file_date_size += tt;
            if ((tt < FILE_READ_512_SIZE) || (f_size(fdst) == log_file_date_size)){
                f_close(fdst);
                QW_FIL_FD_FREE(fdst);
                fdst = NULL;
                open_flag = false;
                trans_cmp = true;
                sprintf(file_path, "%s/%s", g_base_log_dir,  g_log_file_name);
                rt_kprintf("log_file_num_get folder %s %d\n", g_base_log_dir, __LINE__);
                f_unlink(file_path);
            }else{
            	f_read(fdst, buf + read_file_len, FILE_REMIND_SIZE, &tt);
            	read_file_len += tt;
            	log_file_date_size += tt;
            	if ((tt < FILE_REMIND_SIZE) || (f_size(fdst) == log_file_date_size)){
                    f_close(fdst);
                    QW_FIL_FD_FREE(fdst);
                    fdst = NULL;
                    open_flag = false;
                    trans_cmp = true;
                    sprintf(file_path, "%s/%s", g_base_log_dir,  g_log_file_name);
                rt_kprintf("log_file_num_get folder %s %d\n", g_base_log_dir, __LINE__);
                    f_unlink(file_path);
            	}
            }
        }else{
            f_close(fdst);
            QW_FIL_FD_FREE(fdst);
            fdst = NULL;
            open_flag = false;
            trans_cmp = true;
            sprintf(file_path, "%s/%s", g_base_log_dir,  g_log_file_name);
                rt_kprintf("log_file_num_get folder %s %d\n", g_base_log_dir, __LINE__);
            f_unlink(file_path);
            log_file_date_size = 0;
        }
    }
    return true;
}

static bool debug_log_file_read_direct(uint8_t *buf)
{
    static char fnamestr[100] = {0};
    QW_DIR dir;
    QW_FILINFO fno;
    bool get_file_name_flag = false;
    unsigned int tt = 1;

    if (false == open_flag)
    {
        memset(fnamestr, 0 ,100);

        sprintf(fnamestr, "0:/iGPSPORT/System/%s", g_log_file_name);

        if (FR_OK == qw_f_open(&fdst, fnamestr, QW_FA_READ))
        {
            open_flag = true;
            log_file_date_size = 0;
        }
    }

    if (true == open_flag)
    {
        if (!qw_f_eof(fdst))
        {
            qw_f_read(fdst, buf + read_file_len, FILE_READ_512_SIZE, &tt);
            read_file_len += tt;
            log_file_date_size += tt;
            if ((tt < FILE_READ_512_SIZE) || (qw_f_size(fdst) == log_file_date_size))
            {
                qw_f_close(fdst);
                fdst = NULL;
                open_flag = false;
                trans_cmp = true;
                qw_f_unlink(fnamestr);
            }
            else
            {
                qw_f_read(fdst, buf + read_file_len, FILE_REMIND_SIZE, &tt);
                read_file_len += tt;
                log_file_date_size += tt;
                if ((tt < FILE_REMIND_SIZE) || (qw_f_size(fdst) == log_file_date_size))
                {
                    qw_f_close(fdst);
                    fdst = NULL;
                    open_flag = false;
                    trans_cmp = true;
                    qw_f_unlink(fnamestr);
                }
            }
        }
        else
        {
            qw_f_close(fdst);
            fdst = NULL;
            open_flag = false;
            trans_cmp = true;
            qw_f_unlink(fnamestr);
            log_file_date_size = 0;
        }
    }
    return true;
}

static bool log_data_file_send_raw(bool isStart)
{
	uint8_t *data = NULL;
	uint16_t *length = NULL;
	bool ret = false;

	ble_data_var_tx_get_and_ret(&data, &length, BLE_NUS_CH2_UUID);

	read_file_len = 0;
	if(isStart){
		uint16_t encoded_length = 0;
		uint32_t file_size = log_data_file_size_get();
		if(file_size){
			encoded_length = file_upload_pb_encode(&data[PB_LENGTH_FIELD_SIZE],ONE_FRAME_DATA_LENGTH_MAX_CH2_TX - PB_LENGTH_FIELD_SIZE,\
					file_size,0,0,g_log_file_name,0);
			if(encoded_length){

				data[0] = (encoded_length>>24)&0xff;
				data[1] = (encoded_length>>16)&0xff;
				data[2] = (encoded_length>>8)&0xff;
				data[3] = (encoded_length)&0xff;

				*length = PB_LENGTH_FIELD_SIZE + encoded_length;
                if (g_debug_log_flag == true)
                {
                    ret = debug_log_file_read_direct(data + *length);
                }
                else
                {
				    ret = log_file_read_direct(data + *length);
                }
				*length += read_file_len;
			}
		}
	}else{
        if (g_debug_log_flag == true)
        {
            ret = debug_log_file_read_direct(data);
        }
        else
        {
		    ret = log_file_read_direct(data);
        }
		if(ret){
			*length = read_file_len;
		}
	}
	if(ret){
		ble_nus_data_tx_ch2();
		if(isStart){
			ble_cmd_end_data_stream_type_tx(service_type_index_enum_SERVICE_TYPE_INDEX_LOG, DATA_STREM_UPLOAD_MODE, \
					0, SERVICE_OPERATE_TYPE_enum_SERVICE_OPERATE_TYPE_GET, LOG_SUB_OPERATE_TYPE_enum_LOG_GET, *length, enumFILE_END, 0);
		}
	}else{
		ble_cmd_err_status_tx(service_type_index_enum_SERVICE_TYPE_INDEX_LOG,0,SERVICE_OPERATE_TYPE_enum_SERVICE_OPERATE_TYPE_GET,LOG_SUB_OPERATE_TYPE_enum_LOG_GET);
	}

	return ret;
}

static bool transfor_start = true;
uint8_t log_file_transfor(uint8_t service_type,uint8_t op_type)
{
	if(transfor_start && ble_tx_channel_is_free(BLE_NUS_CH2_UUID)){
		if(!log_data_file_send_raw(transfor_start)){
        	trans_cmp = false;
        	transfor_start = true;
        	return true;
		}else{
			transfor_start = false;
	    	if(trans_cmp){
	        	trans_cmp = false;
	        	transfor_start = true;
	        	return true;
	    	}
		}
	}else{
		if(open_flag && ble_tx_channel_is_free(BLE_NUS_CH2_UUID)){
	        if (false == trans_cmp){
	        	if(log_data_file_send_raw(transfor_start)){
		        	if(trans_cmp){
			        	trans_cmp = false;
			        	transfor_start = true;
			        	return true;
		        	}
	        	}else{
		        	trans_cmp = false;
		        	transfor_start = true;
		        	return true;
	        	}
	        }
		}
	}
	return false;
}

void log_file_transfor_abort(uint8_t service_type,uint8_t op_type)
{
    rt_kprintf("log_file_transfor_abort folder %d\n", __LINE__);
	if(open_flag){
		transfor_start = true;
        f_close(fdst);
        QW_FIL_FD_FREE(fdst);
        fdst = NULL;
        open_flag = false;
        read_file_len = 0;
        trans_cmp = false;
	}
}

//-------------------------------------------------------------------------------------------
// Function Name : log_status_handle
// Purpose       : LOG模块通信状态处理接口
// Param[in]     : uint8_t *buf  
// Param[out]    : None
// Return type   : 
// Comment       : 2021-01-18
//-------------------------------------------------------------------------------------------
void log_status_handle(uint8_t *buf)
{
    ble_status_cmd_st *ble_status_cmd_s = (ble_status_cmd_st *)buf;
    uint8_t status = ble_status_cmd_s->status;
//    char file_path[100] = {0};
    
    switch(status)
    {
        case enumSUCCESS_STATUS:
            read_file_len = 0;
//            if (true == trans_cmp)
//            {
//                sprintf(file_path, "%s/%s", g_base_log_dir,  g_log_file_name);
//                f_unlink(file_path);
//            }
//            else
//            {
//                log_file_send(false);
//            }
            break;
        case enmuDATA_ERR_STATUS:
//            log_file_send(true);
            break;
        default:
            break;
    }           
}

//-------------------------------------------------------------------------------------------
// Function Name : log_decode
// Purpose       : LOG日志同步模块解码接口
// Param[in]     : uint8_t * pb_buffer     
//                 uint16_t buffer_length  
//                 END_TYPE end_type       
// Param[out]    : None
// Return type   : 
// Comment       : 2021-01-18
//-------------------------------------------------------------------------------------------
void log_decode(uint8_t * pb_buffer, uint16_t buffer_length, END_TYPE end_type)
{
    uint8_t status = false;
    log_msg log_message;

    memset (&log_message, 0, sizeof(log_msg));    
        
    pb_istream_t decode_stream = pb_istream_from_buffer(pb_buffer, buffer_length);
    status = pb_decode(&decode_stream, log_msg_fields, &log_message);
    // g_base_log_dir = log_message.log_type ? SYSTEM_LOG_DIR : SYSTEM_LOG_HFAULT_DIR;
    if (log_message.log_type == 0)
    {
        g_base_log_dir = SYSTEM_LOG_HFAULT_DIR;
        g_debug_log_flag = false;
    }
    else if (log_message.log_type == 1)
    {
        g_base_log_dir = SYSTEM_LOG_DIR;
        g_debug_log_flag = false;
    }
    else if (log_message.log_type == 2)
    {
        g_debug_log_flag = true;
    }
    else
    {
        g_debug_log_flag = false;
    }

    if (true == status)
    {
        switch(log_message.operate)
        {
            case SERVICE_OPERATE_TYPE_enum_SERVICE_OPERATE_TYPE_GET:
                switch (log_message.sub_operate)
                {
                    case LOG_SUB_OPERATE_TYPE_enum_LOG_NUMBER_GET:
                        log_file_num_send();
                        break;
                    case LOG_SUB_OPERATE_TYPE_enum_LOG_GET:
//                        trans_cmp = false;
//                        memset (g_log_file_name, 0, LOG_FILE_NAME_LENGTH);
//                        log_file_name_get(g_log_file_name);
//                        log_file_send(false);
                        break;
                    case LOG_SUB_OPERATE_TYPE_enum_DEBUG_LOG_PACK_REQUEST:
                        thread_pool_add_task(debug_log_n_trace_zip, NULL, debug_log_n_trace_zip_finish, osPriorityHigh);
                        ble_cmd_success_status_tx(log_message.service, 0,  log_message.operate, log_message.sub_operate);
                        break;
                    default:
                        ble_cmd_status_tx(log_message.service, 0,  log_message.operate, log_message.sub_operate, enumINVALID_CMD);
                        break;
                }
                break;
            case SERVICE_OPERATE_TYPE_enum_SERVICE_OPERATE_TYPE_SET:
            case SERVICE_OPERATE_TYPE_enum_SERVICE_OPERATE_TYPE_ADD:
            case SERVICE_OPERATE_TYPE_enum_SERVICE_OPERATE_TYPE_DEL:
            default:
                ble_cmd_status_tx(log_message.service, 0,  log_message.operate, log_message.sub_operate, enumINVALID_CMD);
                break;
        }
    }
    else
    {
        ble_cmd_status_tx(log_message.service, 0,  log_message.operate, log_message.sub_operate, enmuDATA_ERR_STATUS);
    }
}

//-------------------------------------------------------------------------------------------
// Function Name : log_timeout_handle
// Purpose       : LOG同步超时
// Param[in]     : void  
// Param[out]    : None
// Return type   : 
// Comment       : 2021-01-18
//-------------------------------------------------------------------------------------------
void log_timeout_handle(void)
{
    if (fdst != NULL)
    {
        rt_kprintf("log_timeout_handle folder %d\n", __LINE__);
        f_close(fdst);
        QW_FIL_FD_FREE(fdst);
        open_flag = false;
        read_file_len = 0;
        trans_cmp = true;
    }
}

static void debug_log_file_package_state_send(void)
{
    if (g_device_get_ble_connect_status())
    {
        ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_LOG, 0, SERVICE_OPERATE_TYPE_enum_SERVICE_OPERATE_TYPE_GET, 
            LOG_SUB_OPERATE_TYPE_enum_DEBUG_LOG_PACK_COMPLETE, 0);
    }
}

char zip_str[128] = {0};
extern void add_folder_to_zip(zipFile zf, const char *base_path, const char *folder_path);
extern void add_file_to_zip(zipFile zf, const char *base_path, const char *file_path, QW_FILINFO fileinfo);

static bool debug_log_n_trace_zip(const thread_pool_task* task_info)
{
    zipFile zf;
    QW_FILINFO fno, fileinfo;
    QW_DIR dir;

    if (QW_OK != qw_f_opendir(&dir, "0:/iGPSPORT/System/"))
    {
        return false;
    }

    while (QW_OK == qw_f_readdir(&dir, &fno) && fno.fname[0])
    {
        if (fno.fname[0] == 0)
        {
            break;
        }

        if (!(fno.fattrib & AM_DIR))
        {
            if (strncmp(fno.fname, "debug_log_", strlen("debug_log_")) == 0)
            {
                char full_path[256];
                sprintf(full_path, "0:/iGPSPORT/System/%s", fno.fname);
                qw_f_unlink(full_path);
            }
        }
    }

    uint32_t second = service_datetime_get_gmt_time() + service_get_timezone();
    sprintf(zip_str, "0:/iGPSPORT/System/debug_log_%s.zip", qw_timestr(second));

    // rt_kprintf("create zip file %s\n", zip_str);
    zf = zipOpen(zip_str, APPEND_STATUS_CREATE);
    if (!zf)
    {
        return false;
    }

    // rt_kprintf("add folder to zip: 0:/iGPSPORT/System/log\n");
    add_folder_to_zip(zf, "0:/iGPSPORT/System", "0:/iGPSPORT/System/log");
    if (QW_OK == qw_f_stat("0:/iGPSPORT/System/trace.bin", &fileinfo))
    {
        // rt_kprintf("add file to zip: 0:/iGPSPORT/System/trace.bin\n");
        add_file_to_zip(zf, "0:/iGPSPORT/System", "0:/iGPSPORT/System/trace.bin", fileinfo);
    }
    // rt_kprintf("close zip file\n");
    zipClose(zf, NULL);
    // rt_kprintf("zip file %s created\n", zip_str);

    return true;
}

static void debug_log_n_trace_zip_finish(void *arg, bool res)
{
    // rt_kprintf("debug_log_n_trace_zip_finish!!!!!\n");
    debug_log_file_package_state_send();
}
