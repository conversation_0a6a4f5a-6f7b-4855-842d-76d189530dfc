/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   data_sync_remind.c
@Time    :   2025/08/06 11:13:40
*
**************************************************************************/
#include "data_sync_remind.h"
#include "igs_dev_config.h"
#include "kvdb.h"
#include "cfg_header_def.h"
#include "gui_event_service.h"
#include "focus_mode_srv/focus_mode_srv.h"

#ifndef SIMULATOR
#include "alarm_clock_app/alarm_manager.h"
#include "algo_service_sport_status.h"
#include "app_sensor_hub_service.h"
#include "qw_system_params.h"
#include "sensorhub_common.h"
#include "service_config.h"
#include "service_datetime.h"
#include "service_event.h"
#include "service_gui_health.h"
#include "service_health.h"
#endif

#define DATA_SYNC_INTERVAL 72 * 60 * 60   //72小时

#define DATA_SYNC_TIME_GET_SEC(year, month, day, hour, minute, second) \
    ({ \
        struct tm tm = {0}; \
        tm.tm_year = (year) - 1900; \
        tm.tm_mon = (month) - 1; \
        tm.tm_mday = (day); \
        tm.tm_hour = (hour); \
        tm.tm_min = (minute); \
        tm.tm_sec = (second); \
        tm.tm_isdst = -1; \
        mktime(&tm); \
    })


static cfg_data_sync_t data_sync_info = {0};

#ifndef SIMULATOR
static alarm_id_t data_sync_alarm;
#endif

#ifndef SIMULATOR
static void data_sync_remind_nofity(rt_alarm_t alarm, time_t timestamp);
#endif

static void data_sync_remind_set_alarm(void);
static void data_sync_remind_clr_ararm(void);
static bool data_sync_remind_check_need_remind(void);

uint32_t cal_next_notify_time(void)
{
    uint32_t notify_time = 0;
    qw_tm_t tmp_buf = {0};
    tmp_buf.tm_hour = (data_sync_info.last_sync_tm.tm_hour);
    tmp_buf.tm_min = data_sync_info.last_sync_tm.tm_min;
    tmp_buf.tm_sec = data_sync_info.last_sync_tm.tm_sec;

    if (tmp_buf.tm_hour >= 18 && tmp_buf.tm_hour < 22)
    {
        notify_time = tmp_buf.tm_hour * 60 * 60 + tmp_buf.tm_min * 60 + tmp_buf.tm_sec;
    }
    else
    {
        notify_time = 18 * 60 * 60;
    }
    return notify_time;
}

void data_sync_remind_init(void)
{
    data_sync_time_get(&data_sync_info.last_sync_tm);
    DATA_SYNC_REMIND_LOG_D("init data_sync time: %d-%d-%d %d:%d:%d",
        data_sync_info.last_sync_tm.tm_year,
        data_sync_info.last_sync_tm.tm_mon,
        data_sync_info.last_sync_tm.tm_mday,
        data_sync_info.last_sync_tm.tm_hour,
        data_sync_info.last_sync_tm.tm_min,
        data_sync_info.last_sync_tm.tm_sec
            );
    data_sync_info.enable = data_sync_enable_get();

    if (data_sync_info.enable)
    {
        data_sync_remind_set_alarm();
    }
}

void data_sync_remind_set_last_sync_time(void)
{

    qw_tm_t cur_time = {0};
    service_datetime_get(&cur_time);
    //todo 向kv写同步时间
    memcpy(&data_sync_info.last_sync_tm, &cur_time, sizeof(qw_tm_t));
    data_sync_info.enable = true;
    data_sync_time_set(&data_sync_info.last_sync_tm);
    data_sync_enable_set(true);
    DATA_SYNC_REMIND_LOG_D("data_sync time: %d-%d-%d %d:%d:%d",
        data_sync_info.last_sync_tm.tm_year,
        data_sync_info.last_sync_tm.tm_mon,
        data_sync_info.last_sync_tm.tm_mday,
        data_sync_info.last_sync_tm.tm_hour,
        data_sync_info.last_sync_tm.tm_min,
        data_sync_info.last_sync_tm.tm_sec);

    data_sync_remind_reset();
}

#ifndef SIMULATOR
static void data_sync_remind_nofity(rt_alarm_t alarm, time_t timestamp)
{
    data_sync_remind_clr_ararm();
    data_sync_remind_set_alarm();
    if (!data_sync_info.enable)   //从未同步过
    {
        return;
    }

    qw_tm_t cur_time = {0};
    service_datetime_get(&cur_time);
    time_t cur_time_stamp =  DATA_SYNC_TIME_GET_SEC(cur_time.tm_year, cur_time.tm_mon, cur_time.tm_mday, cur_time.tm_hour, cur_time.tm_min, cur_time.tm_sec);
    time_t last_time_stamp =  DATA_SYNC_TIME_GET_SEC(data_sync_info.last_sync_tm.tm_year, data_sync_info.last_sync_tm.tm_mon, data_sync_info.last_sync_tm.tm_mday,
        data_sync_info.last_sync_tm.tm_hour, data_sync_info.last_sync_tm.tm_min, data_sync_info.last_sync_tm.tm_sec
    );
    DATA_SYNC_REMIND_LOG_D("data_sync_remind_nofity checking");
    if (cur_time_stamp > last_time_stamp && cur_time_stamp - last_time_stamp >= DATA_SYNC_INTERVAL)
    {
        //todo 提醒事件
        if (data_sync_remind_check_need_remind())
        {
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_DATA_SYNC, NULL);
        }
        else
        {
            DATA_SYNC_REMIND_LOG_I("data_sync_remind_nofity check failed");
        }
    }
    else
    {
        DATA_SYNC_REMIND_LOG_D("cur_time_stamp = %ld", (unsigned int)cur_time_stamp);
        DATA_SYNC_REMIND_LOG_D("last_time_stamp = %ld", (unsigned int)last_time_stamp);
        DATA_SYNC_REMIND_LOG_I("data_sync_remind_nofity check failed");
    }
    data_sync_remind_set_last_sync_time();
}
#endif

static void data_sync_remind_set_alarm(void)
{
#ifndef SIMULATOR
    uint32_t next_time = cal_next_notify_time();
    DATA_SYNC_REMIND_LOG_D("next notify time = %d", next_time);
    data_sync_alarm = alarm_manager_create(next_time, 0x80, data_sync_remind_nofity);
    alarm_manager_enable(data_sync_alarm, true);
#endif
}

static void data_sync_remind_clr_ararm(void)
{
#ifndef SIMULATOR
    //todo 清除闹钟
    alarm_manager_enable(data_sync_alarm, false);
    alarm_manager_delete(data_sync_alarm);
#endif
}

bool data_sync_remind_check_need_remind(void)
{
    bool ret = false;
#ifndef SIMULATOR
    if ((enum_status_free == get_sport_status() || enum_status_ready == get_sport_status())
    && (service_gui_get_sleep_status() != SLEEP_ENTER_EVENT)
        && (!get_dnd_state(true))
        && (!get_sleep_state(true)))
    {
        ret = true;
    }
#endif
    return ret;
}

void data_sync_remind_reset(void)
{
    data_sync_remind_clr_ararm();
    data_sync_remind_set_alarm();
}
