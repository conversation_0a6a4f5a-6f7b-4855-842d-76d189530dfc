/************************************************************************
*
* Copyright(c) 2025, igpsport Software Co., Ltd.
* All Rights Reserved.
* @File    :   maf.c
*
**************************************************************************/
#include <stddef.h>
#include "maf.h"

//滑动平均滤波器计算
float maf_f32_exec(maf_f32_t *self, float x)
{
    if (self == NULL)
    {
        return 0.0f;
    }

    if (self->len < self->capacity)
    {
        self->buf[self->len] = x;
        self->len += 1;
        self->sum += x;
    }
    else
    {
        self->sum -= self->buf[self->next];
        self->buf[self->next] = x;
        self->sum += self->buf[self->next];
    }

    self->next += 1;
    if (self->next >= self->capacity)
    {
        self->next = 0;
    }

    return self->sum / self->len;
}

//重置滑动平均滤波器
void maf_f32_reset(maf_f32_t *self)
{
    if (self != NULL)
    {
        self->len = 0;
        self->sum = 0.0f;
        self->next = 0;
    }
}

//滑动平均滤波器计算
int32_t maf_i32_exec(maf_i32_t *self, int32_t x)
{
    if (self == NULL)
    {
        return 0;
    }

    if (self->len < self->capacity)
    {
        self->buf[self->len] = x;
        self->len += 1;
        self->sum += x;
    }
    else
    {
        self->sum -= self->buf[self->next];
        self->buf[self->next] = x;
        self->sum += self->buf[self->next];
    }

    self->next += 1;
    if (self->next >= self->capacity)
    {
        self->next = 0;
    }

    return self->sum / (int32_t)self->len;
}

//重置滑动平均滤波器
void maf_i32_reset(maf_i32_t *self)
{
    if (self != NULL)
    {
        self->len = 0;
        self->sum = 0;
        self->next = 0;
    }
}

//滑动平均滤波器计算
uint32_t maf_u32_exec(maf_u32_t *self, uint32_t x)
{
    if (self == NULL)
    {
        return 0;
    }

    if (self->len < self->capacity)
    {
        self->buf[self->len] = x;
        self->len += 1;
        self->sum += x;
    }
    else
    {
        self->sum -= self->buf[self->next];
        self->buf[self->next] = x;
        self->sum += self->buf[self->next];
    }

    self->next += 1;
    if (self->next >= self->capacity)
    {
        self->next = 0;
    }

    return self->sum / self->len;
}

//重置滑动平均滤波器
void maf_u32_reset(maf_u32_t *self)
{
    if (self != NULL)
    {
        self->len = 0;
        self->sum = 0;
        self->next = 0;
    }
}
