#include "lvgl.h" 

#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP ALIGN(4)
#endif

#ifndef eZIP_RGBARGB888A
#define eZIP_RGBARGB888A
#endif


LV_ATTRIBUTE_MEM_ALIGN_EZIP const  uint8_t sys_set_peripheral_sensors_map[] SECTION(".ROM3_IMG_EZIP.sys_set_peripheral_sensors") = { 
    0x00,0x00,0x08,0x2f,0x46,0x08,0x20,0x00,0x00,0x48,0x00,0x48,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0x03,0x00,0x00,0x00,0xa0,0x00,0x00,0x04,0x08,0x00,0x00,0x07,0x44,
    0x3d,0xf3,0x8f,0x6d,0xa3,0xbe,0x02,0xf8,0x7b,0x17,0x37,0x29,0x4d,0xdb,0x75,0x75,0x69,0x32,0x28,0x2d,0x5d,0x1c,0x87,0xa5,0xeb,0x18,0x82,0x4c,0x4d,0x7f,0xb0,0x14,
    0x58,0xb5,0xa9,0x13,0x13,0x43,0x6b,0x98,0x20,0x48,0x68,0x5b,0xc7,0x3a,0x01,0xdb,0x7f,0xb1,0x93,0xd6,0x4e,0x69,0xed,0xfc,0x31,0xed,0x0f,0xb4,0x0d,0x06,0x5a,0x05,
    0x83,0x4a,0x23,0x43,0x1a,0xeb,0xb6,0x4e,0x22,0x65,0xa4,0xeb,0x8f,0xb5,0x82,0x6a,0x53,0x3b,0xb5,0x8d,0xed,0x42,0x12,0x4a,0x44,0x93,0x90,0x36,0x34,0x21,0x8d,0xdb,
    0xdc,0x77,0xef,0x6b,0xe7,0xe2,0x8b,0x73,0xb6,0xef,0x7c,0xdf,0x3b,0xff,0xc8,0x5e,0x74,0xf6,0x37,0x77,0xdf,0x7b,0x77,0xfe,0xdc,0x7b,0xef,0xfb,0xde,0x3b,0x00,0x00,
    0x44,0xc8,0x23,0x61,0xad,0xae,0x2a,0x98,0xc4,0x3b,0x69,0x58,0x0b,0x80,0x2e,0x40,0x58,0x49,0x5b,0x25,0xc8,0xe0,0xa4,0xef,0xe5,0xf1,0x49,0xf4,0x87,0x30,0x41,0xa3,
    0x8f,0x69,0xeb,0xa7,0xf1,0x05,0x3a,0x7e,0x16,0x90,0x9d,0x82,0xd2,0xb1,0x63,0xe8,0xef,0xff,0x4c,0xe4,0x3d,0xe5,0x14,0x10,0x6b,0xae,0x72,0x41,0x49,0xc9,0x16,0x60,
    0xb8,0x19,0x18,0xdb,0x18,0x83,0x61,0x5a,0x29,0x1c,0x22,0x58,0x07,0x00,0x6e,0x74,0x60,0xe0,0x83,0x4b,0x05,0x07,0x88,0x35,0xdf,0x71,0x3b,0x48,0xac,0x91,0x46,0x0f,
    0xd3,0xbf,0x75,0x96,0x5e,0x0c,0xe1,0x55,0x00,0xf9,0x37,0xb8,0x37,0x72,0x22,0xef,0x01,0x31,0x6f,0xf5,0x56,0xba,0xdc,0x8f,0x68,0xf8,0x1d,0xfb,0x4d,0x15,0x5e,0x27,
    0x50,0xbb,0x31,0x18,0x39,0x9b,0x77,0x80,0x58,0x4b,0xf5,0xf7,0xc9,0x85,0x7e,0x46,0xc3,0xaf,0xe5,0x3e,0xc8,0xb1,0x9d,0x18,0x0c,0xef,0xc9,0x0b,0x40,0xcc,0x53,0xfd,
    0x6d,0x40,0xc9,0x4b,0xa3,0x7a,0xc8,0x2f,0x79,0x1b,0x64,0xb6,0x1d,0xdb,0xc3,0xef,0xe7,0x04,0x10,0x6b,0xae,0xfe,0x22,0x20,0x06,0x48,0x73,0x63,0x9e,0x81,0x51,0xbb,
    0xdc,0x00,0x05,0xf2,0xc7,0x30,0x10,0xee,0xb4,0x15,0x10,0xc5,0x99,0xa7,0x48,0xe5,0x2f,0x68,0x58,0x0a,0x05,0x21,0xd8,0x88,0x81,0xee,0x0e,0xcb,0x01,0x31,0x8f,0xeb,
    0x66,0xc0,0x92,0xe7,0xa7,0x56,0xa6,0x02,0x93,0xf4,0x90,0x4c,0x03,0x62,0xad,0x35,0xf7,0x91,0x4f,0xef,0xa3,0xe1,0x2a,0x28,0x58,0x61,0x5b,0x52,0xb9,0x9b,0x29,0x40,
    0xcc,0xeb,0xfe,0x21,0x7d,0xbd,0x04,0x85,0x2e,0x3c,0x26,0x31,0x56,0xaf,0x15,0xb8,0x25,0x13,0xf1,0xa6,0xa5,0x28,0xe0,0xc4,0xcd,0x64,0x39,0x48,0xf8,0xa2,0xd6,0xa1,
    0xac,0x00,0x31,0x8f,0x3b,0x48,0x5a,0xf7,0x14,0x05,0x9c,0x84,0xdc,0x4f,0xa9,0x49,0xab,0x69,0x17,0x8b,0xc1,0x41,0x68,0x86,0x62,0x15,0x26,0xaf,0x51,0x67,0xdc,0x92,
    0x61,0xb7,0x2a,0x66,0x38,0x71,0x24,0xbb,0xb2,0xb2,0x20,0xbb,0x02,0x72,0xff,0xe8,0x0d,0xb8,0x70,0xf9,0x3a,0xdc,0x55,0x51,0x06,0x0b,0x4b,0xa5,0x1c,0xc5,0x24,0xb9,
    0x5e,0x29,0x70,0x1d,0x06,0x96,0x72,0x4b,0xe1,0xbc,0x7a,0xe6,0x53,0x78,0xe1,0xdf,0x23,0x70,0xfc,0xe2,0xf8,0xf4,0xbe,0x87,0x6a,0x16,0xc2,0xd3,0xf7,0x2c,0x81,0x86,
    0x55,0x0b,0xec,0xb6,0xa2,0x1d,0xf4,0x71,0x42,0x97,0x05,0xc5,0x93,0x40,0xe9,0x5d,0x2b,0xf3,0x9c,0x87,0xde,0xe8,0x87,0x37,0x43,0xa3,0x29,0x8f,0x7b,0xd7,0x2f,0x85,
    0xbd,0x0d,0xcb,0x6c,0x86,0x74,0xbd,0x92,0xf7,0x93,0x32,0xdb,0x70,0x2c,0x43,0xb6,0x0e,0xce,0x73,0xef,0x5e,0x49,0x0b,0x87,0x4b,0xe0,0xf8,0xf0,0x0c,0xcb,0xb2,0x47,
    0x1c,0xdb,0x32,0x06,0xe9,0x78,0x6d,0x65,0x6d,0xf9,0xf0,0x4c,0xe7,0x80,0xae,0x79,0xbf,0x3a,0x75,0xc5,0xe6,0xd5,0x0c,0x1f,0x4c,0xeb,0x62,0xb1,0xaa,0x5c,0xc2,0x73,
    0x56,0x16,0x9e,0x27,0x3e,0xba,0x06,0xf5,0xaf,0xf4,0x19,0xc9,0xdc,0xed,0x85,0x54,0x3a,0x5a,0x9e,0xda,0x82,0x78,0xcb,0xc2,0x02,0x38,0x5d,0xbd,0x89,0x9e,0xfa,0xe9,
    0x81,0x09,0x43,0xe7,0x9e,0x19,0x9c,0xd0,0xd4,0x63,0x99,0x44,0xcb,0x37,0x48,0xa9,0x9b,0x5d,0x62,0xfb,0x39,0xfc,0x07,0x6d,0xde,0x7f,0x11,0xda,0x8e,0x0e,0x4f,0xef,
    0xbb,0xab,0xb2,0xcc,0x90,0x8e,0xb5,0x37,0x27,0xe6,0x73,0x5d,0x7c,0xb3,0xd8,0xcd,0xee,0xd6,0xb6,0xa0,0x58,0x27,0x50,0x9c,0xf8,0x8f,0x7c,0x12,0xfb,0x31,0x1c,0x92,
    0xfa,0xc9,0xd7,0x7d,0x61,0xbe,0x6e,0x1d,0x8f,0xaf,0x5d,0x3c,0xcb,0x7a,0xf8,0x37,0x06,0x42,0xd6,0x59,0x93,0x04,0xb5,0x92,0x66,0x0f,0x59,0x60,0x9b,0x94,0xc3,0x69,
    0xa3,0x2d,0x95,0x3c,0xff,0xcd,0x0a,0x5d,0x7a,0x78,0x3e,0x94,0x4a,0x38,0x7c,0x7f,0x9a,0x6b,0x98,0xa8,0xf2,0xab,0x4a,0x66,0xfd,0xa0,0x4d,0xcb,0x7e,0x47,0x5f,0xb7,
    0x5a,0x01,0x87,0x27,0x7c,0x1f,0xfc,0x74,0xf5,0x8c,0x39,0xf7,0x90,0x15,0x85,0x87,0xa3,0x14,0x5f,0xa2,0x29,0xf5,0xec,0xbe,0xd7,0x09,0x8f,0xac,0x49,0x58,0xd0,0xed,
    0x4b,0xe6,0x41,0xc3,0xca,0x9b,0xa0,0xf7,0xd3,0x1b,0xd0,0x33,0x72,0x3d,0xb6,0xef,0x70,0xdf,0xf8,0xf4,0x35,0x04,0xca,0x24,0x6a,0xbc,0x9a,0xf9,0xab,0x15,0x70,0x7c,
    0x9b,0x9c,0x04,0xdf,0x99,0x72,0xfe,0x9f,0xba,0x47,0x29,0x93,0xbe,0x02,0x6f,0xbd,0x9f,0x70,0x97,0xc7,0xbe,0xbc,0x18,0x9e,0xae,0x5b,0x92,0xd6,0x15,0x15,0xd7,0x55,
    0xe4,0x9d,0x47,0x57,0x88,0x84,0x74,0x2d,0x09,0x90,0xfb,0x4d,0x11,0xef,0xad,0x94,0x80,0xac,0x17,0x8e,0x5a,0xae,0x46,0x65,0x88,0x4c,0xd5,0x62,0x7a,0x25,0x19,0x92,
    0xb0,0x74,0x80,0x01,0x93,0x66,0xbc,0xf1,0x14,0xf4,0x52,0x4f,0xbd,0x52,0xf1,0xa7,0xa9,0x17,0x0e,0x97,0x45,0x54,0xa0,0x1a,0x81,0xa3,0x58,0x4d,0xb2,0xf5,0x0a,0x6a,
    0xa4,0x61,0x22,0x48,0xa3,0xfc,0x88,0xa8,0xe5,0x5c,0xfd,0x34,0x7d,0x1b,0x97,0xda,0x92,0xd3,0xf9,0x54,0x0f,0xa1,0x4d,0x60,0xc0,0x9e,0x0e,0xd2,0xfe,0x7b,0x9d,0xbf,
    0x14,0x11,0x9c,0x9f,0xf8,0xdb,0xa5,0xe9,0xc0,0x29,0x38,0x1e,0xa4,0x15,0xe5,0x3a,0x4a,0xb0,0x16,0x15,0xb0,0x63,0x16,0xc4,0x9a,0xab,0x5c,0x3c,0x2d,0x11,0x6d,0x3d,
    0xf6,0xb7,0x29,0x40,0xb8,0x15,0xfd,0x0f,0x44,0x20,0x61,0x2d,0xee,0x1d,0xc0,0xe0,0xd7,0x60,0x52,0x36,0xef,0xbf,0x08,0x5d,0xbd,0x9f,0xc5,0xc6,0x0d,0xab,0x16,0xc0,
    0x3b,0x8f,0xae,0x00,0xbb,0x05,0x03,0xa1,0xe9,0xb1,0x6f,0x93,0x13,0xfc,0xb4,0x99,0x11,0x29,0xf6,0xc9,0xf0,0x3e,0xd1,0x37,0xea,0xdb,0xb8,0x14,0x72,0x21,0x3e,0x15,
    0x90,0xc3,0x7d,0xe3,0xa6,0xf5,0xc5,0x01,0x21,0xdb,0x20,0xe2,0xe6,0x14,0xeb,0x51,0x2c,0x28,0x17,0xd2,0xb0,0xf2,0x26,0xa1,0xfa,0x24,0xd6,0xea,0xaa,0x22,0xf7,0xaa,
    0x2c,0x06,0x38,0xc9,0xd7,0x56,0xdf,0x53,0xf6,0x16,0x34,0x89,0x77,0xc2,0xff,0x25,0x95,0x8c,0x73,0x17,0xab,0x15,0xe2,0x5e,0x2a,0x7f,0xcf,0x55,0xfc,0xd1,0x8a,0x43,
    0xa6,0xac,0x88,0xc1,0x65,0x07,0x48,0xe8,0xa2,0x81,0x18,0xdf,0x17,0xb0,0x6a,0x88,0x10,0xff,0xd4,0x7d,0x98,0x76,0x31,0x84,0x1e,0x07,0xc1,0x59,0x29,0xca,0xf7,0x73,
    0x19,0x7b,0x32,0xc5,0xa3,0x2c,0x2d,0xa8,0xd7,0x41,0x94,0x2a,0x45,0x58,0x50,0x72,0xc0,0x56,0x5c,0x8e,0x5b,0x96,0x1d,0xe0,0xfe,0x41,0xd7,0x3c,0xf9,0xd1,0x35,0xf8,
    0x5c,0x99,0x04,0x9b,0xe9,0x7a,0x5f,0x5a,0x56,0x2a,0x20,0xa9,0x62,0x11,0x07,0xc8,0xe0,0x84,0x58,0xba,0x28,0x06,0x0c,0x4f,0x16,0xd5,0xd2,0x26,0x30,0x69,0xd3,0x92,
    0x97,0xfe,0x33,0x02,0xdb,0x0f,0x5e,0x9a,0xb5,0xff,0x5b,0x55,0xe5,0xb0,0x6b,0xa3,0x13,0xd6,0xdd,0x3a,0xdf,0x8c,0xfa,0xf3,0x12,0xc1,0x59,0x2e,0xc4,0xef,0x8f,0x7c,
    0x32,0x0b,0xce,0x0c,0x50,0x74,0x5c,0xc4,0xb2,0xab,0x16,0x6f,0xd7,0x90,0x26,0x1c,0x2e,0x7f,0xbf,0x30,0x06,0xf5,0xaf,0xf4,0x99,0x4b,0x16,0x4b,0x4a,0xce,0x48,0xa2,
    0x6e,0x96,0x03,0xd0,0x53,0x8a,0x88,0x92,0x73,0x43,0x51,0x08,0x1e,0x1f,0xce,0x38,0x6f,0xe7,0xe1,0xa1,0xac,0x97,0x78,0x7c,0xf6,0x3c,0x01,0x62,0xe6,0x23,0x90,0x5f,
    0x07,0x1c,0x91,0xc9,0x1b,0x97,0x7d,0xa7,0x47,0x74,0xcd,0x3b,0xf2,0xe1,0x38,0x1c,0xfd,0x30,0x0b,0x2b,0x62,0xec,0x64,0x3c,0x51,0x44,0x98,0x30,0x7b,0xb3,0x46,0xcc,
    0xb8,0xed,0xe8,0xb0,0x10,0x40,0x07,0x42,0x63,0xba,0xe7,0x1e,0xea,0xc9,0xe2,0xa1,0xa0,0x74,0x4c,0xa9,0xc5,0x3e,0xb6,0x73,0xe9,0xfd,0xba,0xa0,0x5a,0xe9,0x6a,0x54,
    0xd6,0x3d,0x77,0x70,0x6c,0xd2,0xf8,0x05,0x64,0xb9,0x4b,0x01,0xd4,0x5f,0x88,0x35,0xc0,0xa6,0xdb,0xf4,0x83,0x5e,0xbf,0xc2,0xf8,0x4a,0x86,0xed,0xe1,0x43,0x8a,0x8b,
    0x5d,0xb0,0xb3,0xb5,0x21,0x6a,0xa9,0x6f,0xac,0x5d,0xa4,0x7b,0xee,0xc3,0x77,0x2c,0x32,0xaa,0xbe,0x23,0x51,0xac,0xca,0x70,0xd6,0xae,0x2c,0xda,0x27,0x30,0x0f,0xfa,
    0x6e,0xcd,0x42,0xb2,0x8c,0xcc,0x56,0x14,0x68,0x58,0x06,0xf3,0x1d,0x06,0x13,0x3d,0x59,0x3e,0x90,0x00,0x84,0xec,0x94,0x88,0x1b,0xe6,0xdd,0xc3,0x74,0x00,0xac,0x48,
    0x14,0xf7,0x6d,0xad,0x48,0xeb,0x6a,0xcf,0xd4,0x7d,0x1e,0x3c,0xeb,0x0d,0x16,0xce,0x8c,0xf5,0x61,0x7b,0x64,0xbf,0xf2,0xaf,0x03,0x4a,0xc7,0x8e,0x41,0x74,0xa1,0xb0,
    0x22,0x51,0x6b,0x65,0xe3,0x2e,0x98,0xdc,0xa7,0xc9,0xb6,0xfc,0x50,0x9f,0x5b,0xe3,0x2c,0x85,0x7f,0x36,0xdd,0x06,0x7f,0x09,0x8f,0x41,0xc7,0xb9,0xab,0xb1,0x25,0xbd,
    0x7c,0x1e,0xc6,0xb2,0xe8,0xc7,0xd7,0x2e,0x86,0xaf,0x2c,0x2f,0xcb,0xa6,0x01,0x14,0x9c,0x59,0xaf,0x72,0x68,0x1e,0x77,0x27,0x8d,0x1e,0xb0,0x23,0xb8,0xaa,0xcb,0x11,
    0x6e,0x55,0x7a,0x6b,0x35,0x7e,0x1e,0x4f,0x11,0x14,0x40,0x16,0xf5,0xbb,0xdf,0xc3,0x40,0xa8,0x6e,0x36,0x20,0x6f,0xf5,0x53,0x34,0x7c,0xce,0x4e,0x38,0x5a,0x55,0x77,
    0x72,0x0a,0xa0,0xb6,0x42,0xad,0x04,0x93,0x43,0x12,0x5a,0x08,0x4b,0xb0,0x15,0xf7,0x84,0x0e,0x6a,0x00,0x5a,0x5d,0x01,0x30,0xcf,0x96,0x7c,0x48,0xc9,0xba,0xdb,0x0c,
    0x64,0xdf,0x5a,0x40,0x93,0xdd,0x56,0x80,0xbc,0x4c,0xd6,0xf3,0xc4,0xec,0x96,0x90,0x12,0x9b,0x5a,0xdc,0xbf,0xa7,0xa2,0xa3,0xc9,0xd6,0xc6,0x16,0x41,0x32,0x02,0xca,
    0x67,0x5d,0x43,0x6e,0x10,0x98,0xbc,0x06,0x83,0x91,0xc1,0x34,0x80,0x5c,0xeb,0x80,0x49,0xff,0xca,0x55,0xe2,0xa7,0xee,0x21,0x25,0xbf,0xa1,0xb0,0xbe,0x9f,0x84,0x4d,
    0x18,0xe8,0x7e,0x4d,0xbb,0xa9,0xa8,0x5e,0xe1,0x3c,0xee,0x3f,0xd0,0x9e,0xc6,0xb9,0xd5,0x97,0xc7,0xdf,0x12,0x9c,0x27,0xd3,0xbf,0x17,0x4b,0x64,0x48,0xbb,0xe7,0x16,
    0x1b,0xca,0x01,0x03,0xdd,0x3f,0xc9,0xfc,0xe2,0x50,0x99,0x1f,0x8c,0x9c,0xa5,0x44,0x69,0xe7,0x9c,0x01,0x24,0xe1,0x0f,0xc8,0x85,0xd2,0xb6,0x7b,0x34,0x73,0x70,0xe6,
    0x75,0xf3,0x42,0xed,0xfe,0xa2,0x86,0xc3,0xf0,0x7b,0x18,0xec,0x7e,0x43,0xdf,0xab,0xe7,0x59,0xb5,0x08,0xdb,0x4e,0x5c,0x07,0x8a,0xd7,0xb5,0xf0,0xe7,0x7a,0xe0,0xa4,
    0xb4,0xa0,0xa9,0xe4,0xf1,0x1b,0x74,0xf8,0xad,0xb9,0x6a,0x39,0xe9,0x2d,0x88,0x93,0x0b,0x84,0x3b,0xe9,0xb3,0xb1,0xa8,0x02,0x72,0x09,0x7c,0xd5,0x08,0x1c,0x2e,0xff,
    0x03,0x00,0x00,0x00,0x45,0xc8,0x20,0xcc,0x5b,0xb3,0x8d,0x3e,0x5f,0x87,0xc2,0x96,0x17,0x61,0x6f,0xe8,0x49,0x44,0x60,0x46,0x4f,0x94,0x32,0x4d,0xc0,0x40,0x77,0x07,
    0x01,0xda,0x42,0xaa,0x07,0x0a,0x10,0xcc,0x10,0xfd,0x82,0x26,0x0c,0x84,0x7e,0x9c,0x0d,0x1c,0x5d,0x80,0xe2,0x90,0xc2,0x9d,0xc0,0x58,0x3d,0x0d,0xdf,0x2e,0x20,0x38,
    0x2f,0x43,0x74,0x72,0x0d,0x3d,0xe0,0xd7,0xcc,0x28,0x41,0xa3,0x27,0x30,0x4f,0x75,0x2b,0x20,0x3e,0x9b,0xc7,0x60,0xde,0xa3,0xc7,0xee,0xc3,0x3d,0xa1,0x83,0x22,0x94,
    0x61,0x36,0x27,0x31,0x8f,0xab,0x96,0x8c,0x6f,0x17,0x9d,0xdd,0x98,0x37,0x58,0x18,0xeb,0x23,0x30,0x41,0xdc,0x1b,0x7e,0x41,0xa4,0x5a,0x34,0x75,0x4f,0x2d,0xae,0x75,
    0x04,0x6a,0x07,0x79,0x77,0x53,0xee,0xc0,0xc0,0x1f,0x81,0xc9,0x7f,0xc6,0xf6,0xc8,0x7e,0x2b,0xd4,0xa3,0x90,0x7b,0xf4,0xae,0xae,0x00,0x70,0x6c,0x03,0x86,0x0f,0x92,
    0xc6,0x07,0x2c,0xc4,0x31,0x4e,0x96,0x72,0x12,0x50,0x3a,0x06,0xb2,0xdc,0x85,0xed,0xe1,0x43,0x56,0xf3,0x47,0xe1,0x0f,0xd4,0x7f,0xcb,0x02,0x88,0x96,0x6f,0x20,0x58,
    0x77,0x93,0xc9,0xd7,0xd2,0x13,0xae,0xa2,0xdd,0xb7,0xd0,0x56,0x49,0xe3,0x32,0xba,0x22,0x66,0x86,0x00,0x97,0x69,0x56,0x0f,0x7d,0xf7,0x02,0xb2,0x08,0xed,0x3b,0x4f,
    0x96,0xfa,0x5f,0x0a,0xb8,0xa7,0xed,0x36,0xd0,0xff,0x01,0xe8,0x44,0x02,0xe2,
};


#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER ALIGN(4)
#endif
LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER const eZIP_RGBARGB888A  lv_img_dsc_t sys_set_peripheral_sensors SECTION(".ROM3_IMG_EZIP_HEADER.sys_set_peripheral_sensors") = { 
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .header.always_zero = 0,
  .header.w = 72,
  .header.h = 72,
  .data_size  = 2095,
  .data = sys_set_peripheral_sensors_map
};
