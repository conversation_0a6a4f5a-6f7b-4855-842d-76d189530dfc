# This is a vpython "spec" file.
#
# Read more about `vpython` and how to modify this file here:
#   https://chromium.googlesource.com/infra/infra/+/main/doc/users/vpython.md
# List of available wheels:
#   https://chromium.googlesource.com/infra/infra/+/main/infra/tools/dockerbuild/wheels.md

python_version: "3.8"

wheel: <
  name: "infra/python/wheels/pytest-py3"
  version: "version:8.3.4"
>

# Required by pytest==8.3.4
wheel: <
  name: "infra/python/wheels/py-py2_py3"
  version: "version:1.11.0"
>

# Required by pytest==8.3.4
wheel: <
  name: "infra/python/wheels/iniconfig-py3"
  version: "version:1.1.1"
>

# Required by pytest==8.3.4
wheel: <
  name: "infra/python/wheels/packaging-py3"
  version: "version:23.0"
>

# Required by pytest==8.3.4
wheel: <
  name: "infra/python/wheels/pluggy-py3"
  version: "version:1.5.0"
>

# Required by pytest==8.3.4
wheel: <
  name: "infra/python/wheels/toml-py3"
  version: "version:0.10.1"
>

# Required by pytest==8.3.4
wheel: <
  name: "infra/python/wheels/tomli-py3"
  version: "version:2.1.0"
>

# Required by pytest==8.3.4
wheel: <
  name: "infra/python/wheels/pyparsing-py3"
  version: "version:3.0.7"
>

# Required by pytest==8.3.4
wheel: <
  name: "infra/python/wheels/attrs-py2_py3"
  version: "version:21.4.0"
>

# Required by pytest==8.3.4
wheel: <
  name: "infra/python/wheels/exceptiongroup-py3"
  version: "version:1.1.2"
>
