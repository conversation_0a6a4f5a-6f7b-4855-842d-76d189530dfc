/************************************************************************
* 
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   train_plan_pb.c
@Time    :   2025/03/25 15:29:28
<AUTHOR>   lxin
* 
**************************************************************************/
#include "train_plan_pb.h"
#include "train_plan.pb.h"
#include "crc8.h"
#include "ble_cmd_response.h"
#include "ble_cmd_common.h"
#include "ble_nus_srv.h"
#include "pb.h"
#include "pb_encode.h"
#include "pb_decode.h"
#include "pb_decode_common.h"
#include "pb_encode_common.h"
#include "qw_mlist.h"
#include "qw_fs_api.h"
#include "app_timer.h"
#include "igs_dev_config.h"
#include "ble_data_inf.h"
#include "system_utils.h"
// #include "segments_port.h"
#include "thread_pool.h"
#include "data_convert.h"
#include "ble_interflow_single.h"
#include "file_upload_pb_encode.h"
#include "ff.h"
#include <stdint.h>

static uint8_t open_flag = false;              //文件打开状态
static uint8_t trans_cmp = false;              //文件传输完成状态
static uint16_t read_file_len = 0;
static FIL *fdst;
static char schedule_name[MAX_SCHEDULE_NAME_LENGTH+1] = {0};


static qw_mlist_t mlist_file;
static void send_schedules_file_number()
{
    uint8_t pb_crc = 0;
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    schedule_data_msg schedule_message;
    qw_mlist_init(&mlist_file);
    memset (&schedule_message, 0, sizeof(schedule_data_msg));
    ble_data_var_tx_get(&data, &length, BLE_NUS_CH3_UUID);

    //参数赋值
    schedule_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_TRAINING_PLAN;
    schedule_message.schedule_date_operate_type = TRAINING_PLAN_OPERATE_TYPE_enum_TRAINING_PLAN_OPERATE_TYPE_GET_SCHEDULE_NUM;
    schedule_message.has_file_number = true;
    schedule_message.file_number = fs_scan_files(&mlist_file, g_fileDirName[enum_mian_disk_schedule]/*SCHEDULE_DIR*/, ".fit", 0, 0, 0, 0);
    qw_mlist_uninit(&mlist_file);
    //编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX);
    //编码
    pb_encode(&encode_stream, schedule_data_msg_fields, &schedule_message);

    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch3();
        
    //对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);

    ble_cmd_end_tx(schedule_message.service_type, 0, schedule_message.schedule_date_operate_type, 0, encode_stream.bytes_written, pb_crc);
}

//计划完成通知APP获取状态
void schedule_course_done_info(uint32_t course_id)
{
    uint8_t ble_data[DATA_LENGTH_CH1] = {0};
    ble_status_cmd_st *ble_status_cmd_s = (ble_status_cmd_st *)ble_data;
		
    memset(ble_data, 0xff, DATA_LENGTH_CH1);

    ble_status_cmd_s->cmd_type = enum_BLE_NOTICE_CMD;
    ble_status_cmd_s->service_type = service_type_index_enum_SERVICE_TYPE_INDEX_TRAINING_PLAN;
    ble_status_cmd_s->sub_service_type = 0;
    
    ble_status_cmd_s->op_type = TRAINING_PLAN_OPERATE_TYPE_enum_TRAINING_PLAN_OPERATE_TYPE_UPDATE_SCHEDULE_STATE_INFO;

    ble_status_cmd_s->sub_op_type = 0;
    ble_status_cmd_s->status = enumSUCCESS_STATUS;
    //预留字节的前四位用来保存完成的课程ID
    ble_status_cmd_s->reserved3[3] = (uint8_t)(0xff & course_id);
    ble_status_cmd_s->reserved3[4] = (uint8_t)(0xff & (course_id >> 8));
    ble_status_cmd_s->reserved3[5] = (uint8_t)(0xff & (course_id >> 16));
    ble_status_cmd_s->reserved3[6] = (uint8_t)(0xff & (course_id >> 24));

    ble_status_cmd_s->crc8 = CRC_Calc8_Table(ble_data, DATA_LENGTH_CH1 - 1);

    ble_nus_data_tx_protocol_header(ble_data);
}




void schedule_data_decode(uint8_t* pb_buffer, uint16_t buffer_length, END_TYPE end_type)
{
    uint8_t status = false;
    schedule_data_msg schedule_message;
    memset(&schedule_message, 0x00, sizeof(schedule_data_msg));
    schedule_message.has_file_number = true;

    memset(schedule_name, 0x00, sizeof(schedule_name));
    schedule_message.schedule_name.arg = schedule_name;
    schedule_message.schedule_name.funcs.decode = &decode_string;

    pb_istream_t decode_stream = pb_istream_from_buffer(pb_buffer, buffer_length);
    status = pb_decode(&decode_stream, schedule_data_msg_fields, &schedule_message);
    if (status)
    {
        switch (schedule_message.schedule_date_operate_type)
        {
        case TRAINING_PLAN_OPERATE_TYPE_enum_TRAINING_PLAN_OPERATE_TYPE_NONE:   
            break;
        case TRAINING_PLAN_OPERATE_TYPE_enum_TRAINING_PLAN_OPERATE_TYPE_SYNC_TRAINING_FILE_START:
            if (strlen(schedule_name) > 0)
            {
                set_schedule_course_name((const char*)schedule_name);
            }
            else
            {
                set_schedule_course_name("_training_plan");//默认训练计划名称
            }
            ble_cmd_success_status_tx(schedule_message.service_type, 0, schedule_message.schedule_date_operate_type, 0);
            break;
        case TRAINING_PLAN_OPERATE_TYPE_enum_TRAINING_PLAN_OPERATE_TYPE_SYNC_TRAINING_FILE_END:
            schedule_courses_file_manage();
            ble_cmd_success_status_tx(schedule_message.service_type, 0, schedule_message.schedule_date_operate_type, 0);
            break;
        case TRAINING_PLAN_OPERATE_TYPE_enum_TRAINING_PLAN_OPERATE_TYPE_GET_SCHEDULE_NUM:
            send_schedules_file_number();
            break;
        case TRAINING_PLAN_OPERATE_TYPE_enum_TRAINING_PLAN_OPERATE_TYPE_GET_SCHEDULE_FILE:
            break;
        default:
            ble_cmd_err_status_tx(schedule_message.service_type, 0, schedule_message.schedule_date_operate_type, 0);
            break;
        }
    }
}


void schedule_status_handle(uint8_t* buf)
{
    // do nothing
}



//文件上传处理
uint32_t schedule_data_file_size_get(uint32_t* sum32)
{
	FIL fdst_temp;
    char fnamestr[70] = { 0 };
    sprintf(fnamestr, "%s/%s", g_fileDirName[enum_mian_disk_schedule]/*SCHEDULE_DIR*/, "Schedules.fit");
    if (FR_OK == f_open(&fdst_temp, fnamestr, FA_READ)){
    	uint32_t schedule_file_obj_size = fdst_temp.obj.objsize;
#ifdef USING_FILE_VERIFY
        #define  BUF_READ_SIZE  2048
        unsigned int br = 0;
        uint8_t *buff = rt_malloc(BUF_READ_SIZE);
        while(1){
            if(FR_OK == f_read(&fdst_temp, buff, BUF_READ_SIZE, &br)){
                if (0 == br){
                    break;
                }else{
                    for(uint16_t i = 0;i < br;i++){
                        *sum32 += buff[i];
                    }
                }
            }else{
                break;
            }
        }
#endif
    	f_close(&fdst_temp);
        rt_free(buff);
        return schedule_file_obj_size;
    }
	return 0;
}

uint32_t schedule_file_date_size = 0;
static bool schedule_data_read_file_direct(uint8_t *buf)
{
#define FILE_READ_512_SIZE		1536   // (8*244)
#define FILE_REMIND_SIZE		416   // (8*244 - 3*512)
    char fnamestr[100] = {0};
    unsigned int tt = 0;
    memset(fnamestr, 0 ,100);

    if (NULL == fdst){
        fdst = QW_FIL_FD_MALLOC(sizeof(FIL));
        if (NULL == fdst) {
            return false;
        }
    }

    if (false == open_flag){
        sprintf(fnamestr, "%s/%s", g_fileDirName[enum_mian_disk_schedule]/*SCHEDULE_DIR*/, "Schedules.fit");
        if (FR_OK == f_open(fdst, fnamestr, FA_READ)) {
            open_flag = true;
            schedule_file_date_size = 0;
        }
    }

    if (true == open_flag){
        if(!f_eof(fdst)){
        	f_read(fdst, buf + read_file_len, FILE_READ_512_SIZE, &tt);
            read_file_len += tt;
            schedule_file_date_size += tt;
            if ((tt < FILE_READ_512_SIZE) || (f_size(fdst) == schedule_file_date_size)){
                f_close(fdst);
                QW_FIL_FD_FREE(fdst);
                fdst = NULL;
                open_flag = false;
                trans_cmp = true;
            }else{
            	f_read(fdst, buf + read_file_len, FILE_REMIND_SIZE, &tt);
            	read_file_len += tt;
            	schedule_file_date_size += tt;
            	if ((tt < FILE_REMIND_SIZE) || (f_size(fdst) == schedule_file_date_size)){
                    f_close(fdst);
                    QW_FIL_FD_FREE(fdst);
                    fdst = NULL;
                    open_flag = false;
                    trans_cmp = true;
            	}
            }
        }else{
            f_close(fdst);
            QW_FIL_FD_FREE(fdst);
            fdst = NULL;
            open_flag = false;
            trans_cmp = true;
            schedule_file_date_size = 0;
        }
    }
    return true;
}



static bool schedule_data_file_send_raw(bool isStart)
{
	uint8_t *data = NULL;
	uint16_t *length = NULL;
	bool ret = false;
    uint32_t sum32 = 0;
    ble_data_var_tx_get_and_ret(&data, &length, BLE_NUS_CH3_UUID);
    read_file_len = 0;
	if(isStart){
		uint16_t encoded_length = 0;
        uint32_t file_size = schedule_data_file_size_get(&sum32);   
        if (file_size) {
			encoded_length = file_upload_pb_encode(&data[PB_LENGTH_FIELD_SIZE],ONE_FRAME_DATA_LENGTH_MAX_CH2_TX - PB_LENGTH_FIELD_SIZE,\
                    file_size, 0, 0, NULL, sum32);
            if (encoded_length) {

				data[0] = (encoded_length>>24)&0xff;
				data[1] = (encoded_length>>16)&0xff;
				data[2] = (encoded_length>>8)&0xff;
				data[3] = (encoded_length)&0xff;

				*length = PB_LENGTH_FIELD_SIZE + encoded_length;
                ret = schedule_data_read_file_direct(data + *length);
                *length += read_file_len;
			}
		}
	}else{
        ret = schedule_data_read_file_direct(data);   
        if (ret) {
            *length = read_file_len;    
        }
	}
	if(ret){
		ble_nus_data_tx_ch3();
		if(isStart){
			ble_cmd_end_data_stream_type_tx(service_type_index_enum_SERVICE_TYPE_INDEX_TRAINING_PLAN, DATA_STREM_UPLOAD_MODE, \
					0, TRAINING_PLAN_OPERATE_TYPE_enum_TRAINING_PLAN_OPERATE_TYPE_GET_SCHEDULE_FILE, 0, *length, enumFILE_END, 0);
		}
	}else{
        ble_cmd_err_status_tx(service_type_index_enum_SERVICE_TYPE_INDEX_TRAINING_PLAN, 0, TRAINING_PLAN_OPERATE_TYPE_enum_TRAINING_PLAN_OPERATE_TYPE_GET_SCHEDULE_FILE, 0);
    }
	return ret;
}



static bool transfor_start = true;
uint8_t schedule_file_transfer(uint8_t service_type,uint8_t op_type)
{
    if (transfor_start && ble_tx_channel_is_free(BLE_NUS_CH3_UUID))
    {
        if (!schedule_data_file_send_raw(transfor_start))
        {
            trans_cmp = false;
            transfor_start = true;
        	return true;
		}else{
            transfor_start = false;
            if (trans_cmp) {
	        	trans_cmp = false;
	        	transfor_start = true;
	        	return true;
	    	}
		}
    }
    else
    {
        if (open_flag && ble_tx_channel_is_free(BLE_NUS_CH3_UUID))
        {
            if (false == trans_cmp)
            {
                if (schedule_data_file_send_raw(transfor_start))
                {
                    if (trans_cmp)
                    {
                        trans_cmp = false;
			        	transfor_start = true;
			        	return true;
		        	}
                }
                else
                {
                    trans_cmp = false;
		        	transfor_start = true;
		        	return true;
	        	}
	        }
		}
	}
	return false;
}

void schedule_file_store_abort(uint8_t service_type, uint8_t op_type)
{
	if(open_flag){
		transfor_start = true;
        f_close(fdst);
        QW_FIL_FD_FREE(fdst);
        fdst = NULL;
        open_flag = false;
        read_file_len = 0;
        trans_cmp = true;
        schedule_file_date_size = 0;
	}
}
 