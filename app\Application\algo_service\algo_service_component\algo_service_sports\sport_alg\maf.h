/************************************************************************
*
* Copyright(c) 2025, igpsport Software Co., Ltd.
* All Rights Reserved.
* @File    :   maf.h
*
**************************************************************************/
#ifndef MAF_H
#define MAF_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

//作用于单精度浮点数的滑动平均滤波器
typedef struct _maf_f32
{
    float *buf;                     //缓冲区，用于保存历史输入
    uint32_t capacity;              //缓冲区容量
    uint32_t len;                   //缓冲区中数据数量
    float sum;                      //用于求和缓冲区中数据的变量
    uint32_t next;                  //缓冲区中下一次要填入数据的位置
} maf_f32_t;

//作用于有符号整数的滑动平均滤波器
typedef struct _maf_i32
{
    int32_t *buf;                   //缓冲区，用于保存历史输入
    uint32_t capacity;              //缓冲区容量（注意考虑溢出问题）
    uint32_t len;                   //缓冲区中数据数量
    int32_t sum;                    //用于求和缓冲区中数据的变量
    uint32_t next;                  //缓冲区中下一次要填入数据的位置
} maf_i32_t;

//作用于无符号整数的滑动平均滤波器
typedef struct _maf_u32
{
    uint32_t *buf;                  //缓冲区，用于保存历史输入
    uint32_t capacity;              //缓冲区容量（注意考虑溢出问题）
    uint32_t len;                   //缓冲区中数据数量
    uint32_t sum;                   //用于求和缓冲区中数据的变量
    uint32_t next;                  //缓冲区中下一次要填入数据的位置
} maf_u32_t;

float maf_f32_exec(maf_f32_t *self, float x);

void maf_f32_reset(maf_f32_t *self);

int32_t maf_i32_exec(maf_i32_t *self, int32_t x);

void maf_i32_reset(maf_i32_t *self);

uint32_t maf_u32_exec(maf_u32_t *self, uint32_t x);

void maf_u32_reset(maf_u32_t *self);

#ifdef __cplusplus
}
#endif

#endif