/*************************************************************
 * @file algo_service_component_common.h
 * <AUTHOR> (chen<PERSON><PERSON>@igpsport.com)
 * @brief 算法组件通用模板
 * @version 0.1
 * @date 2024-11-11
 *
 * @copyright Copyright (c) 2024-2025, <PERSON>han Qiwu Technology Co., Ltd
 *
*************************************************************/
#ifndef __ALGO_SERVICE_COMPONENT_COMMON_H__
#define __ALGO_SERVICE_COMPONENT_COMMON_H__

#include "basictype.h"
#include "rtconfig.h"

#ifdef __cplusplus
extern "C" {
#endif

// 算法数据名称
#define CONFIG_QW_ALG_NAME_NULL             "null"
#define CONFIG_QW_ALG_NAME_TESE             "algo_test"
#define CONFIG_QW_ALG_NAME_HRM              "algo_hrm"
#define CONFIG_QW_ALG_NAME_SPO2             "algo_spo2"
#define CONFIG_QW_ALG_NAME_SLEEP            "algo_sleep_stage"
#define CONFIG_QW_ALG_NAME_STEP             "algo_step_count"
#define CONFIG_QW_ALG_NAME_REST_HR          "algo_rest_hr"
#define CONFIG_QW_ALG_NAME_ALLDAYSTAMINA    "algo_alldaystamina"
#define CONFIG_QW_ALG_NAME_STRESS_SCORE     "algo_stress_score"
#define CONFIG_QW_ALG_NAME_HRV_EVALUATE     "algo_hrv_evaluate"
#define CONFIG_QW_ALG_NAME_FITNESS          "algo_fitness"
#define CONFIG_QW_ALG_NAME_ACTIVITY         "algo_activity_intense"
#define CONFIG_QW_ALG_NAME_CALORIES         "algo_calories"
#define CONFIG_QW_ALG_NAME_DAILY_ACCUM      "algo_daily_accum"
#define CONFIG_QW_ALG_NAME_DAILY_INCREASE   "algo_daily_increase"
#define CONFIG_QW_ALG_NAME_EVENT_TIMER      "algo_event_timer"
#define CONFIG_QW_ALG_NAME_RAISE_WRIST      "algo_raise_wrist"
#define CONFIG_QW_ALG_NAME_POSITION         "algo_position"
#define CONFIG_QW_ALG_NAME_SPORTS_CALORIES  "algo_sports_calories"
#define CONFIG_QW_ALG_NAME_AUTO_SPORTS_CTRL "algo_auto_sports_ctrl"
#define CONFIG_QW_ALG_NAME_RUNNING_DYNAMICS "algo_running_dynamics"
#define CONFIG_QW_ALG_NAME_RD_AVG           "algo_rd_avg"
#define CONFIG_QW_ALG_NAME_COUNT_TIMES      "algo_count_times"
#define CONFIG_QW_ALG_NAME_CHART_ALT        "algo_chart_alt"
#define CONFIG_QW_ALG_NAME_STRESS           "algo_stress"
#define CONFIG_QW_ALG_NAME_HRV              "algo_hrv"
#define CONFIG_QW_ALG_NAME_BUNNY            "algo_bunny"
#define CONFIG_QW_ALG_NAME_RHR              "algo_rhr"
#define CONFIG_QW_ALG_NAME_MOVE_STATUS      "algo_move_status"
#define CONFIG_QW_ALG_NAME_POOL_SWIM        "algo_pool_swim"
#define CONFIG_QW_ALG_NAME_TL_TREND         "algo_tl_trend"

// 事件数据名称 宏定义保持 CONFIG_QW_EVENT_NAME_XXX
#define CONFIG_QW_EVENT_NAME_ONE_MINUTE            "algo_one_min_reached"   // 此项目前只提供给运动报告使用
#define CONFIG_QW_EVENT_NAME_REMIND                "algo_remind_reached"
#define CONFIG_QW_EVENT_NAME_DEVELOPER             "developer_event"
#define CONFIG_QW_EVENT_NAME_WAKEUP                "algo_wakeup"
#define CONFIG_QW_EVENT_NAME_HR_MANUAL_MEASURE     "algo_click_hrm_reached"       // 心率点测
#define CONFIG_QW_EVENT_NAME_CLICK_ALT             "algo_click_alt_cal"           // 高度校准
#define CONFIG_QW_EVENT_NAME_HR_REMIND             "algo_hr_remind_reached"       // 心率提醒
#define CONFIG_QW_EVENT_NAME_ACTIVETYPE            "algo_activetype_chg"          // 用户运动状态改变事件
#define CONFIG_QW_EVENT_NAME_FITNESS_EVENT         "algo_fitness_event"           // fitness启动/暂停/恢复/结束事件
#define CONFIG_QW_EVENT_NAME_SPO2_MANUAL_MEASURE   "algo_click_spo2_reached"      // 血氧点测
#define CONFIG_QW_EVENT_NAME_STRESS_MANUAL_MEASURE "algo_click_stress_reached"    // 压力点测
#define CONFIG_QW_EVENT_NAME_STRESS_REMIND         "algo_stress_remind_reached"   // 压力提醒
#define CONFIG_QW_EVENT_NAME_SYNC_TIME             "algo_sync_time_remind"        // 同步时间提醒
#define CONFIG_QW_EVENT_NAME_BK_STATUS             "algo_backlight_status"        // 背光状态变更通知
#define CONFIG_QW_EVENT_NAME_LCPU_ENV_UPDATE       "algo_lcpu_env_update"         // LCPU环境变量更新
#define CONFIG_QW_EVENT_NAME_FITNESS_SAVE_END      "algo_fitness_save_end"        // 运动保存训练数据准备结束
#define CONFIG_QW_EVENT_NAME_CALORIES_DRV          "algo_calories_drv"
#define CONFIG_QW_EVENT_NAME_MOVE_STATUS_DRV       "algo_move_status_drv"
#define CONFIG_QW_EVENT_NAME_COUNTTIMES_DRV        "algo_count_times_drv"
#define CONFIG_QW_EVENT_NAME_RD_DRV                "algo_rd_drv"
#define CONFIG_QW_EVENT_NAME_NAVI                  "algo_navi"
#define CONFIG_QW_EVENT_NAME_POOL_SWIM_DRV         "algo_pool_swim_drv"
#define CONFIG_QW_EVENT_NAME_SPORTS_CTRL           "algo_sports_ctrl"        // 活动记录事件
#define CONFIG_QW_EVENT_NAME_MOVE_STATUS_CTRL      "algo_move_status_ctrl"   // 运动状态变更
#define CONFIG_QW_EVENT_NAME_GM_PREVIOUS_SAVE      "event_gm_previous_save"   // 日常每30min保存一次gm previous数据
#define CONFIG_QW_EVENT_NAME_MEDIA_CTRL            "event_media_ctrl"           // 音频控制事件

// 刷屏日志重采样
#define LOG_DEBUG_RESAMPLE 10

// 算法配置数据长度
#define ALGO_CONFIG_DATA_LEN 32

// 配置高性能算法任务处理抬腕算法
#define CONFIG_ALGO_TASK_HIGH_PERFORMANCE 1

// 算法类型
typedef enum {
    ALGO_TYPE_START,              // 算法起始类型
    ALGO_TYPE_TEST = ALGO_TYPE_START,
    ALGO_TYPE_HRM,                // 心率
    ALGO_TYPE_SPO2,               // 血氧饱和度
    ALGO_TYPE_SLEEP_STAGE,        // 睡眠分期
    ALGO_TYPE_STEP_COUNT,         // 计步器
    ALGO_TYPE_REST_HR,            // 静息心率
    ALGO_TYPE_ALLDAYSTAMINA,      // 全天体力
    ALGO_TYPE_STRESS_SCORE,       // 压力分数
    ALGO_TYPE_HRV_EVALUATE,       // 心率易变性评估
    ALGO_TYPE_FITNESS,            // 运动算法
    ALGO_TYPE_ACTIVITY_INTENSE,   // 活动强度
    ALGO_TYPE_CALORIES,           // 卡路里
    ALGO_TYPE_DAILY_ACCUM,        // 日常累计数据
    ALGO_TYPE_DAILY_INCREASE,     // 日常新增数据
    ALGO_TYPE_EVENT_TIMER,        // 事件定时器
    ALGO_TYPE_RAISE_WRIST,        // 抬腕亮屏
    ALGO_TYPE_GPS_DATA,           // gps数据
    ALGO_TYPE_ALTITUDE,           // 海拔高度
    ALGO_TYPE_ALTITUDE_AVG,       // 平均海拔
    ALGO_TYPE_ALTITUDE_DOT,       // 高度打点
    ALGO_TYPE_STRESS,             // 压力
    ALGO_TYPE_HRV,                // HRV
    ALGO_TYPE_RHR,                // 静息心率

    ALGO_TYPE_SPEED,              // 速度
    ALGO_TYPE_SPEED_MAX,          // 最大速度
    ALGO_TYPE_SPEED_AVG,          // 平均速度
    ALGO_TYPE_DISTANCE,           // 距离
    ALGO_TYPE_ODOMETER,           // 总里程
    ALGO_TYPE_HEART_RATE,         // 心率（优先级融合）
    ALGO_TYPE_HEART_RATE_AVG,     // 平均心率（及最大心率）
    ALGO_TYPE_CADENCE,            // 踏频
    ALGO_TYPE_CADENCE_AVG,        // 平均踏频
    ALGO_TYPE_POWER,              // 功率
    ALGO_TYPE_POWER_AVG,          // 平均功率
    ALGO_TYPE_GRADE,              // 坡度
    ALGO_TYPE_GRADE_AVG,          // 平均坡度
    ALGO_TYPE_POSITION,           // 坐标位置统计
    ALGO_TYPE_SPORTS_CALORIES,    // 骑行卡路里
    ALGO_TYPE_MOVE_STATUS,        // 运动状态
    ALGO_TYPE_POOL_SWIM,          // 泳池游泳

    ALGO_TYPE_RUNNING_DYNAMICS,   // 跑步动态
    ALGO_TYPE_RD_AVG,             // 平均跑步动态
    ALGO_TYPE_COUNT_TIMES,        // 跳绳动态
    ALGO_TYPE_CHART_ALT,          // 高度图表缓存数据更新

    ALGO_TYPE_TIMER,              // 计时
    ALGO_TYPE_LAP_COUNT,          // 圈数
    ALGO_TYPE_AUTO_SPORTS_CTRL,   // 自动运动控制（包含运动控制）
    ALGO_TYPE_BUNNY,              // 虚拟兔子
    ALGO_TYPE_TL_TREND,           // 负荷趋势
    ALGO_TYPE_END,                // 算法结束类型
} ALGO_TYPE;

// 算法执行的任务id
typedef enum {
    ALGO_TASK_NORMAL = 0,    // 正常性能算法任务
#if (CONFIG_ALGO_TASK_HIGH_PERFORMANCE == 1)
    ALGO_TASK_PERFORMANCE,   // 高性能算法任务
#endif
    ALGO_TASK_MAX,           // 最大任务id
} ALGO_TASK_ID;

// 算法配置类型
typedef enum {
    ALGO_CONFIG_OPEN,              // 算法打开时配置
    ALGO_CONFIG_SAMPLING,          // 算法采样率配置
    ALGO_CONFIG_CAILBRA,           // 算法校准配置
    ALGO_CONFIG_REBOOT_SET,        // 算法重启配置
    ALGO_CONFIG_HR_MODE,           // 心率测量模式配置
    ALGO_CONFIG_HR_REMIND,         // 心率高低提醒配置
    ALGO_CONFIG_SPO2_MODE,         // 血氧测量模式配置
    ALGO_CONFIG_RESET_USER_INFO,   // 重置用户信息
    ALGO_CONFIG_STRESS_MODE,       // 压力测量模式配置
    ALGO_CONFIG_SCORE_MODE_CHG,    // 压力测量模式改变
    ALGO_CONFIG_STRESS_REMIND,     // 压力提醒配置
    ALGO_CONFIG_SLEEP_MANUAL,      // 睡眠手动配置
} ALGO_CONFIG_TYPE;

/**
 * @brief  算法回调函数，在此函数中遍历已订阅的消费者
 */
typedef void (*callback_t)(const void *out, uint32_t len);

// 订阅可选配置
typedef struct optional_config
{
    float sampling_rate;   // 订阅数据采样率:如果采样率为0表示不可配置采样率,例如增量数据,否则进行重采样
    uint32_t batch_time;   // 订阅数据批量上报间隔时间:单位ms
    uint32_t value;        // 其他可选配置
} optional_config_t;

// 算法配置
typedef struct
{
    uint32_t type;                        // 算法配置输入类型
    uint8_t args[ALGO_CONFIG_DATA_LEN];   // 算法配置输入
} algo_config_t;

// 算法组件实现
typedef struct
{
    uint32_t task_id;                                                 // 执行的任务id
    int32_t (*init)(void);                                            // 系统初始化需要做的算法初始化
    int32_t (*open)(void);                                            // 打开算法以及订阅算法需要的输入数据
    int32_t (*feed)(uint32_t input_type, void *data, uint32_t len);   // 算法喂数据
    int32_t (*close)(void);                                           // 关闭算法以及取消订阅算法需要的输入数据
    int32_t (*ioctl)(algo_config_t *config);                          // 配置操作
} algo_compent_ops_t;

// algo_service集成订阅配置
typedef struct _algo_topic_node_t
{
    optional_config_t optional_config;   // 订阅可选配置
    const char *topic_name;              // 话题名称
    uint32_t topic_id;                   // 话题id
    callback_t callback;                 // 回调函数
} algo_topic_node_t;

#define DEFAULT_OPTIONAL_CONFIG \
    {                           \
        .sampling_rate = 0.0f,  \
        .batch_time = 0,        \
        .value = 0,             \
    }   // 默认可选配置

/**
 * @brief 注册算法组件到算法列表
 *
 * @param type 算法类型
 * @param compent 组件
 * @return int32_t 0 ： 成功 -1 ： 失败
 */
int32_t algo_compnent_register(ALGO_TYPE type, algo_compent_ops_t *compent);

/**
 * @brief 批量订阅
 *
 * @param topic_list 订阅列表
 * @param topic_num 个数
 * @return int32_t 0 ： 成功 -1 ： 失败
 */
int32_t algo_topic_list_subscribe(algo_topic_node_t *topic_list, uint32_t topic_num);

/**
 * @brief 批量取消订阅
 *
 * @param topic_list 订阅列表
 * @param topic_num 个数
 * @return int32_t 0 ： 成功 -1 ： 失败
 */
void algo_topic_list_unsubscribe(algo_topic_node_t *topic_list, uint32_t topic_num);

#ifdef __cplusplus
}
#endif

#endif   //__ALGO_SERVICE_COMPONENT_COMMON_H__
