/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   alg_auto_light.c
@Time    :   2025/01/21 14:53:22
*
**************************************************************************/

#include <math.h>
#include "alg_auto_light.h"
#include "backlight_module.h"
#include "qw_time_util.h"
#include "qw_timer.h"
#include "view_page_model.h"
#ifndef SIMULATOR
#include "qw_sensor.h"
#include "qw_sensor_common_config.h"
#include "qw_system_params.h"
#include "subscribe_service.h"
#endif

/*-------------------一些宏定义----------------------------------------------------------*/
#define ADJUST_STEP_TIME   30     //帧时长 单步调整时间间隔ms
#define LUX_GET_TIME       1000   //获取光感强度时间间隔1s
#define BRIGHTNESS_LEVELS  21     //亮度等级数
#define HBM_PCT_CORRECTION 13     //亮度百分比补正
#define ADJUST_LEVEL_NUM   40
#define MEDIAN_ARRY_SIZE   5      // 窗口长度

/*-------------------一些结构体定义----------------------------------------------------------*/

enum brightness_calc_state_e {
    BRIGHTNESS_CALC_INIT,        //亮度计算初始状态
    BRIGHTNESS_CALC_UPDATED,     //亮度已计算更新
    BRIGHTNESS_CALC_UNCHANGED,   //亮度计算未更新
};

struct auto_light_context_t
{
    bool is_enabled;                            //是否start
    bool is_adjusting;                          //是否在调整
    float dispaly_brightness;                   //当前的屏幕亮度
    uint8_t hbm_pct;                            //当前的屏幕hbm模式下百分比
    float light_intensity;                      //当前的屏幕亮度对应的光强
    uint8_t light_level;                        //当前的光强对应的等级,用来判断是否触发调整器
    enum brightness_calc_state_e calc_status;   //算法值计算状态
    AUTO_LIGHT_STOP_REASON_T stop_reason;       //关闭原因
    AUTO_LIGHT_ADJUST_MODE adjust_mode;         //调节模式 保护调节时，超过800nit60s会恢复降低为600nit
    bool is_protect_timer_active;
};

typedef struct
{
    uint8_t light_value;   //亮度百分比
    uint16_t light_nit;    //亮度值
} ThresholdLevel;

typedef struct
{
    uint8_t sum_step;
    float dx;
    float old;
    float new;
} timer_user_param_t;

/*-------------------一些全局静态变量----------------------------------------------------------*/
static qw_timer protect_timer_id = {0};                       //过亮保护定时器
static bool protect_timer_init_flag = false;                  //过亮保护定时器初始化标记
static qw_timer brightness_timer_id = {0};                    //亮度调整定时器
static bool timer_init_flag = false;                          //亮度调整定时器初始化标记

static bool timer_adjust_light_state = false;                 //亮度调整定时器工作中标记
static uint8_t timer_current_step = 1;                        //定时器调整时 当前的步骤数

uint32_t median_filter_raw[MEDIAN_ARRY_SIZE] = {0};           // 原始环形缓冲区（FIFO）
uint32_t median_filter_sorted_arry[MEDIAN_ARRY_SIZE] = {0};   // 始终有序的副本
uint8_t median_filter_head = 0;                               // 指向最旧的元素
uint8_t median_filter_arry_cnt = 0;                           // 到目前为止收到的样本数（1..）

static ThresholdLevel thresholds[BRIGHTNESS_LEVELS] = {{0, 10},   {5, 20},   {10, 35},  {15, 50},  {20, 70},  {25, 90},  {30, 110},
                                                       {35, 130}, {40, 155}, {45, 180}, {50, 205}, {55, 235}, {60, 270}, {65, 310},
                                                       {70, 355}, {75, 405}, {80, 460}, {85, 520}, {90, 582}, {95, 685}, {100, 800}};
static uint16_t adjust_level_arr[] = {
    5,     55,    104,   154,   203,   253,   302,   352,   401,   451,   500,   700,   1000,  1500,  2500,  4000,  5500,  7000,  8500,  10000,
    11000, 12000, 13000, 14000, 15000, 16000, 17000, 18000, 19000, 20000, 21000, 22000, 23000, 24000, 25000, 26000, 27000, 28000, 29000, 30000,
};
//自动亮度运行时参数
static struct auto_light_context_t g_auto_light_ctx = {
    .is_enabled = false,
    .is_adjusting = false,
    .dispaly_brightness = 0.0f,
    .light_intensity = 0.0f,
    .light_level = 0,
    .hbm_pct = 0,
    .calc_status = BRIGHTNESS_CALC_INIT,
    .stop_reason = ALG_AUTO_LIGHT_OFF_OTHER,
    .adjust_mode = AUTO_LIGHT_NORMAL,
    .is_protect_timer_active = false,
};

/*-------------------  一些辅助计算的数学函数 --------------------------------*/

//根据亮度百分比获取亮度值(nit)
static uint16_t get_brightness_nit_from_percent(uint8_t percent)
{
    for (int i = 0; i < BRIGHTNESS_LEVELS; i++)
    {
        if (percent <= thresholds[i].light_value)
        {
            return thresholds[i].light_nit;
        }
    }
    return 800;
}

//根据光照强度计算屏幕亮度（0-1500nit）
static float calculate_brightness(uint32_t lux)
{
    float brightness = 0.0f;
    if (lux <= 5)
    {
        // 当照度≤5lux时，亮度锁定为10nit
        brightness = 10.0f;
    }
    else if (lux <= 500)
    {
        // 线性公式: 10 + (lux/500) * (205-10)
        brightness = 10.0f + ((float) lux / 500.0f) * (205.0f - 10.0f);
    }
    else if (lux >= 30000)
    {
        brightness = 1500.0f;
    }
    else
    {
        // 指数公式: 2.3 * lux^0.62 + 100
        brightness = 2.3f * powf((float) lux, 0.62f) + 100.0f;
    }
    return brightness;
}

//根据屏幕亮度反推对应的光线强度
static uint32_t calculate_lux_from_nit(float nit)
{
    uint32_t ret_lux = 0;
    if (nit <= 10.0f)   // 段 1
    {
        ret_lux = 5;
    }
    else if (nit <= 205.0f)   // 段 2（线性）
    {
        float lux = ((nit - 10.0f) * 500.0f) / (205.0f - 10.0f);
        ret_lux = (uint32_t) (lux + 0.5f);   // 四舍五入
    }
    else if (nit >= 1500.0f)                 // 段 4
    {
        ret_lux = 30000;
    }
    else   // 段 3（指数）
    {
        /* 原指数公式：2.3 * lux^0.62 + 100
           反推：lux = ((nit - 100) / 2.3)^(1/0.62)
        */
        float lux = powf((nit - 100.0f) / 2.3f, 1.0f / 0.62f);
        if (lux < 500.0f)
            lux = 500.0f;   // 防浮点误差
        if (lux > 30000.0f)
            lux = 30000.0f;
        ret_lux = (uint32_t) (lux + 0.5f);
    }
    return ret_lux;
}

//根据屏幕亮度计算hbm百分比
static uint8_t get_hbm_pct_by_nit(float nit)
{
    uint8_t hbm_ptc = 0;
    if (nit < 0.0f)
        nit = 0.0f;
    if (nit > 1500.0f)
        nit = 1500.0f;
    hbm_ptc = (uint8_t) ((nit / 1500.0f) * 100.0f + 0.5f);   // 四舍五入
    return hbm_ptc;
}

//清除中值滤波器
static void median_filter_clean(void)
{
    for (int i = 0; i < MEDIAN_ARRY_SIZE; i++)
    {
        median_filter_raw[i] = 0;
        median_filter_sorted_arry[i] = 0;
    }
    median_filter_head = 0;
    median_filter_arry_cnt = 0;
}

//中值滤波器
static uint32_t median_filter(uint32_t new_val)
{
    /* 1. 计算当前实际窗口长度 len */
    uint8_t len = (median_filter_arry_cnt < MEDIAN_ARRY_SIZE) ? median_filter_arry_cnt + 1 : MEDIAN_ARRY_SIZE;

    /* 2. 如果窗口已满，先在 median_filter_sorted_arry[] 中删掉最旧值 */
    if (median_filter_arry_cnt >= MEDIAN_ARRY_SIZE)
    {
        uint8_t pos_old = 0;
        while (pos_old < len && median_filter_sorted_arry[pos_old] != median_filter_raw[median_filter_head])
        {
            ++pos_old;
        }
        for (uint8_t i = pos_old; i < len - 1; ++i)
        {
            median_filter_sorted_arry[i] = median_filter_sorted_arry[i + 1];
        }
    }

    /* 3. 把新值插入到 median_filter_sorted_arry[] 正确位置（保持升序） */
    uint8_t pos = 0;
    while (pos < len - 1 && new_val > median_filter_sorted_arry[pos])
    {
        ++pos;
    }
    for (uint8_t i = len - 1; i > pos; --i)
    {
        median_filter_sorted_arry[i] = median_filter_sorted_arry[i - 1];
    }
    median_filter_sorted_arry[pos] = new_val;

    /* 4. 更新环形缓冲区 */
    median_filter_raw[median_filter_head] = new_val;
    median_filter_head = (median_filter_head + 1) % MEDIAN_ARRY_SIZE;
    median_filter_arry_cnt++;

    /* 5. 返回当前中位数 */
    return median_filter_sorted_arry[len / 2];
}






#ifndef SIMULATOR
static void service_auto_light_data_update(uint32_t *ltr_in)
{
    if (ltr_in == NULL)
    {
        return;
    }
    uint32_t *ltr = qwos_malloc(sizeof(uint32_t));
    if (ltr == NULL)
    {
        return;
    }
    *ltr = *ltr_in;
    bool ret = service_event_notify(EVT_SERVICE_AUTO_LIGHT, 0, (uint32_t *) ltr);
    if (!ret)
    {
        qwos_free(ltr);
    }
}

static void auto_light_sensor_callback(const void *data, uint32_t len)
{
    if (data == NULL || len != sizeof(struct sensor_light))
    {
        QW_LOG_E("AUTO_LIGHT","Invalid light sensor data");
        return;
    }
    // 检查背光状态，只在亮屏时处理
    if (get_backlight_status() == BK_STATUS_ON && !g_auto_light_ctx.is_adjusting)
    {
        g_auto_light_ctx.is_adjusting = true;
        const struct sensor_light *light_data = (const struct sensor_light *) data;
        uint32_t lux_value = (uint32_t) light_data->light;
        service_auto_light_data_update(&lux_value);
    }
}
#endif

//亮度渐变调整
static void brightness_timer_callback(void *parameter)
{
    timer_user_param_t *param = (timer_user_param_t *) parameter;
    if (timer_current_step <= param->sum_step)
    {
        float after_adjust_nit = 0.0;
        after_adjust_nit = param->old + param->dx * timer_current_step;
        timer_current_step++;
        uint8_t old_hbm_pct = get_hbm_pct_by_nit(g_auto_light_ctx.dispaly_brightness);
        uint8_t new_hbm_pct = get_hbm_pct_by_nit(after_adjust_nit);
        if (old_hbm_pct != new_hbm_pct)
        {
            g_auto_light_ctx.dispaly_brightness = after_adjust_nit;
            g_auto_light_ctx.hbm_pct = new_hbm_pct;
            g_auto_light_ctx.calc_status = BRIGHTNESS_CALC_UPDATED;
            backlight_auto_check_app();
        }
    }
    //调整完毕，清除参数
    if (timer_current_step > param->sum_step || get_backlight_status() != BK_STATUS_ON)
    {
        timer_current_step = 1;
        qw_timer_stop(&brightness_timer_id);
        timer_adjust_light_state = false;
        QW_LOG_I("AUTO_LIGHT","ADJUST OVER. NOW HBM %d NIT:%f LUX:%f", g_auto_light_ctx.hbm_pct, g_auto_light_ctx.dispaly_brightness, g_auto_light_ctx.light_intensity);
    }
}

/**
 * @brief 定时器缓慢调整亮度
 *
 * @param sum_step 一共需要的步骤数
 * @param old 当前
 * @param new 目标值
 * @return uint8_t
 */
static void timer_adjust_light(uint8_t sum_step, float old, float new)
{
    if (sum_step == 0)
    {
        return;
    }
    if (!timer_init_flag)
    {
        qw_timer_init(&brightness_timer_id, QW_TIMER_FLAG_PERIODIC | QW_TIMER_FLAG_SOFT_TIMER, brightness_timer_callback);
        timer_init_flag = true;
    }
    timer_adjust_light_state = true;
    static timer_user_param_t user_param = {0};
    user_param.sum_step = sum_step;
    user_param.dx = (new - old) / sum_step;
    user_param.old = old;
    user_param.new = new;
    qw_timer_start(&brightness_timer_id, ADJUST_STEP_TIME, (void *) &user_param, "brightness_timer_callback");   // 启动定时器
}

//根据光照强度调整亮度等级，返回等级变化的差值
static int check_need_adjust(float ltr)
{
    int ret = 0;

    if (timer_adjust_light_state)
    {
        return ret;
    }
    uint8_t level = 0;
    if (ltr < 30000)
    {
        for (; level < ADJUST_LEVEL_NUM; level++)
        {
            if (ltr <= adjust_level_arr[level])
            {
                break;
            }
        }
    }
    else
    {
        level = ADJUST_LEVEL_NUM - 1;
    }
    if (level != g_auto_light_ctx.light_level)
    {
        ret = abs(level - g_auto_light_ctx.light_level);
        g_auto_light_ctx.light_level = level;
        return ret;
    }
    return ret;
}



//更新
void alg_auto_light_update(uint32_t ltr_in)
{
    //1、收集光强
    uint32_t ltr_avg = median_filter(ltr_in);
    //2、判断是否需要调整
    uint8_t step = check_need_adjust(ltr_avg);
    if (step == 0)
    {
        return;
    }
    //4、调整
    float src_y = calculate_brightness(g_auto_light_ctx.light_intensity);
    float dst_y = calculate_brightness(ltr_avg);
    g_auto_light_ctx.light_intensity = ltr_avg;
    timer_adjust_light(step * 5, src_y, dst_y);
    //5、收尾
    QW_LOG_D("AUTO_LIGHT","ltr_avg:%d src_y:%f--->>>dst_y:%f",ltr_avg, src_y, dst_y);
    return;
}

void alg_set_auto_light_stop_reason(AUTO_LIGHT_STOP_REASON_T reason)
{
    g_auto_light_ctx.stop_reason = reason;
}

void alg_auto_light_set_adjust_mode(AUTO_LIGHT_ADJUST_MODE mode)
{
    if (mode == AUTO_LIGHT_PROTECT && g_auto_light_ctx.adjust_mode == AUTO_LIGHT_NORMAL)
    {
        g_auto_light_ctx.adjust_mode = AUTO_LIGHT_PROTECT;
        if (g_auto_light_ctx.is_protect_timer_active)
        {
            qw_timer_stop(&protect_timer_id);
            g_auto_light_ctx.is_protect_timer_active = false;
        }
    }
}

static void _auot_light_start(void)
{
    if (g_auto_light_ctx.is_enabled)
    {
        return;
    }
    g_auto_light_ctx.is_enabled = true;
    switch (g_auto_light_ctx.stop_reason)
    {
    case ALG_AUTO_LIGHT_OFF_SCREEN:
        break;
    case ALG_AUTO_LIGHT_OFF_OTHER:
        g_auto_light_ctx.dispaly_brightness = 205.0f;
        break;
    case ALG_AUTO_LIGHT_OFF_SWITCH:
        g_auto_light_ctx.dispaly_brightness = get_brightness_nit_from_percent(get_light_value());
        break;
    default:
        break;
    }
    g_auto_light_ctx.light_intensity = calculate_lux_from_nit(g_auto_light_ctx.dispaly_brightness);
    check_need_adjust(g_auto_light_ctx.light_intensity);
    // QW_LOG_D("AUTO_LIGHT","START nit:%f ltr:%f level:%d",g_auto_light_ctx.dispaly_brightness,g_auto_light_ctx.light_intensity,g_auto_light_ctx.light_level);
#ifndef SIMULATOR
    set_system_params()->lcd_auto_light_en = true;
    // 订阅光感传感器数据，替代原来的定时器
    optional_config_t config = {.sampling_rate = 1};   // 使用默认采样率
    int32_t ret = qw_dataserver_subscribe_id(DATA_ID_LIGHT, auto_light_sensor_callback, &config);
    if (ret != 0)
    {
        QW_LOG_E("AUTO_LIGHT","Failed to subscribe light sensor: %d", ret);
        g_auto_light_ctx.is_enabled = false;
        return;
    }
    QW_LOG_I("AUTO_LIGHT","Successfully subscribed to light sensor");
#endif
}

rt_err_t alg_auto_light_start()
{
#ifndef SIMULATOR
    uint32_t *state = qwos_malloc(sizeof(uint32_t));
    if (state == NULL)
    {
        return RT_ERROR;
    }
    *state = 1;
    bool ret = service_event_notify(EVT_SERVICE_AUTO_LIGHT_CTRL, 0, (uint32_t *) state);
    if (!ret)
    {
        qwos_free(state);
        return RT_ERROR;
    }
#endif
    return RT_EOK;
}

static void _auot_light_stop(void)
{
    if (!g_auto_light_ctx.is_enabled)
    {
        return;
    }
    g_auto_light_ctx.is_enabled = false;
#ifndef SIMULATOR
    set_system_params()->lcd_auto_light_en = false;
    int32_t ret = qw_dataserver_unsubscribe_id(DATA_ID_LIGHT, auto_light_sensor_callback);
    if (ret != 0)
    {
        QW_LOG_E("AUTO_LIGHT","Failed to unsubscribe light sensor: %d", ret);
    }
#endif
    if (g_auto_light_ctx.is_protect_timer_active)
    {
        qw_timer_stop(&protect_timer_id);
        g_auto_light_ctx.is_protect_timer_active = false;
        g_auto_light_ctx.adjust_mode = AUTO_LIGHT_NORMAL;
    }
    g_auto_light_ctx.is_adjusting = false;
    g_auto_light_ctx.calc_status = BRIGHTNESS_CALC_INIT;
    median_filter_clean();
}

rt_err_t alg_auto_light_stop()
{
#ifndef SIMULATOR
    uint32_t *state = qwos_malloc(sizeof(uint32_t));
    if (state == NULL)
    {
        return RT_ERROR;
    }
    *state = 0;
    bool ret = service_event_notify(EVT_SERVICE_AUTO_LIGHT_CTRL, 0, (uint32_t *) state);
    if (!ret)
    {
        qwos_free(state);
        return RT_ERROR;
    }
#endif
    return RT_EOK;
}

static void protect_timer_callback(void *parameter)
{
    if (g_auto_light_ctx.adjust_mode == AUTO_LIGHT_NORMAL && g_auto_light_ctx.dispaly_brightness > 800)
    {
        g_auto_light_ctx.adjust_mode = AUTO_LIGHT_PROTECT;
    }
    g_auto_light_ctx.is_protect_timer_active = false;
}

uint8_t alg_auto_light_get(void)
{
    //若get时未计算出结果，则根据情况返回值
    if (g_auto_light_ctx.calc_status != BRIGHTNESS_CALC_UPDATED)
    {
        switch (g_auto_light_ctx.stop_reason)
        {
        case ALG_AUTO_LIGHT_OFF_SCREEN:
            break;
        case ALG_AUTO_LIGHT_OFF_OTHER:
            g_auto_light_ctx.dispaly_brightness = 205.0f;
            break;
        case ALG_AUTO_LIGHT_OFF_SWITCH:
            g_auto_light_ctx.dispaly_brightness = get_brightness_nit_from_percent(get_light_value());
            break;
        default:
            break;
        }
        g_auto_light_ctx.hbm_pct = get_hbm_pct_by_nit(g_auto_light_ctx.dispaly_brightness);
    }

    if (g_auto_light_ctx.dispaly_brightness > 800)
    {
        if (!g_auto_light_ctx.is_protect_timer_active)
        {
            if (!protect_timer_init_flag)
            {
                qw_timer_init(&protect_timer_id, QW_TIMER_FLAG_ONE_SHOT | QW_TIMER_FLAG_SOFT_TIMER, protect_timer_callback);
                protect_timer_init_flag = true;
            }
            qw_timer_start(&protect_timer_id, 60000, NULL, "protect_lcd_timer");   // 启动定时器
            g_auto_light_ctx.is_protect_timer_active = true;
        }

        if (g_auto_light_ctx.adjust_mode == AUTO_LIGHT_NORMAL)
        {
            return g_auto_light_ctx.hbm_pct + HBM_PCT_CORRECTION;
        }
        else
        {
            return get_hbm_pct_by_nit(600) + HBM_PCT_CORRECTION;
        }
    }
    else
    {
        return g_auto_light_ctx.hbm_pct + HBM_PCT_CORRECTION;
    }
}

bool backlight_module_auto_light_update(ser_event_data_t *data)
{
    if (data == NULL)
    {
        return false;
    }
    uint32_t ltr = *(uint32_t *) data->module_data.pdata;
    alg_auto_light_update(ltr);
    g_auto_light_ctx.is_adjusting = false;
    qwos_free(data->module_data.pdata);

    return true;
}

bool backlight_module_auto_light_ctrl(ser_event_data_t *data)
{
    if (data == NULL)
    {
        return false;
    }
    uint32_t state = *(uint32_t *) data->module_data.pdata;
    if (state)
    {
        _auot_light_start();
    }
    else
    {
        _auot_light_stop();
    }
    qwos_free(data->module_data.pdata);
    return true;
}