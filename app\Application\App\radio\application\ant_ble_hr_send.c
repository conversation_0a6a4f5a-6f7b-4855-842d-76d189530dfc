/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ant_ble_hr_send.c
@Time    :   2025/01/09 14:33:41
<AUTHOR>   txy
*
**************************************************************************/

#include "ant_ble_hr_send.h"
#include "rtthread.h"
#include "ant_hrm_tx.h"
#include "ble_lb55x_hr_s.h"
#include "ble_lb55x.h"
#include "ble_ant_module.h"
#include "qw_log.h"
#include "qw_user_debug.h"
#include "ble_data_inf.h"
#include "hr_push.h"  // 包含原有的心率推送接口
#include "subscribe_service.h"
#include "qw_sensor_common_config.h"
#include "ppg/gh3220/include/Ppg.h"

// 测试宏定义
#define HR_SEND_TEST_MODE_ENABLE    1  // 0=关闭测试模式, 1=开启测试模式

#if HR_SEND_TEST_MODE_ENABLE
#include "qw_timer.h"
#define SIM_HR_VALUE_MAX 190 // 模拟心率最大值
#define SIM_HR_VALUE_MIN 60  // 模拟心率最小值
#define SIM_HR_TIMER_INTERVAL 1000 // 模拟心率定时器间隔

// 测试模式相关变量
static bool fake_hr_flag = false;  // 测试模式下是否使用模拟心率
static qw_timer sim_hr_timer;           // 模拟心率定时器
static uint8_t sim_hr_value = SIM_HR_VALUE_MIN; // 模拟心率值
static bool sim_hr_increase_flag = true; // 模拟心率增长标志
#endif

#define HR_SEND_LVL             LOG_LVL_INFO
#define HR_SEND_TAG             "hr.send"

#if (HR_SEND_LVL >= LOG_LVL_DBG)
    #define HR_SEND_LOG_D(fmt, ...)        QW_LOG_D(HR_SEND_TAG, fmt, ##__VA_ARGS__)
#else
    #define HR_SEND_LOG_D(fmt, ...)
#endif

#if (HR_SEND_LVL >= LOG_LVL_INFO)
    #define HR_SEND_LOG_I(fmt, ...)        QW_LOG_I(HR_SEND_TAG, fmt, ##__VA_ARGS__)
#else
    #define HR_SEND_LOG_I(fmt, ...)
#endif

#if (HR_SEND_LVL >= LOG_LVL_WARNING)
    #define HR_SEND_LOG_W(fmt, ...)        QW_LOG_W(HR_SEND_TAG, fmt, ##__VA_ARGS__)
#else
    #define HR_SEND_LOG_W(fmt, ...)
#endif

#if (HR_SEND_LVL >= LOG_LVL_ERROR)
    #define HR_SEND_LOG_E(fmt, ...)        QW_LOG_E(HR_SEND_TAG, fmt, ##__VA_ARGS__)
#else
    #define HR_SEND_LOG_E(fmt, ...)
#endif

static bool is_hr_send = false;
static uint8_t current_hr_value = 0;  // 当前心率值，供ANT模块获取
// 静态函数前向声明
static uint32_t enable_hr_ant(bool enable);
static void ble_hr_send(uint8_t wear_state, uint8_t heart_rate);
static void subscribe_device_hr_algo(bool enable);
static int try_send_hr_to_ble(uint8_t heart_rate);

#if HR_SEND_TEST_MODE_ENABLE
/************************************************************************
 *@function: sim_hr_timer_callback
 *@brief: 模拟心率定时器回调函数
 *@param: p_context - 定时器上下文
 *@return: none
 *************************************************************************/
static void sim_hr_timer_callback(void *p_context)
{
    if (sim_hr_value <= SIM_HR_VALUE_MIN)
    {
        sim_hr_increase_flag = true;
    }
    else if (sim_hr_value >= SIM_HR_VALUE_MAX)
    {
        sim_hr_increase_flag = false;
    }

    if (sim_hr_increase_flag)
    {
        sim_hr_value += 1;
    }
    else
    {
        sim_hr_value -= 1;
    }

    // 更新当前心率值，供ANT模块获取，同时设置心率推送值
    update_hr_push_value(sim_hr_value);

    // 发送模拟心率数据到BLE
    try_send_hr_to_ble(sim_hr_value);
    HR_SEND_LOG_D("sim hr value: %d", sim_hr_value);
}
#endif

/************************************************************************
 *@function:int try_send_hr_to_ble(uint8_t heart_rate)
 *@brief:尝试通过ble发送蓝牙心率
 *@param:wear_stat:佩戴状态, heart_rate:心率值
 *@return:true:发送成功，false:发送失败
*************************************************************************/
extern uint8_t algo_ppg_wear_get(void); // 获取佩戴状态
static int try_send_hr_to_ble(uint8_t heart_rate)
{
    if (ble_is_connected())
    {
        const uint8_t wear_state = fake_hr_flag ? 1 : algo_ppg_wear_get();
        ble_hr_send(wear_state, heart_rate);
        return 0;
    }
    return -1;
}

static void hr_result_callback(const void *in, uint32_t len)
{
    static uint8_t last_hr = 0;
    ppg_out_data *out_data = (ppg_out_data *) in;
    if (NULL == in || len != sizeof(ppg_out_data) || PPG_OUTTYPE_HR != out_data->data_type)
    {
        rt_kprintf("hr_result_callback data error\n");
        return;
    }
    if (out_data->ppg_data.hr_data.hr != last_hr)
    {
        last_hr = out_data->ppg_data.hr_data.hr;
        // 更新当前心率值，供ANT模块获取，同时设置心率推送值
        update_hr_push_value(last_hr);
        try_send_hr_to_ble(last_hr);
    }
}

static void subscribe_device_hr_algo(bool enable)
{
    if (enable)
    {
#if HR_SEND_TEST_MODE_ENABLE
        if (fake_hr_flag)
        {
            // 测试模式且使用模拟心率
            qw_timer_init(&sim_hr_timer, QW_TIMER_FLAG_PERIODIC | QW_TIMER_FLAG_SOFT_TIMER, sim_hr_timer_callback);
            qw_timer_start(&sim_hr_timer, SIM_HR_TIMER_INTERVAL, NULL, "sim_hr_timer");
            HR_SEND_LOG_I("start sim hr timer for test mode");
        }
        else
        {
            // 测试模式下使用真实心率
            optional_config_t config = {.sampling_rate = 1, .value = HR_ALGO_MODE_BG};
            int ret = qw_dataserver_subscribe_id(DATA_ID_ALGO_PPG_HR, hr_result_callback, &config);
            if (ret != 0)
            {
                HR_SEND_LOG_E("subscribe hr data erro ret:%d", ret);
            }
            else
            {
                HR_SEND_LOG_I("subscribe hr data success");
            }
        }
#else
        // 正常模式：只编译真实心率订阅代码
        optional_config_t config = {.sampling_rate = 1, .value = HR_ALGO_MODE_BG};
        int ret = qw_dataserver_subscribe_id(DATA_ID_ALGO_PPG_HR, hr_result_callback, &config);
        if (ret != 0)
        {
            HR_SEND_LOG_E("subscribe hr data erro ret:%d", ret);
        }
        else
        {
            HR_SEND_LOG_I("subscribe hr data success");
        }
#endif
    }
    else
    {
#if HR_SEND_TEST_MODE_ENABLE
        if (fake_hr_flag)
        {
            // 测试模式且使用模拟心率
            qw_timer_stop(&sim_hr_timer);
            HR_SEND_LOG_I("stop sim hr timer");
        }
        else
        {
            // 测试模式下使用真实心率
            int ret = qw_dataserver_unsubscribe_id(DATA_ID_ALGO_PPG_HR, hr_result_callback);
            if (ret != 0)
            {
                HR_SEND_LOG_E("unsubscribe hr data erro ret:%d", ret);
            }
        }
#else
        // 正常模式：只编译真实心率取消订阅代码
        int ret = qw_dataserver_unsubscribe_id(DATA_ID_ALGO_PPG_HR, hr_result_callback);
        if (ret != 0)
        {
            HR_SEND_LOG_E("unsubscribe hr data erro ret:%d", ret);
        }
#endif
    }
}

static uint32_t enable_hr_ant(bool enable)
{
    uint32_t ret = 0;
#ifndef CONFIG_SEND_HR_WITHOUT_ANT
    static bool ant_flag = false;

    if (enable && !ant_flag)
    {
        ant_flag = true;
        ble_ant_nrf_start(NRF_USED_TYPE_HR_SEND);
        ant_hrm_tx_profile_setup();
        ret = ant_hr_open();
        ant_hrm_tx_open();
        HR_SEND_LOG_I("ANT HR enabled, ret=%d", ret);
    }
    else if (!enable && ant_flag)
    {
        ant_flag = false;
        ret = ant_hr_close();
        ant_hrm_tx_close();
        ble_ant_nrf_stop(NRF_USED_TYPE_HR_SEND);
        HR_SEND_LOG_I("ANT HR disabled, ret=%d", ret);
    }
#else
    HR_SEND_LOG_W("ANT not supported in Sim Mode");
    ret = 0;  // 不支持ANT时返回成功，避免影响BLE功能
#endif
    return ret;
}

/************************************************************************
 *@function: ant_ble_hr_open
 *@brief: 开启心率推送功能，支持ANT和BLE两种推送方式
 *@param: mode - 推送模式选择：
 *              0 = ANT和BLE都推送（默认模式）
 *              1 = 只推送ANT（仅ANT协议发送心率数据）
 *              其他值 = 只使用BLE发送（BLE是必须的，ANT是可选的）
 *@return: 0 - 成功开启
 *         0xff - 已经开启，重复调用
 *@note: BLE心率推送是基础功能，始终会启用
 *       ANT推送是可选功能，根据mode参数决定是否启用
 *************************************************************************/
uint32_t ant_ble_hr_open(uint8_t mode)
{
    uint32_t ret = 0;
    if (is_hr_send)
    {
        ret = 0xff;
        HR_SEND_LOG_W("hr send is already open");
        return ret;
    }
    else
    {
        is_hr_send = true;
        HR_SEND_LOG_I("hr send open with mode: %d (0=both, 1=ant only, other=ble only)", mode);
#ifdef RT_USING_PM
        rt_pm_request(PM_SLEEP_MODE_IDLE);
#endif
        // 根据模式参数决定启用哪些功能
        // mode: 0=ANT和BLE都推送, 1=只推送ANT, 其他=只使用BLE发送
        if (mode == 0 || mode == 1)
        {
            // 启用ANT推送（可选功能）
            ret = enable_hr_ant(true);
            if (ret != 0) {
                HR_SEND_LOG_W("enable ANT failed, ret=%d, continue with BLE only", ret);
            }
        }
        // BLE心率推送（必须功能，始终启用）
        subscribe_device_hr_algo(true);
        ble_app_adv_hrs_change(1);   // 广播为心率设备
    }
    return ret;
}

uint32_t ant_ble_hr_close(void)
{
    uint32_t ret = 0;
    if (is_hr_send)
    {
        is_hr_send = false;
        // 重置心率推送值
        update_hr_push_value(0);
        enable_hr_ant(false);
        subscribe_device_hr_algo(false);
        ble_app_adv_hrs_change(0);   // will adv it's WR02.
#ifdef RT_USING_PM
        rt_pm_release(PM_SLEEP_MODE_IDLE);
#endif
    }
    else
    {
        ret = 0xff;
    }
    return ret;
}

/************************************************************************
 *@function: get_push_hr_value
 *@brief: 获取当前心率值，供ANT模块使用
 *@param: none
 *@return: 当前心率值
 *************************************************************************/
uint8_t get_push_hr_value(void)
{
    return current_hr_value;
}

/************************************************************************
 *@function: update_hr_push_value
 *@brief: 更新心率推送值，统一管理心率数据并调用原有的notify_hr_push_value
 *@param: value - 心率值
 *@return: none
 *************************************************************************/
void update_hr_push_value(uint8_t value)
{
    current_hr_value = value;
    // 调用原有的hr_push.cpp中的函数
    notify_hr_push_value(value);
    HR_SEND_LOG_D("update hr push value: %d", value);
}

/************************************************************************
 *@function: get_hr_tx_status
 *@brief: 获取心率推送状态
 *@param: none
 *@return: false - 未开启模拟推送，true - 已开启模拟推送
 *************************************************************************/
bool get_hr_tx_status(void)
{
    return is_hr_send;
}

/************************************************************************
 *@function: sim_hr_tx_status_set
 *@brief: 设置模拟心率推送标志状态（通过宏控制，运行时不可更改）
 *@param: flag - false: 不开启模拟推送，true: 开启模拟推送
 *@return: false - 设置失败（运行时不可更改），true - 设置成功
 *************************************************************************/
bool sim_hr_tx_status_set(bool flag)
{
    fake_hr_flag = flag;
    return fake_hr_flag;
}

bool sim_hr_tx_status_get(void)
{
    return fake_hr_flag;
}


static void ble_hr_send(uint8_t wear_state, uint8_t heart_rate)
{
    if (false == is_hr_send)
        return;
    static uint8_t l_wear_state = 0;
    static uint8_t l_heart_rate = 0;
    if (l_wear_state != wear_state || l_heart_rate != heart_rate)
    {
        l_wear_state = wear_state;
        if (l_wear_state != 0)
        {
            l_heart_rate = heart_rate;
        }
        ble_hrs_notify_heart_rate(l_wear_state, l_heart_rate);
    }
}

int cmd_hr_send(int argc, char *argv[])
{
    if (argc >= 2)
    {
        if (strcmp(argv[1], "open") == 0)
        {
            uint8_t mode = 0;  // 默认模式
            if (argc >= 3) {
                mode = atoi(argv[2]);
            }
            ant_ble_hr_open(mode);
        }
        else if (strcmp(argv[1], "close") == 0)
        {
            ant_ble_hr_close();
        }
        else if (strcmp(argv[1], "sim") == 0)
        {
            if (argc >= 3)
            {
                if (strcmp(argv[2], "status") == 0)
                {
                    if (get_hr_tx_status())
                    {
                        rt_kprintf("HR TX sim is enabled\r\n");
                    }
                    else
                    {
                        rt_kprintf("HR TX sim is disabled\r\n");
                    }
                }
                else if (strcmp(argv[2], "true") == 0)
                {
                    bool ret = sim_hr_tx_status_set(true);
                    rt_kprintf("Set sim to true: %s\r\n", ret ? "success" : "failed");
                }
                else if (strcmp(argv[2], "false") == 0)
                {
                    bool ret = sim_hr_tx_status_set(false);
                    rt_kprintf("Set sim to false: %s\r\n", ret ? "success" : "failed");
                }
                else if (strcmp(argv[2], "fake") == 0)
                {
#if HR_SEND_TEST_MODE_ENABLE
                    if (argc >= 4)
                    {
                        if (strcmp(argv[3], "true") == 0)
                        {
                            fake_hr_flag = true;
                            rt_kprintf("Set fake_hr_flag to true\r\n");
                        }
                        else if (strcmp(argv[3], "false") == 0)
                        {
                            fake_hr_flag = false;
                            rt_kprintf("Set fake_hr_flag to false\r\n");
                        }
                        else
                        {
                            rt_kprintf("Usage: %s sim fake [true|false]\r\n", argv[0]);
                        }
                    }
                    else
                    {
                        rt_kprintf("Current fake_hr_flag: %s\r\n", fake_hr_flag ? "true" : "false");
                    }
#else
                    rt_kprintf("HR_SEND_TEST_MODE_ENABLE is not enabled\r\n");
#endif
                }
                else
                {
                    rt_kprintf("Usage: %s sim [status|true|false|fake]\r\n", argv[0]);
                }
            }
            else
            {
                rt_kprintf("Usage: %s sim [status|true|false]\r\n", argv[0]);
            }
        }
        else
        {
            rt_kprintf("Usage: %s [open|close|sim]\r\n", argv[0]); //shell 命令打印用rt_kprintf
        }
    }
    else
    {
        rt_kprintf("Usage: %s [open|close|sim]\r\n", argv[0]);
    }
    return 0;
}
FINSH_FUNCTION_EXPORT_ALIAS(cmd_hr_send, __cmd_hr_send, Test for heart rate send);
