{
    "folders": [
        {
            "path": "."
        },
        {
            "path": "../.."
        },
        {
            "path": "../../../WR02_GPS"
        },
        {
            "path": "../../../qw_platform"
        },
        {
            "path": "../../../sifli"
        }
    ],
    "settings": {
        "files.autoGuessEncoding": true,
        "C_Cpp.default.configurationProvider": "cl.eide",
        "C_Cpp.errorSquiggles": "disabled",
        "files.associations": {
            "*.bin": "hex",
            ".eideignore": "ignore",
            "*.a51": "a51",
            "*.h": "c",
            "*.c": "c",
            "*.hxx": "cpp",
            "*.hpp": "cpp",
            "*.c++": "cpp",
            "*.cpp": "cpp",
            "*.cxx": "cpp",
            "*.cc": "cpp",
            "chrono": "c",
            "unordered_map": "c",
            "__locale": "c",
            "__threading_support": "c",
            "ios": "cpp",
            "__functional_base": "cpp",
            "array": "cpp",
            "istream": "cpp",
            "locale": "cpp",
            "memory": "cpp",
            "tuple": "cpp",
            "utility": "cpp",
            "iterator": "cpp",
            "string": "cpp",
            "string_view": "cpp",
            "vector": "cpp",
            "typeinfo": "c",
            "__bit_reference": "c",
            "__node_handle": "c",
            "algorithm": "c",
            "bitset": "c",
            "deque": "c",
            "__memory": "c",
            "filesystem": "c",
            "functional": "c",
            "limits": "c",
            "optional": "c",
            "ratio": "c",
            "system_error": "c",
            "type_traits": "c",
            "__functional_base_03": "c",
            "__hash_table": "c",
            "__tuple": "c",
            "__config": "c",
            "*.inc": "cpp",
            "new": "cpp",
            "__debug": "cpp",
            "__errc": "cpp",
            "__external_threading": "cpp",
            "__mutex_base": "cpp",
            "__nullptr": "cpp",
            "__split_buffer": "cpp",
            "__string": "cpp",
            "atomic": "cpp",
            "bit": "cpp",
            "cctype": "cpp",
            "clocale": "cpp",
            "cmath": "cpp",
            "cstdarg": "cpp",
            "cstddef": "cpp",
            "cstdint": "cpp",
            "cstdio": "cpp",
            "cstdlib": "cpp",
            "cstring": "cpp",
            "ctime": "cpp",
            "cwchar": "cpp",
            "cwctype": "cpp",
            "exception": "cpp",
            "fstream": "cpp",
            "future": "cpp",
            "initializer_list": "cpp",
            "iomanip": "cpp",
            "iosfwd": "cpp",
            "iostream": "cpp",
            "mutex": "cpp",
            "ostream": "cpp",
            "queue": "cpp",
            "sstream": "cpp",
            "stack": "cpp",
            "stdexcept": "cpp",
            "streambuf": "cpp",
            "codecvt": "cpp",
            "condition_variable": "cpp",
            "numeric": "cpp",
            "random": "cpp",
            "thread": "cpp",
            "*.tcc": "cpp",
            "cfenv": "cpp",
            "charconv": "cpp",
            "cinttypes": "cpp",
            "complex": "cpp",
            "csetjmp": "cpp",
            "csignal": "cpp",
            "cuchar": "cpp",
            "forward_list": "cpp",
            "list": "cpp",
            "map": "cpp",
            "memory_resource": "cpp",
            "regex": "cpp",
            "set": "cpp",
            "unordered_set": "cpp",
            "rope": "cpp",
            "slist": "cpp",
            "scoped_allocator": "cpp",
            "shared_mutex": "cpp",
            "typeindex": "cpp",
            "valarray": "cpp",
            "__tree": "c",
            "cassert": "c",
            "ccomplex": "c",
            "cerrno": "c",
            "cfloat": "c",
            "ciso646": "c",
            "climits": "c",
            "cstdalign": "c",
            "cstdbool": "c",
            "ctgmath": "c",
            "rb_tree": "c",
            "*.def": "c",
            "xmemory": "c",
            "__functional_03": "c",
            "xutility": "c",
            "xlocmon": "cpp",
            "xtr1common": "cpp",
            "compare": "cpp",
            "span": "cpp",
            "concepts": "cpp",
            "format": "cpp",
            "ranges": "cpp",
            "stop_token": "cpp",
            "xfacet": "cpp",
            "xhash": "cpp",
            "xiosbase": "cpp",
            "xlocale": "cpp",
            "xlocbuf": "cpp",
            "xlocinfo": "cpp",
            "xlocmes": "cpp",
            "xlocnum": "cpp",
            "xloctime": "cpp",
            "xstring": "cpp",
            "xtree": "cpp"
        },
        "[yaml]": {
            "editor.insertSpaces": true,
            "editor.tabSize": 4,
            "editor.autoIndent": "advanced"
        },
        "EIDE.ARM.GCC.InstallDirectory": "C:\\Program Files (x86)\\GNU Arm Embedded Toolchain\\10 2021.10",
        "EIDE.JLink.InstallDirectory": "C:\\Program Files (x86)\\SEGGER\\JLink",
        "cortex-debug.liveWatchRefreshRate": 500,
        "cortex-debug.armToolchainPath": "C:\\Program Files (x86)\\GNU Arm Embedded Toolchain\\10 2021.10\\bin",
        "cortex-debug.JLinkGDBServerPath": "C:\\Program Files (x86)\\SEGGER\\JLink\\JLinkGDBServerCL.exe",
        "git.ignoreLimitWarning": true,
        "iis.activeFolder": "WR02_App",
        "doxdocgen.file.copyrightTag": [],
        "Codegeex.RepoIndex": true,
    },
    "extensions": {
        "recommendations": [
            "cl.eide",
            "keroc.hex-fmt",
            "xiaoyongdong.srecord",
            "hars.cppsnippets",
            "zixuanwang.linkerscript",
            "redhat.vscode-yaml",
            "IBM.output-colorizer",
            "cschlosser.doxdocgen",
            "ms-vscode.vscode-serial-monitor",
            "dan-c-underwood.arm",
            "marus25.cortex-debug"
        ]
    }
}