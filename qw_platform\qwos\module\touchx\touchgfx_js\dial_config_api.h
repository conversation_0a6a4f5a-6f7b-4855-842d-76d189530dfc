#ifndef _DIAL_CONFIG_API_H_
#define _DIAL_CONFIG_API_H_
#include "stdint.h"
#ifdef __cplusplus
extern "C" {
#endif

uint32_t get_dial_them_color(uint32_t index);
uint32_t get_using_dial_index(void);
void set_inuse_dial_index(uint32_t usingDialIndexValue);
uint32_t get_dial_counts(void);
uint32_t get_cfg_current_using_dial_goodsid(void);
uint32_t get_cfg_dial_goodsid_by_index(int index);
const char* get_cfg_dial_type(int index);
uint8_t dial_cfg_is_exist(uint32_t usingDialIndex);
uint32_t get_dial_edit_type();
uint32_t get_dial_edit_type_by_index(uint32_t index);
uint32_t get_dial_cfg_theme_color_num(uint32_t goodsId);
void set_dial_cfg_color_inuse_index(uint32_t goodsId, uint32_t index);
uint32_t get_dial_cfg_color_inuse_index();
uint32_t get_dial_edit_type_num();
void get_edit_data_type(uint32_t *dataType);
uint32_t get_dial_data_num();
uint32_t get_dial_data_type_by_index(uint32_t index);
void set_dial_data_type_by_index(uint32_t index, uint32_t dataType);
uint32_t get_js_support_aod_flag(uint32_t goodsId);
    
#ifdef __cplusplus
}
#endif

#endif

