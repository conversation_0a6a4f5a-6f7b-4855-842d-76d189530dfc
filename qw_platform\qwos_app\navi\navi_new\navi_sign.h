#ifndef NAVI_SIGN_H
#define NAVI_SIGN_H

#ifdef __cplusplus
extern "C" {
#endif

#include "navi_tsgn.h"

//标记点分段器
typedef struct _NaviSignSegmentor
{
    NaviSignSegment *seg_buf;               //缓冲区，保存标记点分段
    uint32_t capacity;                      //缓冲区容量
    uint32_t len;                           //缓冲区中已保存分段的数量
    uint32_t interval;                      //当前分段间隔
    uint32_t INTERVAL;                      //初始分段间隔
    uint32_t cnt;                           //标记点计数
} NaviSignSegmentor;

//标记点匹配器
typedef struct _NaviSignMatcher
{
    NaviSignList *sign_list;                //标记点列表
    NaviSignSegmentArray *seg_array;        //标记点分段数组
} NaviSignMatcher;

//标记点加载器
typedef struct _NaviSignLoader
{
    NaviSignList *sign_list;                //标记点列表
    NaviSignSegmentArray *seg_array;        //标记点分段数组
    NaviSign *buf;                          //缓冲区，保存加载的标记点
    Range range;                            //缓冲区中加载的标记点的范围
    uint32_t capacity;                      //缓冲区容量
    uint32_t len;                           //缓冲区中加载的标记点的数量
} NaviSignLoader;

int navi_sign_segmentor_exec(NaviSignSegmentor *self, const NaviSign *sign);

int navi_sign_segmentor_end(NaviSignSegmentor *self, float dist_end);

int navi_sign_segmentor_data_get(NaviSignSegmentor *self, NaviSignSegmentArray *output);

void navi_sign_segmentor_reset(NaviSignSegmentor *self);

int navi_sign_matcher_exec(NaviSignMatcher *self, float dist, uint8_t is_reverse, NaviSign *output, uint32_t *idx);

int navi_sign_loader_exec(NaviSignLoader *self, uint8_t is_reverse, uint32_t idx);

void navi_sign_loader_data_get(NaviSignLoader *self, NaviSignNearby *sign_nearby);

void navi_sign_loader_reset(NaviSignLoader *self);

#ifdef __cplusplus
}
#endif

#endif