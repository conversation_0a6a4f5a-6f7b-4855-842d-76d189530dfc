{"Version": 1, "WorkspaceRootPath": "E:\\Snowa\\app\\project\\WR02_Win32\\project\\simulator\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialTheme\\EditDialThemeView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwScrollPage\\QwPageIndicator.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\Bitmap.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\Launcher\\LauncherView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\touchgfx_js_api.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\SystemSettings\\UniversalSettings\\LanguageSelectionMenu\\LanguageSelectionMenuModel.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwMsgBox\\QwMsgList.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos_app\\GUI\\Font\\DINPro_Font\\lv_font_DINPro_bold_56.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\MenuCard\\MenuCardView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\MenuCard\\AppCard\\SleepCard.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwUtility\\QwCtrlEvent.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\MenuCard\\MenuCardModel.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\JsApp.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\basic_app_module\\mag_data_module\\compass_algorithm.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\Tools\\Alipay\\AlipayMenu\\AlipayMenuView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\TrainingCourse\\TrainingCourse\\TrainingCourseModel.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\Lib_New\\settings\\cfg_dev_info.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\EditDialDataComponentView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\SleepInfo\\SleepStageView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\Tools\\ToolsMenu\\ToolsMenuView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwMenuFocus\\QwMenuFocus.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\HistoryList\\HistoryList\\HistoryListView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\Summary\\SummaryLap\\SummaryLapContainer.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\SelectDialView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\Barometer\\BarometerView.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\basic_app_module\\auto_wheel_perimeter_srv\\auto_wheel_perimeter_srv.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\Lib_New\\settings\\cfg_hydration_remind.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryModel.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\WebGrid\\WebGridModel.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\draw\\lv_img_cache.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\widgets\\Image.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\draw\\lv_img_decoder.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\draw\\lv_draw_img.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\Screen.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\lv_draw_engine.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\widgets\\Image.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\JSMOCHelper.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\touchgfx_js_api.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\FactoryPage\\SensorSelfCheck\\SensorSelfCheckModel.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\basic_app_module\\activity_record\\activity_fit_app.c||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{4A6BF1B1-C645-4BAD-A9B7-7B6E3DB67B2C}|project.vcxproj|E:\\Snowa\\app\\Application\\App\\UI\\Page\\SystemSettings\\DisplaySettings\\BrightnessSettings\\BrightnessSettingsModel.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "QwPageIndicator.cpp", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwScrollPage\\QwPageIndicator.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwScrollPage\\QwPageIndicator.cpp", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwScrollPage\\QwPageIndicator.cpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwScrollPage\\QwPageIndicator.cpp", "ViewState": "AgIAAFkAAAAAAAAAAAAAwHEAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-09-01T02:00:50.811Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "compass_algorithm.h", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\basic_app_module\\mag_data_module\\compass_algorithm.h", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\basic_app_module\\mag_data_module\\compass_algorithm.h", "ToolTip": "E:\\Snowa\\app\\Application\\App\\basic_app_module\\mag_data_module\\compass_algorithm.h", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\basic_app_module\\mag_data_module\\compass_algorithm.h", "ViewState": "AgIAAEIAAAAAAAAAAAAAAFQAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-26T06:19:33.08Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "TrainingCourseModel.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\TrainingCourse\\TrainingCourse\\TrainingCourseModel.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\TrainingCourse\\TrainingCourse\\TrainingCourseModel.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\TrainingCourse\\TrainingCourse\\TrainingCourseModel.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\TrainingCourse\\TrainingCourse\\TrainingCourseModel.cpp", "ViewState": "AgIAACAAAAAAAAAAAAAswDEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-26T06:17:47.708Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "QwMsgList.cpp", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwMsgBox\\QwMsgList.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwMsgBox\\QwMsgList.cpp", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwMsgBox\\QwMsgList.cpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwMsgBox\\QwMsgList.cpp", "ViewState": "AgIAAJcCAAAAAAAAAAAswKUCAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-26T02:29:14.351Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "EditDialThemeView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialTheme\\EditDialThemeView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialTheme\\EditDialThemeView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialTheme\\EditDialThemeView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialTheme\\EditDialThemeView.cpp", "ViewState": "AgIAAHgAAAAAAAAAAAAswHwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-11T07:13:17.428Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "LanguageSelectionMenuModel.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\SystemSettings\\UniversalSettings\\LanguageSelectionMenu\\LanguageSelectionMenuModel.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\SystemSettings\\UniversalSettings\\LanguageSelectionMenu\\LanguageSelectionMenuModel.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\SystemSettings\\UniversalSettings\\LanguageSelectionMenu\\LanguageSelectionMenuModel.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\SystemSettings\\UniversalSettings\\LanguageSelectionMenu\\LanguageSelectionMenuModel.cpp", "ViewState": "AgIAACwAAAAAAAAAAAAswD0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-28T06:57:35.678Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "LauncherView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Launcher\\LauncherView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Launcher\\LauncherView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Launcher\\LauncherView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Launcher\\LauncherView.cpp", "ViewState": "AgIAAF8AAAAAAAAAAAAAwHIAAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-18T01:51:54.074Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "touchgfx_js_api.c", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\touchgfx_js_api.c", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\touchgfx_js_api.c", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\touchgfx_js_api.c", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\touchgfx_js_api.c", "ViewState": "AgIAAG8AAAAAAAAAAAAAwIMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000423|", "WhenOpened": "2025-08-11T08:16:16.029Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Bitmap.hpp", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\Bitmap.hpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\Bitmap.hpp", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\Bitmap.hpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\Bitmap.hpp", "ViewState": "AgIAAFgAAAAAAAAAAAAAwGgAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-14T03:35:47.579Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "SleepCard.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\MenuCard\\AppCard\\SleepCard.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\MenuCard\\AppCard\\SleepCard.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\MenuCard\\AppCard\\SleepCard.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\MenuCard\\AppCard\\SleepCard.cpp", "ViewState": "AgIAAJ8AAAAAAAAAAAAAwK8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-27T09:47:50.168Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "MenuCardModel.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\MenuCard\\MenuCardModel.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\MenuCard\\MenuCardModel.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\MenuCard\\MenuCardModel.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\MenuCard\\MenuCardModel.cpp", "ViewState": "AgIAAAkBAAAAAAAAAAAAwBkBAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-27T09:53:09.268Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "MenuCardView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\MenuCard\\MenuCardView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\MenuCard\\MenuCardView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\MenuCard\\MenuCardView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\MenuCard\\MenuCardView.cpp", "ViewState": "AgIAAIcCAAAAAAAAAAAAwJ4CAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-27T09:57:59.337Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "QwCtrlEvent.cpp", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwUtility\\QwCtrlEvent.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwUtility\\QwCtrlEvent.cpp", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwUtility\\QwCtrlEvent.cpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwUtility\\QwCtrlEvent.cpp", "ViewState": "AgIAAFYAAAAAAAAAAAAAwF4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-28T02:56:06.118Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "lv_font_DINPro_bold_56.c", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos_app\\GUI\\Font\\DINPro_Font\\lv_font_DINPro_bold_56.c", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos_app\\GUI\\Font\\DINPro_Font\\lv_font_DINPro_bold_56.c", "ToolTip": "E:\\Snowa\\qw_platform\\qwos_app\\GUI\\Font\\DINPro_Font\\lv_font_DINPro_bold_56.c", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos_app\\GUI\\Font\\DINPro_Font\\lv_font_DINPro_bold_56.c", "ViewState": "AgIAAMQHAAAAAAAAAAAswNcHAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000423|", "WhenOpened": "2025-08-28T06:50:13.327Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "cfg_dev_info.c", "DocumentMoniker": "E:\\Snowa\\app\\Application\\Lib_New\\settings\\cfg_dev_info.c", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\Lib_New\\settings\\cfg_dev_info.c", "ToolTip": "E:\\Snowa\\app\\Application\\Lib_New\\settings\\cfg_dev_info.c", "RelativeToolTip": "..\\..\\..\\..\\Application\\Lib_New\\settings\\cfg_dev_info.c", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000423|", "WhenOpened": "2025-08-21T12:49:40.095Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "Image.cpp", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\widgets\\Image.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\widgets\\Image.cpp", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\widgets\\Image.cpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\widgets\\Image.cpp", "ViewState": "AgIAADsAAAAAAAAAAAAcwEkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-14T02:22:57.779Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "JsApp.cpp", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\JsApp.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\JsApp.cpp", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\JsApp.cpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\JsApp.cpp", "ViewState": "AgIAAI0BAAAAAAAAAAAAwJ4BAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-11T01:41:20.653Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "AlipayMenuView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Tools\\Alipay\\AlipayMenu\\AlipayMenuView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Tools\\Alipay\\AlipayMenu\\AlipayMenuView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Tools\\Alipay\\AlipayMenu\\AlipayMenuView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Tools\\Alipay\\AlipayMenu\\AlipayMenuView.cpp", "ViewState": "AgIAAFIAAAAAAAAAAAAAwGYAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-26T06:29:05.868Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "ToolsMenuView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Tools\\ToolsMenu\\ToolsMenuView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Tools\\ToolsMenu\\ToolsMenuView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Tools\\ToolsMenu\\ToolsMenuView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Tools\\ToolsMenu\\ToolsMenuView.cpp", "ViewState": "AgIAABsBAAAAAAAAAAAAwCwBAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-20T09:49:27.472Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "SleepStageView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\SleepInfo\\SleepStageView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\SleepInfo\\SleepStageView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\SleepInfo\\SleepStageView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\SleepInfo\\SleepStageView.cpp", "ViewState": "AgIAAIAAAAAAAAAAAAAAwIgAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-20T11:02:52.272Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "EditDialDataComponentView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\EditDialDataComponentView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\EditDialDataComponentView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\EditDialDataComponentView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\EditDialDataComponent\\EditDialDataComponentView.cpp", "ViewState": "AgIAAEIAAAAAAAAAAAAAwFwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-11T12:14:52.904Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "SummaryLapContainer.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Summary\\SummaryLap\\SummaryLapContainer.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\SummaryLap\\SummaryLapContainer.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Summary\\SummaryLap\\SummaryLapContainer.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\SummaryLap\\SummaryLapContainer.cpp", "ViewState": "AgIAAHgAAAAAAAAAAADwv4EAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-20T08:31:22.397Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "HistoryListView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\HistoryList\\HistoryList\\HistoryListView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\HistoryList\\HistoryList\\HistoryListView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\HistoryList\\HistoryList\\HistoryListView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\HistoryList\\HistoryList\\HistoryListView.cpp", "ViewState": "AgIAAE4AAAAAAAAAAAAAwF4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-20T08:33:41.969Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "QwMenuFocus.hpp", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwMenuFocus\\QwMenuFocus.hpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwMenuFocus\\QwMenuFocus.hpp", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwMenuFocus\\QwMenuFocus.hpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\gui\\GUICtrl\\QwMenuFocus\\QwMenuFocus.hpp", "ViewState": "AgIAAEAGAAAAAAAAAAAAwFAGAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-20T08:34:01.892Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "SensorSelfCheckModel.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\FactoryPage\\SensorSelfCheck\\SensorSelfCheckModel.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\FactoryPage\\SensorSelfCheck\\SensorSelfCheckModel.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\FactoryPage\\SensorSelfCheck\\SensorSelfCheckModel.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\FactoryPage\\SensorSelfCheck\\SensorSelfCheckModel.cpp", "ViewState": "AgIAABQAAAAAAAAAAAAswCsAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-13T08:19:59.366Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "SelectDialView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\SelectDialView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\SelectDialView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\SelectDialView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WatchDial\\SelectDial\\SelectDialView.cpp", "ViewState": "AgIAACsAAAAAAAAAAAAAwDgAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-12T06:59:51.301Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "SummaryView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryView.cpp", "ViewState": "AgIAAJsBAAAAAAAAAAAAwLIBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-07T02:27:49.867Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "cfg_hydration_remind.c", "DocumentMoniker": "E:\\Snowa\\app\\Application\\Lib_New\\settings\\cfg_hydration_remind.c", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\Lib_New\\settings\\cfg_hydration_remind.c", "ToolTip": "E:\\Snowa\\app\\Application\\Lib_New\\settings\\cfg_hydration_remind.c", "RelativeToolTip": "..\\..\\..\\..\\Application\\Lib_New\\settings\\cfg_hydration_remind.c", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000423|", "WhenOpened": "2025-08-19T01:37:14.724Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "activity_fit_app.c", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\basic_app_module\\activity_record\\activity_fit_app.c", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\basic_app_module\\activity_record\\activity_fit_app.c", "ToolTip": "E:\\Snowa\\app\\Application\\App\\basic_app_module\\activity_record\\activity_fit_app.c", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\basic_app_module\\activity_record\\activity_fit_app.c", "ViewState": "AgIAAFcFAAAAAAAAAAAYwGcFAABQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000423|", "WhenOpened": "2025-08-13T09:54:40.645Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "auto_wheel_perimeter_srv.c", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\basic_app_module\\auto_wheel_perimeter_srv\\auto_wheel_perimeter_srv.c", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\basic_app_module\\auto_wheel_perimeter_srv\\auto_wheel_perimeter_srv.c", "ToolTip": "E:\\Snowa\\app\\Application\\App\\basic_app_module\\auto_wheel_perimeter_srv\\auto_wheel_perimeter_srv.c", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\basic_app_module\\auto_wheel_perimeter_srv\\auto_wheel_perimeter_srv.c", "ViewState": "AgIAAOYAAAAAAAAAAAAswO4AAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000423|", "WhenOpened": "2025-08-19T01:33:36.104Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "BarometerView.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\Barometer\\BarometerView.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\Barometer\\BarometerView.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\AppMenu\\Barometer\\BarometerView.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\AppMenu\\Barometer\\BarometerView.cpp", "ViewState": "AgIAAL0AAAAAAAAAAADwv8UAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-19T02:23:04.474Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "lv_img_decoder.c", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\draw\\lv_img_decoder.c", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\draw\\lv_img_decoder.c", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\draw\\lv_img_decoder.c", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\draw\\lv_img_decoder.c", "ViewState": "AgIAABECAAAAAAAAAAAcwB8CAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000423|", "WhenOpened": "2025-08-14T02:26:04.284Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "SummaryModel.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryModel.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryModel.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryModel.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\Summary\\Summary\\SummaryModel.cpp", "ViewState": "AgIAAGQCAAAAAAAAAAAcwHMCAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-07T02:32:29.132Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "JSMOCHelper.hpp", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\JSMOCHelper.hpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\JSMOCHelper.hpp", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\JSMOCHelper.hpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\JSMOCHelper.hpp", "ViewState": "AgIAAHIAAAAAAAAAAAAcwIAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-14T02:23:02.375Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "Screen.cpp", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\Screen.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\Screen.cpp", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\Screen.cpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\Screen.cpp", "ViewState": "AgIAACAAAAAAAAAAAAAcwC4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-14T02:23:03.101Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "WebGridModel.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WebGrid\\WebGridModel.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WebGrid\\WebGridModel.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\WebGrid\\WebGridModel.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\WebGrid\\WebGridModel.cpp", "ViewState": "AgIAAIYAAAAAAAAAAAAswJYAAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-18T01:49:04.827Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "lv_img_cache.c", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\draw\\lv_img_cache.c", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\draw\\lv_img_cache.c", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\draw\\lv_img_cache.c", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\draw\\lv_img_cache.c", "ViewState": "AgIAALEAAAAAAAAAAAAcwMAAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000423|", "WhenOpened": "2025-08-14T04:01:36.522Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "Image.hpp", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\widgets\\Image.hpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\widgets\\Image.hpp", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\widgets\\Image.hpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\include\\touchgfx\\widgets\\Image.hpp", "ViewState": "AgIAAIYAAAAAAAAAAAAcwJYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-14T03:35:15.752Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "lv_draw_img.c", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\draw\\lv_draw_img.c", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\draw\\lv_draw_img.c", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\draw\\lv_draw_img.c", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\lv_engine\\src\\draw\\lv_draw_img.c", "ViewState": "AgIAADkBAAAAAAAAAAAcwEcBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000423|", "WhenOpened": "2025-08-14T02:27:38.214Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "lv_draw_engine.cpp", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\lv_draw_engine.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\lv_draw_engine.cpp", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\lv_draw_engine.cpp", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx\\source\\touchgfx\\lv_draw_engine.cpp", "ViewState": "AgIAACkBAAAAAAAAAAAcwDcBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-14T02:29:23.152Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "touchgfx_js_api.h", "DocumentMoniker": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\touchgfx_js_api.h", "RelativeDocumentMoniker": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\touchgfx_js_api.h", "ToolTip": "E:\\Snowa\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\touchgfx_js_api.h", "RelativeToolTip": "..\\..\\..\\..\\..\\qw_platform\\qwos\\module\\touchx\\touchgfx_js\\touchgfx_js_api.h", "ViewState": "AgIAAO8AAAAAAAAAAAAswDwBAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-08-11T12:03:59.281Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "BrightnessSettingsModel.cpp", "DocumentMoniker": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\SystemSettings\\DisplaySettings\\BrightnessSettings\\BrightnessSettingsModel.cpp", "RelativeDocumentMoniker": "..\\..\\..\\..\\Application\\App\\UI\\Page\\SystemSettings\\DisplaySettings\\BrightnessSettings\\BrightnessSettingsModel.cpp", "ToolTip": "E:\\Snowa\\app\\Application\\App\\UI\\Page\\SystemSettings\\DisplaySettings\\BrightnessSettings\\BrightnessSettingsModel.cpp", "RelativeToolTip": "..\\..\\..\\..\\Application\\App\\UI\\Page\\SystemSettings\\DisplaySettings\\BrightnessSettings\\BrightnessSettingsModel.cpp", "ViewState": "AgIAAD0AAAAAAAAAAAAswEsAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-08-07T02:20:24.839Z"}]}]}]}