<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <remote name="origin" fetch="." review="http://10.0.0.3:8081"/>
  
  <default remote="origin" revision="develop"/>
  
  <project name="app" revision="5f85d54c18e185d8832919a426d997ae4fcf8e1e" upstream="develop" dest-branch="develop"/>
  <project name="qw_algo/navigation" revision="d930065bbee6d6242f8fb714e6c15b2de1ebb4a6" upstream="cm_develop" dest-branch="cm_develop"/>
  <project name="qw_platform" revision="24fc38d98c6282180928f4523e570cf62fc07610" upstream="develop" dest-branch="develop"/>
  <project name="sifli" revision="af4ddca7240a6f49899dc8eb0c91dbb20006ecd4" upstream="develop" dest-branch="develop"/>
  <project name="tools" revision="f99c204a5ddab6e1b1f5d7bd1ddb755838e38193" upstream="develop" dest-branch="develop"/>
  <project name="vendor" revision="49aa2937fd4577cf36cf466b5c8768a5fc425c74" upstream="develop" dest-branch="develop"/>
</manifest>
