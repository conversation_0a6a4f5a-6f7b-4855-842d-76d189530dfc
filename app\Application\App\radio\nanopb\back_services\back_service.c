/***************************************Copyright (c)****************************************/
//                              <PERSON>han <PERSON> Technology Co., Ltd
//
//---------------------------------------File Info--------------------------------------------
// File name         : back_service.c
// Created by        : jiangzhen
// Descriptions      : ble通信后台服务，包括天气、AGPS
//--------------------------------------------------------------------------------------------
// History           :
// 2020-05-14        :原始版本
/*********************************************************************************************/

#include "back_service.h"
#include "back.pb.h"
#include "pb.h"
#include "pb_decode.h"
#include "pb_decode_common.h"
#include "qw_time_api.h"
#include "service_datetime.h"
#include "ble_cmd_response.h"
#include "cfg_header_def.h"
#include "ble_service_data_sync.h"
#include "ble_data_inf.h"
#include "qw_timer.h"
#include "qw_log.h"

#define PB_BACK_LOG_LVL           LOG_LVL_DBG
#define PB_BACK_LOG_TAG           "PB_BACK"

#if (PB_BACK_LOG_LVL >= LOG_LVL_DBG)
    #define PB_BACK_LOG_D(...)              QW_LOG_D(PB_BACK_LOG_TAG, __VA_ARGS__)
#else
    #define PB_BACK_LOG_D(...)
#endif

#if (PB_BACK_LOG_LVL >= LOG_LVL_INFO)
    #define PB_BACK_LOG_I(...)              QW_LOG_I(PB_BACK_LOG_TAG, __VA_ARGS__)
#else
    #define PB_BACK_LOG_I(...)
#endif

#if (PB_BACK_LOG_LVL >= LOG_LVL_WARNING)
    #define PB_BACK_LOG_W(...)              QW_LOG_W(PB_BACK_LOG_TAG, __VA_ARGS__)
#else
    #define PB_BACK_LOG_W(...)
#endif

#if (PB_BACK_LOG_LVL >= LOG_LVL_ERROR)
    #define PB_BACK_LOG_E(...)              QW_LOG_E(PB_BACK_LOG_TAG, __VA_ARGS__)
#else
    #define PB_BACK_LOG_E(...)
#endif

static uint32_t rev_length = 0;
static back_weather_st s_back_weather_data = { 0 };
static back_time_st s_back_time_data = { 0 };         //时间信息
static back_user_st s_back_user_data = { 0 };         //用户信息
static uint8_t is_weather_received = false;
static uint8_t agps_protocol_version = 1;   //版本2：增加卫星类型、时间等信息，主要便于后期增加其它类型的AGPS信息
static weather_data_update_callback_t g_weather_data_update_callback = NULL;
//-------------------------------------------------------------------------------------------
// Function Name : decode_content
// Purpose       : 解码PB中文件内容
// Param[in]     : pb_istream_t *stream     
//                 const pb_field_t *field  
//                 void **arg               
// Param[out]    : None
// Return type   : 
// Comment       : 2019-09-09
//-------------------------------------------------------------------------------------------
static bool decode_content(pb_istream_t* stream, const pb_field_t* field, void** arg)
{
    uint32_t length_temp = stream->bytes_left;

    if (!pb_read(stream, *arg, stream->bytes_left))
        return false;

    rev_length = length_temp - stream->bytes_left;

    return true;
}

//-------------------------------------------------------------------------------------------
// Function Name : three_days_weather_repeated_submsg_decode
// Purpose       : PB解码未来三天天气信息字段
// Param[in]     : pb_istream_t *stream     
//                 const pb_field_t *field  
//                 void **arg               
// Param[out]    : None
// Return type   : static
// Comment       : 2019-04-03
//-------------------------------------------------------------------------------------------
static bool three_days_weather_repeated_submsg_decode(pb_istream_t* stream, const pb_field_t* field, void** arg)
{
    weather_three_days_data_message** expected = (weather_three_days_data_message**)arg;
    weather_three_days_data_message submsg = { "" };

    submsg.date.arg = (*expected)->date.arg;
    submsg.date.funcs.decode = (*expected)->date.funcs.decode;

    submsg.sun_rise_time.arg = (*expected)->sun_rise_time.arg;
    submsg.sun_rise_time.funcs.decode = (*expected)->sun_rise_time.funcs.decode;

    submsg.sun_set_time.arg = (*expected)->sun_set_time.arg;
    submsg.sun_set_time.funcs.decode = (*expected)->sun_set_time.funcs.decode;

    if (!pb_decode(stream, weather_three_days_data_message_fields, &submsg))
        return false;

    (*expected)->has_weather_index = submsg.has_weather_index;
    (*expected)->weather_index = submsg.weather_index;

    (*expected)->has_max_temp = submsg.has_max_temp;
    (*expected)->max_temp = submsg.max_temp;

    (*expected)->has_min_temp = submsg.has_min_temp;
    (*expected)->min_temp = submsg.min_temp;

    (*expected)->has_rain_prob = submsg.has_rain_prob;
    (*expected)->rain_prob = submsg.rain_prob;

    (*expected)++;

    return true;
}

//-------------------------------------------------------------------------------------------
// Function Name : three_hours_weather_repeated_submsg_decode
// Purpose       : PB解码隔三小时字段
// Param[in]     : pb_istream_t *stream     
//                 const pb_field_t *field  
//                 void **arg               
// Param[out]    : None
// Return type   : static
// Comment       : 2019-04-03
//-------------------------------------------------------------------------------------------
static bool three_hours_weather_repeated_submsg_decode(pb_istream_t* stream, const pb_field_t* field, void** arg)
{
    weather_three_hour_data_memsage** expected = (weather_three_hour_data_memsage**)arg;
    weather_three_hour_data_memsage submsg = { "" };

    submsg.time.arg = (*expected)->time.arg;
    submsg.time.funcs.decode = (*expected)->time.funcs.decode;

    submsg.wind_deg.arg = (*expected)->wind_deg.arg;
    submsg.wind_deg.funcs.decode = (*expected)->wind_deg.funcs.decode;

    submsg.wind_spd.arg = (*expected)->wind_spd.arg;
    submsg.wind_spd.funcs.decode = (*expected)->wind_spd.funcs.decode;

    // submsg.sun_highest_time.arg = (*expected)->sun_highest_time.arg;
    // submsg.sun_highest_time.funcs.decode = (*expected)->sun_highest_time.funcs.decode;

    if (!pb_decode(stream, weather_three_hour_data_memsage_fields, &submsg))
        return false;

    (*expected)->has_rain_prob = submsg.has_rain_prob;
    (*expected)->rain_prob = submsg.rain_prob;

    (*expected)->has_wather_index = submsg.has_wather_index;
    (*expected)->wather_index = submsg.wather_index;

    (*expected)->has_temp = submsg.has_temp;
    (*expected)->temp = submsg.temp;

    // (*expected)->has_temp_max = submsg.has_temp_max;
    // (*expected)->temp_max = submsg.temp_max;

    // (*expected)->has_temp_min = submsg.has_temp_min;
    // (*expected)->temp_min = submsg.temp_min;

    (*expected)++;

    return true;
}

//-------------------------------------------------------------------------------------------
// Function Name : back_service_status_handle
// Purpose       : 通道2状态处理接口函数
// Param[in]     : uint8_t *buf  
// Param[out]    : None
// Return type   : 
// Comment       : 2019-04-06
//-------------------------------------------------------------------------------------------
void back_service_status_handle(uint8_t* buf)
{
    ble_status_cmd_st* ble_status_cmd_s = (ble_status_cmd_st*)buf;
    uint8_t status = ble_status_cmd_s->status;

    //如果失败，重新发送获取天气信息
    if (enmuDATA_ERR_STATUS == status)
    {
        switch (ble_status_cmd_s->op_type)
        {
        case BACK_OPERATE_TYPE_enum_BACK_OPERATE_TYPE_GET:
            break;
        default:
            break;
        }
    }
}

//-------------------------------------------------------------------------------------------
// Function Name : back_service_decode
// Purpose       : 后台服务PB解码接口函数
// Param[in]     : uint8_t * pb_buffer     
//                 uint16_t buffer_length  
//                 END_TYPE end_type       
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-14
//-------------------------------------------------------------------------------------------
void back_service_decode(uint8_t* pb_buffer, uint16_t buffer_length, END_TYPE end_type)
{
    uint8_t status;
    uint8_t i = 0;
    //    char file_path[BACK_SERVICE_PATH_NAME_LENGTH] = {0};
    char file_name[MAX_AGPS_NAME_LENGTH] = { 0 };
    uint8_t content[DATA_LENGTH_MAX_512B] = { 0 };
    //    unsigned int tt = 1;
    back_msg back_msg_st;

    rev_length = 0;
    memset(&back_msg_st, 0, sizeof(back_msg));
    weather_three_days_data_message three_days_weather[MAX_THREE_DAYS_COUNT] = { 0 };
    weather_three_hour_data_memsage three_hours_weather[MAX_THREE_HOURS_COUNT] = { 0 };

    back_msg_st.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_BACK;

    for (i = 0; i < MAX_THREE_DAYS_COUNT; i++)
    {
        three_days_weather[i].date.arg = s_back_weather_data.three_days_t[i].date;
        three_days_weather[i].date.funcs.decode = &decode_string;

        three_days_weather[i].sun_rise_time.arg = s_back_weather_data.three_days_t[i].sun_rise_time;
        three_days_weather[i].sun_rise_time.funcs.decode = &decode_string;

        three_days_weather[i].sun_set_time.arg = s_back_weather_data.three_days_t[i].sun_set_time;
        three_days_weather[i].sun_set_time.funcs.decode = &decode_string;
    }

    for (i = 0; i < MAX_THREE_HOURS_COUNT; i++)
    {
        three_hours_weather[i].time.arg = s_back_weather_data.three_hours_t[i].time;
        three_hours_weather[i].time.funcs.decode = &decode_string;

        three_hours_weather[i].wind_deg.arg = s_back_weather_data.three_hours_t[i].wind_deg;
        three_hours_weather[i].wind_deg.funcs.decode = &decode_string;

        three_hours_weather[i].wind_spd.arg = s_back_weather_data.three_hours_t[i].wind_spd;
        three_hours_weather[i].wind_spd.funcs.decode = &decode_string;

        // three_hours_weather[i].sun_highest_time.arg = s_back_weather_data.three_hours_t[i].sun_highest_time;
        // three_hours_weather[i].sun_highest_time.funcs.decode = &decode_string;
    }

    back_msg_st.three_days_msg.funcs.decode = &three_days_weather_repeated_submsg_decode;
    back_msg_st.three_days_msg.arg = three_days_weather;

    back_msg_st.three_hours_msg.funcs.decode = &three_hours_weather_repeated_submsg_decode;
    back_msg_st.three_hours_msg.arg = three_hours_weather;

    back_msg_st.cur_msg.time.arg = s_back_weather_data.cur_weather_t.time;
    back_msg_st.cur_msg.time.funcs.decode = &decode_string;

    back_msg_st.cur_msg.wind_deg.arg = s_back_weather_data.cur_weather_t.wind_deg;
    back_msg_st.cur_msg.wind_deg.funcs.decode = &decode_string;

    back_msg_st.cur_msg.wind_spd.arg = s_back_weather_data.cur_weather_t.wind_spd;
    back_msg_st.cur_msg.wind_spd.funcs.decode = &decode_string;

    back_msg_st.cur_msg.region.arg = s_back_weather_data.cur_weather_t.region;
    back_msg_st.cur_msg.region.funcs.decode = &decode_string;

    back_msg_st.ephemeris_data_msg.file_name.arg = file_name;
    back_msg_st.ephemeris_data_msg.file_name.funcs.decode = &decode_string;

    back_msg_st.ephemeris_data_msg.contents.arg = content;
    back_msg_st.ephemeris_data_msg.contents.funcs.decode = &decode_content;

    pb_istream_t decode_stream = pb_istream_from_buffer(pb_buffer, buffer_length);
    status = pb_decode(&decode_stream, back_msg_fields, &back_msg_st);

    if (back_msg_st.ephemeris_data_msg.has_agps_type
        && back_msg_st.ephemeris_data_msg.has_gps_type)
    {
        agps_protocol_version = 2;
    }

    if (true == status)
    {
        switch (back_msg_st.back_service_type)
        {
        case BACK_SERVICE_TYPE_enum_BACK_SERVICE_TYPE_WEATHER:
            //将解码信息拷贝到天气信息结构体里
            s_back_weather_data.cur_weather_t.cur_temperature = back_msg_st.cur_msg.cur_temperature;
            s_back_weather_data.cur_weather_t.cur_weather = back_msg_st.cur_msg.cur_weather;
            s_back_weather_data.cur_weather_t.cur_day_max_temp = back_msg_st.cur_msg.cur_day_max_temp;
            s_back_weather_data.cur_weather_t.cur_day_min_temp = back_msg_st.cur_msg.cur_day_min_temp;
            // if (back_msg_st.cur_msg.has_rain_prob)
            // {
            //     s_back_weather_data.cur_weather_t.rain_prob = back_msg_st.cur_msg.rain_prob;
            // }
            if (back_msg_st.cur_msg.has_humidity)
            {
                s_back_weather_data.cur_weather_t.humidity = back_msg_st.cur_msg.humidity;
            }
            if (back_msg_st.cur_msg.has_air_quality)
            {
                s_back_weather_data.cur_weather_t.air_quality = back_msg_st.cur_msg.air_quality;
            }
            // if (back_msg_st.cur_msg.has_sun_rise_time)
            // {
            //     s_back_weather_data.cur_weather_t.sun_rise_time = back_msg_st.cur_msg.sun_rise_time;
            // }
            // if (back_msg_st.cur_msg.has_sun_set_time)
            // {
            //     s_back_weather_data.cur_weather_t.sun_set_time = back_msg_st.cur_msg.sun_set_time;
            // }

            for (i = 0; i < MAX_THREE_DAYS_COUNT; i++)
            {
                s_back_weather_data.three_days_t[i].three_days_max_temp = three_days_weather[i].max_temp;
                s_back_weather_data.three_days_t[i].three_days_min_temp = three_days_weather[i].min_temp;
                s_back_weather_data.three_days_t[i].three_days_rain_prob = three_days_weather[i].rain_prob;
                s_back_weather_data.three_days_t[i].three_days_weather_index = three_days_weather[i].weather_index;
            }

            for (i = 0; i < MAX_THREE_HOURS_COUNT; i++)
            {
                s_back_weather_data.three_hours_t[i].three_hour_weather_index = three_hours_weather[i].wather_index;
                s_back_weather_data.three_hours_t[i].three_hour_temp = three_hours_weather[i].temp;
                s_back_weather_data.three_hours_t[i].three_hour_rain_prob = three_hours_weather[i].rain_prob;
                // s_back_weather_data.three_hours_t[i].three_hour_temp_min = three_hours_weather[i].temp_min;
            }

            is_weather_received = true;
            weather_data_update_callback_notify(&s_back_weather_data);
            weather_data_sync_succ_send();
            ble_cmd_success_status_tx(back_msg_st.service_type, back_msg_st.back_service_type, back_msg_st.back_operate_type, 0);
            break;
        case BACK_SERVICE_TYPE_enum_BACK_SERVICE_TYPE_AIR_PRESSURE:
            s_back_weather_data.air_pressure = back_msg_st.air_pressure_msg.air_pressure;
            ble_cmd_success_status_tx(back_msg_st.service_type, back_msg_st.back_service_type, back_msg_st.back_operate_type, 0);
            break;
        case BACK_SERVICE_TYPE_enum_BACK_SERVICE_TYPE_ELEVATION:
            ble_cmd_success_status_tx(back_msg_st.service_type, back_msg_st.back_service_type, back_msg_st.back_operate_type, 0);
            break;
        case BACK_SERVICE_TYPE_enum_BACK_SERVICE_TYPE_TIME:{
            PB_BACK_LOG_D("%s %d has_time_zone = %d time_zone = %d  has_time = %d time = %d",__func__,__LINE__,
                        back_msg_st.time_msg.has_time_zone,back_msg_st.time_msg.time_zone,back_msg_st.time_msg.has_time,back_msg_st.time_msg.time);
            if(back_msg_st.time_msg.has_time_zone) {
                s_back_time_data.time_zone= back_msg_st.time_msg.time_zone;
                service_datetime_set_timezone(s_back_time_data.time_zone);
            }
            if(back_msg_st.time_msg.has_time) {
                s_back_time_data.time = back_msg_st.time_msg.time;
                service_datetime_set_gmt_time(s_back_time_data.time);
            }
            ble_cmd_success_status_tx(back_msg_st.service_type, back_msg_st.back_service_type, back_msg_st.back_operate_type, 0);

            qw_tm_t time_now = {0};
            service_get_rtctime(&time_now);
            PB_BACK_LOG_D("%s %d rtc time: %02d:%02d:%02d",__func__,__LINE__,time_now.tm_hour,time_now.tm_min,time_now.tm_sec);
            break;
        }
        case BACK_SERVICE_TYPE_enum_BACK_SERVICE_TYPE_LOCATE_INFO:{
            if(back_msg_st.locate_info_msg.has_latitude && back_msg_st.locate_info_msg.longitude) {
                struct minmea_float lat;
                struct minmea_float lon;
                lat.value = back_msg_st.locate_info_msg.latitude * GPS_POSITION_SCALE;
                lat.scale = GPS_POSITION_SCALE;
                lon.value = back_msg_st.locate_info_msg.longitude * GPS_POSITION_SCALE;
                lon.scale = GPS_POSITION_SCALE;
                set_cfg_gps_backup_position(&lat, &lon);
                uint32_t cur_s = service_datetime_get_gmt_time() - service_datetime_get_timezone();
                set_cfg_gps_get_time(cur_s);
            }

            ble_cmd_success_status_tx(back_msg_st.service_type, back_msg_st.back_service_type, back_msg_st.back_operate_type, 0);
            break;
        }
#if 0
        case BACK_SERVICE_TYPE_enum_BACK_SERVICE_TYPE_USER_INFO:{
            s_back_user_data.sex= back_msg_st.user_msg.sex;
            s_back_user_data.age= back_msg_st.user_msg.age;
            s_back_user_data.height= back_msg_st.user_msg.height;
            s_back_user_data.weight= back_msg_st.user_msg.weight;
            if(back_msg_st.user_msg.has_step_length) {
                s_back_user_data.step_length= back_msg_st.user_msg.step_length;
            }
            if(back_msg_st.user_msg.has_hr_max) {
                s_back_user_data.hr_max= back_msg_st.user_msg.hr_max;
            }
            if(back_msg_st.user_msg.has_hr_rest) {
                s_back_user_data.hr_rest= back_msg_st.user_msg.hr_rest;
            }
            if(back_msg_st.user_msg.has_hr_lactic_acid) {
                s_back_user_data.hr_lactic_acid= back_msg_st.user_msg.hr_lactic_acid;
            }
            if(back_msg_st.user_msg.has_body_fat_rate) {
                s_back_user_data.body_fat_rate= back_msg_st.user_msg.body_fat_rate;
            }
            if(back_msg_st.user_msg.has_sport_ftp) {
                s_back_user_data.sport_ftp= back_msg_st.user_msg.sport_ftp;
            }
            if(back_msg_st.user_msg.has_vo2max) {
                s_back_user_data.vo2max= back_msg_st.user_msg.vo2max;
            }

            set_user_info_gender(s_back_user_data.sex);
            // 注意：出生日期 1900/01/01 00:00:00 开始 UTC -2209017943  s_back_user_data.age = utc + 2209017943
            set_user_info_birth(s_back_user_data.age/10000, s_back_user_data.age%10000/100, s_back_user_data.age%10000%100);
            set_user_info_height(s_back_user_data.height); 
            set_user_info_weight(s_back_user_data.weight);

            set_user_info_ftp_ride(s_back_user_data.sport_ftp);
            set_user_info_hrm_max(s_back_user_data.hr_max);
            set_user_info_hrm_resting(s_back_user_data.hr_rest);
            set_user_info_hrm_lactic(s_back_user_data.hr_lactic_acid);
            rt_kprintf("BACK_SERVICE_TYPE_USER_INFO sex = %d age = %d  height = %d weight = %d\r\n",back_msg_st.user_msg.sex,back_msg_st.user_msg.age,back_msg_st.user_msg.height,back_msg_st.user_msg.weight);
            ble_cmd_success_status_tx(back_msg_st.service_type, back_msg_st.back_service_type, back_msg_st.back_operate_type, 0);
            break;
        }
#endif
        default:
            break;
        }
    }
    else
    {
        ble_cmd_err_status_tx(back_msg_st.service_type, back_msg_st.back_service_type, back_msg_st.back_operate_type, 0);
    }
}


//-------------------------------------------------------------------------------------------
// Function Name : weather_data_get_cmd
// Purpose       : 向手机APP发送获取天气命令
// Param[in]     : None
// Param[out]    : None
// Return type   : 
// Comment       : 2019-08-26
//-------------------------------------------------------------------------------------------
void weather_data_get_cmd(void)
{
    if (g_device_get_ble_connect_status())
    {
        ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_BACK, BACK_SERVICE_TYPE_enum_BACK_SERVICE_TYPE_WEATHER,
            BACK_OPERATE_TYPE_enum_BACK_OPERATE_TYPE_GET, 0xff, 0);
    }
    weather_data_sync_request_succ_send();
}

//-------------------------------------------------------------------------------------------
// Function Name : weather_data_update
// Purpose       : 应用层获取天气参数接口,避免界面刷新重复获取，只允许进入界面获取一次
// Param[in]     : flag = true，向APP获取天气  
// Param[out]    : None
// Return type   : back_weather_st
// Comment       : 2019-05-31
//-------------------------------------------------------------------------------------------
back_weather_st* weather_data_update(uint8_t flag)
{
    if (true == flag)
    {
        weather_data_update_reset();
        weather_data_get_cmd();
    }

    if (!is_weather_received)
    {
        return NULL;
    }

    return &s_back_weather_data;
}

void weather_data_update_reset()
{
    is_weather_received = false;
}

/************************************************************************
 *@function:void weather_data_update_callback_register(weather_data_update_callback_t callback);
 *@brief:注册天气更新回调函数
 *@param: callback - 天气更新回调函数
 *@return:null
*************************************************************************/
void weather_data_update_callback_register(weather_data_update_callback_t callback)
{
    g_weather_data_update_callback = callback;
}

/************************************************************************
 *@function:void weather_data_update_callback_unregister(void);
 *@brief:注销天气更新回调函数
 *@param: null
 *@return:null
*************************************************************************/
void weather_data_update_callback_unregister(void)
{
    g_weather_data_update_callback = NULL;
}

/************************************************************************
 *@function:weather_data_update_callback_notify(back_weather_st* weather_data);
 *@brief:通知天气更新
 *@param: back_weather_st* weather_data - 天气数据
 *@return:null
*************************************************************************/
void weather_data_update_callback_notify(back_weather_st* weather_data)
{
    if (NULL != g_weather_data_update_callback)
    {
        g_weather_data_update_callback(weather_data);
    }
}

//-------------------------------------------------------------------------------------------
// Function Name : agps_data_get_cmd
// Purpose       : 主动向APP获取AGPS信息
// Param[in]     : void  
// Param[out]    : None
// Return type   : 
// Comment       : 2021-01-13
//-------------------------------------------------------------------------------------------
void agps_data_get_cmd(void)
{
    PB_BACK_LOG_D("apgs data get cmd!!!!");
    if (g_device_get_ble_connect_status())
    {
#ifdef DEVELOPER_SETTING_EPO_SYNC
        epo_sync_request_start();
#endif
        ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_BACK, BACK_SERVICE_TYPE_enum_BACK_SERVICE_TYPE_EPHEMERIS,
            BACK_OPERATE_TYPE_enum_BACK_OPERATE_TYPE_GET, 0xff, 0);
    }
}

//-------------------------------------------------------------------------------------------
// Function Name : void designated_agps_data_get_cmd(uint8_t sub_op, uint8_t satellite);
// Purpose       : 主动向APP获取指定卫星AGPS信息
// Param[in]     : uint8_t sub_op - 星历文件时段类型（BACK_SUB_OPERATE_TYPE）
// Param[in]     : uint8_t satellite - 指定卫星类型（GPS_TYPE）
// Param[out]    : None
// Return type   : 
//-------------------------------------------------------------------------------------------
void designated_agps_data_get_cmd(uint8_t sub_op, uint8_t satellite)
{
    PB_BACK_LOG_D("designated apgs data get cmd sub_op %d satellite %d!!!!", sub_op, satellite);
    uint8_t sub_op_send = 0;
    if (sub_op == 0)
    {
        sub_op_send = 0xff;
    }
    else
    {
        sub_op_send = sub_op;
    }
    if (g_device_get_ble_connect_status())
    {
#ifdef DEVELOPER_SETTING_EPO_SYNC
        epo_sync_request_start();
#endif
        ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_BACK, BACK_SERVICE_TYPE_enum_BACK_SERVICE_TYPE_EPHEMERIS,
            BACK_OPERATE_TYPE_enum_BACK_OPERATE_TYPE_GET, sub_op, satellite);
    }
}

//-------------------------------------------------------------------------------------------
// Function Name : void designated_agps_data_get_cmd_with_timestamp(uint8_t sub_op, uint8_t satellite, uint32_t timestamp);
// Purpose       : 主动向APP获取指定卫星AGPS信息
// Param[in]     : uint8_t sub_op - 星历文件时段类型（BACK_SUB_OPERATE_TYPE）
// Param[in]     : uint8_t satellite - 指定卫星类型（GPS_TYPE）
// Param[in]     : uint32_t timestamp - 时间戳
// Param[out]    : None
// Return type   : 
//-------------------------------------------------------------------------------------------
void designated_agps_data_get_cmd_with_timestamp(uint8_t sub_op, uint8_t satellite, uint32_t timestamp)
{
    PB_BACK_LOG_D("designated apgs data get cmd sub_op %d satellite %d timestamp %d!!!!", sub_op, satellite, timestamp);
    uint8_t sub_op_send = 0;
    if (sub_op == 0)
    {
        sub_op_send = 0xff;
    }
    else
    {
        sub_op_send = sub_op;
    }
    if (g_device_get_ble_connect_status())
    {
#ifdef DEVELOPER_SETTING_EPO_SYNC
        epo_sync_request_start();
#endif
        ble_cmd_notice_tx_with_timestamp(service_type_index_enum_SERVICE_TYPE_INDEX_BACK, BACK_SERVICE_TYPE_enum_BACK_SERVICE_TYPE_EPHEMERIS,
            BACK_OPERATE_TYPE_enum_BACK_OPERATE_TYPE_GET, sub_op_send, satellite, timestamp);
    }
}

//-------------------------------------------------------------------------------------------
// Function Name : time_data_get_cmd
// Purpose       : 主动向APP获取时间信息
// Param[in]     : void  
// Param[out]    : None
// Return type   : 
// Comment       : 2025-05-09
//-------------------------------------------------------------------------------------------
void time_data_get_cmd(void)
{
    ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_BACK, BACK_SERVICE_TYPE_enum_BACK_SERVICE_TYPE_TIME,
        BACK_OPERATE_TYPE_enum_BACK_OPERATE_TYPE_GET, 0xff, 0);
}

// Function Name : position_data_get_cmd
// Purpose       : 主动向APP获取GPS位置信息
// Param[in]     : void  
// Param[out]    : None
// Return type   : 
// Comment       : 2021-01-13
//-------------------------------------------------------------------------------------------
void position_data_get_cmd(void)
{
    ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_BACK, BACK_SERVICE_TYPE_enum_BACK_SERVICE_TYPE_LOCATE_INFO,
        BACK_OPERATE_TYPE_enum_BACK_OPERATE_TYPE_GET, 0xff, 0);
}

#ifdef DEVELOPER_SETTING_EPO_SYNC

#define EPO_SYNC_TIMER_INTERVAL   (2 * 60 * 1000)
#define EPO_SYNC_REQUEST_WAIT_CNT_MAX   (5)
#define EPO_FILE_NUM              4

static qw_timer epo_sync_timer;
uint8_t epo_sync_cnt = 0;
uint8_t epo_sync_request_wait_cnt = 0;

// 开始申请epo文件同步，恢复已同步文件数量计数
void epo_sync_request_start(void)
{
    epo_sync_cnt = EPO_FILE_NUM;
}

// 每同步成功一个文件，计数减1
void epo_sync_file_add(void)
{
    if (epo_sync_cnt)
    {
        epo_sync_cnt--;
    }
}

static void epo_sync_timer_callback(void *p_context)
{
    static uint8_t sub_op_cnt = 0;
    static uint8_t satellite_cnt = 1;
    uint32_t timestamp = service_datetime_get_gmt_time();
    PB_BACK_LOG_D("conn sta %d, epo_sync_cnt %d, epo_sync_request_wait_cnt %d", g_device_get_ble_connect_status(), epo_sync_cnt, epo_sync_request_wait_cnt);
    if (g_device_get_ble_connect_status())
    {
        // if ((epo_sync_cnt == 0) || (epo_sync_request_wait_cnt >= EPO_SYNC_REQUEST_WAIT_CNT_MAX))
        {
            // 发送EPO同步请求
            // agps_data_get_cmd();
            // designated_agps_data_get_cmd(sub_op_cnt, satellite_cnt);
            designated_agps_data_get_cmd_with_timestamp(sub_op_cnt, satellite_cnt, timestamp);
            epo_sync_request_wait_cnt = 0;
            if (satellite_cnt >= _GPS_TYPE_MAX)
            {
                satellite_cnt = 0;
                if (sub_op_cnt >= _BACK_SUB_OPERATE_TYPE_MAX)
                {
                    sub_op_cnt = 0;
                }
                else
                {
                    sub_op_cnt++;
                }
            }
            else
            {
                satellite_cnt++;
            }
        }
        // else
        // {
        //     epo_sync_request_wait_cnt++;
        // }
    }

}

//-------------------------------------------------------------------------------------------
// Function Name : void epo_sync_test_set(bool state)
// Purpose       : 设置epo文件同步测试
// Param[in]     : bool state - true:开启测试，false:关闭测试
// Param[out]    : None
// Return type   : null
//-------------------------------------------------------------------------------------------
void epo_sync_test_set(bool state)
{
    // 开启定时获取EPO文件
    uint32_t timestamp = service_datetime_get_gmt_time();
    if (state)
    {
        if (g_device_get_ble_connect_status())
        {
            PB_BACK_LOG_D("epo_sync_test_set start cnt %d", epo_sync_cnt);
            if (epo_sync_cnt == 0)
            {
                // 发送EPO同步请求
                // agps_data_get_cmd();
                // designated_agps_data_get_cmd(0, 0);
                designated_agps_data_get_cmd_with_timestamp(0, 0, timestamp);
            }
        }
        qw_timer_init(&epo_sync_timer, QW_TIMER_FLAG_PERIODIC | QW_TIMER_FLAG_SOFT_TIMER, epo_sync_timer_callback);
        qw_timer_start(&epo_sync_timer, EPO_SYNC_TIMER_INTERVAL, NULL, "EPO_SYNC_TIMER");
    }
    else
    {
        qw_timer_stop(&epo_sync_timer);
        epo_sync_request_wait_cnt = 0;
    }
}

#endif
