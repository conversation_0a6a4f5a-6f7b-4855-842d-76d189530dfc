#ifndef NAVI_WAYPOINT_H
#define NAVI_WAYPOINT_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

//路点，仅含经纬度
typedef struct _NaviWaypoint
{
    double lng;
    double lat;
} NaviWaypoint;

//路点，带海拔数据
typedef struct _NaviWaypointA
{
    double lng;
    double lat;
    float alt;
} NaviWaypointA;

//路点，带海拔和距离数据
typedef struct _NaviWaypointAd
{
    double lng;
    double lat;
    float alt;
    float dist;
} NaviWaypointAd;

void navi_waypoint_ad_copy(NaviWaypointAd *self, const NaviWaypointAd *wpad);

void navi_waypoint_ad_reset(NaviWaypointAd *self);

//路点，带海拔、距离和航向数据
typedef struct _NaviWaypointAdc
{
    double lng;
    double lat;
    float alt;
    float dist;
    float course;
} NaviWaypointAdc;

void navi_waypoint_adc_copy(NaviWaypointAdc *self, const NaviWaypointAdc *wpadc);

void navi_waypoint_adc_reset(NaviWaypointAdc *self);

//路点，带距离和航向数据
typedef struct _NaviWaypointDc
{
    double lng;
    double lat;
    float dist;
    float course;
} NaviWaypointDc;

void navi_waypoint_dc_copy(NaviWaypointDc *self, const NaviWaypointDc *wpdc);

#ifdef __cplusplus
}
#endif

#endif