/************************************************************************​
*Copyright(c) 2025, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#ifndef ALG_POWER_ESTIMATION_PORT_H
#define ALG_POWER_ESTIMATION_PORT_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

typedef struct _alg_power_estimation_init
{
    float body_weight;              //体重（kg）
    float bike_weight;              //车重（kg）
} alg_power_estimation_init_t;

typedef struct _alg_power_estimation_input
{
    uint32_t timestamp;             //时间戳（s）
    float grade;                    //坡度，实际值，比如3%的坡，这里就是0.03
    float spd;                      //速度（m/s）
} alg_power_estimation_input_t;

void alg_power_estimation_init(const alg_power_estimation_init_t *init);

float alg_power_estimation_exec(const alg_power_estimation_input_t *input);

#ifdef __cplusplus
}
#endif

#endif