#ifndef NAVI_TMPX_H
#define NAVI_TMPX_H

#ifdef __cplusplus
extern "C" {
#endif

#include "navi_tclm.h"

//用于读取tmp1文件
typedef struct _Tmp1Reader
{
    QW_FIL *fp;                     //文件指针
    uint32_t num;                   //爬坡信息总数
} Tmp1Reader;

//用于写tmp1文件
typedef struct _Tmp1Writer
{
    QW_FIL *fp;                     //文件指针
    uint32_t num;                   //爬坡信息总数
} Tmp1Writer;

//用于读取tmp2文件
typedef struct _Tmp2Reader
{
    QW_FIL *fp;                     //文件指针
    uint32_t num;                   //爬坡点总数
} Tmp2Reader;

//用于写tmp2文件
typedef struct _Tmp2Writer
{
    QW_FIL *fp;                     //文件指针
    uint32_t num;                   //爬坡点总数
} Tmp2Writer;

//导航线路爬坡点缓存（临时文件版本）
typedef struct _NaviTmpRouteCpCache
{
    NaviRouteCpBuf cp_buf;
    Tmp2Reader *tmp2_reader;        //用于从tmp2文件中读取爬坡点
} NaviTmpRouteCpCache;

//导航线路爬坡点list（临时文件版本）
typedef struct _NaviTmpRouteCpList
{
    NaviTmpRouteCpCache cache;
    uint32_t len;
} NaviTmpRouteCpList;

//爬坡类型
typedef enum _NaviClimbType
{
    enumCLIMB_POS = 0,              //上坡
    enumCLIMB_NEG = 1,              //下坡
} NaviClimbType;

//导航线路爬坡点信息
typedef struct _NaviRouteCpInfo
{
    float dist;                     //距离，沿线路从起点算起（m）
    float alt;                      //海拔（m）
    uint32_t idx;                   //爬坡点索引
} NaviRouteCpInfo;

//爬坡信息
typedef struct _NaviClimbInfo
{
    NaviClimbType type;             //上坡/下坡
    NaviRouteCpInfo start;          //上坡/下坡起点信息
    NaviRouteCpInfo end;            //上坡/下坡终点信息
} NaviClimbInfo;

//爬坡信息列表
typedef struct _NaviClimbInfoList
{
    NaviClimbInfo *buf;             //缓冲区，保存爬坡信息
    uint32_t capacity;              //缓冲区容量
    uint32_t len;                   //缓冲区中爬坡数量
} NaviClimbInfoList;

int tmp1_header_read(Tmp1Reader *self, uint8_t *header);

int tmp1_climb_info_read(Tmp1Reader *self, uint32_t idx, NaviClimbInfo *info);

int tmp1_header_write(Tmp1Writer *self);

int tmp1_climb_info_write(Tmp1Writer *self, const NaviClimbInfo *info);

int tmp2_header_read(Tmp2Reader *self, uint8_t *header);

int tmp2_route_cp_read(Tmp2Reader *self, uint32_t start, uint32_t end, uint32_t cp_num, NaviClimbpoint *cp_buf);

int tmp2_header_write(Tmp2Writer *self);

int tmp2_route_cp_write(Tmp2Writer *self, const NaviClimbpoint *cp);

int navi_tmp_route_cp_list_get(NaviTmpRouteCpList *self, uint32_t idx, NaviClimbpoint *output);

void navi_tmp_route_cp_list_reset(NaviTmpRouteCpList *self);

void navi_route_cp_info_copy(NaviRouteCpInfo *self, const NaviRouteCpInfo *info);

void navi_route_cp_info_update(NaviRouteCpInfo *self, float dist, float alt, uint32_t idx);

void navi_climb_info_copy(NaviClimbInfo *self, const NaviClimbInfo *info);

#ifdef __cplusplus
}
#endif

#endif