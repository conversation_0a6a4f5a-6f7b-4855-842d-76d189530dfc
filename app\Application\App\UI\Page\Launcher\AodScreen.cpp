/************************************************************************
* 
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   AodScreen.cpp
@Time    :   2025/04/09 18:58:55
<AUTHOR>   lxin
* 
**************************************************************************/
#include "AodScreen.h"
#include "qw_time_util.h"
#include "service_datetime.h"
#include "MvcApp.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../touchgfx_js/jsApp.h"
#include "../touchgfx_js/js_data_servers.h"
#include "../touchgfx_js/quickjs_user.h"
#include "../touchgfx_js/touchgfx_js_api.h"
static AodScreen* p_instance_ = nullptr;

AodScreen::AodScreen() :
    use_default_(false)
{
    is_screen_visable_ = false;
}

/**
 * @brief 初始化
 */
void AodScreen::init()
{
    p_instance_ = this;
    getRootContainer().setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
}

/**
 * @brief 显示
 */
void AodScreen::show()
{
    bg_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
    bg_.setColor(lv_color_black());
    bool get_dial_container(CacheableContainer **container);
    get_dial_container(&jsContainer_);

    uint32_t dialGoodsId = get_cfg_current_using_dial_goodsid();
    const char *dialType = get_cfg_dial_type(get_using_dial_index());
    if (get_js_support_aod_flag(dialGoodsId) && jsContainer_)
    {   
        get_dial_path(dialGoodsId, dialType, jsPath_);

        if(get_dial_cache_buf() == NULL)
        {
            create_dial_cache_buf();
        }
       
        set_js_aod_mode(jsPath_, true);
        
        js_start_dial_timer();
        if(jsContainer_->getCacheBitmapZoomValue() != 255)
        {
            jsContainer_->setCacheBitmapZoom(255);
        }
        add(*jsContainer_);
        use_default_ = false;
    }
    else
    {
        char data_text[50];

        getRootContainer().add(bg_);
        getRootContainer().add(utc_time_);

        bg_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
        bg_.setColor(lv_color_black());
        
        qw_tm_t tm_time = { 0 };
        time_t tmp = service_datetime_get_gmt_time() + service_datetime_get_timezone();
        service_sec_2_rtctime(tmp, &tm_time);
        sprintf(data_text, "%d:%02d", tm_time.tm_hour, tm_time.tm_min);

        utc_time_.setTextFont(&NUMBER_XL_FONT);
        utc_time_.setTextAlignment(CENTER);
        utc_time_.setColor(lv_color_white());
        utc_time_.setLabelAlpha(LV_OPA_TRANSP);
        utc_time_.setTypedDynamicText(data_text);
        utc_time_.resizeToCurrentTextWithAlignment();
        utc_time_.setAlign(ALIGN_IN_CENTER);

        use_default_ = true;
    }

    is_screen_visable_ = true;
}

/**
 * @brief 隐藏
 */
void AodScreen::hide()
{
    getRootContainer().removeAll();
    uint32_t dialGoodsId = get_cfg_current_using_dial_goodsid();
    if (get_js_support_aod_flag(dialGoodsId))
    {
        set_js_aod_mode(jsPath_, false);
        PageManager *page_manager = MvcApp::get_mvc_app()->getPageManager();
        if(strcmp(page_manager->get_cur_page(),"Launcher") == 0)
        {
            js_start_dial_timer();
        }
        else
        {
            js_free_dial_cache_buf();
        }
    }
    // else if(strcmp(page_manager->get_cur_page(),"SelectDial") == 0)
    // {
    //     js_free_dial_cache_buf();
    // }
    // else
    // {
    //     js_free_dial_cache_buf();
    //     js_stop_dial_timer();
    // }
    is_screen_visable_ = false;
}

/**
 * @brief 更新
 */
void AodScreen::handleTickEvent()
{
    if (is_screen_visable_)
    {
        if (use_default_)
        {
            char data_text[50];

            qw_tm_t tm_time = { 0 };
            time_t tmp = service_datetime_get_gmt_time() + service_datetime_get_timezone();
            service_sec_2_rtctime(tmp, &tm_time);
            sprintf(data_text, "%d:%02d", tm_time.tm_hour, tm_time.tm_min);
            
            utc_time_.setTypedDynamicText(data_text);
            utc_time_.resizeToCurrentTextWithAlignment();
            utc_time_.setAlign(ALIGN_IN_CENTER);
        }
    }
}
