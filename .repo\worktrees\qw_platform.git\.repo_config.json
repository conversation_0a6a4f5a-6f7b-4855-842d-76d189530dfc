{"core.repositoryformatversion": ["1"], "core.filemode": ["false"], "core.symlinks": ["false"], "core.ignorecase": ["true"], "extensions.worktreeconfig": ["true"], "filter.lfs.smudge": ["git-lfs smudge --skip -- %f"], "filter.lfs.process": ["git-lfs filter-process --skip"], "remote.origin.url": ["ssh://yanx<PERSON><PERSON><PERSON>@********:29418/qw_platform"], "remote.origin.review": ["http://********:8081"], "remote.origin.projectname": ["qw_platform"], "remote.origin.fetch": ["+refs/heads/*:refs/remotes/origin/*"]}