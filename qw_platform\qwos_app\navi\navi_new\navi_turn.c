#include <string.h>
#include <stdbool.h>
#include "navi_util.h"
#include "navi_turn.h"

//转向计算
//-1 - 输入无效
//0 - 计算成功，输出有效
//1 - 转向计算中，输出无效
int navi_turn_calculator_exec(NaviTurnCalculator *self, const NaviWaypointDc *wpdc, NaviTurnEx *output)
{
    if (self == NULL || wpdc == NULL || output == NULL)
    {
        return -1;
    }

    //从第三个路点开始才可能有转向
    if (self->cnt < 2)
    {
        if (self->cnt == 0)
        {
            navi_waypoint_dc_copy(&self->last_wp, wpdc);
        }
        else
        {
            navi_waypoint_dc_copy(&self->cur_wp, wpdc);
        }

        self->cnt += 1;
        return 1;
    }

    self->cnt += 1;

    //计算在当前路点发生的转向角度
    const float angle = navi_util_course_angle_calc(self->cur_wp.course, wpdc->course);
    const float dist = self->cur_wp.dist - self->last_wp.dist;

    int32_t cur_dir = 0;

    //转向一个很小的角度，即视为转向开始，累积转向直至转向结束
    if (angle >= 4.0f)
    {
        cur_dir = 1;
    }
    else if (angle <= -4.0f)
    {
        cur_dir = -1;
    }
    else
    {
        cur_dir = 0;
    }

    //本次转向方向（0直行1右转-1左转）和上一次转向（即正在累积/合并的转向）方向相同，则继续累积转向
    if (self->dir == cur_dir)
    {
        if (self->dir != 0)
        {
            //距离较近的连续转向才能累积
            if (dist < 20.0f)
            {
                self->angle_sum += angle;
                navi_waypoint_dc_copy(&self->last_wp, &self->cur_wp);
                navi_waypoint_dc_copy(&self->cur_wp, wpdc);
                return 1;
            }
        }
        else
        {
            //直行不累积
            navi_waypoint_dc_copy(&self->last_wp, &self->cur_wp);
            navi_waypoint_dc_copy(&self->cur_wp, wpdc);
            return 1;
        }
    }

    if (self->dir == 0)
    {
        //之前是直行，现在在转向，则当前路点是转向起点
        navi_waypoint_dc_copy(&self->start, &self->cur_wp);
        self->angle_sum = angle;
        self->dir = cur_dir;
        navi_waypoint_dc_copy(&self->last_wp, &self->cur_wp);
        navi_waypoint_dc_copy(&self->cur_wp, wpdc);
        return 1;
    }

    //当前路点转向发生变更，则当前路点作为新的转向的起点，而上一个路点作为当前转向的终点
    navi_waypoint_dc_copy(&output->start, &self->start);
    navi_waypoint_dc_copy(&output->end, &self->last_wp);
    output->end.course = self->cur_wp.course;
    output->angle = self->angle_sum;

    //开始新的转向
    navi_waypoint_dc_copy(&self->start, &self->cur_wp);
    self->angle_sum = angle;
    self->dir = cur_dir;
    navi_waypoint_dc_copy(&self->last_wp, &self->cur_wp);
    navi_waypoint_dc_copy(&self->cur_wp, wpdc);

    return 0;
}

//转向计算结束，输出最后一个转向（如果存在的话）
//-1 - 输入无效
//0 - 最后一个转向存在，输出有效
//1 - 最后一个转向不存在，输出无效
int navi_turn_calculator_end(NaviTurnCalculator *self, NaviTurnEx *output)
{
    if (self == NULL || output == NULL)
    {
        return -1;
    }

    if (self->dir == 0)
    {
        return 1;
    }

    navi_waypoint_dc_copy(&output->start, &self->start);
    navi_waypoint_dc_copy(&output->end, &self->last_wp);
    output->end.course = self->cur_wp.course;
    output->angle = self->angle_sum;

    return 0;
}

//重置转向计算器
void navi_turn_calculator_reset(NaviTurnCalculator *self)
{
    if (self != NULL)
    {
        self->cnt = 0;
        self->angle_sum = 0.0;
        self->dir = 0;
    }
}

//转向分段计算，所有转向计算完成后，获取最终结果即可，中间结果没有意义
int navi_turn_segmentor_exec(NaviTurnSegmentor *self, const NaviTurn *turn)
{
    if (self == NULL || turn == NULL)
    {
        return -1;
    }

    if (self->cnt == 0)
    {
        self->len = 0;
        dseg_update(&self->seg_buf[self->len].dseg, 0.0f, turn->start_dist);
        self->seg_buf[self->len].range.start = self->cnt;
        self->cnt += 1;
        return 0;
    }

    //更新分段数据
    self->seg_buf[self->len].dseg.dist_max = turn->start_dist;
    self->cnt += 1;

    if (self->cnt < (self->len + 1) * self->interval)
    {
        //尚未完成一个分段，返回即可
        return 0;
    }

    //完成了一个分段
    self->seg_buf[self->len].range.end = self->cnt;
    self->len += 1;

    if (self->len < self->capacity)
    {
        //创建新的分段
        dseg_update(&self->seg_buf[self->len].dseg, turn->start_dist, turn->start_dist);
        self->seg_buf[self->len].range.start = self->cnt;
    }
    else
    {
        //分段数量已经达到最大数量，则两两合并，以减少一半分段数量
        self->interval *= 2;
        const uint32_t merge_num = self->capacity / 2;

        //两两合并
        for (uint32_t i = 0; i < merge_num; i++)
        {
            self->seg_buf[i*2].range.end = self->seg_buf[i*2+1].range.end;
            self->seg_buf[i*2].dseg.dist_max = self->seg_buf[i*2+1].dseg.dist_max;
        }

        //将分段移动到前半部分
        for (uint32_t i = 1; i < merge_num; i++)
        {
            dseg_copy(&self->seg_buf[i].dseg, &self->seg_buf[i*2].dseg);
            range_copy(&self->seg_buf[i].range, &self->seg_buf[i*2].range);
        }

        self->len = merge_num;

        //分段容量为偶数，则两两合并后没有多余的分段，开始新的分段即可
        if (self->capacity % 2 == 0)
        {
            dseg_update(&self->seg_buf[self->len].dseg, turn->start_dist, turn->start_dist);
            self->seg_buf[self->len].range.start = self->cnt;
        }
        else
        {
            //分段容量为奇数，则两两合并后多余一个分段，基于该分段继续进行分段计算
            dseg_copy(&self->seg_buf[self->len].dseg, &self->seg_buf[self->capacity-1].dseg);
            range_copy(&self->seg_buf[self->len].range, &self->seg_buf[self->capacity-1].range);
        }
    }

    return 0;
}

//完成转向分段计算，在所有转向处理完毕后必须调用，以处理最后一个未完成的分段
int navi_turn_segmentor_end(NaviTurnSegmentor *self, float dist_end)
{
    if (self == NULL)
    {
        return -1;
    }

    //将路书终点距离赋给最后一个分段，使得分段能够覆盖整个路书
    if (dist_end > self->seg_buf[self->len].dseg.dist_max)
    {
        self->seg_buf[self->len].dseg.dist_max = dist_end;
    }

    self->seg_buf[self->len].range.end = self->cnt;

    if (self->len > 0)
    {
        const uint32_t cnt = self->seg_buf[self->len].range.end - self->seg_buf[self->len].range.start;
        //最后一个分段不足一半，将最后一个未完成分段合并到上一个分段中，上一个分段必定是完成的
        if (cnt < self->interval / 2)
        {
            self->seg_buf[self->len-1].range.end = self->seg_buf[self->len].range.end;
            self->seg_buf[self->len-1].dseg.dist_max = self->seg_buf[self->len].dseg.dist_max;
        }
        else
        {
            //最后一个分段已经达到一半，则创建一个新的分段
            self->len += 1;
        }
    }
    else
    {
        //如果一个分段都没有完成，那么直接创建一个分段
        self->len = 1;
    }

    return 0;
}

//获取转向分段数据，应该仅在所有分段完成后调用
int navi_turn_segmentor_data_get(NaviTurnSegmentor *self, NaviTurnSegmentArray *output)
{
    if (self == NULL || output == NULL)
    {
        return -1;
    }

    output->len = self->len;
    output->segments = self->seg_buf;

    return 0;
}

//重置转向分段器
void navi_turn_segmentor_reset(NaviTurnSegmentor *self)
{
    if (self != NULL)
    {
        self->len = 0;
        self->cnt = 0;
        self->interval = self->INTERVAL;
    }
}

//转向匹配
int navi_turn_matcher_exec(NaviTurnMatcher *self, float dist, uint8_t is_reverse, NaviTurn *output, uint32_t *idx)
{
    if (self == NULL || output == NULL || idx == NULL)
    {
        return -1;
    }

    const float total_dist = self->seg_array->segments[self->seg_array->len-1].dseg.dist_max;

    if (is_reverse == true)
    {
        dist = total_dist - dist;
    }

    uint32_t seg_idx = 0;
    uint8_t is_find = false;

    //遍历所有分段，找到包含当前距离的分段
    for (uint32_t i = 0; i < self->seg_array->len; i++)
    {
        if (dseg_is_contain(&self->seg_array->segments[i].dseg, dist) == true)
        {
            is_find = true;
            seg_idx = i;
            break;
        }
    }

    if (is_find == false)
    {
        return 1;
    }

    NaviTurn turn = { 0 };

    const uint32_t start = self->seg_array->segments[seg_idx].range.start;
    const uint32_t end = self->seg_array->segments[seg_idx].range.end;

    //从找到的分段中找到下一个转向
    if (is_reverse == true)
    {
        uint8_t is_turn_valid = false;
        uint32_t turn_idx = 0;

        for (uint32_t i = start; i < end; i++)
        {
            if (navi_turn_list_get(self->turn_list, i, &turn) == 0)
            {
                if (turn.end_dist > dist)
                {
                    if (i > 0)
                    {
                        is_turn_valid = true;
                        turn_idx = i - 1;
                    }
                    break;
                }
            }

            if (i == end - 1)
            {
                is_turn_valid = true;
                turn_idx = i;
            }
        }

        if (is_turn_valid == true)
        {
            if (navi_turn_list_get(self->turn_list, turn_idx, &turn) == 0)
            {
                navi_turn_copy(output, &turn);

                const float tmp = output->start_dist;
                output->start_dist = total_dist - output->end_dist;
                output->end_dist = total_dist - tmp;

                output->angle = -output->angle;

                *idx = self->turn_list->len - 1 - turn_idx;

                return 0;
            }
        }
    }
    else
    {
        for (uint32_t i = start; i < end; i++)
        {
            if (navi_turn_list_get(self->turn_list, i, &turn) == 0)
            {
                if (turn.start_dist >= dist)
                {
                    navi_turn_copy(output, &turn);
                    *idx = i;
                    return 0;
                }
            }
        }
    }

    return 1;
}
