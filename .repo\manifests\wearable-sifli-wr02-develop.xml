<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <!--定义默认远端仓库地址-->
  <remote  name="origin"  fetch="."   review="http://10.0.0.3:8081"  autodotgit="true" /> <!--远端gitd地址1-->
  
  <!--定义默认的项目属性-->
  <default remote="origin"  revision="develop"/>
  
  <!-- 定义包含的清单文件 -->

  <!-- sifli sdk -->
  <project path="sifli" name="sifli" />

  <!-- qwos and  qw_app -->
  <project path="qw_platform" name="qw_platform" />

  <!-- application and project  -->
  <project path="app" name="app" />

  <!-- third firmware bin or lib,for example, gps and nordic-->
  <project path="vendor" name="vendor" />

  <!-- tools,for example gps python 、crash parser and ota packet.-->
  <project path="tools" name="tools" />

  <!-- qw_algo  -->
  <project name="qw_algo/navigation" path="qw_algo/navigation" revision="cm_develop"/>

</manifest>
