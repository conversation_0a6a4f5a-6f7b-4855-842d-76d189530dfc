#include "lvgl.h" 

#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP ALIGN(4)
#endif

#ifndef eZIP_RGBARGB888A
#define eZIP_RGBARGB888A
#endif


LV_ATTRIBUTE_MEM_ALIGN_EZIP const  uint8_t hrv_base_example_map[] SECTION(".ROM3_IMG_EZIP.hrv_base_example") = { 
    0x00,0x00,0x00,0x60,0x46,0x08,0x20,0x00,0x00,0x1b,0x00,0x16,0x00,0x00,0x00,0x00,0x00,0x16,0x00,0x01,0x00,0x00,0x00,0x38,0xdd,0x3a,0xb4,0x01,0x18,0x0c,0x81,0x28,
    0x4c,0x98,0x8c,0xd1,0xd8,0x8c,0xd1,0xa8,0xb9,0xa4,0x84,0xda,0xb6,0xe2,0xcf,0x3b,0xf7,0x0c,0xe6,0x33,0x00,0x00,0x00,0x00,0xf5,0xd6,0x4c,0xeb,0x7b,0xa1,0xae,0xd5,
    0xa9,0xce,0xd5,0xa5,0x8e,0xd9,0xf3,0xa6,0x1b,0x7b,0x61,0x0f,0xb3,0x2f,0x86,0x19,0x66,0x98,0x61,0x86,0x19,0x66,0x7f,0x9b,0x1d,0xf3,0x83,0x5c,0x7f,0x1c,0xce,0x4d,
};


#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER ALIGN(4)
#endif
LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER const eZIP_RGBARGB888A  lv_img_dsc_t hrv_base_example SECTION(".ROM3_IMG_EZIP_HEADER.hrv_base_example") = { 
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .header.always_zero = 0,
  .header.w = 27,
  .header.h = 22,
  .data_size  = 96,
  .data = hrv_base_example_map
};
