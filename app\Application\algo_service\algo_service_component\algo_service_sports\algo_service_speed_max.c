﻿/***********************************************************
 * @file algo_service_speed_max.c
 * <AUTHOR> (<EMAIL>)
 * @brief 最大速度算法组件实现
 * @version 0.1
 * @date 2024-11-28
 *
 * @copyright Copyright (c) 2024-2025, Wuhan Qiwu Technology Co., Ltd
 *
***********************************************************/
#include "algo_service_speed_max.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_task.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"

// 输入数据
typedef struct
{
    uint32_t enhanced_speed;         // 速度, 1000 * m/s 32位扩展
    saving_status_e saving_status;   //数据记录的状态
} algo_speed_max_sub_t;

static algo_speed_max_sub_t s_algo_in = {0};

// 发布数据
static algo_speed_max_pub_t s_algo_out = {0};

// 本算法打开标记
static bool s_is_open = false;

/**
 * @brief 算法处理
 *
 * @param algo_out 输出数据
 * @param algo_in 输入数据
 */
static void algo_speed_max_deal(algo_speed_max_pub_t *algo_out, const algo_speed_max_sub_t *algo_in)
{
    if (enum_status_saving == algo_in->saving_status)
    {
        if (algo_out->max_speed < algo_in->enhanced_speed)
        {
            algo_out->max_speed = algo_in->enhanced_speed;
            algo_out->max_pace = (0 == algo_in->enhanced_speed) ? 0 : (uint16_t) (1000000 / algo_in->enhanced_speed);
        }

        if (algo_out->max_speed_lap < algo_in->enhanced_speed)
        {
            algo_out->max_speed_lap = algo_in->enhanced_speed;
            algo_out->max_pace_lap = (0 == algo_in->enhanced_speed) ? 0 : (uint16_t) (1000000 / algo_in->enhanced_speed);
        }
    }
}

/**
 * @brief 算法控制
 *
 * @param algo_out 输出数据
 * @param ctrl_type 控制类型
 */
static void algo_speed_max_ctrl(algo_speed_max_pub_t *algo_out, ctrl_type_e ctrl_type)
{
    if (enum_ctrl_start == ctrl_type)
    {
        algo_out->max_speed = 0;
        algo_out->max_speed_lap = 0;
        algo_out->max_speed_pre_lap = 0;
        algo_out->max_pace = 0;
        algo_out->max_pace_lap = 0;
        algo_out->max_pace_pre_lap = 0;
    }
    else if (enum_ctrl_lap == ctrl_type)
    {
        algo_out->max_speed_pre_lap = algo_out->max_speed_lap;
        algo_out->max_speed_lap = 0;
        algo_out->max_pace_pre_lap = algo_out->max_pace_lap;
        algo_out->max_pace_lap = 0;
    }
}

/**
 * @brief 算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_speed_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_SPEED_MAX;
    head.input_type = DATA_ID_ALGO_SPEED;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法控制订阅处理
 *
 * @param in 控制数据
 * @param len 数据长度
 */
static void algo_sports_ctrl_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_SPEED_MAX;
    head.input_type = DATA_ID_EVENT_SPORTS_CTRL;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_speed_max_out_callback(const void *out, uint32_t len)
{
    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_ALGO_SPEED_MAX, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_speed",
        .topic_id = DATA_ID_ALGO_SPEED,
        .callback = algo_speed_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
};

/**
 * @brief 算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_speed_max_init(void)
{
    algo_speed_max_sub_t *algo_in = &s_algo_in;
    algo_speed_max_pub_t *algo_out = &s_algo_out;

    memset(algo_in, 0, sizeof(algo_speed_max_sub_t));
    memset(algo_out, 0, sizeof(algo_speed_max_pub_t));
    return 0;
}

/**
 * @brief 算法open
 *
 * @return int32_t 结果
 */
static int32_t algo_speed_max_open(void)
{
    int32_t ret = -1;

    if (s_is_open)
    {
        ALGO_COMP_LOG_W("%s already open", __FUNCTION__);
        return 0;
    }

    ALGO_COMP_LOG_D("%s open", __FUNCTION__);

    s_is_open = true;
    algo_speed_max_init();

    return algo_topic_list_subscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
}

/**
 * @brief 算法feed
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_speed_max_feed(uint32_t input_type, void *data, uint32_t len)
{
    algo_speed_max_sub_t *algo_in = &s_algo_in;
    algo_speed_max_pub_t *algo_out = &s_algo_out;

    switch (input_type)
    {
    case DATA_ID_ALGO_SPEED:
    {
        const algo_speed_pub_t *speed_data = (algo_speed_pub_t *) data;
        algo_in->enhanced_speed = speed_data->enhanced_speed;

        //算法处理
        algo_speed_max_deal(algo_out, algo_in);
    }
    break;
    case DATA_ID_EVENT_SPORTS_CTRL:
    {
        const algo_sports_ctrl_t *sports_ctrl = (algo_sports_ctrl_t *) data;
        algo_in->saving_status = sports_ctrl->saving_status;

        //算法控制
        algo_speed_max_ctrl(algo_out, sports_ctrl->ctrl_type);
    }
    break;
    default:
        break;
    }

    //数据发布
    algo_speed_max_out_callback(algo_out, sizeof(algo_speed_max_pub_t));
    return 0;
}

/**
 * @brief 算法close
 *
 * @return int32_t 结果
 */
static int32_t algo_speed_max_close(void)
{
    if (!s_is_open)
    {
        ALGO_COMP_LOG_W("%s already close", __FUNCTION__);
        return 0;
    }

    algo_topic_list_unsubscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));

    s_is_open = false;
    ALGO_COMP_LOG_D("%s close", __FUNCTION__);
    return 0;
}

// 算法组件实现
static algo_compent_ops_t s_speed_max_algo = {
    .init = algo_speed_max_init,
    .open = algo_speed_max_open,
    .feed = algo_speed_max_feed,
    .close = algo_speed_max_close,
    .ioctl = NULL,
};

/**
 * @brief 组件注册
 *
 * @return int32_t 结果
 */
int32_t register_speed_max_algo(void)
{
    algo_compnent_register(ALGO_TYPE_SPEED_MAX, &s_speed_max_algo);
    return 0;
}