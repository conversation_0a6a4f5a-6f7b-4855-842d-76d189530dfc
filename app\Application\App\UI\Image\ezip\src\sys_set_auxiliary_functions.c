#include "lvgl.h" 

#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP ALIGN(4)
#endif

#ifndef eZIP_RGBARGB888A
#define eZIP_RGBARGB888A
#endif


LV_ATTRIBUTE_MEM_ALIGN_EZIP const  uint8_t sys_set_auxiliary_functions_map[] SECTION(".ROM3_IMG_EZIP.sys_set_auxiliary_functions") = { 
    0x00,0x00,0x06,0x74,0x46,0x08,0x20,0x00,0x00,0x48,0x00,0x48,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0x03,0x00,0x00,0x00,0x90,0x00,0x00,0x03,0x54,0x00,0x00,0x05,0x84,
    0x3d,0xf3,0x8b,0x6d,0xaa,0x8e,0xe2,0xf8,0x39,0xbf,0x3b,0x10,0x07,0x75,0xcb,0xa6,0xbd,0xcb,0x1e,0xd0,0xb8,0xee,0x65,0x3c,0x40,0x42,0x14,0xc7,0xc6,0x83,0x80,0x40,
    0x8b,0x60,0x94,0xc8,0x34,0x91,0x37,0x45,0x25,0x31,0x51,0x23,0x06,0x14,0x21,0x01,0xe2,0x48,0xe0,0x55,0x40,0x4c,0x0c,0x09,0xf0,0xc0,0x34,0x41,0xf9,0xd3,0x0e,0xd9,
    0x12,0x12,0x18,0x81,0xc8,0x1e,0xfc,0x13,0x42,0xb2,0x61,0x80,0x87,0x41,0x3b,0x06,0x1b,0x35,0xdb,0x58,0xd2,0xdf,0xf1,0xdc,0x96,0x8e,0xad,0xeb,0x6d,0xbb,0xf6,0xf6,
    0x77,0x6f,0x6b,0x4f,0x72,0x77,0x2f,0x6d,0xef,0xbf,0x0f,0xe7,0x7c,0xcf,0x9f,0x1f,0x44,0x70,0x90,0x0d,0x06,0xbd,0x75,0x9a,0x26,0xe6,0x83,0x8c,0x34,0x10,0xa0,0x07,
    0x80,0xe6,0x02,0x62,0x0d,0x48,0xa8,0x06,0x04,0xf7,0xe3,0x9f,0x11,0x3f,0xf4,0x23,0x02,0xb8,0x0b,0x44,0x7d,0x20,0xe0,0x86,0x24,0xb8,0x06,0x24,0xba,0x87,0x23,0x5a,
    0x57,0x6d,0xed,0xa9,0x61,0x2b,0x9f,0xc9,0x56,0x40,0x43,0xa1,0x95,0x1e,0x21,0x70,0x05,0x48,0x7c,0x95,0x5f,0xb8,0x99,0x3f,0xaa,0xc9,0xf5,0x9a,0x84,0xd8,0xc1,0xe0,
    0x4e,0x8e,0xd2,0x58,0x9b,0xae,0x77,0x06,0x0b,0x0e,0xd0,0x83,0x3b,0xab,0x5e,0xd0,0x66,0x60,0x0b,0x10,0xac,0xe3,0xed,0xa5,0xbc,0xde,0x0c,0xe9,0x08,0x09,0xdc,0x5f,
    0x51,0x1d,0xb8,0xec,0x78,0x40,0x43,0xfd,0x2b,0x57,0xa3,0xc4,0x0f,0x38,0x64,0xde,0x50,0xfd,0x9f,0xc2,0xde,0x79,0x5c,0x68,0xb0,0xd3,0x55,0x1d,0xb8,0xe6,0x38,0x40,
    0xff,0xf6,0xfb,0xde,0x95,0x52,0x7e,0xca,0x60,0x5e,0xb6,0x5b,0xe3,0x10,0xf0,0x1b,0x97,0xdb,0xbf,0xdb,0x11,0x80,0x1e,0xde,0xf5,0xbd,0x0e,0x82,0xbe,0xe2,0xc3,0x46,
    0x27,0x25,0x02,0x22,0xe8,0x24,0x10,0x1b,0x2b,0xf5,0x33,0xff,0xd8,0x02,0x68,0x30,0xb8,0xfa,0x45,0x44,0xf9,0x2d,0x5f,0xb8,0x05,0x9c,0x6b,0x21,0x49,0xf8,0x5e,0xa5,
    0xee,0x3f,0xa7,0x14,0xd0,0x50,0xc8,0xfb,0x09,0x22,0xec,0xe3,0xa0,0x9f,0x09,0x05,0x60,0x1c,0x72,0x2d,0x1c,0x72,0x6d,0x79,0x07,0x14,0xbe,0xe3,0x7d,0x8e,0x34,0x38,
    0xc0,0x87,0xeb,0xa0,0xc0,0x2c,0x1d,0xa4,0x9c,0x01,0x85,0xef,0x79,0x97,0xca,0x08,0xfc,0xc8,0x9e,0xf3,0x3c,0x14,0xa8,0x71,0xb8,0xad,0x30,0x0b,0xb7,0x9c,0x00,0x3d,
    0xec,0xf7,0xbd,0xcf,0xaa,0xf7,0x03,0x14,0xbe,0xb1,0x26,0x89,0xc6,0x64,0xc2,0x2d,0xb2,0xf6,0x9c,0x90,0xf7,0xeb,0x22,0x81,0x63,0x98,0x1b,0x41,0x1e,0x4a,0xf6,0x85,
    0xc8,0x32,0x85,0xb7,0x72,0xf1,0xb5,0xbb,0x48,0xe0,0xc4,0x42,0x09,0x61,0x59,0x38,0xe4,0xdb,0x96,0x73,0x88,0x19,0x70,0xb8,0xbe,0xd9,0x02,0x45,0x6a,0xa8,0xc1,0xbc,
    0x89,0x15,0xb7,0x98,0x76,0x58,0x15,0x31,0x9c,0xa8,0x60,0x47,0x60,0x7b,0x56,0x1e,0x54,0x44,0x82,0x9c,0xbe,0xda,0xd6,0xa0,0x31,0xde,0xe0,0x96,0x65,0x9a,0xca,0x49,
    0xaa,0x83,0x73,0xa1,0xeb,0xfe,0xf8,0xf1,0x92,0xa6,0x2a,0xf5,0x61,0x26,0x69,0x13,0xef,0x2e,0x67,0xe4,0x41,0x46,0x11,0x28,0x05,0xfc,0xae,0xa2,0xce,0x31,0xc0,0xac,
    0x7e,0xf3,0xca,0x94,0xcf,0xb7,0x6e,0xf6,0xf0,0x56,0xaf,0x14,0xd2,0x08,0x8d,0xd5,0x18,0xf3,0x24,0x91,0x81,0xbb,0x1d,0x50,0x01,0xa7,0x75,0x6f,0x4f,0x52,0x38,0xb1,
    0xef,0x7a,0x27,0x79,0x95,0x0a,0x9b,0x85,0x33,0xd7,0xa7,0x15,0x69,0xa3,0xb7,0x52,0xd5,0x3e,0x18,0x10,0x52,0xd9,0x9e,0x7d,0x3d,0xaa,0xf3,0xfe,0xda,0x94,0x80,0xa2,
    0x5d,0x39,0x71,0xe3,0xa9,0x58,0x73,0x52,0xfd,0x46,0xa5,0x17,0x21,0xd1,0xf2,0xbe,0xbe,0x35,0xe5,0xa6,0x22,0x6d,0x8c,0x2c,0x78,0xa7,0xa4,0x2b,0xbf,0x78,0x69,0xc0,
    0x91,0xd9,0xac,0x5c,0x8b,0x34,0x09,0xb3,0x61,0x97,0xca,0x79,0x4e,0xf3,0xe2,0x6a,0x87,0x56,0x8d,0x72,0x61,0xf2,0x10,0x8b,0x4d,0x02,0x95,0x59,0xa6,0xa9,0x5c,0x75,
    0xca,0x17,0x08,0x0d,0x22,0xd9,0x0c,0xd9,0x8e,0x31,0xa9,0x91,0xca,0x73,0xf9,0x3e,0x3f,0x65,0x35,0xd4,0x4d,0xd1,0xa0,0xc7,0x03,0x76,0xe5,0xcf,0x12,0xaf,0x73,0x92,
    0x65,0xb3,0x33,0x27,0x16,0xd9,0x52,0x30,0x32,0x87,0x5a,0x9c,0xb2,0x34,0x43,0xe2,0xb4,0x55,0x99,0x69,0xa2,0xf8,0xc6,0x75,0x26,0x93,0x17,0x8d,0x9f,0x6b,0x9c,0x93,
    0xea,0xf7,0x13,0xb3,0x5a,0xe2,0xbd,0xac,0x00,0xca,0x70,0x46,0x27,0x01,0x7a,0x18,0x5c,0xf5,0x8b,0x15,0xeb,0x56,0x46,0xd1,0x97,0xae,0xae,0x31,0x7b,0x81,0xe6,0xc5,
    0x4f,0x3e,0xbf,0x78,0xe9,0x7e,0xd6,0x65,0x81,0x45,0x5e,0x47,0x65,0x13,0x57,0x3c,0x55,0x2e,0xea,0x99,0xbd,0xa4,0xea,0x8a,0x39,0x9d,0x13,0x89,0x09,0x73,0x90,0x77,
    0x8a,0x29,0x6d,0x5b,0xa5,0x59,0xe3,0x80,0x04,0xe2,0x5b,0x56,0x3e,0x5c,0xac,0xc1,0xf4,0xd8,0x02,0xc6,0x08,0x2f,0xeb,0x56,0x3d,0xa2,0x3d,0xd7,0x4a,0x0f,0x82,0xe8,
    0xc9,0x67,0x23,0x1a,0xd7,0x14,0x2b,0x43,0x28,0xee,0x25,0x86,0x6e,0x59,0x25,0xcc,0x89,0xf6,0x1f,0x00,0x44,0x60,0x0b,0xdf,0x5b,0xb5,0x89,0x24,0x7e,0x07,0x0a,0xed,
    0x42,0xd7,0xfd,0xe8,0xfe,0xe2,0xa5,0x01,0x68,0xdd,0xdb,0x9b,0xd1,0x39,0x67,0x4e,0x2c,0x1a,0x3f,0x5e,0xd2,0x54,0xa5,0xe4,0x39,0xcb,0x8c,0x3f,0x14,0xc1,0xa5,0x80,
    0x2a,0xf1,0x24,0xbe,0x60,0x6f,0x16,0xe7,0xa8,0x31,0x11,0xfd,0x8b,0xd0,0x04,0x25,0x4b,0x0e,0x68,0x30,0xe8,0xad,0xe3,0x7d,0x4d,0x09,0x85,0x09,0x20,0x4d,0x13,0xf3,
    0x4b,0x18,0x4c,0x6d,0x44,0x80,0xa4,0x86,0x12,0x07,0x33,0xa3,0x07,0x82,0x80,0x3c,0x25,0x10,0xa6,0x76,0x93,0x45,0x9a,0xe6,0x96,0x38,0x98,0xf8,0x0f,0xc1,0x2d,0x01,
    0x88,0x25,0x81,0x36,0x35,0xec,0x15,0x40,0x50,0x5d,0x02,0x61,0x96,0xc2,0xf0,0xba,0x51,0x07,0xb9,0x4b,0x24,0x92,0x9b,0xa6,0xc1,0x5f,0xc2,0xee,0x87,0x30,0x5a,0x0d,
    0x67,0x0a,0x10,0x8c,0xcc,0xa9,0xf2,0x47,0x01,0x91,0xd3,0x9e,0x6d,0xeb,0x66,0x4f,0x74,0x33,0xeb,0xdf,0xd4,0xc8,0x0f,0x5c,0x89,0x45,0x19,0xc0,0x23,0x3b,0x61,0x24,
    0x36,0xaa,0x31,0x38,0xf5,0xd1,0xcd,0x5e,0x07,0x82,0xae,0x28,0x20,0x3e,0xb8,0x6b,0xd7,0x43,0xa4,0xf3,0x88,0xc4,0xe6,0x74,0xcf,0xbe,0x1e,0x85,0x29,0x5e,0x9e,0x8f,
    0x35,0xab,0x44,0x7d,0x4e,0xd2,0x9f,0x54,0x9e,0xa3,0x32,0xc4,0x2a,0xf5,0xb3,0x1d,0x31,0x40,0x02,0x6e,0xd8,0x07,0x28,0xf5,0x0b,0x6f,0xf9,0xa2,0xde,0x16,0x48,0x08,
    0xd4,0x36,0x9e,0xe9,0x25,0xc1,0x35,0xa7,0x84,0x58,0xa2,0x30,0x27,0x9b,0xff,0xa8,0xc8,0x7a,0x92,0xf0,0xe4,0x93,0x79,0x10,0x89,0x6e,0x7b,0xc4,0xb9,0x27,0xab,0x21,
    0x59,0x3a,0xaf,0xb3,0xa0,0xbd,0xb8,0x5d,0xa1,0x07,0x8e,0x8d,0x03,0x1a,0x8e,0x68,0x5d,0xce,0x49,0xef,0xf5,0x39,0x0b,0x7b,0xee,0x13,0x44,0x6c,0x9d,0xf4,0xef,0xda,
    0xda,0x53,0xc3,0x84,0xd8,0x61,0xb7,0xfe,0x98,0x8d,0x53,0x55,0xea,0x10,0x67,0xf4,0xab,0x2e,0xdd,0x7f,0x70,0xea,0xc8,0x95,0xe8,0xa4,0xdd,0xfa,0xd3,0xbc,0xb8,0x2a,
    0xe3,0x39,0x74,0xbe,0xd2,0x7d,0x19,0xd0,0x8e,0xa4,0x33,0xe9,0x51,0x1a,0x6b,0x73,0xa2,0xfe,0xa8,0x1d,0xd6,0xd3,0xe1,0xd9,0xee,0x76,0x7f,0x52,0x40,0xba,0xde,0x19,
    0x04,0xa4,0x23,0x4e,0xd7,0x9f,0x7c,0x85,0x18,0x77,0x13,0xfd,0xf8,0x34,0x7e,0x69,0xbe,0xaa,0x61,0xf0,0x13,0xb8,0xdf,0x69,0xfa,0xa3,0x4a,0x87,0x24,0xd1,0xe7,0x2e,
    0x57,0xa0,0x3f,0x25,0xa0,0x8a,0xea,0xc0,0x65,0x16,0xa9,0xe3,0x76,0x78,0x8f,0x99,0xfe,0xa8,0xa8,0x87,0x10,0xf1,0xfb,0x0a,0xbd,0xfd,0x68,0xea,0x75,0xb1,0xf1,0x25,
    0x0e,0xd8,0xa9,0x02,0x88,0xb1,0x42,0x1a,0x2f,0x0a,0xe3,0xcd,0x69,0x26,0x1d,0xfe,0x44,0x60,0x56,0x34,0xb3,0x5c,0x31,0x77,0xcf,0x79,0xd6,0xff,0x71,0x9a,0xf0,0x9b,
    0x6c,0xe1,0x90,0x6f,0x1b,0x01,0xed,0x72,0xe2,0x88,0x26,0x1e,0x56,0x56,0x89,0xb6,0x20,0x6d,0xc1,0x1c,0xfd,0xf4,0x1f,0xd3,0x02,0x64,0xd8,0x50,0xd0,0xdb,0x81,0x08,
    0xcb,0x8a,0x7a,0xda,0x0c,0xf0,0xb6,0xcb,0x1d,0xf8,0x39,0xb3,0xa5,0xe7,0x29,0x05,0x93,0xd8,0xc8,0xbb,0x50,0xd1,0xae,0x56,0x20,0x7c,0x96,0x09,0x1c,0x53,0x0f,0x32,
    0x6c,0x30,0xe8,0x7b,0x4d,0x20,0xfd,0xf6,0x7f,0xf5,0x9c,0x94,0x1e,0x14,0x9b,0x87,0xf8,0xcf,0x21,0x60,0x4b,0xf1,0x80,0xa1,0x6e,0x31,0x43,0x5b,0x30,0x1d,0x38,0x86,
    0xfd,0x07,0x00,0x00,0x45,0x48,0x63,0xe1,0x90,0x6f,0x3d,0x01,0x1d,0x87,0x42,0x36,0x82,0x43,0x2e,0x77,0xe0,0x23,0x44,0x3e,0x9a,0xa6,0x89,0x74,0x3f,0x70,0xb9,0xfd,
    0x6d,0x92,0x70,0x05,0x1f,0x86,0x0a,0x10,0xcc,0x3d,0x22,0xda,0xf0,0x8c,0x1e,0xf8,0x30,0x1b,0x38,0x19,0x01,0x32,0xac,0x52,0xf7,0x9f,0x93,0x24,0x1a,0x89,0xa0,0xb3,
    0x70,0xe8,0xe0,0x61,0xa4,0xc8,0xbc,0x0a,0xbd,0xfd,0x68,0x4e,0x57,0x99,0xee,0x09,0x1c,0x72,0xdb,0x38,0xe4,0x76,0x39,0x98,0xcc,0x55,0x0d,0x68,0xc7,0x6c,0x77,0xbb,
    0xdf,0x12,0xcc,0xd9,0x9c,0x14,0x1e,0xf0,0x36,0xc8,0x08,0x6c,0xe7,0x93,0x5b,0x1c,0x13,0x4d,0x04,0xb7,0x05,0x60,0xab,0x4b,0xf7,0x1f,0xb4,0xd4,0x0f,0x73,0x39,0x79,
    0x68,0xc0,0xfb,0x0a,0x4a,0xda,0x04,0x84,0x1b,0x6c,0x0c,0xa5,0x9f,0x58,0x67,0x7e,0xad,0xd0,0x03,0xc7,0xf2,0x72,0x75,0x2b,0x2e,0x12,0x0c,0x2e,0xd3,0x67,0xe1,0xcc,
    0xf5,0x80,0xb8,0x16,0x89,0x96,0xe7,0x91,0xc6,0x08,0x6f,0x57,0x58,0x6d,0xbb,0x88,0xe4,0xf9,0x4a,0xfd,0x6c,0x47,0xde,0xf1,0x5b,0x7d,0xc1,0xbe,0xbe,0x35,0xe5,0xe5,
    0x5a,0xa4,0x09,0x50,0x2e,0x14,0x08,0x0d,0x20,0xa1,0x8e,0xc1,0xd5,0xf2,0x8d,0x6a,0xf8,0xc5,0x9e,0xca,0xe0,0x9e,0x0c,0x81,0x1e,0xf0,0xfe,0x26,0x87,0xcd,0x2d,0x21,
    0xb0,0x17,0x01,0xaf,0x8f,0x49,0xf9,0x77,0x95,0xde,0xfe,0xa7,0x6a,0xff,0xfc,0x0f,0x52,0xfd,0x71,0x58,
};


#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER ALIGN(4)
#endif
LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER const eZIP_RGBARGB888A  lv_img_dsc_t sys_set_auxiliary_functions SECTION(".ROM3_IMG_EZIP_HEADER.sys_set_auxiliary_functions") = { 
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .header.always_zero = 0,
  .header.w = 72,
  .header.h = 72,
  .data_size  = 1652,
  .data = sys_set_auxiliary_functions_map
};
