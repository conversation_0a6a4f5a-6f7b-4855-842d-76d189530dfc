/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   activity_fit_session.c
@Time    :   2024/12/10 10:25:33
<AUTHOR>   lxin
*
**************************************************************************/
#include "activity_fit_session.h"
#include "activity_fit_app.h"
#include "data_convert.h"
#include "qw_fit_api.h"
#include "subscribe_data.h"
#include "subscribe_data_protocol.h"
#include "thread_pool.h"
#ifndef SIMULATOR
#include "../sport_ability/service_sport_ability.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_task.h"
#include "subscribe_service.h"
#endif
#include "../../qwos_app/sports_data/sports_data_get_string.h"
#include "cfg_header_def.h"
#include "gui_event_service.h"
#include "view_page_model_sports.h"

static bool s_is_activity_fit_session_open = false;

static FIT_SESSION_MESG g_activity_session;                 // session完整缓存
static FIT_SESSION_EXTEND_MESG g_activity_session_extend;   // session extend完整缓存
static msg_sportremind_info_t g_sportremind_info;           // 运动提醒信息

static uint32_t g_session_best_5km_time = 0;                // 5km最佳时间
static uint32_t g_session_best_10km_time = 0;               // 10km最佳时间
static uint32_t g_session_best_half_marathon = 0;           // 半马最佳时间
static uint32_t g_session_best_marathon = 0;                // 全马最佳时间

//CP数据
static critical_power_t s_session_cp;

uint16_t get_session_cp20m(void)
{
    return s_session_cp.cp20m;
}

#ifndef SIMULATOR
/**
 * @brief 订阅算法
 * @param algo 算法名称
 * @param cb 数据处理回调
 * @param cfg 频率配置
 */
static void subscribe_algo(uint32_t algo_id, callback_t cb, optional_config_t *cfg)
{
    if (algo_id == 0 || cb == NULL || cfg == NULL)
    {
        return;
    }

    int32_t ret = qw_dataserver_subscribe_id(algo_id, cb, cfg);
    if (ret != 0)
    {
        ACTIVITY_LOG_E("[activity_fit_session] @%s@ subscribe %d error = %d", __FUNCTION__, algo_id, ret);
    }
    else
    {
        ACTIVITY_LOG_D("[activity_fit_session] @%s@ subscribe %d ok", __FUNCTION__, algo_id);
    }
}

/**
 * @brief 取消订阅算法
 * @param algo 算法名称
 * @param cb 数据处理回调
 */
static void unsubscribe_algo(uint32_t data_id, callback_t cb)
{
    if (data_id == 0 || cb == NULL)
    {
        return;
    }

    int32_t ret = qw_dataserver_unsubscribe_id(data_id, cb);
    if (ret != 0)
    {
        ACTIVITY_LOG_E("[activity_fit_session] @%s@ unsubscribe %d error = %d", __FUNCTION__, data_id, ret);
    }
    else
    {
        ACTIVITY_LOG_D("[activity_fit_session] @%s@ unsubscribe %d ok", __FUNCTION__, data_id);
    }
}
#endif

/**
 * @brief 取时间算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_timer_in_callback(const void *in, uint32_t len)
{
    const algo_timer_pub_t *p_ctrl = (algo_timer_pub_t *) in;

    if (p_ctrl != NULL)
    {
        g_activity_session.timestamp = p_ctrl->timer_total.timestamp;
        g_activity_session.start_time = p_ctrl->timer_total.start_time;
        g_activity_session.total_elapsed_time = p_ctrl->total_elapsed_time;
        g_activity_session.total_timer_time = p_ctrl->timer_total.timer_time;
        g_activity_session.total_moving_time = p_ctrl->timer_total.moving_time;
        g_activity_session.num_laps = p_ctrl->num_laps;
    }
}

/**
 * @brief 取速度均值算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_speed_avg_in_callback(const void *in, uint32_t len)
{
    const algo_speed_avg_pub_t *p_ctrl = (algo_speed_avg_pub_t *) in;

    if (p_ctrl != NULL)
    {
        g_activity_session.enhanced_avg_speed = p_ctrl->avg_speed;
        g_activity_session.avg_speed = p_ctrl->avg_speed > FIT_UINT16_INVALID ? FIT_UINT16_INVALID : (FIT_UINT16) p_ctrl->avg_speed;

        for (uint8_t i = 0; i < FIT_SESSION_MESG_TIME_IN_SPD_ZONE_COUNT; i++)
        {
            g_activity_session.time_in_speed_zone[i] = p_ctrl->time_in_speed_zone[i];
        }
    }
}

/**
 * @brief 取速度极值算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_speed_max_in_callback(const void *in, uint32_t len)
{
    const algo_speed_max_pub_t *p_ctrl = (algo_speed_max_pub_t *) in;

    if (p_ctrl != NULL)
    {
        g_activity_session.enhanced_max_speed = p_ctrl->max_speed;
        g_activity_session.max_speed = p_ctrl->max_speed > FIT_UINT16_INVALID ? FIT_UINT16_INVALID : (FIT_UINT16) p_ctrl->max_speed;
    }
}

/**
 * @brief 取距离算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_distance_in_callback(const void *in, uint32_t len)
{
    const algo_distance_pub_t *p_ctrl = (algo_distance_pub_t *) in;

    if (p_ctrl != NULL)
    {
        g_activity_session.total_distance = p_ctrl->distance;

        if (g_activity_session.total_distance >= 500000 && g_session_best_5km_time == 0)
        {
            g_session_best_5km_time = g_activity_session.total_timer_time;   // 5km最佳时间
        }
        if (g_activity_session.total_distance >= 1000000 && g_session_best_10km_time == 0)
        {
            g_session_best_10km_time = g_activity_session.total_timer_time;   // 10km最佳时间
        }
        if (g_activity_session.total_distance >= 2109000 && g_session_best_half_marathon == 0)
        {
            g_session_best_half_marathon = g_activity_session.total_timer_time;   // 半马最佳时间
        }
        if (g_activity_session.total_distance >= 4219000 && g_session_best_marathon == 0)
        {
            g_session_best_marathon = g_activity_session.total_timer_time;   // 全马最佳时间
        }
    }
}

/**
 * @brief 取心率均值算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_heart_rate_avg_in_callback(const void *in, uint32_t len)
{
    const algo_heart_rate_avg_pub_t *p_ctrl = (algo_heart_rate_avg_pub_t *) in;

    if (p_ctrl != NULL)
    {
        g_activity_session.avg_heart_rate = p_ctrl->avg_heart_rate;
        g_activity_session.max_heart_rate = p_ctrl->max_heart_rate;
        g_activity_session.min_heart_rate = p_ctrl->min_heart_rate;

        for (uint8_t i = 0; i < FIT_SESSION_MESG_TIME_IN_HR_ZONE_COUNT; i++)
        {
            g_activity_session.time_in_hr_zone[i] = p_ctrl->time_in_heart_rate_zone[i];
        }
    }
}

/**
 * @brief 取高度算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_altitude_in_callback(const void *in, uint32_t len)
{
    const algo_altitude_avg_pub_t *p_ctrl = (algo_altitude_avg_pub_t *) in;

    if (p_ctrl != NULL)
    {
        g_activity_session.avg_altitude = UTIL_Altitude_100_2FitAlt(p_ctrl->avg_altitude);
        g_activity_session.max_altitude = UTIL_Altitude_100_2FitAlt(p_ctrl->max_altitude);
        g_activity_session.min_altitude = UTIL_Altitude_100_2FitAlt(p_ctrl->min_altitude);
    }
}

/**
 * @brief 取功率均值算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_pwr_avg_in_callback(const void *in, uint32_t len)
{
    const algo_power_avg_pub_t *p_ctrl = (algo_power_avg_pub_t *) in;

    if (p_ctrl != NULL)
    {
        g_activity_session.avg_power = p_ctrl->avg_power;
        g_activity_session.max_power = p_ctrl->max_power;
        g_activity_session.normalized_power = p_ctrl->normalized_power;
        g_activity_session.training_stress_score = p_ctrl->training_stress_score;
        g_activity_session.intensity_factor = p_ctrl->intensity_factor;
        g_activity_session.left_right_balance = p_ctrl->left_right_balance;
        g_activity_session.avg_left_torque_effectiveness = p_ctrl->avg_left_torque_effectiveness;
        g_activity_session.avg_right_torque_effectiveness = p_ctrl->avg_right_torque_effectiveness;
        g_activity_session.avg_left_pedal_smoothness = p_ctrl->avg_left_pedal_smoothness;
        g_activity_session.avg_right_pedal_smoothness = p_ctrl->avg_right_pedal_smoothness;

        for (uint8_t i = 0; i < FIT_SESSION_MESG_TIME_IN_POWER_ZONE_COUNT; i++)
        {
            g_activity_session.time_in_power_zone[i] = p_ctrl->time_in_power_zone[i];
        }

        // CP
        memcpy(&s_session_cp, &p_ctrl->critical_power, sizeof(s_session_cp));
    }
}

/**
 * @brief 取踏频均值算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_cadence_avg_in_callback(const void *in, uint32_t len)
{
    const algo_cadence_avg_pub_t *p_ctrl = (algo_cadence_avg_pub_t *) in;

    if (p_ctrl != NULL)
    {
        if (FIT_SPORTS_CYCLING <= get_activity_sport_type())   //骑行
        {
            g_activity_session.avg_cadence = p_ctrl->avg_cadence;
            g_activity_session.max_cadence = p_ctrl->max_cadence;

            for (uint8_t i = 0; i < FIT_SESSION_MESG_TIME_IN_CADENCE_ZONE_COUNT; i++)
            {
                g_activity_session.time_in_cadence_zone[i] = p_ctrl->time_in_cadence_zone[i];
            }
        }
    }
}

/**
 * @brief 取坡度均值算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_grade_avg_in_callback(const void *in, uint32_t len)
{
    const algo_grade_avg_pub_t *p_ctrl = (algo_grade_avg_pub_t *) in;

    if (p_ctrl != NULL)
    {
        g_activity_session.total_ascent = p_ctrl->total_ascent;
        g_activity_session.total_descent = p_ctrl->total_descent;
        g_activity_session.avg_grade = p_ctrl->avg_grade;
        g_activity_session.avg_pos_grade = p_ctrl->avg_pos_grade;
        g_activity_session.avg_neg_grade = p_ctrl->avg_neg_grade;
        g_activity_session.max_pos_grade = p_ctrl->max_pos_grade;
        g_activity_session.max_neg_grade = p_ctrl->max_neg_grade;
        g_activity_session.avg_pos_vertical_speed = p_ctrl->avg_pos_vertical_speed;
        g_activity_session.avg_neg_vertical_speed = p_ctrl->avg_neg_vertical_speed;
        g_activity_session.max_pos_vertical_speed = p_ctrl->max_pos_vertical_speed;
        g_activity_session.max_neg_vertical_speed = p_ctrl->max_neg_vertical_speed;
        g_activity_session_extend.pos_distance = p_ctrl->uphill_distance;
        g_activity_session_extend.neg_distance = p_ctrl->downhill_distance;
    }
}

/**
 * @brief 取坐标位置统计算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_position_in_callback(const void *in, uint32_t len)
{
    const algo_position_pub_t *p_ctrl = (algo_position_pub_t *) in;

    if (p_ctrl != NULL)
    {
        util_convert_gps_int_2_semicircles(p_ctrl->start_lat, p_ctrl->start_lon, &g_activity_session.start_position_lat,
                                           &g_activity_session.start_position_long);
        util_convert_gps_int_2_semicircles(p_ctrl->nec_lat, p_ctrl->nec_lon, &g_activity_session.nec_lat, &g_activity_session.nec_long);
        util_convert_gps_int_2_semicircles(p_ctrl->swc_lat, p_ctrl->swc_lon, &g_activity_session.swc_lat, &g_activity_session.swc_long);
    }
}

/**
 * @brief 取骑行卡路里算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_sports_calories_in_callback(const void *in, uint32_t len)
{
    const algo_sports_calories_pub_t *p_ctrl = (algo_sports_calories_pub_t *) in;

    if (p_ctrl != NULL)
    {
        g_activity_session.total_calories = p_ctrl->total_calories;

        // 运动警示
        SPORTTYPE sport_type = get_current_sport_mode();
        uint32_t threshold = get_sport_remind_value(sport_type, SPORT_REMIND_CONSUME, true);                //警示阈值
        invaild_sports_data(&g_sportremind_info.sports_data);
        g_sportremind_info.sports_data.value = g_activity_session.total_calories / threshold * threshold;   //显示整数倍
        g_sportremind_info.sports_data.decimal = 0;

        if (p_ctrl->alert.high_alert_event)
        {
            g_sportremind_info.type = SPORT_CONSUME;
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_SPORT_REMIND, (void *) &g_sportremind_info);
        }
    }
}

/**
 * @brief 取平均跑步动态算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_rd_avg_in_callback(const void *in, uint32_t len)
{
    const algo_rd_avg_pub_t *p_ctrl = (algo_rd_avg_pub_t *) in;

    if (p_ctrl != NULL)
    {
        g_activity_session.avg_vertical_oscillation = p_ctrl->avg_vertical_oscillation;
        g_activity_session.avg_stance_time_percent = p_ctrl->avg_stance_time_percent;
        g_activity_session.avg_stance_time = p_ctrl->avg_ground_contact_time;
        g_activity_session.avg_vertical_ratio = p_ctrl->avg_vertical_ratio;
        g_activity_session.avg_stance_time_balance = p_ctrl->avg_ground_contact_balance;
        g_activity_session.avg_step_length = 0xffff == p_ctrl->avg_step_length ? 0xffff : p_ctrl->avg_step_length * 10;

        g_activity_session_extend.max_step_length = 0xffff == p_ctrl->max_step_length ? 0xffff : p_ctrl->max_step_length * 10;

        if (FIT_SPORTS_CYCLING > get_activity_sport_type())   //跑步
        {
            g_activity_session.total_cycles = p_ctrl->step_count;
            g_activity_session.avg_cadence = p_ctrl->avg_cadence / 2;   //spm to strides/min
            g_activity_session.max_cadence = p_ctrl->max_cadence / 2;   //spm to strides/min
            g_activity_session.avg_fractional_cadence = p_ctrl->avg_cadence % 2 * 64;
            g_activity_session.max_fractional_cadence = p_ctrl->max_cadence % 2 * 64;
        }
        else if (FIT_SPORTS_ELLIPTICAL_MACHINE == get_activity_sport_type()) //椭圆机
        {
            g_activity_session.total_cycles = p_ctrl->step_count;
            g_activity_session.avg_cadence = p_ctrl->avg_cadence;
            g_activity_session.max_cadence = p_ctrl->max_cadence;
        }
    }
}

/**
 * @brief 取跳绳算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_fitness_in_callback(const void *in, uint32_t len)
{
    const algo_count_times_sports_pub_t *p_ctrl = (algo_count_times_sports_pub_t *) in;

    if (p_ctrl != NULL)
    {
        g_activity_session.jump_count = p_ctrl->count;
        g_activity_session.stand_count = p_ctrl->trip;

        g_activity_session_extend.best_combo = p_ctrl->max_combo;
    }
}

/**
 * @brief 取泳池游泳算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_pool_swim_in_callback(const void *in, uint32_t len)
{
    const algo_pool_swim_pub_t *p_ctrl = (algo_pool_swim_pub_t *) in;

    if (p_ctrl != NULL)
    {
        g_activity_session.timestamp = p_ctrl->timestamp;
        g_activity_session.start_time = p_ctrl->start_time;
        g_activity_session.total_elapsed_time = p_ctrl->total_elapsed_time;
        g_activity_session.total_timer_time = p_ctrl->total_timer_time;
        g_activity_session.total_moving_time = p_ctrl->total_moving_time;
        g_activity_session.total_distance = p_ctrl->total_distance;
        g_activity_session.total_cycles = p_ctrl->total_strokes;
        g_activity_session.enhanced_avg_speed = p_ctrl->avg_speed;
        g_activity_session.enhanced_max_speed = p_ctrl->max_speed;
        g_activity_session.avg_speed = p_ctrl->avg_speed;
        g_activity_session.max_speed = p_ctrl->max_speed;
        g_activity_session.message_index = 0;
        g_activity_session.num_lengths = p_ctrl->num_lengths;
        g_activity_session.first_lap_index = 0;
        g_activity_session.avg_swolf = p_ctrl->avg_swolf;
        g_activity_session.avg_strokes_length = p_ctrl->avg_strokes_length;
        g_activity_session.swim_stroke = p_ctrl->swim_stroke;
        g_activity_session.avg_cadence = p_ctrl->avg_cadence;
        g_activity_session.sport = FIT_SPORT_SWIMMING;
        g_activity_session.sub_sport = FIT_SUB_SPORT_LAP_SWIMMING;
        g_activity_session.pool_length = get_sport_swim_value() * 100;
        g_activity_session.pool_length_unit = 0;
        g_activity_session.avg_lap_time = p_ctrl->avg_lap_time;
        g_activity_session.num_laps = p_ctrl->num_laps;
    }
}

/**
 * @brief 取Gomore运动算法结算结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_fitness_save_end_in_callback(const void *in, uint32_t len)
{
#ifndef SIMULATOR
    const lib_gm_summary_outputs_t *summary = lib_gm_summary_outputs_get();

    if (summary != NULL)
    {
        g_activity_session.training_load_peak = (float) summary->trainingLoad;
        g_activity_session.total_training_effect = (float) summary->teAerobic;
        g_activity_session.total_anaerobic_training_effect = (float) summary->teAnaerobic;
    }
    else
    {
        memset(&g_activity_session.training_load_peak, 0xff, sizeof(g_activity_session.training_load_peak));
        memset(&g_activity_session.total_training_effect, 0xff, sizeof(g_activity_session.total_training_effect));
        memset(&g_activity_session.total_anaerobic_training_effect, 0xff, sizeof(g_activity_session.total_anaerobic_training_effect));
    }
#endif
}

///////////////////////////////////////////////////////////////

void activity_session_begin()
{
    if (s_is_activity_fit_session_open)
    {
        ACTIVITY_LOG_D("[activity_fit_session] @%s@ already open", __FUNCTION__);
        return;
    }
    s_is_activity_fit_session_open = true;

    fit_data_init(FIT_MESG_SESSION, &g_activity_session);
    fit_data_init(FIT_MESG_SESSION_EXTEND, &g_activity_session_extend);
    g_activity_session_extend.training_perception = TRAINING_FEEL_MEDIUM;

    g_session_best_5km_time = 0;        // 5km最佳时间
    g_session_best_10km_time = 0;       // 10km最佳时间
    g_session_best_half_marathon = 0;   // 半马最佳时间
    g_session_best_marathon = 0;        // 全马最佳时间

#ifndef SIMULATOR
    optional_config_t config = {
        .sampling_rate = 0,
    };

    if (get_activity_sport_type() == FIT_SPORTS_POOL_SWIMMING)
    {
        subscribe_algo(DATA_ID_ALGO_HEART_RATE_AVG, algo_heart_rate_avg_in_callback, &config);                   // 平均心率
        subscribe_algo(DATA_ID_ALGO_POOL_SWIM, algo_pool_swim_in_callback, &config);                 // 泳池游泳
        subscribe_algo(DATA_ID_ALGO_SPORTS_CALORIES, algo_sports_calories_in_callback, &config);     // 卡路里
        subscribe_algo(DATA_ID_EVENT_FITNESS_SAVE_END, algo_fitness_save_end_in_callback, &config);   //订阅Gomore运动算法结算
    }
    else
    {
        subscribe_algo(DATA_ID_ALGO_TIMER, algo_timer_in_callback, &config);                                     // 时间
        subscribe_algo(DATA_ID_ALGO_SPEED_AVG, algo_speed_avg_in_callback, &config);                             // 平均速度
        subscribe_algo(DATA_ID_ALGO_SPEED_MAX, algo_speed_max_in_callback, &config);                             // 最大速度
        subscribe_algo(DATA_ID_ALGO_DISTANCE, algo_distance_in_callback, &config);                               // 距离
        subscribe_algo(DATA_ID_ALGO_HEART_RATE_AVG, algo_heart_rate_avg_in_callback, &config);                   // 平均心率
        subscribe_algo(DATA_ID_ALGO_ALTITUDE_AVG, algo_altitude_in_callback, &config);                           // 高度
        subscribe_algo(DATA_ID_ALGO_POWER_AVG, algo_pwr_avg_in_callback, &config);                               // 功率
        subscribe_algo(DATA_ID_ALGO_CADENCE_AVG, algo_cadence_avg_in_callback, &config);                         // 踏频
        subscribe_algo(DATA_ID_ALGO_GRADE_AVG, algo_grade_avg_in_callback, &config);                             // 平均坡度
        subscribe_algo(DATA_ID_ALGO_SPORTS_CALORIES, algo_sports_calories_in_callback, &config);     // 卡路里
        subscribe_algo(DATA_ID_EVENT_FITNESS_SAVE_END, algo_fitness_save_end_in_callback, &config);   //订阅Gomore运动算法结算

        if (get_sport_type_is_outdoor((SPORTTYPE) get_activity_sport_type()))
        {
            subscribe_algo(DATA_ID_ALGO_POSITION, algo_position_in_callback, &config);   // 坐标位置统计
        }

        if (FIT_SPORTS_CYCLING > get_activity_sport_type() || FIT_SPORTS_ELLIPTICAL_MACHINE == get_activity_sport_type()) //跑步/椭圆机
        {
            subscribe_algo(DATA_ID_ALGO_RD_AVG, algo_rd_avg_in_callback, &config);   // 平均跑步动态
        }
        if (get_activity_sport_type() == FIT_SPORTS_JUMP_ROPE || get_activity_sport_type() == FIT_SPORTS_STRENGTH_TRAINING
            || get_activity_sport_type() == FIT_SPORTS_ROWING_MACHINE)
        {
            subscribe_algo(DATA_ID_ALGO_JUMPROPE, algo_fitness_in_callback, &config);   // 跳绳等训练
        }
    }
#endif
}

void activity_session_end()
{
    if (!s_is_activity_fit_session_open)
    {
        ACTIVITY_LOG_E("[activity_fit_session] @%s@ already close", __FUNCTION__);
        return;
    }

#ifndef SIMULATOR
    if (get_activity_sport_type() == FIT_SPORTS_POOL_SWIMMING)                                      // 泳池游泳
    {
        unsubscribe_algo(DATA_ID_ALGO_HEART_RATE_AVG, algo_heart_rate_avg_in_callback);                   // 平均心率
        unsubscribe_algo(DATA_ID_ALGO_POOL_SWIM, algo_pool_swim_in_callback);                 // 泳池游泳
        unsubscribe_algo(DATA_ID_ALGO_SPORTS_CALORIES, algo_sports_calories_in_callback);     // 卡路里
        unsubscribe_algo(DATA_ID_EVENT_FITNESS_SAVE_END, algo_fitness_save_end_in_callback);   //Gomore运动算法结算
    }
    else
    {
        unsubscribe_algo(DATA_ID_ALGO_TIMER, algo_timer_in_callback);                                     // 时间
        unsubscribe_algo(DATA_ID_ALGO_SPEED_AVG, algo_speed_avg_in_callback);                             // 平均速度
        unsubscribe_algo(DATA_ID_ALGO_SPEED_MAX, algo_speed_max_in_callback);                             // 最大速度
        unsubscribe_algo(DATA_ID_ALGO_DISTANCE, algo_distance_in_callback);                               // 距离
        unsubscribe_algo(DATA_ID_ALGO_HEART_RATE_AVG, algo_heart_rate_avg_in_callback);                   // 平均心率
        unsubscribe_algo(DATA_ID_ALGO_ALTITUDE_AVG, algo_altitude_in_callback);                           // 高度
        unsubscribe_algo(DATA_ID_ALGO_POWER_AVG, algo_pwr_avg_in_callback);                               // 功率
        unsubscribe_algo(DATA_ID_ALGO_CADENCE_AVG, algo_cadence_avg_in_callback);                         // 踏频
        unsubscribe_algo(DATA_ID_ALGO_GRADE_AVG, algo_grade_avg_in_callback);                             // 平均坡度
        unsubscribe_algo(DATA_ID_ALGO_SPORTS_CALORIES, algo_sports_calories_in_callback);     // 卡路里
        unsubscribe_algo(DATA_ID_EVENT_FITNESS_SAVE_END, algo_fitness_save_end_in_callback);   //Gomore运动算法结算

        if (get_sport_type_is_outdoor((SPORTTYPE) get_activity_sport_type()))
        {
            unsubscribe_algo(DATA_ID_ALGO_POSITION, algo_position_in_callback);   // 坐标位置统计
        }

        if (FIT_SPORTS_CYCLING > get_activity_sport_type() || FIT_SPORTS_ELLIPTICAL_MACHINE == get_activity_sport_type()) //跑步/椭圆机
        {
            unsubscribe_algo(DATA_ID_ALGO_RD_AVG, algo_rd_avg_in_callback);   // 平均跑步动态
        }
        if (get_activity_sport_type() == FIT_SPORTS_JUMP_ROPE || get_activity_sport_type() == FIT_SPORTS_STRENGTH_TRAINING
            || get_activity_sport_type() == FIT_SPORTS_ROWING_MACHINE)
        {
            unsubscribe_algo(DATA_ID_ALGO_JUMPROPE, algo_fitness_in_callback);   // 跳绳等训练
        }
    }
#endif

    s_is_activity_fit_session_open = false;
}

FIT_SESSION_MESG *get_session()
{
    return &g_activity_session;
}

FIT_SESSION_EXTEND_MESG *get_session_extend()
{
    return &g_activity_session_extend;
}

bool is_fit_session_invalid()
{
    return false;   // WR02产品要求保留所有文件 v0.3

    //if (VALIDE_ACTIVITY_DIST > g_activity_session.total_distance ||
    //    VALIDE_ACTIVITY_MOVING_TIME > g_activity_session.total_timer_time)
    //{
    //    return true;
    //}
    //else
    //{
    //    return false;
    //}
}

FIT_SESSION_MESG *get_simulator_session()
{
    g_activity_session.start_position_lat = 305398540;
    g_activity_session.start_position_long = 1143647960;
    g_activity_session.total_distance = 40000;
    g_activity_session.total_cycles = 100;
    g_activity_session.nec_lat = 305398540;
    g_activity_session.nec_long = 1143647960;
    g_activity_session.swc_lat = 305398540;
    g_activity_session.swc_long = 1143647960;
    for (int i = 0; i < FIT_SESSION_MESG_TIME_IN_HR_ZONE_COUNT; i++)
    {
        g_activity_session.time_in_hr_zone[i] = g_activity_session.total_timer_time / FIT_SESSION_MESG_TIME_IN_HR_ZONE_COUNT;
    }
    for (int i = 0; i < FIT_SESSION_MESG_TIME_IN_SPD_ZONE_COUNT; i++)
    {
        g_activity_session.time_in_speed_zone[i] = g_activity_session.total_timer_time / FIT_SESSION_MESG_TIME_IN_SPD_ZONE_COUNT;
    }
    for (int i = 0; i < FIT_SESSION_MESG_TIME_IN_CADENCE_ZONE_COUNT; i++)
    {
        g_activity_session.time_in_cadence_zone[i] = g_activity_session.total_timer_time / FIT_SESSION_MESG_TIME_IN_CADENCE_ZONE_COUNT;
    }
    for (int i = 0; i < FIT_SESSION_MESG_TIME_IN_POWER_ZONE_COUNT; i++)
    {
        g_activity_session.time_in_power_zone[i] = g_activity_session.total_timer_time / FIT_SESSION_MESG_TIME_IN_POWER_ZONE_COUNT;
    }
    g_activity_session.avg_lap_time = g_activity_session.num_laps > 0 ? g_activity_session.total_timer_time / g_activity_session.num_laps : 0;
    g_activity_session.enhanced_avg_speed = 5000;
    g_activity_session.enhanced_max_speed = 5000;
    g_activity_session.total_calories = 5000;
    g_activity_session.avg_speed = 5000;
    g_activity_session.max_speed = 5000;
    g_activity_session.avg_power = 250;
    g_activity_session.max_power = 250;
    g_activity_session.total_ascent = 100;
    g_activity_session.total_descent = 100;
    g_activity_session.first_lap_index = 0;
    g_activity_session.normalized_power = 250;
    g_activity_session.training_stress_score = 10;
    g_activity_session.intensity_factor = 5000;
    g_activity_session.left_right_balance = 0;
    g_activity_session.avg_altitude = 0;
    g_activity_session.max_altitude = 0;
    g_activity_session.avg_grade = 5;
    g_activity_session.avg_pos_grade = 5;
    g_activity_session.avg_neg_grade = 5;
    g_activity_session.max_pos_grade = 5;
    g_activity_session.max_neg_grade = 5;
    g_activity_session.avg_pos_vertical_speed = 50;
    g_activity_session.avg_neg_vertical_speed = 50;
    g_activity_session.max_pos_vertical_speed = 50;
    g_activity_session.max_neg_vertical_speed = 50;
    g_activity_session.min_altitude = 675;
    g_activity_session.avg_vertical_oscillation = 200;
    g_activity_session.avg_stance_time_percent = 150;
    g_activity_session.avg_stance_time = 100;
    g_activity_session.avg_vertical_ratio = 150;
    g_activity_session.avg_stance_time_balance = 150;
    g_activity_session.avg_step_length = 800;
    g_activity_session.jump_count = 100;
    g_activity_session.stand_count = 100;
    g_activity_session.avg_heart_rate = 120;
    g_activity_session.max_heart_rate = 150;
    g_activity_session.avg_cadence = 40;
    g_activity_session.max_cadence = 50;
    g_activity_session.avg_temperature = 25;
    g_activity_session.max_temperature = 25;
    g_activity_session.min_heart_rate = 100;
    g_activity_session.avg_left_torque_effectiveness = 50;
    g_activity_session.avg_right_torque_effectiveness = 50;
    g_activity_session.avg_left_pedal_smoothness = 50;
    g_activity_session.avg_right_pedal_smoothness = 50;

    return &g_activity_session;
}

/**
 * @brief 检查个人纪录 在activity_session_end之前调用
 */
void check_session_for_record()
{
    // USER_ACHIEVEMENTS_TIME,              // 个人纪录类型：最长运动时间 (s)
    // USER_ACHIEVEMENTS_DISTANCE_RIDE,     // 个人纪录类型：最长骑行距离 (cm)
    // USER_ACHIEVEMENTS_DISTANCE_RUN,      // 个人纪录类型：最长跑步距离 (cm)
    // USER_ACHIEVEMENTS_DISTANCE_SWIM,     // 个人纪录类型：最长游泳距离 (cm)
    // USER_ACHIEVEMENTS_AVG_SPEED,         // 个人纪录类型：最高平均速度 (mm/s)
    // USER_ACHIEVEMENTS_AVG_PACE,          // 个人纪录类型：最高平均配速 (s/km)
    // USER_ACHIEVEMENTS_ASCENT,            // 个人纪录类型：最大爬升     (cm)
    // USER_ACHIEVEMENTS_PWR_RIDE,          // 个人纪录类型：最高骑行功率 (W)
    // USER_ACHIEVEMENTS_JUMP_ROPE,         // 个人纪录类型：最高跳绳次数 (次)
    // USER_ACHIEVEMENTS_5KM,               // 个人纪录类型：5km最快时间 (s)
    // USER_ACHIEVEMENTS_10KM,              // 个人纪录类型：10km最快时间 (s)
    // USER_ACHIEVEMENTS_HALF_MARATHON,     // 个人纪录类型：半马最快时间 (s)
    // USER_ACHIEVEMENTS_MARATHON,          // 个人纪录类型：全马最快时间 (s)

    FIT_SPORTS sport_type = get_activity_sport_type();

    update_achievements_data(USER_ACHIEVEMENTS_TIME, fit_save_start_time_get(), g_activity_session.total_timer_time);
    if (sport_type >= FIT_SPORTS_CYCLING && sport_type <= FIT_SPORTS_TRIP_CYCLING)
    {
        if (sport_type != FIT_SPORTS_INDOOR_CYCLING)
        {
            update_achievements_data(USER_ACHIEVEMENTS_ASCENT, fit_save_start_time_get(), g_activity_session.total_ascent);
        }
        update_achievements_data(USER_ACHIEVEMENTS_DISTANCE_RIDE, fit_save_start_time_get(), g_activity_session.total_distance);
        update_achievements_data(USER_ACHIEVEMENTS_PWR_RIDE, fit_save_start_time_get(), g_activity_session.max_power);
        update_achievements_data(USER_ACHIEVEMENTS_AVG_SPEED, fit_save_start_time_get(), g_activity_session.enhanced_avg_speed);
    }
    if (sport_type < FIT_SPORTS_WALKING)
    {
        update_achievements_data(USER_ACHIEVEMENTS_DISTANCE_RUN, fit_save_start_time_get(), g_activity_session.total_distance);
        if (g_activity_session.enhanced_avg_speed != UINT32_MAX && g_activity_session.enhanced_avg_speed > 0)
        {
            update_achievements_data(USER_ACHIEVEMENTS_AVG_PACE, fit_save_start_time_get(), 1000000 / g_activity_session.enhanced_avg_speed);
        }

        if (g_activity_session.total_distance >= 4219000 && g_session_best_marathon != 0)
        {
            update_achievements_data(USER_ACHIEVEMENTS_MARATHON, fit_save_start_time_get(), g_session_best_marathon);
        }
        else if (g_activity_session.total_distance >= 2109000 && g_session_best_half_marathon != 0)
        {
            update_achievements_data(USER_ACHIEVEMENTS_HALF_MARATHON, fit_save_start_time_get(), g_session_best_half_marathon);
        }
        else if (g_activity_session.total_distance >= 1000000 && g_session_best_10km_time != 0)
        {
            update_achievements_data(USER_ACHIEVEMENTS_10KM, fit_save_start_time_get(), g_session_best_10km_time);
        }
        else if (g_activity_session.total_distance >= 500000 && g_session_best_5km_time != 0)
        {
            update_achievements_data(USER_ACHIEVEMENTS_5KM, fit_save_start_time_get(), g_session_best_5km_time);
        }
    }
    if (sport_type >= FIT_SPORTS_POOL_SWIMMING && sport_type <= FIT_SPORTS_OPEN_WATER_SWIMMING)
    {
        update_achievements_data(USER_ACHIEVEMENTS_DISTANCE_SWIM, fit_save_start_time_get(), g_activity_session.total_distance);
    }
    if (sport_type == FIT_SPORTS_JUMP_ROPE)
    {
        update_achievements_data(USER_ACHIEVEMENTS_JUMP_ROPE, fit_save_start_time_get(), g_activity_session.jump_count);
    }

    cfg_save(enum_cfg_achievements);   // 保存用户数据
}