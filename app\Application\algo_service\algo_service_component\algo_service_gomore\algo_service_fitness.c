/**
 * @file algo_service_fitness.c
 * <AUTHOR> (<EMAIL>)
 * @brief fitness算法组件实现
 * @version 0.1
 * @date 2025-1-16
 *
 * @copyright Copyright (c) 2024-2025, <PERSON>han Qiwu Technology Co., Ltd
 *
 */
#include "algo_service_fitness.h"
#include "algo_service_component_log.h"
#include "algo_service_component_common.h"
#include "algo_service_task.h"
#include "algo_service_adapter.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"
#include "lib_gm_common.h"
#include "GoMoreLib.h"

#define CONFIG_QW_ALG_NOTIFIERGPS_ENABLE

// 本算法打开标记
static bool s_is_fitness_open = false;

// 启动运动的类型
static FITNESS_TYPE_E s_fitness_type = 0;

// 启动运动携带值：目前只用于设置游泳池长度
static uint32_t s_fitness_val = 0;

// 启动运动的状态
static sport_status_t s_fitness_sta = SPORT_INVALID;

// 算法输出数据
static algo_fitness_pub_t s_algo_fitness_out = {0};

/**
 * @brief fitness算法用户运动状态事件输入订阅处理
 *
 * @param in 输入数据
 * @param len 输出数据长度
 */
static void algo_fitness_fitness_sta_in_callback(const void *in, uint32_t len)
{
    // 输入数据发布后通知算法线程
    ALGO_COMP_LOG_D("fitness:sta len:%u", len);
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_FITNESS;
    head.input_type = DATA_ID_EVENT_FITNESS_EVENT;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("fitness:sta msg");
    }
}

/**
 * @brief fitness算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_fitness_out_callback(const void *out, uint32_t len)
{
    const algo_fitness_pub_t *algo_fitness = (algo_fitness_pub_t *)out;
    static uint8_t count = 0;
    if (count % LOG_DEBUG_RESAMPLE == 0)
    {
        count = 0;
        ALGO_COMP_LOG_D("fitness:out %u,len:%u\n", algo_fitness->data_type, len);
    }
    count++;

    // 算法输出后发布或则2次处理
    uint32_t real_len = sizeof(algo_fitness_pub_t) - sizeof(fitness_data_t) + algo_fitness->data_len;
    if (qw_dataserver_publish_id(DATA_ID_ALGO_FITNESS, out, real_len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("fitness:pub");
    }
}

/**
 * @brief 设置自动暂停恢复开关
 *
 * @param autoNotifier 自动暂停恢复开关，true:开自动暂停恢复，false:关自动暂停恢复
 * @param wType 运动类型
 */
static void algo_fitness_notifiergps_enable(bool autoNotifier, uint8_t wType)
{
#ifdef CONFIG_QW_ALG_NOTIFIERGPS_ENABLE
    static bool is_gpsworkout_open = false;
    if (wType != WORKOUT_OUTDOOR_CYCLING && wType != WORKOUT_OUTDOOR_RUNNING &&
        wType != WORKOUT_INDOOR_RUNNING && wType != WORKOUT_TRAIL_RUNNING) {
        ALGO_COMP_LOG_E("autoNotifier %d, wType:%d", autoNotifier, wType);
        return;
    }
    if (autoNotifier)
    {
        if (!is_gpsworkout_open)
        {
            structNotifierGPS notifier = {0};
            notifier.setMode = 0;
            notifier.autoNotifier = true;
            int16_t ret = setNotifierGPS(&notifier);
            if (ret != 0) {
                ALGO_COMP_LOG_E("setNotifierGPS enable:%d", ret);
            }
            ALGO_COMP_LOG_I("setNotifierGPS enable setMode:%u,autoNotifier:%u", notifier.setMode, notifier.autoNotifier);
        }
        is_gpsworkout_open = true;
    } else {
        if (is_gpsworkout_open)
        {
            structNotifierGPS notifier = {0};
            notifier.setMode = 0;
            notifier.autoNotifier = false;
            int16_t ret = setNotifierGPS(&notifier);
            if (ret != 0) {
                ALGO_COMP_LOG_E("setNotifierGPS disable:%d", ret);
            }
            ALGO_COMP_LOG_I("setNotifierGPS disable setMode:%u,autoNotifier:%u", notifier.setMode, notifier.autoNotifier);
        }
        is_gpsworkout_open = false;
    }
#endif
}

/**
 * @brief fitness算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_fitness_init(void)
{
    ALGO_COMP_LOG_I("fitness:init ok");
    return 0;
}

/**
 * @brief 打开fitness算法
 *
 * @return int32_t 结果
 */
static int32_t algo_fitness_open(void)
{
    if (s_is_fitness_open)
    {
        ALGO_COMP_LOG_I("fitness:open already");
        return 0;
    }

    int32_t ret = 0;
    do
    {
        ret = lib_gomore_init();
        if (ret != 0)
        {
            ALGO_COMP_LOG_E("fitness:open gm init");
            break;
        }
        lib_start_fitness_session(s_fitness_type, s_fitness_val); // 启动fitness算法会话
        algo_fitness_notifiergps_enable(true, s_fitness_type); // 自动运动恢复暂停长开

        ret = lib_gomore_set_open_map(GM_FITNESS, algo_fitness_out_callback);
        if (ret != 0)
        {
            ALGO_COMP_LOG_E("fitness:open set");
            break;
        }

        // 订阅用户运动启停状态改变事件
        optional_config_t config = {.sampling_rate = 0};
        ret = qw_dataserver_subscribe_id(DATA_ID_EVENT_FITNESS_EVENT, algo_fitness_fitness_sta_in_callback, &config);
        if (ret != 0)
        {
            ALGO_COMP_LOG_E("fitness:open event");
            break;
        }
        s_is_fitness_open = true;
        ALGO_COMP_LOG_I("fitness:open ok");
    } while (0);

    if (ret != 0)
    {
        // fitness算法初始化过程出现问题 通知运动线程 避免运动线程结算时无法收到算法数据导致卡死
        uint8_t data = 0;
        if (qw_dataserver_publish_id(DATA_ID_EVENT_FITNESS_SAVE_END, &data, sizeof(data)) != ERRO_CODE_OK)
        {
            ALGO_COMP_LOG_E("publish CONFIG_QW_EVENT_NAME_FITNESS_SAVE_END error");
        }
    }

    memset(&s_algo_fitness_out, 0, sizeof(s_algo_fitness_out));
    return ret;
}

/**
 * @brief 发布fitness总结数据
 *
 * @param end_out
 */
static void algo_fitness_summary_out_callback(const struct EndFitnessOutput *end_out)
{
    // 复位发布总结数据
    memset(&s_algo_fitness_out, 0, sizeof(s_algo_fitness_out));

    // 设置发布总结数据
    s_algo_fitness_out.sport_type = s_fitness_type;
    s_algo_fitness_out.data_type = FITNESS_SUMMARY_DATA;
    s_algo_fitness_out.data_len = sizeof(fitness_summary_t);
    fitness_summary_t *summary = &s_algo_fitness_out.data.summary;
    if (s_fitness_type == WORKOUT_OUTDOOR_CYCLING || s_fitness_type == WORKOUT_INDOOR_CYCLING)
    {
        // 设置总结骑行数据内容
        memcpy(summary->cyclingLevelOut, end_out->fitness.cyclingLevelOut, sizeof(summary->cyclingLevelOut));
        uint8_t cyclingIndex_len = sizeof(summary->cyclingIndex) > sizeof(end_out->fitness.cyclingIndex) ?
            sizeof(end_out->fitness.cyclingIndex) : sizeof(summary->cyclingIndex);
        memcpy(summary->cyclingIndex, end_out->fitness.cyclingIndex, cyclingIndex_len);
        memcpy(summary->cValueOut, end_out->fitness.cValueOut, sizeof(summary->cValueOut));
        summary->hrEstorOut[1] = end_out->fitness.hrEstorOut[0];
    }
    if (s_fitness_type == WORKOUT_OUTDOOR_RUNNING || s_fitness_type == WORKOUT_INDOOR_RUNNING ||
        s_fitness_type == WORKOUT_TRAIL_RUNNING) {
        // 设置总结跑步数据内容
        memcpy(summary->bestRunTime, end_out->fitness.bestRunTime, sizeof(summary->bestRunTime));
        memcpy(summary->runLevelOut, end_out->fitness.runLevelOut, sizeof(summary->runLevelOut));
        memcpy(summary->personalZone, end_out->fitness.personalZone, sizeof(summary->personalZone));
        memcpy(summary->vValueOut, end_out->fitness.vValueOut, sizeof(summary->vValueOut));
        memcpy(summary->pace, end_out->fitness.pace, sizeof(summary->pace));
        memcpy(summary->efficiency, end_out->fitness.efficiency, sizeof(summary->efficiency));
        summary->hrEstorOut[0] = end_out->fitness.hrEstorOut[0];
    }

    // 设置总结公共数据内容
    summary->tlTrend[0] = end_out->fitness.tlTrend[0];
    summary->recoveryTime[0] = end_out->fitness.recoveryTime[0];
    summary->trainingLoad[0] = end_out->fitness.trainingLoad[0];
    summary->trainingStatus[0] = end_out->fitness.trainingStatus[0];

    algo_fitness_out_callback(&s_algo_fitness_out, sizeof(algo_fitness_pub_t));
}

/**
 * @brief 设置运动自动暂停恢复速度门限
 *
 * @param resume_spd 恢复速度
 * @param stop_spd 暂停速度
 */
static void algo_fitness_set_fitnessnotifier_spd(float resume_spd, float stop_spd)
{
#ifdef CONFIG_QW_ALG_NOTIFIERGPS_ENABLE
    structNotifierGPS notifier = {0};

    // 设置运动暂停速度门限
    notifier.setMode = 1;
    notifier.speedThresPause = stop_spd;
    int16_t ret = setNotifierGPS(&notifier);
    if (ret != 0) {
        ALGO_COMP_LOG_E("setNotifierGPS pause spd %d", ret);
    }
    ALGO_COMP_LOG_I("setNotifierGPS Mode:%u,spdPause:%.4f", notifier.setMode, stop_spd);

    // 设置运动恢复速度门限
    notifier.setMode = 2;
    notifier.speedThresResume = resume_spd;
    ret = setNotifierGPS(&notifier);
    if (ret != 0) {
        ALGO_COMP_LOG_E("setNotifierGPS resume spd %d", ret);
    }
    ALGO_COMP_LOG_I("setNotifierGPS Mode:%u,spdResume:%.4f", notifier.setMode, resume_spd);
#endif
}

/**
 * @brief 主动暂停恢复配置
 *
 * @param is_pause 是否暂停 1:暂停 0:恢复
 */
static void algo_fitness_manual_operate_config(bool is_pause)
{
#ifdef CONFIG_QW_ALG_NOTIFIERGPS_ENABLE
    structNotifierGPS notifier = {0};

    // 设置运动主动暂停和恢复
    notifier.setMode = is_pause ? 5 : 6; // 5:暂停 6:恢复
    int16_t ret = setNotifierGPS(&notifier);
    if (ret != 0) {
        ALGO_COMP_LOG_E("setNotifierGPS pause spd %d", ret);
    }
    ALGO_COMP_LOG_I("setNotifierGPS Mode:%u", notifier.setMode);
#endif
}

/**
 * @brief feedfitness算法
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_fitness_feed(uint32_t input_type, void *data, uint32_t len)
{
    if (!s_is_fitness_open)
    {
        ALGO_COMP_LOG_I("fitness:feed not open");
        return 0;
    }
    if (DATA_ID_EVENT_FITNESS_EVENT == input_type) {
        static float last_spdthres = -1;
        notify_algo_sport *notify = (notify_algo_sport *)data;
        if (notify->sport_status == SPORT_END) {
            struct EndFitnessOutput end_out = {0};
            lib_stop_fitness_session(&end_out, notify->cali_distance);
            ALGO_COMP_LOG_I("fitness:tlTrend:%.2f,recoveryTime:%d,trainingLoad:%u,trainingStatus:%d,cail_dis:%u",
                end_out.fitness.tlTrend[0], end_out.fitness.recoveryTime[0], end_out.fitness.trainingLoad[0],
                end_out.fitness.trainingStatus[0], notify->cali_distance);
            algo_fitness_summary_out_callback(&end_out);
            last_spdthres = -1;
        } else if (notify->sport_status != SPORT_INVALID) {
            // 更新速度门限值
            float spdthres = notify->min_pause_speed;
            ALGO_COMP_LOG_I("fitness:sport_type:%d,sport_status:%d,switch:%u,min_pause_speed:%.4f",
                notify->sport_type, notify->sport_status, notify->atuo_control_swich, notify->min_pause_speed);
            if (spdthres != last_spdthres) {
                last_spdthres = spdthres;
                algo_fitness_set_fitnessnotifier_spd(spdthres, spdthres);
            }
            // 更新运动状态
            if (notify->sport_status == SPORT_PAUSED) {
                algo_fitness_manual_operate_config(true);
            } else if (notify->sport_status == SPORT_CONTINUE) {
                algo_fitness_manual_operate_config(false);
            } else if (notify->sport_status == SPORT_CANCEL) {
                last_spdthres = -1;
            }
        } else {
            ALGO_COMP_LOG_E("fitness:sport_type:%d,sport_status:%d,switch:%u,min_pause_speed:%.4f",
                notify->sport_type, notify->sport_status, notify->atuo_control_swich, notify->min_pause_speed);
        }
        return 0;
    }
    lib_gomore_feed(input_type, data, len);
    return 0;
}

/**
 * @brief 关闭fitness算法
 *
 * @return int32_t 结果
 */
static int32_t algo_fitness_close(void)
{
    if (!s_is_fitness_open)
    {
        ALGO_COMP_LOG_I("fitness:close already");
        return 0;
    }
    s_is_fitness_open = false;
    lib_gomore_reset_open_map(GM_FITNESS);
    algo_fitness_notifiergps_enable(false, s_fitness_type);
    int32_t ret = qw_dataserver_unsubscribe_id(DATA_ID_EVENT_FITNESS_EVENT, algo_fitness_fitness_sta_in_callback);
    if (ret != 0)
    {
        ALGO_COMP_LOG_E("fitness:close event");
    }

    // 复位全局变量
    s_fitness_type = 0;
    s_fitness_val = 0;
    s_fitness_sta = SPORT_INVALID;
    memset(&s_algo_fitness_out, 0, sizeof(algo_fitness_pub_t));
    ALGO_COMP_LOG_I("fitness:close ok");
    return ret;
}

/**
 * @brief fitness算法控制接口
 *
 * @return int32_t 结果
 */
static int32_t algo_fitness_ioctl(algo_config_t *config)
{
    switch (config->type) {
        case ALGO_CONFIG_OPEN:
            {
                optional_config_t *option = (optional_config_t *)config->args;
                s_fitness_type = (FITNESS_TYPE_E)(option->value >> 16);
                s_fitness_val = option->value & 0xFFFF;
                ALGO_COMP_LOG_I("fitness:ioctl type:%d,value:%u", s_fitness_type, s_fitness_val);
            }
            break;
        default:
            return -1;
    }
    return 0;
}

// fitness算法组件实现
static algo_compent_ops_t s_fitness_algo =
{
    .init = algo_fitness_init,
    .open = algo_fitness_open,
    .feed = algo_fitness_feed,
    .close = algo_fitness_close,
    .ioctl = algo_fitness_ioctl,
};

/**
 * @brief fitness组件注册
 *
 * @return int32_t 结果
 */
int32_t register_fitness_algo(void)
{
    return algo_compnent_register(ALGO_TYPE_FITNESS, &s_fitness_algo);
}

#ifdef RT_USING_FINSH
/**
 * @brief fitness算法仿真测试
 *
 * @param argc 参数个数
 * @param argv 参数
 * @return int32_t 错误码
 */
static int32_t algo_fitness_test(int32_t argc, char* argv[])
{
    int32_t ret = -1;
    ALGO_COMP_LOG_I("fitness:test start");
    if (argc < 2)
    {
        ALGO_COMP_LOG_E("Invalid parameter len < 2");
        return 0;
    } else if (strcmp(argv[1], "read") == 0) {
        ALGO_COMP_LOG_I("fitness:test read");
        ret = 0;
    } else if (strcmp(argv[1], "pub") == 0) {
        // 运动数据仿真发布
        ALGO_COMP_LOG_I("fitness:test fitness pub");
        memset(&s_algo_fitness_out, 0, sizeof(s_algo_fitness_out));
        s_algo_fitness_out.data_type = atoi(argv[2]);
        if(FITNESS_SUMMARY_DATA == s_algo_fitness_out.data_type) {
            s_algo_fitness_out.data_len = sizeof(fitness_summary_t);
            fitness_summary_t *summary = &s_algo_fitness_out.data.summary;
            summary->tlTrend[0] = atof(argv[3]);
            summary->recoveryTime[0] = atoi(argv[4]);
            summary->trainingLoad[0] = atoi(argv[5]);
            summary->trainingStatus[0] = atoi(argv[6]);
        } else if (FITNESS_REAKTIME_DATA == s_algo_fitness_out.data_type) {
            struct mFitnessUpdate *mfitness = &s_algo_fitness_out.data.rt_data.fitnessOut.fitness;
            mfitness->aerobic = atof(argv[3]);
            mfitness->anaerobic = atof(argv[4]);
            mfitness->teAerobic = atof(argv[5]);
            mfitness->teAnaerobic = atof(argv[6]);
            mfitness->fitnessStatus = atoi(argv[7]);
            s_algo_fitness_out.data_len = sizeof(fitness_rtdata_t);

        }
        algo_fitness_out_callback(&s_algo_fitness_out, sizeof(algo_fitness_pub_t));
        ret = 0;
    } else {
        ALGO_COMP_LOG_E("Invalid parameter");
    }
    ALGO_COMP_LOG_I("fitness:test end ret=%d", ret);
    return ret;
}
FINSH_FUNCTION_EXPORT(algo_fitness_test, "algo fitness sim");
MSH_CMD_EXPORT(algo_fitness_test, "algo fitness sim");

/**
 * 命令行进行测试应该发送这些
 * 读取运动实时数据值:algo_fitness_test read
 * 运动算法输出数据发布仿真:algo_fitness_test pub data_type(FITNESS_DATA_TYPE) tlTrend recoveryTime trainingLoad trainingStatus
 *     示例 总结数据: algo_fitness_test pub 2 5 6 7 8
 *          实时数据：algo_fitness_test pub 1 5 6 7 8 9
 *
 */
#endif
