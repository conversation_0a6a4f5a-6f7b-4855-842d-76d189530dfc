0000000000000000000000000000000000000000 d82a4057878482e9d29b8d1d2965925c6d69326e yanxuqiang <<EMAIL>> 1752630863 +0800
d82a4057878482e9d29b8d1d2965925c6d69326e d82a4057878482e9d29b8d1d2965925c6d69326e yanxuqiang <<EMAIL>> 1752631103 +0800
d82a4057878482e9d29b8d1d2965925c6d69326e **************************************** yanxuqiang <<EMAIL>> 1752632424 +0800	commit: Sonar: 解决dial模块sonar bugs
**************************************** **************************************** yanxuqiang <<EMAIL>> 1752635883 +0800	commit: Sonar: 解决AppMenu模块sonar bugs
**************************************** fec7bdbf2751b6dcad4a5ca7653b01bc54ee16a5 yanxuqiang <<EMAIL>> 1752829799 +0800	pull --rebase: Fast-forward
fec7bdbf2751b6dcad4a5ca7653b01bc54ee16a5 e4311fa0ed33e694e8aaacbfe247378fcb58999c yanxuqiang <<EMAIL>> 1752830189 +0800	commit: BUG: 18091解决运动跳绳记圈数据类型不对问题
e4311fa0ed33e694e8aaacbfe247378fcb58999c 846edc6d80a8e45fdf4b6f1ec882e9f7ec2e7d0e yanxuqiang <<EMAIL>> 1752830287 +0800	commit: GUI: 优化summary申请内存逻辑，进入页面直接申请三块
846edc6d80a8e45fdf4b6f1ec882e9f7ec2e7d0e 846edc6d80a8e45fdf4b6f1ec882e9f7ec2e7d0e yanxuqiang <<EMAIL>> 1752834162 +0800	reset: moving to HEAD
846edc6d80a8e45fdf4b6f1ec882e9f7ec2e7d0e b1ea3578b92d43f6c28c5d4a5f18d6088e6b94ff yanxuqiang <<EMAIL>> 1752834225 +0800	commit: GUI: 更改UI心率获取接口，最新是显示上一次心率而不是点测心率
b1ea3578b92d43f6c28c5d4a5f18d6088e6b94ff bb084a8ae1305839b1b066c1f151f1b9502ae804 yanxuqiang <<EMAIL>> 1754358289 +0800	pull --rebase: Fast-forward
bb084a8ae1305839b1b066c1f151f1b9502ae804 b565126d6db23f39070839891c239528564a5207 yanxuqiang <<EMAIL>> 1754359345 +0800	commit: GUI: 优化系统表盘4显示效果
b565126d6db23f39070839891c239528564a5207 a104e5f7205370137141f462562380c425f84f1a yanxuqiang <<EMAIL>> 1754386421 +0800	pull --rebase (start): checkout a104e5f7205370137141f462562380c425f84f1a
a104e5f7205370137141f462562380c425f84f1a a104e5f7205370137141f462562380c425f84f1a yanxuqiang <<EMAIL>> 1754386421 +0800	pull --rebase (finish): returning to refs/heads/develop
a104e5f7205370137141f462562380c425f84f1a 68cd4b18e295d90903a47ab32ba43bf123e08744 yanxuqiang <<EMAIL>> 1754386578 +0800	commit (cherry-pick): GUI: 完成V0.85需求
68cd4b18e295d90903a47ab32ba43bf123e08744 889e57301d61c65af423b8299745c77b15fb6ab9 yanxuqiang <<EMAIL>> 1754386626 +0800	commit (amend): GUI: 完成V0.85需求
889e57301d61c65af423b8299745c77b15fb6ab9 3815e2fd62098c538249e755b79c6c311edbd032 yanxuqiang <<EMAIL>> 1754388020 +0800	pull --rebase: Fast-forward
3815e2fd62098c538249e755b79c6c311edbd032 f8ae0a96253eebdef9ff572757e7e4dc40be9a63 yanxuqiang <<EMAIL>> 1754388129 +0800	commit: GUI: 完善查找手表、手机功能
f8ae0a96253eebdef9ff572757e7e4dc40be9a63 7a60493b7237938450efdd83f61cbff8ed363679 yanxuqiang <<EMAIL>> 1754390760 +0800	commit (amend): GUI: 完善查找手表、手机功能
7a60493b7237938450efdd83f61cbff8ed363679 7a60493b7237938450efdd83f61cbff8ed363679 yanxuqiang <<EMAIL>> 1754530142 +0800	reset: moving to HEAD
7a60493b7237938450efdd83f61cbff8ed363679 8187131064f0c57fd2c9f14c27f692e76faf2074 yanxuqiang <<EMAIL>> 1754530147 +0800	pull --rebase: Fast-forward
8187131064f0c57fd2c9f14c27f692e76faf2074 c31b0b1779777e71ccb6e93f95adbc1cad6a96db yanxuqiang <<EMAIL>> 1754531172 +0800	commit: BUG: 解决禅道迭代bug
c31b0b1779777e71ccb6e93f95adbc1cad6a96db c31b0b1779777e71ccb6e93f95adbc1cad6a96db yanxuqiang <<EMAIL>> 1754538755 +0800	reset: moving to HEAD
c31b0b1779777e71ccb6e93f95adbc1cad6a96db 03926308e2fc3510cac4d6dcd1c04cd329dbfd97 yanxuqiang <<EMAIL>> 1754538759 +0800	pull --rebase (start): checkout 03926308e2fc3510cac4d6dcd1c04cd329dbfd97
03926308e2fc3510cac4d6dcd1c04cd329dbfd97 03926308e2fc3510cac4d6dcd1c04cd329dbfd97 yanxuqiang <<EMAIL>> 1754538759 +0800	pull --rebase (finish): returning to refs/heads/develop
03926308e2fc3510cac4d6dcd1c04cd329dbfd97 ec5fd280aaa2fedd42a75b4fe67cc6e119fd29f5 yanxuqiang <<EMAIL>> 1754538799 +0800	commit: BUG: 解决运动结算页记圈页面数据单位显示不正确问题
ec5fd280aaa2fedd42a75b4fe67cc6e119fd29f5 ec5fd280aaa2fedd42a75b4fe67cc6e119fd29f5 yanxuqiang <<EMAIL>> 1754556437 +0800	reset: moving to HEAD
ec5fd280aaa2fedd42a75b4fe67cc6e119fd29f5 20b46e4b252c1db2eace2233e16d90f45fad9a71 yanxuqiang <<EMAIL>> 1754556441 +0800	pull --rebase (start): checkout 20b46e4b252c1db2eace2233e16d90f45fad9a71
20b46e4b252c1db2eace2233e16d90f45fad9a71 20b46e4b252c1db2eace2233e16d90f45fad9a71 yanxuqiang <<EMAIL>> 1754556441 +0800	pull --rebase (finish): returning to refs/heads/develop
20b46e4b252c1db2eace2233e16d90f45fad9a71 00b357c811b69e4b9c80d603dcbb015d5d3e67f8 yanxuqiang <<EMAIL>> 1754556502 +0800	commit: BUG: 19613解决gps高度校准成功，弹出弹窗不会熄屏问题
00b357c811b69e4b9c80d603dcbb015d5d3e67f8 00b357c811b69e4b9c80d603dcbb015d5d3e67f8 yanxuqiang <<EMAIL>> 1754567886 +0800	reset: moving to HEAD
00b357c811b69e4b9c80d603dcbb015d5d3e67f8 163782a90b374bab1e39b5e3284f342dd930295a yanxuqiang <<EMAIL>> 1754567892 +0800	pull --rebase (start): checkout 163782a90b374bab1e39b5e3284f342dd930295a
163782a90b374bab1e39b5e3284f342dd930295a 163782a90b374bab1e39b5e3284f342dd930295a yanxuqiang <<EMAIL>> 1754567892 +0800	pull --rebase (finish): returning to refs/heads/develop
163782a90b374bab1e39b5e3284f342dd930295a 3ae663ce3be627dd86025f348a7e37147a68dcb7 yanxuqiang <<EMAIL>> 1754567929 +0800	commit: GUI: 替换找手表，找手机gif图片
3ae663ce3be627dd86025f348a7e37147a68dcb7 3ae663ce3be627dd86025f348a7e37147a68dcb7 yanxuqiang <<EMAIL>> 1754633767 +0800	reset: moving to HEAD
3ae663ce3be627dd86025f348a7e37147a68dcb7 d45f770cd56d191ace1da63d7a85fe883bdc8907 yanxuqiang <<EMAIL>> 1754633778 +0800	pull --rebase (start): checkout d45f770cd56d191ace1da63d7a85fe883bdc8907
d45f770cd56d191ace1da63d7a85fe883bdc8907 d45f770cd56d191ace1da63d7a85fe883bdc8907 yanxuqiang <<EMAIL>> 1754633778 +0800	pull --rebase (finish): returning to refs/heads/develop
d45f770cd56d191ace1da63d7a85fe883bdc8907 bdc831d2c967fb5216c7e4d20339bca81282eec2 yanxuqiang <<EMAIL>> 1754635858 +0800	commit: BUG: 19495解决睡眠进度条和设计稿不一致问题
bdc831d2c967fb5216c7e4d20339bca81282eec2 bdc831d2c967fb5216c7e4d20339bca81282eec2 yanxuqiang <<EMAIL>> 1754643382 +0800	reset: moving to HEAD
bdc831d2c967fb5216c7e4d20339bca81282eec2 a4b86342945bbaf2e7b8b236b9057871a1501d84 yanxuqiang <<EMAIL>> 1754643388 +0800	pull --rebase (start): checkout a4b86342945bbaf2e7b8b236b9057871a1501d84
a4b86342945bbaf2e7b8b236b9057871a1501d84 a4b86342945bbaf2e7b8b236b9057871a1501d84 yanxuqiang <<EMAIL>> 1754643388 +0800	pull --rebase (finish): returning to refs/heads/develop
a4b86342945bbaf2e7b8b236b9057871a1501d84 a4b86342945bbaf2e7b8b236b9057871a1501d84 yanxuqiang <<EMAIL>> 1754649940 +0800	reset: moving to HEAD
a4b86342945bbaf2e7b8b236b9057871a1501d84 980ec9c9e2e89329514f9268c54dc76c2109796b yanxuqiang <<EMAIL>> 1754649945 +0800	pull --rebase: Fast-forward
980ec9c9e2e89329514f9268c54dc76c2109796b c82df4878d9e6582002e7c377252575ecd2aaf07 yanxuqiang <<EMAIL>> 1754649991 +0800	commit: GUI: 优化运动结算页记圈页面Ui显示，如果没有单位则不显示-，优化表盘脚本
c82df4878d9e6582002e7c377252575ecd2aaf07 090a6c5fefdbd324d78f55a89680f2b71be23860 yanxuqiang <<EMAIL>> 1754650042 +0800	commit (amend): GUI: 优化运动结算页记圈页面Ui显示，如果没有单位则不显示-
090a6c5fefdbd324d78f55a89680f2b71be23860 d8e96beff4eb95afc0b2b81342d2c0b0fe217a6a yanxuqiang <<EMAIL>> 1754650118 +0800	commit (amend): GUI: 优化运动结算页记圈页面Ui显示，如果没有单位则不显示-
d8e96beff4eb95afc0b2b81342d2c0b0fe217a6a d8e96beff4eb95afc0b2b81342d2c0b0fe217a6a yanxuqiang <<EMAIL>> 1754884546 +0800	reset: moving to HEAD
d8e96beff4eb95afc0b2b81342d2c0b0fe217a6a db16a2eaeb7a707b65819bafbb3557c07f1915b8 yanxuqiang <<EMAIL>> 1754884549 +0800	pull --rebase: Fast-forward
db16a2eaeb7a707b65819bafbb3557c07f1915b8 6366d6e7619238b7ac23caa9cb74f61702b4816f yanxuqiang <<EMAIL>> 1754884580 +0800	commit: GUI: 优化表盘存储缩略图逻辑
6366d6e7619238b7ac23caa9cb74f61702b4816f 6366d6e7619238b7ac23caa9cb74f61702b4816f yanxuqiang <<EMAIL>> 1754969383 +0800	reset: moving to HEAD
6366d6e7619238b7ac23caa9cb74f61702b4816f b4ac3d293c32bcbbc8bfd0d42237ee9eae3a40ea yanxuqiang <<EMAIL>> 1754969387 +0800	pull --rebase: Fast-forward
b4ac3d293c32bcbbc8bfd0d42237ee9eae3a40ea 107e3d3a1cff83b6a23ac6714a60011fe3ae14b4 yanxuqiang <<EMAIL>> 1754969404 +0800	commit: GUI: 优化表盘存储逻辑,直接把已经显示的表盘buf存起来，不需要再去重新创建
107e3d3a1cff83b6a23ac6714a60011fe3ae14b4 05ac2a2a0ce9cdb7cb6de9bb385602fca52aaf4e yanxuqiang <<EMAIL>> 1754969412 +0800	commit (amend): GUI: 优化表盘存储逻辑,直接把已经显示的表盘buf存起来
05ac2a2a0ce9cdb7cb6de9bb385602fca52aaf4e b50df81d9a60252053aa67995a5a4a87a3fbf711 yanxuqiang <<EMAIL>> 1755001686 +0800	commit (amend): GUI: 优化表盘存储逻辑
b50df81d9a60252053aa67995a5a4a87a3fbf711 c3e69f52830325dd7fcfd138dc7bb3cb91986bc3 yanxuqiang <<EMAIL>> 1755002098 +0800	pull --rebase (start): checkout c3e69f52830325dd7fcfd138dc7bb3cb91986bc3
c3e69f52830325dd7fcfd138dc7bb3cb91986bc3 98acbbff0237d72e9fec2f4130d0a648cddfc02b yanxuqiang <<EMAIL>> 1755002098 +0800	pull --rebase (pick): GUI: 优化表盘存储逻辑
98acbbff0237d72e9fec2f4130d0a648cddfc02b 98acbbff0237d72e9fec2f4130d0a648cddfc02b yanxuqiang <<EMAIL>> 1755002098 +0800	pull --rebase (finish): returning to refs/heads/develop
98acbbff0237d72e9fec2f4130d0a648cddfc02b 07e853b0ebc69475d8aa13e23aad53cffef01ca4 yanxuqiang <<EMAIL>> 1755071931 +0800	commit (amend): GUI: 优化表盘存储逻辑
07e853b0ebc69475d8aa13e23aad53cffef01ca4 bc3d94b2edda3fff68eb35a9273e025dba3b9552 yanxuqiang <<EMAIL>> 1755072114 +0800	pull --rebase (start): checkout bc3d94b2edda3fff68eb35a9273e025dba3b9552
bc3d94b2edda3fff68eb35a9273e025dba3b9552 59ac6aa1ab8aae4be47db213eb6717fc314aeba0 yanxuqiang <<EMAIL>> 1755072114 +0800	pull --rebase (pick): GUI: 优化表盘存储逻辑
59ac6aa1ab8aae4be47db213eb6717fc314aeba0 59ac6aa1ab8aae4be47db213eb6717fc314aeba0 yanxuqiang <<EMAIL>> 1755072114 +0800	pull --rebase (finish): returning to refs/heads/develop
59ac6aa1ab8aae4be47db213eb6717fc314aeba0 59ac6aa1ab8aae4be47db213eb6717fc314aeba0 yanxuqiang <<EMAIL>> 1755078860 +0800	reset: moving to HEAD
59ac6aa1ab8aae4be47db213eb6717fc314aeba0 e0238cab998600f31390ae3f9a77fc36021d2c13 yanxuqiang <<EMAIL>> 1755078865 +0800	pull --rebase (start): checkout e0238cab998600f31390ae3f9a77fc36021d2c13
e0238cab998600f31390ae3f9a77fc36021d2c13 23c20ce982554ff1d92d2fd989f7790092dc3268 yanxuqiang <<EMAIL>> 1755078866 +0800	pull --rebase (pick): GUI: 优化表盘存储逻辑
23c20ce982554ff1d92d2fd989f7790092dc3268 23c20ce982554ff1d92d2fd989f7790092dc3268 yanxuqiang <<EMAIL>> 1755078866 +0800	pull --rebase (finish): returning to refs/heads/develop
23c20ce982554ff1d92d2fd989f7790092dc3268 d7df5defb9df148a4522713c8b43a0896a79fcf6 yanxuqiang <<EMAIL>> 1755154020 +0800	commit (amend): GUI: 优化表盘存储逻辑
d7df5defb9df148a4522713c8b43a0896a79fcf6 7d9d2de79162fd79f3a6185405d2d53c5eea7c72 yanxuqiang <<EMAIL>> 1755226705 +0800	commit (amend): GUI: 优化表盘存储逻辑
7d9d2de79162fd79f3a6185405d2d53c5eea7c72 c1b54f4e09dd85eed1f999c1b4c5591298245e19 yanxuqiang <<EMAIL>> 1755228485 +0800	pull --rebase (start): checkout c1b54f4e09dd85eed1f999c1b4c5591298245e19
c1b54f4e09dd85eed1f999c1b4c5591298245e19 c1b54f4e09dd85eed1f999c1b4c5591298245e19 yanxuqiang <<EMAIL>> 1755228485 +0800	pull --rebase (finish): returning to refs/heads/develop
c1b54f4e09dd85eed1f999c1b4c5591298245e19 25a810a96859f5e6804733124fc7b26738b70087 yanxuqiang <<EMAIL>> 1755228575 +0800	checkout: moving from develop to wr02_release
25a810a96859f5e6804733124fc7b26738b70087 e30e300ba32415e068b3532a2aadcabffcedbefb yanxuqiang <<EMAIL>> 1755228648 +0800	checkout: moving from wr02_release to FETCH_HEAD
e30e300ba32415e068b3532a2aadcabffcedbefb 25a810a96859f5e6804733124fc7b26738b70087 yanxuqiang <<EMAIL>> 1755228833 +0800	checkout: moving from e30e300ba32415e068b3532a2aadcabffcedbefb to wr02_release
25a810a96859f5e6804733124fc7b26738b70087 25a810a96859f5e6804733124fc7b26738b70087 yanxuqiang <<EMAIL>> 1755228963 +0800	reset: moving to 25a810a96859f5e6804733124fc7b26738b70087
25a810a96859f5e6804733124fc7b26738b70087 5f8eed38dff103b53bb50e509f83ce9871ecd1f9 yanxuqiang <<EMAIL>> 1755229033 +0800	commit: GUI: 完成V0.85迭代需求
5f8eed38dff103b53bb50e509f83ce9871ecd1f9 1433ab379ad5f1fa85c0fdffd0c0165ebc7c266b yanxuqiang <<EMAIL>> 1755229043 +0800	commit (amend): GUI: 完成V0.85迭代需求
1433ab379ad5f1fa85c0fdffd0c0165ebc7c266b 54f85a83fee8b58bc68ee9792d8e70487248db6c yanxuqiang <<EMAIL>> 1755237091 +0800	pull --rebase (start): checkout 54f85a83fee8b58bc68ee9792d8e70487248db6c
54f85a83fee8b58bc68ee9792d8e70487248db6c 54f85a83fee8b58bc68ee9792d8e70487248db6c yanxuqiang <<EMAIL>> 1755237091 +0800	pull --rebase (finish): returning to refs/heads/wr02_release
54f85a83fee8b58bc68ee9792d8e70487248db6c 54f85a83fee8b58bc68ee9792d8e70487248db6c yanxuqiang <<EMAIL>> 1755238345 +0800	reset: moving to 54f85a83fee8b58bc68ee9792d8e70487248db6c
54f85a83fee8b58bc68ee9792d8e70487248db6c 49124dab57e9624386208797922a9bdcfa55853a yanxuqiang <<EMAIL>> 1755240072 +0800	commit: GUI: 完成V0.85需求
49124dab57e9624386208797922a9bdcfa55853a 1b1b43d62591e12b84e8f9cadec29cef591af681 yanxuqiang <<EMAIL>> 1755240080 +0800	commit (amend): GUI: 完成V0.85需求
1b1b43d62591e12b84e8f9cadec29cef591af681 42f3ee9d4a6f57613709114cb0f83585c4b7e09b yanxuqiang <<EMAIL>> 1755253119 +0800	pull --rebase (start): checkout 42f3ee9d4a6f57613709114cb0f83585c4b7e09b
42f3ee9d4a6f57613709114cb0f83585c4b7e09b 42f3ee9d4a6f57613709114cb0f83585c4b7e09b yanxuqiang <<EMAIL>> 1755253119 +0800	pull --rebase (finish): returning to refs/heads/wr02_release
42f3ee9d4a6f57613709114cb0f83585c4b7e09b 42f3ee9d4a6f57613709114cb0f83585c4b7e09b yanxuqiang <<EMAIL>> 1755307448 +0800	reset: moving to 42f3ee9d4a6f57613709114cb0f83585c4b7e09b
42f3ee9d4a6f57613709114cb0f83585c4b7e09b 5cc4e6910ef6cbe6c90beb581f42b64fff88db5d yanxuqiang <<EMAIL>> 1755307464 +0800	pull --rebase: Fast-forward
5cc4e6910ef6cbe6c90beb581f42b64fff88db5d 5cc4e6910ef6cbe6c90beb581f42b64fff88db5d yanxuqiang <<EMAIL>> 1755307678 +0800	reset: moving to HEAD
5cc4e6910ef6cbe6c90beb581f42b64fff88db5d 5cc4e6910ef6cbe6c90beb581f42b64fff88db5d yanxuqiang <<EMAIL>> 1755307735 +0800	reset: moving to 5cc4e6910ef6cbe6c90beb581f42b64fff88db5d
5cc4e6910ef6cbe6c90beb581f42b64fff88db5d 5cc4e6910ef6cbe6c90beb581f42b64fff88db5d yanxuqiang <<EMAIL>> 1755307766 +0800	reset: moving to HEAD
5cc4e6910ef6cbe6c90beb581f42b64fff88db5d 5cc4e6910ef6cbe6c90beb581f42b64fff88db5d yanxuqiang <<EMAIL>> 1755307826 +0800	reset: moving to 5cc4e6910ef6cbe6c90beb581f42b64fff88db5d
5cc4e6910ef6cbe6c90beb581f42b64fff88db5d 2f9f5f3a631ef32e9dc07e595e2047ea07600972 yanxuqiang <<EMAIL>> 1755309981 +0800	commit: GUI: 完善查找手表，手机功能
2f9f5f3a631ef32e9dc07e595e2047ea07600972 23eb63f6ee1e1b98c8804e2f9fd9916edefd577b yanxuqiang <<EMAIL>> 1755323587 +0800	pull --rebase (start): checkout 23eb63f6ee1e1b98c8804e2f9fd9916edefd577b
23eb63f6ee1e1b98c8804e2f9fd9916edefd577b 23eb63f6ee1e1b98c8804e2f9fd9916edefd577b yanxuqiang <<EMAIL>> 1755323623 +0800	rebase (finish): returning to refs/heads/wr02_release
23eb63f6ee1e1b98c8804e2f9fd9916edefd577b a54eb8c5d352978157162e8bcab3d9c2a43c3d18 yanxuqiang <<EMAIL>> 1755325646 +0800	pull --rebase: Fast-forward
a54eb8c5d352978157162e8bcab3d9c2a43c3d18 a54eb8c5d352978157162e8bcab3d9c2a43c3d18 yanxuqiang <<EMAIL>> 1755329488 +0800	reset: moving to HEAD
a54eb8c5d352978157162e8bcab3d9c2a43c3d18 647d6bd68718c717988f59f9cb0e55c458bb45ed yanxuqiang <<EMAIL>> 1755329493 +0800	pull --rebase: Fast-forward
647d6bd68718c717988f59f9cb0e55c458bb45ed c1b54f4e09dd85eed1f999c1b4c5591298245e19 yanxuqiang <<EMAIL>> 1755566761 +0800	checkout: moving from wr02_release to develop
c1b54f4e09dd85eed1f999c1b4c5591298245e19 aed5f4f623c7af318ef2767905cbb37011cd76a9 yanxuqiang <<EMAIL>> 1755566774 +0800	pull --rebase: Fast-forward
aed5f4f623c7af318ef2767905cbb37011cd76a9 aed5f4f623c7af318ef2767905cbb37011cd76a9 yanxuqiang <<EMAIL>> 1755572449 +0800	reset: moving to HEAD
aed5f4f623c7af318ef2767905cbb37011cd76a9 28d129e85117b03aaeefbc6455d26351bd992aae yanxuqiang <<EMAIL>> 1755572453 +0800	pull --rebase: Fast-forward
28d129e85117b03aaeefbc6455d26351bd992aae 8fc654b7e630cc01bb3adaf1e819f23d796d9e63 yanxuqiang <<EMAIL>> 1755572522 +0800	commit: BUG: 解决气压图表打点超过横坐标值问题
8fc654b7e630cc01bb3adaf1e819f23d796d9e63 14bea3338e3c1292bb20e419cbf9254411489bdd yanxuqiang <<EMAIL>> 1755572525 +0800	commit (amend): BUG: 解决气压图表打点超过横坐标值问题
14bea3338e3c1292bb20e419cbf9254411489bdd 14bea3338e3c1292bb20e419cbf9254411489bdd yanxuqiang <<EMAIL>> 1755661003 +0800	reset: moving to HEAD
14bea3338e3c1292bb20e419cbf9254411489bdd 8a829d5b891749ed37a67d0f58f167c44408de5b yanxuqiang <<EMAIL>> 1755661008 +0800	pull --rebase (start): checkout 8a829d5b891749ed37a67d0f58f167c44408de5b
8a829d5b891749ed37a67d0f58f167c44408de5b 8a829d5b891749ed37a67d0f58f167c44408de5b yanxuqiang <<EMAIL>> 1755661008 +0800	pull --rebase (finish): returning to refs/heads/develop
8a829d5b891749ed37a67d0f58f167c44408de5b ed0c25285d8f80f2721fefdee5e6a96dc1294f90 yanxuqiang <<EMAIL>> 1755661051 +0800	commit: GUI: 优化各页面切回表盘响应速度
ed0c25285d8f80f2721fefdee5e6a96dc1294f90 a90e9480ce26928d93f0f870e689ccba98dde620 yanxuqiang <<EMAIL>> 1755661055 +0800	commit (amend): GUI: 优化各页面切回表盘响应速度
a90e9480ce26928d93f0f870e689ccba98dde620 aa8233512d7a812d2ca2616dfbb63f0a7114bc4d yanxuqiang <<EMAIL>> 1755679630 +0800	commit (amend): GUI: 优化各页面切回表盘响应速度
aa8233512d7a812d2ca2616dfbb63f0a7114bc4d 66aa68e7fafa7ffcda9cd6ae2266ae1dcb11b2e4 yanxuqiang <<EMAIL>> 1755681143 +0800	commit (amend): GUI: 优化各页面切回表盘响应速度
66aa68e7fafa7ffcda9cd6ae2266ae1dcb11b2e4 2d26295245b909918c2b2476c62d14c541e53853 yanxuqiang <<EMAIL>> 1755681165 +0800	commit (amend): GUI: 优化各页面切回表盘响应速度
2d26295245b909918c2b2476c62d14c541e53853 f20ed5b98766a4253c8e5edea97a5c9a5ae7f7b5 yanxuqiang <<EMAIL>> 1755691039 +0800	pull --rebase (start): checkout f20ed5b98766a4253c8e5edea97a5c9a5ae7f7b5
f20ed5b98766a4253c8e5edea97a5c9a5ae7f7b5 f20ed5b98766a4253c8e5edea97a5c9a5ae7f7b5 yanxuqiang <<EMAIL>> 1755691039 +0800	pull --rebase (finish): returning to refs/heads/develop
f20ed5b98766a4253c8e5edea97a5c9a5ae7f7b5 f20ed5b98766a4253c8e5edea97a5c9a5ae7f7b5 yanxuqiang <<EMAIL>> 1755692690 +0800	reset: moving to HEAD
f20ed5b98766a4253c8e5edea97a5c9a5ae7f7b5 5d7f235e27c22f5f68ca3167d96ca6ec2b4e5418 yanxuqiang <<EMAIL>> 1755692695 +0800	pull --rebase: Fast-forward
5d7f235e27c22f5f68ca3167d96ca6ec2b4e5418 852d71fe5b1e342ae208ce0694bd0f4a7237e91f yanxuqiang <<EMAIL>> 1755692741 +0800	commit: GUI: 去掉无效测试表盘
852d71fe5b1e342ae208ce0694bd0f4a7237e91f 852d71fe5b1e342ae208ce0694bd0f4a7237e91f yanxuqiang <<EMAIL>> 1755744071 +0800	reset: moving to HEAD
852d71fe5b1e342ae208ce0694bd0f4a7237e91f edde3d6b40f9ecce2aed1ff7daa4954bc5cef3af yanxuqiang <<EMAIL>> 1755744075 +0800	pull --rebase (start): checkout edde3d6b40f9ecce2aed1ff7daa4954bc5cef3af
edde3d6b40f9ecce2aed1ff7daa4954bc5cef3af edde3d6b40f9ecce2aed1ff7daa4954bc5cef3af yanxuqiang <<EMAIL>> 1755744075 +0800	pull --rebase (finish): returning to refs/heads/develop
edde3d6b40f9ecce2aed1ff7daa4954bc5cef3af e9c82c7ad4e8ff48a81a99f77cfc4ec95d008412 yanxuqiang <<EMAIL>> 1755744124 +0800	commit: BUG: 解决高度手动校准时ui卡顿问题
e9c82c7ad4e8ff48a81a99f77cfc4ec95d008412 e9c82c7ad4e8ff48a81a99f77cfc4ec95d008412 yanxuqiang <<EMAIL>> 1755780093 +0800	reset: moving to HEAD
e9c82c7ad4e8ff48a81a99f77cfc4ec95d008412 0e2e4108634eb4bad80b904a81df22fb486585ab yanxuqiang <<EMAIL>> 1755780098 +0800	pull --rebase: Fast-forward
0e2e4108634eb4bad80b904a81df22fb486585ab 525ca3eae583572074fa5971b4d4df910a41b7e5 yanxuqiang <<EMAIL>> 1755780155 +0800	commit: GUI: 重构系统表盘0
525ca3eae583572074fa5971b4d4df910a41b7e5 525ca3eae583572074fa5971b4d4df910a41b7e5 yanxuqiang <<EMAIL>> 1755831028 +0800	reset: moving to HEAD
525ca3eae583572074fa5971b4d4df910a41b7e5 7dad70c34aee56f0bba70baed806c720f3cd5598 yanxuqiang <<EMAIL>> 1755831049 +0800	pull --rebase (start): checkout 7dad70c34aee56f0bba70baed806c720f3cd5598
7dad70c34aee56f0bba70baed806c720f3cd5598 972daef1fb931bdb08c5884a25862f2dacec7387 yanxuqiang <<EMAIL>> 1755831049 +0800	pull --rebase (pick): GUI: 重构系统表盘0
972daef1fb931bdb08c5884a25862f2dacec7387 972daef1fb931bdb08c5884a25862f2dacec7387 yanxuqiang <<EMAIL>> 1755831049 +0800	pull --rebase (finish): returning to refs/heads/develop
972daef1fb931bdb08c5884a25862f2dacec7387 d5a53bf5db95c6ed0e37ef94bbdb9365d0d5b2d6 yanxuqiang <<EMAIL>> 1755835188 +0800	commit (amend): GUI: 重构系统表盘0
d5a53bf5db95c6ed0e37ef94bbdb9365d0d5b2d6 d5a53bf5db95c6ed0e37ef94bbdb9365d0d5b2d6 yanxuqiang <<EMAIL>> 1756177775 +0800	reset: moving to HEAD
d5a53bf5db95c6ed0e37ef94bbdb9365d0d5b2d6 e5efc2c7ab858f825127d51a70997e7e31f63139 yanxuqiang <<EMAIL>> 1756177891 +0800	commit: GUI: 修改表盘抬腕亮屏渲染逻辑
e5efc2c7ab858f825127d51a70997e7e31f63139 681f7a6d9b4952bd04b61fcbcfde261c96483e33 yanxuqiang <<EMAIL>> 1756177896 +0800	commit (amend): GUI: 修改表盘抬腕亮屏渲染逻辑
681f7a6d9b4952bd04b61fcbcfde261c96483e33 681f7a6d9b4952bd04b61fcbcfde261c96483e33 yanxuqiang <<EMAIL>> 1756189093 +0800	reset: moving to HEAD
681f7a6d9b4952bd04b61fcbcfde261c96483e33 7316f9c178e8b14fb418e0e7d1e5cc98049075f6 yanxuqiang <<EMAIL>> 1756189097 +0800	pull --rebase (start): checkout 7316f9c178e8b14fb418e0e7d1e5cc98049075f6
7316f9c178e8b14fb418e0e7d1e5cc98049075f6 152b0d6aa4cc3cb26f9ba209ef99b6be3d1f2ffa yanxuqiang <<EMAIL>> 1756189097 +0800	pull --rebase (pick): GUI: 修改表盘抬腕亮屏渲染逻辑
152b0d6aa4cc3cb26f9ba209ef99b6be3d1f2ffa 152b0d6aa4cc3cb26f9ba209ef99b6be3d1f2ffa yanxuqiang <<EMAIL>> 1756189097 +0800	pull --rebase (finish): returning to refs/heads/develop
152b0d6aa4cc3cb26f9ba209ef99b6be3d1f2ffa 152b0d6aa4cc3cb26f9ba209ef99b6be3d1f2ffa yanxuqiang <<EMAIL>> 1756190551 +0800	reset: moving to HEAD
152b0d6aa4cc3cb26f9ba209ef99b6be3d1f2ffa 512ac5fa56ed570c33bd8c0f5dd8b9a1cb05f456 yanxuqiang <<EMAIL>> 1756190606 +0800	commit: GUI: 新增标志，判断是否从launcher页面进入该页面,是则返回launcher
512ac5fa56ed570c33bd8c0f5dd8b9a1cb05f456 5526f2cbc37857c4916ee597b9cc665e94892642 yanxuqiang <<EMAIL>> 1756190697 +0800	commit (amend): GUI: 新增标志，判断是否从launcher页面进入该页面
5526f2cbc37857c4916ee597b9cc665e94892642 183e2b10aa24284d167045efcb5df481c6d6fc83 yanxuqiang <<EMAIL>> 1756190778 +0800	commit (amend): GUI: 新增标志，判断是否从launcher页面进入该页面
183e2b10aa24284d167045efcb5df481c6d6fc83 183e2b10aa24284d167045efcb5df481c6d6fc83 yanxuqiang <<EMAIL>> 1756278366 +0800	reset: moving to HEAD
183e2b10aa24284d167045efcb5df481c6d6fc83 92063d65c03b30af95cdfc524e8cbce897efe8a5 yanxuqiang <<EMAIL>> 1756278376 +0800	pull --rebase (start): checkout 92063d65c03b30af95cdfc524e8cbce897efe8a5
92063d65c03b30af95cdfc524e8cbce897efe8a5 92063d65c03b30af95cdfc524e8cbce897efe8a5 yanxuqiang <<EMAIL>> 1756278376 +0800	pull --rebase (finish): returning to refs/heads/develop
92063d65c03b30af95cdfc524e8cbce897efe8a5 89a2336749c88feaf3a24fc6c23be1021e265695 yanxuqiang <<EMAIL>> 1756278436 +0800	commit: GUI: 新增Aod表盘功能
89a2336749c88feaf3a24fc6c23be1021e265695 da773269d0300f5f0b76bba6879b96a1083f0abc yanxuqiang <<EMAIL>> 1756278443 +0800	commit (amend): GUI: 新增Aod表盘功能
da773269d0300f5f0b76bba6879b96a1083f0abc da773269d0300f5f0b76bba6879b96a1083f0abc yanxuqiang <<EMAIL>> 1756363260 +0800	reset: moving to HEAD
da773269d0300f5f0b76bba6879b96a1083f0abc da773269d0300f5f0b76bba6879b96a1083f0abc yanxuqiang <<EMAIL>> 1756364481 +0800	reset: moving to HEAD
da773269d0300f5f0b76bba6879b96a1083f0abc 1eb8d006b52b2ca59ba26a0a93f418cf1060757b yanxuqiang <<EMAIL>> 1756364501 +0800	pull --rebase (start): checkout 1eb8d006b52b2ca59ba26a0a93f418cf1060757b
1eb8d006b52b2ca59ba26a0a93f418cf1060757b 1eb8d006b52b2ca59ba26a0a93f418cf1060757b yanxuqiang <<EMAIL>> 1756364501 +0800	pull --rebase (finish): returning to refs/heads/develop
1eb8d006b52b2ca59ba26a0a93f418cf1060757b c4947d4055d3ad747f6e178e59cd34b977b18669 yanxuqiang <<EMAIL>> 1756365321 +0800	commit: GUI: 完成V0.86需求，调整睡眠卡片显示
c4947d4055d3ad747f6e178e59cd34b977b18669 67e6b0236641bfacec556305ed971c421e5680a5 yanxuqiang <<EMAIL>> 1756366000 +0800	commit (amend): GUI: 完成V0.86需求，调整睡眠卡片显示
67e6b0236641bfacec556305ed971c421e5680a5 67e6b0236641bfacec556305ed971c421e5680a5 yanxuqiang <<EMAIL>> 1756372154 +0800	reset: moving to HEAD
67e6b0236641bfacec556305ed971c421e5680a5 e9b6522936d54b7bf810429ce54e7e29ade3d08c yanxuqiang <<EMAIL>> 1756372158 +0800	pull --rebase (start): checkout e9b6522936d54b7bf810429ce54e7e29ade3d08c
e9b6522936d54b7bf810429ce54e7e29ade3d08c e9b6522936d54b7bf810429ce54e7e29ade3d08c yanxuqiang <<EMAIL>> 1756372158 +0800	pull --rebase (finish): returning to refs/heads/develop
e9b6522936d54b7bf810429ce54e7e29ade3d08c 970a013ea6f94e34e02733731f2180d94640a939 yanxuqiang <<EMAIL>> 1756372196 +0800	commit: GUI: aod
970a013ea6f94e34e02733731f2180d94640a939 1d6534b5070180f25487113d97ce9c8b0f2bc3f2 yanxuqiang <<EMAIL>> 1756372202 +0800	commit (amend): GUI: 显示aod表盘时编辑主题数据不回launcher
1d6534b5070180f25487113d97ce9c8b0f2bc3f2 bd0fb58f47f4d74e8856cc3f5ead55cd53e4fb75 yanxuqiang <<EMAIL>> 1756372459 +0800	commit (amend): GUI: 显示aod表盘时编辑主题数据不回launcher
bd0fb58f47f4d74e8856cc3f5ead55cd53e4fb75 ef67400eb726bf73fc8558c8ba3725a16bfa876b yanxuqiang <<EMAIL>> 1756372467 +0800	commit (amend): GUI: 显示aod表盘时编辑主题数据不回launcher
ef67400eb726bf73fc8558c8ba3725a16bfa876b 5f8c62284ce44285a6c7e5362514ebec739de7e5 yanxuqiang <<EMAIL>> 1756382812 +0800	commit: GUI: 调整页面整体显示，适配整体框架
5f8c62284ce44285a6c7e5362514ebec739de7e5 5f8c62284ce44285a6c7e5362514ebec739de7e5 yanxuqiang <<EMAIL>> 1756461305 +0800	reset: moving to HEAD
5f8c62284ce44285a6c7e5362514ebec739de7e5 9fa673e89eedbc2cc85ee5ca177f76d81a15b59f yanxuqiang <<EMAIL>> 1756461312 +0800	pull --rebase (start): checkout 9fa673e89eedbc2cc85ee5ca177f76d81a15b59f
9fa673e89eedbc2cc85ee5ca177f76d81a15b59f 9fa673e89eedbc2cc85ee5ca177f76d81a15b59f yanxuqiang <<EMAIL>> 1756461312 +0800	pull --rebase (finish): returning to refs/heads/develop
9fa673e89eedbc2cc85ee5ca177f76d81a15b59f ef603aeb8948669acc9ba022861ad4984409bfe0 yanxuqiang <<EMAIL>> 1756461396 +0800	commit: BUG: 解决睡眠卡片小睡单位显示不正确问题
ef603aeb8948669acc9ba022861ad4984409bfe0 e3c7d2a9f8303ba6fb06802c22f95e99d5c28749 yanxuqiang <<EMAIL>> 1756461404 +0800	commit (amend): BUG: 解决睡眠卡片小睡单位显示不正确问题
e3c7d2a9f8303ba6fb06802c22f95e99d5c28749 1df7906a0cb02caca47a0e76e6766a10aa2bcec0 yanxuqiang <<EMAIL>> 1756519415 +0800	pull --rebase (start): checkout 1df7906a0cb02caca47a0e76e6766a10aa2bcec0
1df7906a0cb02caca47a0e76e6766a10aa2bcec0 4d767ad1436be43d931cff66fe927180d7b58e75 yanxuqiang <<EMAIL>> 1756519415 +0800	pull --rebase (pick): BUG: 解决睡眠卡片小睡单位显示不正确问题
4d767ad1436be43d931cff66fe927180d7b58e75 4d767ad1436be43d931cff66fe927180d7b58e75 yanxuqiang <<EMAIL>> 1756519415 +0800	pull --rebase (finish): returning to refs/heads/develop
4d767ad1436be43d931cff66fe927180d7b58e75 045094f993dc41fed6395983c2aaed9fe0838d8c yanxuqiang <<EMAIL>> 1756523103 +0800	commit: GUI: 优化aod表盘显示逻辑，未进入launcher进入aod显示系统表盘
