/************************************************************************​
*Copyright(c) 2025, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#ifndef ALG_CALORIE_PORT_H
#define ALG_CALORIE_PORT_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

typedef struct _alg_calorie_init
{
    float weight;                       //体重（kg）
    uint8_t gender;                     //性别，0-女性 1-男性
    uint8_t age;                        //年龄（岁）
} alg_calorie_init_t;

typedef struct _alg_calorie_input
{
    uint32_t timestamp;                 //时间戳（s）
    float pwr_est;                      //估计功率（w），小于0表示无效
    uint16_t power;                     //功率（w），0xFFFF表示无效
    uint8_t hr;                         //心率（bpm），0xFF表示无效
} alg_calorie_input_t;

void alg_calorie_init(const alg_calorie_init_t *init);

float alg_calorie_exec(const alg_calorie_input_t *input);

#ifdef __cplusplus
}
#endif

#endif