/**
 * @*************************************** Copyright (c) ***************************************
 * @                              <PERSON>han Qiwu Technology Co., Ltd
 * @*********************************************************************************************
 * @Author: Jiang<PERSON>hen
 * @Date: 2021-11-30 15:19:33
 * @LastEditTime: 2022-01-06 16:47:27
 * @LastEditors: JiangZhen
 * @FilePath: \iGS630_App\Application\App\radio\sensor\sensor_ant\bpwr\ant_bpwr_rx.c
 * @Description: ANT功率计模块接收.c文件
 * @*********************************************************************************************
 */
#include "app_error.h"
#include "ant_bpwr.h"
#include "ant_bpwr_rx.h"
#include "ant_request_controller.h"
#include "qw_sensor_common.h"
#include "sensor_ant_common.h"
#include "qw_sensor_data.h"
#include "ant_parameters.h"
#include "ant_interface.h"
#include "basictype.h"
#include "cfg_header_def.h"

uint8_t g_pwrCad = 0;
uint8_t g_pwrSpd = 0;

#if ANT_SENSOR_BPWR_ENABLED

typedef struct _ant_bpwr_params_update
{
    uint16_t cl_desired;                        //想要设置的曲柄长度，单位：0.1mm
    uint16_t cl_got;                            //从功率计获取到的曲柄长度，单位：0.1mm
    uint8_t is_inited;                          //是否初始化
    uint8_t should_get_params;                  //是否需要获取功率计参数
    uint8_t get_params_immediately;             //是否立即获取功率计参数
    uint8_t repeat_num;                         //重试次数
    uint8_t should_set_cl;                      //是否需要设置功率计曲柄长度
    uint8_t set_cl_immediately;                 //是否立即设置功率计曲柄长度
    uint8_t request_calibration;                //是否请求校准请求
    uint8_t calibration_repeat_num;             //请求校准请求重试次数
    uint8_t calibration_key;                    //请求校准参数
} ant_bpwr_params_update_t;

static ant_bpwr_params_update_t s_bpwr_params = {
    .cl_desired = 0xFFFF,
    .cl_got = 0xFFFF,
    .is_inited = false,
    .should_get_params = true,
    .get_params_immediately = false,
    .should_set_cl = true,
    .set_cl_immediately = false,
    .repeat_num = 6,
    .request_calibration = false,
    .calibration_repeat_num = 10,
    .calibration_key = true,
};

//--------------------------------------函数申明-------------------------------------------//
static void ant_bpwr_rx_evt_handler(ant_bpwr_profile_t * p_profile, ant_bpwr_evt_t event);
static void bpwr_ant_evt(ant_evt_t *p_ant_evt, void * p_context);


//--------------------------------------变量定义-------------------------------------------//
BPWR_DISP_PROFILE_CONFIG_DEF(m_ant_bpwr,
                             ant_bpwr_rx_evt_handler);

static ant_bpwr_profile_t m_ant_bpwr;
static uint32_t s_pwr_cad_ms           = 0;
static uint32_t s_pwr_spd_ms           = 0;
static uint8_t low_power_indicate_flag = false;
uint8_t g_Calibration_Key       = false;

NRF_SDH_ANT_OBSERVER(m_bpwr_ant_observer, ANT_BPWR_ANT_OBSERVER_PRIO, bpwr_ant_evt, &m_ant_bpwr);
//--------------------------------------函数定义-------------------------------------------//
/**
 * @*********************************************************************************************
 * @description: 加载ANT PWR接收通道默认配置
 * @param {ant_channel_config_t } *p_channel_config
 * @return {*}
 * @*********************************************************************************************
 */
static void LoadChnConf_bpwr_rx(ant_channel_config_t  *p_channel_config)
{
    p_channel_config->channel_number    = sensor_ant_channel_num_get(SENSOR_TYPE_BPWR);
    p_channel_config->channel_type      = CHANNEL_TYPE_SLAVE;
    p_channel_config->ext_assign        = BPWR_EXT_ASSIGN;
    p_channel_config->rf_freq           = BPWR_ANTPLUS_RF_FREQ;
    p_channel_config->transmission_type = CHAN_ID_TRANS_TYPE;
    p_channel_config->device_type       = BPWR_DEVICE_TYPE;
    p_channel_config->channel_period    = BPWR_MSG_PERIOD;
    p_channel_config->network_number    = ANTPLUS_NETWORK_NUM;
}

/**
 * @*********************************************************************************************
 * @description: ANT PWR接收通道中断处理函数
 * @param {ant_bpwr_profile_t *} p_profile
 * @param {ant_bpwr_evt_t} event
 * @return {*}
 * @*********************************************************************************************
 */
static void ant_bpwr_rx_evt_handler(ant_bpwr_profile_t * p_profile, ant_bpwr_evt_t event)
{
    sensor_search_infor_t       sensor_search_infor;
    // sensor_connect_infor_t      *p_sensor_connect       = sensor_connect_infor_get(SENSOR_TYPE_BPWR);
    sensor_module_evt_handler   evt_handler             = sensor_module_evt_handler_get();
    sensor_saved_work_t         *p_sensor_saved         = NULL;
    sensor_work_state_t         sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    sensor_module_param_input_t *p_param                = sensor_module_param_input_get();
    sensor_original_data_t      *p_sensor_original_data = sensor_original_data_get();
    int8_t                      index                   = -1;
    sensor_connect_infor_t      sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_BPWR, &sensor_connect);

    sensor_ant_leave_rx_search(SENSOR_TYPE_BPWR);

    switch (event)
    {
    case ANT_BPWR_PAGE_1_UPDATED:
        break;
    case ANT_BPWR_PAGE_16_UPDATED:
        /* fall through */
    case ANT_BPWR_PAGE_17_UPDATED:
        /* fall through */
    case ANT_BPWR_PAGE_18_UPDATED:
        /* fall through */
    case ANT_BPWR_PAGE_80_UPDATED:
        /* fall through */
    case ANT_BPWR_PAGE_81_UPDATED:
        break;

    case ANT_BPWR_CALIB_TIMEOUT:
    case ANT_BPWR_CALIB_REQUEST_TX_FAILED: //这里处理超时时间不可控
        break;

    default:
        // never occurred
        break;
    }

    //更新显示信息
    memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
    memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
    sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
    sensor_search_infor.sensor_type = SENSOR_TYPE_BPWR;

    // sensor_saved_work_infor_get(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state);

    if (SENSOR_CONNECT_STATE_CONNECTING == sensor_connect.state)
    {
        sensor_connect.state = SENSOR_CONNECT_STATE_CONNECTED;
        sensor_connect_infor_set(SENSOR_TYPE_BPWR, &sensor_connect);

        if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
        {
            if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
            {
                p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
                cfg_mark_update(enum_cfg_ant_ble_dev);
            }
            else if (SENSOR_WORK_STATE_FORBIDDEN == sensor_work_state)
            {
                sensor_infor_t sensor_infor = {0};
                sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                sensor_disconnect(&sensor_infor);
            }
            sensor_saved_work_infor_release_write_lock(index);
        }
        else
        {
            if(sensor_disconnect_item_check(&sensor_search_infor))
            {
                sensor_disconnect_info_remove(&sensor_search_infor);
                sensor_infor_t sensor_infor = {0};
                sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                sensor_disconnect(&sensor_infor);
                return;
            }
            sensor_saved_work_infor_add(&sensor_search_infor);
            sensor_search_infor_del(&sensor_search_infor);
        }

        if (NULL != evt_handler)
        {
            evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, TRUE);
            evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, SENSOR_TYPE_BPWR, SENSOR_RADIO_TYPE_ANT, 0);
            evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, SENSOR_TYPE_BPWR, SENSOR_RADIO_TYPE_ANT, TRUE);
        }
        low_power_indicate_flag = TRUE;
    }
    if (SENSOR_CONNECT_STATE_CONNECTED == sensor_connect.state)
    {
        if (NULL != evt_handler)
        {
            evt_handler(EVENT_SENSOR_DATA_UPDATE, NULL, SENSOR_TYPE_BPWR, SENSOR_RADIO_TYPE_ANT, 0);
        }
        if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
        {
            if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
            {
                p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
            }
            sensor_saved_work_infor_release_write_lock(index);
        }
    }


    p_sensor_original_data->crank_length = sensor_saved_size_get(&sensor_search_infor);
}

/**
 * @*********************************************************************************************
 * @description:功率计ANT事件处理函数
 * @param {ant_evt_t} *p_ant_evt
 * @param {void *} p_context
 * @return {*}
 * @*********************************************************************************************
 */
static void bpwr_ant_evt(ant_evt_t *p_ant_evt, void * p_context)
{
    sensor_search_infor_t     sensor_search_infor;
    // sensor_connect_infor_t      *p_sensor_connect       = sensor_connect_infor_get(SENSOR_TYPE_BPWR);
    sensor_module_evt_handler   evt_handler             = sensor_module_evt_handler_get();
    sensor_saved_work_t         *p_sensor_saved         = NULL;
    sensor_original_data_t      *p_sensor_original_data = sensor_original_data_get();
    sensor_module_param_input_t *p_param                = sensor_module_param_input_get();
    sensor_work_state_t         sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    //uint8_t                     channelstate            = 0;
    int8_t                      index                   = -1;
    ret_code_t                  err_code                = NRF_SUCCESS;
    sensor_connect_infor_t      sensor_connect;
    bool found = sensor_connect_infor_get(SENSOR_TYPE_BPWR, &sensor_connect);
    bool manual_connect_status_sent = false;

    if (p_ant_evt->channel != m_ant_bpwr.channel_number)
    {
        return;
    }

    sensor_systime_update();

    ant_bpwr_disp_evt_handler(p_ant_evt, p_context, &p_sensor_original_data->pwrData);

    if (0xff != p_sensor_original_data->pwrData.cadence)
    {
        g_pwrCad = true;
    }
    else
    {
        g_pwrCad = false;
    }

    if(0xff != p_sensor_original_data->pwrData.cadence/* && 0 != cadData*/)
    {
        s_pwr_cad_ms = *p_param->sysTime_ms;
    }
    else
    {
        if(*p_param->sysTime_ms - s_pwr_cad_ms > 5000)
        {
            p_sensor_original_data->pwrData.cadence = 0xff;
        }
    }

	// if (SENSOR_CONNECT_STATE_CONNECTED != p_cbsc_sensor_connect->state)
	{
		// if (SENSOR_CONNECT_STATE_CONNECTED != p_spd_sensor_connect->state)
		{
			if (0xffff != p_sensor_original_data->pwrData.wheel_speed && 0xffff != p_sensor_original_data->pwrData.wheel_delta)
			{
				  p_sensor_original_data->wheel_size = 2096;//花鼓功率计，取默认轮径2096
			}

			if(0xffff != p_sensor_original_data->pwrData.wheel_speed && 0 != p_sensor_original_data->pwrData.wheel_speed)
			{
				s_pwr_spd_ms = *p_param->sysTime_ms;
			}
			else
			{
				if(*p_param->sysTime_ms - s_pwr_spd_ms > 5000)
				{
                    p_sensor_original_data->pwrData.wheel_delta = 0xffff;
                    p_sensor_original_data->pwrData.wheel_speed = 0xffff;
				}
			}
		}
	}

    if (p_sensor_original_data->pwrData.battery_status >= SENSOR_BATT_NEW &&
            p_sensor_original_data->pwrData.battery_status <= SENSOR_BATT_CRITICAL)
    {
        p_sensor_original_data->battery_list.bpwr = (SENSOR_BATT_MAX - p_sensor_original_data->pwrData.battery_status) * 20;
    }

    if (5 == p_sensor_original_data->pwrData.battery_status && true == low_power_indicate_flag)    //5表示Battery status: critical.
    {
        low_power_indicate_flag = false;
        if (NULL != evt_handler)
        {
            evt_handler(EVENT_SENSOR_LOW_POWER, &sensor_search_infor.sensor_id, SENSOR_TYPE_BPWR, SENSOR_RADIO_TYPE_ANT, 0);
        }
    }

    if (p_ant_evt->channel == m_ant_bpwr.channel_number)
    {
        switch (p_ant_evt->event)
        {
            case EVENT_CHANNEL_CLOSED:
                memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
                memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
                sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
                sensor_search_infor.sensor_type = SENSOR_TYPE_BPWR;

            	sensor_ant_leave_rx_search(SENSOR_TYPE_BPWR);

                err_code = sd_ant_channel_unassign(m_ant_bpwr.channel_number);
                APP_ERROR_CHECK(err_code);
                m_ant_bpwr.channel_number = 0;
                sensor_ant_channel_num_unassign(SENSOR_TYPE_BPWR);

                bool forbidden_mask = sensor_connect_infor_get_forbidden_mask(SENSOR_TYPE_BPWR);
                if(sensor_connect_infor_get(SENSOR_TYPE_BPWR, &sensor_connect))
                {
                    sensor_connect_infor_clear(SENSOR_TYPE_BPWR);
                    if (NULL != evt_handler && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)
                    {
                        manual_connect_status_sent = true;
                        evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, &sensor_connect.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                    }
                }

                if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
                {
                    if (SENSOR_WORK_STATE_IDLE != sensor_work_state)
                    {
                        p_sensor_saved->rssi             [index] = 0;
                        p_sensor_saved->battery_voltage  [index] = 0xff;
                        p_sensor_saved->sensor_work_state[index] = SENSOR_WORK_STATE_SAVED;
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }

                if((!forbidden_mask && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)       //连接完成后异常断连
                    || (forbidden_mask && sensor_connect.state == SENSOR_CONNECT_STATE_CLOSE_WAIT)
                    || sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)                      //连接超时
                {
                    // connected状态下断连，检索saved数组是否有同类型sensor并进行连接
                    sensor_connect_from_saved_info(sensor_search_infor.sensor_type);
                }

                if (NULL != evt_handler)
                {
                    evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, SENSOR_TYPE_BPWR, SENSOR_RADIO_TYPE_ANT, 0);
                    if (!manual_connect_status_sent)
                    {
                        evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, SENSOR_TYPE_BPWR, SENSOR_RADIO_TYPE_ANT, FALSE);
                    }
                }

                memset(&p_sensor_original_data->pwrData, 0xff, sizeof(pwr_data_t));
                p_sensor_original_data->battery_list.bpwr = 0;
                break;
            case EVENT_RX_FAIL_GO_TO_SEARCH:
                //sd_ant_channel_close(BPWR_CHANNEL_NUMBER);
                //sensor_ant_close(SENSOR_TYPE_BPWR);
            	sensor_ant_enter_rx_search(SENSOR_TYPE_BPWR);
                break;
            case EVENT_RX_SEARCH_TIMEOUT:
            default:
                break;
         }
    }
}

/**
 * @*********************************************************************************************
 * @description: 设置PWR通道
 * @param {ant_id_t} *id
 * @return {*}
 * @*********************************************************************************************
 */
void ant_bpwr_rx_profile_setup(ant_id_t *id)
{
    ret_code_t err_code = NRF_SUCCESS;
    // sensor_connect_infor_t *p_sensor_connect = sensor_connect_infor_get(SENSOR_TYPE_BPWR);
    sensor_connect_infor_t sensor_connect;
    ant_channel_config_t channel_config;

    sensor_connect_infor_get(SENSOR_TYPE_BPWR, &sensor_connect);

    memcpy ((uint8_t *)&sensor_connect.sensor_id.ant_id, (uint8_t *)id, sizeof(ant_id_t));
    sensor_connect_infor_set(SENSOR_TYPE_BPWR, &sensor_connect);

    /*
    //device num的组成
    //1byte   1byte    |     1byte      |      1byte                  从左到右高到低
    //   device id     | device type    |MSN:extended device number LSN:Transmission Type
    */
    uint16_t sensor_id = (uint16_t)id->id;
    uint8_t trans_type = CHAN_ID_TRANS_TYPE;
    if (id->id > 0xffff)
    {
        trans_type = id->trans_type;
    }

    //加载参数
    LoadChnConf_bpwr_rx(&channel_config);
    channel_config.device_number = sensor_id;
    channel_config.transmission_type = trans_type;

    err_code = ant_bpwr_disp_init(&m_ant_bpwr, (const ant_channel_config_t *)&channel_config, BPWR_DISP_PROFILE_CONFIG(m_ant_bpwr));
    APP_ERROR_CHECK(err_code);

    g_pwrSpd = 0;
    g_pwrCad = 0;
}

/**
 * @*********************************************************************************************
 * @description: 开启ANT指定设备ID的指定PWR通道
 * @param {*}
 * @return {*}
 * @*********************************************************************************************
 */
void ant_bpwr_rx_open(void)
{
    ret_code_t err_code = NRF_SUCCESS;
    err_code = ant_bpwr_disp_open(&m_ant_bpwr);
    APP_ERROR_CHECK(err_code);
}

/**
 * @*********************************************************************************************
 * @description: 功率校正请求
 * @param {bool} calibration_from_key
 * @return {*}
 * @*********************************************************************************************
 */
bool ant_bpwr_rx_calib(bool calibration_from_key)
{
    uint32_t err_code = 0;
    uint8_t  channelstate = 0;
    ant_bpwr_page1_data_t page1;

    if (calibration_from_key)
    {
        g_Calibration_Key = true;
    }

    page1    = ANT_BPWR_GENERAL_CALIB_REQUEST();

    uint8_t channel_num = sensor_ant_channel_num_get(SENSOR_TYPE_BPWR);
    if (channel_num < 1 || channel_num >= NRF_SDH_ANT_TOTAL_CHANNELS_ALLOCATED)
    {
        return false;
    }

    err_code = sd_ant_channel_status_get(channel_num, &channelstate);
    if (err_code != NRF_SUCCESS)
    {
        sd_ant_channel_close(channel_num);
        //log_file_printf("ant_bpwr_rx_calib channel_number: %d, channelstate:%d, err_code:%d\n",BPWR_CHANNEL_NUMBER, channelstate, err_code);
        //log_file_store();
        return false;
    }
    if (STATUS_TRACKING_CHANNEL == channelstate)
    {
        err_code = ant_bpwr_calib_request(&m_ant_bpwr, &page1);
    }
    return (err_code == NRF_SUCCESS);
}

void ant_bpwr_rx_calib_request(bool calibration_from_key)
{
    s_bpwr_params.request_calibration = true;
    s_bpwr_params.calibration_key = calibration_from_key;
    s_bpwr_params.calibration_repeat_num = 10;
}

static void ant_bpwr_rx_calib_request_update()
{
    if(s_bpwr_params.request_calibration)
    {
        if(s_bpwr_params.calibration_repeat_num > 0)
        {
            rt_kprintf("ant_bpwr_rx_calib\n");
            if(ant_bpwr_rx_calib(s_bpwr_params.calibration_key))
            {
                s_bpwr_params.calibration_repeat_num = 0;
                s_bpwr_params.request_calibration = false;
            }
            else
            {
                s_bpwr_params.calibration_repeat_num --;
            }
        }
        else
        {
            s_bpwr_params.calibration_repeat_num = 0;
            s_bpwr_params.request_calibration = false;
        }
    }
}

ret_code_t ant_bpwr_crank_parameters_request(void)
{
    // sensor_connect_infor_t *p_sensor_connect = sensor_connect_infor_get(SENSOR_TYPE_BPWR);
    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_BPWR, SENSOR_CONNECT_STATE_CONNECTED);
    ant_request_controller_t bpwr_crank_params_request;
    ant_bpwr_profile_t *p_profile = &m_ant_bpwr;

    if (res)
    {
        ant_request_controller_init(&bpwr_crank_params_request);
        bpwr_crank_params_request.page_70.descriptor = 0xFF01;  //descriptor byte2 << 8 | descriptor byte1
        bpwr_crank_params_request.page_70.page_number = 0x02;
        bpwr_crank_params_request.page_70.command_type = ANT_PAGE70_COMMAND_PAGE_DATA_REQUEST; //Value = 1 (0x01) for Request Data Page
        bpwr_crank_params_request.page_70.transmission_response.items.transmit_count = 1; //Number of re-transmissions.

        return ant_request_controller_request(&bpwr_crank_params_request, p_profile->channel_number, &bpwr_crank_params_request.page_70);
    }

    return NRF_SUCCESS;
}

ret_code_t ant_bpwr_crank_length_set(uint16_t crank_length)
{
    if (crank_length == 0xFFFF)
    {
        return NRF_SUCCESS;
    }

    // sensor_connect_infor_t *p_sensor_connect = sensor_connect_infor_get(SENSOR_TYPE_BPWR);
    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_BPWR, SENSOR_CONNECT_STATE_CONNECTED);
    ant_bpwr_page2_data_t bpwr_page2_data = { 0 };
    ant_bpwr_profile_t *p_profile = &m_ant_bpwr;

    bpwr_page2_data.subpage = enumANT_BPWR_PAGE2_SUBPAGE_CRANK_PARAMETERS;
    bpwr_page2_data.crank_params.crank_length = crank_length;

    if (res)
    {
        return ant_bpwr_crank_length_tx(p_profile, &bpwr_page2_data);
    }

    return NRF_SUCCESS;
}

void ant_bpwr_data_update(uint32_t runtime_ms)
{
    if(false == sensor_connect_infor_get_state(SENSOR_TYPE_BPWR, SENSOR_CONNECT_STATE_CONNECTED))
    {
        return;
    }
    static ant_bpwr_params_update_t *p_bpwr_params = &s_bpwr_params;

    static uint32_t s_last_time = 0;
    static uint8_t s_could_get_params = true;
    static uint8_t s_could_set_cl = true;

    static sensor_connect_infor_t s_last_bpwr_connected = { 0 };

    sensor_search_infor_t       sensor_search_infor;
    // sensor_connect_infor_t      *p_sensor_connect       = sensor_connect_infor_get(SENSOR_TYPE_BPWR);
    sensor_connect_infor_t      sensor_connect;
    bool found       = sensor_connect_infor_get(SENSOR_TYPE_BPWR, &sensor_connect);
    sensor_original_data_t      *p_sensor_original_data = sensor_original_data_get();

    if (!found || sensor_connect.radio_type != SENSOR_RADIO_TYPE_ANT || p_sensor_original_data == NULL)
    {
        return;
    }

    if (sensor_connect.state != SENSOR_CONNECT_STATE_CONNECTED)
    {
        p_bpwr_params->is_inited = false;
        return;
    }

    if (runtime_ms >= s_last_time + 5000)
    {
        s_last_time = runtime_ms;
        s_could_get_params = true;
        s_could_set_cl = true;
    }
    else if (runtime_ms < s_last_time + 1000)
    {
        //确保1s内最多进入1次
        return;
    }

    memset(&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
    memcpy(&sensor_search_infor.sensor_id, &sensor_connect.sensor_id, sizeof(sensor_id_t));
    sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
    sensor_search_infor.sensor_type = SENSOR_TYPE_BPWR;
    const uint16_t cl_saved = sensor_saved_size_get(&sensor_search_infor);

    if (p_bpwr_params->is_inited == true)
    {
        //更换了功率计，需要重新初始化
        if (sensor_connect.sensor_id.ant_id.id != s_last_bpwr_connected.sensor_id.ant_id.id)
        {
            p_bpwr_params->is_inited = false;
        }
    }

    if (p_bpwr_params->is_inited == false)
    {
        memcpy(&s_last_bpwr_connected, &sensor_connect, sizeof(sensor_connect_infor_t));

        p_sensor_original_data->pwrData.is_support_params_get = 0xFF;
        p_sensor_original_data->pwrData.is_support_cl_set = 0xFF;
        p_sensor_original_data->pwrData.crank_length = 0xFFFF;
        p_sensor_original_data->pwrData.cl_status = 0xFF;
        p_sensor_original_data->pwrData.sw_status = 0xFF;
        p_sensor_original_data->pwrData.availability_status = 0xFF;
        p_sensor_original_data->pwrData.custom_calib_status = 0xFF;
        p_sensor_original_data->pwrData.auto_crank_length = 0xFF;

        p_bpwr_params->cl_desired = cl_saved;
        p_bpwr_params->cl_got = 0xFFFF;
        p_bpwr_params->should_get_params = true;
        p_bpwr_params->get_params_immediately = true;
        p_bpwr_params->should_set_cl = true;
        p_bpwr_params->set_cl_immediately = true;
        p_bpwr_params->repeat_num = 6;

        p_bpwr_params->is_inited = true;
    }

    if (p_sensor_original_data->pwrData.is_support_params_get == 0xFF)
    {
        if (p_bpwr_params->repeat_num > 0)
        {
            if (p_bpwr_params->get_params_immediately == true || s_could_get_params == true)
            {
                ant_bpwr_crank_parameters_request();
                p_bpwr_params->repeat_num -= 1;
                p_bpwr_params->get_params_immediately = false;
                s_could_get_params = false;
            }
            return;
        }

        p_sensor_original_data->pwrData.is_support_params_get = false;
    }

    if (p_sensor_original_data->pwrData.is_support_params_get == true)
    {
        p_bpwr_params->cl_got = p_sensor_original_data->pwrData.crank_length;

        if (p_bpwr_params->cl_desired != cl_saved)
        {
            p_bpwr_params->cl_desired = cl_saved;
            p_bpwr_params->set_cl_immediately = true;
        }

        if (p_bpwr_params->cl_desired != p_bpwr_params->cl_got)
        {
            p_bpwr_params->should_set_cl = true;
        }
        else
        {
            p_bpwr_params->should_set_cl = false;
            p_bpwr_params->set_cl_immediately = false;
            p_bpwr_params->should_get_params = false;
            p_bpwr_params->get_params_immediately = false;
        }
    }
    else
    {
        p_bpwr_params->cl_desired = cl_saved;
        p_bpwr_params->should_set_cl = true;
    }

    if (p_bpwr_params->should_set_cl == true && p_sensor_original_data->pwrData.is_support_cl_set != false)
    {
        if (p_bpwr_params->set_cl_immediately == true)
        {
            ant_bpwr_crank_length_set(p_bpwr_params->cl_desired);
            p_bpwr_params->set_cl_immediately = false;
            s_could_set_cl = false;
            //这里返回是为了避免立即获取参数
            return;
        }
        else
        {
            if (s_could_set_cl == true)
            {
                ant_bpwr_crank_length_set(p_bpwr_params->cl_desired);
                s_could_set_cl = false;
                //这里返回是为了避免立即获取参数
                return;
            }
        }

        p_bpwr_params->should_get_params = true;
    }

    if (p_bpwr_params->should_get_params == true && p_sensor_original_data->pwrData.is_support_params_get == true)
    {
        if (p_bpwr_params->get_params_immediately == true)
        {
            ant_bpwr_crank_parameters_request();
            p_bpwr_params->get_params_immediately = false;
            s_could_get_params = false;
        }
        else
        {
            if (s_could_get_params == true)
            {
                ant_bpwr_crank_parameters_request();
                s_could_get_params = false;
            }
        }
    }

    ant_bpwr_rx_calib_request_update();
}

#endif //ANT_SENSOR_BPWR_ENABLED
