#include <string.h>
#include <stddef.h>
#include <stdbool.h>
#include "navi_tsgn.h"

//读取tsgn文件头，用于检查文件类型和版本号等
int tsgn_header_read(TsgnReader *self, uint8_t *header)
{
    if (self == NULL || header == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 0) != QW_OK)
    {
        return -1;
    }

    UINT br = 0;

    if (qw_f_read(self->fp, header, 32, &br) != QW_OK || br != 32)
    {
        return -1;
    }

    return 0;
}

//从tsgn文件中加载标记点分段数组
int tsgn_sign_segment_array_read(TsgnReader *self, NaviSignSegmentArray *seg_array)
{
    if (self == NULL || seg_array == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 32) != QW_OK)
    {
        return -1;
    }

    uint32_t buf[3] = { 0 };

    UINT br = 0;

    if (qw_f_read(self->fp, buf, 12, &br) != QW_OK || br != 12)
    {
        return -1;
    }

    const uint32_t check_code = buf[0];
    const uint32_t size = buf[1];
    const uint32_t num = buf[2];

    if (check_code != 0 || size != 16 || num > NAVI_SIGN_SEGMENTS_NUM)
    {
        return -1;
    }

    seg_array->len = num;

    if (seg_array->len == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const UINT btr = seg_array->len * 16;

    if (qw_f_read(self->fp, seg_array->segments, btr, &br) != QW_OK || br != btr)
    {
        return -1;
    }

    return 0;
}

//读取标记点数量
int tsgn_sign_num_read(TsgnReader *self, uint32_t *sign_num)
{
    if (self == NULL || sign_num == NULL)
    {
        return -1;
    }

    //32 + (12 + 16 * 20) = 364
    if (qw_f_lseek(self->fp, 364) != QW_OK)
    {
        return -1;
    }

    uint32_t buf[3] = { 0 };

    UINT br = 0;

    if (qw_f_read(self->fp, buf, 12, &br) != QW_OK || br != 12)
    {
        return -1;
    }

    const uint32_t check_code = buf[0];
    const uint32_t size = buf[1];
    const uint32_t num = buf[2];

    if (check_code != 1 || size != 56)
    {
        return -1;
    }

    *sign_num = num;

    return 0;
}

//读取指定范围的标记点
int tsgn_sign_data_read(TsgnReader *self, uint32_t start, uint32_t end, uint32_t sign_num, NaviSign *sign_buf)
{
    if (self == NULL || sign_buf == NULL)
    {
        return -1;
    }

    if (start > end || end > sign_num)
    {
        return -1;
    }

    const uint32_t ntr = end - start;

    if (ntr == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    //32 + (12 + 16 * 20) + 12 = 376
    const uint32_t offset = 376 + start * 56;

    if (qw_f_lseek(self->fp, offset) != QW_OK)
    {
        return -1;
    }

    const UINT btr = ntr * 56;

    UINT br = 0;

    if (qw_f_read(self->fp, sign_buf, btr, &br) != QW_OK || br != btr)
    {
        return -1;
    }

    return 0;
}

//写入tsgn文件头占位符
int tsgn_header_placeholder_write(TsgnRWriter *self)
{
    if (self == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 0) != QW_OK)
    {
        return -1;
    }

    const uint8_t buf[32] = {
        'S', 'h', 'i', 't', ' ', 'H', 'a', 'p', 'p', 'e', 'n', 's', '.',
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 32, &bw) != QW_OK || bw != 32)
    {
        return -1;
    }

    return 0;
}

//写入tsgn文件头
int tsgn_header_write(TsgnRWriter *self)
{
    if (self == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 0) != QW_OK)
    {
        return -1;
    }

    UINT bw = 0;

    const uint8_t buf[32] = {
        'T', 'S', 'G', 'N', NAVI_FILE_MAJOR_VERSION, NAVI_FILE_MINOR_VERSION,
        'D', 'e', 's', 'i', 'g', 'n', 'e', 'd', ' ', 'b', 'y', ' ', 'J', 'u', 'n',
        'j', 'i', 'e', ' ', 'D', 'i', 'n', 'g', 0, 0, 0,
    };

    if (qw_f_write(self->fp, buf, 32, &bw) != QW_OK || bw != 32)
    {
        return -1;
    }

    return 0;
}

//向tsgn文件中写入标记点分段数组
int tsgn_sign_segment_array_write(TsgnRWriter *self, const NaviSignSegmentArray *seg_array)
{
    if (self == NULL || seg_array == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 32) != QW_OK)
    {
        return -1;
    }

    const uint32_t buf[3] = { 0, 16, seg_array->len };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    if (seg_array->len == 0)
    {
        return 0;
    }

    const UINT btw = seg_array->len * 16;

    if (qw_f_write(self->fp, seg_array->segments, btw, &bw) != QW_OK || bw != btw)
    {
        return -1;
    }

    return 0;
}

//写入标记点数量
int tsgn_sign_num_write(TsgnRWriter *self, uint32_t num)
{
    if (self == NULL)
    {
        return -1;
    }

    //32 + (12 + 16 * 20) = 364
    if (qw_f_lseek(self->fp, 364) != QW_OK)
    {
        return -1;
    }

    const uint32_t buf[3] = { 1, 56, num };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    return 0;
}

//写入一个标记点
int tsgn_sign_data_write(TsgnRWriter *self, const NaviSign *sign)
{
    if (self == NULL || sign == NULL)
    {
        return -1;
    }

    UINT bw = 0;

    if (qw_f_write(self->fp, sign, 56, &bw) != QW_OK || bw != 56)
    {
        return -1;
    }

    return 0;
}

//读取指定标记点
int tsgn_sign_data_read2(TsgnRWriter *self, uint32_t idx, uint32_t sign_num, NaviSign *sign)
{
    if (self == NULL || sign == NULL)
    {
        return -1;
    }

    if (idx >= sign_num)
    {
        return -1;
    }

    //32 + (12 + 16 * 20) + 12 = 376
    const uint32_t offset = 376 + idx * 56;

    if (qw_f_lseek(self->fp, offset) != QW_OK)
    {
        return -1;
    }

    UINT br = 0;

    if (qw_f_read(self->fp, sign, 56, &br) != QW_OK || br != 56)
    {
        return -1;
    }

    return 0;
}

//写入指定标记点
int tsgn_sign_data_write2(TsgnRWriter *self, uint32_t idx, uint32_t sign_num, const NaviSign *sign)
{
    if (self == NULL || sign == NULL)
    {
        return -1;
    }

    if (idx >= sign_num)
    {
        return -1;
    }

    //32 + (12 + 16 * 20) + 12 = 376
    const uint32_t offset = 376 + idx * 56;

    if (qw_f_lseek(self->fp, offset) != QW_OK)
    {
        return -1;
    }

    UINT bw = 0;

    if (qw_f_write(self->fp, sign, 56, &bw) != QW_OK || bw != 56)
    {
        return -1;
    }

    return 0;
}

//复制一个标记点数据到自身
void navi_sign_copy(NaviSign *self, NaviSign *sign)
{
    if (self != NULL && sign != NULL)
    {
        memcpy(self, sign, sizeof(NaviSign));
    }
}

//从标记点列表中获取指定标记点
int navi_sign_list_get(NaviSignList *self, uint32_t idx, NaviSign *output)
{
    if (self == NULL || output == NULL)
    {
        return -1;
    }

    if (idx >= self->len)
    {
        return -1;
    }

    //缓存命中
    for (uint32_t i = 0; i < self->cache.len; i++)
    {
        const uint32_t start = self->cache.sign_buf[i].range.start;
        const uint32_t end = self->cache.sign_buf[i].range.end;

        if (idx >= start && idx < end)
        {
            for (uint32_t j = start; j < end; j++)
            {
                if (idx == j)
                {
                    navi_sign_copy(output, &self->cache.sign_buf[i].buf[j-start]);
                    return 0;
                }
            }
        }
    }

    //缓存未命中
    NaviSignBuf *sign_buf = &self->cache.sign_buf[self->cache.next];

    //加载指定标记点开始的若干标记点
    sign_buf->range.start = idx;
    sign_buf->range.end = sign_buf->range.start + sign_buf->capacity;
    if (sign_buf->range.end > self->len)
    {
        sign_buf->range.end = self->len;
    }

    if (tsgn_sign_data_read(self->cache.tsgn_reader, sign_buf->range.start, sign_buf->range.end, self->len, sign_buf->buf) != 0)
    {
        return -1;
    }

    navi_sign_copy(output, &sign_buf->buf[idx - sign_buf->range.start]);

    if (self->cache.len < self->cache.capacity)
    {
        self->cache.len += 1;
    }

    self->cache.next += 1;
    if (self->cache.next >= self->cache.capacity)
    {
        self->cache.next = 0;
    }

    return 0;
}

//重置标记点缓冲区
void navi_sign_buf_reset(NaviSignBuf *self)
{
    if (self != NULL)
    {
        self->range.start = 0;
        self->range.end = 0;
    }
}

//重置标记点缓存
void navi_sign_cache_reset(NaviSignCache *self)
{
    if (self != NULL)
    {
        self->len = 0;
        self->next = 0;

        for (uint32_t i = 0; i < self->capacity; i++)
        {
            navi_sign_buf_reset(&self->sign_buf[i]);
        }
    }
}

//重置标记点list
void navi_sign_list_reset(NaviSignList *self)
{
    if (self != NULL)
    {
        navi_sign_cache_reset(&self->cache);
    }
}
