/************************************************************************
*
* Copyright(c) 2024, igpsport Software Co., Ltd.
* All Rights Reserved.
*
**************************************************************************/
#ifndef NAVI_ROUTE_PORT_H
#define NAVI_ROUTE_PORT_H

#ifdef __cplusplus
extern "C" {
#endif

#include "navi_route.h"
#include "navi_waypoint.h"

#define MAX_ROUTE_PATHNAME_LEN 128

#define NAVI_ROUTE_WP_COMPRESS_BUF_SIZE             100         //用于路点压缩的缓冲区大小
#define NAVI_ROUTE_WP_COMPRESS_H_THRES              5.0f        //路点压缩偏差阈值
#define NAVI_ROUTE_SEGMENT_INTERVAL                 20          //线路分段默认间隔
#define NAVI_ROUTE_WP_CACHE_BUF_SIZE                50          //路点缓存缓冲区大小
#define NAVI_ROUTE_WP_CACHE_BUF_NUM                 8           //路点缓存缓冲区数量
#define NAVI_ROUTE_WP_NEARBY_BUF_SIZE               300         //指定路点前后共需加载的路点的数量

void navi_route_init(void);

void navi_route_uninit(void);

int navi_route_is_tcnx_valid(const char *path);

int navi_route_tcnx_load(const char *path);

int navi_route_process_start(const char *path);

int navi_route_process(const NaviWaypointAdc *wpadc);

int navi_route_process_end(const NaviWaypointAdc *wpadc);

void navi_route_process_terminate(void);

void navi_route_process_exit(void);

int navi_route_match(double lng, double lat, float course, uint8_t is_reverse, NaviRouteProgress *progress);

int navi_route_find(double lng, double lat, uint8_t is_reverse, NaviRouteProgress *progress);

int navi_route_wp_find(float dist, uint8_t is_reverse, double *lng, double *lat);

int navi_route_wp_load(uint8_t is_reverse, uint32_t idx);

void navi_route_match_stop(void);

void navi_route_reverse(void);

const NaviRouteData* navi_route_data_get_impl(void);

const NaviRouteWpSample* navi_route_wp_sample_get_impl(void);

const NaviRouteWpNearby* navi_route_wp_nearby_get_impl(void);

#ifdef __cplusplus
}
#endif

#endif
