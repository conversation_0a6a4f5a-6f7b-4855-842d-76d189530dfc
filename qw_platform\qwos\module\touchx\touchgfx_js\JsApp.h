/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   JsApp.h
* 
**************************************************************************/
#ifndef _JSAPP_H_
#define _JSAPP_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "../../quickjs/quickjs.h"
#include "touchgfx_js_api.h"

    /**
     * @brief 初始化
     */
    void js_touchgfx_add_app(JSContext* ctx, JSModuleDef* m);
    int js_touchgfx_init_app(JSContext* ctx, JSModuleDef* m);
    JSValue js_touchgfx_app_method(JSContext* ctx, JSValueConst this_val, int magic, JSValueConst param[]);

    /**
     * @brief 表盘截屏
     */
    void js_create_new_dial_snapshot(uint32_t goodsId, void *buff, uint32_t index);

    /**
     * @brief 创建表盘 会保留缓存 不析构的话同一个表盘只会创建一次
     * @param void
     */
    bool js_create_dial();
    /**
     * @brief 更新js表盘
     */
    bool js_refresh_dial_view(const char *dialPath);
    /**
     * @brief 创建表盘cache buff
     */
    bool create_dial_cache_buf();
    /**
     * @brief 暂停更新表盘事件 
     */
    bool js_stop_dial_timer();
    /**
     * @brief 开始更新表盘事件 
     */
    bool js_start_dial_timer();
    /**
     * @brief 释放表盘缓存buf
     */
    void js_free_dial_cache_buf();
    /**
     * @brief 析构表盘
     */
    void js_destory_dial();

    /**
     * @brief 获取表盘的buf
     */
    void* get_dial_cache_buf();
    
    typedef struct{
        void *buf;
        uint32_t goodsid;
    }dial_info_t;
    void js_recover_dial_snapshot(dial_info_t *dial_info);

#ifdef __cplusplus
}
#endif

#endif
