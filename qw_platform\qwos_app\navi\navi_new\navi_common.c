#include <string.h>
#include <stddef.h>
#include <stdbool.h>
#include "navi_common.h"

//使用给定值覆写自身的范围值
void bbox_update(Bbox *self, double lng_min, double lng_max, double lat_min, double lat_max)
{
    if (self != NULL)
    {
        self->lng_min = lng_min;
        self->lng_max = lng_max;
        self->lat_min = lat_min;
        self->lat_max = lat_max;
    }
}

//使用给定bbox覆写自身
void bbox_copy(Bbox *self, const Bbox *bbox)
{
    if (self != NULL && bbox != NULL)
    {
        memcpy(self, bbox, sizeof(Bbox));
    }
}

//合并给定bbox的范围值，除非给定bbox位于自身内部，否则合并后范围会变大
void bbox_merge(Bbox *self, const Bbox *bbox)
{
    if (self != NULL && bbox != NULL)
    {
        if (bbox->lng_min < self->lng_min)
        {
            self->lng_min = bbox->lng_min;
        }

        if (bbox->lng_max > self->lng_max)
        {
            self->lng_max = bbox->lng_max;
        }

        if (bbox->lat_min < self->lat_min)
        {
            self->lat_min = bbox->lat_min;
        }

        if (bbox->lat_max > self->lat_max)
        {
            self->lat_max = bbox->lat_max;
        }
    }
}

//使用给定经纬度扩展自身范围，除非该点位于自身内部，否则扩展后范围会变大
void bbox_extend(Bbox *self, double lng, double lat)
{
    if (self != NULL)
    {
        if (lng < self->lng_min)
        {
            self->lng_min = lng;
        }

        if (lng > self->lng_max)
        {
            self->lng_max = lng;
        }

        if (lat < self->lat_min)
        {
            self->lat_min = lat;
        }

        if (lat > self->lat_max)
        {
            self->lat_max = lat;
        }
    }
}

//检查两个bbox是否重叠
//true - 重叠
//false - 不重叠
uint8_t bbox_is_overlap(Bbox *self, const Bbox *bbox)
{
    if (self != NULL && bbox != NULL)
    {
        if (bbox->lng_min > self->lng_max || bbox->lng_max < self->lng_min || bbox->lat_min > self->lat_max || bbox->lat_max < self->lat_min)
        {
            return false;
        }
        else
        {
            return true;
        }
    }

    return false;
}

//使用给定值覆写自身的范围值
void dseg_update(Dseg *self, float dist_min, float dist_max)
{
    if (self != NULL)
    {
        self->dist_min = dist_min;
        self->dist_max = dist_max;
    }
}

//使用给定dseg覆写自身
void dseg_copy(Dseg *self, const Dseg *dseg)
{
    if (self != NULL && dseg != NULL)
    {
        self->dist_min = dseg->dist_min;
        self->dist_max = dseg->dist_max;
    }
}

//检查一个距离值是否在分段中
//true - 在
//false - 不在
uint8_t dseg_is_contain(Dseg *self, float dist)
{
    if (self != NULL)
    {
        if (dist >= self->dist_min && dist <= self->dist_max)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    return false;
}

//使用给定值覆写自身范围值
void range_update(Range *self, uint32_t start, uint32_t end)
{
    if (self != NULL)
    {
        self->start = start;
        self->end = end;
    }
}

//使用给定range覆写自身范围值
void range_copy(Range *self, const Range *range)
{
    if (self != NULL && range != NULL)
    {
        memcpy(self, range, sizeof(Range));
    }
}