/***************************************Copyright (c)****************************************/
//                              <PERSON>han <PERSON>wu Technology Co., Ltd
//
//---------------------------------------File Info--------------------------------------------
// File name         : ble_cmd_response.h
// Created by        : jiangzhen
// Descriptions      : ble通道1 状态、结束、特殊通知命令公共接口函数.h文件
//--------------------------------------------------------------------------------------------
// History           :
// 2020-05-14        :原始版本
/*********************************************************************************************/
#if !defined(__BLE_CMD_RESPONSE_H_)
    #define __BLE_CMD_RESPONSE_H_

    #if defined(__cplusplus)
extern "C"
{
#endif

#include "stdint.h"
#include "ble_cmd_common.h"

//-------------------------------------------------------------------------------------------
// Function Name : ble_cmd_status_tx
// Purpose       : 通道1 发送状态命令
// Param[in]     : uint8_t service_type      
//                 uint8_t sub_service_type  
//                 uint8_t op_type           
//                 uint8_t sub_op_type       
//                 STATUS_TYPE status        
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-14
//-------------------------------------------------------------------------------------------
void ble_cmd_status_tx(uint8_t service_type, uint8_t sub_service_type, uint8_t op_type, uint8_t sub_op_type, STATUS_TYPE status);

//-------------------------------------------------------------------------------------------
// Function Name : ble_cmd_notice_tx
// Purpose       : 通道1 发送特殊通知命令
// Param[in]     : uint8_t service_type      
//                 uint8_t sub_service_type  
//                 uint8_t op_type           
//                 uint8_t sub_op_type       
//                 uint8_t status            
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-14
//-------------------------------------------------------------------------------------------
void ble_cmd_notice_tx(uint8_t service_type, uint8_t sub_service_type, uint8_t op_type, uint8_t sub_op_type, uint8_t status);

//-------------------------------------------------------------------------------------------
// Function Name : ble_cmd_notice_tx_with_timestamp
// Purpose       : 通道1 发送特殊通知命令，带时间戳
// Param[in]     : uint8_t service_type      
//                 uint8_t sub_service_type  
//                 uint8_t op_type           
//                 uint8_t sub_op_type       
//                 uint8_t status            
//                 uint32_t timestamp
// Param[out]    : None
// Return type   : 
//-------------------------------------------------------------------------------------------
void ble_cmd_notice_tx_with_timestamp(uint8_t service_type, uint8_t sub_service_type, uint8_t op_type, uint8_t sub_op_type, uint8_t status, uint32_t timestamp);

//-------------------------------------------------------------------------------------------
// Function Name : ble_cmd_end_type_tx
// Purpose       : 发送结束命令，命令结束、帧结束、文件结束等
// Param[in]     : uint32_t service_index  
//                 uint32_t sub_service    
//                 uint32_t operate_type   
//                 uint16_t data_length    
//                 END_TYPE end_type       
//                 uint8_t pb_crc          
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-15
//-------------------------------------------------------------------------------------------
void ble_cmd_end_type_tx(uint32_t service_index, uint32_t sub_service, uint32_t operate_type, uint32_t sub_operate_type, uint16_t data_length, END_TYPE end_type, uint8_t pb_crc);

void ble_cmd_end_data_stream_type_tx(uint32_t service_index, uint32_t data_flow,uint32_t sub_service, uint32_t operate_type, uint32_t sub_operate_type, uint16_t data_length, \
		END_TYPE end_type, uint8_t pb_crc);
//-------------------------------------------------------------------------------------------
// Function Name : ble_cmd_end_tx
// Purpose       : 通道1 发送结束命令
// Param[in]     : uint32_t service_index  
//                 uint32_t sub_service    
//                 uint32_t operate_type   
//                 uint16_t data_length    
//                 uint8_t pb_crc          
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-14
//-------------------------------------------------------------------------------------------
void ble_cmd_end_tx(uint32_t service_index, uint32_t sub_service, uint32_t operate_type, uint32_t sub_operate_type, uint16_t data_length, uint8_t pb_crc);


//-------------------------------------------------------------------------------------------
// Function Name : ble_cmd_err_status_tx
// Purpose       : 通道1 回复数据错误状态命令
// Param[in]     : ble_end_cmd_st *end_cmd  
// Param[out]    : None
// Return type   : 
// Comment       : 2019-04-01
//-------------------------------------------------------------------------------------------
void ble_cmd_err_status_tx(uint8_t service_type, uint8_t sub_service_type, uint8_t op_type, uint8_t sub_op_type);

//-------------------------------------------------------------------------------------------
// Function Name : ble_cmd_success_status_tx
// Purpose       : 通道1 回复成功状态命令
// Param[in]     : uint8_t service_type      
//                 uint8_t sub_service_type  
//                 uint8_t op_type           
//                 uint8_t sub_op_type       
// Param[out]    : None
// Return type   : 
// Comment       : 2019-04-01
//-------------------------------------------------------------------------------------------
void ble_cmd_success_status_tx(uint8_t service_type, uint8_t sub_service_type, uint8_t op_type, uint8_t sub_op_type);

void ble_periph_cmd_status_tx(uint8_t service_type, uint8_t sub_service_type, uint8_t op_type, uint8_t sub_op_type, STATUS_TYPE status, uint8_t channel);
void ble_periph_cmd_err_status_tx(uint8_t service_type, uint8_t sub_service_type, uint8_t op_type, uint8_t sub_op_type,uint8_t channel);
void ble_periph_cmd_success_status_tx(uint8_t service_type, uint8_t sub_service_type, uint8_t op_type, uint8_t sub_op_type,uint8_t channel);

#if defined(__cplusplus)
    #endif

#endif
