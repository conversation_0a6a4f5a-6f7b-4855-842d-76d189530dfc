/***************************************Copyright (c)****************************************/
//                              <PERSON>han <PERSON> Technology Co., Ltd
//
//---------------------------------------File Info--------------------------------------------
// File name         : factory_pb.c
// Created by        : jiangzhen
// Descriptions      : 工厂配置.c文件
//--------------------------------------------------------------------------------------------
// History           :
// 2019-07-25        :原始版本
/*********************************************************************************************/
#include "ble_cmd_response.h"
#include "ble_cmd_common.h"
#include "ble_nus_srv.h"

#include "pb.h"
#include "pb_encode.h"
#include "pb_decode.h"
#include "pb_decode_common.h"
#include "pb_encode_common.h"

#include "factory.pb.h"
#include "factory_pb.h"

#include "file_manage.h"
#include "sys_time.h"
#include "file_system_manage.h"
#include "crc8.h"
#include "igs_global.h"
#include "string.h"
#include "factory_pb_data_inf.h"
#include "simfdsflash.h"
#include "qw_time_service.h"
#include "qw_system_params.h"
#include "cfg_header_def.h"
#include "factory_data.h"
#include "lib_gm_common.h"

//-------------------------------------------------------------------------------------------
// Function Name : string_to_long_hex
// Purpose       : 将字符串转换成uint64_t类型
// Param[in]     : char * src       
//                 uint64_t *value  
// Param[out]    : None
// Return type   : static
// Comment       : 2020-12-23
//-------------------------------------------------------------------------------------------
static void string_to_long_hex(char * src, uint64_t *value)
{
    *value = 0;
    
    if (strspn(src, "0123456789ABCDEF") != strlen(src))
    {
        return;
    }
    else
    {
        for (uint8_t i = 0; i < strlen(src); i++)
        {
            if (src[i] >= '0' && src[i] <= '9')
            {
                *value = *value * 16 + src[i] - '0';
            }

            if (src[i] >= 'A' && src[i] <= 'F')
            {
                *value = *value * 16 + src[i] - 55;
            }
            
        }
    }
}

//-------------------------------------------------------------------------------------------
// Function Name : factory_battary_send
// Purpose       : 发送电池电量信息
// Param[in]     : void  
// Param[out]    : None
// Return type   : static
// Comment       : 2021-02-04
//-------------------------------------------------------------------------------------------
static void factory_battary_send(void)
{
  uint8_t pb_crc = 0;
  uint8_t *data = NULL;
  uint16_t *length = NULL;	

  factory_msg factory_message;

  memset(&factory_message, 0, sizeof(factory_msg));
  ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);
  qw_sys_global_variable_t *sys_param = get_system_params();

  //参数赋值
  factory_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_FACTORY;      
  factory_message.factory_operate_type = FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_BATTARY_GET;
  factory_message.has_battary_msg = true;
  factory_message.battary_msg.has_power_percent = true;
  factory_message.battary_msg.power_percent = sys_param->battery_level;
  factory_message.battary_msg.has_voltage = true;
  factory_message.battary_msg.voltage = sys_param->battery_vol;
  factory_message.battary_msg.has_charge_status = true;
  factory_message.battary_msg.charge_status = sys_param->charge_state;
  
  //编码缓存
  pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
  //编码
  pb_encode(&encode_stream, factory_msg_fields, &factory_message);

  *length = encode_stream.bytes_written;
  ble_nus_data_tx_ch0( );

  //对pb编码进行crc校验
  pb_crc = CRC_Calc8_Table_L(data, *length);

  //命令协议 发送通道2
  ble_cmd_end_tx(factory_message.service_type, 0, factory_message.factory_operate_type, 0, encode_stream.bytes_written, pb_crc);
}


//-------------------------------------------------------------------------------------------
// Function Name : factory_memory_send
// Purpose       : 发送内存信息
// Param[in]     : void  
// Param[out]    : None
// Return type   : static
// Comment       : 2021-02-04
//-------------------------------------------------------------------------------------------
static void factory_memory_send(void)
{
    uint8_t pb_crc = 0;
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    uint32_t total_size = 0;
    uint32_t free_size = 0;

    factory_msg factory_message;

    memset(&factory_message, 0, sizeof(factory_msg));
    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);
    file_manage_memory_get(&total_size, &free_size);    
    //参数赋值
    factory_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_FACTORY;      
    factory_message.factory_operate_type = FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_MEMORY_GET;
    factory_message.has_memory_msg = true;
    factory_message.memory_msg.has_remain = true;
    factory_message.memory_msg.remain = free_size;
    factory_message.memory_msg.has_total = true;
    factory_message.memory_msg.total = total_size;

    //编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    //编码
    pb_encode(&encode_stream, factory_msg_fields, &factory_message);

    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch0( );

    //对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);

    //命令协议 发送通道2
    ble_cmd_end_tx(factory_message.service_type, 0, factory_message.factory_operate_type, 0, encode_stream.bytes_written, pb_crc);
}


//-------------------------------------------------------------------------------------------
// Function Name : factory_sensor_encode
// Purpose       : 传感器编码
// Param[in]     : pb_ostream_t *stream     
//                 const pb_field_t *field  
//                 void * const *arg        
// Param[out]    : None
// Return type   : static
// Comment       : 2021-02-04
//-------------------------------------------------------------------------------------------
static bool factory_sensor_encode(pb_ostream_t *stream, const pb_field_t *field, void * const *arg)
{
    uint8_t status = false;    
    factory_sensor_message factory_sensor_st;
    qw_tm_t sysTime_t;
    uint32_t sysTime_s = 0;

    memset ((char *)&sysTime_t, 0, sizeof(qw_tm_t));
    sys_rtc_get(&sysTime_t);
    //sysTime_s = util_calendar_2_fittime(&sysTime_t, 0, 0, 0, 0, 0, 0);
    sysTime_s = service_rtctime_2_fittime((tm_t *)&sysTime_t);

    for (uint8_t i = FACTORY_SENSOR_TYPE_enum_GPS; i <= _FACTORY_SENSOR_TYPE_MAX; i ++)
    {
        memset(&factory_sensor_st, 0, sizeof(factory_sensor_message));
        switch (i)
        {
            case FACTORY_SENSOR_TYPE_enum_GPS:
                factory_sensor_st.has_sensor_type = true;
                factory_sensor_st.sensor_type = FACTORY_SENSOR_TYPE_enum_GPS;
                factory_sensor_st.has_status = true;
                factory_sensor_st.status = true;            //需要修改
                factory_sensor_st.has_data = true;
                factory_sensor_st.data = factory_gps_status_get();
                break;
            case FACTORY_SENSOR_TYPE_enum_RTC:
                factory_sensor_st.has_sensor_type = true;
                factory_sensor_st.sensor_type = FACTORY_SENSOR_TYPE_enum_RTC;
                factory_sensor_st.has_status = true;
                factory_sensor_st.status = true;    //需要修改 
                factory_sensor_st.has_data = true;
                factory_sensor_st.data = sysTime_s;
                break;
            case FACTORY_SENSOR_TYPE_enum_AIR:
                factory_sensor_st.has_sensor_type = true;
                factory_sensor_st.sensor_type = FACTORY_SENSOR_TYPE_enum_AIR;
                factory_sensor_st.has_status = false;
                factory_sensor_st.has_data = true;
                factory_sensor_st.data = factory_sensor_alt_altitude_get(); // 单位dm
                break;
            case FACTORY_SENSOR_TYPE_enum_TEM:
                factory_sensor_st.has_sensor_type = true;
                factory_sensor_st.sensor_type = FACTORY_SENSOR_TYPE_enum_TEM;
                factory_sensor_st.has_status = false;
                factory_sensor_st.has_data = true;
                factory_sensor_st.data = factory_sensor_alt_temperature_get();
                break;
            case FACTORY_SENSOR_TYPE_enum_ACC:
                factory_sensor_st.has_sensor_type = true;
                factory_sensor_st.sensor_type = FACTORY_SENSOR_TYPE_enum_ACC;
                factory_sensor_st.has_status = true;
                factory_sensor_st.status = factory_acc_is_ok();
                factory_sensor_st.has_data = false;
                break;
            case FACTORY_SENSOR_TYPE_enum_ANT:
                factory_sensor_st.has_sensor_type = true;
                factory_sensor_st.sensor_type = FACTORY_SENSOR_TYPE_enum_ANT;
                factory_sensor_st.has_status = true;
                factory_sensor_st.status = factory_ant_sensor_is_ok();
                factory_sensor_st.has_data = false;
                break;
            case FACTORY_SENSOR_TYPE_enum_KEY:
                factory_sensor_st.has_sensor_type = true;
                factory_sensor_st.sensor_type = FACTORY_SENSOR_TYPE_enum_KEY;
                factory_sensor_st.has_status = true;
                factory_sensor_st.status = true;       //需要修改 
                factory_sensor_st.has_data = false;
                break;
            default:
                break;
        }

        status = pb_encode_tag_for_field(stream, field);
        status &= pb_encode_submessage(stream, factory_sensor_message_fields, &factory_sensor_st); 
    }
    
    return status;
}


//-------------------------------------------------------------------------------------------
// Function Name : factory_sensor_send
// Purpose       : 发送传感器相关的信息，包括GPS、RTC、气压、温度、加速度、-
//                 ANT、KEY等
// Param[in]     : None
// Param[out]    : None
// Return type   : static
// Comment       : 2021-02-04
//-------------------------------------------------------------------------------------------
static void factory_sensor_send(void)
{
  uint8_t pb_crc = 0;
  uint8_t *data = NULL;
  uint16_t *length = NULL;	

  factory_msg factory_message;
  factory_sensor_message factory_sensor_st;

  memset(&factory_message, 0, sizeof(factory_msg));
  memset(&factory_sensor_st, 0, sizeof(factory_sensor_message));
  ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

  //参数赋值
  factory_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_FACTORY;      
  factory_message.factory_operate_type = FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_SENSOR_GET;
  factory_message.factory_sensor_msg.arg = &factory_sensor_st;
  factory_message.factory_sensor_msg.funcs.encode = &factory_sensor_encode;

  //编码缓存
  pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
  //编码
  pb_encode(&encode_stream, factory_msg_fields, &factory_message);

  *length = encode_stream.bytes_written;
  ble_nus_data_tx_ch0( );

  //对pb编码进行crc校验
  pb_crc = CRC_Calc8_Table_L(data, *length);

  //命令协议 发送通道2
  ble_cmd_end_tx(factory_message.service_type, 0, factory_message.factory_operate_type, 0, encode_stream.bytes_written, pb_crc);
}

//-------------------------------------------------------------------------------------------
// Function Name : factory_rtc_send
// Purpose       : 发送RTC时间
// Param[in]     : void  
// Param[out]    : None
// Return type   : static
// Comment       : 2021-03-02
//-------------------------------------------------------------------------------------------
static void factory_rtc_send(void)
{
    uint8_t pb_crc = 0;
    uint8_t *data = NULL;
    uint16_t *length = NULL;  

    factory_msg factory_message;

    memset(&factory_message, 0, sizeof(factory_msg));
    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

    //参数赋值
    factory_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_FACTORY;      
    factory_message.factory_operate_type = FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_RTC_GET;
    factory_message.rtc_msg.has_time = true;
    factory_message.rtc_msg.time = g_sysTime.sysTime_s;

    //编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    //编码
    pb_encode(&encode_stream, factory_msg_fields, &factory_message);

    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch0( );

    //对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);

    //命令协议 发送通道2
    ble_cmd_end_tx(factory_message.service_type, 0, factory_message.factory_operate_type, 0, encode_stream.bytes_written, pb_crc);
}

//-------------------------------------------------------------------------------------------
// Function Name : factory_gnss_send
// Purpose       : 发送RTC时间
// Param[in]     : void  
// Param[out]    : None
// Return type   : static
// Comment       : 2021-03-02
//-------------------------------------------------------------------------------------------
static void factory_gnss_send(void)
{
    uint8_t pb_crc = 0;
    uint8_t *data = NULL;
    uint16_t *length = NULL;  

    factory_msg factory_message;

    memset(&factory_message, 0, sizeof(factory_msg));
    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

    //参数赋值
    factory_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_FACTORY;      
    factory_message.factory_operate_type = FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_RTC_GET;
    factory_message.gps_gnss_msg.has_gnss_gnss_cmd= true;
    factory_message.gps_gnss_msg.gnss_gnss_cmd = factory_gps_workmode_get();

    //编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    //编码
    pb_encode(&encode_stream, factory_msg_fields, &factory_message);

    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch0( );

    //对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);

    //命令协议 发送通道2
    ble_cmd_end_tx(factory_message.service_type, 0, factory_message.factory_operate_type, 0, encode_stream.bytes_written, pb_crc);
}


//-------------------------------------------------------------------------------------------
// Function Name : factory_sn_encode
// Purpose       : PB子消息编码
// Param[in]     : pb_ostream_t *stream     
//                 const pb_field_t *field  
//                 void * const *arg        
// Param[out]    : None
// Return type   : static
// Comment       : 2019-07-25
//-------------------------------------------------------------------------------------------
static bool factory_sn_gomore_encode(pb_ostream_t *stream, const pb_field_t *field, void * const *arg)
{
  uint8_t status = false;    
  factory_sn_message factory_sn_st;
  memset(&factory_sn_st, 0, sizeof(factory_sn_message));
  // char gomore_key[65];
  char sn_str[PRODUCT_SN_LENGHT] = {0};
  // rt_memset(gomore_key, 0, sizeof(gomore_key));
  // gomore_pkey_get(gomore_key, 64);
  //获取产品SN号
  snprintf(sn_str, 11, "%s", factory_data_get_sn());
  factory_sn_st.sn.arg = sn_str;//factory_get_product_sn();
  factory_sn_st.sn.funcs.encode = &encode_string;
//   factory_sn_st.go_more_key.arg = gomore_key;
//   factory_sn_st.go_more_key.funcs.encode = &encode_string;
  status = pb_encode_tag_for_field(stream, field);
  status &= pb_encode_submessage(stream, factory_sn_message_fields, &factory_sn_st); 

  return status;
}

//-------------------------------------------------------------------------------------------
// Function Name : factory_sn_send
// Purpose       : 发送工厂配置信息
// Param[in]     : None
// Param[out]    : None
// Return type   : 
// Comment       : 2019-07-25
//-------------------------------------------------------------------------------------------
static void factory_sn_send(void)
{
  uint8_t pb_crc = 0;
  uint8_t *data = NULL;
  uint16_t *length = NULL;	

  factory_msg factory_message;

  memset(&factory_message, 0, sizeof(factory_msg));
  ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

  //参数赋值
  factory_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_FACTORY;      
  factory_message.factory_operate_type = FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_SN_GET;//FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_SN_SEND;
  factory_message.factory_sn_msg.funcs.encode = &factory_sn_gomore_encode;

  //编码缓存
  pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
  //编码
  pb_encode(&encode_stream, factory_msg_fields, &factory_message);

  *length = encode_stream.bytes_written;
  ble_nus_data_tx_ch0( );

  //对pb编码进行crc校验
  pb_crc = CRC_Calc8_Table_L(data, *length);

  //命令协议 发送通道2
  ble_cmd_end_tx(factory_message.service_type, 0, factory_message.factory_operate_type, 0, encode_stream.bytes_written, pb_crc);
}

//-------------------------------------------------------------------------------------------
// Function Name : factory_sn_decode
// Purpose       : PB子消息解码
// Param[in]     : pb_istream_t *stream     
//                 const pb_field_t *field  
//                 void **arg               
// Param[out]    : None
// Return type   : static
// Comment       : 2019-07-25
//-------------------------------------------------------------------------------------------
static bool factory_sn_gomorekey_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
  factory_sn_message** expected = (factory_sn_message**)arg;
  factory_sn_message submsg = {0};

  submsg.sn.arg = (*expected)->sn.arg;
  submsg.sn.funcs.decode = (*expected)->sn.funcs.decode;

  submsg.go_more_key.arg = (*expected)->go_more_key.arg;
  submsg.go_more_key.funcs.decode = (*expected)->go_more_key.funcs.decode;

  if (!pb_decode(stream, factory_sn_message_fields, &submsg))
      return false;

  return true;       
}

//-------------------------------------------------------------------------------------------
// Function Name : factory_config_decode
// Purpose       : 工厂配置功能 PB命令解析接口函数
// Param[in]     : uint8_t * pb_buffer     
//                 uint16_t buffer_length  
//                 END_TYPE end_type       
// Param[out]    : None
// Return type   : 
// Comment       : 2019-07-25
//-------------------------------------------------------------------------------------------
void factory_config_decode(uint8_t * pb_buffer, uint16_t buffer_length, END_TYPE end_type)
{
    uint8_t status = false;
    factory_msg factory_message;
    factory_sn_message factory_sn_st;
    char sn[PRODUCT_SN_LENGHT] = {0};
    char go_more_key[65] = {0};
    uint64_t sn_num = 0;

    memset(&factory_message, 0, sizeof(factory_msg));
    memset(&factory_sn_st, 0, sizeof(factory_sn_message));

    factory_sn_st.sn.arg = sn;
    factory_sn_st.sn.funcs.decode = &decode_string;
    factory_sn_st.go_more_key.arg = go_more_key;
    factory_sn_st.go_more_key.funcs.decode = &decode_string;

    factory_message.factory_sn_msg.arg = &factory_sn_st;
    factory_message.factory_sn_msg.funcs.decode = &factory_sn_gomorekey_decode;

    pb_istream_t decode_stream = pb_istream_from_buffer(pb_buffer, buffer_length);
    
    status = pb_decode(&decode_stream, factory_msg_fields, &factory_message);

    if (true == status)
    {
        switch(factory_message.factory_operate_type)
        {
#if 1
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_BATTARY_GET:
                factory_battary_send();
                break;

            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_SN_GET:
                factory_sn_send();
                break;
#else
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_SN_SET:
                string_to_long_hex((char *)sn, &sn_num);
                if (0 != sn_num)
                {
                    factory_sn_updated(sn_num);
                    simfds_write_sn_and_gomore_key(sn, rt_strlen(sn) + 1, go_more_key, rt_strlen(go_more_key) + 1);
                    ble_cmd_success_status_tx(factory_message.service_type, 0, factory_message.factory_operate_type, 0);
                }
                else
                {
                    ble_cmd_status_tx(factory_message.service_type, 0,  factory_message.factory_operate_type, 0, enumFAIL_STATUS);
                }                
                break;
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_SENSOR_GET:
                factory_sensor_send();
                break;
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_MEMORY_GET:
                factory_memory_send();
                break;
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_BATTARY_GET:
                factory_battary_send();
                break;
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_SIM_FIT_SET:
                ble_cmd_success_status_tx(factory_message.service_type, 0, factory_message.factory_operate_type, 0);
                factory_simulate_ride_files(factory_message.sim_fit_msg.num, factory_message.sim_fit_msg.size);
                break;
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_GPS_COORDINATE_SET:     //暂时不用
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_SUN_TIME_SET:                       //暂时不用
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_SUN_TIME_GET:                       //暂时不用
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_POWER_SAVE_TIME_SET:    //暂时不用
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_RTC_SET:                                    //暂时不用             
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_RTC_GET:
                factory_rtc_send();
                break;
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_FILTER_SET:                             //暂时不用
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_FILTER_GET:                             //暂时不用
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_GPS_CONTROL_CMD_SET:    //暂时不用
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_GPS_CONTROL_CMD_GET:    //暂时不用             
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_GPS_GNSS_SET:
                if (factory_message.gps_gnss_msg.has_gnss_gnss_cmd)
                {
                    factory_gps_workmode_set((uint32_t)factory_message.gps_gnss_msg.gnss_gnss_cmd);
                }
                break;
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_GPS_GNSS_GET:                          
                factory_gnss_send();
                break;
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_GPS_DYNAMIC_SET:                //暂时不用
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_GPS_DYNAMIC_GET:                //暂时不用
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_GPS_CMD_SET:
                ble_cmd_success_status_tx(factory_message.service_type, 0, factory_message.factory_operate_type, 0);
                break;
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_CONTROL_SET:
                ble_cmd_success_status_tx(factory_message.service_type, 0, factory_message.factory_operate_type, 0);
                switch(factory_message.control_msg.cmd_type)
                {                    
                    case CONTROL_CMD_TYPE_enum_CMD_ENTER_CHECK:
                    	factory_system_enter_check_mode();
                        break;
                    case CONTROL_CMD_TYPE_enum_CMD_LOCK:        //暂时不用
                        break;
                    case CONTROL_CMD_TYPE_enum_CMD_FORMAT:
                        break;
                    case CONTROL_CMD_TYPE_enum_CMD_LFORMAT:
                    	factory_system_filesystem_format();
                        break;
                    case CONTROL_CMD_TYPE_enum_CMD_PARA_RESET:
                    	factory_system_params_format();
                        break;
                    case CONTROL_CMD_TYPE_enum_CMD_FACTORY_RESET:
                    	factory_system_facory_reset();
                        break;
                    case CONTROL_CMD_TYPE_enum_CMD_TEMPERATURE_LOG:
                    	factory_system_enable_temperatue_log(factory_message.control_msg.has_status);
                        break;
                    case CONTROL_CMD_TYPE_enum_CMD_VOLTAGE_LOG:
                    	factory_system_enable_voltage_log(factory_message.control_msg.has_status);
                        break;
                    case CONTROL_CMD_TYPE_enum_CMD_ANT_LOG:
                    	factory_system_enable_ant_log(factory_message.control_msg.has_status);
                        break;
                    default:
                        break;
                }                
                break;
#endif
            default:
                ble_cmd_status_tx(factory_message.service_type, 0,  factory_message.factory_operate_type, 0, enumINVALID_CMD);
                break;
        }
    }    
    else
    {
        ble_cmd_err_status_tx(factory_message.service_type, 0, factory_message.factory_operate_type, 0);
    }    
}

//-------------------------------------------------------------------------------------------
// Function Name : factory_config_handle
// Purpose       : 工厂配置功能BLE通信通道2状态处理接口函数
// Param[in]     : uint8_t *buf  
// Param[out]    : None
// Return type   : 
// Comment       : 2019-07-25
//-------------------------------------------------------------------------------------------
void factory_config_handle(uint8_t *buf)
{
    ble_status_cmd_st *ble_status_cmd_s = (ble_status_cmd_st *)buf;
    uint8_t status = ble_status_cmd_s->status;

    if (enmuDATA_ERR_STATUS == status)
    {   
        switch(ble_status_cmd_s->op_type)
        {
            case FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_SN_SEND:
                factory_sn_send();
                break;
            default:
                break;
        }   
    }
}

/************************************************************************
 *@function:void factory_bat_data_get_cmd(uint8_t bat_lvl);
 *@brief: 发送电池电量数据
 *@param: bat_lvl:电池电量值
 *@return:null
*************************************************************************/
void factory_bat_data_get_cmd(uint8_t bat_lvl)
{
    if (g_device_get_ble_connect_status())
    {
        ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_FACTORY, 0, 
        FACTORY_OPERATE_TYPE_enum_FACTORY_OPERATE_TYPE_BATTARY_GET, 0xff, bat_lvl);
    }
}
