/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   freetypefont.h
@Time    :   2024/12/10 14:26:05
* 
**************************************************************************/

#ifndef __UI_FREETYPEFONT_H
#define __UI_FREETYPEFONT_H

#include <stdint.h>
#include "qw_fs.h"
#include "font_manager.h"
#include "../lv_engine/lvgl.h"
#include "../lv_engine/src/font/lv_font.h"

#ifdef __cplusplus
extern "C" {
#endif

	#define LV_FONT_T_DEF() {NULL, NULL, 0, 0, 0, 0, 0, NULL, NULL, NULL}

	typedef struct
	{
		uint16_t version;
		uint16_t total_num;

		// lv_font_t里的内容
		lv_coord_t line_height;         /**< The real line height where any text fits*/
		lv_coord_t base_line;           /**< Base line measured from the top of the line_height*/
		uint8_t subpx;					/**< An element of `lv_font_subpx_t`*/

		int8_t underline_position;      /**< Distance between the top of the underline and base line (< 0 means below the base line)*/
		int8_t underline_thickness;     /**< Thickness of the underline*/

		int8_t rsv[9];
	} ttf_idx_head_t;

	typedef struct
	{
		uint32_t div_num;
		uint32_t max_size;
		uint32_t rsv[3];
	} ttf_idx_tail_t;

	typedef struct
	{
        uint8_t mark;
#ifdef SIMULATOR
        QW_FIL* fp_idx;
        QW_FIL* fp_buffer;
#else
        QW_FIL fp_idx;
        QW_FIL fp_buffer;
#endif             // SIMULATOR
	} ttf_user_data_t;

	typedef struct
	{
		uint32_t letter;
		uint32_t mark;
	} ttf_lru_head_t;

	typedef struct
	{
		lv_font_glyph_dsc_t dsc;
		uint32_t size;
		uint32_t mark;
		uint16_t letter;
		uint8_t * buf;
	} ttf_lru_data_t;

	typedef struct
	{		
		const char* path;		  //ttf文件路径
		const char* id;			  //字体ID标识符
		uint8_t size;			  //字体大小
	} FREETYPE_HEADER_t;

	typedef struct
	{	
		int size_num;	
		const FREETYPE_HEADER_t* g_freetypefont_head_def;
		lv_font_t* g_font_lsit;
		ttf_user_data_t* g_font_data;
	} qw_font_type_t;

	int freetypefont_check_buffer(qw_font_type_t * qw_font_text, const char* save_path);
	void freetypefont_create_buffer(qw_font_type_t * qw_font_text, const char* save_path);
	int freetypefont_check_pct_get();

	/********************************************************************************************
	* Function/Macro Name : freetypefont_init
	* Purpose : 默认字体初始化
	* Param[in] :
	* param ---
	* Param[out] :
	* param ---
	* Return type :
	* Comment : 2023-11-23 16:41:37
	********************************************************************************************/
	void freetypefont_init(qw_font_type_t * qw_font_text, const char* save_path);
	void freetypefont_init_base_part(qw_font_type_t * qw_font_full);

	/********************************************************************************************
	* Function/Macro Name : freetypefont_temp_font_add
	* Purpose : 注册一种临时字体, 字体结构需要使用者自行管理, 删除和调用时需要使用
	* Param[in] :
	* param ---
	* Param[out] :
	* param ---
	* Return type :
	* Comment : 2023-11-23 16:42:37
	********************************************************************************************/
	void freetypefont_temp_font_add(const FREETYPE_HEADER_t* path_ttf);

	/********************************************************************************************
	* Function/Macro Name : freetypefont_temp_font_delete
	* Purpose : 删除临时字体
	* Param[in] :
	* param ---
	* Param[out] :
	* param ---
	* Return type :
	* Comment : 2023-11-23 16:43:17
	********************************************************************************************/
	void freetypefont_temp_font_delete(const FREETYPE_HEADER_t* path_ttf);

	/********************************************************************************************
	* Function/Macro Name : freetypefont_temp_font_get
	* Purpose :使用临时字体
	* Param[in] :
	* param ---
	* Param[out] :
	* param ---
	* Return type : NULL为无效值
	* Comment : 2023-11-23 16:43:45
	********************************************************************************************/
	const lv_font_t* freetypefont_temp_font_get(const FREETYPE_HEADER_t* path_ttf);


#ifdef __cplusplus
}
#endif

#endif	//__UI_FREETYPEFONT_H
