#ifndef NAVI_ROUTE_H
#define NAVI_ROUTE_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include "navi_common.h"
#include "navi_waypoint.h"
#include "navi_tcnx.h"

//路点过滤器，负责滤除无效路点
//无效路点定义为：经纬度无效，或者距离上一个有效路点距离过近
typedef struct _NaviRouteWpFilter
{
    NaviWaypointAdc wpadc;
    uint32_t cnt;               //有效路点计数
} NaviRouteWpFilter;

//导航线路数据计算器，负责计算导航线路整体数据
typedef struct _NaviRouteDataCalculator
{
    NaviWaypointAd wpad;
    Bbox bbox;                  //路书bbox
    float dist;                 //距离（m）
    float ascent;               //总升（m）
    float descent;              //总降（m）
    float alt_min;              //最小海拔（m）
    float alt_max;              //最大海拔（m）
    uint8_t is_first;
} NaviRouteDataCalculator;

//导航线路路点采样器，负责抽取全局路点，用于全局预览等
typedef struct _NaviRouteWpSampler
{
    NaviWaypointDc *wpdc_buf;   //缓冲区，保存全局路点
    float *dd_buf;              //缓冲区，保存本次路点到前后路点的距离变化量之和
    uint32_t capacity;          //全局路点最大点数
    uint32_t len;               //实际选取的路点个数
} NaviRouteWpSampler;

//导航线路路点压缩器，负责压缩线路路点，以减少路点数量
typedef struct _NaviRouteWpCompressor
{
    NaviWaypointDc *buf;        //缓冲区，用于中间计算
    uint32_t capacity;          //缓冲区容量
    uint32_t len;               //缓冲区已存入数据数量
    float h_thres;              //压缩阈值（m），压缩前后最大垂直距离误差不超过该阈值
} NaviRouteWpCompressor;

//导航线路分段器，用于将线路均匀分段，便于分段查找
typedef struct _NaviRouteSegmentor
{
    NaviRouteSegment *seg_buf;  //缓冲区，保存分段信息
    uint32_t capacity;          //分段最大数量
    uint32_t len;               //已创建的分段数量
    uint32_t interval;          //分段间隔
    uint32_t INTERVAL;          //起始分段间隔
    uint32_t cnt;               //路点计数
} NaviRouteSegmentor;

//导航线路进度，即给定位置在导航线路上最匹配的所在
typedef struct _NaviRouteProgress
{
    double lng;                 //最匹配的所在的经度
    double lat;                 //最匹配的所在的纬度
    float dist;                 //最匹配的所在所对应的距离
    uint32_t idx;               //最匹配的所在所对应的路点的索引，取刚经过的那个路点
} NaviRouteProgress;

//导航线路匹配器，根据给定经纬度和航向查找最接近的路点
typedef struct _NaviRouteMatcher
{
    NaviRouteWpList *wp_list;               //路点列表
    NaviRouteSegmentArray *seg_array;       //线路分段
    NaviRouteData *route_data;              //线路数据
    float ref_dist;                         //参考距离，和参考距离差异较小的路点权重更高
} NaviRouteMatcher;

//导航线路查找器，根据给定条件查找相应的路点
typedef struct _NaviRouteFinder
{
    NaviRouteWpList *wp_list;               //路点列表
    NaviRouteSegmentArray *seg_array;       //线路分段
    NaviRouteData *route_data;              //线路数据
} NaviRouteFinder;

//导航线路路点加载器
typedef struct _NaviRouteWpLoader
{
    NaviRouteWpList *wp_list;               //路点列表
    NaviWaypointDc *buf;                    //缓冲区，用于保存加载的路点
    NaviRouteData *route_data;              //线路数据
    Range range;                            //加载的路点的范围
    uint32_t capacity;                      //缓冲区容量
    uint32_t len;                           //缓冲区已存入数据数量
} NaviRouteWpLoader;

int navi_route_wp_filter_exec(NaviRouteWpFilter *self, const NaviWaypointA *wpa, NaviWaypointAdc *output);

int navi_route_wp_filter_the_last_get(NaviRouteWpFilter *self, NaviWaypointAdc *output);

void navi_route_wp_filter_reset(NaviRouteWpFilter *self);

int navi_route_data_calculator_exec(NaviRouteDataCalculator *self, const NaviWaypointAd *wpad);

int navi_route_data_calculator_data_get(NaviRouteDataCalculator *self, NaviRouteData *output);

void navi_route_data_calculator_reset(NaviRouteDataCalculator *self);

int navi_route_wp_sampler_exec(NaviRouteWpSampler *self, const NaviWaypointDc *wpdc);

int navi_route_wp_sampler_data_get(NaviRouteWpSampler *self, NaviRouteWpSample *output);

void navi_route_wp_sampler_reset(NaviRouteWpSampler *self);

int navi_route_wp_compressor_exec(NaviRouteWpCompressor *self, const NaviWaypointDc *wpdc, NaviWaypointDc *output);

int navi_route_wp_compressor_end(NaviRouteWpCompressor *self, NaviWaypointDc *output);

void navi_route_wp_compressor_reset(NaviRouteWpCompressor *self);

int navi_route_segmentor_exec(NaviRouteSegmentor *self, const NaviWaypointDc *wpdc);

void navi_route_segmentor_end(NaviRouteSegmentor *self);

int navi_route_segmentor_data_get(NaviRouteSegmentor *self, NaviRouteSegmentArray *output);

void navi_route_segmentor_reset(NaviRouteSegmentor *self);

int navi_route_matcher_exec(NaviRouteMatcher *self, double lng, double lat, float course, uint8_t is_reverse, NaviRouteProgress *progress);

void navi_route_matcher_reset(NaviRouteMatcher *self);

int navi_route_finder_exec(NaviRouteFinder *self, double lng, double lat, uint8_t is_reverse, NaviRouteProgress *progress);

int navi_route_finder_exec2(NaviRouteFinder *self, float dist, uint8_t is_reverse, double *lng, double *lat);

int navi_route_wp_loader_exec(NaviRouteWpLoader *self, uint8_t is_reverse, uint32_t idx);

void navi_route_wp_loader_data_get(NaviRouteWpLoader *self, NaviRouteWpNearby *wp_nearby);

void navi_route_wp_loader_reset(NaviRouteWpLoader *self);

#ifdef __cplusplus
}
#endif

#endif