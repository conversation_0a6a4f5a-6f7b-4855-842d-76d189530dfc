/****************************************Copyright (c)****************************************
* <PERSON><PERSON>wu Technology Co., Ltd
*
*---------------------------------------File Info--------------------------------------------
* File path :
* Created by : Lxin
* LastEditors: Lxin
* Descriptions :
*--------------------------------------------------------------------------------------------
* History :
* 2023-04-28 16:06:01: Lxin 原始版本
*
*********************************************************************************************/

#include "qw_os_gui.h"
#include "QwAppCtrlInc.h"
#include "../../qwos_app/sports_data/sports_data_show.h"
#include "../../qwos_app/GUI/Translate/QwDataKeyText.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QwGUITheme.h"
#include "Image/images.h"
#include "qw_fit.h"
#include "qw_fit_api.h"
#include "view_page_model_sports.h"
#include "workout_interface.h"
#include "interval_train/interval_train_summary.h"

constexpr auto LAP_LIST_ITEM_HEIGHT = 88;

static int g_lap_list_position = 0;

void GridLap::setup()
{
    add(canvas_);
    canvas_.setPosition(0, 0, getWidth(), getHeight());
    canvas_.set_draw_func(draw_content_cb_);
    canvas_.setForceRefreshBG(true);
}

void GridLap::on_notify()
{
    canvas_.invalidate();
}

void GridLap::on_focus(bool focus)
{
    //The method is an intentionally-blank override.
}

int GridLap::get_scroll_pos_status()
{
    sports_data_show_t temp_data = {0};
    sports_data_show_string_get(&temp_data, DATATYPE_NORMAL_LAP_NUM, false);

    int lap_num = atoi(temp_data.data_full);
    int total_list_height = lap_num * LAP_LIST_ITEM_HEIGHT;

    if (total_list_height <= 310)
    {
        return GridLap::POS::BOTH;
    }
    else if (g_lap_list_position >= 0)
    {
        return GridLap::POS::START;
    }
    else if (total_list_height + g_lap_list_position <= 310)
    {
        return GridLap::POS::END;
    }
    else
    {
        return GridLap::POS::MID;
    }
}

void GridLap::rest_scroll_pos()
{
    g_lap_list_position = 0;
}

void GridLap::draw_content()
{
    setup_list();
    setup_top();
}

void GridLap::setup_top()
{
	uint16_t text_width = 0;
	uint16_t text_y_pos = 0;
	uint16_t data_y_pos = 0;

    sports_data_show_t temp_data = {0};
    sports_data_show_string_get(&temp_data, DATATYPE_TIME_TIME, metric_);

    ctrl_info draw_info(true, false, 3, CTRL_X::FULL, CTRL_Y::ROW_1, temp_data.show_key);
    GridText::draw_text_grid(&draw_info, &temp_data, sports_type_);
}

void GridLap::setup_list()
{
    int lap_num = get_sports_lap_data_count() + 1;
    int total_list_height = lap_num * LAP_LIST_ITEM_HEIGHT;
    int start_y_pos = g_lap_list_position;

    for (int i = 0; i < lap_num; i++)
    {
        if (start_y_pos + LAP_LIST_ITEM_HEIGHT > 0)
        {
            setup_one_item(lap_num - i - 1, start_y_pos);
        }
        start_y_pos += LAP_LIST_ITEM_HEIGHT;
    }
}

void GridLap::setup_one_item(int idx, int pos)
{
    char text[20] = {0};
    int item_y_pos = getHeight() / 3 + pos;
	int text_y_pos = (LAP_LIST_ITEM_HEIGHT - NUMBER_NO_40_FONT.line_height) / 2 + 3; // 视觉微调下移
    sports_data_show_t temp_data = {0};

    // 背景 
    qw_gui_set_lv_color((((get_sports_lap_data_count() - idx) % 2) == 0 ? lv_color_hex(0x333333) : lv_color_black()));
    qw_gui_fill_rect(0, item_y_pos, getWidth(), item_y_pos + LAP_LIST_ITEM_HEIGHT);

    // 序号
    sprintf_array(text, "%02d", idx + 1);
    qw_gui_set_lv_color(lv_color_hex(0xFFFFFF));
	qw_gui_set_text_align(QW_GUI_TA_CENTER, QW_GUI_TA_CENTER, false);
	qw_gui_set_text_font(&NUMBER_NO_40_FONT);
	qw_gui_set_text_range(qw_gui_text_width(text, &NUMBER_NO_40_FONT), NUMBER_NO_40_FONT.line_height);
    qw_gui_draw_text(text, 44, item_y_pos + text_y_pos);


    //如果是间歇训练，需要显示间歇训练的类型
    if (get_cur_course_type() == enum_course_interval)
    {
        it_summary_info_t* step_info = interval_train_summary_get_step_info(idx);
        if (step_info != NULL)
        {
            if ((step_info->type == IT_TRAIN_TEXT_TRAIN || step_info->type == IT_TRAIN_TEXT_REST))
            {
                if (step_info->type == IT_TRAIN_TEXT_TRAIN)
                {
                    snprintf(text, sizeof(text), "T");
                }
                else
                {
                    snprintf(text, sizeof(text), "R");
                }
                qw_gui_set_text_align(QW_GUI_TA_CENTER, QW_GUI_TA_CENTER, false);
                qw_gui_set_text_font(&NUMBER_NO_40_FONT);
                qw_gui_set_text_range(qw_gui_text_width(text, &NUMBER_NO_40_FONT), NUMBER_NO_40_FONT.line_height);
                qw_gui_draw_text(text, 100, item_y_pos + text_y_pos);   
            }
        }
    }

    if (idx == get_sports_lap_data_count())
    {
        sports_data_show_string_get(&temp_data, get_sports_lap_data_type((SPORTTYPE)sports_type_, 0), metric_);
    }
    else
    {
        get_sports_lap_data_by_index((SPORTTYPE)sports_type_, idx, 0, &temp_data, metric_);      
    }
    qw_gui_set_lv_color(lv_color_white());
	qw_gui_set_text_range(qw_gui_text_width(temp_data.data_full, &NUMBER_NO_40_FONT), NUMBER_NO_40_FONT.line_height);
    qw_gui_draw_text(temp_data.data_full, 110, item_y_pos + text_y_pos);
    
    if (idx == get_sports_lap_data_count())
    {
        sports_data_show_string_get(&temp_data, get_sports_lap_data_type((SPORTTYPE)sports_type_, 1), metric_);
    }
    else
    {
        get_sports_lap_data_by_index((SPORTTYPE)sports_type_, idx, 1, &temp_data, metric_); 
    }
    // 显示数据，如果有单位则加上单位
    char display_text[32] = {0};
    if (strlen(temp_data.unit) > 0 && strcmp(temp_data.unit, "-") != 0) {
        snprintf(display_text, sizeof(display_text), "%s%s", temp_data.data_full, temp_data.unit);
    } else {
        strncpy(display_text, temp_data.data_full, sizeof(display_text) - 1);
    }
    qw_gui_set_lv_color(lv_color_white());
	qw_gui_set_text_range(qw_gui_text_width(display_text, &NUMBER_NO_40_FONT), NUMBER_NO_40_FONT.line_height);
    qw_gui_draw_text(display_text, getWidth() - 28 - qw_gui_text_width(display_text, &NUMBER_NO_40_FONT), item_y_pos + text_y_pos);
}
