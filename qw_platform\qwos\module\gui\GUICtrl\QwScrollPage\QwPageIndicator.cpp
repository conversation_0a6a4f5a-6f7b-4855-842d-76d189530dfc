/****************************************Copyright (c)****************************************
* <PERSON><PERSON>wu Technology Co., Ltd
*
*---------------------------------------File Info--------------------------------------------
* File path :
* Created by : Lxin
* LastEditors: Lxin
* Descriptions :
*--------------------------------------------------------------------------------------------
* History :
* 2023-04-28 16:06:01: Lxin 原始版本
*
*********************************************************************************************/
#include "QwPageIndicator.h"

// ========================= TiledImageArc extensions =========================
void TiledImageArc::setBitmapList(const Bitmap* list, uint8_t count)
{
    bitmapList = list;
    bitmapListCount = count;
}

void TiledImageArc::clearBitmapList()
{
    bitmapList = nullptr;
    bitmapListCount = 0;
}

bool TiledImageArc::hasBitmapList() const
{
    return (bitmapList != nullptr) && (bitmapListCount > 0);
}

Bitmap TiledImageArc::getFirstBitmapFromList() const
{
    if (hasBitmapList()) {
        return bitmapList[0];
    }
    return Bitmap();
}

void TiledImageArc::setDynamicBitmapSelector(const Bitmap* normalList, uint8_t normalCount,
                                             const Bitmap& highlighted, uint8_t currentPageIndex)
{
    dynamicNormalList = normalList;
    dynamicNormalCount = normalCount;
    dynamicHighlighted = highlighted;
    dynamicCurrentPage = currentPageIndex;
}

void TiledImageArc::draw(const Rect& invalidatedArea) const
{
    // Determine base tile size (assume uniform sizes across list); fallback to single bitmap
    Bitmap baseBmp = hasBitmapList() ? getFirstBitmapFromList() : bitmap;
    uint16_t bitmapWidth = baseBmp.getWidth();
    uint16_t bitmapHeight = baseBmp.getHeight();

    if (bitmapWidth == 0 || bitmapHeight == 0) {
        return;
    }

    lv_draw_ctx_t* draw_context = lv_get_draw_context();
    Rect clipRect;

    if (!touchx_caculate_context_draw_cliprect(draw_context, invalidatedArea, clipRect)) {
        return;
    }

    Rect mAbs = getAbsoluteRect();
    mAbs.x += xOffset;
    mAbs.y += yOffset;

    lv_draw_img_dsc_t img_dsc;
    lv_draw_img_dsc_init(&img_dsc);
    img_dsc.opa = alpha;
    img_dsc.recolor = lv_color_hex(recolor.color);
    img_dsc.recolor_opa = recolor_alpha;

    lv_area_t clip_area;
    txRect_to_lvArea(clipRect, clip_area);

    lv_area_t coords;
    txRect_to_lvArea(mAbs, coords);

    const lv_area_t* clip_area_ori = draw_context->clip_area;
    draw_context->clip_area = &clip_area;

    // 使用固定的逻辑网格高度来计算圆弧和间距，确保一致性
    const uint16_t logicalTileHeight = 26;  // 使用较大尺寸作为逻辑网格
    const uint16_t logicalTileSpace = TiledImageArc::INDICATOR_SPACE;

    float circleRadius = HAL::DISPLAY_WIDTH / 2 - 12 - logicalTileHeight / 2;
    uint32_t tileIndex = 0;

    lv_area_t area;
    area.y1 = coords.y1;

    for (; area.y1 <= coords.y2; area.y1 += (logicalTileHeight + logicalTileSpace), ++tileIndex)
    {
        // 计算当前tile的逻辑中心Y
        int16_t logicalCenterY = area.y1 + logicalTileHeight / 2 - HAL::DISPLAY_HEIGHT / 2;

#if 1
        float x_f = circleRadius - sqrtf((float)((circleRadius * circleRadius) - (logicalCenterY * logicalCenterY)));
        area.x1 = coords.x1 + (int16_t)(x_f + 0.5f);
#else
        area.x1 = coords.x1;
#endif

        // 动态选择位图：如果设置了动态选择器，根据当前页选择位图
        const void* drawId = nullptr;
        uint16_t currentBmpWidth = bitmapWidth;
        uint16_t currentBmpHeight = bitmapHeight;

        if (dynamicNormalList != nullptr && dynamicNormalCount > 0) {
            // 使用动态位图选择
            if (tileIndex == dynamicCurrentPage) {
                // 当前页：使用高亮位图
                drawId = dynamicHighlighted.getId();
                currentBmpWidth = dynamicHighlighted.getWidth();
                currentBmpHeight = dynamicHighlighted.getHeight();
            } else {
                // 非当前页：使用普通位图（循环）
                const Bitmap& b = dynamicNormalList[tileIndex % dynamicNormalCount];
                drawId = b.getId();
                currentBmpWidth = b.getWidth();
                currentBmpHeight = b.getHeight();
            }
        } else if (hasBitmapList()) {
            const Bitmap& b = bitmapList[tileIndex % bitmapListCount];
            drawId = b.getId();
            currentBmpWidth = b.getWidth();
            currentBmpHeight = b.getHeight();
        } else {
            drawId = bitmap.getId();
        }

        // 在逻辑网格内居中绘制实际位图
        int16_t centerOffsetX = (logicalTileHeight - currentBmpWidth) / 2;
        int16_t centerOffsetY = (logicalTileHeight - currentBmpHeight) / 2;

        area.x1 += centerOffsetX;
        area.x2 = area.x1 + currentBmpWidth - 1;
        area.y2 = area.y1 + centerOffsetY + currentBmpHeight - 1;
        area.y1 += centerOffsetY;

        lv_draw_img(draw_context, &img_dsc, &area, drawId);
    }

    draw_context->clip_area = clip_area_ori;
}

//////////////////////////////////////////////////////////////////

QwPageIndicator::QwPageIndicator() :
    Container(),
    unselectedPages(),
    selectedPage(),
    numberOfPages(0),
    currentPage(0),
    normalBitmap(),
    highlightedBitmap(),
    normalBitmapList(nullptr),
    normalBitmapCount(0)
{
    unselectedPages.setXY(0, 0);
    // 只添加一层，用于绘制所有指示器
    Container::add(unselectedPages);
}

void QwPageIndicator::setNumberOfPages(uint8_t size)
{
    numberOfPages = size;

    // Determine the base bitmap to compute geometry
    Bitmap bmp;
    if (unselectedPages.hasBitmapList()) {
        bmp = unselectedPages.getFirstBitmapFromList();
    } else if (unselectedPages.getBitmap()) {
        bmp = Bitmap(unselectedPages.getBitmap());
    }

    if (bmp.getWidth() > 0 && bmp.getHeight() > 0)
    {
        const int w = bmp.getWidth();
        const int h = bmp.getHeight();
        if (size == 0) {
            unselectedPages.setWidth(0);
            unselectedPages.setHeight(0);
            selectedPage.setWidth(0);
            selectedPage.setHeight(0);
            invalidate();
            setWidthHeight(unselectedPages);
            return;
        }

        unselectedPages.setWidth(w + (size - 1) * TiledImageArc::INDICATOR_SPACE);
        unselectedPages.setHeight(h * size + (size - 1) * TiledImageArc::INDICATOR_SPACE);

        selectedPage.setWidth(w + (size - 1) * TiledImageArc::INDICATOR_SPACE);
        selectedPage.setHeight(h);
        // adjust size of container according to the actual bitmaps
        invalidate();
        setWidthHeight(unselectedPages);
        if (getParent()) {
            setY((getParent()->getHeight() - getHeight()) / 2);
        }
        // Clamp current page within range
        uint8_t clamped = (size > 0) ? (currentPage < size ? currentPage : (uint8_t)(size - 1)) : 0;
        setCurrentPage(clamped);
        invalidate();
    }
}

void QwPageIndicator::setBitmaps(const Bitmap& normalPage, const Bitmap& highlightedPage)
{
    // 存储位图用于动态选择
    normalBitmap = normalPage;
    highlightedBitmap = highlightedPage;
    normalBitmapList = &normalBitmap;
    normalBitmapCount = 1;

    // 更新单层的动态选择器
    unselectedPages.setDynamicBitmapSelector(&normalBitmap, 1, highlightedBitmap, currentPage);

    if (numberOfPages > 0)
    {
        setNumberOfPages(numberOfPages);
    }
}

void QwPageIndicator::setBitmaps(const Bitmap* normalPages, uint8_t normalCount, const Bitmap& highlightedPage)
{
    // 存储位图用于动态选择
    normalBitmapList = normalPages;
    normalBitmapCount = normalCount;
    highlightedBitmap = highlightedPage;

    // 更新单层的动态选择器
    unselectedPages.setDynamicBitmapSelector(normalPages, normalCount, highlightedBitmap, currentPage);

    if (numberOfPages > 0)
    {
        setNumberOfPages(numberOfPages);
    }
}

void QwPageIndicator::goUp()
{
    if (numberOfPages == 0) return;
    setCurrentPage((currentPage + 1) % numberOfPages);
}

void QwPageIndicator::goDown()
{
    if (numberOfPages == 0) return;
    setCurrentPage((currentPage + numberOfPages - 1) % numberOfPages);
}

void QwPageIndicator::setCurrentPage(uint8_t page)
{
    if (page < numberOfPages && page != currentPage)
    {
        currentPage = page;

        // 更新动态选择器的当前页，触发重绘
        if (normalBitmapList != nullptr && normalBitmapCount > 0) {
            unselectedPages.setDynamicBitmapSelector(normalBitmapList, normalBitmapCount, highlightedBitmap, currentPage);
        }

        invalidate();  // 触发重绘
    }
}

uint8_t QwPageIndicator::getNumberOfPages() const
{
    return numberOfPages;
}

uint8_t QwPageIndicator::getCurrentPage() const
{
    return currentPage;
}
