/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   activity_fit_lap.c
@Time    :   2024/12/10 10:25:04
<AUTHOR>   lxin
*
**************************************************************************/
#include "activity_fit_lap.h"
#include "activity_fit_app.h"
#include "qw_fit_api.h"
#include "subscribe_data.h"
#include "subscribe_data_protocol.h"
#include "thread_pool.h"
#include "view_page_model_sports.h"
#ifndef SIMULATOR
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_task.h"
#include "subscribe_service.h"
#endif

// 防重入标记
static bool s_is_activity_fit_lap_open = false;

static FIT_LAP_MESG g_activity_lap;                                         // lap完整缓存
static FIT_LENGTH_MESG g_activity_length;                                      // length完整缓存
static FIT_LAP_MESG g_idle_lap;                                         // 空闲lap完整缓存
static FIT_LENGTH_MESG g_idle_length;                                      // 空闲length完整缓存
static FIT_LAP_MESG g_activity_fake_lap;    // fake_lap完整缓存 使用的是当前圈数据

#ifndef SIMULATOR
/**
 * @brief 订阅算法
 * @param algo 算法名称
 * @param cb 数据处理回调
 * @param cfg 频率配置
 */
static void subscribe_algo(uint32_t algo_id, callback_t cb, optional_config_t* cfg)
{
    if (algo_id == 0 || cb == NULL || cfg == NULL)
    {
        return;
    }

    int32_t ret = qw_dataserver_subscribe_id(algo_id, cb, cfg);
    if (ret != 0)
    {
        ACTIVITY_LOG_E("[activity_fit_lap] @%s@ subscribe %d error = %d", __FUNCTION__, algo_id, ret);
    }
    else
    {
        ACTIVITY_LOG_D("[activity_fit_lap] @%s@ subscribe %d ok", __FUNCTION__, algo_id);
    }
}

/**
 * @brief 取消订阅算法
 * @param algo 算法名称
 * @param cb 数据处理回调
 */
static void unsubscribe_algo(uint32_t data_id, callback_t cb)
{
    if (data_id == 0 || cb == NULL)
    {
        return;
    }

    int32_t ret = qw_dataserver_unsubscribe_id(data_id, cb);
    if (ret != 0)
    {
        ACTIVITY_LOG_E("[activity_fit_lap] @%s@ unsubscribe %d error = %d", __FUNCTION__, data_id, ret);
    }
    else
    {
        ACTIVITY_LOG_D("[activity_fit_lap] @%s@ unsubscribe %d ok", __FUNCTION__, data_id);
    }
}
#endif

/**
 * @brief 取时间算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_timer_in_callback(const void *in, uint32_t len)
{
    const algo_timer_pub_t* p_ctrl = (algo_timer_pub_t *)in;

    if (p_ctrl != NULL)
    {
        g_activity_lap.timestamp = p_ctrl->timer_pre_lap.timestamp;
        g_activity_lap.start_time = p_ctrl->timer_pre_lap.start_time;
        g_activity_lap.total_timer_time = p_ctrl->timer_pre_lap.timer_time;
        g_activity_lap.total_moving_time = p_ctrl->timer_pre_lap.moving_time;

        g_activity_fake_lap.timestamp = p_ctrl->timer_lap.timestamp;
        g_activity_fake_lap.start_time = p_ctrl->timer_lap.start_time;
        g_activity_fake_lap.total_timer_time = p_ctrl->timer_lap.timer_time;
        g_activity_fake_lap.total_moving_time = p_ctrl->timer_lap.moving_time;
    }
}

/**
 * @brief 取速度算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_speed_avg_in_callback(const void *in, uint32_t len)
{
    const algo_speed_avg_pub_t* p_ctrl = (algo_speed_avg_pub_t *)in;

    if (p_ctrl != NULL)
    {
        if (p_ctrl->avg_speed_pre_lap != UINT32_MAX)
        {
        g_activity_lap.enhanced_avg_speed = p_ctrl->avg_speed_pre_lap;
            g_activity_lap.avg_speed = p_ctrl->avg_speed_pre_lap > FIT_UINT16_INVALID ? FIT_UINT16_INVALID : (FIT_UINT16) p_ctrl->avg_speed_pre_lap;
            
            g_activity_fake_lap.enhanced_avg_speed = p_ctrl->avg_speed_lap;
            g_activity_fake_lap.avg_speed = p_ctrl->avg_speed_lap > FIT_UINT16_INVALID ? FIT_UINT16_INVALID : (FIT_UINT16) p_ctrl->avg_speed_lap;
    }
        else
        {
            g_activity_lap.enhanced_avg_speed = FIT_UINT32_INVALID;
            g_activity_lap.avg_speed = FIT_UINT16_INVALID;
            
            g_activity_fake_lap.enhanced_avg_speed = FIT_UINT32_INVALID;
            g_activity_fake_lap.avg_speed = FIT_UINT16_INVALID;
}
    }
}

/**
 * @brief 取速度极值算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_speed_max_in_callback(const void *in, uint32_t len)
{
    const algo_speed_max_pub_t* p_ctrl = (algo_speed_max_pub_t *)in;

    if (p_ctrl != NULL)
    {
        if (p_ctrl->max_speed_pre_lap != UINT32_MAX)
        {
        g_activity_lap.enhanced_max_speed = p_ctrl->max_speed_pre_lap;
            g_activity_lap.max_speed = p_ctrl->max_speed_pre_lap > FIT_UINT16_INVALID ? FIT_UINT16_INVALID : (FIT_UINT16) p_ctrl->max_speed_pre_lap;
            
            g_activity_fake_lap.enhanced_max_speed = p_ctrl->max_speed_lap;
            g_activity_fake_lap.max_speed = p_ctrl->max_speed_lap > FIT_UINT16_INVALID ? FIT_UINT16_INVALID : (FIT_UINT16) p_ctrl->max_speed_lap;
    }
        else
        {
            g_activity_lap.enhanced_max_speed = FIT_UINT32_INVALID;
            g_activity_lap.max_speed = FIT_UINT16_INVALID;
            
            g_activity_fake_lap.enhanced_max_speed = FIT_UINT32_INVALID;
            g_activity_fake_lap.max_speed = FIT_UINT16_INVALID;
}
    }
}

/**
 * @brief 取距离算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_distance_in_callback(const void *in, uint32_t len)
{
    const algo_distance_pub_t* p_ctrl = (algo_distance_pub_t *)in;

    if (p_ctrl != NULL)
    {
        g_activity_lap.total_distance = p_ctrl->distance_pre_lap;

        g_activity_fake_lap.total_distance = p_ctrl->distance_lap;
    }
}

/**
 * @brief 取心率算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_heart_rate_avg_in_callback(const void *in, uint32_t len)
{
    const algo_heart_rate_avg_pub_t* p_ctrl = (algo_heart_rate_avg_pub_t *)in;

    if (p_ctrl != NULL)
    {
        g_activity_lap.avg_heart_rate = p_ctrl->avg_heart_rate_pre_lap;
        g_activity_lap.max_heart_rate = p_ctrl->max_heart_rate_pre_lap;
        g_activity_lap.min_heart_rate = p_ctrl->min_heart_rate_pre_lap;
        
        g_activity_fake_lap.avg_heart_rate = p_ctrl->avg_heart_rate_lap;
        g_activity_fake_lap.max_heart_rate = p_ctrl->max_heart_rate_lap;
        g_activity_fake_lap.min_heart_rate = p_ctrl->min_heart_rate_lap;
    }
}

/**
 * @brief 取高度算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_altitude_in_callback(const void *in, uint32_t len)
{
    const algo_altitude_avg_pub_t* p_ctrl = (algo_altitude_avg_pub_t *)in;

    if (p_ctrl != NULL)
    {
        g_activity_lap.avg_altitude = UTIL_Altitude_100_2FitAlt(p_ctrl->avg_altitude_pre_lap);
        g_activity_lap.max_altitude = UTIL_Altitude_100_2FitAlt(p_ctrl->max_altitude_pre_lap);
        g_activity_lap.min_altitude = UTIL_Altitude_100_2FitAlt(p_ctrl->min_altitude_pre_lap);
        
        g_activity_fake_lap.avg_altitude = UTIL_Altitude_100_2FitAlt(p_ctrl->avg_altitude_lap);
        g_activity_fake_lap.max_altitude = UTIL_Altitude_100_2FitAlt(p_ctrl->max_altitude_lap);
        g_activity_fake_lap.min_altitude = UTIL_Altitude_100_2FitAlt(p_ctrl->min_altitude_lap);
    }
}

/**
 * @brief 取功率均值算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_pwr_avg_in_callback(const void *in, uint32_t len)
{
    const algo_power_avg_pub_t* p_ctrl = (algo_power_avg_pub_t *)in;

    if (p_ctrl != NULL)
    {
        g_activity_lap.avg_power = p_ctrl->avg_power_pre_lap;
        g_activity_lap.max_power = p_ctrl->max_power_pre_lap;
        g_activity_lap.normalized_power = p_ctrl->normalized_power_pre_lap;
        g_activity_lap.left_right_balance = p_ctrl->left_right_balance_pre_lap;
        g_activity_lap.avg_left_torque_effectiveness = p_ctrl->avg_left_torque_effectiveness_pre_lap;
        g_activity_lap.avg_right_torque_effectiveness = p_ctrl->avg_right_torque_effectiveness_pre_lap;
        g_activity_lap.avg_left_pedal_smoothness = p_ctrl->avg_left_pedal_smoothness_pre_lap;
        g_activity_lap.avg_right_pedal_smoothness = p_ctrl->avg_right_pedal_smoothness_pre_lap;

        g_activity_fake_lap.avg_power = p_ctrl->avg_power_lap;
        g_activity_fake_lap.max_power = p_ctrl->max_power_lap;
        g_activity_fake_lap.normalized_power = p_ctrl->normalized_power_lap;
        g_activity_fake_lap.left_right_balance = p_ctrl->left_right_balance_lap;
        g_activity_fake_lap.avg_left_torque_effectiveness = p_ctrl->avg_left_torque_effectiveness_lap;
        g_activity_fake_lap.avg_right_torque_effectiveness = p_ctrl->avg_right_torque_effectiveness_lap;
        g_activity_fake_lap.avg_left_pedal_smoothness = p_ctrl->avg_left_pedal_smoothness_lap;
        g_activity_fake_lap.avg_right_pedal_smoothness = p_ctrl->avg_right_pedal_smoothness_lap;
    }
}

/**
 * @brief 取踏频均值算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_cadence_avg_in_callback(const void *in, uint32_t len)
{
    const algo_cadence_avg_pub_t* p_ctrl = (algo_cadence_avg_pub_t *)in;

    if (p_ctrl != NULL)
    {
        if (FIT_SPORTS_CYCLING <= get_activity_sport_type()) //骑行
        {
            g_activity_lap.avg_cadence = p_ctrl->avg_cadence_pre_lap;
            g_activity_lap.max_cadence = p_ctrl->max_cadence_pre_lap;

            g_activity_fake_lap.avg_cadence = p_ctrl->avg_cadence_lap;
            g_activity_fake_lap.max_cadence = p_ctrl->max_cadence_lap;
        }
    }
}

/**
 * @brief 取坡度均值算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_grade_avg_in_callback(const void *in, uint32_t len)
{
    const algo_grade_avg_pub_t* p_ctrl = (algo_grade_avg_pub_t *)in;

    if (p_ctrl != NULL)
    {
        g_activity_lap.total_ascent = p_ctrl->total_ascent_pre_lap;
        g_activity_lap.total_descent = p_ctrl->total_descent_pre_lap;
        g_activity_lap.avg_grade = p_ctrl->avg_grade_pre_lap;
        g_activity_lap.avg_pos_grade = p_ctrl->avg_pos_grade_pre_lap;
        g_activity_lap.avg_neg_grade = p_ctrl->avg_neg_grade_pre_lap;
        g_activity_lap.max_pos_grade = p_ctrl->max_pos_grade_pre_lap;
        g_activity_lap.max_neg_grade = p_ctrl->max_neg_grade_pre_lap;
        g_activity_lap.avg_pos_vertical_speed = p_ctrl->avg_pos_vertical_speed_pre_lap;
        g_activity_lap.avg_neg_vertical_speed = p_ctrl->avg_neg_vertical_speed_pre_lap;
        g_activity_lap.max_pos_vertical_speed = p_ctrl->max_pos_vertical_speed_pre_lap;
        g_activity_lap.max_neg_vertical_speed = p_ctrl->max_neg_vertical_speed_pre_lap;
        
        g_activity_fake_lap.total_ascent = p_ctrl->total_ascent_lap;
        g_activity_fake_lap.total_descent = p_ctrl->total_descent_lap;
        g_activity_fake_lap.avg_grade = p_ctrl->avg_grade_lap;
        g_activity_fake_lap.avg_pos_grade = p_ctrl->avg_pos_grade_lap;
        g_activity_fake_lap.avg_neg_grade = p_ctrl->avg_neg_grade_lap;
        g_activity_fake_lap.max_pos_grade = p_ctrl->max_pos_grade_lap;
        g_activity_fake_lap.max_neg_grade = p_ctrl->max_neg_grade_lap;
        g_activity_fake_lap.avg_pos_vertical_speed = p_ctrl->avg_pos_vertical_speed_lap;
        g_activity_fake_lap.avg_neg_vertical_speed = p_ctrl->avg_neg_vertical_speed_lap;
        g_activity_fake_lap.max_pos_vertical_speed = p_ctrl->max_pos_vertical_speed_lap;
        g_activity_fake_lap.max_neg_vertical_speed = p_ctrl->max_neg_vertical_speed_lap;
    }
}

/**
 * @brief 取坐标位置统计算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_position_in_callback(const void *in, uint32_t len)
{
    const algo_position_pub_t* p_ctrl = (algo_position_pub_t *)in;

    if (p_ctrl != NULL)
    {
        util_convert_gps_int_2_semicircles(p_ctrl->start_lat_pre_lap, p_ctrl->start_lon_pre_lap, &g_activity_lap.start_position_lat,
                                           &g_activity_lap.start_position_long);
        util_convert_gps_int_2_semicircles(p_ctrl->end_lat_pre_lap, p_ctrl->end_lon_pre_lap, &g_activity_lap.end_position_lat,
                                           &g_activity_lap.end_position_long);

        util_convert_gps_int_2_semicircles(p_ctrl->start_lat_lap, p_ctrl->start_lon_lap, &g_activity_fake_lap.start_position_lat,
                                           &g_activity_fake_lap.start_position_long);
        util_convert_gps_int_2_semicircles(p_ctrl->end_lat_lap, p_ctrl->end_lon_lap, &g_activity_fake_lap.end_position_lat,
                                           &g_activity_fake_lap.end_position_long);
    }
}

/**
 * @brief 取骑行卡路里算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_sports_calories_in_callback(const void *in, uint32_t len)
{
    const algo_sports_calories_pub_t* p_ctrl = (algo_sports_calories_pub_t *)in;

    if (p_ctrl != NULL)
    {
        g_activity_lap.total_calories = p_ctrl->total_calories_pre_lap;
        
        g_activity_fake_lap.total_calories = p_ctrl->total_calories_lap;
    }
}

/**
 * @brief 取平均跑步动态算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_rd_avg_in_callback(const void *in, uint32_t len)
{
    const algo_rd_avg_pub_t* p_ctrl = (algo_rd_avg_pub_t *)in;

    if (p_ctrl != NULL)
    {
        g_activity_lap.avg_vertical_oscillation = p_ctrl->avg_vertical_oscillation_pre_lap;
        g_activity_lap.avg_stance_time_percent = p_ctrl->avg_stance_time_percent_pre_lap;
        g_activity_lap.avg_stance_time = p_ctrl->avg_ground_contact_time_pre_lap;
        g_activity_lap.avg_vertical_ratio = p_ctrl->avg_vertical_ratio_pre_lap;
        g_activity_lap.avg_stance_time_balance = p_ctrl->avg_ground_contact_balance_pre_lap;
        g_activity_lap.avg_step_length = 0xffff == p_ctrl->avg_step_length_pre_lap ? 0xffff : p_ctrl->avg_step_length_pre_lap * 10;

        if (FIT_SPORTS_CYCLING > get_activity_sport_type()) //跑步
        {
            g_activity_lap.total_cycles = p_ctrl->step_count_pre_lap;
            g_activity_lap.avg_cadence = p_ctrl->avg_cadence_pre_lap / 2; //spm to strides/min
            g_activity_lap.max_cadence = p_ctrl->max_cadence_pre_lap / 2; //spm to strides/min
            g_activity_lap.avg_fractional_cadence = p_ctrl->avg_cadence_pre_lap % 2 * 64;
            g_activity_lap.max_fractional_cadence = p_ctrl->max_cadence_pre_lap % 2 * 64;
        }
        else if (FIT_SPORTS_ELLIPTICAL_MACHINE == get_activity_sport_type()) //椭圆机
        {
            g_activity_lap.total_cycles = p_ctrl->step_count_pre_lap;
            g_activity_lap.avg_cadence = p_ctrl->avg_cadence_pre_lap;
            g_activity_lap.max_cadence = p_ctrl->max_cadence_pre_lap;
        }

        g_activity_fake_lap.avg_vertical_oscillation = p_ctrl->avg_vertical_oscillation_lap;
        g_activity_fake_lap.avg_stance_time_percent = p_ctrl->avg_stance_time_percent_lap;
        g_activity_fake_lap.avg_stance_time = p_ctrl->avg_ground_contact_time_lap;
        g_activity_fake_lap.avg_vertical_ratio = p_ctrl->avg_vertical_ratio_lap;
        g_activity_fake_lap.avg_stance_time_balance = p_ctrl->avg_ground_contact_balance_lap;
        g_activity_fake_lap.avg_step_length = 0xffff == p_ctrl->avg_step_length_lap ? 0xffff : p_ctrl->avg_step_length_lap * 10;

        if (FIT_SPORTS_CYCLING > get_activity_sport_type())   //跑步
        {
            g_activity_fake_lap.total_cycles = p_ctrl->step_count_lap;
            g_activity_fake_lap.avg_cadence = p_ctrl->avg_cadence_lap / 2;   //spm to strides/min
            g_activity_fake_lap.max_cadence = p_ctrl->max_cadence_lap / 2;   //spm to strides/min
            g_activity_fake_lap.avg_fractional_cadence = p_ctrl->avg_cadence_lap % 2 * 64;
            g_activity_fake_lap.max_fractional_cadence = p_ctrl->max_cadence_lap % 2 * 64;
    }
        else if (FIT_SPORTS_ELLIPTICAL_MACHINE == get_activity_sport_type())   //椭圆机
        {
            g_activity_fake_lap.total_cycles = p_ctrl->step_count_lap;
            g_activity_fake_lap.avg_cadence = p_ctrl->avg_cadence_lap;
            g_activity_fake_lap.max_cadence = p_ctrl->max_cadence_lap;
}
    }
}

/**
 * @brief 取跳绳算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_fitness_in_callback(const void *in, uint32_t len)
{
    const algo_count_times_sports_pub_t* p_ctrl = (algo_count_times_sports_pub_t *)in;

    if (p_ctrl != NULL)
    {
        g_activity_lap.jump_count = p_ctrl->count_pre_group;
        g_activity_lap.stand_count = p_ctrl->trip_pre_group;
        
        g_activity_fake_lap.jump_count = p_ctrl->count_group;
        g_activity_fake_lap.stand_count = p_ctrl->trip_group;
    }
}

/**
 * @brief 取泳池游泳算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_pool_swim_in_callback(const void *in, uint32_t len)
{
    const algo_pool_swim_pub_t* p_ctrl = (algo_pool_swim_pub_t *)in;

    if (p_ctrl != NULL)
    {
        if (s_last_num_idle_lengths != p_ctrl->num_idle_lengths) //空闲趟数变化
        {
            memset(&g_idle_length, 0xff, sizeof(g_idle_length));
            memset(&g_idle_lap, 0xff, sizeof(g_idle_length));

            //空闲Length
            g_idle_length.timestamp = p_ctrl->idle_length_timestamp;
            g_idle_length.start_time = p_ctrl->idle_length_start_time;
            g_idle_length.total_elapsed_time = p_ctrl->idle_length_time;
            g_idle_length.total_timer_time = p_ctrl->idle_length_time;
            g_idle_length.message_index = p_ctrl->idle_length_index;
            g_idle_length.length_type = 0;



            //空闲Lap
            g_idle_lap.timestamp = g_idle_length.timestamp;
            g_idle_lap.start_time = g_idle_length.start_time;
            g_idle_lap.total_elapsed_time = g_idle_length.total_elapsed_time;
            g_idle_lap.total_timer_time = g_idle_length.total_timer_time;
            g_idle_lap.message_index = 0 < p_ctrl->num_laps ? p_ctrl->num_laps - 1 : 0;
            g_idle_lap.num_lengths = 1;
            g_idle_lap.first_length_index = g_idle_length.message_index;
            g_idle_lap.total_distance = 0;

            s_last_num_idle_lengths = p_ctrl->num_idle_lengths; //更新空闲趟数
        }

        //Length
        if (g_activity_lap.num_lengths < p_ctrl->num_lengths_lap) //趟数变化时更新
        {
            g_activity_length.timestamp = p_ctrl->timestamp;
            g_activity_length.start_time = p_ctrl->start_time_pre_length;
            g_activity_length.total_elapsed_time = p_ctrl->total_elapsed_time_length;
            g_activity_length.total_timer_time = p_ctrl->total_timer_time_length;
            g_activity_length.message_index = p_ctrl->length_index;
            g_activity_length.total_strokes = p_ctrl->total_strokes_length;
            g_activity_length.avg_speed = p_ctrl->avg_speed_pre_length;
            g_activity_length.event = FIT_EVENT_LENGTH;
            g_activity_length.event_type = FIT_EVENT_TYPE_STOP;
            g_activity_length.swim_stroke = p_ctrl->swim_stroke_length;
            g_activity_length.avg_swimming_cadence = p_ctrl->avg_cadence_length;
            g_activity_length.length_type = p_ctrl->length_type;
            // memset(&g_idle_length, 0xff, sizeof(g_idle_length));
            // memset(&g_idle_lap, 0xff, sizeof(g_idle_length));

         
        }

        //Lap
        // if (g_activity_lap.message_index + 1 == p_ctrl->num_laps) //圈数变化时更新
        {
            g_activity_lap.timestamp = p_ctrl->timestamp_pre_lap;
            g_activity_lap.start_time = p_ctrl->start_time_pre_lap;
            g_activity_lap.total_elapsed_time = p_ctrl->total_elapsed_time_pre_lap;
            g_activity_lap.total_timer_time = p_ctrl->total_timer_time_pre_lap;
            g_activity_lap.total_moving_time = p_ctrl->total_moving_time_pre_lap;
            g_activity_lap.total_distance = p_ctrl->total_distance_pre_lap;
            g_activity_lap.total_cycles = p_ctrl->total_strokes_pre_lap;
            g_activity_lap.enhanced_avg_speed = p_ctrl->avg_speed_pre_lap;
            g_activity_lap.enhanced_max_speed = p_ctrl->max_speed_pre_lap;
            g_activity_lap.avg_speed = p_ctrl->avg_speed_pre_lap;
            g_activity_lap.max_speed = p_ctrl->max_speed_pre_lap;
            g_activity_lap.message_index = 0 < p_ctrl->num_laps ? p_ctrl->num_laps - 1 : 0;
            g_activity_lap.num_lengths = p_ctrl->num_lengths_lap;
            g_activity_lap.first_length_index = p_ctrl->first_length_index;
            g_activity_lap.avg_swolf = p_ctrl->avg_swolf_pre_lap;
            g_activity_lap.avg_strokes_length = p_ctrl->avg_strokes_length_pre_lap;
            g_activity_lap.swim_stroke = p_ctrl->swim_stroke_lap;
            g_activity_lap.avg_cadence = p_ctrl->avg_cadence_pre_lap;
            g_activity_lap.sport = FIT_SPORT_SWIMMING;
            g_activity_lap.sub_sport = FIT_SUB_SPORT_LAP_SWIMMING;

            memcpy(&g_activity_fake_lap, &g_activity_lap, sizeof(FIT_LAP_MESG));
        }
    }
}

///////////////////////////////////////////////////////////////

void activity_lap_begin()
{
    if (s_is_activity_fit_lap_open)
    {
        ACTIVITY_LOG_D("[activity_fit_lap] @%s@ already open", __FUNCTION__);
        return;
    }
    s_is_activity_fit_lap_open = true;

    fit_data_init(FIT_MESG_LAP, &g_activity_lap);
    fit_data_init(FIT_MESG_LAP, &g_activity_fake_lap);

#ifndef SIMULATOR
    optional_config_t config = {
        .sampling_rate = 0,
    };

    if (get_activity_sport_type() == FIT_SPORTS_POOL_SWIMMING) // 泳池游泳
    {
        subscribe_algo(DATA_ID_ALGO_HEART_RATE_AVG, algo_heart_rate_avg_in_callback, &config);   // 平均心率
        subscribe_algo(DATA_ID_ALGO_POOL_SWIM, algo_pool_swim_in_callback, &config);   // 泳池游泳
        return;
    }

    subscribe_algo(DATA_ID_ALGO_TIMER, algo_timer_in_callback, &config);   // 时间
    subscribe_algo(DATA_ID_ALGO_SPEED_AVG, algo_speed_avg_in_callback, &config);   // 平均速度
    subscribe_algo(DATA_ID_ALGO_SPEED_MAX, algo_speed_max_in_callback, &config);   // 最大速度
    subscribe_algo(DATA_ID_ALGO_DISTANCE, algo_distance_in_callback, &config);   // 距离
    subscribe_algo(DATA_ID_ALGO_HEART_RATE_AVG, algo_heart_rate_avg_in_callback, &config);   // 平均心率
    subscribe_algo(DATA_ID_ALGO_ALTITUDE_AVG, algo_altitude_in_callback, &config);   // 高度
    subscribe_algo(DATA_ID_ALGO_POWER_AVG, algo_pwr_avg_in_callback, &config);   // 功率
    subscribe_algo(DATA_ID_ALGO_CADENCE_AVG, algo_cadence_avg_in_callback, &config);   // 踏频
    subscribe_algo(DATA_ID_ALGO_GRADE_AVG, algo_grade_avg_in_callback, &config);   // 平均坡度
    subscribe_algo(DATA_ID_ALGO_SPORTS_CALORIES, algo_sports_calories_in_callback, &config);   // 骑行卡路里

    if (get_sport_type_is_outdoor((SPORTTYPE)get_activity_sport_type()))
    {
        subscribe_algo(DATA_ID_ALGO_POSITION, algo_position_in_callback, &config);   // 坐标位置统计
    }

    if (FIT_SPORTS_CYCLING > get_activity_sport_type() || FIT_SPORTS_ELLIPTICAL_MACHINE == get_activity_sport_type()) //跑步/椭圆机
    {
        subscribe_algo(DATA_ID_ALGO_RD_AVG, algo_rd_avg_in_callback, &config);   // 平均跑步动态
    }
    if (get_activity_sport_type() == FIT_SPORTS_JUMP_ROPE || get_activity_sport_type() == FIT_SPORTS_STRENGTH_TRAINING
        || get_activity_sport_type() == FIT_SPORTS_ROWING_MACHINE)
    {
        subscribe_algo(DATA_ID_ALGO_JUMPROPE, algo_fitness_in_callback, &config);   // 跳绳等训练
    }
#endif
}

void activity_lap_end()
{
    if (!s_is_activity_fit_lap_open)
    {
        ACTIVITY_LOG_E("[activity_fit_lap] @%s@ already close", __FUNCTION__);
        return;
    }
    s_is_activity_fit_lap_open = false;

#ifndef SIMULATOR
    if (get_activity_sport_type() == FIT_SPORTS_POOL_SWIMMING) // 泳池游泳
    {
        unsubscribe_algo(DATA_ID_ALGO_HEART_RATE_AVG, algo_heart_rate_avg_in_callback);   // 平均心率
        unsubscribe_algo(DATA_ID_ALGO_POOL_SWIM, algo_pool_swim_in_callback);   // 泳池游泳
        return;
    }

    unsubscribe_algo(DATA_ID_ALGO_TIMER, algo_timer_in_callback);   // 时间
    unsubscribe_algo(DATA_ID_ALGO_SPEED_AVG, algo_speed_avg_in_callback);   // 平均速度
    unsubscribe_algo(DATA_ID_ALGO_SPEED_MAX, algo_speed_max_in_callback);   // 最大速度
    unsubscribe_algo(DATA_ID_ALGO_DISTANCE, algo_distance_in_callback);   // 距离
    unsubscribe_algo(DATA_ID_ALGO_HEART_RATE_AVG, algo_heart_rate_avg_in_callback);   // 平均心率
    unsubscribe_algo(DATA_ID_ALGO_ALTITUDE_AVG, algo_altitude_in_callback);   // 高度
    unsubscribe_algo(DATA_ID_ALGO_POWER_AVG, algo_pwr_avg_in_callback);   // 功率
    unsubscribe_algo(DATA_ID_ALGO_CADENCE_AVG, algo_cadence_avg_in_callback);   // 踏频
    unsubscribe_algo(DATA_ID_ALGO_GRADE_AVG, algo_grade_avg_in_callback);   // 平均坡度
    unsubscribe_algo(DATA_ID_ALGO_SPORTS_CALORIES, algo_sports_calories_in_callback);   // 骑行卡路里

    if (get_sport_type_is_outdoor((SPORTTYPE)get_activity_sport_type()))
    {
        unsubscribe_algo(DATA_ID_ALGO_POSITION, algo_position_in_callback);   // 坐标位置统计
    }

    if (FIT_SPORTS_CYCLING > get_activity_sport_type() || FIT_SPORTS_ELLIPTICAL_MACHINE == get_activity_sport_type()) //跑步/椭圆机
    {
        unsubscribe_algo(DATA_ID_ALGO_RD_AVG, algo_rd_avg_in_callback);   // 平均跑步动态
    }
    if (get_activity_sport_type() == FIT_SPORTS_JUMP_ROPE || get_activity_sport_type() == FIT_SPORTS_STRENGTH_TRAINING
        || get_activity_sport_type() == FIT_SPORTS_ROWING_MACHINE)
    {
        unsubscribe_algo(DATA_ID_ALGO_JUMPROPE, algo_fitness_in_callback);   // 跳绳等训练
    }
#endif
}

FIT_LAP_MESG* get_lap()
{
    return &g_activity_lap;
}

FIT_LENGTH_MESG* get_length()
{
    return &g_activity_length;
}

FIT_LAP_MESG* get_idle_lap()
{
    return &g_idle_lap;
}

FIT_LENGTH_MESG* get_idle_length()
{
    return &g_idle_length;
}

FIT_LAP_MESG* get_simulator_lap()
{
    g_activity_lap.start_position_lat = 305398540;
    g_activity_lap.start_position_long = 1143647960;
    g_activity_lap.end_position_lat = 305398540;
    g_activity_lap.end_position_long = 1143647960;
    g_activity_lap.total_distance = 40000;
    g_activity_lap.total_cycles = 100;
    g_activity_lap.enhanced_avg_speed = 5000;
    g_activity_lap.enhanced_max_speed = 5000;
    g_activity_lap.total_calories = 100;
    g_activity_lap.avg_speed = 5000;
    g_activity_lap.max_speed = 5000;
    g_activity_lap.avg_power = 250;
    g_activity_lap.max_power = 250;
    g_activity_lap.total_ascent = 10;
    g_activity_lap.total_descent = 10;
    g_activity_lap.normalized_power = 250;
    g_activity_lap.left_right_balance = 50;
    g_activity_lap.avg_altitude = 675;
    g_activity_lap.max_altitude = 675;
    g_activity_lap.avg_grade = 5;
    g_activity_lap.avg_pos_grade = 5;
    g_activity_lap.avg_neg_grade = 5;
    g_activity_lap.max_pos_grade = 5;
    g_activity_lap.max_neg_grade = 5;
    g_activity_lap.avg_pos_vertical_speed = 50;
    g_activity_lap.avg_neg_vertical_speed = 50;
    g_activity_lap.max_pos_vertical_speed = 50;
    g_activity_lap.max_neg_vertical_speed = 50;
    g_activity_lap.min_altitude = 675;
    g_activity_lap.avg_vertical_oscillation = 200;
    g_activity_lap.avg_stance_time_percent = 150;
    g_activity_lap.avg_stance_time = 100;
    g_activity_lap.avg_vertical_ratio = 150;
    g_activity_lap.avg_stance_time_balance = 150;
    g_activity_lap.avg_step_length = 800;
    g_activity_lap.jump_count = 100;
    g_activity_lap.stand_count = 100;
    g_activity_lap.avg_heart_rate = 120;
    g_activity_lap.max_heart_rate = 150;
    g_activity_lap.avg_cadence = 40;
    g_activity_lap.max_cadence = 50;
    g_activity_lap.avg_temperature = 25;
    g_activity_lap.max_temperature = 25;
    g_activity_lap.min_heart_rate = 100;
    g_activity_lap.avg_left_torque_effectiveness = 50;
    g_activity_lap.avg_right_torque_effectiveness = 50;
    g_activity_lap.avg_left_pedal_smoothness = 50;
    g_activity_lap.avg_right_pedal_smoothness = 50;

    return &g_activity_lap;
}

FIT_LAP_MESG *get_fake_lap()
{
    return &g_activity_fake_lap;
}