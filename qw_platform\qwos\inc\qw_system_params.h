﻿
/************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   qw_system_params.h
@Time    :   2025/01/03 16:17:30
*
************************************************************/
#ifndef __INCLUDE_QW_SYSTEM_PARAMS_H__
#define __INCLUDE_QW_SYSTEM_PARAMS_H__
// #include "mem_map.h"
#include <inttypes.h>
#include <rtdevice.h>
#include <rtthread.h>
#ifndef IGS_BOOT
// #include <sensor.h>
#endif // !1
#include <stdbool.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>
#include <sys/types.h>
#ifdef __cplusplus
extern "C" {
#endif


#define ENABLE_AUTO_CHECK_TEST

//自检项目列表
typedef enum {
    ITEM_NULL,
    ITEM_MOTOR,               //FAT_DEV_MOTOR
    ITEM_KEY,                 //XX
    ITEM_TP,                  //FAT_DEV_TP
    ITEM_LCD,                 //FAT_DEV_LCD
    ITEM_ACC,                 //FAT_DEV_SIX_IMU
    ITEM_GPS,                 //FAT_DEV_GPS
    ITEM_GREY,                //XX
    ITEM_BLANK,               //XX
    ITEM_MIC,                 //NN
    ITEM_SPEAK,               //NN
    ITEM_FLASH,               //NN
    ITEM_CHARGING,            //FAT_DEV_CHARGE
    ITEM_STANDBY_CURRENT,     //XX
    ITEM_POWER_OFF_CURRENT,   //XX
    ITEM_GEOMAGNET,           //FAT_DEV_MAG
    ITEM_BAROMETER,           //FAT_DEV_BARO
    ITEM_BEEP,                //FAT_DEV_BEEP
    ITEM_ALIPAY,              //FAT_DEV_ALIPAY
    ITEM_EMMC,                //FAT_DEV_EMMC
    ITEM_BLE,                 //FAT_DEV_BLE
    ITEM_KNOB,                //FAT_DEV_KNOB
    ITEM_LIGHT,               //FAT_DEV_LIGHT
    ITEM_PPG,                 //FAT_DEV_PPG
    ITEM_UPAN,                //XX
    ITEM_BATTERY,             //FAT_DEV_BATTERY
} ENUM_ITEM;

typedef enum {
    RESULT_NG,   /* 未检查 */
    RESULT_OK,   /* 通过 */
    RESULT_FAIL, /* 未通过 */
} ENUM_CHCK_RESULT;

typedef enum {
    // 需要同步修改 g_fat_dev_name
    FAT_DEV_SIX_IMU = 0,   //六轴imu   √
    FAT_DEV_BARO,          //气压计   √
    FAT_DEV_MAG,           //磁力计   √
    FAT_DEV_LIGHT,         //光感   √
    FAT_DEV_KNOB,          //旋钮   √
    FAT_DEV_ALIPAY,        //支付宝
    FAT_DEV_MOTOR,         //马达   √
    FAT_DEV_BATTERY,       //电池   √
    FAT_DEV_BLE,           //蓝牙
    FAT_DEV_BEEP,          //蜂鸣器
    FAT_DEV_LCD,           //屏幕
    FAT_DEV_PPG,           //ppg   √
    FAT_DEV_EMMC,
    FAT_DEV_GPS,
    FAT_DEV_TP,
    FAT_DEV_CHARGE,        //充电   √
    // FAT_DEV_EXRTC,
    FAT_ALG_GM,           //GM算法  √
    FAT_DEV_MAX,
} fat_dev_type;

typedef enum {
    CAPTURE_MASK_NONE = 0,
    CAPTURE_MASK_HR =1,
} capture_mask_type_t;

typedef struct
{
    fat_dev_type dev_type;   //设备类型
    uint8_t dev_id;          //设备id
    uint8_t dev_state;       //设备init状态  1:初始化成功
} fat_dev_info_t;


//boot_params_t 用于在bootloader和应用程序之间传递环境变量的结构体
typedef struct boot_params_S
{
    bool is_factory_mode;   //是否是工厂模式
    int hw_version;   //硬件版本号
} boot_params_t;

//工模烧录数据结构体(32B对齐)
typedef struct factory_data_params_S
{
    uint8_t sn[32];        //SN号
    uint8_t key[32];       //密匙
    uint8_t license[128];  //证书
} factory_data_params_t;


//注意：结构体大小不得超出USER_GLOBAL_SHARE_SIZE定义的大小
//系统全局变量,大小核共同访问固定PSRAM地址
typedef struct
{
    uint8_t charge_state;        //充电状态
    uint8_t battery_level;       //电池电量
    uint16_t battery_vol;        //电池电压
    uint8_t wear_state;          //佩戴状态
    uint8_t live_wear_en;        //活体佩戴检测开关
    uint8_t reserved;            //algo_ppg佩戴状态
    bool is_sleeping;            //是否处于睡眠状态
    bool hcpu_init;
    bool lcpu_init;
    bool lcd_en;                                //屏幕状态，true:亮，false:灭
    bool lcd_auto_light_en;                     //屏幕自动亮度开关，true:开，false:关
    fat_dev_info_t fat_dev_info[FAT_DEV_MAX];   //工模使用，传递设备init状态
    factory_data_params_t factory_data;         //工模烧录数据
    uint32_t check_flag;                        //工模使用,开机通讯自检标志
    uint32_t check_result;                      //工模使用,开机通讯自检结果
    uint32_t capture_mask;                      //开发者选项,数据采集开关
    uint32_t light_lux;                         //光感强度值
    boot_params_t boot_env;                     //boot参数.
    int8_t active_type;                         //活动状态
    int32_t gm_rel_ver[4];                      //GM版本号
    int32_t epo_valid_time;                     //EPO更新时间
} qw_sys_global_variable_t __attribute__((aligned(32)));

int system_params_init(void);
qw_sys_global_variable_t *get_system_params(void);
qw_sys_global_variable_t *set_system_params(void);
char *get_algo_share_mem(void);
char *get_user_env_mem(void);

/**
 * @brief 充电状态获取
 *
 * @return uint8_t 0 ：非充电状态 1 ：充电中
 */
uint8_t get_charge_state(void);

/**
 * @brief algo_ppg佩戴状态获取
 *
 * @return uint8_t 0 ：未佩戴 1 ：佩戴
 */
uint8_t algo_ppg_wear_get(void);

/**
 * @brief 活体佩戴检测开关状态设置
 *
 * @param onoff 开关状态 0 ：关闭活体佩戴检测 1 ：开启活体佩戴检测
 */
void set_live_wear_switch(uint8_t onoff);

/**
 * @brief 活体佩戴检测开关状态获取
 *
 * @return uint8_t 0 ：关闭活体佩戴检测 1 ：开启活体佩戴检测
 */
uint8_t get_live_wear_switch(void);

/**
 * @brief 获取gm算法版本号
 *
 * @param version 输出版本号缓存
 * @param len 版本号缓存长度
 */
void gm_rel_version_get(int32_t *version, uint8_t len);

/**
 * @brief 设置gm算法版本号q
 *
 * @param version 版本号缓存
 * @param len 版本号缓存长度
 */
void gm_rel_version_set(int32_t *version, uint8_t len);

/**
 * @brief 获取epo有效期
 */
int32_t get_epo_valid_time(void);

/**
 * @brief 设置epo有效期
 *
 * @param epo_time 有效期时间戳
 */
void set_epo_valid_time(int32_t epo_time);

/************************************************************************
 *@function:void set_user_sleep_status(bool is_sleep)
 *@brief:用于设置用户的睡眠状态
 *@param:is_sleep 是一个布尔类型，表示用户是否处于睡眠状态
*************************************************************************/
void set_user_sleep_status(bool is_sleep);

/************************************************************************
 *@function:bool get_user_sleep_status(void)
 *@brief:获取用户的睡眠状态
 *@param:返回用户是否处于睡眠的状态,ture表示已入睡
*************************************************************************/
bool get_user_sleep_status(void);

// 产线自检结果初始化
extern int production_check_init(void);

extern ENUM_ITEM production_get_item_index(fat_dev_type index);

// 产线自检结果设置
extern void production_set_check_result(ENUM_ITEM item, ENUM_CHCK_RESULT result);
// 产线自检结果获取
extern ENUM_CHCK_RESULT production_get_check_result_by_item(ENUM_ITEM item);
// 产线自检结果获取，不通过/通过
extern uint32_t production_get_check_result(void);
// 产线自检标志获取，未检测/检测
extern uint32_t production_get_check_flag(void);

/**
 * @brief 工模：设置外设的init信息
 *
 * @param info fat_dev_info_t
 */
void fat_set_dev_init_info(fat_dev_info_t info);

/************************************************************************
 *@function:void update_capture_flag(capture_mask_type_t bit, bool flag);
 *@brief:用于设置算法采集开关,异步数据采集模式
 *@param:bit 对应capture_mask_type_t的枚举值,flag true表示开启,false表示关闭
*************************************************************************/
void update_capture_flag(capture_mask_type_t bit, bool flag);

/************************************************************************
 *@function:void update_capture_flag(capture_mask_type_t bit, bool flag);
 *@brief: 用于获取算法采集开关,异步数据采集模式
 *@param: bit 对应capture_mask_type_t的枚举值
 *@return: true表示开启,false表示关闭
*************************************************************************/
bool get_capture_flag(capture_mask_type_t bit);


//设置电池电量
void set_system_battery(uint8_t battery);

/**
 * @brief 获取电池电量
 *
 * @return uint8_t
 */
uint8_t get_system_battery(void);





/************************************************************************
 *@function:void active_type_set(int8_t state)
 *@brief:设置用户的活动状态
 *@param:用户处于的运动状态,0:seden 1:walking 2:running 3:outdoor cycyling
*************************************************************************/
void active_type_set(int8_t state);

/************************************************************************
 *@function:int8_t active_type_get(void)
 *@brief:获取用户的运动状态
 *@param:返回用户处于的运动状态,0:seden 1:walking 2:running 3:outdoor cycyling
*************************************************************************/
int8_t active_type_get(void);

/************************************************************************
 *@function:void light_lux_set(uint32_t lux)
 *@brief:设置驱动光感强度值
 *@param:lux:light sensor lux raw data
*************************************************************************/
void light_lux_set(uint32_t lux);

/************************************************************************
 *@function:uint32_t light_lux_get(void)
 *@brief:获取驱动光感强度值
 *@param:返回lux:light sensor lux raw data
*************************************************************************/
uint32_t light_lux_get(void);

#ifdef __cplusplus
} /* extern "C" */
#endif
#endif   // __INCLUDE_QW_SYSTEM_PARAMS_H__