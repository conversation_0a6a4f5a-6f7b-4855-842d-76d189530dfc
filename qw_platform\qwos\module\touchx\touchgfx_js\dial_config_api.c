#include "dial_config_api.h"
#include "dial_common_api.h"
#include "qwos.h"
#include "qw_fs.h"


uint32_t get_dial_them_color(uint32_t index)
{
    char colorStr[20] = {0};
    uint32_t themeColor = 0;
    sprintf_array(colorStr, "color%u", index);
    get_dial_value_from_cfg(get_dial_config_path(), colorStr, &themeColor, NULL);
    return themeColor;
}

uint32_t get_using_dial_index(void)
{
	uint32_t usingDialIndex = 0;
	get_dial_value_from_cfg(DIAL_COMMON_CFG_PATH, "usingDialIndex", &usingDialIndex, NULL);

	return usingDialIndex;
}
    
void set_inuse_dial_index(uint32_t usingDialIndexValue)
{
    char goodsIdStr[10] = {0};
    uint32_t dialGoodsId = 0;
    set_dial_value_in_cfg(DIAL_COMMON_CFG_PATH, "usingDialIndex", usingDialIndexValue, OPERATION_MODIFY);

    sprintf_array(goodsIdStr, "dial%u", usingDialIndexValue);
    get_dial_value_from_cfg(DIAL_COMMON_CFG_PATH, goodsIdStr, &dialGoodsId, NULL);
    set_current_dial_goodsid(dialGoodsId);
}

uint32_t get_dial_counts(void)
{
	uint32_t dialCount = 0;
	get_dial_value_from_cfg(DIAL_COMMON_CFG_PATH, "dialCount", &dialCount, NULL);

	return dialCount;
}

uint32_t get_cfg_current_using_dial_goodsid(void)
{
	uint32_t usingDialIndex = 0;
	char goodsIdStr[10] = {0};
	uint32_t dialGoodsId = 0;
	get_dial_value_from_cfg(DIAL_COMMON_CFG_PATH, "usingDialIndex", &usingDialIndex, NULL);
	// 获取表盘索引对应的goodsId
    sprintf_array(goodsIdStr, "dial%u", usingDialIndex);
    get_dial_value_from_cfg(DIAL_COMMON_CFG_PATH, goodsIdStr, &dialGoodsId, NULL);
    set_current_dial_goodsid(dialGoodsId);
	return dialGoodsId;
}

uint32_t get_cfg_dial_goodsid_by_index(int index)
{
	char dialStr[10] = {0};
	uint32_t dialGoodsId = 0;

	// 获取表盘索引对应的goodsId
    sprintf_array(dialStr, "dial%d", index);
    get_dial_value_from_cfg(DIAL_COMMON_CFG_PATH, dialStr, &dialGoodsId, NULL);
	return dialGoodsId;
}

const char* get_cfg_dial_type(int index)
{
    char dialTypeStr[24] = {0};
	const char *dialType = NULL;
    uint32_t dialGoodsId = 0;

	// 获取表盘索引对应的goodsType
    snprintf(dialTypeStr, 24, "dial%dType", index);
    get_dial_value_from_cfg(DIAL_COMMON_CFG_PATH, dialTypeStr, &dialGoodsId, &dialType);
    rt_kprintf("\n@@@dialtype:%s\n", dialType);
    if(strcmp(dialType, "photo") == 0)
    {
        return dialType;
    }
    else
    {
        return "";
    }
}

uint8_t dial_cfg_is_exist(uint32_t usingDialIndex)
{
	char goodsIdStr[10] = {0};
	uint32_t dialGoodsId = 0;
	// 获取表盘索引对应的goodsId
    sprintf_array(goodsIdStr, "dial%u", usingDialIndex);
    get_dial_value_from_cfg(DIAL_COMMON_CFG_PATH, goodsIdStr, &dialGoodsId, NULL);
	// rt_kprintf("\n@@@@@@@@@@dialCfgPath:%s\n", dialCfgPath);
    QW_FIL *fp = NULL;
    if (QW_OK != qw_f_open(&fp, get_dial_config_path(), QW_FA_READ | QW_FA_OPEN_EXISTING))
    {
        return 0;
    }
    qw_f_close(fp);
    return 1;

}

uint32_t get_dial_edit_type()
{
    uint32_t editType = 0;
	get_dial_value_from_cfg(get_dial_config_path(), "edit_type", &editType, NULL);
	return editType;
}

uint32_t get_dial_edit_type_by_index(uint32_t index)
{
    uint32_t editType = 0;
	char cfgPath[50] = {0};
    uint32_t goodsId = get_cfg_dial_goodsid_by_index(index);
    const char *dialType = get_cfg_dial_type(index);
#ifdef SIMULATOR
	sprintf_array(cfgPath, "./Dial/%u%s/config.cfg", goodsId, dialType);
#else
	sprintf_array(cfgPath, "%sDial/%u%s/config.cfg", BASE_PATH, goodsId, dialType);
#endif
	get_dial_value_from_cfg(cfgPath, "edit_type", &editType, NULL);
	return editType;
}

// 获取表盘的主题颜色数量
uint32_t get_dial_cfg_theme_color_num(uint32_t goodsId)
{
    uint32_t colorNum = 0;
    get_dial_value_from_cfg(get_dial_config_path(), "color_num", &colorNum, NULL);
    return colorNum;
}

void set_dial_cfg_color_inuse_index(uint32_t goodsId, uint32_t index)
{
    set_dial_value_in_cfg(get_dial_config_path(), "color_inuse_index", index, OPERATION_MODIFY);
}

uint32_t get_dial_cfg_color_inuse_index()
{
    uint32_t colorInuseIndex = 0;
    get_dial_value_from_cfg(get_dial_config_path(), "color_inuse_index", &colorInuseIndex, NULL);
    return colorInuseIndex;
}

uint32_t get_dial_edit_type_num()
{
    uint32_t editDataTypeNum = 0;
    get_dial_value_from_cfg(get_dial_config_path(), "edit_data_type_num", &editDataTypeNum, NULL);

    return editDataTypeNum;
}

void get_edit_data_type(uint32_t *dataType)
{
    uint32_t editDataTypeNum = get_dial_edit_type_num();

    for(int i = 0; i < editDataTypeNum; i++)
    {
        char editDataTypeStr[20] = {0};
        sprintf_array(editDataTypeStr, "edit_data_type%d", i);
        get_dial_value_from_cfg(get_dial_config_path(), editDataTypeStr, &dataType[i], NULL);
        // rt_kprintf("get_edit_data_type:%u\n", dataType[i]);
    }
}

uint32_t get_dial_data_num()
{
    uint32_t dataNum = 0;

    get_dial_value_from_cfg(get_dial_config_path(), "edit_data_num", &dataNum, NULL);
    return dataNum;
}

uint32_t get_dial_data_type_by_index(uint32_t index)
{
    char dataStr[20] = {0};
    uint32_t dataType = 0;
    sprintf_array(dataStr, "edit_data%u", index);

    get_dial_value_from_cfg(get_dial_config_path(), dataStr, &dataType, NULL);

    return dataType;
}

void set_dial_data_type_by_index(uint32_t index, uint32_t dataType)
{
    char dataStr[20] = {0};
    sprintf_array(dataStr, "edit_data%u", index);
    rt_kprintf("set_dial_data_typeindex:%u, dataType%u\n",index, dataType);
    set_dial_value_in_cfg(get_dial_config_path(), dataStr, dataType, OPERATION_MODIFY);
}

uint32_t get_js_support_aod_flag(uint32_t goodsId)
{
    uint32_t flag = 0;
    get_dial_value_from_cfg(get_dial_config_path(), "is_support_aod", &flag, NULL);
    return flag;

}
