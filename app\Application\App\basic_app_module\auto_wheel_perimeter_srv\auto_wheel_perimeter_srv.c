/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   activity_fit_simulator.c
@Time    :   2024/12/10 10:25:43
<AUTHOR>   lxin
*
**************************************************************************/
#include "auto_wheel_perimeter_srv.h"
#include "GUIMsg/MsgBoxDataDef.h"
#include "GUIMsg/MsgBoxService.h"
#include "activity_record/activity_fit_app.h"
#include "alg_wheel_size.h"
#include "gui_event_service.h"
#include "qw_fit.h"
#include "qw_log.h"
#include "qw_sensor_common.h"   //TODO: ant输入数据结构放在哪？
#include "subscribe_data.h"
#include "subscribe_data_protocol.h"
#ifndef SIMULATOR
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_task.h"
#include "qw_sensor.h"
#include "subscribe_service.h"
#endif

#define AUTO_WHEEL_LOG_LVL LOG_LVL_ERROR
#define AUTO_WHEEL_LOG_TAG "AUTO_WHEEL"

#if (AUTO_WHEEL_LOG_LVL >= LOG_LVL_DBG)
#define AUTO_WHEEL_LOG_D(...) QW_LOG_D(AUTO_WHEEL_LOG_TAG, __VA_ARGS__)
#else
#define AUTO_WHEEL_LOG_D(...)
#endif

#if (AUTO_WHEEL_LOG_LVL >= LOG_LVL_INFO)
#define AUTO_WHEEL_LOG_I(...) QW_LOG_I(AUTO_WHEEL_LOG_TAG, __VA_ARGS__)
#else
#define AUTO_WHEEL_LOG_I(...)
#endif

#if (AUTO_WHEEL_LOG_LVL >= LOG_LVL_WARNING)
#define AUTO_WHEEL_LOG_W(...) QW_LOG_W(AUTO_WHEEL_LOG_TAG, __VA_ARGS__)
#else
#define AUTO_WHEEL_LOG_W(...)
#endif

#if (AUTO_WHEEL_LOG_LVL >= LOG_LVL_ERROR)
#define AUTO_WHEEL_LOG_E(...) QW_LOG_E(AUTO_WHEEL_LOG_TAG, __VA_ARGS__)
#else
#define AUTO_WHEEL_LOG_E(...)
#endif

#define AUTO_WHEEL_SIZE_TIP_INTERVAL_TICK 300       //如果提示时超时,等待下一次提示的时间间隔,单位为 auto_wheel_size_input_update 调用频率
#define AUTO_WHEEL_SIZE_TIP_STOP          (-1)      //停止发送提示的状态值,赋值给 s_algo_in.post_tip_tick_cd
#if WHEEL_SIZE_SIM_TEST
#define AUTO_WHEEL_SIZE_UPDATE_RANGE_PCT 1          //提示误差百分比(+-%)
#else
#define AUTO_WHEEL_SIZE_UPDATE_RANGE_PCT 8          //提示误差百分比(+-%)
#endif                                              // WHEEL_SIZE_SIM_TEST
#define AUTO_WHEEL_SIZE_REQUIRED_DISTANCE 50000     //测算结果必需达到的距离 100*m
#define AUTO_WHEEL_SIZE_REQUIRED_TIME     0         //测算结果必需达到的时间 1000*s
#define AUTO_WHEEL_SIZE_MAX_TIME          1200000   //报警持续时间 1000*s

// 输入数据
typedef struct
{
    uint32_t total_moving_time;
    uint32_t total_distance;
    uint32_t gps_speed;     //gps速度,  1000 * m/s,
    uint16_t wheel_speed;   //圈数,
    int16_t grade;          //坡度,  100 * %

    int16_t post_tip_tick_cd;
    uint16_t wheel_perimeter;        //当前轮径,  100 * mm
    uint16_t cal_wheel_perimeter;    //计算轮径,  100 * mm
    saving_status_e saving_status;   //数据记录的状态
} algo_auto_wheel_sub_t;

static bool s_is_auto_wheel_cal_open = false;

static algo_auto_wheel_sub_t s_algo_in = {0};
static msg_tip_select_t s_tip_select = {0};

static void auto_wheel_deal()
{
    if (s_algo_in.saving_status < enum_status_saving)
    {
        if (0xFFFF != s_algo_in.cal_wheel_perimeter || 0 != s_algo_in.post_tip_tick_cd)
        {
            wheel_size_auto_calc_reset();
            s_algo_in.cal_wheel_perimeter = 0xFFFF;
            s_algo_in.post_tip_tick_cd = 0;
        }
        return;
    }

    uint32_t deviation = 0;

    if (s_algo_in.gps_speed >= 500 && s_algo_in.gps_speed <= 277500 && s_algo_in.wheel_speed != UINT16_MAX && s_algo_in.grade != INT16_MAX)
    {
        s_algo_in.cal_wheel_perimeter = wheel_size_auto_calc(s_algo_in.gps_speed, s_algo_in.wheel_speed, s_algo_in.grade);
    }

    if (s_algo_in.cal_wheel_perimeter != UINT16_MAX && s_algo_in.wheel_perimeter != UINT16_MAX)
    {
        if (0 < s_algo_in.post_tip_tick_cd)
        {
            s_algo_in.post_tip_tick_cd--;
        }
        else if (AUTO_WHEEL_SIZE_TIP_STOP != s_algo_in.post_tip_tick_cd)
        {
            //保证必要的骑行过程
            if (AUTO_WHEEL_SIZE_REQUIRED_DISTANCE <= s_algo_in.total_distance && AUTO_WHEEL_SIZE_REQUIRED_TIME <= s_algo_in.total_moving_time
                && AUTO_WHEEL_SIZE_MAX_TIME > s_algo_in.total_moving_time)
            {
                deviation = (s_algo_in.wheel_perimeter > s_algo_in.cal_wheel_perimeter) ? (s_algo_in.wheel_perimeter - s_algo_in.cal_wheel_perimeter)
                                                                                        : (s_algo_in.cal_wheel_perimeter - s_algo_in.wheel_perimeter);

                //误差值大于设定才更新, 否侧继续收集数据
                if (deviation * 100 > (uint32_t) s_algo_in.wheel_perimeter * AUTO_WHEEL_SIZE_UPDATE_RANGE_PCT)
                {
                    s_algo_in.post_tip_tick_cd = AUTO_WHEEL_SIZE_TIP_STOP;

                    s_tip_select.wheel_perimeter = s_algo_in.cal_wheel_perimeter;
                    s_tip_select.type = MSG_TIP_SELECT_WHEEL_PERIMETER;

                    submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_MODIFY_WHEEL_PERIMETER, (void *) &s_tip_select);
                }
            }
        }
    }
}

/**
 * @brief 算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_grade_in_callback_grade(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    const algo_grade_pub_t *p_ctrl = (algo_grade_pub_t *) in;
    if (p_ctrl != NULL)
    {
        s_algo_in.grade = p_ctrl->grade;
    }
}

/**
 * @brief GPS速度数据算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_speed_in_callback_gps(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
#ifndef SIMULATOR
    const gps_pub_t *p_ctrl = (gps_pub_t *) in;
    if (p_ctrl != NULL)
    {
        s_algo_in.gps_speed = p_ctrl->speed;
    }
#endif
}

/**
 * @brief pwr轮径数据算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_speed_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    const algo_speed_pub_t *p_ctrl = (algo_speed_pub_t *) in;
    if (p_ctrl != NULL)
    {
        if (p_ctrl->spd_source == enum_spd_source_lev || p_ctrl->spd_source == enum_spd_source_spd || p_ctrl->spd_source == enum_spd_source_cbsc)
        {
            s_algo_in.wheel_speed = p_ctrl->wheel_speed;
            s_algo_in.wheel_perimeter = p_ctrl->wheel_size;
        }
        else
        {
            s_algo_in.wheel_speed = UINT16_MAX;
            s_algo_in.wheel_perimeter = UINT16_MAX;
        }
    }
}

/**
 * @brief 取时间算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_timer_in_callback(const void *in, uint32_t len)
{
    const algo_timer_pub_t *p_ctrl = (algo_timer_pub_t *) in;
    if (p_ctrl != NULL)
    {
        s_algo_in.total_moving_time = p_ctrl->timer_total.moving_time;
    }
}

/**
 * @brief 取距离算法结果
 * @param[const void *] in 算法输入
 * @param[uint32_t] len 结构长度
 */
static void algo_distance_in_callback(const void *in, uint32_t len)
{
    const algo_distance_pub_t *p_ctrl = (algo_distance_pub_t *) in;
    if (p_ctrl != NULL)
    {
        s_algo_in.total_distance = p_ctrl->distance;
    }
}

/**
 * @brief 运动状态算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_sports_ctrl_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    const algo_sports_ctrl_t *p_ctrl = (algo_sports_ctrl_t *) in;
    if (p_ctrl != NULL)
    {
        s_algo_in.saving_status = p_ctrl->saving_status;

        if (p_ctrl->ctrl_type == enum_ctrl_null)
        {
            auto_wheel_deal();   // 轮径计算
        }
    }
}

/**
 * @brief 订阅算法定义记组运动
 */
#ifndef SIMULATOR
static algo_topic_node_t s_algo_topic_node_group[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_grade",
        .topic_id = DATA_ID_ALGO_GRADE,
        .callback = algo_grade_in_callback_grade,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_gps_data",
        .topic_id = DATA_ID_ALGO_GPS_DATA,
        .callback = algo_speed_in_callback_gps,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_speed",
        .topic_id = DATA_ID_ALGO_SPEED,
        .callback = algo_speed_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_timer",
        .topic_id = DATA_ID_ALGO_TIMER,
        .callback = algo_timer_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_distance",
        .topic_id = DATA_ID_ALGO_DISTANCE,
        .callback = algo_distance_in_callback,
    },
};
#endif

/**
 * @brief 开启摘要任务, 在活动准备阶段开始时调用(活动状态改变之前)
 */
void auto_wheel_perimeter_detect_begin()
{
    int32_t ret = -1;
    if (s_is_auto_wheel_cal_open)
    {
        AUTO_WHEEL_LOG_D("[auto_wheel_perimeter_srv] @%s@ already open", __FUNCTION__);
        return;
    }
    s_is_auto_wheel_cal_open = true;

    wheel_size_auto_calc_reset();
    s_algo_in.gps_speed = UINT32_MAX;
    s_algo_in.wheel_speed = UINT16_MAX;
    s_algo_in.grade = INT16_MAX;
    s_algo_in.post_tip_tick_cd = 0;
    s_algo_in.wheel_perimeter = UINT16_MAX;
    s_algo_in.cal_wheel_perimeter = UINT16_MAX;
    s_algo_in.saving_status = enum_status_free;

#ifndef SIMULATOR
    optional_config_t config = {
        .sampling_rate = 0,
    };

    if (get_activity_sport_type() >= FIT_SPORTS_CYCLING && get_activity_sport_type() <= FIT_SPORTS_TRIP_CYCLING
        && get_activity_sport_type() != FIT_SPORTS_INDOOR_CYCLING)   //全部室外骑行的类型
    {
        ret = algo_topic_list_subscribe(s_algo_topic_node_group, sizeof(s_algo_topic_node_group) / sizeof(s_algo_topic_node_group[0]));
    }
#endif
}

/**
 * @brief 关闭摘要任务, 在结束阶段开始时调用(关闭文件句柄之前)
 */
void auto_wheel_perimeter_detect_end()
{
    int ret = -1;

    if (!s_is_auto_wheel_cal_open)
    {
        AUTO_WHEEL_LOG_D("%s already close", __FUNCTION__);
        return;
    }

    s_is_auto_wheel_cal_open = false;
    wheel_size_auto_calc_reset();

#ifndef SIMULATOR
    if (get_activity_sport_type() >= FIT_SPORTS_CYCLING && get_activity_sport_type() <= FIT_SPORTS_TRIP_CYCLING
        && get_activity_sport_type() != FIT_SPORTS_INDOOR_CYCLING)   //全部室外骑行的类型
    {
        algo_topic_list_unsubscribe(s_algo_topic_node_group, sizeof(s_algo_topic_node_group) / sizeof(s_algo_topic_node_group[0]));
    }
#endif

    AUTO_WHEEL_LOG_D("%s ok", __FUNCTION__);
}

/**
 * @brief 自动轮径提示暂存 过一段时间再提示
 */
void auto_wheel_perimeter_recall_tip()
{
    if (AUTO_WHEEL_SIZE_TIP_STOP == s_algo_in.post_tip_tick_cd && 0xFFFF != s_algo_in.cal_wheel_perimeter)
    {
        s_algo_in.post_tip_tick_cd = AUTO_WHEEL_SIZE_TIP_INTERVAL_TICK;
    }
}
