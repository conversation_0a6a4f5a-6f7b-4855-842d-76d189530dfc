/** 
 * @*************************************** Copyright (c) ***************************************
 * @                              <PERSON>han <PERSON>wu Technology Co., Ltd
 * @*********************************************************************************************
 * @Author: Sun
 * @Date: 2024-07-09 10:01:30
 * @LastEditTime: 2024-07-09 17:02:03
 * @LastEditors: Sun
 * @FilePath: \iGS630_App\Application\App\radio\sensor\sensor_ble\bsc\ble_bsc_cfg.c
 * @Description: 
 * @*********************************************************************************************
 */
#include "ble_cscs_c.h"
#include "ble_bas_cfg.h"
#include "ble_cad_cfg.h"
#include "ble_spd_cfg.h"
#include "qw_sensor_scan.h"
#include "cm_backtrace_log.h"
#include "ble_central.h"
#include "cfg_header_def.h"

BLE_CSCS_C_ARRAY_DEF(m_cscs_c, 3);
static uint8_t cbsc_low_power_indicate_flag = false;
static uint16_t m_conn_handle_cscs_c = BLE_CONN_HANDLE_INVALID;  /**< Connection handle for the CSCS central application */

uint16_t ble_cscs_conn_handle_get()
{
    return m_conn_handle_cscs_c;
}
void ble_cscs_conn_handle_set(uint16_t handle)
{
    m_conn_handle_cscs_c = handle;
}

/**@brief Handles events coming from the Cycling Speed and Cadence central module.
 */
static void cscs_c_evt_handler(ble_cscs_c_t * p_cscs_c, ble_cscs_c_evt_t * p_cscs_c_evt)
{
    sensor_type_t               sensor_type             = SENSOR_TYPE_INVALID;;
    sensor_original_data_t      *p_sensor_original_data = sensor_original_data_get();
    sensor_module_evt_handler   evt_handler             = sensor_module_evt_handler_get();
    sensor_saved_work_t         *p_sensor_saved         = NULL;
    bool found = false;
    sensor_module_param_input_t *p_param                = sensor_module_param_input_get();
    sensor_work_state_t         sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    int8_t                      index                   = -1;
    sensor_connect_infor_t sensor_connect;
    sensor_search_infor_t  sensor_search_infor;

    uint16_t cadence_event_time;            ///< Speed or cadence event time.
    uint16_t cadence_rev_count;             ///< Speed or cadence revolution count.
    uint16_t speed_event_time;              ///< Speed event time.
    uint16_t speed_rev_count;               ///< Speed revolution count.		

    uint8_t  cadData      = 0Xff;
    uint16_t wheel_speed  = 0xffff;
    uint16_t wheel_delta  = 0xffff;
		
    switch (p_cscs_c_evt->evt_type)
    {
        case BLE_CSCS_C_EVT_DISCOVERY_COMPLETE:
        {
            ret_code_t err_code;
            err_code = ble_cscs_c_handles_assign(p_cscs_c,
                                                p_cscs_c_evt->conn_handle,
                                                &p_cscs_c_evt->params.cscs_db);
            APP_ERROR_CHECK(err_code);
            // Cycling Speed and Cadence service discovered. Enable notification of Heart Rate Measurement.
            err_code = ble_cscs_c_csc_notif_enable(p_cscs_c);
            APP_ERROR_CHECK(err_code);
            rt_kprintf("BLE_CSCS_C_EVT_DISCOVERY_COMPLETE\n");
            ble_sensor_conn_handle_set(SENSOR_TYPE_CBSC, p_cscs_c_evt->conn_handle);
            ble_gap_connect_release();
        } break; // BLE_CSCS_C_EVT_DISCOVERY_COMPLETE

        case BLE_CSCS_C_EVT_CSC_NOTIFICATION:
        {
            cadence_event_time = p_cscs_c_evt->params.csc.last_crank_event_time;
            cadence_rev_count  = p_cscs_c_evt->params.csc.cumulative_crank_revs;
            speed_event_time   = p_cscs_c_evt->params.csc.last_wheel_event_time;
            speed_rev_count    = p_cscs_c_evt->params.csc.cumulative_wheel_revs;
            uint8_t sensor_connect_change = false;

            if (p_cscs_c_evt->params.csc.is_wheel_rev_data_present && p_cscs_c_evt->params.csc.is_crank_rev_data_present)
            {
                sensor_type = SENSOR_TYPE_CBSC;
            }
            else
            {
                if (p_cscs_c_evt->params.csc.is_wheel_rev_data_present)
                {
                    sensor_type = SENSOR_TYPE_SPD;
                }
                else
                {
                    sensor_type = SENSOR_TYPE_CAD;
                }
            }

            sensor_type_t connect_type = sensor_connecting_dev_type_get();
            // rt_kprintf("sensor_type = %d, connect_type = %d, m_conn_handle_cscs_c = %d, conn_handle = %d\n", 
            //     sensor_type, connect_type, m_conn_handle_cscs_c, p_cscs_c_evt->conn_handle);
            if (SENSOR_TYPE_CBSC != sensor_type 
                && SENSOR_TYPE_CBSC == connect_type
                && m_conn_handle_cscs_c == p_cscs_c_evt->conn_handle)
            {
                if(sensor_connect_infor_get(sensor_type, &sensor_connect))
                {
                    if (sensor_connect.state != SENSOR_CONNECT_STATE_DISCOVERY)
                    {
                        found = sensor_connect_infor_get(SENSOR_TYPE_CBSC, &sensor_connect);
                        if (found)
                        {
                            sensor_connect_infor_clear(SENSOR_TYPE_CBSC);
                        }

                        sensor_connect.radio_type = SENSOR_RADIO_TYPE_BLE;
                        sensor_connect.state = SENSOR_CONNECT_STATE_DISCOVERY;
                        sensor_connect.disconnect_by_forbidden = 0;
                        sensor_connect.state_timeout = 0;
                        if (SENSOR_TYPE_CAD == sensor_type)
                        {
                            sensor_connect_infor_copy(SENSOR_TYPE_CAD, &sensor_connect);
                            ble_sensor_conn_handle_set(SENSOR_TYPE_CAD, m_conn_handle_cscs_c);
                        }
                        else
                        {
                            sensor_connect_infor_copy(SENSOR_TYPE_SPD, &sensor_connect);
                            ble_sensor_conn_handle_set(SENSOR_TYPE_SPD, m_conn_handle_cscs_c);
                        }
                        ble_sensor_conn_handle_set(SENSOR_TYPE_CBSC, BLE_CONN_HANDLE_INVALID);
                        sensor_ant_scan_start();
                        sensor_connect_change = true;
                    }
                }
            }
            else
            {
                sensor_connect_infor_get(sensor_type, &sensor_connect);
                if (SENSOR_CONNECT_STATE_CONNECTED == sensor_connect.state)
                {
                    // m_conn_handle_cscs_c = p_cscs_c_evt->conn_handle;
                    sensor_connect.radio_type = SENSOR_RADIO_TYPE_BLE;
                    sensor_connect.state = SENSOR_CONNECT_STATE_DISCOVERY;
                    sensor_connect_infor_set(sensor_type, &sensor_connect);
                    if (SENSOR_TYPE_CAD == sensor_type)
                    {
                        ble_cad_conn_handle_set(p_cscs_c_evt->conn_handle);
                    }
                    else if(SENSOR_TYPE_SPD == sensor_type)
                    {
                        ble_spd_conn_handle_set(p_cscs_c_evt->conn_handle);
                    }
                    m_conn_handle_cscs_c = BLE_CONN_HANDLE_INVALID;
                    sensor_ant_scan_start();
                    sensor_connect_change = true;
                }                
            }

            memset(&sensor_search_infor, 0x00, sizeof(sensor_search_infor_t));
            memcpy((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));

            bool ble_sensor_found = sensor_ble_search_infor_get(sensor_connect.sensor_id.ble_mac_addr, &sensor_search_infor);
            sensor_search_infor.radio_type = SENSOR_RADIO_TYPE_BLE;
            sensor_search_infor.sensor_type = sensor_type;

            if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
            {
                if (SENSOR_WORK_STATE_FORBIDDEN == sensor_work_state)
                {
                    sensor_infor_t sensor_infor = {0};
                    sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                    sensor_disconnect(&sensor_infor);
                }
                else
                {
                    p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                    p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
                }
                sensor_saved_work_infor_release_write_lock(index);
            }
            else
            {
                sensor_search_infor.sensor_type = SENSOR_TYPE_CBSC;
                sensor_search_infor_del(&sensor_search_infor);
                sensor_search_infor.sensor_type = sensor_type;
                if(!sensor_disconnect_item_check(&sensor_search_infor))
                {
                    if(ble_sensor_found)
                    {
                        sensor_saved_work_infor_add(&sensor_search_infor);
                    }
                }
                else
                {
                    rt_kprintf("sensor at disconnect info list\n");
                }
            }
            if (true == sensor_connect_change && NULL != evt_handler)
            {
                evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_BLE, TRUE);
                evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, sensor_search_infor.sensor_type, sensor_search_infor.radio_type, 0);
                evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_connect.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_BLE, TRUE);
            }
            // if (SENSOR_CONNECT_STATE_DISCOVERY == sensor_connect.state)
            {
                if (NULL != evt_handler)
                {
                    evt_handler(EVENT_SENSOR_DATA_UPDATE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_BLE, 0);
                }
            }
            if (SENSOR_TYPE_CBSC == sensor_type)
            {
                p_sensor_original_data->wheel_size = sensor_saved_size_get(&sensor_search_infor);
                DecodeSpd(speed_event_time, speed_rev_count, &wheel_speed, &wheel_delta);
                DecodeCad(cadence_event_time, cadence_rev_count, &cadData);

                if (0xff != cadData)
                {
                    p_sensor_original_data->cbscData.cadence = cadData;
                }
                if (0xffff != wheel_speed)
                {
                    p_sensor_original_data->cbscData.wheel_speed = wheel_speed;
                    if (0xffff != wheel_delta)
                    {
                        p_sensor_original_data->cbscData.wheel_delta += wheel_delta;
                    }
                } 
            }
            else if(SENSOR_TYPE_SPD == sensor_type)
            { 
                p_sensor_original_data->wheel_size = sensor_saved_size_get(&sensor_search_infor);	

                DecodeSpd(speed_event_time, speed_rev_count, &wheel_speed, &wheel_delta);
                if (0xffff != wheel_speed)
                {
                    p_sensor_original_data->spdData.wheel_speed = wheel_speed;
                    if (0xffff != wheel_delta)
                    {
                        p_sensor_original_data->spdData.wheel_delta += wheel_delta;
                    }
                }
            }
            else
            {
                DecodeCad(cadence_event_time, cadence_rev_count, &cadData);
                if (0xff != cadData)
                {
                    p_sensor_original_data->cadData.cadence = cadData;
                }
            }
        } break; // BLE_CSCS_C_EVT_HRM_NOTIFICATION

        default:
            // No implementation needed.
            break;
    }
}

/** 
 * @*********************************************************************************************
 * @description: 根据保存传感器信息，判断是速度，还是踏频类传感器，未保存则默认为速度踏频二合一
 * @param {uint8_t} *mac_addr 
 * @return {*}
 * @*********************************************************************************************
 */
sensor_type_t ble_cscs_dev_type_get(uint8_t const *mac_addr)
{
    return sensor_saved_ble_cscs_dev_type_get(mac_addr);
}

//速度踏频
void ble_central_cscs_disc_handler(ble_db_discovery_evt_t * p_evt)
{
    for (uint8_t i = 0; i < 3; i ++)
    {
        if (m_cscs_c[i].conn_handle == BLE_CONN_HANDLE_INVALID) 
        {
            ble_cscs_on_db_disc_evt(&m_cscs_c[i], p_evt);
            break;
        }
    }
}

/**@brief Cycling Speed and Cadence Collector initialization.
 */
void cscs_c_init(void)
{
    ret_code_t       err_code;
    ble_cscs_c_init_t cscs_c_init_obj;

    cscs_c_init_obj.evt_handler = cscs_c_evt_handler;
    
    for (uint8_t i = 0; i < 3; i++)
    {
        err_code = ble_cscs_c_init(&m_cscs_c[i], &cscs_c_init_obj);
        APP_ERROR_CHECK(err_code);
    }
}
