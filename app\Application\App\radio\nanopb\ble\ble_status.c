/***************************************Copyright (c)****************************************/
//                              <PERSON>han <PERSON> Technology Co., Ltd
//
//---------------------------------------File Info--------------------------------------------
// File name         : ble_pb.c
// Created by        : jiangzhen
// Descriptions      : BLE pb通信.c文件
//--------------------------------------------------------------------------------------------
// History           :
// 2019-06-08        :原始版本
/*********************************************************************************************/
#include "ble_nus_srv.h"
#include "ble_cmd_common.h"
#include "ble_cmd_response.h"

#include "pb.h"
#include "pb_encode.h"
#include "pb_decode.h"
#include "pb_decode_common.h"
#include "pb_encode_common.h"

#include "ble.pb.h"
#include "ble_status.h"

#include "igs_global.h"
#include "crc8.h"
#include "ble_status_data_inf.h"
#include "ble_lb55x.h"
#include "ble_data_inf.h"
#include "gui_event_service.h"
#include "activity_fit_app.h"
#include "ble_peripheral.h"
#include "cfg_header_def.h"

static bool app_connect_status = false;         //APP连接状态标志
//static uint8_t bond_req = false;
static ble_unbond_report_callback_t g_ble_unbond_report_callback = NULL;
static uint8_t local_user_id[__MEMBER_ID_MAX_LENGTH] = {0};
//-------------------------------------------------------------------------------------------
// Function Name : is_app_connect
// Purpose       : 获取与手机APP连接状态，IOS无法确定是连接的手机还是APP
// Param[in]     : void  
// Param[out]    : None
// Return type   : 
// Comment       : 2019-06-12
//-------------------------------------------------------------------------------------------
bool is_app_connect(void)
{
    return app_connect_status;
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_bond_repeated_submsg_encode
// Purpose       : 蓝牙绑定PB子消息编码接口函数
// Param[in]     : pb_ostream_t *stream     
//                 const pb_field_t *field  
//                 void * const *arg        
// Param[out]    : None
// Return type   : static
// Comment       : 2019-06-10
//-------------------------------------------------------------------------------------------
static bool ble_bond_repeated_submsg_encode(pb_ostream_t *stream, const pb_field_t *field, void * const *arg)
{
    uint8_t status = false;
    
    ble_data_message ** expected = (ble_data_message **)arg;
    
    status = pb_encode_tag_for_field(stream, field);
    status &= pb_encode_submessage(stream, ble_data_message_fields, *expected); 

    return status;
}


static bool ble_bond_repeated_submsg_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    ble_data_message** expected = (ble_data_message**)arg;
    
    if (!pb_decode(stream, ble_data_message_fields, *expected))
        return false;

    return true;
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_bond_info_send
// Purpose       : 向APP发送绑定信息
// Param[in]     : None
// Param[out]    : None
// Return type   : static
// Comment       : 2019-06-10
//-------------------------------------------------------------------------------------------
static void ble_bond_info_send()
{
    uint8_t pb_crc = 0;
    ble_msg ble_message;
    ble_data_message ble_data_msg;
    uint8_t *data = NULL;
    uint16_t *length = NULL;
     uint8_t bond_status = 0;

    memset(&ble_message, 0, sizeof(ble_msg));
    memset(&ble_data_msg, 0, sizeof(ble_data_message));
    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

    bond_status = ble_bond_status(); 

    //参数赋值
    ble_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_BLE;
    ble_message.ble_operate_type = BLE_OPERATE_TYPE_enum_BLE_OPERATE_TYPE_BOND_INFO;

    ble_data_msg.has_status = true;
    ble_data_msg.status = bond_status;
    if (bond_status && g_device_get_userconfog_member_id() != NULL)     		//有绑定，且member_id有效
    {
        if ((g_device_get_userconfog_member_id())[0] != 0xff)
        {
            ble_data_msg.member_id.arg = g_device_get_userconfog_member_id();
            ble_data_msg.member_id.funcs.encode = &encode_string;
        }
    }
    
    ble_message.ble_data_msg.arg = &ble_data_msg;
    ble_message.ble_data_msg.funcs.encode = &ble_bond_repeated_submsg_encode;
    
    //编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    //编码
    pb_encode(&encode_stream, ble_msg_fields, &ble_message);

	*length = encode_stream.bytes_written;
    ble_nus_data_tx_ch0( );
    
    //对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);
    
    //命令协议 发送通道2
    ble_cmd_end_tx(ble_message.service_type, 0, ble_message.ble_operate_type, 0, encode_stream.bytes_written, pb_crc);
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_status_status_handle
// Purpose       : BLE通信状态处理函数
// Param[in]     : uint8_t *buf  
// Param[out]    : None
// Return type   : 
// Comment       : 2019-06-10
//-------------------------------------------------------------------------------------------
void ble_status_status_handle(uint8_t *buf)
{
    ble_status_cmd_st *ble_status_cmd_s = (ble_status_cmd_st *)buf;
    uint8_t status = ble_status_cmd_s->status;

    if (enmuDATA_ERR_STATUS == status)
    {
        switch (ble_status_cmd_s->op_type)
        {
            case BLE_OPERATE_TYPE_enum_BLE_OPERATE_TYPE_BOND_INFO:
                ble_bond_info_send();
                break;
            case BLE_OPERATE_TYPE_enum_BLE_OPERATE_TYPE_CONNECT_STATUS:
            default:
                break;
        }
    }
}        

// 上报是否允许配对，以及当前存入的user id
static void ble_watch_bond_user_get(void)
{
    uint8_t pb_crc = 0;
    ble_msg ble_message;
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    uint8_t user_id[__MEMBER_ID_MAX_LENGTH] = {0};
    uint8_t user_id_len = 0;
    g_get_app_user_id(user_id, &user_id_len);

    memset(&ble_message, 0, sizeof(ble_msg));

    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

    //参数赋值
    ble_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_BLE;
    ble_message.ble_operate_type = BLE_OPERATE_TYPE_enum_BLE_OPERATE_TYPE_WATCH_BOND_USER_GET;

    ble_message.has_ble_bond_user_msg = true;
    ble_message.ble_bond_user_msg.has_bond_allow = true;
    ble_message.ble_bond_user_msg.bond_allow = ble_app_bond_allow_get();
    ble_message.ble_bond_user_msg.user_id.arg = user_id;
    ble_message.ble_bond_user_msg.user_id.funcs.encode = &encode_string;

    //编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    //编码
    pb_encode(&encode_stream, ble_msg_fields, &ble_message);

    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch0( );

    //对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);

    ble_cmd_end_tx(ble_message.service_type, 0, ble_message.ble_operate_type, 0, encode_stream.bytes_written, pb_crc);
}

// 接收配对请求，并回复是否允许配对，以及当前存入的user id
static void ble_watch_bond_user_set(ble_msg *ble_message)
{
    uint8_t pb_crc = 0;
    ble_msg ble_message_reply;
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    uint8_t last_user_id[__MEMBER_ID_MAX_LENGTH] = {0};
    uint8_t local_user_id_len = 0;

    memset(local_user_id, 0, __MEMBER_ID_MAX_LENGTH);
    g_get_app_user_id(last_user_id, &local_user_id_len);
    memcpy(local_user_id, last_user_id, local_user_id_len);

    memset(&ble_message_reply, 0, sizeof(ble_msg));

    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

    if (ble_message->has_ble_bond_user_msg)
    {
        
        //参数赋值
        ble_message_reply.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_BLE;
        ble_message_reply.ble_operate_type = BLE_OPERATE_TYPE_enum_BLE_OPERATE_TYPE_WATCH_BOND_USER_SET;

        ble_message_reply.has_ble_bond_user_msg = true;
        ble_message_reply.ble_bond_user_msg.has_state = true;
        static char *p_push_to_page = "Launcher";
        if ((0 == local_user_id_len) || (0 == strcmp((const char*)ble_message->ble_bond_user_msg.user_id.arg, (const char*)local_user_id)))
        {
            
            BLE_PB_LOG_I("%s %d: user_id not change: %s bind type: %d", __func__, __LINE__, ble_message->ble_bond_user_msg.user_id.arg, g_device_get_app_request_bind_type());
            ble_message_reply.ble_bond_user_msg.state = 0;   //0表示未绑定或user id相同
            if (g_device_get_app_request_bind_type() != BLE_APP_REQUEST_BIND_TYPE_NONE)//绑定类型不为空
            {
                if (g_device_get_app_request_bind_type() == BLE_APP_REQUEST_BIND_QR_CODE)
                {
                    //二维码配对绑定，才会去保存user id
                    g_set_app_user_id(ble_message->ble_bond_user_msg.user_id.arg, strlen(ble_message->ble_bond_user_msg.user_id.arg));
                    cfg_save(enum_cfg_ble);
                    ble_app_bond_save();// 保存此次的绑定信息
                    ble_app_adv_bond_state_change(false, false);
                }
                else if ((g_device_get_app_request_bind_type() == BLE_APP_REQUEST_BIND_OTHER) &&
                        (0 == local_user_id_len))
                {
                    ble_app_bond_resume();// 非二维码页面的绑定行为，不允许在无user id（该条件几乎不可能出现，但需要考虑）的情况下绑定，清除此次的绑定信息，恢复之前的绑定信息
                }
                g_device_set_app_request_bind_type(BLE_APP_REQUEST_BIND_TYPE_NONE);
                submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_BIND_WATCH_SUCC, (void *)p_push_to_page);//绑定成功
            }
        }
        else
        {
            BLE_PB_LOG_I("%s %d: user_id change: %s bind type: %d", __func__, __LINE__, ble_message->ble_bond_user_msg.user_id.arg, g_device_get_app_request_bind_type());
            ble_message_reply.ble_bond_user_msg.state = 1;   //1表示已绑定且user id不同
            //只有二维码配对绑定，才提醒用户ID变更。
            if (g_device_get_app_request_bind_type() == BLE_APP_REQUEST_BIND_QR_CODE)
            {
                //因为会恢复出厂，所以不需要保存User ID
                static msg_tip_select_t s_tip_select = {};
                s_tip_select.type = MSG_TIP_FACTORY_RESTORE_ID_CHANGE;
                submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_FACTORY_RESET_ID_CHANGE, (void *)(&s_tip_select));//提醒用户ID变更
            }
            else if (g_device_get_app_request_bind_type() == BLE_APP_REQUEST_BIND_OTHER)
            {
                //因为不符合标准的绑定流程，所以不需要保存User ID，相反应该恢复绑定信息到前一次状态。
                ble_app_bond_resume();//恢复绑定信息
                g_device_set_app_request_bind_type(BLE_APP_REQUEST_BIND_TYPE_NONE);
                // submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_BIND_WATCH_SUCC, (void *)p_push_to_page);//绑定成功
            }
        }
        ble_app_wait_user_id_timer_stop(true);
    }

    if (local_user_id_len != 0)
    {
        ble_message_reply.ble_bond_user_msg.user_id.arg = local_user_id;
        ble_message_reply.ble_bond_user_msg.user_id.funcs.encode = &encode_string;
    }

    //编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    //编码
    pb_encode(&encode_stream, ble_msg_fields, &ble_message_reply);

    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch0( );

    //对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);

    ble_cmd_end_tx(ble_message_reply.service_type, 0, ble_message_reply.ble_operate_type, 0, encode_stream.bytes_written, pb_crc);
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_bond_info_send
// Purpose       : 回复app是否已与手机处于配对状态
// Param[in]     : None
// Param[out]    : None
// Return type   : static
// Comment       : 2025-07-03
//-------------------------------------------------------------------------------------------
static void ble_bonds_status_get(void)
{
    uint8_t pb_crc = 0;
    ble_msg ble_message;
    uint8_t *data = NULL;
    uint16_t *length = NULL;

    memset(&ble_message, 0, sizeof(ble_msg));

    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

    //参数赋值
    ble_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_BLE;
    ble_message.ble_operate_type = BLE_OPERATE_TYPE_enum_BLE_OPERATE_TYPE_BOND_STATUS_GET;

    ble_message.has_ble_bond_status_msg = true;
    ble_message.ble_bond_status_msg.has_bond_status = true;
    if (ble_app_conn_bond_check() || ble_app_is_pair_complete())
    {
        ble_message.ble_bond_status_msg.bond_status = 1;
    }
    else
    {
        ble_message.ble_bond_status_msg.bond_status = 0;
    }
    //编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    //编码
    pb_encode(&encode_stream, ble_msg_fields, &ble_message);

    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch0( );

    //对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);

    ble_cmd_end_tx(ble_message.service_type, 0, ble_message.ble_operate_type, 0, encode_stream.bytes_written, pb_crc);
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_status_decode
// Purpose       : BLE PB解码接口函数
// Param[in]     : uint8_t * pb_buffer     
//                 uint16_t buffer_length  
//                 END_TYPE end_type       
// Param[out]    : None
// Return type   : 
// Comment       : 2019-06-08
//-------------------------------------------------------------------------------------------
void ble_status_decode(uint8_t * pb_buffer, uint16_t buffer_length, END_TYPE end_type)
{  
    uint8_t status = false;
    char member_id[__MEMBER_ID_MAX_LENGTH] = {0};
    char user_id[__MEMBER_ID_MAX_LENGTH] = {0};
    ble_msg ble_message;
    ble_data_message ble_data_msg;
        
    memset(&ble_message, 0, sizeof(ble_msg));
    memset(&ble_data_msg, 0, sizeof(ble_data_message));

    ble_message.ble_data_msg.arg = &ble_data_msg;
    ble_message.ble_data_msg.funcs.decode = &ble_bond_repeated_submsg_decode;
        
    ble_data_msg.member_id.arg = member_id;
    ble_data_msg.member_id.funcs.decode = &decode_string;

    ble_message.ble_bond_user_msg.user_id.arg = user_id;
    ble_message.ble_bond_user_msg.user_id.funcs.decode = &decode_string;
    
    pb_istream_t decode_stream = pb_istream_from_buffer(pb_buffer, buffer_length);
    
    status = pb_decode(&decode_stream, ble_msg_fields, &ble_message);

    if (true == status)
    {
        switch (ble_message.ble_operate_type)
        {
            case BLE_OPERATE_TYPE_enum_BLE_OPERATE_TYPE_BOND_INFO:			
                ble_bond_info_send();
                break;
            case BLE_OPERATE_TYPE_enum_BLE_OPERATE_TYPE_BOND_REQ:		
                // ble_peripheral_bond();                
                ble_lb55x_peripheral_bond();
                ble_cmd_success_status_tx(ble_message.service_type, 0, ble_message.ble_operate_type, 0);
                break;
            case BLE_OPERATE_TYPE_enum_BLE_OPERATE_TYPE_CONNECT_STATUS:
                app_connect_status = ble_data_msg.status;
                if (g_device_get_userconfog_member_id() != NULL)
                {
                    memcpy(g_device_get_userconfog_member_id(), member_id,__MEMBER_ID_MAX_LENGTH);
                }
                ble_cmd_success_status_tx(ble_message.service_type, 0, ble_message.ble_operate_type, 0);
                break;
            case BLE_OPERATE_TYPE_enum_BLE_OPERATE_TYPE_UNBOND:
                if (g_device_get_userconfog_member_id() != NULL)
                {
                    memset(g_device_get_userconfog_member_id(), 0x00, __MEMBER_ID_MAX_LENGTH);
                }
                submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_UNBIND_WATCH_APP, NULL);
                // ble_unbond_report_callback_notify();
                //停止运动记录
                // stop_activity_record_directly();
                ble_cmd_success_status_tx(ble_message.service_type, 0, ble_message.ble_operate_type, 0);
                // osDelay(500);
                // ble_bonds_delete();      //TODO: Watch 设备不需要重启，应另外处理
                break;
            case BLE_OPERATE_TYPE_enum_BLE_OPERATE_TYPE_WATCH_BOND_USER_GET:
                ble_watch_bond_user_get();
                break;
            case BLE_OPERATE_TYPE_enum_BLE_OPERATE_TYPE_WATCH_BOND_USER_SET:
                ble_watch_bond_user_set(&ble_message);
                break;
            case BLE_OPERATE_TYPE_enum_BLE_OPERATE_TYPE_BOND_STATUS_GET:
                ble_bonds_status_get();
                break;
            default:
                break;
        }
    }
    else
    {
        ble_cmd_err_status_tx(ble_message.service_type, 0, ble_message.ble_operate_type, 0);
    }
}

/************************************************************************
 *@function:void ble_unbond_report_callback_register(ble_unbond_report_callback_t callback);
 *@brief:注册BLE解绑通知回调函数
 *@param: callback - 解绑通知回调函数指针
 *@return:null
*************************************************************************/
void ble_unbond_report_callback_register(ble_unbond_report_callback_t callback)
{
    g_ble_unbond_report_callback = callback;
}

/************************************************************************
 *@function:void ble_unbond_report_callback_unregister(void);
 *@brief:注销BLE解绑通知回调函数
 *@param: null
 *@return:null
*************************************************************************/
void ble_unbond_report_callback_unregister(void)
{
    g_ble_unbond_report_callback = NULL;
}

/************************************************************************
 *@function:void ble_unbond_report_callback_notify(void);
 *@brief:BLE解绑通知上报
 *@param: null
 *@return:null
*************************************************************************/
void ble_unbond_report_callback_notify(void)
{
    if (NULL != g_ble_unbond_report_callback)
    {
        g_ble_unbond_report_callback();
    }
}


/************************************************************************
 *@function:void ble_bond_status_update(BLE_BOND_STATUS_ENUM status);
 *@brief:BLE绑定流程状态主动上报更新
 *@param: status - 绑定状态(BLE_BOND_STATUS_ENUM)
 *@return:null
*************************************************************************/
void ble_bond_status_update(BLE_BOND_STATUS_ENUM status)
{
    if (g_device_get_ble_connect_status())
    {
        ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_BLE, 0, BLE_OPERATE_TYPE_enum_BLE_OPERATE_TYPE_CONNECT_STATUS,
                           0xff, status);
    }
}

/************************************************************************
 *@function:void ble_unbond_status_update(void);
 *@brief: BLE解绑状态主动上报更新
 *@param: null
 *@return:null
*************************************************************************/
void ble_unbond_status_update(void)
{
    if (g_device_get_ble_connect_status())
    {
        ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_BLE, 0, BLE_OPERATE_TYPE_enum_BLE_OPERATE_TYPE_UNBOND,
                           0xff, 0);
    }
}
