/*
 * Copyright (c) 2006-2021, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2024-10-08     qwkj       the first version
 */
#include "dfu_nrf_uart.h"
#include "dfu_uart.h"
#include "ble_common.h"
#include "drv_common.h"
#include "ser_user_data_def.h"
#include "power_ctl.h"
#include "nrf_sdh.h"
#include "ble_pm_manager.h"
#include "qw_fs.h"
#include "ble_ant_module.h"


// #define MIN(a,b) (((a) < (b)) ? (a) : (b))
// #define MODULE_PRINTF rt_kprintf

// 使用动态路径而不是硬编码路径
extern char g_dfu_dat_pack_path[256];
extern char g_dfu_bin_pack_path[256];

extern uint32_t sd_softdevice_disable(void);
extern void ota_update_sub_progress(uint8_t sub_progress);  // OTA子进度更新接口
#define MODULE_PRINTF(...)

bool dfu_update_start_value = false;
bool nrf_dfu_uart_end = false;
dfu_uart_data_s dfu_data_read;
nrf_dfu_response_select_s rsp_select;
nrf_dfu_response_select_s rsp_select_firmwaqre;
nrf_dfu_response_crc_s p_crc_rsp;

uint8_t ping_id = 0;
uint8_t prn = 0;
uint16_t mtu = 0;
uint32_t stp, stp_max;

uint8_t dfu_uart_step = DFU_UART_NOTHING;

bool dfu_uart_step_open = true;//true : write ; false : read
bool dfu_uart_step_prn = true;//true : write ; false : read
bool dfu_uart_step_mtu = true;//true : write ; false : read
bool dfu_uart_step_select_obj = true;//true : write ; false : read
bool dfu_uart_step_creat_obj = true;//true : write ; false : read
bool dfu_uart_step_stream_data_crc = true;//true : write ; false : read
bool dfu_uart_step_get_crc = true;//true : write ; false : read
bool dfu_uart_step_execute_obj = true;//true : write ; false : read
bool dfu_uart_step_select_obj_data = true;//true : write ; false : read

static void put_uint16_le(uint8_t *p_data, uint16_t data)
{
    *(p_data) = (uint8_t)(data);
    *(p_data + 1) = (uint8_t)(data >> 8);
}

static uint16_t get_uint16_le(const uint8_t *p_data)
{
	uint16_t data;

	data  = ((uint16_t)*(p_data));
	data += ((uint16_t)*(p_data + 1) << 8);

	return data;
}

static uint32_t get_uint32_le(const uint8_t *p_data)
{
    uint32_t data;

    data  = ((uint32_t)*(p_data) <<  0);
    data += ((uint32_t)*(p_data + 1) <<  8);
    data += ((uint32_t)*(p_data + 2) << 16);
    data += ((uint32_t)*(p_data + 3) << 24);

    return data;
}

static void put_uint32_le(uint8_t *p_data, uint32_t data)
{
    *(p_data) = (uint8_t)(data >>  0);
    *(p_data + 1) = (uint8_t)(data >>  8);
    *(p_data + 2) = (uint8_t)(data >> 16);
    *(p_data + 3) = (uint8_t)(data >> 24);
}

uint32_t crc32_compute(uint8_t const * p_data, uint32_t size, uint32_t const * p_crc)
{
    uint32_t crc;
    uint32_t i, j;

    crc = (p_crc == NULL) ? 0xFFFFFFFF : ~(*p_crc);
    for (i = 0; i < size; i++)
    {
        crc = crc ^ p_data[i];
        for (j = 8; j > 0; j--)
        {
            crc = (crc >> 1) ^ (0xEDB88320U & ((crc & 1) ? 0xFFFFFFFF : 0));
        }
    }
    return ~crc;
}

#define	SLIP_END				0300//0xC0
#define	SLIP_ESC				0333//0xDB
#define	SLIP_ESC_END			0334//0xDC
#define	SLIP_ESC_ESC			0335//0xDD

void encode_slip(uint8_t *pDestData, uint32_t *pDestSize, const uint8_t *pSrcData, uint32_t nSrcSize)
{
	uint32_t n, nDestSize;

	nDestSize = 0;

	for (n = 0; n < nSrcSize; n++)
	{
		uint8_t nSrcByte = *(pSrcData + n);

		if (nSrcByte == SLIP_END)
		{
			*pDestData++ = SLIP_ESC;
			*pDestData++ = SLIP_ESC_END;
			nDestSize += 2;
		}
		else if (nSrcByte == SLIP_ESC)
		{
			*pDestData++ = SLIP_ESC;
			*pDestData++ = SLIP_ESC_ESC;
			nDestSize += 2;
		}
		else
		{
			*pDestData++ = nSrcByte;
			nDestSize++;
		}
	}

	*pDestData = SLIP_END;
	nDestSize++;

	*pDestSize = nDestSize;
}

int decode_slip(uint8_t *pDestData, uint32_t *pDestSize, const uint8_t *pSrcData, uint32_t nSrcSize)
{
	int err_code = 1;
	uint32_t n, nDestSize = 0;
	bool is_escaped = false;

	for (n = 0; n < nSrcSize; n++)
	{
		uint8_t nSrcByte = *(pSrcData + n);

		if (nSrcByte == SLIP_END)
		{
			if (!is_escaped)
            {
                err_code = 0;  // Done. OK
                *pDestData++ = nSrcByte;
                nDestSize++;
            }

			break;
		}
		else if (nSrcByte == SLIP_ESC)
		{
			if (is_escaped)
			{
				// should not get SLIP_ESC twice...
				err_code = 1;
				break;
			}
			else
				is_escaped = true;
		}
		else if (nSrcByte == SLIP_ESC_END)
		{
			if (is_escaped)
			{
				is_escaped = false;

				*pDestData++ = SLIP_END;
			}
			else
				*pDestData++ = nSrcByte;

			nDestSize++;
		}
		else if (nSrcByte == SLIP_ESC_ESC)
		{
			if (is_escaped)
			{
				is_escaped = false;

				*pDestData++ = SLIP_ESC;
			}
			else
				*pDestData++ = nSrcByte;

			nDestSize++;
		}
		else
		{
			*pDestData++ = nSrcByte;
			nDestSize++;
		}
	}

	*pDestSize = nDestSize;

	return err_code;
}

#define UART_SLIP_SIZE_MAX		128
#define UART_SLIP_BUFF_SIZE		(UART_SLIP_SIZE_MAX * 2 + 1)

static uint8_t uart_slip_buff[UART_SLIP_BUFF_SIZE];

ERROR_STATUS_DFU_E dfu_uart_slip_write_data(uint8_t *send_data, uint32_t data_cnt)
{
    ERROR_STATUS_DFU_E err_code = ERROR_STATUS_DFU_OK;

	uint32_t nSlipSize = 0;

	if (data_cnt > UART_SLIP_SIZE_MAX)
	{
	    rt_kprintf("Cannot encode SLIP!\n");

		err_code = ERROR_STATUS_DFU_CAN_NOT_SLIP;
	}
	else
	{
		encode_slip(uart_slip_buff, &nSlipSize, send_data, data_cnt);
        // for(int i = 0; i < data_cnt; i++)
        // {
        //     rt_kprintf("%02x ",uart_slip_buff[i]);
        // }
		err_code = dfu_uart_write_data(uart_slip_buff, nSlipSize);
	}

	return err_code;
}

static ERROR_STATUS_DFU_E dfu_serial_ping(uint8_t id)
{
    ERROR_STATUS_DFU_E err_code = ERROR_STATUS_DFU_OK;
    uint8_t send_data[2] = { NRF_DFU_OP_PING };

    if(dfu_uart_step_open)
    {
        send_data[1] = id;

        rt_kprintf("PING: Before write data, id=%d\n", id);
        err_code = dfu_uart_slip_write_data(send_data, sizeof(send_data));
        rt_kprintf("PING: After write data, err_code=%d\n", err_code);

        if(err_code == ERROR_STATUS_DFU_OK)
        {
            err_code = ERROR_STATUS_DFU_READ;
            dfu_uart_step_open = false;
            rt_kprintf("PING: Setting step_open to false\n");
        }

        MODULE_PRINTF("err_code 1:%d\n",err_code);
    }
    else
    {
        rt_kprintf("PING: Waiting for sem_read with 3s timeout\n");
        // 改为使用带超时版本，3000毫秒(3秒)超时
        if(rt_sem_take(sem_read, rt_tick_from_millisecond(3000)) != RT_EOK) {
            rt_kprintf("PING: Timeout waiting for response!\n");
            // 超时后重置状态，下次重新发送PING
            dfu_uart_step_open = true;
            return ERROR_STATUS_DFU_READ;
        }
        rt_kprintf("PING: Got sem_read\n");

        memset(&dfu_data_read, 0, sizeof(dfu_data_read));

        dfu_data_read.find_data = NRF_DFU_OP_PING;

        rt_kprintf("PING: Before read data\n");
        err_code = dfu_uart_read_data(&dfu_data_read);
        rt_kprintf("PING: After read data, err_code=%d, data_cnt=%d\n", err_code, dfu_data_read.data_cnt);

        // 打印接收到的数据前10个字节
        rt_kprintf("PING response: ");
        for(int i = 0; i < (dfu_data_read.data_cnt > 10 ? 10 : dfu_data_read.data_cnt); i++)
        {
            rt_kprintf("%02x ", dfu_data_read.dfu_data_buf[i]);
        }
        rt_kprintf("\n");

        MODULE_PRINTF("err_code 2:%d\n",err_code);

        if (!err_code)
        {
            if (dfu_data_read.data_cnt != 5 ||
                dfu_data_read.dfu_data_buf[3] != id)
            {
                err_code = ERROR_STATUS_DFU_DATA;
                rt_kprintf("ping id fail! data_cnt=%d, id=%d, received_id=%d\n",
                          dfu_data_read.data_cnt, id, dfu_data_read.dfu_data_buf[3]);
            }
            else
            {
                rt_kprintf("ping id pass!\n");
            }
        }
    }

    return err_code;
}

static ERROR_STATUS_DFU_E dfu_serial_set_prn(uint8_t prn)
{
    ERROR_STATUS_DFU_E err_code = ERROR_STATUS_DFU_OK;
    uint8_t send_data[3] = { NRF_DFU_OP_RECEIPT_NOTIF_SET };

    if(dfu_uart_step_prn)
    {
        send_data[1] = 0x00;
        send_data[2] = prn;

        err_code = dfu_uart_slip_write_data(send_data, sizeof(send_data));

        if(err_code == ERROR_STATUS_DFU_OK)
        {
            err_code = ERROR_STATUS_DFU_READ;
            dfu_uart_step_prn = false;
        }

        MODULE_PRINTF("err_code 3:%d\n",err_code);
    }
    else
    {
        rt_sem_take(sem_read,RT_WAITING_FOREVER);

        memset(&dfu_data_read,0,sizeof(dfu_data_read));

        dfu_data_read.find_data = NRF_DFU_OP_RECEIPT_NOTIF_SET;

        err_code = dfu_uart_read_data(&dfu_data_read);

        MODULE_PRINTF("err_code 4:%d\n",err_code);

        if (!err_code)
        {
            if (dfu_data_read.data_cnt != 4 ||
                dfu_data_read.dfu_data_buf[0] != 0x60 ||
                dfu_data_read.dfu_data_buf[1] != NRF_DFU_OP_RECEIPT_NOTIF_SET ||
                dfu_data_read.dfu_data_buf[2] != 0x01)
            {
                err_code = ERROR_STATUS_DFU_DATA;
                rt_kprintf("set_prn fail!\n");
            }
            else
            {
                rt_kprintf("set_prn pass!\n");
            }
        }
    }

    return err_code;
}

static ERROR_STATUS_DFU_E dfu_serial_get_mtu()
{
    ERROR_STATUS_DFU_E err_code = ERROR_STATUS_DFU_OK;
    uint8_t send_data[1] = { NRF_DFU_OP_MTU_GET };

    if(dfu_uart_step_mtu)
    {
        err_code = dfu_uart_slip_write_data(send_data, sizeof(send_data));

        if(err_code == ERROR_STATUS_DFU_OK)
        {
            err_code = ERROR_STATUS_DFU_READ;
            dfu_uart_step_mtu = false;
        }

        MODULE_PRINTF("err_code 5:%d\n",err_code);
    }
    else
    {
        rt_sem_take(sem_read,RT_WAITING_FOREVER);

        memset(&dfu_data_read,0,sizeof(dfu_data_read));

        dfu_data_read.find_data = NRF_DFU_OP_MTU_GET;

        err_code = dfu_uart_read_data(&dfu_data_read);

        MODULE_PRINTF("err_code 6:%d\n",err_code);

        if (!err_code)
        {
            if (dfu_data_read.data_cnt != 6 ||
                dfu_data_read.dfu_data_buf[0] != 0x60 ||
                dfu_data_read.dfu_data_buf[1] != NRF_DFU_OP_MTU_GET ||
                dfu_data_read.dfu_data_buf[2] != 0x01)
            {
                err_code = ERROR_STATUS_DFU_DATA;
                rt_kprintf("get_mtu fail!\n");
            }
            else
            {
                mtu = get_uint16_le(dfu_data_read.dfu_data_buf + 3);
                rt_kprintf("get_mtu pass!mtu:%d\n",mtu);
            }
        }
    }

    return err_code;
}

static ERROR_STATUS_DFU_E dfu_serial_select_obj(uint8_t obj_type, nrf_dfu_response_select_s *p_select_rsp)
{
    ERROR_STATUS_DFU_E err_code = ERROR_STATUS_DFU_OK;
    uint8_t send_data[2] = { NRF_DFU_OP_OBJECT_SELECT };

    if(dfu_uart_step_select_obj)
    {
        send_data[1] = obj_type;

        err_code = dfu_uart_slip_write_data(send_data, sizeof(send_data));

        if(err_code == ERROR_STATUS_DFU_OK)
        {
            err_code = ERROR_STATUS_DFU_READ;
            dfu_uart_step_select_obj = false;
        }

        MODULE_PRINTF("err_code 7:%d\n",err_code);
    }
    else
    {
        rt_sem_take(sem_read,RT_WAITING_FOREVER);

        memset(&dfu_data_read,0,sizeof(dfu_data_read));

        dfu_data_read.find_data = NRF_DFU_OP_OBJECT_SELECT;

        err_code = dfu_uart_read_data(&dfu_data_read);

        MODULE_PRINTF("err_code 8:%d\n",err_code);

        if (!err_code)
        {
            if (dfu_data_read.data_cnt != 16 ||
                dfu_data_read.dfu_data_buf[0] != 0x60 ||
                dfu_data_read.dfu_data_buf[1] != NRF_DFU_OP_OBJECT_SELECT ||
                dfu_data_read.dfu_data_buf[2] != 0x01)
            {
                err_code = ERROR_STATUS_DFU_DATA;
                rt_kprintf("select_obj fail!\n");
            }
            else
            {
                p_select_rsp->max_size = get_uint32_le(dfu_data_read.dfu_data_buf + 3);
                p_select_rsp->offset = get_uint32_le(dfu_data_read.dfu_data_buf + 7);
                p_select_rsp->crc = get_uint32_le(dfu_data_read.dfu_data_buf + 11);
                rt_kprintf("Object selected:  max_size:%u offset:%u crc:0x%08X\n", p_select_rsp->max_size, p_select_rsp->offset, p_select_rsp->crc);
                rt_kprintf("select_obj pass!\n");
            }
        }
    }

    return err_code;
}

static ERROR_STATUS_DFU_E dfu_serial_create_obj(uint8_t obj_type, uint32_t obj_size)
{
    ERROR_STATUS_DFU_E err_code = ERROR_STATUS_DFU_OK;
    uint8_t send_data[6] = { NRF_DFU_OP_OBJECT_CREATE };

    if(dfu_uart_step_creat_obj)
    {
        send_data[1] = obj_type;
        put_uint32_le(send_data + 2, obj_size);

        err_code = dfu_uart_slip_write_data(send_data, sizeof(send_data));

        if(err_code == ERROR_STATUS_DFU_OK)
        {
            err_code = ERROR_STATUS_DFU_READ;
            dfu_uart_step_creat_obj = false;
        }

        MODULE_PRINTF("err_code 9:%d\n",err_code);
    }
    else
    {
        rt_sem_take(sem_read,RT_WAITING_FOREVER);

        memset(&dfu_data_read,0,sizeof(dfu_data_read));

        dfu_data_read.find_data = NRF_DFU_OP_OBJECT_CREATE;

        err_code = dfu_uart_read_data(&dfu_data_read);

        MODULE_PRINTF("err_code 10:%d\n",err_code);

        if (!err_code)
        {
            if (dfu_data_read.data_cnt != 4 ||
                dfu_data_read.dfu_data_buf[0] != 0x60 ||
                dfu_data_read.dfu_data_buf[1] != NRF_DFU_OP_OBJECT_CREATE ||
                dfu_data_read.dfu_data_buf[2] != 0x01)
            {
                err_code = ERROR_STATUS_DFU_DATA;
                rt_kprintf("create_obj fail!\n");
            }
            else
            {
                MODULE_PRINTF("create_obj pass!\n");
            }
        }
    }

    return err_code;
}

static ERROR_STATUS_DFU_E dfu_serial_stream_data_crc(const uint8_t *p_data, uint32_t data_size, uint32_t pos_s, uint32_t *p_crc)
{
    ERROR_STATUS_DFU_E err_code = ERROR_STATUS_DFU_OK;
    uint8_t send_data[256] = { NRF_DFU_OP_OBJECT_WRITE };

    if(dfu_uart_step_stream_data_crc)
    {
        MODULE_PRINTF("Streaming Data: len:%u offset:%u crc:0x%08X\n", data_size, pos_s, *p_crc);

		if (mtu >= 5)
		{
			stp_max = (mtu - 1) / 2 - 1;
		}
		else
		{
			rt_kprintf("MTU is too small to send data!\n");

			err_code = ERROR_STATUS_DFU_MTU_TOO_SMALL;
		}
        // rt_kprintf("data_crc:\n");

        for (int pos_index = 0; !err_code && pos_index < data_size; pos_index += stp)
        {
            send_data[0] = NRF_DFU_OP_OBJECT_WRITE;
            stp = MIN((data_size - pos_index), stp_max);
            memcpy(send_data + 1, p_data + pos_index, stp);
            err_code = dfu_uart_slip_write_data(send_data, stp + 1);
        }

        // rt_kprintf("\n");

        if(err_code == ERROR_STATUS_DFU_OK)
        {
            dfu_uart_step_stream_data_crc = false;
            MODULE_PRINTF("stream_data_crc pass!\n");
        }

        MODULE_PRINTF("err_code 11:%d\n",err_code);
    }
    else
    {
        //empty branch
    }

    return err_code;
}

static ERROR_STATUS_DFU_E dfu_serial_get_crc(const uint8_t *p_data, uint32_t data_size)
{
    uint32_t p_crc = 0;
    ERROR_STATUS_DFU_E err_code = ERROR_STATUS_DFU_OK;
    uint8_t send_data[1] = { NRF_DFU_OP_CRC_GET };

    if(dfu_uart_step_get_crc)
    {
        err_code = dfu_uart_slip_write_data(send_data, sizeof(send_data));

        if(err_code == ERROR_STATUS_DFU_OK)
        {
            err_code = ERROR_STATUS_DFU_READ;
            dfu_uart_step_get_crc = false;
        }

        MODULE_PRINTF("err_code 12:%d\n",err_code);
    }
    else
    {
        rt_sem_take(sem_read,RT_WAITING_FOREVER);

        memset(&dfu_data_read,0,sizeof(dfu_data_read));

        dfu_data_read.find_data = NRF_DFU_OP_CRC_GET;

        err_code = dfu_uart_read_data(&dfu_data_read);

        MODULE_PRINTF("err_code 13:%d\n",err_code);

        if (!err_code)
        {
            if (dfu_data_read.data_cnt != 12 ||
                dfu_data_read.dfu_data_buf[0] != 0x60 ||
                dfu_data_read.dfu_data_buf[1] != NRF_DFU_OP_CRC_GET ||
                dfu_data_read.dfu_data_buf[2] != 0x01)
            {
                err_code = ERROR_STATUS_DFU_DATA;
                rt_kprintf("get_crc fail 1!\n");
            }
            else
            {
				p_crc_rsp.offset = get_uint32_le(dfu_data_read.dfu_data_buf + 3);
				p_crc_rsp.crc    = get_uint32_le(dfu_data_read.dfu_data_buf + 7);

                p_crc = crc32_compute(p_data, data_size, &p_crc);

                if (p_crc_rsp.offset != data_size)
                {
                    rt_kprintf("Invalid offset (%u -> %u)!\n", data_size, p_crc_rsp.offset);

                    err_code = ERROR_STATUS_DFU_INIT_OFFESET;
                }
                if (p_crc_rsp.crc != p_crc)
                {
                    rt_kprintf("Invalid CRC (0x%08X -> 0x%08X)!\n", p_crc, p_crc_rsp.crc);

                    err_code = ERROR_STATUS_DFU_INIT_CRC;
                }

                if (!err_code)
                {
                    MODULE_PRINTF("get_crc pass!\n");
                }
                else
                {
                    rt_kprintf("get_crc fail 2!\n");
                }
            }
        }
    }

    return err_code;
}

static ERROR_STATUS_DFU_E dfu_serial_execute_obj(void)
{
    ERROR_STATUS_DFU_E err_code = ERROR_STATUS_DFU_OK;
    uint8_t send_data[1] = { NRF_DFU_OP_OBJECT_EXECUTE };

    if(dfu_uart_step_execute_obj)
    {
        err_code = dfu_uart_slip_write_data(send_data, sizeof(send_data));

        if(err_code == ERROR_STATUS_DFU_OK)
        {
            err_code = ERROR_STATUS_DFU_READ;
            dfu_uart_step_execute_obj = false;
        }

        MODULE_PRINTF("err_code 14:%d\n",err_code);
    }
    else
    {
        rt_sem_take(sem_read,RT_WAITING_FOREVER);

        memset(&dfu_data_read,0,sizeof(dfu_data_read));

        dfu_data_read.find_data = NRF_DFU_OP_OBJECT_EXECUTE;

        err_code = dfu_uart_read_data(&dfu_data_read);

        MODULE_PRINTF("err_code 15:%d\n",err_code);

        if (!err_code)
        {
            if (dfu_data_read.data_cnt != 4 ||
                dfu_data_read.dfu_data_buf[0] != 0x60 ||
                dfu_data_read.dfu_data_buf[1] != NRF_DFU_OP_OBJECT_EXECUTE ||
                dfu_data_read.dfu_data_buf[2] != 0x01)
            {
                err_code = ERROR_STATUS_DFU_DATA;
                rt_kprintf("execute_obj fail!\n");
            }
            else
            {
                MODULE_PRINTF("execute_obj pass!\n");
            }
        }
    }

    return err_code;
}

static ERROR_STATUS_DFU_E dfu_serial_select_obj_data(uint8_t obj_type, nrf_dfu_response_select_s *p_select_rsp)
{
    ERROR_STATUS_DFU_E err_code = ERROR_STATUS_DFU_OK;
    uint8_t send_data[2] = { NRF_DFU_OP_OBJECT_SELECT };

    if(dfu_uart_step_select_obj_data)
    {
        send_data[1] = obj_type;

        err_code = dfu_uart_slip_write_data(send_data, sizeof(send_data));

        if(err_code == ERROR_STATUS_DFU_OK)
        {
            err_code = ERROR_STATUS_DFU_READ;
            dfu_uart_step_select_obj_data = false;
        }

        MODULE_PRINTF("err_code 16:%d\n",err_code);
    }
    else
    {
        rt_sem_take(sem_read,RT_WAITING_FOREVER);

        memset(&dfu_data_read,0,sizeof(dfu_data_read));

        dfu_data_read.find_data = NRF_DFU_OP_OBJECT_SELECT;

        err_code = dfu_uart_read_data(&dfu_data_read);

        MODULE_PRINTF("err_code 17:%d\n",err_code);

        if (!err_code)
        {
            if (dfu_data_read.data_cnt != 16 ||
                dfu_data_read.dfu_data_buf[0] != 0x60 ||
                dfu_data_read.dfu_data_buf[1] != NRF_DFU_OP_OBJECT_SELECT ||
                dfu_data_read.dfu_data_buf[2] != 0x01)
            {
                err_code = ERROR_STATUS_DFU_DATA;
                rt_kprintf("select_obj fail!\n");
            }
            else
            {
                p_select_rsp->max_size = get_uint32_le(dfu_data_read.dfu_data_buf + 3);
                p_select_rsp->offset = get_uint32_le(dfu_data_read.dfu_data_buf + 7);
                p_select_rsp->crc = get_uint32_le(dfu_data_read.dfu_data_buf + 11);
                MODULE_PRINTF("Object selected:  max_size:%u offset:%u crc:0x%08X\n", p_select_rsp->max_size, p_select_rsp->offset, p_select_rsp->crc);
                MODULE_PRINTF("select_obj pass!\n");
            }
        }
    }

    return err_code;
}

int only_test_run = 1;
uint32_t pos = 0;
uint32_t stp_size = 0;

uint32_t crc_32 = 0;

uint32_t crc_32_firmware = 0;

ERROR_STATUS_DFU_E dfu_update(void)
{
    ERROR_STATUS_DFU_E err_code = ERROR_STATUS_DFU_BUSY;

    switch(dfu_uart_step)
    {
        case DFU_UART_NOTHING:
            rt_kprintf("1 - DFU_UART_NOTHING\n");
            dfu_uart_step = DFU_UART_STEP_PING;
            break;
//-------------------------------------------dfu_uart_open---------------------------------------------------------------
        case DFU_UART_STEP_PING:
            rt_kprintf("2 - DFU_UART_STEP_PING, step_open=%d\n", dfu_uart_step_open);
            ping_id = 1;

            err_code = dfu_serial_ping(ping_id);

            // 检查重试次数
            static int ping_retries = 0;
            if (err_code != ERROR_STATUS_DFU_OK) {
                ping_retries++;
                rt_kprintf("PING failed, retry #%d, err=%d\n", ping_retries, err_code);
                if (ping_retries > 10) {
                    // 超过重试次数，尝试恢复
                    rt_kprintf("Too many PING retries, resetting state\n");
                    dfu_uart_step_open = true;
                    ping_retries = 0;
                }
            } else {
                ping_retries = 0;
            }
            break;
        case DFU_UART_STEP_SET_PRN:
            rt_kprintf("3 - DFU_UART_STEP_SET_PRN, step_prn=%d\n", dfu_uart_step_prn);
            prn = 0;
            err_code = dfu_serial_set_prn(prn);
            break;
        case DFU_UART_STEP_GET_MTU:
            rt_kprintf("4 - DFU_UART_STEP_GET_MTU, step_mtu=%d\n", dfu_uart_step_mtu);
            err_code = dfu_serial_get_mtu();
            break;
//-------------------------------------------dfu_uart_init_pack---------------------------------------------------------------
        case DFU_UART_STEP_SELECT_OBJ_CMD:
            rt_kprintf("5\n");
            err_code = dfu_serial_select_obj(0x01, &rsp_select);
            if (NRF_DATA_PACK_SIZE > rsp_select.max_size)
            {
                rt_kprintf("Init packet too big!\n");

                err_code = ERROR_STATUS_DFU_INIT_PACK_TOO_BIG;
            }
            break;
        case DFU_UART_STEP_CREATE_OBJ_INIT_PACK:
            // rt_kprintf("6\n");
            err_code = dfu_serial_create_obj(0x01, NRF_DATA_PACK_SIZE);
            break;
        case DFU_UART_STEP_STREAM_DATA_CRC:
            // rt_kprintf("7\n");
            err_code = dfu_serial_stream_data_crc(nrf_dat_pack, NRF_DATA_PACK_SIZE, 0, &crc_32);
            break;
        case DFU_UART_STEP_GET_CRC:
            // rt_kprintf("8\n");
            err_code = dfu_serial_get_crc(nrf_dat_pack,NRF_DATA_PACK_SIZE);
            break;
        case DFU_UART_STEP_EXECUTE_OBJ:
            // rt_kprintf("9\n");
            err_code = dfu_serial_execute_obj();
            break;
//-------------------------------------------dfu_uart_write_firmware---------------------------------------------------------------
        case DFU_UART_STEP_SELECT_OBJ_DATA:
            // rt_kprintf("10\n");
            err_code = dfu_serial_select_obj_data(0x02, &rsp_select_firmwaqre);
            pos = rsp_select_firmwaqre.offset;
            crc_32_firmware = crc32_compute(nrf_bin_pack, pos, &crc_32);
            dfu_uart_step_creat_obj = true;
            dfu_uart_step_stream_data_crc = true;
            dfu_uart_step_execute_obj = true;
            dfu_uart_step_get_crc = true;
            rt_kprintf("dfu_serial_firmware:  max_size:%u offset:%u data_size:%u\n", rsp_select_firmwaqre.max_size, rsp_select_firmwaqre.offset, NRF_BIN_PACK_SIZE);
            break;
        case DFU_UART_STEP_CREATE_OBJ_FIRMWARE_PACK:
            // rt_kprintf("11\n");
            stp_size = MIN((NRF_BIN_PACK_SIZE - pos), rsp_select_firmwaqre.max_size);
            err_code = dfu_serial_create_obj(0x02, stp_size);
            break;
        case DFU_UART_STEP_STREAM_DATA_CRC_FIRMWARE:
            // rt_kprintf("12\n");
            err_code = dfu_serial_stream_data_crc(nrf_bin_pack + pos, stp_size, pos, &crc_32_firmware);
            break;
        case DFU_UART_STEP_GET_CRC_FIRMWARE:
            // rt_kprintf("13\n");
            err_code = dfu_serial_get_crc(nrf_bin_pack,(pos + stp_size));
            break;
        case DFU_UART_STEP_EXECUTE_OBJ_FIRMWARE:
            // rt_kprintf("14\n");
            err_code = dfu_serial_execute_obj();
            break;
        case DFU_UART_STEP_CREATE_OBJ_FIRMWARE:
            // rt_kprintf("15\n");
            pos += stp_size;

            get_nrf_dfu_uart_percent();

            if(pos < NRF_BIN_PACK_SIZE)
            {
                dfu_uart_step_creat_obj = true;
                dfu_uart_step_stream_data_crc = true;
                dfu_uart_step_execute_obj = true;
                dfu_uart_step_get_crc = true;
                dfu_uart_step = DFU_UART_STEP_CREATE_OBJ_FIRMWARE_PACK;
                MODULE_PRINTF("pos:%d\n",pos);
                return err_code;
            }
            else
            {
                rt_kprintf("ERROR_STATUS_DFU_OK\n");
                err_code = ERROR_STATUS_DFU_OK;
            }
            break;
        case DFU_UART_STEP_END:
            // rt_kprintf("16\n");
            err_code = ERROR_STATUS_DFU_OK;
            nrf_dfu_uart_end = true;
            rt_kprintf("dfu_uart_step_end!!!\n");
            break;
        default:
            break;
    }

    if(err_code == ERROR_STATUS_DFU_OK)
    {
        dfu_uart_step++;
        MODULE_PRINTF("dfu_uart_step:%d\n",dfu_uart_step);
    }

    return err_code;
}

bool get_nrf_dfu_uart_end_flag(void)
{
    return nrf_dfu_uart_end;
}

void dfu_end_func(void)
{
    dfu_uart_step_open = true;//true : write ; false : read
    dfu_uart_step_prn = true;//true : write ; false : read
    dfu_uart_step_mtu = true;//true : write ; false : read
    dfu_uart_step_select_obj = true;//true : write ; false : read
    dfu_uart_step_creat_obj = true;//true : write ; false : read
    dfu_uart_step_stream_data_crc = true;//true : write ; false : read
    dfu_uart_step_get_crc = true;//true : write ; false : read
    dfu_uart_step_execute_obj = true;//true : write ; false : read
    dfu_uart_step_select_obj_data = true;//true : write ; false : read

    dfu_update_start_value = false;

    nrf_dfu_rsc_deinit();

    test_uart_thread_delete();
    rt_sem_delete(sem_read);//动态创建，需要使用delete

    // 删除升级使用的固件和数据文件
    rt_kprintf("Deleting firmware files...\n");



    rt_kprintf("Deleting DAT file: %s\n", g_dfu_dat_pack_path);
    QW_FRESULT ret = qw_f_unlink(g_dfu_dat_pack_path);
    if (ret != FS_OK)
    {
        rt_kprintf("Failed to delete dat pack file: %d\n", ret);
    }
    else
    {
        rt_kprintf("DAT pack file deleted successfully\n");
    }

    rt_kprintf("Deleting BIN file: %s\n", g_dfu_bin_pack_path);
    ret = qw_f_unlink(g_dfu_bin_pack_path);
    if (ret != FS_OK)
    {
        rt_kprintf("Failed to delete bin pack file: %d\n", ret);
    }
    else
    {
        rt_kprintf("BIN pack file deleted successfully\n");
    }

    rt_kprintf("-----dfu_end_func-----\n");

    // drv_reboot();
}

void dfu_thread_func(void *pramater)
{
    rt_kprintf("DFU thread: Creating semaphore\n");
    sem_read = rt_sem_create("sem_read", 0, RT_IPC_FLAG_FIFO);
    if(sem_read == RT_NULL) {
        rt_kprintf("DFU thread: Failed to create semaphore!\n");
        return;
    }
    rt_kprintf("DFU thread: Semaphore created successfully\n");

    rt_thread_mdelay(150);
    rt_pm_request(PM_SLEEP_MODE_IDLE);
    rt_kprintf("DFU thread: Entering main loop, initial step=%d\n", dfu_uart_step);

    int loop_count = 0;
    uint8_t last_progress = 0;

    while(1)
    {
        loop_count++;
        ERROR_STATUS_DFU_E result = dfu_update();
        // rt_kprintf("DFU thread: dfu_update returned %d, new step=%d\n", result, dfu_uart_step);

        // 获取当前DFU进度并更新总体进度
        uint8_t current_progress = get_nrf_dfu_uart_percent();

        // 只有当进度变化超过1%或达到100%时才更新
        if (current_progress != last_progress &&
            (current_progress >= last_progress + 1 || current_progress == 100 || last_progress == 0)) {

            // 更新子项目进度到总体进度
            ota_update_sub_progress(current_progress);

            last_progress = current_progress;
        }

        // 检查是否卡在同一步骤
        static uint8_t last_step = 0xFF;
        static int stuck_count = 0;

        if (dfu_uart_step == last_step) {
            stuck_count++;
            if (stuck_count >= 5) {
                rt_kprintf("DFU thread: WARNING! Possibly stuck at step %d for %d iterations\n",
                           dfu_uart_step, stuck_count);

                // 尝试重置步骤或强制释放信号量
                if (dfu_uart_step == DFU_UART_STEP_PING && !dfu_uart_step_open) {
                    rt_kprintf("DFU thread: Trying to reset PING step\n");
                    dfu_uart_step_open = true;
                    rt_sem_release(sem_read); // 防止任何等待的信号量
                    stuck_count = 0;
                }
            }
        } else {
            last_step = dfu_uart_step;
            stuck_count = 0;
        }

        rt_thread_mdelay(100);
        if(nrf_dfu_uart_end)
        {
            rt_kprintf("DFU thread: nrf_dfu_uart_end flag set, breaking loop\n");

            // 确保DFU完成时进度为100%
            rt_kprintf("[NRF_DFU] DFU completed, ensuring 100%% progress\n");
            ota_update_sub_progress(100);

            break;
        }
    }

    rt_kprintf("DFU thread: Calling dfu_end_func\n");

    dfu_end_func();
}

void dfu_thread_creat()
{

    nrf_dfu_uart_thread_func();//先初始化uart

    dfu_thread_func(RT_NULL);//直接执行
    // rt_thread_t dfu_nrf_thread = rt_thread_create("dfu_nrf_thread", dfu_thread_func, RT_NULL, 10*(1024), 10, 5);

    // if(dfu_nrf_thread != RT_NULL)
    // {
    //     rt_thread_startup(dfu_nrf_thread);
    // }
}

uint8_t get_nrf_dfu_uart_percent(void)
{
    uint8_t nrf_dfu_uart_percent = 0;

    if((pos <= NRF_BIN_PACK_SIZE) && (nrf_dfu_uart_end == false))
    {
        nrf_dfu_uart_percent = (((pos*1.0f)/NRF_BIN_PACK_SIZE) * 100);
    }
    else if(pos == NRF_BIN_PACK_SIZE)
    {
        nrf_dfu_uart_percent = 100;
    }

    return nrf_dfu_uart_percent;
}



bool check_nrf528xx_version(bool start_value)
{
    if(start_value)
    {
        //set nrf52805 enter DFU
        rt_kprintf("check_nrf528xx_version enter dfu\n");

        // 先处理基本的初始化和延时
        rt_thread_mdelay(100);

        // 修改处理流程：
        // 1. 设置睡眠模式
        // 2. 然后进入DFU模式
        // 3. 最后禁用软设备

        // 设置睡眠模式
        rt_kprintf("Set master sleep\n");
        ser_user_settings_master_sleep(true);
        rt_thread_mdelay(200);

        // 首先进入DFU模式
        rt_kprintf("Enter DFU mode\n");
        ser_user_settings_enter_dfu();
        rt_thread_mdelay(200);

        // 最后禁用软设备
        rt_kprintf("Disable softdevice\n");
        sd_softdevice_disable();
        rt_thread_mdelay(200);

        rt_kprintf("Starting DFU thread\n");
        dfu_thread_creat();
        rt_kprintf("DFU initialization complete\n");
        return true;
    }
    else
    {
        rt_kprintf("Please Set the start flag\n");
    }

    return false;
}



bool nordic_serialization_init(void)
{
    ret_code_t err_code;


    if(0 == nrf_dfu_rsc_init())
    {
        // rt_pm_request(PM_SLEEP_MODE_IDLE);
        // ble_ant_resume_prepare(NULL);

        rt_kprintf("ble_ant_resume_prepare success\n");
        rt_thread_mdelay(2000);
        int nordic_ant_version = ser_user_settings_read_version();
        //打印版本
        rt_kprintf("ant_version = %d\n",nordic_ant_version);
        return true;
    }
    else
    {
        rt_kprintf("nrf_dfu_rsc_init fail");
    }
    return false;
}


// 全局变量用于存储DFU升级路径
static char g_nordic_dfu_folder[256] = {0};
static bool g_nordic_dfu_pending = false;

/**
 * @brief Nordic DFU延迟启动线程
 * @param parameter 线程参数（未使用）
 */
static void nordic_dfu_delayed_start_thread(void *parameter)
{
    rt_kprintf("[NRF_DFU] Delayed start thread started\n");



    g_nordic_dfu_pending = false;
    rt_kprintf("[NRF_DFU] Delayed start thread completed\n");
}

/**
 * @brief 简化的Nordic DFU升级接口
 * @param nordic_folder_path Nordic固件文件夹路径
 * @return 0-成功，负数-失败
 */
int nrf_dfu_upgrade(const char *nordic_folder_path)
{
    char dat_pack_path[256] = {0};
    char bin_pack_path[256] = {0};

    if (nordic_folder_path == NULL) {
        rt_kprintf("[NRF_DFU] Error: Invalid folder path\n");
        return -1;
    }

    rt_kprintf("[NRF_DFU] Starting Nordic 52832 DFU upgrade\n");
    rt_kprintf("[NRF_DFU] Firmware folder: %s\n", nordic_folder_path);

    // 构建固件文件路径
    snprintf(dat_pack_path, sizeof(dat_pack_path), "%s/dat_pack.bin", nordic_folder_path);
    snprintf(bin_pack_path, sizeof(bin_pack_path), "%s/bin_pack.bin", nordic_folder_path);

    rt_kprintf("[NRF_DFU] DAT pack file: %s\n", dat_pack_path);
    rt_kprintf("[NRF_DFU] BIN pack file: %s\n", bin_pack_path);

    // 检查固件文件是否存在
    QW_FIL *test_file = NULL;
    if (qw_f_open(&test_file, dat_pack_path, QW_FA_READ) != QW_OK) {
        rt_kprintf("[NRF_DFU] Error: DAT pack file not found: %s\n", dat_pack_path);
        return -1;
    }
    qw_f_close(test_file);

    if (qw_f_open(&test_file, bin_pack_path, QW_FA_READ) != QW_OK) {
        rt_kprintf("[NRF_DFU] Error: BIN pack file not found: %s\n", bin_pack_path);
        return -1;
    }
    qw_f_close(test_file);

    rt_kprintf("[NRF_DFU] Firmware files verified successfully\n");

    // 设置新路径
    strcpy(g_dfu_dat_pack_path, dat_pack_path);
    strcpy(g_dfu_bin_pack_path, bin_pack_path);

    // 保存文件夹路径用于延迟启动
    strcpy(g_nordic_dfu_folder, nordic_folder_path);
    g_nordic_dfu_pending = true;

    rt_kprintf("[NRF_DFU] Starting Nordic DFU process...\n");

    // 启动Nordic蓝牙模块
    ble_ant_nrf_start(NRF_USED_TYPE_SENSOR);
    rt_thread_mdelay(10);

    // 初始化Nordic序列化
    if (nordic_serialization_init()) {
        rt_kprintf("[NRF_DFU] Nordic serialization initialized successfully\n");

        // 启动DFU过程
        if (check_nrf528xx_version(true)) {
            rt_kprintf("[NRF_DFU] Nordic DFU process started successfully\n");
        } else {
            rt_kprintf("[NRF_DFU] Failed to start Nordic DFU process\n");
        }
    } else {
        rt_kprintf("[NRF_DFU] Failed to initialize Nordic serialization\n");
    }

    ble_ant_nrf_stop(NRF_USED_TYPE_SENSOR);

    return 0;
}

void dfu_test_cmd(int argc, char *argv[])
{
    if(strcmp(argv[1], "0") == 0)
    {
        dfu_thread_creat();
    }
    else if(strcmp(argv[1], "1") == 0)
    {
        dfu_uart_step_open = true;
        dfu_uart_step = DFU_UART_STEP_PING;
        rt_sem_release(sem_read);
    }
    else if(strcmp(argv[1], "2") == 0)
    {
        dfu_uart_step_open = true;//true : write ; false : read
        dfu_uart_step_prn = true;//true : write ; false : read
        dfu_uart_step_mtu = true;//true : write ; false : read
        dfu_uart_step_select_obj = true;//true : write ; false : read
        dfu_uart_step_creat_obj = true;//true : write ; false : read
        dfu_uart_step_stream_data_crc = true;//true : write ; false : read
        dfu_uart_step_get_crc = true;//true : write ; false : read
        dfu_uart_step_execute_obj = true;//true : write ; false : read
        dfu_uart_step_select_obj_data = true;//true : write ; false : read
    }
    else if(strcmp(argv[1], "3") == 0)
    {
        check_nrf528xx_version(true);
    }
    else if(strcmp(argv[1], "4") == 0)
    {
        // 测试新的简化接口
        nrf_dfu_upgrade("0:/iGPSPORT/Vendor/nordic");
    }
}
MSH_CMD_EXPORT(dfu_test_cmd,dfu_test_cmd);
