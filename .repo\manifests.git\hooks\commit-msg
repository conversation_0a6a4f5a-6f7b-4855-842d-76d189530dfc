#!/bin/sh
# DO NOT EDIT THIS FILE
# All updates should be sent upstream: https://gerrit.googlesource.com/gerrit/
# This is synced from commit: 62f5bbea67f6dafa6e22a601a0c298214c510caf
# DO NOT EDIT THIS FILE
#
# Part of Gerrit Code Review (https://www.gerritcodereview.com/)
#
# Copyright (C) 2009 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

set -u

# avoid [[ which is not POSIX sh.
if test "$#" != 1 ; then
  echo "$0 requires an argument."
  exit 1
fi

if test ! -f "$1" ; then
  echo "file does not exist: $1"
  exit 1
fi

# Do not create a change id if requested
case "$(git config --get gerrit.createChangeId)" in
  false)
    exit 0
    ;;
  always)
    ;;
  *)
    # Do not create a change id for squash/fixup commits.
    if head -n1 "$1" | LC_ALL=C grep -q '^[a-z][a-z]*! '; then
      exit 0
    fi
    ;;
esac


if git rev-parse --verify HEAD >/dev/null 2>&1; then
  refhash="$(git rev-parse HEAD)"
else
  refhash="$(git hash-object -t tree /dev/null)"
fi

random=$({ git var GIT_COMMITTER_IDENT ; echo "$refhash" ; cat "$1"; } | git hash-object --stdin)
dest="$1.tmp.${random}"

trap 'rm -f "$dest" "$dest-2"' EXIT

if ! cat "$1" | sed -e '/>8/q' | git stripspace --strip-comments > "${dest}" ; then
   echo "cannot strip comments from $1"
   exit 1
fi

if test ! -s "${dest}" ; then
  echo "file is empty: $1"
  exit 1
fi

reviewurl="$(git config --get gerrit.reviewUrl)"
if test -n "${reviewurl}" ; then
  token="Link"
  value="${reviewurl%/}/id/I$random"
  pattern=".*/id/I[0-9a-f]\{40\}"
else
  token="Change-Id"
  value="I$random"
  pattern=".*"
fi

if git interpret-trailers --parse < "$1" | grep -q "^$token: $pattern$" ; then
  exit 0
fi

# There must be a Signed-off-by trailer for the code below to work. Insert a
# sentinel at the end to make sure there is one.
# Avoid the --in-place option which only appeared in Git 2.8
if ! git interpret-trailers \
         --trailer "Signed-off-by: SENTINEL" < "$1" > "$dest-2" ; then
  echo "cannot insert Signed-off-by sentinel line in $1"
  exit 1
fi

# Make sure the trailer appears before any Signed-off-by trailers by inserting
# it as if it was a Signed-off-by trailer and then use sed to remove the
# Signed-off-by prefix and the Signed-off-by sentinel line.
# Avoid the --in-place option which only appeared in Git 2.8
# Avoid the --where option which only appeared in Git 2.15
if ! git -c trailer.where=before interpret-trailers \
         --trailer "Signed-off-by: $token: $value" < "$dest-2" |
     sed -e "s/^Signed-off-by: \($token: \)/\1/" \
         -e "/^Signed-off-by: SENTINEL/d" > "$dest" ; then
  echo "cannot insert $token line in $1"
  exit 1
fi

if ! mv "${dest}" "$1" ; then
  echo "cannot mv ${dest} to $1"
  exit 1
fi
