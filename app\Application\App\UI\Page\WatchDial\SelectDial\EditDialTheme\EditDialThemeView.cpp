/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   EditDialThemeView.cpp
* 
**************************************************************************/
#include "EditDialThemeView.h"
#include "../../qwos_app/GUI/Font/QwFont.h"
#include "../../qwos_app/GUI/QwGUITheme.h"
#include "Image/images.h"
#include "GUI/QwGUIKey.h"
#include "../touchgfx_js/touchgfx_js_api.h"
#include "../touchgfx_js/js_data_servers.h"
#include "../touchgfx_js/JsApp.h"

static const char g_edit_color[] = "_edit_color";
TM_KEY(g_edit_color)

EditDialThemeView::EditDialThemeView(PageManager* manager) :
	PageView(manager),
	index_(0),
	colorNum_(0),
	tick_(0),
	indicator_tick_(0),
	jsAppObjectName_(nullptr),
	cashBuf_(nullptr),
	isReleaseCashBuf_(true),
	jsContainer_(nullptr),
	backLauncherStatus_(BACK_LAUNCHER_STATUS_NONE),
	isRefreshFlag_(true)
{

}

void EditDialThemeView::quit()
{
	//The method is an intentionally-blank override.
}

EditDialThemeView::~EditDialThemeView()
{
	if(strcmp(manager_->get_next_page(), "Launcher") == 0)
	{
		if(backLauncherStatus_ == BACK_LAUNCHER_STATUS_NONE)
		{
			set_dial_cfg_color_inuse_index(dialGoodsId_, index_);
			show_js_dial_theme_color_preview_view(jsPath_, index_);
			isReleaseCashBuf_ = false;
		}
		create_dial_snapshot(dialGoodsId_, cashBuf_, true);
	}
	else if(strcmp(manager_->get_next_page(), "SelectDial") == 0)
	{
		qjs_touchgfx_set_theme_color(jsPath_, get_dial_them_color(usingColorIndex_), usingColorIndex_);
	}

	if(isReleaseCashBuf_)
	{
		if(cashBuf_ != nullptr)
		{
			HAL::freeScreenCachedBuffer(cashBuf_);
		}

	}
	js_free_dial_cache_buf();
	remove(*jsContainer_);
}
void EditDialThemeView::setup()
{
	if(cashBuf_ == nullptr)
	{
		cashBuf_ = HAL::getScreenCachedBuffer(LV_COLOR_DEPTH/8);
	}
	dialGoodsId_ = get_cfg_current_using_dial_goodsid();
	colorNum_ = get_dial_cfg_theme_color_num(dialGoodsId_);
	const char *dialType = get_cfg_dial_type(get_using_dial_index());
	get_dial_path(dialGoodsId_, dialType, jsPath_);
	// 获取当前正在使用的主题颜色索引
	usingColorIndex_ = get_dial_cfg_color_inuse_index(dialGoodsId_);
	delete_backup_config_file();
	// rt_kprintf("@@@@colorNum_%d index_%d dialGoodsId_:%u\n",colorNum_, index_, dialGoodsId_);
	add(bg_);
	bg_.setPosition(0, 0, HAL::DISPLAY_WIDTH, HAL::DISPLAY_HEIGHT);
	bg_.setColor(0x000000);

	bool get_dial_container(CacheableContainer **container);
	if (js_create_dial(jsPath_))
    {
		get_dial_container(&jsContainer_);
		add(*jsContainer_);
		jsContainer_->setCacheBitmapZoom(176);
		jsContainer_->setAlign(ALIGN_IN_CENTER, 0, 0);
		js_stop_dial_timer();
	}	
	else
	{
        get_dial_container(&jsContainer_);
		add(*jsContainer_);
		jsContainer_->setCacheBitmapZoom(176);
		if(isRefreshFlag_)
		{
			js_refresh_dial_view(jsPath_);
			isRefreshFlag_ = true;
		}
		js_stop_dial_timer();
		jsContainer_->setAlign(ALIGN_IN_CENTER, 0, 0);
	}
	
	add(title_text_);
	title_text_.setTextFont(&PUBLIC_NO_24_R_FONT);//PUBLIC_NO_38_M_FONT
    title_text_.setColor(lv_color_white());
    title_text_.setLabelAlpha(LV_OPA_TRANSP);
	title_text_.setTypedDynamicText(_TM(g_edit_color));
	uint8_t title_text_width = title_text_.getTextWidth();
	title_text_.setWidthHeight(title_text_width, 33);
	title_text_.setAlign(ALIGN_IN_TM, 0, 26);

	add(circle_);
	circle_.setWidthHeight(322, 322);
    circle_.setCircle(161, 161, 161);
    circle_.setLineWidth(2);
    circle_.setArc(-120, 240);
    circle_.setColor(0xffffff);
	circle_.setAlign(ALIGN_IN_CENTER, 0, 0);

    add(fab_);
    fab_.setup(FABTN_START::OK);
	static char jsPath[4][50] = {0};
	for(uint8_t i = 0; i < colorNum_; i++)
	{

		
#ifdef SIMULATOR
		snprintf(jsPath[i], 50, "./Dial/%u/yuan%d.bin", dialGoodsId_, i);
#else
		snprintf(jsPath[i], 50, "%s/Dial/%u/yuan%d.bin", BASE_PATH, dialGoodsId_, i);
#endif
		bitmapList[i] = Bitmap(jsPath[i]);
	}

	add(indicator_);
    indicator_.setAlign(ALIGN_IN_LM, 12);
	indicator_.setBitmaps(bitmapList, colorNum_, Bitmap(&dial_select));
    indicator_.setNumberOfPages(colorNum_);
    indicator_.setCurrentPage(usingColorIndex_);
	index_ = usingColorIndex_;
	indicator_tick_ = 2;

	bg_.invalidate();
} 

void EditDialThemeView::handleTickEvent()
{
	if(jsContainer_->getCacheBitmapZoomValue() == 255)
	{
		isRefreshFlag_ = false;
		getRootContainer().removeAll();
		setup();
	}
    if (indicator_tick_ > 0)
    {
        if (UI_TICK_TO_MS_AUTO(tick_, 1000))
        {
            indicator_tick_--;
            if (indicator_tick_ == 0)
            {
                indicator_.setVisible(false);
            }
        }
    }
}

void EditDialThemeView::handleKeyEvent(uint8_t c)
{
	indicator_.setVisible(true);
    indicator_tick_ = 2;
    if (c == KEY_CLK_BACK)
    {
		// js_destory_dial();
		// remove(*jsContainer_);
		isReleaseCashBuf_ = true;
	    manager_->push("SelectDial");
    }
	else if (c == KEY_CLK_START)
	{
		set_dial_cfg_color_inuse_index(dialGoodsId_, index_);
		if(get_dial_edit_type(dialGoodsId_) == EDIT_TYPE_ALL)
		{
		    init_dial_edit_data_index();
			init_dial_prv_edit_data_index();
			isReleaseCashBuf_ = true;
			manager_->push("EditDialDataComponent");
		}
		else
		{
			jsContainer_->setCacheBitmapZoom(255);
			isReleaseCashBuf_ = false;
			backLauncherStatus_ = BACK_LAUNCHER_STATUS_KEY;
		    manager_->push("Launcher");
		}
	}
	else if(c == KEY_KNOB_DOWN || c == KEY_KNOB_UP)
	{
		if(c == KEY_KNOB_DOWN)
		{
			if (index_ <= 0) index_ = colorNum_ - 1;
			else index_--;
			rt_kprintf("upupup colorNum_:%d Index%d dialGoodsId_:%u\n", colorNum_, index_, dialGoodsId_);
		}else if(c == KEY_KNOB_UP)
		{
			if (index_ >= colorNum_-1) index_ = 0;
			else index_++;
			rt_kprintf("downdown colorNum_:%d Index%d dialGoodsId_:%u\n", colorNum_, index_, dialGoodsId_);
		}
		show_js_dial_theme_color_preview_view(jsPath_, index_);
		jsContainer_->updateCache();
		indicator_.setCurrentPage(index_);
	}
}

void EditDialThemeView::handleClickEvent(const ClickEvent& evt)
{
    if (fab_.get_fab_absoluteRect(FABTN_TYPE::START).intersect(evt.getX(), evt.getY()))
	{
		set_dial_cfg_color_inuse_index(dialGoodsId_, index_);
		uint32_t edit_type = get_dial_edit_type(dialGoodsId_);

		if(edit_type == EDIT_TYPE_ALL)
		{
			// 初始化数据选择页面数据索引
			init_dial_edit_data_index();
			init_dial_prv_edit_data_index();
			isReleaseCashBuf_ = true;
			manager_->push("EditDialDataComponent");
		}
		else
		{
			isReleaseCashBuf_ = false;
			backLauncherStatus_ = BACK_LAUNCHER_STATUS_CLICK;
		    manager_->push("Launcher");
		}
        
	}
}

void EditDialThemeView::handleDragEvent(const DragEvent& evt)
{
    indicator_.setVisible(true);
    indicator_tick_ = 2;
}

void EditDialThemeView::handleGestureEvent(const GestureEvent& evt)
{
    indicator_.setVisible(true);
    indicator_tick_ = 2;
	if(evt.getType() == GestureEvent::GestureEventType::SWIPE_VERTICAL)
	{
		// 下滑事件
		if (evt.getVelocity() < -GESTURE_EXIT_ACCURACY || evt.getVelocity() > GESTURE_EXIT_ACCURACY)
		{
			if(evt.getVelocity() < -GESTURE_EXIT_ACCURACY)
			{
				if (index_ <= 0) index_ = colorNum_ - 1;
				else index_--;
				rt_kprintf("upupup colorNum_:%d Index%d dialGoodsId_:%u\n", colorNum_, index_, dialGoodsId_);
			}else if(evt.getVelocity() > GESTURE_EXIT_ACCURACY)
			{
				if (index_ >= colorNum_-1) index_ = 0;
				else index_++;
				rt_kprintf("downdown colorNum_:%d Index%d dialGoodsId_:%u\n", colorNum_, index_, dialGoodsId_);
			}
			show_js_dial_theme_color_preview_view(jsPath_, index_);
			jsContainer_->updateCache();
			indicator_.setCurrentPage(index_);
		}
	}
}

// Notification Callback function
// ObserverDrawable Callback function
// custom function

