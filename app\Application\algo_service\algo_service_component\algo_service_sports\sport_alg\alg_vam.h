/************************************************************************​
*Copyright(c) 2025, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#ifndef ALG_VAM_H
#define ALG_VAM_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

//VAM
//意大利语velocità ascensionale media的缩写，以m/h为单位的平均上升速度
//普遍使用后，引申为：Vertical Ascent in Meters/hour

//计算VAM的输入数据
typedef struct _VamCalcInput
{
    uint32_t timestamp;                     //时间戳
    float alt;                              //海拔（m），规定小于-999.0m为无效值
} VamCalcInput;

//计算VAM的输出数据
typedef struct _VamCalcOutput
{
    float vam;                              //实时VAM（m/h）
    float vam_30s;                          //30s平均VAM（m/h）
} VamCalcOutput;

//计算VAM
//假定每秒计算一次
typedef struct _VamCalculator
{
    float *buf;                             //缓冲区，保存历史海拔输入
    uint32_t capacity;                      //缓冲区容量，必须为30，对应30s的输入海拔
    uint32_t len;                           //缓冲区中已保存的海拔数据数量
    uint32_t next;                          //下一次要存储的海拔数据的位置（索引）
    uint32_t timestamp;                     //上一次输入的时间戳
} VamCalculator;

int vam_calculator_exec(VamCalculator *self, const VamCalcInput *input, VamCalcOutput *output);

void vam_calculator_reset(VamCalculator *self);

#ifdef __cplusplus
}
#endif

#endif