/************************************************************************​
*Copyright(c) 2024, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#ifndef ALG_CALORIE_H
#define ALG_CALORIE_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

//卡路里功率计算方案
typedef struct _CalorieByPowerCalculator
{
    uint32_t cnt;
    uint32_t timestamp;                 //时间戳（s）
} CalorieByPowerCalculator;

//卡路里心率计算方案
typedef struct _CalorieByHrCalculator
{
    float hr_max;                       //最大心率（bpm）
    float hr_zones[5];                  //心率区间（50% 60% 70% 80% 90% * HRmax）
    float coeff_zones[5];               //系数区间，用于计算心率在对应心率区间时卡路里系数
    float weight;                       //体重（kg）
    uint32_t cnt;
    uint32_t timestamp;
    uint8_t gender;                     //性别，0-女性 1-男性
    uint8_t age;                        //年龄（岁）
} CalorieByHrCalculator;

//卡路里估计功率计算方案（估计功率使用运动学公式计算得到）
typedef struct _CalorieByPwrEstCalculator
{
    uint32_t cnt;
    uint32_t timestamp;                 //时间戳（s）
} CalorieByPwrEstCalculator;

//卡路里计算初始化所需数据
typedef struct _CalorieCalcInit
{
    float weight;                       //体重（kg）
    uint8_t gender;                     //性别，0-女性 1-男性
    uint8_t age;                        //年龄（岁）
} CalorieCalcInit;

//卡路里计算输入数据
typedef struct _CalorieCalcInput
{
    uint32_t timestamp;                 //时间戳（s）
    float pwr_est;                      //估计功率（w），小于0表示无效
    uint16_t power;                     //功率（w），0xFFFF表示无效
    uint8_t hr;                         //心率（bpm），0xFF表示无效
} CalorieCalcInput;

//卡路里计算
typedef struct _CalorieCalculator
{
    CalorieByPowerCalculator cal_power_calculator;
    CalorieByHrCalculator cal_hr_calculator;
    CalorieByPwrEstCalculator cal_pwr_est_calculator;
} CalorieCalculator;

void calorie_calculator_init(CalorieCalculator *self, const CalorieCalcInit *init);

float calorie_calculator_exec(CalorieCalculator *self, const CalorieCalcInput *input);

#ifdef __cplusplus
}
#endif

#endif