#include "lvgl.h" 

#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP ALIGN(4)
#endif

#ifndef eZIP_RGBARGB888A
#define eZIP_RGBARGB888A
#endif


LV_ATTRIBUTE_MEM_ALIGN_EZIP const  uint8_t paring_map[] SECTION(".ROM3_IMG_EZIP.paring") = { 
    0x00,0x00,0x0a,0xcb,0x46,0x08,0x20,0x00,0x01,0x1f,0x00,0x7b,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0x04,0x00,0x00,0x00,0x70,0x00,0x00,0x03,0x10,0x00,0x00,0x05,0xd8,
    0x00,0x00,0x08,0xe0,0x3d,0x73,0x4b,0x76,0x1c,0x27,0x03,0x70,0x9c,0xca,0x16,0x7a,0x1e,0x80,0x67,0x28,0x18,0xae,0x0b,0xa0,0xa3,0xc0,0x0a,0xb8,0xc3,0x26,0x7a,0x96,
    0x20,0x7b,0x09,0xb0,0x0a,0x28,0x06,0x98,0x15,0x8c,0x95,0xe9,0x0d,0xc0,0x74,0x15,0x3c,0x00,0x2f,0xdc,0x29,0x7a,0x0f,0x46,0x3a,0x28,0x8d,0xcf,0x39,0xb6,0x75,0xb1,
    0x64,0xcb,0xf1,0xf7,0x55,0xb9,0xd2,0x33,0xb1,0x15,0x5b,0x47,0xfa,0xfc,0x5f,0x00,0xac,0x0e,0x1e,0x74,0x5d,0x27,0xf4,0x87,0xf0,0x39,0x77,0x8b,0x54,0x55,0x55,0x1f,
    0x00,0x60,0xd9,0x7d,0x37,0x21,0x9c,0xd6,0xfe,0x53,0xec,0x68,0x3e,0x1a,0x44,0x04,0xb0,0x92,0x7c,0x6c,0x94,0xd3,0xee,0x7c,0x5e,0x90,0x10,0x40,0x66,0x8e,0x4f,0xc4,
    0x53,0x23,0x9e,0x07,0xa4,0x9d,0x0b,0x00,0xc8,0x1d,0xf9,0xd8,0xcd,0x26,0x99,0x92,0x47,0x5c,0x74,0x04,0xa4,0x98,0x06,0x80,0xbc,0x91,0x0f,0xe2,0x79,0x0e,0x51,0x20,
    0x24,0xc7,0x94,0x36,0xec,0xd1,0xda,0x32,0xc7,0xaa,0xf7,0xb2,0xaa,0x7c,0x48,0x31,0x26,0xff,0x38,0x08,0x08,0x52,0x4a,0xa7,0xb5,0x2f,0xb5,0xd6,0x36,0x73,0xd6,0x6e,
    0xe8,0x18,0x01,0x76,0x56,0x84,0xf5,0xe2,0x69,0x97,0xf9,0x65,0xc7,0x79,0x26,0xf5,0xb8,0xde,0x73,0x8d,0xc7,0xd1,0x8a,0xaf,0xd8,0x3a,0x90,0x20,0xc2,0x68,0x4b,0x6b,
    0x6e,0x8c,0xec,0xfd,0x45,0xee,0xe9,0x44,0xe7,0xe7,0xe1,0x0f,0x30,0x29,0x20,0xb3,0x70,0xa8,0xfd,0xc0,0x8c,0xf5,0xb5,0xb5,0x7a,0xaa,0x69,0xb8,0x1c,0x72,0xef,0xfb,
    0xa3,0x23,0xe7,0xdb,0x45,0xcb,0xd9,0x3e,0x23,0x72,0x01,0xc4,0xb3,0x60,0xc7,0xf7,0x58,0x40,0xce,0x59,0x0a,0x53,0x69,0x25,0x73,0x04,0x7b,0x12,0x4f,0x5f,0x40,0xed,
    0x12,0xdd,0x2e,0x00,0x58,0xb0,0x96,0xb8,0x11,0x44,0xae,0x8e,0x18,0xf2,0x01,0x80,0x55,0x24,0x7a,0x9a,0x59,0xbd,0x17,0x01,0x35,0x95,0xdc,0x9d,0x04,0xd7,0xbd,0x28,
    0x8a,0xc6,0xb0,0x70,0xca,0x05,0xa9,0xe5,0x13,0x9a,0xcb,0xea,0xf3,0x65,0xae,0xe2,0xb5,0xcd,0x49,0x85,0x67,0xfe,0xba,0x8b,0x02,0x3a,0x40,0xea,0xd4,0xab,0xa8,0xc8,
    0x27,0xb2,0x78,0x95,0x34,0xfa,0xb0,0x12,0x14,0xa5,0xb5,0x10,0x01,0x32,0x44,0xf6,0x4f,0x45,0x70,0x9e,0x68,0x96,0x28,0x9b,0x71,0xa8,0xbb,0x8b,0x7c,0x66,0xd0,0xa6,
    0x88,0x3e,0xec,0x1f,0x43,0x46,0x1a,0xf9,0xcc,0x72,0x86,0x05,0x38,0xcf,0x14,0x4d,0xc8,0xfa,0x16,0x43,0x35,0x1a,0xf3,0xa2,0xb5,0x34,0x73,0xcb,0x1f,0xe6,0xbe,0x52,
    0xcb,0xec,0xb4,0x52,0xfb,0x4e,0xde,0x5b,0x08,0x09,0x10,0xb9,0xce,0xce,0xbd,0x97,0x69,0xae,0xf5,0x29,0x7b,0xe5,0x8f,0x58,0x19,0x89,0x5b,0x44,0xb5,0x65,0xf9,0x00,
    0x50,0x6c,0x7e,0xbc,0xa9,0xc5,0x1a,0xdd,0xab,0xc0,0x20,0xe0,0x4c,0xab,0x1d,0x60,0xa7,0xad,0xe9,0xb5,0x23,0xb9,0x27,0xb5,0x27,0xe4,0x03,0x40,0x8b,0x7d,0x9b,0x52,
    0x45,0x3e,0x00,0x44,0x3d,0xab,0x44,0x3f,0xd4,0x7c,0x00,0xb6,0x19,0xf5,0xa8,0x95,0x1a,0x29,0xad,0xa9,0x55,0x23,0x1f,0x80,0xb2,0xc4,0x23,0x33,0x08,0xe6,0xda,0x17,
    0x8d,0xab,0xdd,0xdd,0x8b,0x4c,0x44,0xae,0x68,0xcc,0x3c,0xeb,0x9c,0xb6,0x3d,0xf2,0x01,0x28,0x33,0xdd,0x32,0x72,0x69,0x5c,0x92,0x19,0xa3,0x77,0x5d,0xff,0xfa,0xba,
    0x17,0x95,0xa5,0xb8,0x4f,0xa9,0xc7,0x53,0xb1,0xf7,0x48,0xcd,0x07,0xa0,0x9c,0x74,0xcb,0x6c,0xe2,0x4b,0xf5,0x3f,0x2e,0x73,0x37,0xf5,0x88,0x94,0x6a,0x7b,0x98,0x94,
    0xe9,0x62,0x04,0x97,0x20,0xfd,0xa2,0xe0,0x0c,0xb0,0xd1,0x74,0xab,0xc9,0x29,0x9c,0xa9,0xe8,0xa8,0x27,0xa2,0x66,0xc6,0xb3,0xb7,0xc8,0x07,0x60,0x5b,0xe2,0xb9,0x49,
    0x67,0xf5,0xb6,0xfc,0x4c,0x09,0x89,0x39,0x51,0x1f,0x35,0x1f,0x80,0xe5,0xea,0x3c,0xd1,0xf5,0x9c,0x27,0x2d,0x6e,0xf3,0xef,0x73,0xef,0xbf,0xaf,0x4f,0xbb,0x60,0xa1,
    0xbf,0x61,0x45,0x58,0x47,0x08,0x35,0xba,0xfe,0x83,0x7c,0x00,0xf2,0xd7,0x79,0x82,0xa5,0x63,0x65,0x23,0x3d,0xdb,0xe8,0xcf,0x3a,0x5b,0xfa,0xfa,0x77,0x51,0x96,0xf9,
    0x7d,0xdf,0xdf,0x36,0x12,0x32,0x32,0xb1,0xe3,0x88,0x00,0x09,0x2b,0xd2,0x2e,0x80,0xb2,0xd2,0x2d,0xe5,0x5b,0xd3,0x31,0xc2,0x31,0x75,0x14,0x5b,0x4b,0x69,0xed,0xe6,
    0x17,0x09,0xa2,0x33,0x33,0xa6,0xa1,0xf6,0x91,0xa6,0xad,0x09,0x85,0x14,0xa5,0xa3,0xd2,0x2f,0xe4,0x03,0x10,0x9e,0xfe,0xc8,0x80,0xda,0xce,0xc5,0x57,0x3a,0x09,0x85,
    0x33,0x25,0x22,0x79,0x13,0x91,0x67,0x2a,0xd6,0x04,0xa4,0x5f,0x41,0xf7,0xfd,0x5f,0x3c,0x76,0x5d,0x57,0x77,0xe3,0xd4,0x87,0x01,0x1c,0xd7,0x14,0x4f,0xc4,0x33,0x0d,
    0xce,0x03,0xec,0x0f,0xbd,0x16,0x5a,0xcf,0x65,0x56,0x7b,0x8c,0x25,0x02,0xc6,0xcb,0x41,0xed,0x79,0x9f,0xbe,0xfb,0xbd,0x0d,0x99,0xcb,0x13,0xcb,0x09,0xc0,0x5b,0x3c,
    0x66,0xa3,0x0a,0x8f,0x53,0x2f,0x55,0x55,0xa9,0x29,0xe9,0xe8,0x0f,0xe9,0x39,0x56,0x4e,0xa4,0xbd,0x9f,0x83,0xbe,0xdf,0x51,0x09,0x99,0xef,0xf4,0x39,0xe6,0x79,0x5c,
    0x72,0x79,0x90,0xa9,0x3e,0xff,0xe2,0xf3,0xe3,0x47,0x96,0x14,0x80,0xb7,0x78,0x64,0x02,0xf1,0xd4,0x76,0x13,0x8b,0x82,0x1e,0x4f,0xba,0xa2,0x35,0xfb,0x4c,0x3e,0x52,
    0x11,0xbe,0x99,0x02,0xf2,0x01,0x48,0x27,0x9e,0xc6,0x43,0x3c,0xb2,0xe0,0x47,0x95,0x89,0x04,0x24,0x6d,0x74,0x87,0x7c,0x00,0x52,0xa4,0x27,0x1e,0xe2,0xa9,0xa7,0x6a,
    0x45,0x85,0x8b,0x27,0x44,0x40,0x8d,0xc7,0x38,0x2d,0xf2,0x01,0x98,0x1f,0xf5,0x44,0x8b,0xe7,0x56,0x54,0x2e,0x2c,0xcd,0xf2,0x4d,0xc3,0xc4,0x58,0x0d,0xc8,0x47,0x40,
    0xae,0x02,0x34,0xf2,0x01,0x98,0x17,0xf5,0xa8,0xa9,0x88,0xa7,0xc0,0xfa,0x4e,0x08,0xad,0x43,0x40,0xca,0xa3,0xfe,0x23,0x90,0x0f,0x40,0x86,0xa8,0x67,0xaa,0xb3,0x13,
    0xda,0x7a,0xde,0x9a,0x7c,0xed,0xb3,0xab,0xd8,0xeb,0x91,0x0f,0x40,0x7c,0xd4,0xd3,0x24,0x68,0xcb,0x97,0x8e,0x70,0x48,0xb4,0x89,0x8d,0x7e,0x90,0x0f,0x40,0x5c,0xd4,
    0x33,0x55,0xe7,0xa9,0x37,0x52,0x5c,0x0e,0x11,0x50,0x3d,0x51,0x80,0x8e,0x8a,0x7e,0x90,0x0f,0x40,0x7c,0x87,0x6b,0x0f,0xe2,0xf1,0xe9,0x82,0x45,0x45,0x3f,0xc8,0x07,
    0x20,0x61,0xd4,0x73,0xa7,0xe2,0xe9,0x0b,0x48,0xa4,0x8a,0x7e,0x90,0x0f,0x40,0xa0,0x40,0x1c,0xe9,0xd6,0x5e,0xe7,0x26,0x38,0xfa,0x41,0x3e,0x00,0x81,0x51,0x4f,0xea,
    0x54,0x6d,0x83,0xf5,0x9f,0xb1,0xe8,0xa7,0x09,0x11,0x17,0xf2,0x01,0x78,0xcc,0x99,0xa8,0x27,0x2e,0xfa,0x99,0x48,0x45,0x69,0xb5,0x03,0xf8,0xbc,0xd9,0x27,0xbe,0x53,
    0x23,0xe2,0x11,0x3b,0x89,0x7a,0x9c,0xed,0x73,0x47,0xed,0xe7,0xd1,0x75,0xc8,0x07,0xc0,0x3f,0x7a,0xb9,0x46,0x08,0x8b,0xda,0x0f,0x91,0x0f,0xc0,0x3c,0x76,0xda,0xe1,
    0x8a,0xa9,0xfd,0x78,0x49,0x0b,0xf9,0x00,0xf8,0x49,0x44,0x51,0xeb,0xf1,0x46,0xf9,0xa4,0xb5,0xc8,0x07,0xe0,0xff,0x75,0x9b,0x98,0x94,0x0b,0x59,0x07,0xa6,0x5e,0xb7,
    0xb9,0x46,0x3e,0x00,0x7e,0x75,0x1b,0x45,0xca,0x95,0xac,0xd6,0x25,0x91,0x0f,0x80,0x7f,0x8b,0x5d,0x91,0x72,0xf9,0x45,0x8c,0x76,0xae,0x14,0x05,0x67,0x80,0xbc,0x35,
    0x0c,0x52,0xaf,0x48,0x4e,0xcc,0x5d,0x91,0x6f,0x93,0xf7,0xf5,0xc7,0x4b,0x7d,0xbc,0xd0,0xc7,0x5b,0x7d,0xbc,0xd6,0x6f,0x93,0x37,0x05,0x8d,0xf7,0x25,0x1b,0x29,0x14,
    0x39,0x5e,0x86,0x14,0xe2,0x1a,0x13,0x2d,0xed,0x3c,0xf5,0xba,0x4e,0x7c,0x27,0x90,0x4f,0x79,0xd2,0x79,0x4f,0x7f,0xfc,0x4c,0x1f,0x3f,0x1a,0xf8,0xee,0x43,0xfd,0xf1,
    0x81,0xde,0x94,0xff,0x58,0x71,0xbc,0x17,0x76,0xbc,0x1f,0x0f,0x7c,0xf7,0x2b,0xfd,0xf1,0x2a,0xf1,0x78,0xe6,0xfe,0xfe,0x7e,0x87,0x35,0x0f,0x20,0xed,0x2a,0x4e,0x3c,
    0x1f,0x0f,0x89,0xc2,0xf2,0x7d,0xf3,0xbd,0x3e,0xef,0x33,0x89,0xc7,0xfb,0x6c,0x80,0x28,0x3e,0x1e,0x12,0x85,0xe5,0x7b,0xfa,0x68,0x13,0x8f,0x67,0xee,0xef,0x73,0xac,
    0x8e,0xfb,0x4b,0x55,0x4d,0xad,0x08,0xf9,0x94,0xc3,0x4f,0xf5,0xf1,0xbe,0xe3,0x9c,0x2f,0xd8,0x48,0x61,0xad,0xf1,0xbe,0xec,0x38,0xe7,0xf3,0x2b,0x8e,0x97,0xb3,0xcd,
    0xae,0x22,0xae,0xd9,0x75,0xd1,0x99,0xc8,0x67,0x3b,0x7f,0xbc,0x2f,0x4e,0x44,0x00,0xcf,0x22,0x02,0x7d,0xfe,0xd7,0x12,0x8e,0xf7,0x5d,0xcf,0xf1,0x7e,0x12,0x30,0xde,
    0xd7,0x13,0x8e,0xf7,0x1d,0x7d,0xfe,0x37,0x58,0x25,0xa4,0x5d,0x90,0x87,0x97,0x89,0xcf,0x4f,0x3d,0xde,0x37,0x0b,0x1f,0x2f,0x6b,0xed,0x66,0xa8,0xcd,0x4e,0xbd,0x67,
    0x9a,0x91,0x39,0x7b,0x34,0x7f,0xc8,0xa7,0x0c,0xde,0x0b,0x3c,0xff,0x05,0xe3,0xc1,0xd6,0x0b,0xef,0xc8,0xa7,0x0c,0xfe,0x13,0x78,0xfe,0x5b,0xc6,0x5b,0xae,0x38,0x9a,
    0xf0,0x1a,0x40,0x3e,0xc5,0xf1,0x49,0xe2,0xf3,0x53,0x8f,0xf7,0xba,0xf0,0xf1,0x8a,0x2c,0xa8,0xc2,0x34,0xff,0x05,0x00,0x00,0x3c,0x76,0x5d,0x57,0x77,0xe3,0xd4,0x87,
    0x01,0x1c,0xd7,0x14,0x4f,0xc4,0x33,0x0d,0xce,0x43,0x4a,0xf4,0x6f,0xfc,0xc2,0xf3,0xf6,0x7f,0xbd,0xd2,0x78,0x3f,0xf7,0x1c,0xef,0x37,0x89,0xc7,0xfb,0x68,0x81,0xb9,
    0x17,0x8e,0x7b,0x10,0x11,0xd7,0xec,0x89,0x98,0xf9,0x11,0xc7,0x03,0x94,0xc2,0x07,0xfa,0xf8,0x83,0xe3,0x9c,0x3f,0xea,0xe3,0x95,0xe7,0x78,0xaf,0x3c,0xc6,0xfb,0x53,
    0xc0,0x78,0xe6,0xfe,0x7e,0xbf,0xf0,0x78,0x7f,0x0e,0x18,0x2f,0x9a,0xaa,0xaa,0xd4,0x12,0xd7,0xc0,0x63,0x90,0x4f,0x21,0xe8,0xc5,0xfc,0x2f,0xfd,0xf1,0x6d,0x7d,0x7c,
    0x38,0x72,0x8a,0x89,0x28,0xbe,0xa5,0xcf,0xfb,0x9b,0xe7,0x78,0xff,0x36,0xe7,0x3b,0xc6,0xbb,0x04,0x8e,0x67,0xee,0xef,0x97,0x8e,0xfb,0xfb,0x6b,0xa2,0xf1,0x3e,0xb2,
    0xf7,0xf7,0x97,0x02,0xfe,0x3c,0x82,0x15,0x1a,0x2c,0x62,0xe1,0xba,0xe6,0xc4,0xd4,0x15,0xf5,0x47,0xfc,0xa7,0xfe,0xf8,0x81,0x0e,0x49,0xbf,0xaa,0x3f,0x5f,0xea,0xe3,
    0x85,0x3e,0xde,0xea,0xe3,0x13,0xfd,0xdd,0xef,0x22,0x85,0x36,0x34,0xde,0x6b,0xfd,0xdd,0x6f,0x23,0xc7,0xfb,0xa1,0x1e,0xef,0x2b,0xfa,0xf3,0x5c,0xda,0x78,0xb0,0x2d,
    0x90,0x4f,0x99,0x12,0xfa,0x54,0x7f,0x7c,0x5a,0xf0,0x78,0x6f,0xf4,0xc7,0x9b,0x52,0xc7,0x8b,0x44,0x4d,0xbc,0xad,0xcf,0x11,0xd7,0xec,0x05,0x45,0xda,0x05,0xb0,0x3c,
    0x0d,0x53,0x70,0xb8,0x06,0x0a,0xfb,0x9d,0xb0,0x90,0x0f,0x00,0x35,0x9f,0x1c,0x91,0x8f,0x20,0xf2,0x01,0x48,0x10,0xc5,0x0c,0xb5,0x93,0x6d,0xa1,0x55,0x51,0x6c,0x7e,
    0x36,0x57,0xb5,0x4f,0xb4,0x84,0x7c,0x00,0xfc,0x90,0x4c,0x41,0x9a,0xb4,0x53,0x0b,0xab,0x46,0x3e,0x00,0xfe,0x51,0x8c,0xa0,0xee,0x93,0x56,0xd4,0xc8,0x07,0xc0,0x5d,
    0x3c,0x75,0xa5,0x5e,0x7b,0x15,0x76,0xed,0x33,0x47,0x63,0xb2,0x46,0x3e,0x00,0xfe,0x6d,0x63,0x49,0xf4,0xe3,0x7c,0x66,0xe1,0x3b,0xc7,0xc8,0x07,0x60,0x66,0x01,0xd9,
    0x46,0x00,0x6a,0xef,0x51,0x8f,0x4f,0xca,0xd5,0x8f,0x14,0x91,0x0f,0x40,0x40,0xcb,0x7d,0x22,0xad,0x68,0xf6,0x1e,0xf5,0x78,0x74,0xb9,0x1a,0x6a,0x3e,0x00,0xf1,0x1d,
    0x1c,0x99,0x32,0x6a,0xda,0x53,0xd4,0x43,0xc1,0x19,0xc0,0x33,0x2d,0x98,0x88,0x7e,0xea,0x1d,0x47,0x3f,0xb1,0x51,0xcf,0x33,0x69,0x21,0x1f,0x00,0xa2,0x1f,0xef,0x79,
    0x99,0x11,0xf5,0x34,0x44,0x3e,0x00,0x09,0x0a,0xc8,0x63,0x6f,0x7a,0x7d,0xed,0xe5,0x5e,0x05,0x34,0x26,0x9e,0x98,0xa8,0x07,0xf9,0x00,0x24,0x8e,0x7e,0xee,0x38,0xfd,
    0xba,0x44,0xce,0xc5,0xe8,0x7c,0x20,0x1f,0x80,0xc8,0x14,0x4a,0xbf,0xf1,0xdb,0x89,0x6b,0x2f,0x77,0x96,0x6e,0xa9,0x94,0x51,0x0f,0xf2,0x01,0x98,0x17,0xfd,0x88,0x89,
    0xf4,0x4b,0xdd,0x49,0x04,0xd4,0x38,0xd2,0x2d,0x19,0x3b,0x87,0xc8,0x07,0x60,0x46,0xf4,0x63,0x36,0x9f,0xde,0x84,0x62,0xe2,0x8d,0xdf,0xdc,0xa9,0x78,0x84,0x4f,0x6b,
    0x7d,0xa2,0x40,0x8d,0x7c,0x00,0x66,0x46,0x3f,0x86,0xf6,0x0e,0x05,0x34,0xd5,0xd9,0x7a,0x78,0xe6,0xb9,0x73,0x87,0x7c,0x00,0xe6,0x47,0x3f,0x93,0x45,0xd7,0x0d,0x0a,
    0xe8,0x32,0x25,0x9e,0xb1,0x5a,0x57,0xa0,0xbc,0x90,0x0f,0x80,0x87,0x80,0x7c,0xda,0xe7,0x62,0x6a,0x53,0x9a,0x8d,0xa8,0x8f,0xaa,0xf0,0x36,0xbc,0xb2,0xe2,0x51,0x13,
    0xe2,0x31,0x42,0x11,0xae,0x71,0x5c,0xe2,0x41,0x3e,0x00,0x69,0xd3,0x2f,0xe1,0xea,0xfe,0x58,0x91,0x95,0x18,0x05,0x19,0x61,0xf8,0x88,0x47,0x26,0x9a,0xab,0xc3,0x89,
    0x35,0x05,0xe0,0x97,0x7e,0xe9,0xcd,0x77,0xf1,0xa8,0x75,0x98,0x02,0xf4,0x64,0xa1,0xd5,0x7c,0x67,0xce,0x71,0xa5,0x6b,0x0b,0x46,0x3b,0xcd,0x94,0x74,0x7a,0xa9,0x96,
    0x98,0xd3,0x96,0x27,0xf2,0x01,0x98,0x57,0xff,0xf1,0x79,0xab,0x1b,0x01,0x8d,0x16,0xa1,0x7b,0x69,0xd8,0xad,0x16,0xa4,0x56,0x4c,0xb1,0x5c,0xd1,0x8e,0x08,0x14,0x4f,
    0xed,0x7b,0x03,0xc8,0x07,0x20,0x4c,0x40,0xbe,0xc5,0x63,0x61,0xbb,0x60,0xae,0x34,0xac,0xee,0xa5,0x62,0xaa,0x14,0xe9,0xf4,0xda,0xe9,0x59,0xc4,0x43,0xda,0x05,0x10,
    0x29,0x20,0xbd,0x31,0xcf,0x9e,0x9b,0xd2,0x99,0x86,0xf5,0xa4,0xd6,0xaf,0xad,0xa4,0x4c,0xc9,0xde,0x45,0x6c,0xbe,0x29,0x51,0x40,0x7d,0xe7,0xd9,0xfd,0x23,0x1f,0x80,
    0xcc,0x1d,0xb0,0xae,0x57,0xb8,0x49,0x21,0xa0,0x81,0x8d,0x5c,0xf7,0x44,0x74,0xee,0x45,0x54,0x2e,0xd1,0x04,0xd7,0x5f,0x9e,0x44,0x3b,0xd2,0x53,0xac,0x41,0x05,0x66,
    0xe4,0x03,0x90,0x0e,0x9f,0x02,0x74,0x5f,0x40,0x32,0x34,0x3d,0x99,0x3a,0xf7,0x56,0x53,0x0a,0x15,0x4c,0x82,0xa2,0xf2,0xac,0x74,0xeb,0xc6,0x7f,0x01,0x00,0x00,0x00,
    0x3d,0x76,0x5d,0x57,0x77,0xe3,0xd4,0x87,0x01,0x1c,0xd7,0x14,0x4f,0xc4,0x33,0x0d,0xce,0x03,0xc0,0x8c,0xbd,0x50,0xef,0xfd,0x19,0x8e,0x2c,0x1f,0x80,0x78,0xaa,0xaa,
    0x32,0x1b,0x50,0x45,0x5c,0x2a,0xd7,0x96,0x90,0xfe,0x6d,0x61,0x5f,0xc6,0x72,0xc6,0xb3,0x23,0x1f,0x80,0x15,0x69,0x66,0x5c,0xfb,0x4e,0x42,0xb9,0x45,0x64,0x65,0x63,
    0x8e,0xd6,0x4a,0xa7,0x5d,0xe9,0x99,0x1f,0x38,0xb1,0x6e,0x00,0x66,0x47,0x3f,0x4a,0xef,0xe5,0x26,0x36,0x82,0xb8,0x49,0xc8,0x0a,0x42,0xda,0x48,0xea,0x6a,0x3e,0xcd,
    0xd8,0x73,0x85,0xd3,0x1b,0x5f,0xa4,0x92,0xed,0xdc,0xa8,0x07,0xf9,0x00,0x24,0x4c,0xbf,0xf4,0x46,0x3f,0x27,0xda,0xe0,0xc2,0x1e,0xd2,0x96,0x28,0x6f,0x02,0xba,0x3e,
    0x39,0x4f,0x3d,0xb9,0xc6,0x70,0x1e,0xf8,0x7f,0x39,0x52,0xcd,0x03,0xf2,0x01,0x28,0x2b,0xfd,0xca,0xb1,0xe1,0xc5,0x88,0x4c,0xe4,0xc6,0x52,0x4c,0x6a,0x3e,0x00,0xb9,
    0xd2,0xaf,0x94,0x9b,0xb3,0x44,0xb9,0xa6,0x8a,0x7a,0x90,0x0f,0x40,0xa1,0x29,0xc9,0x1e,0x40,0x3e,0x00,0x05,0xa7,0x26,0xf7,0x2c,0xd6,0x58,0xf9,0x28,0xd6,0x17,0x40,
    0x92,0xfd,0xa1,0x56,0xbc,0x47,0xb5,0xa6,0x50,0x8f,0xbc,0x95,0x00,0xb2,0xd4,0x7e,0x7c,0x37,0xf6,0xb5,0xb2,0xd8,0xb5,0xd9,0x64,0x16,0xce,0xc5,0x1c,0xfa,0xe7,0x1e,
    0x8e,0x35,0x25,0x79,0x8a,0x9d,0xdc,0xae,0xeb,0x9a,0x95,0xaa,0xed,0xe4,0xe4,0x70,0xef,0xf5,0x22,0xd3,0xb6,0x17,0x03,0xed,0x73,0x11,0x21,0x89,0xa6,0x27,0xc4,0xb9,
    0x42,0x5d,0x5f,0x3e,0xb7,0x89,0xd2,0x13,0x74,0x9b,0x18,0xb1,0x91,0x50,0x98,0xa8,0x07,0x96,0xe2,0x3a,0x67,0x5f,0xf8,0x44,0x4f,0x56,0x50,0xd9,0xe4,0x90,0x9b,0x13,
    0x51,0x04,0x40,0xb6,0x97,0x9d,0x5c,0x20,0xbd,0x2b,0xad,0x86,0x45,0xcd,0x07,0x00,0x92,0x46,0x71,0xc8,0x07,0x60,0x43,0x45,0x67,0x40,0x3e,0x00,0x74,0x56,0x63,0x9f,
    0x21,0x57,0x79,0xe5,0xe8,0xc8,0xe7,0xce,0xac,0x1f,0x80,0x59,0x35,0xd1,0x06,0xf1,0x8c,0xc8,0xc7,0x11,0x1e,0x8a,0xae,0xeb,0xee,0xbe,0xa8,0x6c,0x9f,0x51,0x52,0x58,
    0x07,0x04,0xb4,0x8c,0x78,0xfa,0xdd,0x2e,0x35,0xd1,0x16,0x94,0xb6,0xa5,0x7e,0xaf,0x9c,0xe7,0xb4,0x44,0x01,0x7c,0x04,0x64,0xf7,0x90,0xdc,0x48,0x97,0xae,0x59,0xa2,
    0x66,0xe5,0xdb,0x6a,0x97,0xe4,0xed,0x00,0xb3,0x23,0xa0,0xda,0x15,0x65,0xef,0x41,0x3a,0xef,0xe6,0xa4,0x97,0x7a,0x74,0x2c,0x91,0xc1,0x45,0x53,0x31,0x0b,0x90,0x29,
    0xd5,0x5f,0x3d,0xad,0xd7,0xf7,0x21,0xd6,0xea,0xcc,0xf5,0xe5,0x63,0x52,0x8f,0x96,0x65,0xb1,0x6c,0xde,0x0b,0xb0,0xfb,0x56,0xbb,0xb5,0x1f,0x29,0x06,0xe2,0x01,0x58,
    0x36,0xf2,0xf1,0xed,0xfc,0xec,0x84,0xcb,0x5a,0xa1,0x28,0xc0,0x6e,0xe5,0xb3,0x73,0x09,0x29,0x2d,0x9d,0x0b,0xcb,0x02,0x60,0x45,0xf9,0x8c,0x15,0xc7,0xee,0x51,0x38,
    0xbd,0xb4,0x13,0x00,0x16,0xe2,0xbf,0xb0,0xd8,0x77,0x65,
};


#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER ALIGN(4)
#endif
LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER const eZIP_RGBARGB888A  lv_img_dsc_t paring SECTION(".ROM3_IMG_EZIP_HEADER.paring") = { 
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .header.always_zero = 0,
  .header.w = 287,
  .header.h = 123,
  .data_size  = 2763,
  .data = paring_map
};
