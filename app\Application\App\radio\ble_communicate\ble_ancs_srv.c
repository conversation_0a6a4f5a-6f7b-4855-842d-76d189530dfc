/************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ble_ancs_srv.c
@Time    :   2025/08/26 10:00:00
*
************************************************************/
#include <stdbool.h>
#include <stdint.h>
#include <string.h>

#include "nordic_common.h"
#include "nrf.h"
#include "app_error.h"
#include "ble_hci.h"
#include "ble_gap.h"
#include "ble_err.h"
#include "ble_ancs_srv.h"
#include "peer_manager.h"
#include "app_timer.h"
#include "fds.h"
#include "nrf_sdh.h"
#include "nrf_sdh_soc.h"
#include "nrf_sdh_ble.h"
#include "nrf_ble_ancs_c.h"

#include "nrf_log.h"
#include "nrf_log_ctrl.h"
#include "nrf_log_default_backends.h"

#include "ins_service.h"
#include "ins_common.h"
#include "../nanopb/intelligent_notification_service/ancs_service.h"

#define SECURITY_REQUEST_DELAY         APP_TIMER_TICKS(1500)                        /**< Delay after connection until security request is sent, if necessary (ticks). */
#define DEAD_BEEF                      0xDEADBEEF                                   /**< Value used as error code on stack dump. Can be used to identify stack location on stack unwind. */
#define BLE_ANCS_NOTIF_ATTR_ID_APP_IDENTIFIER_MASK   	(1 << 0)
#define BLE_ANCS_NOTIF_ATTR_ID_TITLE_MASK 				(1 << 1)
#define BLE_ANCS_NOTIF_ATTR_ID_MESSAGE_MASK 			(1 << 2)
#define BLE_ANCS_NOTIF_ATTR_ID_DATE_MASK 				(1 << 3)
#define BLE_ANCS_NOTIF_ATTR_MASK						0x0F

BLE_ANCS_C_DEF(m_ancs_c);                                                           /**< Apple Notification Service Client instance. */
static ble_ancs_c_attr_t      m_notif_attr_app_id_latest;                     /**< Local copy of the newest app attribute. */

static uint8_t m_attr_appid[BLE_ANCS_ATTR_DATA_MAX];                                        /**< Buffer to store attribute data. */
static uint8_t m_attr_title[BLE_ANCS_ATTR_DATA_MAX];                                        /**< Buffer to store attribute data. */
static uint8_t m_attr_message[BLE_ANCS_ATTR_MESSAGE_MAX];                                      /**< Buffer to store attribute data. */
static uint8_t m_attr_date[BLE_ANCS_ATTR_DATA_MAX];                                         /**< Buffer to store attribute data. */
static uint8_t m_attr_disp_name[BLE_ANCS_ATTR_DATA_MAX];                                    /**< Buffer to store attribute data. */

static uint8_t  m_ancs_filter_category_id[ANCS_FILTER_CATEGORY_ID_NUM] =
	{ANCS_FILTER_CATEGORY_ID_OTHER,
    ANCS_FILTER_CATEGORY_ID_INCOMING_CALL,
//	ANCS_FILTER_CATEGORY_ID_MISSED_CALL,
	ANCS_FILTER_CATEGORY_ID_VOICE_MAIL,
	ANCS_FILTER_CATEGORY_ID_SOCIAL,
	ANCS_FILTER_CATEGORY_ID_SCHEDULE,
	ANCS_FILTER_CATEGORY_ID_EMAIL,
	ANCS_FILTER_CATEGORY_ID_NEWS,
	ANCS_FILTER_CATEGORY_ID_HEALTH_AND_FITNESS,
	ANCS_FILTER_CATEGORY_ID_BUSINESS_AND_FINANCE,
	ANCS_FILTER_CATEGORY_ID_LOCATION,
	ANCS_FILTER_CATEGORY_ID_ENTERTAINMENT};

static char const * m_ancs_filter_app_identifier[ANCS_FILTER_APP_IDENTIFIER_NUM]= 
{
	ANCS_FILTER_APP_IDENTIFIER_MOBILEPHONE,
	ANCS_FILTER_APP_IDENTIFIER_SMS,	
};


#define TIMER_INTERVAL             APP_TIMER_TICKS(100)

uint8_t g_timer_tick = UINT8_MAX;
uint8_t ancs_rev_complete = true;

APP_TIMER_DEF(ancs_timer_id);

/**@brief Function for setting up GATTC notifications from the Notification Provider.
 *
 * @details This function is called when a successful connection has been established.
 */
static void apple_notification_setup(void)
{
    ret_code_t ret;

    nrf_delay_ms(100); // Delay because we cannot add a CCCD to close to starting encryption. iOS specific.

    ret = ble_ancs_c_notif_source_notif_enable(&m_ancs_c);
    APP_ERROR_CHECK(ret);

    ret = ble_ancs_c_data_source_notif_enable(&m_ancs_c);
    APP_ERROR_CHECK(ret);

    NRF_LOG_DEBUG("Notifications Enabled.");
}

/**@brief Function for handling the Apple Notification Service client.
 *
 * @details This function is called for all events in the Apple Notification client that
 *          are passed to the application.
 *
 * @param[in] p_evt  Event received from the Apple Notification Service client.
 */
static void on_ancs_c_evt(ble_ancs_c_evt_t * p_evt)
{
    ret_code_t ret = NRF_SUCCESS;
    uint8_t i = 0;
    static uint16_t attr_mask = 0x00;

    switch (p_evt->evt_type)
    {
        case BLE_ANCS_C_EVT_DISCOVERY_COMPLETE:
            NRF_LOG_DEBUG("Apple Notification Center Service discovered on the server.");
            ret = nrf_ble_ancs_c_handles_assign(&m_ancs_c, p_evt->conn_handle, &p_evt->service);
            APP_ERROR_CHECK(ret);
            apple_notification_setup();
            break;

        case BLE_ANCS_C_EVT_NOTIF:
            //过滤开机时收到的未接电话和短信
            if (p_evt->notif.evt_flags.pre_existing == 1)
            {
                break;
            }

            for (i = 0; i < ANCS_FILTER_CATEGORY_ID_NUM; i++)    
            {
                if (m_ancs_filter_category_id[i] == p_evt->notif.category_id)
                {
                    if (ancs_catagory_is_filter((ANCS_CATAGORY_ID)m_ancs_filter_category_id[i]))
                    {
                        uint16_t ins_index = g_ins_data_t.index;

                        if (BLE_ANCS_EVENT_ID_NOTIFICATION_REMOVED == p_evt->notif.evt_id)
                        {
                        	if (BLE_ANCS_CATEGORY_ID_INCOMING_CALL == p_evt->notif.category_id)
                        	{
                                memset ((void *)&g_ins_data_t, 0, sizeof(ins_data_t));
                                g_ins_data_t.index = ins_index;
                                g_ins_data_t.category_id = p_evt->notif.category_id;
                                g_ins_data_t.even_id = p_evt->notif.evt_id;
                                g_ins_data_t.uid = p_evt->notif.notif_uid;
                                attr_mask = 0;

                        		//手机端处理了来电：接通或拒接
								ANCS_deal_interface(p_evt->app_id);
                        	}
                        }
                        else
                        {
                        	if (ancs_rev_complete == false)
                        	{
                        		break;
                        	}

                            memset ((void *)&g_ins_data_t, 0, sizeof(ins_data_t));
                            g_ins_data_t.index = ins_index;
                            g_ins_data_t.category_id = p_evt->notif.category_id;
                            g_ins_data_t.even_id = p_evt->notif.evt_id;
                            g_ins_data_t.uid = p_evt->notif.notif_uid;
                            attr_mask = 0;

                            ret = nrf_ble_ancs_c_request_attrs(&m_ancs_c, &p_evt->notif);
                            if (NRF_SUCCESS != ret)
                            {
                                break;
                            }
                            ancs_rev_complete = false;
                        }
                        break;
                    }
                }
            }      
            break;

        case BLE_ANCS_C_EVT_NOTIF_ATTRIBUTE:
            if (BLE_ANCS_NOTIF_ATTR_ID_APP_IDENTIFIER == p_evt->attr.attr_id)
            {
#if ANCS_FILTER_APP_ENABLED
				for (i = 0; i < ANCS_FILTER_APP_IDENTIFIER_NUM; i++)
				{
					if (strcmp((char *)(p_evt->attr.p_attr_data), m_ancs_filter_app_identifier[i]))
					{
						memset ((void *)&g_ins_data_t, 0, sizeof(ins_data_t));
						return;
					}
				}
#endif
				if (!strcmp((char *)(p_evt->attr.p_attr_data), ANCS_FILTER_APP_IDENTIFIER_SMS))
				{
					g_ins_data_t.msg_type = enumMSG_NOTE_TYPE;
				}
				else if (!strcmp((char *)(p_evt->attr.p_attr_data), ANCS_FILTER_APP_IDENTIFIER_MOBILEPHONE))
				{
					g_ins_data_t.msg_type = enumMSG_INCOMING_TYPE;
				}
				else
				{
					g_ins_data_t.msg_type = enumMSG_APP_TYPE;
				}
            }

			if (p_evt->attr.attr_id == BLE_ANCS_NOTIF_ATTR_ID_APP_IDENTIFIER)
			{
				m_notif_attr_app_id_latest = p_evt->attr;
			}

            switch (p_evt->attr.attr_id)
			{
				case BLE_ANCS_NOTIF_ATTR_ID_APP_IDENTIFIER:
					attr_mask |= BLE_ANCS_NOTIF_ATTR_ID_APP_IDENTIFIER_MASK;
					break;
				case BLE_ANCS_NOTIF_ATTR_ID_TITLE:
					attr_mask |= BLE_ANCS_NOTIF_ATTR_ID_TITLE_MASK;
					memcpy (g_ins_data_t.title, p_evt->attr.p_attr_data, p_evt->attr.attr_len);
					break;
				case BLE_ANCS_NOTIF_ATTR_ID_MESSAGE:
					attr_mask |= BLE_ANCS_NOTIF_ATTR_ID_MESSAGE_MASK;
					memcpy (g_ins_data_t.message, p_evt->attr.p_attr_data, p_evt->attr.attr_len);
					break;
				case BLE_ANCS_NOTIF_ATTR_ID_DATE:
					attr_mask |= BLE_ANCS_NOTIF_ATTR_ID_DATE_MASK;
					memcpy (g_ins_data_t.date, p_evt->attr.p_attr_data, p_evt->attr.attr_len);
					break;
				default:
					break;
			}

            if (m_notif_attr_app_id_latest.attr_len != 0 && BLE_ANCS_NOTIF_ATTR_MASK == attr_mask)
			{
            	g_timer_tick = 0;
			}
            break;
        case BLE_ANCS_C_EVT_DISCOVERY_FAILED:
            NRF_LOG_DEBUG("Apple Notification Center Service not discovered on the server.");
            break;

        case BLE_ANCS_C_EVT_APP_ATTRIBUTE:
			memcpy (g_ins_data_t.app_name, p_evt->attr.p_attr_data, p_evt->attr.attr_len);
			ANCS_deal_interface(p_evt->app_id);
            break;
        case BLE_ANCS_C_EVT_NP_ERROR:
            break;
        default:
            // No implementation needed.
            break;
    }
}

/**@brief Function for handling the Apple Notification Service client errors.
 *
 * @param[in] nrf_error  Error code containing information about what went wrong.
 */
static void apple_notification_error_handler(uint32_t nrf_error)
{
    APP_ERROR_HANDLER(nrf_error);
}

//-------------------------------------------------------------------------------------------
// Function Name : db_disc_ancs_handle
// Purpose       : 搜索ANCS服务
// Param[in]     : ble_db_discovery_evt_t * p_evt  
// Param[out]    : None
// Return type   : 
// Comment       : 2019-05-11
//-------------------------------------------------------------------------------------------
void db_disc_ancs_handle(ble_db_discovery_evt_t * p_evt)
{
	if (p_evt->params.discovered_db.srv_uuid.uuid == ANCS_UUID_SERVICE)
	{
	    ble_ancs_c_on_db_disc_evt(&m_ancs_c, p_evt);
	}
}

//-------------------------------------------------------------------------------------------
// Function Name : ancs_conn_handle_invalid_set
// Purpose       : 设置ancs connect handle无效
// Param[in]     : uint16_t conn_handle  
// Param[out]    : None
// Return type   : 
// Comment       : 2019-05-11
//-------------------------------------------------------------------------------------------
void ancs_conn_handle_invalid_set(uint16_t conn_handle)
{
    if (conn_handle == m_ancs_c.conn_handle)
    {
        m_ancs_c.conn_handle = BLE_CONN_HANDLE_INVALID;
    }
}

static void timeout_handler(void* p_context)
{
	if (g_timer_tick != UINT8_MAX)
	{
		g_timer_tick++;
		if (g_timer_tick == 1)		// 1 * TIMER_INTERVAL
		{
			nrf_ble_ancs_c_app_attr_request(&m_ancs_c, m_notif_attr_app_id_latest.p_attr_data, m_notif_attr_app_id_latest.attr_len);
		}
		else if (g_timer_tick > 2)		// 2 * TIMER_INTERVAL
		{
			ancs_rev_complete = true;
			g_timer_tick = UINT8_MAX;
		}
	}
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_ancs_service_init
// Purpose       : ble ancs服务初始化函数
// Param[in]     : void  
// Param[out]    : None
// Return type   : 
// Comment       : 2019-05-11
//-------------------------------------------------------------------------------------------
void ble_ancs_service_init(void)
{
    ble_ancs_c_init_t ancs_init_obj;
    ret_code_t        ret;

    memset(&ancs_init_obj, 0, sizeof(ancs_init_obj));

    ret = nrf_ble_ancs_c_attr_add(&m_ancs_c,
                                  BLE_ANCS_NOTIF_ATTR_ID_APP_IDENTIFIER,
                                  m_attr_appid,
                                  BLE_ANCS_ATTR_DATA_MAX);
    APP_ERROR_CHECK(ret);

    ret = nrf_ble_ancs_c_app_attr_add(&m_ancs_c,
                                      BLE_ANCS_APP_ATTR_ID_DISPLAY_NAME,
                                      m_attr_disp_name,
                                      sizeof(m_attr_disp_name));
    APP_ERROR_CHECK(ret);

    ret = nrf_ble_ancs_c_attr_add(&m_ancs_c,
                                  BLE_ANCS_NOTIF_ATTR_ID_TITLE,
                                  m_attr_title,
                                  BLE_ANCS_ATTR_DATA_MAX);
    APP_ERROR_CHECK(ret);

    ret = nrf_ble_ancs_c_attr_add(&m_ancs_c,
                                  BLE_ANCS_NOTIF_ATTR_ID_MESSAGE,
                                  m_attr_message,
								  BLE_ANCS_ATTR_MESSAGE_MAX);
    APP_ERROR_CHECK(ret);

    ret = nrf_ble_ancs_c_attr_add(&m_ancs_c,
                                  BLE_ANCS_NOTIF_ATTR_ID_DATE,
                                  m_attr_date,
                                  BLE_ANCS_ATTR_DATA_MAX);
    APP_ERROR_CHECK(ret);
    
    ancs_init_obj.evt_handler   = on_ancs_c_evt;
    ancs_init_obj.error_handler = apple_notification_error_handler;
    
    ret = ble_ancs_c_init(&m_ancs_c, &ancs_init_obj);
    APP_ERROR_CHECK(ret);

    ret = app_timer_create(&ancs_timer_id, APP_TIMER_MODE_REPEATED, timeout_handler);
	APP_ERROR_CHECK(ret);

}

void ble_ancs_service_timer_start(void)
{
    ret_code_t ret;

    ret = app_timer_start(ancs_timer_id, TIMER_INTERVAL, NULL, "ble_ancs_srv");
    APP_ERROR_CHECK(ret);
}

void ble_ancs_service_timer_stop(void)
{
    ret_code_t ret;

    ret = app_timer_stop(ancs_timer_id);
    APP_ERROR_CHECK(ret);
}
