#include "lvgl.h" 

#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP ALIGN(4)
#endif

#ifndef eZIP_RGBARGB888A
#define eZIP_RGBARGB888A
#endif


LV_ATTRIBUTE_MEM_ALIGN_EZIP const  uint8_t sys_set_focus_mode_map[] SECTION(".ROM3_IMG_EZIP.sys_set_focus_mode") = { 
    0x00,0x00,0x05,0xdf,0x46,0x08,0x20,0x00,0x00,0x48,0x00,0x48,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0x03,0x00,0x00,0x00,0x94,0x00,0x00,0x03,0x04,0x00,0x00,0x04,0xfc,
    0x3d,0x73,0x8b,0x8d,0xaa,0xea,0xe2,0xf8,0x5a,0x7b,0x86,0x8f,0xe4,0x53,0x7c,0x21,0x0e,0x0d,0x0f,0x60,0xa4,0x4f,0xc5,0x86,0x12,0x82,0xa1,0x81,0xcc,0x85,0x56,0x5e,
    0x14,0x4c,0x34,0x30,0x4c,0x2f,0x09,0x44,0x45,0x6c,0x62,0xd0,0x17,0x63,0x14,0x68,0x8b,0xc4,0xbe,0xf8,0x42,0x8c,0x37,0x4c,0x6c,0x13,0x98,0x19,0xa8,0xbe,0x58,0x12,
    0x1e,0x6c,0x9b,0x99,0x29,0x8c,0x4a,0xa4,0xa1,0xb1,0x4d,0xe5,0x01,0x0c,0xf0,0x40,0xe8,0x68,0x42,0x62,0x89,0x95,0xb4,0xcc,0x72,0x9d,0x99,0x1e,0x3a,0xb5,0x73,0x9f,
    0xb3,0xcf,0xad,0xb3,0x92,0x3d,0x67,0x77,0x66,0xba,0xcf,0x99,0xdf,0xfe,0xaf,0xb5,0xf6,0xda,0x1b,0x00,0x44,0x30,0x91,0x91,0x3f,0xbe,0x01,0x1c,0x73,0x9b,0xb8,0x5b,
    0x07,0x04,0xb5,0x80,0xb0,0x8e,0x5b,0x0d,0xf7,0x57,0xf3,0x7b,0x2e,0xf5,0x6b,0xdc,0x1e,0x72,0xbb,0xc7,0xbd,0xbb,0x80,0x78,0x13,0xe8,0xd1,0x24,0x00,0x8e,0xc2,0x83,
    0xe9,0x38,0x5e,0xd8,0xfd,0xb7,0x96,0xcf,0x64,0x28,0x20,0x6a,0x8b,0xd4,0x02,0xe1,0x2e,0x00,0xe1,0x83,0x24,0xed,0x48,0xc1,0xa8,0xfc,0x27,0x0d,0x01,0x25,0x07,0x00,
    0xe6,0xfa,0x31,0xdc,0x3c,0x65,0x39,0x40,0xb4,0x3f,0xf2,0x0c,0x08,0xe1,0xe7,0xee,0xab,0xdc,0xb6,0xca,0xbd,0x1b,0x9e,0x01,0x14,0x9f,0x63,0x70,0xc7,0xcf,0xa6,0x07,
    0x44,0xfb,0x47,0x5e,0x04,0x41,0x6f,0x70,0xf7,0x65,0x03,0xc4,0x7a,0x9e,0x5d,0xf1,0x04,0x06,0xdd,0x93,0xa6,0x03,0x44,0x81,0x68,0x80,0x1f,0xee,0x1d,0xee,0x3e,0x6f,
    0x82,0x30,0x77,0x0c,0x43,0x9e,0x93,0xa6,0x00,0x44,0xad,0x91,0x97,0x80,0xc4,0x07,0xdc,0x6d,0x04,0x73,0xd9,0x30,0xc0,0xec,0x21,0x0c,0x35,0xff,0x6e,0x08,0x20,0x6a,
    0xb9,0xf4,0x2c,0x40,0xf2,0x63,0xee,0xfa,0x4d,0x06,0x26,0xd3,0x12,0x40,0xd4,0x86,0x61,0xef,0xa0,0xae,0x80,0xd8,0x9d,0xde,0x66,0x77,0xfa,0x84,0xbb,0xff,0x03,0x2b,
    0x18,0x81,0x1f,0xc3,0x9e,0x7e,0xe9,0x80,0x68,0xef,0xc8,0xd3,0xb0,0x82,0xbe,0x98,0xcf,0x4c,0xd6,0xb2,0x02,0x90,0x2a,0x06,0x44,0x81,0xd8,0x4e,0x40,0xfa,0x86,0x87,
    0x5a,0x0f,0x56,0x35,0xa2,0x5d,0xb9,0xdc,0xad,0x22,0x40,0x14,0x18,0x79,0x9d,0xe1,0x7c,0x0d,0xd6,0xb7,0x04,0x07,0xee,0xc6,0x6c,0x81,0x5b,0x54,0xa0,0x9c,0x0f,0x6d,
    0x02,0x07,0xd2,0x65,0xcc,0x8a,0xd3,0xd9,0x3e,0x10,0x65,0xc2,0xe9,0x61,0xed,0x9d,0xb4,0x09,0x1c,0xd5,0x9a,0xa8,0x25,0x76,0xb4,0x62,0x17,0x9b,0x87,0xf3,0x3e,0xd8,
    0xd5,0x10,0x37,0x66,0xae,0xb8,0x45,0xe9,0x6e,0x65,0x63,0x38,0xe9,0x80,0x7d,0xbc,0x2c,0x05,0xd9,0x28,0x20,0x17,0xa1,0x22,0x47,0xa3,0x5a,0xe0,0x8a,0x12,0x52,0xf9,
    0xf2,0x80,0x93,0xfa,0xc1,0xc9,0x8e,0xa2,0x15,0x94,0x5e,0x04,0x26,0x7f,0xd1,0x63,0x9d,0xd3,0x35,0xde,0x0b,0xb1,0xc4,0x18,0x78,0x5c,0x0d,0xe9,0xbf,0xeb,0x0f,0x1a,
    0x08,0x69,0xb6,0x46,0xd9,0x4f,0x72,0x16,0xfc,0x62,0x6a,0x85,0x2c,0x1f,0x8e,0x6f,0xf8,0x08,0x44,0xa7,0xc6,0x52,0x7d,0xf5,0xaa,0xc0,0x8a,0x34,0x9d,0x32,0x88,0x90,
    0x73,0x1f,0xbf,0x7c,0x2a,0x0a,0xd6,0x56,0x3a,0x94,0x0f,0x8a,0x72,0x54,0x28,0x99,0xa6,0xbc,0xa7,0x7c,0x66,0x4c,0x1c,0x12,0x7b,0xf2,0xba,0xd8,0x7c,0x55,0xfe,0x9b,
    0xec,0xc2,0x53,0x81,0xa0,0xa8,0x27,0x9f,0x75,0xd6,0x1f,0x00,0xaf,0x6b,0x33,0x78,0xd7,0x34,0xe8,0x0b,0x69,0xfa,0xaf,0x27,0xf2,0x00,0x8a,0x9d,0x93,0xb9,0x65,0xa1,
    0x80,0xe9,0x9e,0xc8,0xae,0x9c,0x7c,0xa0,0x74,0x8d,0x4d,0x5c,0xa3,0x39,0x73,0x6f,0x76,0xc9,0x81,0x53,0x0e,0x18,0xd5,0xba,0xc7,0xfb,0x16,0xdc,0x52,0x17,0x48,0xb4,
    0x25,0x7b,0x90,0x4e,0xef,0x04,0x1a,0xe2,0x4e,0xa5,0x80,0x92,0x0e,0x09,0x1d,0x75,0x98,0x63,0x0f,0x39,0x64,0x56,0x38,0x99,0xa6,0xc4,0x24,0xa9,0x59,0x8e,0xe0,0xc7,
    0xa5,0x59,0x2c,0xbd,0xc1,0x2e,0x25,0x8d,0x5b,0x01,0xfa,0x7f,0x2a,0xd5,0xb5,0x62,0xc9,0xd1,0x8c,0x84,0xd3,0x07,0x99,0xa9,0x5a,0x81,0x54,0x4e,0x3c,0x2b,0xd2,0x6a,
    0x16,0x2b,0x28,0x7d,0x6e,0xa5,0xb9,0x65,0x06,0x57,0x29,0xe3,0x4f,0x48,0x9b,0x80,0x95,0x62,0xd1,0x89,0xa7,0x84,0x43,0x3d,0x3d,0x16,0x7a,0x12,0x55,0x84,0x0b,0x0a,
    0x42,0xb1,0xdf,0x8a,0xea,0x79,0x0c,0x29,0x71,0x4d,0xca,0xb8,0x19,0x80,0xe0,0x15,0x2b,0x17,0xe0,0x4a,0xdd,0x26,0x0d,0x10,0xb5,0x45,0x6a,0xf9,0xb2,0x55,0x86,0xf4,
    0xad,0x6e,0xff,0x02,0x44,0x60,0xa3,0xd6,0x4b,0x1d,0x40,0xc9,0xcf,0x40,0x63,0xeb,0x1a,0xef,0x85,0xee,0xf1,0x3e,0xd0,0xcb,0xa8,0x25,0xa6,0xf9,0x98,0x22,0x3d,0x72,
    0x72,0x27,0xd8,0xc0,0xa2,0x53,0x63,0x92,0x00,0x21,0x6c,0x87,0xaa,0x65,0x07,0x44,0xfe,0xf8,0x06,0x20,0xa8,0xa9,0xa2,0xc8,0xa5,0x20,0xc7,0xdc,0xa6,0x2a,0x86,0x9c,
    0x36,0xa3,0xb8,0x58,0x5d,0x95,0x43,0x4e,0xbb,0x2f,0xd8,0xbd,0x6a,0xab,0x1c,0x72,0xa5,0x45,0xb8,0x25,0x38,0x40,0xaf,0xab,0x92,0xc8,0x49,0xe8,0xb6,0x02,0x48,0x5a,
    0x80,0x8e,0x25,0xc6,0xac,0xcd,0x07,0xe1,0x86,0xe2,0x62,0xab,0x65,0x8c,0xed,0x1b,0x3e,0x22,0x65,0x5d,0x92,0xcf,0xba,0x27,0x7a,0x35,0x06,0x84,0xd7,0x95,0x20,0xed,
    0x92,0xb1,0x82,0xd6,0x1b,0x8e,0xba,0x50,0x54,0xee,0xad,0xa1,0x8d,0x0b,0x19,0x0f,0xa9,0x67,0x79,0xb1,0x44,0x45,0x7c,0x6f,0x8d,0x26,0x67,0x06,0x83,0x9e,0x14,0x20,
    0xd2,0x14,0x50,0xe2,0x9a,0x4d,0xe2,0x33,0x5d,0x51,0x4b,0x8d,0x87,0x76,0x0b,0xcc,0xda,0xc4,0x22,0x8c,0xab,0x80,0xee,0x55,0xd3,0x79,0x36,0x3e,0xc9,0x68,0x1a,0x10,
    0xc1,0x5d,0xbb,0xfd,0x36,0x8f,0xab,0xa1,0x72,0x3e,0x21,0xdf,0x50,0x1a,0x10,0xe2,0x4d,0x2d,0x1f,0xae,0xf3,0xb9,0x83,0x36,0x50,0x0f,0xf4,0x2f,0x14,0xab,0xf4,0x68,
    0x52,0xcb,0xb1,0xbd,0x6b,0x1a,0x52,0xcd,0x48,0xeb,0xaa,0xaf,0x70,0x92,0x08,0x06,0x1e,0xb3,0xa2,0x40,0xf4,0x05,0x56,0xd1,0x0f,0x32,0xd6,0x42,0x7a,0x9b,0xd7,0xb5,
    0x59,0x8b,0xc9,0xb9,0x83,0x21,0xcf,0x7a,0xf5,0x0f,0x27,0x3c,0x98,0x8e,0xc3,0xaa,0xa7,0xcc,0x37,0x8b,0x86,0xb9,0x17,0xf6,0x2c,0xf6,0xb6,0xd4,0x5e,0xee,0xc8,0x20,
    0xbf,0x36,0x9b,0x75,0xab,0x54,0x3f,0x97,0xa5,0xab,0x18,0xf2,0x6e,0xcd,0x7c,0xc7,0x39,0xbf,0x27,0x3d,0xc0,0xe4,0x0c,0x03,0xa4,0xd4,0x6d,0x7a,0x6f,0xc6,0xe7,0x90,
    0x4f,0x67,0xf6,0x3d,0x69,0x98,0xeb,0x37,0x52,0xd5,0xf9,0x14,0xd2,0x59,0x7f,0x40,0x2f,0xf5,0xf4,0x71,0xec,0xb9,0x98,0x15,0x10,0x86,0x9b,0xa7,0xf8,0xf5,0x8c,0x51,
    0x80,0xf2,0x2d,0x0d,0x74,0x8a,0x65,0x7f,0xc0,0x4a,0xf1,0x5e,0xf6,0x8c,0xaf,0xf2,0x6b,0xbd,0xbc,0x8d,0x53,0xfe,0x4f,0x86,0xa6,0x67,0xce,0x7c,0x99,0xa5,0x4a,0xa4,
    0xe9,0x94,0x4e,0xe2,0x49,0xb6,0x63,0xd8,0x77,0x36,0x2f,0xa0,0x79,0x5f,0x3f,0xc7,0x17,0xff,0xf2,0x2a,0x29,0xe0,0x2b,0xae,0xda,0x0f,0xe7,0x3f,0x17,0x5b,0x48,0x71,
    0x27,0x96,0x59,0xc5,0x35,0x0a,0x41,0xf7,0x5b,0x85,0x0f,0x0e,0x55,0x3e,0x41,0xb7,0xb2,0xaa,0x3e,0xb6,0x7c,0x0e,0xbd,0xc4,0x6b,0xc8,0x6b,0xe5,0xfc,0x02,0xcb,0x9e,
    0x56,0x95,0x42,0xad,0xc9,0xde,0xae,0x85,0x7b,0x59,0x10,0xdf,0x15,0x77,0xf4,0xbc,0xc4,0x66,0x0f,0xf1,0x4b,0xc2,0xc6,0x70,0xde,0x2d,0x06,0x4e,0x4e,0x05,0xa5,0x54,
    0x24,0xa9,0x46,0xb3,0x8a,0x72,0x0a,0x28,0x48,0x59,0x1b,0x79,0x07,0xb9,0xaa,0xb5,0x51,0x46,0xc3,0x51,0x8e,0x39,0x0d,0xa5,0xc0,0x51,0xec,0x5f,0x45,0x28,0x60,0x14,
    0x88,0xed,0x03,0x84,0xf3,0x60,0x6d,0x3b,0x0d,0x21,0xf7,0x61,0x04,0xa4,0x52,0xff,0x51,0x14,0xfa,0x02,0x86,0x3d,0xfd,0x40,0xb4,0x8b,0xbb,0x09,0x0b,0x82,0xf9,0x13,
    0x28,0xd9,0x8e,0x21,0xcf,0x9b,0xe5,0xc0,0x29,0x0a,0x50,0x1a,0x92,0x77,0x10,0x60,0xb6,0x91,0xbb,0xc3,0xd6,0x61,0x43,0x7d,0xe0,0xf8,0x67,0x23,0x86,0x7d,0x67,0x2b,
    0x19,0x05,0x4b,0xbe,0x6d,0x4b,0xec,0x28,0x5f,0x3e,0x32,0x31,0x99,0xab,0xdc,0x3a,0x59,0x35,0x17,0xb5,0x18,0x0c,0xcb,0x9a,0x9b,0xd6,0x91,0x3a,0x76,0xbb,0xe3,0xdc,
    0xf5,0x9b,0x08,0xcc,0x1d,0x40,0xec,0xc1,0xa0,0xfb,0x4b,0x2d,0x07,0xc5,0x8a,0x44,0xdc,0x7a,0x79,0x1b,0xfb,0x78,0x07,0xf7,0xda,0x0d,0x74,0xa5,0x6f,0xf9,0x67,0x7c,
    0xcf,0x8a,0x09,0xca,0x18,0x1d,0x35,0x79,0xc4,0xc0,0xd0,0x1a,0x00,0x27,0x67,0x3b,0xb1,0x87,0xff,0x6a,0x96,0x48,0x63,0x06,0x08,0xaf,0x80,0xa0,0x38,0x4f,0x4c,0x14,
    0x43,0xbe,0x21,0xd9,0xf8,0x51,0xf3,0xf9,0xdc,0x7d,0xe1,0xff,0xf0,0xe4,0xaa,0xed,0xdc,0xdb,0x02,0xe8,0x50,0x5c,0x71,0x03,0xdf,0x65,0x2d,0x7f,0x54,0xc3,0x6d,0x65,
    0x11,0xf7,0x9c,0xe1,0x76,0x1f,0x08,0x6e,0x81,0xc0,0xdb,0x7c,0xbd,0xc1,0x09,0xe8,0x3a,0x24,0x71,0x02,0xc3,0xee,0x5f,0xf5,0xd6,0xe7,0xbf,0x94,0xfb,0x11,0x68,
};


#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER ALIGN(4)
#endif
LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER const eZIP_RGBARGB888A  lv_img_dsc_t sys_set_focus_mode SECTION(".ROM3_IMG_EZIP_HEADER.sys_set_focus_mode") = { 
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .header.always_zero = 0,
  .header.w = 72,
  .header.h = 72,
  .data_size  = 1503,
  .data = sys_set_focus_mode_map
};
