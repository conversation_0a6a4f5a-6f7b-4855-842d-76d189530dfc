#ifndef NAVI_TNAV_H
#define NAVI_TNAV_H

#ifdef __cplusplus
extern "C" {
#endif

#include "navi_common.h"
#include "navi_waypoint.h"
#include "qw_fs.h"

//用于读取tnav文件
typedef struct _TnavReader
{
    QW_FIL *fp;
} TnavReader;

//用于写入tnav文件
typedef struct _TnavWriter
{
    QW_FIL *fp;
} TnavWriter;

//转向分段
typedef struct _NaviTurnSegment
{
    Dseg dseg;
    Range range;
} NaviTurnSegment;

//转向分段数组
typedef struct _NaviTurnSegmentArray
{
    NaviTurnSegment *segments;              //缓冲区，保存转向分段
    uint32_t len;                           //转向分段数量
} NaviTurnSegmentArray;

//转向
typedef struct _NaviTurn
{
    float start_dist;                       //起点距离（m），从路书起点算起
    float end_dist;                         //终点距离（m），从路书起点算起
    float angle;                            //转向角度（°）
    float dist;                             //转向的距离/长度（m）
    char wayname[NAVI_TURN_WAYNAME_LEN];    //转向路名（正向）
    char wayname2[NAVI_TURN_WAYNAME_LEN];   //转向路名（反向）
} NaviTurn;

//转向缓冲区
typedef struct _NaviTurnBuf
{
    NaviTurn *buf;                          //缓冲区，保存转向
    Range range;                            //缓冲区中转向的索引范围
    uint32_t capacity;                      //缓冲区容量
} NaviTurnBuf;

//转向缓存
typedef struct _NaviTurnCache
{
    NaviTurnBuf *turn_buf;                  //转向缓冲区，可以有多个
    TnavReader *tnav_reader;                //用于读取tnav文件，加载转向
    uint32_t capacity;                      //转向缓冲区容量/数量
    uint32_t len;                           //已启用的转向缓冲区数量
    uint32_t next;                          //下一个要使用的转向缓冲区
} NaviTurnCache;

//转向列表，一个抽象列表，用于获取任一转向
typedef struct _NaviTurnList
{
    NaviTurnCache cache;
    uint32_t len;                           //转向总数
} NaviTurnList;

int tnav_header_read(TnavReader *self, uint8_t *header);

int tnav_turn_segment_array_read(TnavReader *self, NaviTurnSegmentArray *seg_array);

int tnav_turn_num_read(TnavReader *self, uint32_t *turn_num);

int tnav_turn_data_read(TnavReader *self, uint32_t start, uint32_t end, uint32_t turn_num, NaviTurn *turn_buf);

int tnav_header_placeholder_write(TnavWriter *self);

int tnav_header_write(TnavWriter *self);

int tnav_turn_segment_array_write(TnavWriter *self, const NaviTurnSegmentArray *seg_array);

int tnav_turn_num_write(TnavWriter *self, uint32_t num);

int tnav_turn_data_write(TnavWriter *self, const NaviTurn *turn);

void navi_turn_copy(NaviTurn *self, const NaviTurn *turn);

int navi_turn_list_get(NaviTurnList *self, uint32_t idx, NaviTurn *output);

void navi_turn_buf_reset(NaviTurnBuf *self);

void navi_turn_cache_reset(NaviTurnCache *self);

void navi_turn_list_reset(NaviTurnList *self);

#ifdef __cplusplus
}
#endif

#endif