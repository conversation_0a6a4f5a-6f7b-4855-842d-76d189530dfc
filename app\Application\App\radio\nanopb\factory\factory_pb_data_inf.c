/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   factory_pb_data_inf.c
@Time    :   2025/03/25 15:24:09
<AUTHOR>   lxin
*
**************************************************************************/
#include "factory_pb_data_inf.h"
#include "cfg_header_def.h"
#include "voltmeter.h"
#include "altitude/alg_altitude.h"
#include "acc_api.h"
#include "basic_app_module/ant_ble_sensor_ctl.h"
#include "basic_app_module/basic_app.h"
#include "ble_data_inf.h"

// extern uint32_t ubloc_gnss_mode_get(void);
// extern void ubloc_gnss_mode_config(uint32_t mode);

uint32_t factory_pb_get_power_precent(void)
{
	return voltmeter_cur_get();
}

uint32_t factory_pb_get_power_vol(void)
{
	return battery_voltage_cur_get();
}

int32_t factory_gps_status_get(void)
{
	return true;
}

int32_t factory_sensor_alt_altitude_get(void)
{
	return sensor_alt_altitude_get();
}

int32_t factory_sensor_alt_temperature_get(void)
{
	return sensor_alt_temperature_get();
}

bool factory_acc_is_ok(void)
{
	uint8_t id = 0;
	return acc_id_read(&id);
}

bool factory_ant_sensor_is_ok(void)
{
	return ant_ble_sensor_is_connect(SENSOR_TYPE_HRM);
}

uint32_t factory_gps_workmode_get(void)
{
	return 0;//ubloc_gnss_mode_get();
}

uint8_t* factory_get_product_sn(void)
{
	static char product_sn[PRODUCT_SN_LENGHT] = {0};
    #ifdef USE_SN_OLD
	sprintf(product_sn, "%X%08X", get_serial_number_high(), get_serial_number_low());
    #else
    snprintf(product_sn, 11, "%s", factory_data_get_sn());
    #endif
	return (uint8_t *)&product_sn[0];
}

void factory_sn_updated(uint64_t sn_num)
{
    set_serial_number((sn_num % 4294967296), (sn_num / 4294967296));
	g_device_set_operate_event(BLE_OPERATE_SN_UPDATE);
}

void factory_gps_workmode_set(uint32_t mode)
{
	// ubloc_gnss_mode_config(mode);
}

void factory_system_enter_check_mode(void)
{
	device_factory_creat_app();
	poweroff_reset_app(true);
}

void factory_system_filesystem_format(void)
{
	delete_all_app();
}

void factory_system_params_format(void)
{
	device_reset_app();
}

void factory_system_facory_reset(void)
{
	delete_all_app();
}

void factory_system_enable_temperatue_log(uint8_t sta)
{
//null
}

void factory_system_enable_voltage_log(uint8_t sta)
{
//null
}

void factory_system_enable_ant_log(uint8_t sta)
{
//null
}

void factory_simulate_ride_files(uint32_t num,uint32_t size)
{
//null
}


