#include "lvgl.h" 

#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP ALIGN(4)
#endif

#ifndef eZIP_RGBARGB888A
#define eZIP_RGBARGB888A
#endif


LV_ATTRIBUTE_MEM_ALIGN_EZIP const  uint8_t music_stylus_img_map[] SECTION(".ROM3_IMG_EZIP.music_stylus_img") = { 
    0x00,0x00,0x10,0xc2,0x46,0x08,0x20,0x00,0x00,0x44,0x00,0xe3,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0x08,0x00,0x00,0x00,0xb4,0x00,0x00,0x04,0x48,0x00,0x00,0x09,0xac,
    0x00,0x00,0x0b,0xc8,0x00,0x00,0x0c,0x20,0x00,0x00,0x0c,0x78,0x00,0x00,0x0d,0x6c,0x00,0x00,0x10,0x7c,0x3d,0x33,0x01,0x8e,0xe2,0x3a,0xf3,0xf8,0xeb,0x6b,0x34,0x3a,
    0xd0,0x48,0x02,0x49,0xa0,0x83,0x43,0xe2,0x1a,0x89,0x04,0x11,0xc7,0x31,0x8b,0x8c,0x17,0x93,0x5d,0xdb,0xac,0x6d,0x50,0x11,0x8a,0x5d,0xc0,0x95,0x6c,0xe1,0x5d,0x27,
    0x76,0x85,0x2d,0x6f,0x36,0x71,0x88,0x6d,0x16,0x53,0x6b,0x3b,0x2e,0x5b,0x10,0x27,0xac,0x8b,0x32,0xa9,0x72,0x2a,0x76,0x9c,0x50,0xc0,0x12,0xd6,0xb1,0x1d,0x8c,0x63,
    0x2f,0x16,0x96,0x8d,0xd7,0x09,0x20,0x02,0x16,0x12,0x08,0x10,0x9a,0x91,0x84,0x24,0x24,0x81,0xa4,0xb9,0xfa,0xca,0xf7,0x26,0x23,0xb9,0xa7,0xd5,0x3d,0xd3,0xd3,0x3d,
    0x9a,0x43,0x7e,0x5f,0x55,0x57,0x6b,0x5e,0xbf,0x7e,0xc7,0xef,0xfd,0xbf,0xef,0x7d,0xaf,0x05,0x00,0x00,0xa4,0x50,0x82,0xec,0xb3,0xab,0xdd,0x4f,0x50,0x14,0xb5,0x1d,
    0xfe,0xe4,0x62,0x7a,0x51,0x46,0x8d,0x32,0x6b,0xdb,0x54,0x55,0x92,0xdf,0x9e,0x88,0x71,0xd2,0x89,0xe8,0xe4,0xdc,0x15,0xf7,0x12,0x80,0xf1,0x74,0xcc,0x30,0xb0,0x51,
    0xa8,0x96,0x12,0x03,0xcf,0x26,0x6a,0xe1,0x12,0x02,0x84,0x66,0x68,0x87,0x95,0xf7,0x41,0xc6,0xd3,0x27,0x15,0x90,0x74,0x32,0x02,0x84,0x00,0x21,0x40,0x08,0x10,0x02,
    0x24,0xdd,0x80,0x30,0x34,0xcd,0x5a,0x6b,0x81,0xa2,0x65,0x59,0x66,0xd2,0x1a,0x08,0x4c,0x80,0x83,0xcb,0x0e,0x57,0x16,0x45,0x21,0xab,0x93,0xc1,0x19,0x75,0x06,0x6e,
    0x2b,0xd4,0xe6,0x84,0xc1,0x61,0xe3,0x0d,0xc1,0x54,0x36,0x1a,0xfb,0x22,0x62,0x38,0xf8,0x6f,0x09,0x32,0x60,0x5f,0xca,0x29,0x24,0xa4,0x86,0x44,0xc0,0x18,0x37,0xfe,
    0x90,0x6a,0xb8,0x94,0x51,0x48,0x92,0x40,0xa8,0x6d,0x74,0x41,0x78,0x50,0x0c,0x9f,0x14,0x20,0x29,0x02,0x42,0x0b,0x0c,0x13,0x02,0x23,0x26,0xcc,0x65,0x62,0x81,0x71,
    0xdb,0x6d,0xb7,0x2d,0xdf,0xf5,0xfc,0x73,0x9b,0xac,0xcc,0xf2,0x72,0xdb,0x85,0xf9,0xce,0xea,0xea,0x07,0xd6,0xac,0x59,0x53,0x1e,0x43,0x8c,0xe1,0x26,0x5c,0x21,0x21,
    0xfa,0x19,0xd1,0xea,0xdd,0x77,0xdf,0x7d,0x15,0x6d,0x6d,0x6d,0x77,0x7b,0x7c,0xbe,0xbb,0x44,0x41,0xb8,0x6b,0xda,0xa5,0x4b,0x96,0x96,0x7d,0x70,0x60,0x60,0xc6,0xd0,
    0xcd,0x9b,0x2f,0x9f,0x3c,0x75,0xea,0x52,0x65,0x65,0xe5,0x51,0x87,0xc3,0xf1,0xfb,0x93,0x27,0x4f,0xfe,0xc1,0x80,0x5a,0x50,0xac,0x2e,0xc4,0xc6,0x53,0x15,0x78,0x05,
    0x9b,0x9b,0x9b,0xd7,0xf9,0xfd,0xfe,0x75,0xa2,0x24,0xd5,0x4c,0xc0,0x56,0x5e,0xe1,0x0f,0x04,0xbe,0xd3,0xd3,0xdb,0xfb,0x2f,0x73,0x2a,0x2b,0x0f,0xe4,0xe5,0xe6,0xee,
    0x3f,0x75,0xea,0xd4,0xd1,0x68,0x2e,0x14,0xcb,0x4e,0xc4,0xc6,0x0b,0x86,0xd3,0xe9,0xdc,0x04,0x8a,0x78,0x00,0x14,0x71,0x47,0x02,0x62,0x05,0xcb,0x07,0x02,0x1b,0xfa,
    0xae,0x5f,0xbf,0xb7,0x62,0xee,0xdc,0x57,0xcb,0x4a,0x4a,0x5e,0x6b,0x68,0x68,0x38,0x1b,0x61,0x27,0xb2,0x1b,0x85,0x42,0x1b,0x74,0x13,0x5d,0x18,0x2b,0x57,0xae,0x9c,
    0x07,0x32,0xde,0x39,0x34,0x3c,0xbc,0x37,0x41,0x30,0x94,0x63,0xcb,0x0d,0xf8,0xfd,0xdf,0xbd,0xd2,0xde,0xfe,0x52,0x55,0x55,0xd5,0xc6,0x28,0xdb,0xb3,0xdd,0x32,0x90,
    0x68,0x31,0xa3,0xa6,0xa6,0xe6,0xee,0x4b,0x97,0x2e,0xed,0xc2,0x32,0x4e,0xe6,0xd6,0x22,0x49,0xd2,0x57,0x61,0x41,0xea,0xe7,0xcd,0x9b,0xf7,0xe4,0xc3,0x0f,0x3f,0x9c,
    0x1f,0x01,0x0a,0x67,0x55,0x21,0xba,0x0d,0x54,0x57,0x57,0xaf,0xef,0x1f,0x18,0x78,0x5a,0x10,0xc5,0x95,0xa9,0xb0,0xdf,0xc2,0x64,0x1d,0x5e,0x9f,0xef,0x47,0xef,0x1c,
    0x3d,0xba,0x63,0xed,0xda,0xb5,0x65,0x51,0xf2,0x95,0xd8,0x81,0x84,0x24,0xa6,0xf9,0x7c,0x61,0x75,0xf5,0x37,0x6f,0x0e,0x0d,0xed,0x80,0x95,0xa9,0x32,0x32,0x58,0x41,
    0x10,0x2c,0x4d,0xd6,0xeb,0xf5,0x18,0xae,0x1b,0x08,0x04,0x1e,0x3c,0xd5,0xd4,0xb4,0x7d,0xd5,0xaa,0x55,0x73,0xcd,0x40,0xa1,0x23,0x04,0x51,0x5a,0x47,0x19,0xff,0x38,
    0x32,0x34,0xf4,0x23,0xa8,0x33,0xd3,0xf0,0x57,0xf7,0x33,0x4d,0xe8,0xf0,0x81,0x7d,0xa6,0x60,0xf8,0xbc,0x5e,0xf4,0xda,0x2b,0x3f,0x8f,0xe9,0x1d,0x08,0xb8,0x1b,0x5b,
    0x5a,0x5a,0xbe,0xbf,0x61,0xc3,0x86,0xa2,0x58,0x95,0x4f,0xc5,0x12,0x37,0x70,0xcc,0x00,0x37,0x79,0x06,0x94,0xe1,0x34,0x33,0xb9,0xe2,0xe9,0x33,0xc2,0x7e,0xef,0xde,
    0xf7,0x16,0x9a,0xe2,0xc8,0x0b,0x2b,0x7b,0xed,0xa5,0x9d,0xe8,0xbd,0x37,0x0f,0x8d,0xfd,0xbe,0xd6,0xdd,0x65,0x5a,0x59,0xb6,0x8c,0x8c,0xdd,0xbb,0xea,0xeb,0xff,0xb3,
    0xae,0xae,0x2e,0xa0,0xc5,0x4d,0x2b,0x47,0x61,0x8d,0xd2,0xc3,0xbb,0xc9,0xe0,0xe0,0xe0,0x77,0xcd,0xc2,0xd0,0x9a,0x9c,0xf4,0xd7,0xff,0x51,0x84,0x95,0x0d,0x0d,0x0d,
    0x59,0x82,0x10,0xe6,0x3e,0x7e,0xff,0x96,0xad,0x5b,0xb7,0xba,0x01,0xc8,0x6e,0xa3,0x89,0x1b,0xad,0xa1,0x0e,0x4d,0x57,0x69,0x6f,0x6f,0xff,0x4e,0xaa,0x04,0xd0,0x58,
    0x6c,0xc4,0xe3,0x79,0x68,0xc9,0x92,0x25,0x7f,0x6f,0xf6,0x2c,0xc3,0xe9,0x25,0x5d,0xc9,0xde,0x5a,0xad,0x64,0xb7,0xfd,0x83,0x83,0x9b,0xb7,0x6c,0xd9,0x92,0x67,0x24,
    0xc0,0xd2,0xd1,0xd4,0x81,0xd3,0x71,0x9c,0x81,0xa6,0xf3,0x77,0x52,0x48,0x18,0x57,0xbf,0xfb,0xee,0xbb,0x9b,0x8c,0x04,0x58,0x2a,0xda,0x36,0x3b,0x7f,0xfe,0xfc,0x7f,
    0xf7,0x78,0xbd,0x4f,0xc7,0x73,0x80,0x39,0xd9,0xd9,0x28,0x33,0x33,0x13,0xdd,0xf9,0x0f,0x6b,0xd0,0xe0,0xf5,0x5e,0xe4,0x87,0x6d,0x95,0x61,0xb9,0x60,0x80,0xed,0xb9,
    0xd6,0x8d,0x9a,0xff,0x7c,0x1a,0x79,0x3d,0x1e,0x04,0x67,0xa2,0xf8,0x7d,0x49,0xa2,0xe9,0x3f,0x2d,0x5a,0xb2,0xe4,0x9b,0x6f,0x1f,0x3e,0x7c,0x25,0x52,0x80,0xa5,0x22,
    0xed,0x2c,0xab,0x57,0xaf,0x9e,0xd3,0xd4,0xd4,0xf4,0xab,0x78,0x1c,0xd4,0x68,0x86,0x41,0x70,0x18,0x83,0x49,0x3b,0x82,0x40,0x0c,0xe4,0x13,0x68,0x18,0x02,0x2c,0xe4,
    0x3b,0x68,0x64,0x64,0x24,0x2e,0x50,0xb2,0x32,0x33,0x9f,0x6c,0x6d,0x6d,0xfd,0x49,0x24,0x20,0x11,0x33,0xd5,0x0b,0x17,0x2e,0xdc,0x13,0x0f,0x18,0x39,0x39,0x39,0xa8,
    0xb4,0xb4,0x14,0xcd,0x28,0x29,0x31,0x04,0x23,0xb8,0x65,0xda,0x6c,0xa8,0x60,0xea,0x54,0x54,0x02,0xef,0x4d,0x2b,0x2c,0xc4,0x2b,0x6c,0x19,0x88,0xcf,0xef,0xaf,0xdb,
    0xbc,0x79,0xf3,0xd4,0x48,0x6e,0xf3,0x17,0xa4,0x10,0x98,0x2c,0xcb,0x76,0xb8,0xd1,0x48,0x65,0xb3,0xe6,0xcc,0xf9,0xad,0x28,0x08,0x77,0x21,0x0b,0x96,0x97,0x97,0x87,
    0xa6,0x4e,0x9d,0x8a,0xec,0x76,0xbb,0xe6,0x73,0xe8,0x3b,0xec,0x37,0x45,0x51,0x9a,0xf5,0x06,0x06,0x06,0x50,0x5f,0x5f,0x1f,0x0a,0x04,0x02,0x56,0x86,0x83,0x1c,0xb9,
    0xb9,0x0f,0x9e,0x3b,0x77,0x6e,0x9f,0xc6,0x23,0x1e,0xfa,0xe6,0x59,0x18,0x10,0xa3,0x05,0x63,0xe9,0xd2,0xa5,0xb7,0xc7,0x03,0x46,0xf1,0xf4,0xe9,0x88,0x65,0x98,0xb0,
    0x72,0x41,0x10,0x90,0x28,0x8a,0x48,0x80,0x4b,0x96,0xa4,0x31,0x28,0x34,0x4d,0x23,0x1a,0xea,0x32,0x70,0x71,0x70,0x51,0xf4,0xe7,0xc3,0xca,0xcf,0xcf,0x47,0x34,0xc0,
    0xba,0xd6,0xd3,0x83,0x78,0x9e,0x37,0x3d,0x26,0xaf,0xd7,0x7b,0x27,0xdc,0xf6,0xe9,0x3d,0xa7,0xf5,0x1e,0xf4,0xf5,0xf7,0xdf,0x6e,0x05,0x46,0x76,0x4e,0x4e,0x50,0x19,
    0x4a,0x18,0x12,0x4c,0xde,0xef,0xf7,0xe3,0x41,0x05,0x57,0x5a,0xc2,0x40,0x14,0x0a,0xc1,0xcf,0x05,0x98,0xac,0xdf,0xe7,0x43,0x5e,0xb8,0xd4,0x13,0x77,0x00,0xe0,0x02,
    0x68,0xd3,0x8a,0xf1,0x82,0x50,0xbb,0x7e,0xfd,0xfa,0x12,0x8d,0x47,0xdc,0x28,0x10,0x4e,0xf3,0x45,0x9e,0x5f,0x6a,0xb6,0x53,0xbc,0xc2,0x05,0xb0,0xa2,0x4a,0x37,0xc1,
    0x8a,0xc0,0x30,0x8c,0x4a,0x1e,0xd7,0x0f,0x68,0xd4,0x9f,0x5a,0x50,0x80,0x1c,0x0e,0x87,0x69,0x20,0xb0,0x00,0x73,0x9a,0x5b,0x5b,0xbf,0xa2,0xf3,0x8c,0xd1,0x54,0xc8,
    0xba,0x75,0xeb,0x4a,0x61,0xf5,0x96,0x98,0xed,0x34,0x37,0x37,0x37,0x78,0x29,0x57,0x1e,0x4f,0x0c,0xbb,0x4a,0x2c,0x26,0x81,0x7a,0x30,0x44,0xa5,0x52,0x70,0x8c,0xc1,
    0xae,0xc8,0xb2,0xac,0x69,0x28,0xbe,0x91,0x91,0x1a,0x3d,0x8f,0xa1,0xb5,0xdc,0xe6,0xf2,0xe5,0xcb,0x95,0x40,0x6b,0x9a,0x69,0x20,0x53,0xa6,0xa8,0xd5,0x16,0x33,0x0c,
    0xa5,0x61,0x98,0x58,0x31,0xa3,0x96,0x03,0xee,0x98,0x9d,0x9d,0x6d,0xbe,0x3d,0x41,0xa8,0x8e,0x29,0x86,0x80,0x8f,0x57,0x9a,0xed,0x2c,0x2b,0x2b,0x2b,0x18,0x3f,0xc2,
    0xa4,0x6f,0x71,0x67,0xc0,0x0a,0x53,0xc7,0x13,0x65,0x1f,0x31,0xbb,0x8d,0x24,0xcd,0x8b,0x09,0x08,0x74,0x5e,0x6a,0xb6,0xb3,0xcc,0xcc,0xcc,0xb0,0xad,0xd3,0x8a,0x32,
    0xc2,0x76,0x26,0x00,0x8b,0xc1,0x8c,0x01,0x01,0xf0,0x7a,0x5b,0xb4,0x01,0xc0,0x15,0x8f,0x3d,0xf6,0x58,0xae,0x56,0xf8,0xd3,0x04,0x22,0x5a,0x70,0x97,0x0c,0x55,0xbe,
    0xa1,0x94,0xba,0x15,0xc3,0xdb,0xb3,0xa4,0x68,0xcb,0x66,0xb3,0xe9,0xe6,0x36,0x46,0x86,0x09,0xb9,0x48,0xa1,0x61,0x85,0xc8,0xa2,0x68,0x3a,0x8c,0xe3,0x81,0x2a,0x93,
    0x2e,0x49,0x95,0x78,0x59,0x72,0x1d,0x55,0x5b,0x0c,0xad,0x9b,0x35,0x44,0x35,0x8f,0xc7,0x93,0xa1,0x17,0x54,0xc7,0x03,0x81,0x79,0x99,0xde,0x72,0x55,0x32,0x96,0x15,
    0x32,0xb7,0xac,0x12,0x15,0x10,0xda,0x02,0x10,0x88,0x6b,0x9c,0xf1,0xc4,0x4c,0x96,0x85,0x78,0xac,0xa2,0x59,0x1f,0x37,0x0c,0xc8,0xc2,0xbb,0x30,0x36,0xd9,0x30,0x10,
    0x20,0xef,0x31,0x1d,0xfc,0x54,0x41,0x94,0x56,0xa5,0xed,0xc8,0xda,0x24,0x22,0xf6,0x15,0x8b,0x4d,0x99,0x32,0x45,0x6b,0x8e,0x92,0x26,0x10,0x8a,0x61,0xfa,0xcc,0x76,
    0x84,0x13,0xa9,0x78,0xf9,0xb9,0x56,0x06,0xac,0x84,0xc1,0x9b,0x07,0x22,0x17,0x14,0x14,0x0c,0x1b,0x56,0x88,0x8d,0x65,0xbb,0x4c,0x1f,0x9e,0xe0,0x0c,0xa2,0x34,0x36,
    0x4e,0x0a,0xc1,0x99,0xa9,0x32,0x66,0xe0,0xf3,0x90,0x60,0xf2,0x90,0x07,0x4a,0x73,0xbf,0xfe,0xfa,0xeb,0xdd,0x86,0x81,0xb0,0x19,0x19,0x57,0x4d,0x03,0xf1,0x78,0x82,
    0x83,0x1d,0x5b,0x55,0x98,0x88,0x95,0x34,0x7b,0xec,0xe4,0x05,0x6d,0x28,0x5d,0x66,0x64,0x64,0xc4,0xbc,0xd2,0x68,0xba,0x55,0xef,0x08,0xa5,0x09,0xa4,0xb0,0xa0,0xa0,
    0xc5,0x6c,0xcc,0xc2,0x52,0x1e,0x1e,0x1a,0x0a,0xf3,0x7b,0x8e,0xe3,0x2c,0xed,0x08,0x78,0x2b,0x67,0xb9,0xcf,0x37,0x05,0x9c,0xf9,0x2a,0xfb,0x88,0xd5,0x20,0xae,0x35,
    0xeb,0xed,0x09,0x78,0x94,0xe3,0xf6,0xc5,0xf7,0xdf,0x7f,0xff,0x02,0x4c,0xe0,0x53,0xb3,0x1d,0xde,0x84,0xc1,0x2a,0x55,0x82,0x15,0x82,0x27,0x45,0x9b,0xd8,0x75,0xf0,
    0xbb,0x9c,0x02,0x46,0xf0,0x63,0xd1,0xe0,0x20,0xf2,0x5b,0x38,0x0e,0x40,0xf2,0x78,0x26,0x52,0xea,0x2e,0xea,0x0c,0xc4,0x34,0x10,0x1f,0xc4,0x91,0x81,0xfe,0xfe,0x70,
    0xc9,0xc3,0xa4,0x70,0x16,0xcb,0xc4,0x10,0x53,0x82,0xd9,0x68,0x46,0x46,0x98,0xba,0x30,0xec,0xc1,0x81,0x01,0x2b,0xde,0x37,0x5c,0x38,0x7d,0xfa,0x1f,0x75,0x62,0x8b,
    0xa8,0xa9,0x90,0xe0,0x89,0x32,0x3b,0xfb,0x63,0x2b,0xbd,0xe2,0x55,0xec,0xed,0x0b,0xdf,0xac,0xf0,0x6a,0xe3,0x74,0x3b,0x03,0x4f,0x32,0x02,0x18,0x1c,0x2f,0xec,0x70,
    0x26,0xc2,0xf5,0x28,0x55,0x20,0xed,0x07,0xd0,0x56,0xb6,0x5b,0x18,0x43,0x43,0xc3,0x7b,0xef,0x9d,0xd7,0x72,0x97,0xa0,0x42,0x30,0x15,0xad,0x17,0x6b,0x6b,0x6b,0x8f,
    0xc1,0xa0,0xcf,0x58,0x81,0xd2,0xd7,0xdb,0x1b,0xfc,0x0e,0xaa,0xce,0x2e,0xf1,0xca,0x67,0xc1,0x84,0xf1,0xc9,0x18,0x03,0x1a,0x85,0x84,0x0f,0x86,0xf8,0x58,0x8f,0x61,
    0x70,0xaa,0x40,0x8c,0x83,0x75,0x4f,0x4f,0x0f,0x1a,0x19,0x1e,0xb6,0x14,0x9c,0x41,0x71,0x0d,0x7a,0x1f,0xd3,0x94,0xbb,0xcc,0xb8,0xfd,0x6b,0xcf,0x9e,0x3d,0x03,0x30,
    0xf0,0x77,0xac,0x1e,0xdb,0xf1,0x24,0xae,0x75,0x77,0xa3,0x80,0x6a,0x8b,0xc4,0xc1,0x36,0xf8,0xed,0x14,0x5c,0x09,0x5f,0xc1,0xc0,0xa9,0xda,0x5a,0x47,0xed,0xc6,0x8d,
    0x1b,0xa8,0x1b,0xda,0x19,0xb6,0x08,0x03,0xfa,0xbc,0x5a,0x54,0x54,0xf4,0x7f,0x7a,0xee,0x12,0xf1,0x9b,0x2a,0xb6,0x69,0x05,0x05,0xbf,0x87,0x8a,0xd7,0xad,0x9e,0x3f,
    0xfa,0xae,0x5f,0x47,0x6e,0x97,0x2b,0xe8,0x46,0x72,0x0c,0x87,0x3d,0x1f,0xb8,0x48,0x37,0xc0,0xec,0xea,0xea,0x42,0x1e,0x0b,0xdb,0xac,0x22,0x8e,0xbd,0xd9,0xd0,0xd0,
    0x70,0x56,0xcf,0x5d,0xc6,0x80,0xe0,0xcf,0xef,0x5a,0x0d,0x9c,0x38,0x71,0xe2,0x13,0xd8,0xee,0xf6,0xc7,0x23,0xb1,0x82,0xd3,0x25,0xea,0x74,0xbb,0xd1,0x95,0xf6,0x76,
    0xd4,0x0b,0xae,0x54,0x58,0x52,0x8e,0xa6,0x38,0xf2,0xc7,0xd5,0x9b,0x31,0x73,0x0e,0x12,0x24,0x19,0x75,0x76,0x76,0xa2,0xab,0x1d,0x1d,0xe8,0x3a,0xc0,0x8c,0xc7,0x27,
    0x04,0x98,0x63,0x57,0x51,0x61,0xe1,0xff,0x46,0x72,0x97,0x60,0x8c,0x51,0x15,0x8e,0x3b,0x01,0x16,0x17,0x16,0x1e,0x80,0xc1,0xad,0x82,0x43,0xdb,0xec,0xb8,0x80,0x81,
    0x95,0xc6,0xd7,0x43,0xf7,0xd4,0xa1,0x9a,0xaf,0x2d,0x83,0x40,0x39,0x82,0x02,0xa0,0x04,0xac,0x9b,0x9c,0x5c,0x07,0x5c,0x79,0x68,0xcb,0x86,0x7b,0x83,0xff,0x87,0x89,
    0xa7,0x65,0xd8,0x6c,0x07,0x60,0x81,0x3f,0x8c,0xe4,0x2e,0x61,0x40,0xb0,0x4a,0x40,0xce,0x9c,0x96,0x4a,0xe6,0xce,0x9f,0xff,0x2b,0x90,0xef,0x93,0xf1,0x3e,0xad,0x66,
    0x03,0x00,0x7c,0x4d,0xb4,0x41,0xac,0x3a,0x5d,0x5c,0x5c,0xbc,0x2f,0x9a,0x3a,0xb4,0x62,0x88,0xa6,0xeb,0xd4,0x7c,0xf9,0xcb,0xaf,0x42,0x0a,0x7e,0x14,0xa5,0xa9,0x65,
    0xda,0xed,0xbf,0x68,0x6c,0x6c,0x6c,0xd2,0x8a,0x1d,0xea,0x70,0x41,0xab,0xa4,0xa3,0x09,0xe4,0xe0,0xc1,0x83,0xee,0x7c,0x87,0xe3,0x65,0x78,0xde,0x96,0x6e,0x30,0xc0,
    0x55,0x5e,0x3e,0x74,0xe8,0xd0,0x2b,0x46,0xd4,0xa1,0xb7,0xcb,0x68,0x42,0x39,0x7d,0xfa,0xf4,0x11,0x48,0xd6,0xf6,0xe8,0x25,0x72,0xa9,0x68,0xa0,0xea,0x77,0x66,0xcd,
    0x9a,0xb5,0xa7,0xaa,0xaa,0x4a,0xd2,0x51,0x87,0x18,0x15,0x48,0x48,0x25,0x9a,0x50,0x9a,0x9b,0x9b,0xf7,0x80,0xfc,0x9e,0x4f,0x0b,0x18,0x0c,0xf3,0xf1,0xf4,0xa2,0xa2,
    0x9f,0xe0,0x73,0x99,0x51,0x75,0xe8,0xe6,0x21,0x7a,0xae,0x83,0x6d,0xc7,0x8e,0x1d,0xcf,0x03,0x94,0x17,0xad,0x0e,0xb8,0xe9,0x53,0xed,0x93,0x01,0xfe,0xc6,0x71,0xb9,
    0xf5,0xbc,0xe5,0x20,0x9a,0x9f,0x97,0xf7,0xc2,0x27,0x9f,0x7c,0x72,0x5c,0x0f,0x86,0x5e,0x86,0x1e,0x29,0x31,0xd3,0x84,0xb2,0x71,0xe3,0x46,0xff,0xfa,0xf5,0xeb,0x9f,
    0x83,0xf4,0xba,0xde,0x8a,0xfb,0xfc,0x6e,0xdf,0x2f,0x51,0xe7,0xc5,0x73,0x88,0x16,0x7c,0x63,0x17,0x03,0xd7,0xbf,0xd6,0xad,0xb4,0x06,0x83,0xa6,0x1b,0x0b,0xf2,0xf3,
    0x77,0x80,0x8b,0xeb,0x65,0xd9,0x52,0xa4,0x05,0xff,0x0b,0x00,0xa4,0x50,0x04,0x93,0x65,0x99,0x83,0x1b,0xa7,0xf7,0x7c,0xde,0x82,0x05,0x8f,0xfa,0xbc,0xde,0xef,0x41,
    0xbd,0xa9,0xc8,0x84,0xed,0x7c,0x69,0x2f,0x5a,0xfc,0x95,0x5b,0xc2,0xca,0xfe,0xee,0x6f,0x6e,0x31,0xd3,0x54,0xd0,0x58,0x86,0x79,0xb3,0xa8,0xa4,0xe4,0xc5,0xff,0xff,
    0xe8,0xa3,0x8f,0x75,0xaa,0x48,0x14,0x45,0xf9,0x22,0xb5,0x41,0x47,0x7a,0x08,0x2f,0xf3,0x70,0xe3,0xf5,0x9e,0x5f,0x68,0x69,0x79,0xd1,0x91,0x9f,0xff,0x3d,0x9a,0xa6,
    0x3f,0x44,0xc9,0xb5,0x80,0x8d,0xe3,0x7e,0x56,0x55,0x53,0xf3,0xc3,0x08,0x30,0x50,0xa4,0xb9,0x8c,0x41,0x8d,0x56,0x01,0x43,0x01,0x05,0x20,0x3d,0xa5,0x9c,0x6d,0x6a,
    0x3a,0xb8,0x62,0xc5,0x8a,0xe6,0xab,0x1d,0x1d,0xff,0x1c,0x08,0x04,0xbe,0x05,0x45,0xd9,0x89,0x24,0xc1,0xd0,0xf4,0x47,0xd9,0xd9,0xd9,0xbf,0xf8,0xec,0xb3,0xcf,0x7e,
    0x1d,0xa5,0xaa,0x1f,0xe6,0x22,0x46,0x6b,0x8f,0x36,0xd2,0x69,0x34,0xa5,0x1c,0x3b,0x76,0xec,0xdc,0xa5,0xb6,0xb6,0x1f,0x14,0xe4,0xe7,0x3f,0xc4,0xb2,0xec,0xe1,0x44,
    0x80,0xa0,0x19,0xa6,0xd5,0x9e,0x99,0xf9,0x8c,0xd3,0xe9,0x7c,0x24,0x5e,0x30,0x0c,0x29,0xc4,0xa8,0x52,0xb0,0x9d,0x39,0x73,0xe6,0x30,0x0c,0xee,0x8d,0xf5,0x1b,0x36,
    0xac,0x1d,0xbe,0x71,0xe3,0x7e,0x41,0x14,0xef,0x81,0xe2,0x9c,0xb8,0x2a,0x82,0x61,0x4e,0x73,0x36,0xdb,0x5b,0x33,0x8a,0x8b,0x7f,0x77,0xfc,0xf8,0xf1,0x3f,0x47,0xa9,
    0x1e,0x35,0x66,0x98,0x06,0xa2,0x80,0x22,0xc1,0x9f,0x19,0x7a,0x75,0xaa,0xaa,0xaa,0x24,0xec,0x46,0xf0,0xe7,0xc1,0x5b,0x97,0x2e,0xad,0x1d,0xe8,0xef,0x5f,0x2e,0xf0,
    0xfc,0xed,0x82,0x20,0x2c,0x8b,0xf4,0x5e,0x94,0x7e,0xdb,0x38,0x96,0x6d,0xb4,0xdb,0xed,0x0d,0x8b,0x16,0x2d,0xfa,0x60,0xff,0xfe,0xfd,0x9d,0x06,0x5e,0xe3,0x43,0xca,
    0x46,0x13,0x06,0x24,0x34,0x38,0x11,0xa0,0xf8,0x43,0x4a,0x89,0xe8,0x72,0x9f,0x9e,0x38,0xd1,0x08,0x37,0x7c,0xa1,0x95,0x2b,0x57,0x3a,0xbb,0xbb,0xbb,0x6b,0xfc,0x3c,
    0x5f,0x0d,0x70,0x2a,0x65,0x49,0xaa,0x00,0xf7,0x9a,0x0f,0x8f,0x6c,0xaa,0xf6,0x2f,0xc1,0xd5,0x09,0x3b,0x46,0x1b,0xc3,0x71,0xe7,0xb3,0x72,0x72,0xce,0xdd,0xb1,0x6c,
    0xd9,0x9f,0x76,0xef,0xde,0x3d,0x18,0xc3,0x30,0x4d,0xc1,0x08,0xf6,0x6f,0x45,0xbe,0xd1,0xb6,0xe5,0x68,0xd6,0xe2,0xea,0x3d,0x22,0x23,0x79,0xb9,0xb2,0x6c,0x61,0x59,
    0x91,0x95,0xa0,0xcc,0x2b,0x62,0x9e,0xb9,0xad,0xdb,0x0a,0x10,0x85,0x0b,0x71,0x46,0x03,0xf4,0x04,0x1a,0x6f,0x05,0x44,0x5c,0x80,0x8c,0xba,0x10,0xdc,0xb0,0x1b,0x31,
    0x49,0x02,0x13,0x17,0x10,0x71,0x03,0x12,0x01,0x0c,0x9a,0x40,0x38,0x96,0x5d,0x63,0xc2,0x81,0xa8,0xc1,0x84,0x62,0x0c,0x13,0x2a,0x8e,0x87,0x72,0xf8,0xd0,0x36,0x2a,
    0x4e,0xa4,0xdc,0xd8,0x89,0x6c,0x5c,0x31,0xf8,0x51,0x40,0x59,0x46,0x57,0x7f,0x34,0x8f,0x50,0xb5,0x33,0xe1,0xc6,0x26,0x39,0x10,0xea,0x65,0xc5,0x49,0x33,0x3a,0xd5,
    0x80,0x24,0xdb,0x08,0x10,0x02,0x84,0x00,0x21,0x40,0x08,0x10,0x02,0x84,0x00,0x21,0x40,0x08,0x10,0x02,0x84,0x00,0x21,0x40,0x08,0x10,0x02,0x84,0x00,0x21,0x40,0x08,
    0x90,0x2f,0xb8,0xfd,0x05,0x00,0x00,0x00,0xa4,0x50,0x02,0x4d,0x96,0xe5,0x2c,0xe5,0xef,0x16,0x57,0xef,0x11,0x19,0xc9,0xcb,0x95,0x65,0xce,0xf2,0xe2,0x84,0x8e,0x49,
    0x6d,0x34,0x22,0x46,0x80,0x10,0x20,0x04,0x08,0x01,0x42,0x80,0x10,0x20,0x04,0x08,0x01,0x42,0x80,0x10,0x20,0x04,0x08,0x01,0x42,0x80,0x10,0x20,0x04,0x08,0x01,0x42,
    0x80,0x10,0x23,0x40,0x08,0x10,0x02,0x84,0x00,0x21,0x40,0x08,0x10,0x02,0x84,0x00,0x21,0x40,0x08,0x10,0x02,0x84,0x00,0x21,0x40,0x26,0x95,0xfd,0x05,0x00,0x00,0x00,
    0xa4,0x50,0x02,0x4d,0x96,0xe5,0x2c,0xe5,0xef,0x16,0x57,0xef,0x11,0x19,0xc9,0xcb,0x95,0x65,0xce,0xf2,0xe2,0x84,0x8e,0x49,0x6d,0x34,0x22,0x46,0x80,0x10,0x20,0x04,
    0x08,0x01,0x42,0x80,0x10,0x20,0x04,0x08,0x01,0x42,0x80,0x10,0x20,0x04,0x08,0x01,0x42,0x80,0x10,0x20,0x04,0x08,0x01,0x42,0x80,0x10,0x23,0x40,0x08,0x10,0x02,0x84,
    0x00,0x21,0x40,0x08,0x10,0x02,0x84,0x00,0x21,0x40,0x08,0x10,0x02,0x84,0x00,0x21,0x40,0x26,0x95,0xfd,0x05,0x00,0x00,0x00,0xa4,0x50,0x02,0x4d,0x96,0xe5,0x2c,0xe5,
    0xef,0x16,0x57,0xef,0x11,0x19,0xc9,0xcb,0x95,0x65,0xce,0xf2,0xe2,0x84,0x8e,0x49,0x6d,0x34,0x22,0x46,0x80,0x10,0x20,0x04,0x08,0x01,0x42,0x80,0x10,0x20,0x04,0x08,
    0x01,0x42,0x80,0x10,0x20,0x04,0x08,0x01,0x42,0x80,0x10,0x20,0x71,0x34,0x8a,0x42,0xc7,0xd5,0x65,0xcd,0x1d,0xd7,0x9e,0x22,0x0a,0x09,0xb7,0xed,0xc9,0x84,0x92,0xe8,
    0x0f,0x44,0x76,0xf5,0x22,0xb4,0xb8,0x7b,0xb6,0xca,0x32,0xda,0xa6,0xae,0x9b,0xac,0x0f,0x45,0x89,0x56,0x88,0xa8,0x2e,0x58,0x50,0x5a,0xf4,0x9c,0x56,0xc5,0x64,0xa9,
    0x24,0xa1,0x40,0x28,0x8a,0xe2,0xe1,0xc6,0x8f,0x1b,0x04,0x45,0x3d,0x9b,0x2a,0xae,0x93,0x14,0x59,0xaa,0xbf,0xad,0xa6,0x92,0xeb,0x24,0x2b,0xa8,0xf2,0xa9,0xea,0x3a,
    0x49,0x01,0x12,0x72,0x1d,0x49,0xa3,0xfc,0x99,0x64,0xbb,0x4e,0xd2,0x3e,0xf9,0x83,0xdb,0x70,0x70,0xe3,0xd4,0xe5,0xe7,0x5d,0xbd,0x6f,0xc3,0xd3,0xbf,0x4d,0x96,0xeb,
    0x24,0x2d,0x0f,0xd1,0x0b,0xb0,0x2c,0x23,0xff,0x38,0x99,0xae,0x93,0xe4,0x4c,0x95,0x1a,0x07,0x64,0xee,0x8c,0xa2,0xe3,0x90,0xc1,0xfe,0x97,0xb6,0xeb,0x74,0xad,0xf8,
    0x22,0x64,0xaa,0x86,0x03,0x2c,0x0c,0x77,0xf2,0x03,0x49,0xb5,0xdc,0x84,0x4a,0x95,0x03,0x4c,0xaa,0xe4,0x26,0xa9,0x74,0xb8,0x4b,0x89,0xdc,0x24,0x65,0x80,0xa4,0x8a,
    0xeb,0x50,0xa9,0x76,0xf6,0x4f,0xb6,0xeb,0xfc,0x05,0x00,0x00,0xa4,0x50,0x8a,0x99,0x2c,0xcb,0x1c,0xdc,0x38,0x75,0xf9,0x79,0x57,0xcf,0x88,0x46,0xf5,0x1d,0xce,0xf2,
    0xe2,0xa7,0x46,0x7f,0xcc,0x9d,0x3b,0x37,0xf7,0xe2,0xc5,0x8b,0x37,0xad,0xf4,0x4f,0xa7,0x1a,0x10,0x8a,0xa2,0x78,0xb8,0x49,0xe3,0x06,0x4a,0x51,0xcf,0x6a,0x54,0xdf,
    0xde,0xdc,0x71,0xed,0xa9,0xd2,0xd2,0xd2,0x6f,0x95,0x96,0x95,0xb5,0x7a,0x7d,0xbe,0x1b,0x70,0x3f,0x53,0x56,0x56,0xb6,0xc2,0x74,0xff,0x28,0x05,0x0d,0x54,0xc2,0xc0,
    0x2d,0x43,0x5d,0xde,0xe2,0xee,0xd9,0x2a,0xcb,0x68,0xdb,0xe8,0xef,0x9e,0xee,0x6e,0xb4,0xf7,0xa5,0x9f,0xa2,0x63,0x7f,0x38,0xaa,0xae,0x3a,0xc0,0xb1,0x6c,0xd5,0x95,
    0x2b,0x57,0xba,0x63,0xed,0x9b,0x4d,0x45,0x20,0xa0,0x12,0x11,0xa0,0xf0,0x6a,0xd7,0x61,0x68,0xd4,0x28,0x88,0x7f,0xfd,0xfb,0x8d,0x43,0x07,0xd0,0xde,0xff,0xfe,0x29,
    0xf2,0x79,0xbd,0x5a,0x4d,0xe4,0x07,0x04,0xa1,0x1e,0xee,0x0f,0x4c,0x0a,0x85,0x28,0x94,0x92,0xa5,0x2e,0xfb,0xcd,0x6f,0xdf,0xda,0xf5,0x3f,0xfb,0x5f,0xff,0xf6,0x89,
    0x0f,0x8f,0x1b,0x99,0xdc,0x26,0x97,0xcb,0xf5,0xeb,0xc9,0x04,0x24,0x2c,0xc0,0x2e,0x58,0xb0,0xe0,0xdf,0x46,0x3c,0x9e,0x67,0x63,0x18,0x77,0x57,0xc8,0x75,0x06,0xd3,
    0x36,0xa8,0xea,0x05,0xd8,0xea,0xea,0xea,0x7f,0x02,0x18,0x3f,0x8e,0x71,0x11,0x67,0x84,0x5c,0x07,0x4d,0x0a,0x20,0x21,0x85,0x04,0xc7,0x08,0x30,0xbe,0x61,0x72,0xd7,
    0x78,0x10,0x76,0x9d,0xb5,0x69,0x0f,0x24,0xb4,0xd3,0x8c,0xb9,0x0b,0x45,0xd3,0x37,0xcc,0xb6,0x05,0x12,0xab,0x9f,0x3d,0x7b,0xb6,0x3d,0xdd,0x15,0x12,0x36,0xb6,0xdc,
    0xbc,0xbc,0xdf,0x58,0xc8,0x2d,0xe6,0x08,0x06,0x5d,0x87,0x4a,0x97,0x80,0x8a,0xad,0xa2,0xb2,0xf2,0x85,0x40,0x20,0xf0,0x88,0x05,0x30,0xf7,0xc2,0xae,0xf3,0x76,0xba,
    0x2a,0x64,0x5c,0xb6,0xba,0x6b,0xe7,0xce,0x27,0x20,0xd0,0xb6,0x59,0x80,0x5c,0x9f,0xb6,0x2e,0x83,0x93,0x33,0xb8,0xf1,0xca,0xb2,0xba,0xba,0xba,0x40,0x5e,0x41,0xc1,
    0xe3,0x16,0x1a,0x75,0x42,0x9a,0x5f,0x9f,0xb6,0x79,0x88,0x5e,0x72,0x36,0xa7,0xb2,0x72,0x37,0x1f,0x08,0x6c,0xb6,0xd0,0xe8,0xd7,0xdd,0x6e,0xf7,0xfb,0x69,0xb7,0xed,
    0x86,0x8c,0x57,0x17,0xac,0xb9,0xff,0xfe,0xc7,0x41,0x41,0x6e,0x0b,0x4a,0xa9,0x4f,0x5b,0x85,0xe8,0x05,0xd8,0x45,0x8b,0x17,0xaf,0x1b,0xec,0xef,0xff,0xa5,0x85,0x46,
    0x9f,0x01,0x95,0x3c,0x99,0x96,0x40,0x22,0xb8,0xce,0xcf,0xc1,0x75,0x36,0x9a,0x6e,0x53,0x92,0x6a,0x3b,0x3b,0x3b,0x3f,0x4a,0x37,0x97,0xd1,0x75,0x9d,0x65,0x4b,0x97,
    0x62,0xd7,0xb9,0x6e,0xda,0x73,0x68,0xba,0x3e,0x6d,0x15,0xa2,0xe7,0x3a,0x4e,0xa7,0x73,0xd3,0xd0,0xf0,0xf0,0x5e,0x0b,0xf1,0x64,0x9b,0xbb,0xa3,0xe3,0xe9,0xb4,0x04,
    0xa2,0xe7,0x3a,0xb3,0x2b,0x2a,0x5e,0x15,0x78,0xfe,0x1b,0xa6,0x13,0x1e,0x51,0xbc,0xa5,0xab,0xab,0xeb,0x64,0xba,0xb9,0x8c,0xae,0xeb,0x2c,0xfe,0xd2,0x97,0x9e,0x80,
    0xdb,0xb0,0xe9,0x33,0x02,0xc3,0xbc,0x90,0xb6,0x0a,0x09,0xa9,0xc4,0xae,0x5e,0x4c,0x70,0x9d,0x07,0xc1,0x75,0x7e,0x66,0xba,0x4d,0x8a,0xfa,0x41,0x67,0x47,0x47,0x7d,
    0xba,0x02,0xd1,0xfc,0xe6,0x0a,0x27,0xda,0xfd,0x82,0x28,0xde,0x6b,0xb6,0x5d,0x96,0x61,0x2a,0xd2,0xd1,0x65,0x34,0xd3,0x7a,0x6c,0x0b,0x17,0x2e,0xc4,0xae,0x23,0x98,
    0x6d,0x57,0x14,0xc5,0xfb,0xd3,0x12,0x88,0xde,0xbf,0x2b,0x8e,0x1c,0x39,0x72,0x21,0x27,0x3b,0xfb,0x71,0x4b,0xed,0xa2,0x34,0xb6,0x08,0xae,0xf3,0x06,0xb8,0xce,0xd7,
    0x63,0x6c,0xce,0x05,0x30,0x16,0xd3,0xe9,0x0c,0x44,0xcf,0x75,0x66,0xce,0x9c,0xf9,0x78,0x8c,0xaa,0x38,0x4a,0x53,0xd4,0x2a,0x97,0xcb,0xd5,0x9f,0xd6,0x40,0x14,0xae,
    0x13,0x66,0x0d,0x0d,0x0d,0x67,0xb3,0x32,0x33,0xb7,0x45,0x7b,0x97,0x61,0x58,0xf4,0xc8,0xa3,0xdf,0x47,0x00,0xe2,0xee,0x8e,0x8e,0x8e,0xb3,0x69,0xef,0x32,0xd1,0xfe,
    0x1f,0x3c,0x73,0xd6,0xac,0x77,0x24,0x49,0xba,0x5d,0xeb,0x9d,0xda,0x3b,0x56,0xa0,0x6f,0x6f,0x79,0x14,0x95,0x94,0x95,0x87,0xfd,0x8f,0x78,0x52,0x00,0xd1,0x83,0x52,
    0x5b,0x5b,0xfb,0xd5,0xf6,0xab,0x57,0x3f,0x50,0x96,0xd9,0x6c,0x36,0xdf,0x96,0xff,0xf8,0xa1,0x7d,0xd5,0xea,0x3a,0x75,0xbe,0x7a,0xa7,0xb3,0x7c,0xc6,0x31,0x7a,0xb2,
    0x00,0xd1,0xda,0x75,0x1a,0x1b,0x1b,0xff,0x58,0x38,0x6d,0x5a,0x1d,0xc3,0x30,0x1f,0xc3,0xf3,0xab,0x9c,0xcd,0xf6,0xca,0xd7,0x6e,0xbd,0x75,0xd1,0x78,0x18,0xc1,0x7c,
    0x75,0xc5,0xa4,0x52,0x48,0x84,0x2c,0x56,0x52,0x67,0xb5,0xad,0xee,0xde,0x27,0x24,0x59,0x56,0x05,0x5e,0xea,0x98,0xb3,0xbc,0xe8,0xce,0xbf,0x00,0xa5,0xd0,0x24,0x34,
    0x59,0x96,0x19,0x7c,0xa7,0x28,0x4a,0x0c,0xfd,0xce,0x52,0xd7,0x69,0x71,0xf7,0x6c,0x95,0x65,0xb4,0x4d,0x51,0xb4,0xc3,0x59,0x5e,0xfc,0xd4,0xa4,0x04,0xa2,0x01,0x88,
    0x83,0x1b,0xa7,0x2a,0xe6,0xcf,0xbb,0xba,0x6b,0x11,0x62,0xb6,0x43,0x8d,0x0f,0x30,0x8c,0x20,0x44,0xf4,0x05,0x31,0x0d,0x28,0xfe,0x51,0x05,0x29,0xed,0x2f,0x20,0x17,
    0xf6,0x9c,
};


#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER ALIGN(4)
#endif
LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER const eZIP_RGBARGB888A  lv_img_dsc_t music_stylus_img SECTION(".ROM3_IMG_EZIP_HEADER.music_stylus_img") = { 
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .header.always_zero = 0,
  .header.w = 68,
  .header.h = 227,
  .data_size  = 4290,
  .data = music_stylus_img_map
};
