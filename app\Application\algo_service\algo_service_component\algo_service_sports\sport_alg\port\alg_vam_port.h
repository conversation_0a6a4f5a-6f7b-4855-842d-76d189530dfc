/************************************************************************​
*Copyright(c) 2025, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#ifndef ALG_VAM_PORT_H
#define ALG_VAM_PORT_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

#define ALG_VAM_BUF_SIZE                30

typedef struct _alg_vam_input
{
    uint32_t timestamp;
    float alt;                          //m
} alg_vam_input_t;

typedef struct _alg_vam_output
{
    float vam;                          //m/h
    float vam_30s;                      //m/h
} alg_vam_output_t;

void alg_vam_init(void);

int alg_vam_exec(const alg_vam_input_t *input, alg_vam_output_t *output);

#ifdef __cplusplus
}
#endif

#endif