/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   data_sync_remind.c
@Time    :   2025/08/06 11:13:40
*
**************************************************************************/

#pragma once

#include <stdbool.h>
#include <stdint.h>
#include "qw_log.h"
#if defined(__cplusplus)
extern "C"
{
#endif


#define DATA_SYNC_REMIND_LVL               LOG_LVL_DBG
#define DATA_SYNC_REMIND_TAG               "DATA_SYNC_REMIND_TAG"

#if (DATA_SYNC_REMIND_LVL >= LOG_LVL_DBG)
    #define DATA_SYNC_REMIND_LOG_D(...)        QW_LOG_D(DATA_SYNC_REMIND_TAG, __VA_ARGS__)
#else
    #define DATA_SYNC_REMIND_LOG_D(...)
#endif

#if (DATA_SYNC_REMIND_LVL >= LOG_LVL_INFO)
    #define DATA_SYNC_REMIND_LOG_I(...)        QW_LOG_I(DATA_SYNC_REMIND_TAG, __VA_ARGS__)
#else
    #define DATA_SYNC_REMIND_LOG_I(...)
#endif

#if (DATA_SYNC_REMIND_LVL >= LOG_LVL_WARNING)
    #define DATA_SYNC_REMIND_LOG_W(...)        QW_LOG_W(DATA_SYNC_REMIND_TAG, __VA_ARGS__)
#else
    #define DATA_SYNC_REMIND_LOG_W(...)
#endif

#if (DATA_SYNC_REMIND_LVL >= LOG_LVL_ERROR)
    #define DATA_SYNC_REMIND_LOG_E(...)        QW_LOG_E(DATA_SYNC_REMIND_TAG, __VA_ARGS__)
#else
    #define DATA_SYNC_REMIND_LOG_E(...)
#endif


// 数据同步提醒模块初始化
void data_sync_remind_init(void);

// 设置上次同步时间
void data_sync_remind_set_last_sync_time(void);

void data_sync_remind_reset(void);

#if defined(__cplusplus)
}
#endif