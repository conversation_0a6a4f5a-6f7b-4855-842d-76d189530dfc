/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ble_test_cmd.c
@Time    :   2025/01/06 13:55:20
*
**************************************************************************/

#include "ble_test_cmd.h"
#include <string.h>
#include <rtthread.h>
#include "qw_general.h"
#include "ble_data_inf.h"
#include "ble_lb55x.h"
#include "bf0_ble_common.h"
#include "cmsis_os2.h"
#include "ble_test_module/barometer_debug_cmd.h"
#include "ble_test_module/button_debug_cmd.h"
#include "ble_test_module/ui_debug_cmd.h"
#include "ble_test_module/ble_common_debug_cmd.h"
#include "ble_test_module/gps_debug_cmd.h"
#include "ble_test_module/sensor_test_cmd.h"
#include "cfg_header_def.h"
#include "production_test.h"  // 添加新的头文件引用
#include "qw_time_util.h"
#include "thread_pool.h"

static ble_debug_list_t ble_debug_data_list;
static rt_sem_t ble_debug_data_sem;
static rt_thread_t ble_debug_thread = NULL;
static uint8_t ble_debug_chn_sta = false;
static bool app_is_disconnect = false;
// static uint8_t ble_debug_rx_data[BLE_DEBUG_DATA_MAX_LEN];
static uint8_t ble_debug_tx_data[BLE_DEBUG_DATA_MAX_LEN];
static uint8_t notif_send_data[BLE_DEBUG_DATA_MAX_LEN] = {0};
static uint8_t notif_send_data_len = 0;
static ble_debug_data_t l_rx_data = {
    .data = NULL,
    .length = 0,
};
static ble_debug_data_t l_tx_data = {
    .data = ble_debug_tx_data,
    .length = 0,
};

static osMutexId_t ble_debug_send_lock = NULL;

#define BLE_DEBUG_SEND_ENTER_CRITICAL()                     \
    do {                                                    \
        if(NULL == ble_debug_send_lock)                     \
        {                                                   \
            osMutexAttr_t attr = {0};                       \
            attr.name = "ble_debug_send_lock";              \
            ble_debug_send_lock = osMutexNew(&attr);        \
        }                                                   \
        osMutexAcquire(ble_debug_send_lock, osWaitForever); \
    } while(0)

#define BLE_DEBUG_SEND_EXIT_CRITICAL()                      \
    do {                                                    \
        if (NULL == ble_debug_send_lock)                    \
            break;                                          \
        osMutexRelease(ble_debug_send_lock);                \
    } while(0)

static void qw_ble_debug_code(ble_debug_data_t *rx_data, ble_debug_data_t *tx_data);
/*==================================================================
    ble测试指令内部链表操作
==================================================================*/

// ble测试指令链表初始化
static void ble_debug_data_list_init(ble_debug_list_t *list)
{
    list->head = NULL;
    list->tail = NULL;
    list->size = 0;
}

// ble测试指令链表插入数据
static bool ble_debug_data_list_insert(ble_debug_list_t *list, uint8_t *data, uint8_t length)
{
    ble_debug_data_node_t *new_node = (ble_debug_data_node_t *)QW_MALLOC(sizeof(ble_debug_data_node_t));
    if (new_node == NULL) {
        return false;
    }
    new_node->data = (uint8_t *)QW_MALLOC(length);
    if (new_node->data == NULL) {
        QW_FREE(new_node);
        return false;
    }
    memcpy(new_node->data, data, length);
    new_node->length = length;
    new_node->next = NULL;

    if (list->tail == NULL) {
        list->head = new_node;
    } else {
        list->tail->next = new_node;
    }
    list->tail = new_node;
    list->size++;
    return true;
}

// 获取ble测试指令链表中的第一个节点
static bool ble_debug_data_list_get(ble_debug_list_t *list, ble_debug_data_node_t **node)
{
    if (list->head == NULL) {
        return false;
    }
    *node = list->head;
    list->head = list->head->next;
    if (list->head == NULL) {
        list->tail = NULL;
    }
    list->size--;
    return true;
}

// 释放ble测试指令节点
static void ble_debug_data_node_free(ble_debug_data_node_t *node)
{
    if (node != NULL) {
        QW_FREE(node->data);
        node->data = NULL;
        QW_FREE(node);
        node = NULL;
    }
}

// 释放ble测试指令链表
static void ble_debug_data_list_free(ble_debug_list_t *list)
{
    ble_debug_data_node_t *current = list->head;
    while (current != NULL) {
        ble_debug_data_node_t *next = current->next;
        ble_debug_data_node_free(current);
        current = next;
    }
    list->head = NULL;
    list->tail = NULL;
    list->size = 0;
}

/*==================================================================
    ble测试指令内部调用接口
==================================================================*/

// 释放信号量给ble测试指令线程启动处理
static void ble_debug_data_deal_notify(void)
{
    if (ble_debug_thread)
    {
        rt_sem_release(ble_debug_data_sem);
    }
}

// 从ble测试指令链表中获取数据
static bool ble_debug_data_get(ble_debug_data_t *data, ble_debug_data_node_t **node)
{
    // ble_debug_data_node_t *node = NULL;
    if (ble_debug_data_list_get(&ble_debug_data_list, node))
    {
        data->data = (*node)->data;
        data->length = (*node)->length;
        // rt_hexdump("ble_debug_data_get: ", 16, data->data, data->length);
        // ble_debug_data_node_free(node);
        return true;
    }
    else
    {
        return false;
    }
}

// ble测试通用指令
static uint8_t ble_debug_general_cmd(uint8_t *rx_data, uint8_t rx_length, uint8_t *tx_data, uint8_t *tx_length)
{
    uint8_t rsp_code = RSP_SUCCESS;
    static uint8_t cmd_count = 0;
    switch (rx_data[0])
    {
        case BLE_DEBUG_GENERAL_CMD_GET_DEVICE_INFO:
        {
            *tx_length = 0;
            uint16_t soft_version = get_major_app_version();
            tx_data[*tx_length] = soft_version / 100;
            (*tx_length)++;
            tx_data[*tx_length] = soft_version % 100;
            (*tx_length)++;
            uint16_t hard_version = get_hardware_version();
            tx_data[*tx_length] = hard_version / 100;
            (*tx_length)++;
            tx_data[*tx_length] = hard_version % 100;
            (*tx_length)++;
            uint8_t ble_addr[BD_ADDR_LEN] = {0};
            g_device_get_ble_mac_addr(ble_addr, BD_ADDR_LEN);

            memcpy(&tx_data[*tx_length], ble_addr, BD_ADDR_LEN);
            *tx_length += BD_ADDR_LEN;
            rsp_code = RSP_SUCCESS;
        }
        break;

        case BLE_DEBUG_GENERAL_CMD_COUNT:
        {
            *tx_length = 0;
            tx_data[*tx_length] = cmd_count;
            cmd_count++;
            (*tx_length)++;
            if (cmd_count == 0) // 0表示溢出，从1开始?
            {
                cmd_count = 1;  // 仅上电初次允许获取计数值为0，后续因累加导致超过255，应从1开始，避免上位机误认为重启
            }
            rsp_code = RSP_SUCCESS;
        }
        break;

        default:
        {
            rsp_code = RSP_PAYLOAD_TITLE_WRONG;
        }
        break;
    }
    return rsp_code;
}

static uint8_t ble_debug_extral_cmd(uint8_t *rx_data, uint8_t rx_length, uint8_t *tx_data, uint8_t *tx_length)
{
    uint8_t rsp_code = RSP_SUCCESS;
    uint8_t rx_data_cnt = 0;

    switch (rx_data[rx_data_cnt++])
    {
        case BLE_DEBUG_EXTRAL_CMD_BAROMETER:
        {
            // 气压计调试指令
            *tx_length = 0;
            //TODO: 气压计调试指令
            rsp_code = baro_cmd_process(&rx_data[rx_data_cnt], rx_length - rx_data_cnt, tx_data, tx_length);
        }
        break;

        case BLE_DEBUG_EXTRAL_CMD_BUTTON:
        {
            // 按键调试指令
            *tx_length = 0;
            rsp_code = button_cmd_process(&rx_data[rx_data_cnt], rx_length - rx_data_cnt, tx_data, tx_length);
        }
        break;

        case BLE_DEBUG_EXTRAL_CMD_GPS:
        {
            // GPS调试指令
            *tx_length = 0;
            rsp_code = gps_cmd_process(&rx_data[rx_data_cnt], rx_length - rx_data_cnt, tx_data, tx_length);
        }
        break;

        case BLE_DEBUG_EXTRAL_CMD_UI:
        {
            // UI调试指令
            *tx_length = 0;
            rsp_code = ui_cmd_process(&rx_data[rx_data_cnt], rx_length - rx_data_cnt, tx_data, tx_length);
        }
        break;

        // case BLE_DEBUG_EXTRAL_CMD_SENSOR:
        // {
        //     // 传感器调试指令
        //     *tx_length = 0;
        //     rsp_code = sensor_test_cmd_process(&rx_data[rx_data_cnt], rx_length - rx_data_cnt, tx_data, tx_length);
        // }
        // break;

        default:
        {
            rsp_code = RSP_PAYLOAD_TITLE_WRONG;
        }
        break;
    }
    return rsp_code;
}

// 错误回复接口
static void ble_debug_data_err_rsp(ble_debug_data_t *tx_data, uint8_t err_code)
{
    tx_data->data[0] = err_code;
    tx_data->data[1] = 0;
    // tx_data->length = 2;
}

/*==================================================================
    ble测试指令开放接口
==================================================================*/

// 缓存ble测试指令并通知处理线程
void ble_debug_data_deal(uint8_t *data, uint8_t length)
{
    ble_debug_data_list_insert(&ble_debug_data_list, data, length);
    ble_debug_data_deal_notify();
}

// 已被开启ble 测试通道 notify
void ble_debug_channel_status_set(void)
{
    ble_debug_chn_sta = true;
}

// 关闭ble 测试通道 notify
void ble_debug_channel_status_clear(void)
{
    ble_debug_chn_sta = false;
}

// 查询ble 测试通道 notify 状态
bool ble_debug_channel_is_start(void)
{
    return ble_debug_chn_sta;
}

/************************************************************************
 *@function:int32_t ble_debug_data_notif(uint8_t cmd_type, uint8_t *data, uint8_t length)
 *@brief:ble上报数据接口
 *@param: cmd_type: 指令类型 BLE_DEBUG_DATA_CMD_ENUM, data:数据指针, length:数据长度
 *@return:0 成功 -1 异常
*************************************************************************/
int32_t ble_debug_data_notif(uint8_t cmd_type, uint8_t *data, uint8_t length)
{
    if ((ble_is_connected()) && ble_debug_channel_is_start())
    {
#ifdef ENABLE_PRODUCTION_TEST
        if(cmd_type != 0xFF)
        {
            notif_send_data[notif_send_data_len] = BLE_DEBUG_UPDATE_HEAD;
            notif_send_data_len++;
            notif_send_data[notif_send_data_len] = cmd_type;
            notif_send_data_len++;
            notif_send_data[notif_send_data_len] = length;
            notif_send_data_len++;
        }
#else
        notif_send_data[notif_send_data_len] = BLE_DEBUG_UPDATE_HEAD;
        notif_send_data_len++;
        notif_send_data[notif_send_data_len] = cmd_type;
        notif_send_data_len++;
        notif_send_data[notif_send_data_len] = length;
        notif_send_data_len++;
#endif
        memcpy(&notif_send_data[notif_send_data_len], data, length);
        notif_send_data_len += length;
        // rt_hexdump("ble_debug_data_notif: ", 16, notif_send_data, notif_send_data_len);
        BLE_DEBUG_SEND_ENTER_CRITICAL();
        lb55x_ble_nus_debug_data_send(notif_send_data, (uint16_t *)&notif_send_data_len);
        BLE_DEBUG_SEND_EXIT_CRITICAL();
        notif_send_data_len = 0;
        memset(notif_send_data, 0, BLE_DEBUG_DATA_MAX_LEN);
    }
    else
    {
        // rt_kprintf("ble_debug_data_notif: ble is not connected or ble debug channel is not start\n");
        return -1;
    }
    return 0;
}

/*==================================================================
    ble测试指令线程操作
==================================================================*/

static void ble_debug_entry(void *parameter)
{
    bool data_get_flag = false;
    uint32_t delay_time = RT_WAITING_FOREVER;
    ble_debug_data_node_t *current_node = NULL;
    while (1) {
        rt_sem_take(ble_debug_data_sem, delay_time);
        if ((ble_is_connected()) &&
            ble_debug_channel_is_start())
        {
            if (delay_time == RT_WAITING_FOREVER)
            {
                delay_time = BLE_DEBUG_THREAD_DELAY;
            }
            if (app_is_disconnect)
            {
                app_is_disconnect = false;
            }
            data_get_flag = ble_debug_data_get(&l_rx_data, &current_node);
            if (data_get_flag)
            {
                qw_ble_debug_code(&l_rx_data, &l_tx_data);
                BLE_DEBUG_SEND_ENTER_CRITICAL();
                lb55x_ble_nus_debug_data_send(l_tx_data.data, (uint16_t *)&l_tx_data.length);
                BLE_DEBUG_SEND_EXIT_CRITICAL();
                ble_debug_data_node_free(current_node);
            }
        }
        else
        {
            if (!app_is_disconnect)
            {
                delay_time = RT_WAITING_FOREVER;
                ble_debug_data_list_free(&ble_debug_data_list);
                app_is_disconnect = true;
                notif_send_data_len = 0;
                memset(notif_send_data, 0, BLE_DEBUG_DATA_MAX_LEN);
            }
        }
    }
}

int ble_debug_init(void)
{
    // 初始化ble测试指令链表
    ble_debug_data_list_init(&ble_debug_data_list);

    // 创建ble测试指令信号量
    ble_debug_data_sem = rt_sem_create("ble_debug_data_sem", 0, RT_IPC_FLAG_FIFO);

    ble_debug_thread = rt_thread_create("ble_debug_thread", ble_debug_entry, NULL, 4096, 5, RT_THREAD_TICK_DEFAULT);
    rt_thread_startup(ble_debug_thread);
    return RT_EOK;
}

/*==================================================================
    ble测试指令集处理
    APP -> WR02 :
                  (BLE_DEBUG_DATA_CMD)
    短指令头(0x51) | 指令类型(1 byte) | payload数据长度(1byte) | payload
    例： 0x51           0x01                0x01                0x01 (获取设备基本信息)

    WR02 -> APP :
    (BLE_DEBUG_DATA_RSP)
    指令回复状态(1 byte) | payload数据长度(1byte) | payload
                                                (软件版本 0.42) (硬件版本 1.00)         (ble addr 小端)
    例： 0x01               0x01                    0x00 0x2a    0x01 0x00      0x12 0x34 0x56 0x78 0x9a 0xbc
==================================================================*/

static void qw_ble_debug_code(ble_debug_data_t *rx_data, ble_debug_data_t *tx_data)
{
    if (rx_data->length < 2)
    {
        ble_debug_data_err_rsp(tx_data, RSP_INVALID);
    }
#ifdef ENABLE_PRODUCTION_TEST
    else if (rx_data->data[0] == 'A' && rx_data->data[1] == 'T')
    {
        // 处理产线测试AT命令
        uint16_t response_len = 0;
        handle_production_test(rx_data->data, rx_data->length, tx_data->data, &response_len);
        tx_data->length = response_len;
        return;  // 直接返回，不进行后续处理
    }
#endif
    else if (rx_data->data[BLE_DEBUG_RECV_HEAD] == BLE_DEBUG_CODE_HEAD)
    {
        switch (rx_data->data[BLE_DEBUG_RECV_CMD])
        {
            case CMD_GENERAL:
            {
                tx_data->data[BLE_DEBUG_RESP_STA] = ble_debug_general_cmd(&rx_data->data[BLE_DEBUG_RECV_PAYLOAD_TITLE], rx_data->data[BLE_DEBUG_RECV_PAYLOAD_LEN], &tx_data->data[BLE_DEBUG_RESP_PAYLOAD], &tx_data->data[BLE_DEBUG_RESP_PAYLOAD_LEN]);
            }
            break;

            case CMD_COMMON_TIME:
            {
                // 通用时间指令
                tx_data->data[BLE_DEBUG_RESP_STA] = ble_common_time_debug_cmd(&rx_data->data[BLE_DEBUG_RECV_PAYLOAD_TITLE], rx_data->data[BLE_DEBUG_RECV_PAYLOAD_LEN], &tx_data->data[BLE_DEBUG_RESP_PAYLOAD], &tx_data->data[BLE_DEBUG_RESP_PAYLOAD_LEN]);
            }
            break;

            case CMD_COMMON_SYS:
            {
                // 通用系统指令
                tx_data->data[BLE_DEBUG_RESP_STA] = ble_common_sys_debug_cmd(&rx_data->data[BLE_DEBUG_RECV_PAYLOAD_TITLE], rx_data->data[BLE_DEBUG_RECV_PAYLOAD_LEN], &tx_data->data[BLE_DEBUG_RESP_PAYLOAD], &tx_data->data[BLE_DEBUG_RESP_PAYLOAD_LEN]);
            }
            break;

            case CMD_EXTRAL:
            {
                // 额外指令
                tx_data->data[BLE_DEBUG_RESP_STA] = ble_debug_extral_cmd(&rx_data->data[BLE_DEBUG_RECV_PAYLOAD_TITLE], rx_data->data[BLE_DEBUG_RECV_PAYLOAD_LEN], &tx_data->data[BLE_DEBUG_RESP_PAYLOAD], &tx_data->data[BLE_DEBUG_RESP_PAYLOAD_LEN]);
            }
            break;

            default:
            {
                // 不支持的指令类型
                ble_debug_data_err_rsp(tx_data, RSP_CMD_WRONG);
            }
            break;
        }
    }
    else
    {
        // 指令头错误，不是ble测试指令，直接回复
        memcpy(&tx_data->data[BLE_DEBUG_RESP_PAYLOAD], rx_data->data, rx_data->length);
        tx_data->data[BLE_DEBUG_RESP_STA] = RSP_HEAD_WRONG;
        tx_data->data[BLE_DEBUG_RESP_PAYLOAD_LEN] = rx_data->length;
    }
    tx_data->length = tx_data->data[BLE_DEBUG_RESP_PAYLOAD_LEN] + BLE_DEBUG_RESP_PAYLOAD_LEN + 1;
    return;
}

/*==================================================================
    临时调试指令
==================================================================*/

#include "zip.h"
#include "unzip.h"
#include "qw_fs.h"
uint8_t zip_buffer[512];
char l_path[256];
char str[128] = {0};

// 压缩文件夹和压缩文件的两个接口示例
// create_folder_zip("0:/iGPSPORT/Dial/3_test.zip", "0:/iGPSPORT/Dial/3");
// create_file_zip();

// 压缩文件
void add_file_to_zip(zipFile zf, const char *base_path, const char *file_path, QW_FILINFO fileinfo)
{
    QW_FIL *fp = NULL;
    UINT bytes_read;
    FRESULT res;

    //打开源文件
    if (QW_OK != qw_f_open(&fp, file_path, QW_FA_READ))
    {
        // rt_kprintf("open file %s failed\n", file_path);
        return;
    }

    //生成压缩包内相对路径
    const char *zip_entry_name = file_path + strlen(base_path) + 1;

    //添加新文件到压缩包
    zip_fileinfo zi = {0};

    zi.tmz_date.tm_year = ((fileinfo.fdate & 0xFE00) >> 9) + 1980;
    zi.tmz_date.tm_mon = ((fileinfo.fdate & 0x01E0) >> 5) - 1;
    zi.tmz_date.tm_mday = (fileinfo.fdate & 0x001F);
    zi.tmz_date.tm_hour = ((fileinfo.ftime & 0xF800) >> 11);
    zi.tmz_date.tm_min = ((fileinfo.ftime & 0x07E0) >> 5);
    zi.tmz_date.tm_sec = (fileinfo.ftime & 0x001F) * 2;
    // zipOpenNewFileInZip(zf, zip_entry_name, &zi, NULL, 0, NULL, 0, NULL, Z_DEFLATED, Z_BEST_SPEED);
    zipOpenNewFileInZip3(zf, zip_entry_name, &zi, NULL, 0, NULL, 0, NULL, Z_DEFLATED, Z_BEST_SPEED, 0, \
        -12/*-MAX_WBITS*/, 5/*DEF_MEM_LEVEL*/, Z_DEFAULT_STRATEGY, NULL, 0);

    //分块读取并写入
    while (((res = qw_f_read(fp, zip_buffer, sizeof(zip_buffer), &bytes_read)) == QW_OK) &&
           (bytes_read > 0))
    {
        zipWriteInFileInZip(zf, zip_buffer, bytes_read);
    }

    //关闭文件
    zipCloseFileInZip(zf);
    qw_f_close(fp);
}

// 压缩文件夹
void add_folder_to_zip(zipFile zf, const char *base_path, const char *folder_path)
{
    QW_DIR dir;
    QW_FILINFO fileinfo;

    if (QW_OK != qw_f_opendir(&dir, folder_path))
    {
        // rt_kprintf("open dir %s failed\n", folder_path);
        return;
    }

    while (QW_OK == qw_f_readdir(&dir, &fileinfo) && fileinfo.fname[0])
    {
        sprintf(l_path, "%s/%s", folder_path, fileinfo.fname);

        if (fileinfo.fattrib & AM_DIR)
        {
            add_folder_to_zip(zf, base_path, l_path);
        }
        else
        {
            add_file_to_zip(zf, base_path, l_path, fileinfo);
        }
    }
}

static void create_folder_zip(const char *zip_path, const char *folder_path)
{
    zipFile zf;
    zf = zipOpen(zip_path, APPEND_STATUS_CREATE);
    if (!zf)
    {
        // rt_kprintf("create zip file failed\n");
        return;
    }

    add_folder_to_zip(zf, folder_path, folder_path);
    zipClose(zf, NULL);
}

static bool debug_log_n_trace_zip(const thread_pool_task* task_info)
{
    zipFile zf;
    QW_FILINFO fno, fileinfo;
    QW_DIR dir;

    if (QW_OK != qw_f_opendir(&dir, "0:/iGPSPORT/System/"))
    {
        return false;
    }

    while (QW_OK == qw_f_readdir(&dir, &fno) && fno.fname[0])
    {
        if (fno.fname[0] == 0)
        {
            break;
        }

        if (!(fno.fattrib & AM_DIR))
        {
            if (strncmp(fno.fname, "debug_log_", strlen("debug_log_")) == 0)
            {
                char full_path[256];
                sprintf(full_path, "0:/iGPSPORT/System/%s", fno.fname);
                qw_f_unlink(full_path);
            }
        }
    }

    uint32_t second = service_datetime_get_gmt_time() + service_get_timezone();
    sprintf(str, "0:/iGPSPORT/System/debug_log_%s.zip", qw_timestr(second));

    // rt_kprintf("create zip file %s\n", str);
    zf = zipOpen(str, APPEND_STATUS_CREATE);
    if (!zf)
    {
        return false;
    }

    // rt_kprintf("add folder to zip: 0:/iGPSPORT/System/log\n");
    add_folder_to_zip(zf, "0:/iGPSPORT/System", "0:/iGPSPORT/System/log");
    if (QW_OK == qw_f_stat("0:/iGPSPORT/System/trace.bin", &fileinfo))
    {
        // rt_kprintf("add file to zip: 0:/iGPSPORT/System/trace.bin\n");
        add_file_to_zip(zf, "0:/iGPSPORT/System", "0:/iGPSPORT/System/trace.bin", fileinfo);
    }
    // rt_kprintf("close zip file\n");
    zipClose(zf, NULL);
    // rt_kprintf("zip file %s created\n", str);

    return true;
}

static void debug_log_n_trace_zip_finish(void *arg, bool res)
{
    rt_kprintf("debug_log_n_trace_zip_finish!!!!!\n");
}

uint8_t buffer[512];
static void create_file_zip(void)
{
    // 1. 初始化压缩包
    zipFile zf;
    zf = zipOpen("0:/iGPSPORT/Dial/flag_test.zip", APPEND_STATUS_CREATE);

    if (!zf)
    {
        // rt_kprintf("create zip file failed\n");
        return;
    }

    // 2. 打开源文件
    QW_FIL *fp = NULL;
    if (QW_OK != qw_f_open(&fp, "0:/iGPSPORT/Dial/flag.txt", QW_FA_READ))
    {
        // rt_kprintf("open file failed\n");
        zipClose(zf, NULL);
        return;
    }

    //3. 添加文件条目
    zip_fileinfo zi = {0};
    int ret = 0;
    ret = zipOpenNewFileInZip3(zf, "flag.txt", &zi, NULL, 0, NULL, 0, NULL, Z_DEFLATED, Z_BEST_SPEED, 0, \
        -12/*-MAX_WBITS*/, 5/*DEF_MEM_LEVEL*/, Z_DEFAULT_STRATEGY, NULL, 0);
    // if (ZIP_OK != zipOpenNewFileInZip(zf, "flag.txt", &zi, NULL, 0, NULL, 0, NULL, Z_DEFLATED, Z_BEST_SPEED))
    if (ret != ZIP_OK)
    {
        // rt_kprintf("add file to zip failed!! %d\n", ret);
        qw_f_close(fp);
        zipClose(zf, NULL);
        return;
    }
    // rt_hexdump("zf:", 16, (uint8_t *)zf, sizeof(zf));
    // 4. 写入数据
    UINT bytes_read;
    while (QW_OK == qw_f_read(fp, buffer, sizeof(buffer), &bytes_read) && bytes_read > 0)
    {
        // rt_hexdump("zip buffer:", 16, buffer, bytes_read);
        zipWriteInFileInZip(zf, buffer, bytes_read);
    }
    zipCloseFileInZip(zf);
    qw_f_close(fp);
    zipClose(zf, NULL);
}

char create_path_dir[256];
static FRESULT create_path(const char *path)
{
    FRESULT res;
    // char dir[256];
    char *p = (char *)path;

    while ((p = strchr(p, '/')))
    {
        strncpy(create_path_dir, path, p - path + 1);
        create_path_dir[p - path + 1] = '\0';
        res = qw_f_mkdir(create_path_dir);
        // rt_kprintf("create_path_dir %s\r\n", create_path_dir);
        if (res != QW_OK && res != FR_EXIST)
        {
            // rt_kprintf("create dir %s failed, res %d\r\n", create_path_dir, res);
            return res;
        }
        p++;
    }
    return QW_OK;
}

//解压的接口示例
// unzip_ret = create_unzip_folder("0:/iGPSPORT/Dial/log_test.zip", "0:/iGPSPORT/Dial");
// unzip_ret = create_unzip_folder("0:/iGPSPORT/Dial/3_test.zip", "0:/iGPSPORT/Dial/3_test");

char filename_inzip[256];
char dirpath[256];
char output_patch[256];
uint8_t unzip_write_buffer[1024]; //根据RAM调整缓冲区大小
int create_unzip_folder(const char *zip_path, const char *dest_dir)
{
    unzFile zipfile = unzOpen(zip_path);
    if (!zipfile)
    {
        return -1;
    }

    unz_global_info global_info;
    if (UNZ_OK != unzGetGlobalInfo(zipfile, &global_info))
    {
        unzClose(zipfile);
        return -2;
    }

    // 确保目标根目录存在
    if (create_path(dest_dir) != 0)
    {
        unzClose(zipfile);
        return -7;
    }

    // 遍历压缩包内文件
    for (uLong i = 0; i < global_info.number_entry; i++)
    {
        unz_file_info file_info;

        //获取文件信息
        if (UNZ_OK != unzGetCurrentFileInfo(zipfile, &file_info, filename_inzip, \
                                    sizeof(filename_inzip), NULL, 0, NULL, 0))
        {
            unzClose(zipfile);
            return -3;
        }

        // 构建完整输出路径
        snprintf(output_patch, sizeof(output_patch), "%s/%s", dest_dir, filename_inzip);

        //跳过目录项（通过文件名末尾'/'判断）
        if (filename_inzip[strlen(filename_inzip) - 1] == '/')
        {
            // 目录项，创建目录结构
            if (create_path(output_patch) != 0)
            {
                unzClose(zipfile);
                return -8;
            }
            unzGoToNextFile(zipfile);
            continue;
        }


        // 创建父目录
        char *last_slash = strrchr(output_patch, '/');
        if (last_slash)
        {
            *last_slash = '\0';  // 临时截断字符串，只保留目录部分
            int ret = create_path(output_patch);  // 创建目录
            *last_slash = '/';   // 恢复原始路径

            if (ret != 0)
            {
                unzClose(zipfile);
                return -9;
            }
        }

        // 打开目标文件
        QW_FIL *fp = NULL;
        if (QW_OK != qw_f_open(&fp, output_patch, QW_FA_WRITE | QW_FA_CREATE_ALWAYS))
        {
            unzClose(zipfile);
            return -4;
        }

        // 开始解压当前文件
        if (UNZ_OK != unzOpenCurrentFile(zipfile))
        {
            qw_f_close(fp);
            unzClose(zipfile);
            return -5;
        }

        // 读取并写入数据
        int len;
        do {
            len = unzReadCurrentFile(zipfile, unzip_write_buffer, sizeof(unzip_write_buffer));
            if (len < 0)
            {
                break;
            }

            if (len > 0)
            {
                UINT bytes_written = 0;
                if (QW_OK != qw_f_write(fp, unzip_write_buffer, len, &bytes_written) || bytes_written != len)
                {
                    qw_f_close(fp);
                    unzCloseCurrentFile(zipfile);
                    unzClose(zipfile);
                    return -10;
                }
            }
        } while (len > 0);

        qw_f_close(fp);
        unzCloseCurrentFile(zipfile);

        if (len < 0 && len != UNZ_OK)
        {
            unzClose(zipfile);
            return -6;
        }

        unzGoToNextFile(zipfile);
    }
    unzClose(zipfile);
    return 0;
}
