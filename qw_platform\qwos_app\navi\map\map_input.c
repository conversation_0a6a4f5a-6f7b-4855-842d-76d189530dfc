﻿/************************************************************************​
*Copyright(c) 2024, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
/*
地图绘制模块，数据输入、参数计算
 */

#include "map_input.h"
#include "utility_geometry.h"
#include "data_convert.h"
#include "data_check.h"
#include "gps_bearing.h"
#include "cfg_header_def.h"
#if __NAVI_DEBUG
#include "igs_global.h"
#endif

#if __ZOOM_SCALE_9
// const static uint32_t s_zoom_scale[ZOOM_NUM] = {2000000, 1000000, 500000, 200000, 100000, 50000, 20000, 10000, 5000};
const static uint32_t s_zoom_scale[ZOOM_NUM] = {1000000, 500000, 250000, 100000, 50000, 25000, 10000, 5000, 2500};
#else
const static uint32_t s_zoom_scale[ZOOM_NUM] = {80000, 40000, 20000, 10000, 5000};
#endif
static map_input_gps_t s_map_gps; //GPS数据锁定，防止绘图过程中GPS数据被更新
static map_input_gps_t s_map_gps_view; //预览GPS坐标，用于地图拖拽

static map_input_data_t s_map_input; //输入结构
static map_input_data_t *sp_map_input = &s_map_input;

static navi_data_t s_map_data; //数据结构
static navi_data_t *sp_map_data = &s_map_data;

static uint16_t last_map_zoom = 0;
static uint16_t last_map_zoom_virtual= 0;
static uint16_t last_scale_len = 0;
static uint32_t last_zoom_distance = 0;

uint16_t get_last_zoom(void)
{
    return last_map_zoom;
}

uint16_t get_last_zoom_virtual(void)
{
    return last_map_zoom_virtual;
}

uint32_t get_last_zoom_distance(void)
{
    return last_zoom_distance;
}

// GPS数据锁定，防止绘图过程中被更改导致计算错乱
void navi_gps_lock(void)
{
    int32_t saved_lat = sp_map_input->saved_lat;
    int32_t saved_lon = sp_map_input->saved_lon;
    // s_map_gps.status = sp_map_input->status;
    if (sp_map_input->status && !position_invalid_check(&sp_map_input->gps_lat) && !position_invalid_check(&sp_map_input->gps_lon)) // 定位状态
    {
        s_map_gps.lat.value = sp_map_input->gps_lat.value;
        s_map_gps.lat.scale = sp_map_input->gps_lat.scale;
        s_map_gps.lon.value = sp_map_input->gps_lon.value;
        s_map_gps.lon.scale = sp_map_input->gps_lon.scale;
        s_map_gps.course = (0x7fff != sp_map_input->course ? sp_map_input->course : s_map_gps.course);
        set_navi_last_lat(sp_map_input->gps_lat.value);
        set_navi_last_lon(sp_map_input->gps_lon.value);
        s_map_gps.status = true;
    }
    else
    {
        s_map_gps.status = false;
        // if (position_invalid_check(&s_map_gps.lat) || position_invalid_check(&s_map_gps.lon)) // 上一次的GPS也无效
        {
            if (0x7fffffff != saved_lat && 0x7fffffff != saved_lon)
            {
                int_to_minmea(&s_map_gps.lat, saved_lat, 10000000);
                int_to_minmea(&s_map_gps.lon, saved_lon, 10000000);
            }
        }
    }
}

//获取锁定的GPS数据
map_input_gps_t* get_navi_gps(void)
{
    return &s_map_gps;
}

//预览GPS坐标，用于地图拖拽
map_input_gps_t* get_navi_gps_view(void)
{
    return &s_map_gps_view;
}

//地图GPS坐标，实际或预览
map_input_gps_t* get_navi_gps_map(bool updata_map)
{
    if (false != s_map_input.touch_map)
    {
        return get_navi_gps_view();
    }
    else if( s_map_data.leave_touch_map == true && updata_map == false)
    {
        return get_navi_gps_view();
    }
    else
    {
        return get_navi_gps();
    }
}

//获取地图相关数据输入结构
map_input_data_t* get_map_input(void)
{
    return &s_map_input;
}

//获取导航输入数据结构
navi_data_t* get_navi_data(void)
{
    return &s_map_data;
}

//获取缩放等级
uint16_t get_zoom(void)
{
    return sp_map_data->zoom;
}

int navi_gps_init(void)
{
    int ret = QW_OK;

    map_input_gps_t *p_navi_gps = get_navi_gps();
    p_navi_gps->lat.value = 0x7fffffff;
    p_navi_gps->lon.value = 0x7fffffff;
    p_navi_gps->lat.scale = 1;
    p_navi_gps->lon.scale = 1;
    p_navi_gps->course = 0;
    return ret;
}

#if __ZOOM_SCALE_9
//获取当前是哪个主文件
// extern uint16_t get_prifile(uint16_t zoom);
#endif

//获取当前是哪个子文件
// extern uint16_t get_subfile(uint16_t zoom);

static uint16_t get_prifile(uint16_t zoom)
{
    return 0;
}

static uint16_t get_subfile(uint16_t zoom)
{
    return 0;
}

//设置缩放等级
void set_zoom(uint16_t zoom, bool is_location)
{
    //计算新的zoom，但不直接改变zoom，先保存在zoom_update中
    sp_map_data->zoom_update = zoom;
#if __ZOOM_SCALE_9
    //参数改变导致地图数据需要update一次
    if (get_prifile(sp_map_data->zoom_update) != get_prifile(get_zoom_virtual())
            || get_subfile(sp_map_data->zoom_update) != get_subfile(get_zoom_virtual()))
#else
    if (get_subfile(sp_map_data->zoom_update) != get_subfile(get_zoom_virtual()))
#endif
    {
        // sp_map_data->draw_after_update = true; //需要在更新之后绘制
        sp_map_data->map_zoom_flage = true; // 地图缩放标志
    }
    // else
    {
        sp_map_data->draw_trigger = true; //触发一次绘制
    }

    if ((false == sp_map_data->map_lock && sp_map_data->processing == false) || is_location) //地图锁没有被占，表示此时更新（update）或绘制（draw）没有在进行中
    {
        sp_map_data->zoom = sp_map_data->zoom_update; //立即更新zoom
    }
}

//放大（拉近）（+）
uint16_t zoom_in(bool mediate)
{
    //计算新的zoom，但不直接改变zoom，先保存在zoom_update中
#if MAX_ZOOM_20K
    if(ZOOM_NUM > sp_map_data->zoom_update)
    {
        sp_map_data->zoom_update++;
    }
    else
    {
        sp_map_data->zoom_update = sp_map_data->with_route ? 0 : 1; //有导航线路时=0
    }
#else //最大比例尺10km，要跳过zoom1
    if (sp_map_data->with_route) //有导航线路时
    {
        if (0 == sp_map_data->zoom_update)
        {
            sp_map_data->zoom_update = 2;
        }
        else if (ZOOM_NUM > sp_map_data->zoom_update)
        {
            sp_map_data->zoom_update++;
        }
        else //ZOOM_NUM == sp_map_data->zoom_update
        {
            //empty branch
        }
    }
    else //无导航线路时
    {
        if (0 == sp_map_data->zoom_update)
        {
            sp_map_data->zoom_update = 2;
        }
        else if (ZOOM_NUM > sp_map_data->zoom_update)
        {
            sp_map_data->zoom_update++;
        }
        else //ZOOM_NUM == sp_map_data->zoom_update
        {
            //empty branch
        }
    }
#endif
    //参数改变导致地图数据需要update一次
    if (get_prifile(sp_map_data->zoom_update) != get_prifile(get_zoom_virtual())
            || get_subfile(sp_map_data->zoom_update) != get_subfile(get_zoom_virtual()))
    {
        if (mediate == false)
        {
            sp_map_data->map_zoom_flage = true; // 地图缩放标志
        }
        // sp_map_data->draw_after_update = true; //需要在更新之后绘制
    }
    // else
    {
        sp_map_data->draw_trigger = true; //触发一次绘制
    }

    if (false == sp_map_data->map_lock && sp_map_data->processing == false) //地图锁没有被占，表示此时更新（update）或绘制（draw）没有在进行中
    {
        sp_map_data->zoom = sp_map_data->zoom_update; //立即更新zoom
    }

    return sp_map_data->zoom;
}

//缩小（推远）（-）
//is_press_machine_button: 1:按下;0:未按下 (如果是通过机械按键进行缩放，那么不需要限制)
uint16_t zoom_out(bool is_press_machine_button, bool mediate)
{
    navi_data_t* p_map_data = get_navi_data();
    sp_map_data->no_transition_effect = false;

    //计算新的zoom，但不直接改变zoom，先保存在zoom_update中
#if MAX_ZOOM_20K
    uint16_t start_zoom = sp_map_data->with_route ? 0 : 1;

    if(start_zoom < sp_map_data->zoom_update)//有导航线路时0<
    {
        sp_map_data->zoom_update--;
    }
    else
    {
        sp_map_data->zoom_update = ZOOM_NUM;
    }
#else //最大比例尺10km，要跳过zoom1
    if (sp_map_data->with_route) //有导航线路时
    {
        if (0 == sp_map_data->zoom_update && is_press_machine_button)
        {
            sp_map_data->zoom_update = ZOOM_NUM;
            sp_map_data->no_transition_effect = true; //不显示过渡效果（从10km或全景比例尺zoom_out到50m比例尺的操作时，绘制中间帧会错乱，因为坐标转换会超出int16_t范围）
        }
        else if (2 < sp_map_data->zoom_update)
        {
            sp_map_data->zoom_update--;
        }
        else //2 >= sp_map_data->zoom_update
        {
            // if (is_press_machine_button)
            {
                sp_map_data->zoom_update = 0;
            }
        }
    }
    else //无导航线路时
    {
        if (0 == sp_map_data->zoom_update)
        {
            sp_map_data->zoom_update = ZOOM_NUM;
            sp_map_data->no_transition_effect = true; //不显示过渡效果（从10km或全景比例尺zoom_out到50m比例尺的操作时，绘制中间帧会错乱，因为坐标转换会超出int16_t范围）

        }
        else if (2 < sp_map_data->zoom_update)
        {
            sp_map_data->zoom_update--;
        }
        else //2 >= sp_map_data->zoom_update
        {
            if (is_press_machine_button)
            {
                sp_map_data->zoom_update = ZOOM_NUM;
                sp_map_data->no_transition_effect = true; //不显示过渡效果（从10km或全景比例尺zoom_out到50m比例尺的操作时，绘制中间帧会错乱，因为坐标转换会超出int16_t范围）
            }
            //empty branch
        }
    }
#endif
    //参数改变导致地图数据需要update一次
    if (get_prifile(sp_map_data->zoom_update) != get_prifile(get_zoom_virtual())
            || get_subfile(sp_map_data->zoom_update) != get_subfile(get_zoom_virtual()))
    {
        if (mediate == false)
        {
            sp_map_data->map_zoom_flage = true; // 地图缩放标志
        }
        // sp_map_data->draw_after_update = true; // 需要在更新之后绘制
    }
    // else
    {
        sp_map_data->draw_trigger = true; //触发一次绘制
    }

    if (false == sp_map_data->map_lock && sp_map_data->processing == false) //地图锁没有被占，表示此时更新（update）或绘制（draw）没有在进行中
    {
        sp_map_data->zoom = sp_map_data->zoom_update; //立即更新zoom
    }

#if __NAVI_DEBUG
    rt_kprintf("zoom=%d, zoom_update=%d\r\n", sp_map_data->zoom, sp_map_data->zoom_update);
#endif
    return sp_map_data->zoom;
}

uint8_t get_map_draw_trigger(void)
{
    navi_data_t* p_map_data = get_navi_data();

    return p_map_data->draw_trigger;
}

//获取比例尺距离
uint32_t get_zoom_dist(void)
{
    uint32_t zoom_dist = 0;
    navi_data_t* p_map_data = get_navi_data();
    map_input_data_t *p_map_input = get_map_input();

    if(0 < get_zoom() && ZOOM_NUM >= get_zoom()) //标准比例尺
    {
        zoom_dist = s_zoom_scale[get_zoom() - 1];
    }
    else //全景比例尺
    {
        zoom_dist = p_map_data->total_distance_scale * p_map_input->map_rang_x / 8;
    }

    return zoom_dist;
}

uint32_t get_zoom_dist_with_zoom(uint16_t zoom)
{
    uint32_t zoom_dist = 0;
    navi_data_t* p_map_data = get_navi_data();

    if(0 < zoom && ZOOM_NUM >= zoom) //标准比例尺
    {
        zoom_dist = s_zoom_scale[zoom - 1];
    }
    else //全景比例尺
    {
        zoom_dist = p_map_data->total_distance_scale * p_map_data->scale_len;
    }

    return zoom_dist;
}

static uint16_t get_scale_len_with_zoom(uint16_t zoom)
{
    map_input_data_t *p_map_input = get_map_input();
    uint16_t scale_len = 0 != p_map_input->map_rang_x ? p_map_input->map_rang_x / 8 : 466 / 8;

#if __ZOOM_SCALE_9
    if (enum_zoom_500m >= zoom && enum_zoom_2km <= zoom)
    {
        scale_len = scale_len * 5 / 4;
    }
    else if (enum_zoom_5km >= zoom && enum_zoom_20km <= zoom)
    {
        scale_len = scale_len * 25 / 16;
    }
#endif

    return scale_len;
}

//Get virtual zoom when total view.
uint16_t get_zoom_virtual(void)
{
    uint16_t zoom = 0;
    navi_data_t* p_map_data = get_navi_data();
    uint32_t dist_scale_0 = 0, dist_scale_1 = 0;

#if __ZOOM_SCALE_9
    uint16_t i;
    if (0 < get_zoom())
    {
        zoom = get_zoom();
    }
    else
    {
        for (i = 1; i < ZOOM_NUM; i++)
        {
            dist_scale_0 = get_zoom_dist_with_zoom(i) / get_scale_len_with_zoom(i);
            dist_scale_1 = get_zoom_dist_with_zoom(i + 1) / get_scale_len_with_zoom(i + 1);

            if (dist_scale_0 >= p_map_data->total_distance_scale && dist_scale_1 < p_map_data->total_distance_scale)
            {
                zoom = i;
                break;
            }
        }
    }

#if __LCD_SIZE_MAX
    // zoom += 1;
#endif
    return zoom;
#else
    return get_zoom();
#endif
}

//检查点是否在画布区域内
int canvas_check_point(const point16_t *point)
{
    int ret = false;

#if defined(IGS_DEV) || defined(SIMULATOR)
    if(point->x >= sp_map_input->map_origin.x && point->x < sp_map_input->map_origin.x + sp_map_input->map_rang_x
        && point->y >= sp_map_input->map_origin.y && point->y < sp_map_input->map_origin.y + sp_map_data->map_real_y)
    {
        ret = true;
    }
#else
    ret = true;
#endif

    return ret;
}

//检查矩形是否在画布区域内
int map_rect_check(const rect16_t *rect , bool is_check_way_name)
{
    int ret = false;

//#ifdef IGS_DEV

int16_t rect_high = sp_map_input->map_origin.y + sp_map_data->map_real_y;
int16_t rect_top = sp_map_input->map_origin.y + rect_high / 20;
int16_t rect_bottom = rect_high - rect_high / 10;
//rt_kprintf("rect_high = %d  rect_top = %d  rect_bottom = %d\n", rect_high, rect_top, rect_bottom);
if(is_check_way_name)
{
    if(rect->left >= sp_map_input->map_origin.x && rect->right < sp_map_input->map_origin.x + sp_map_input->map_rang_x
        && rect->top >=  rect_top && rect->bottom < rect_bottom)
    {
        ret = true;
    }
}else{
    if(rect->left >= sp_map_input->map_origin.x && rect->right < sp_map_input->map_origin.x + sp_map_input->map_rang_x
       && rect->top >= sp_map_input->map_origin.y && rect->bottom < sp_map_input->map_origin.y + sp_map_data->map_real_y)
    {
        ret = true;
    }
}
//#else
//    ret = true;
//#endif

    return ret;
}

//Check line segment whether or not across screen.
int canvas_line_check(const point16_t *point0, const point16_t *point1)
{
    int ret = false;
    map_input_data_t *p_map_input = get_map_input();
    navi_data_t* p_map_data = get_navi_data();
    point16_t top_left, top_right, bottom_left, bottom_right;

    //Input check.
    if(NULL == point0 || NULL == point1)
    {
        return ret;
    }

    if(canvas_check_point(point0) || canvas_check_point(point1)) //True if either point within screen range.
    {
        ret = true;
    }
     else
    {
        top_left.x = p_map_input->map_origin.x;
        top_right.x = p_map_input->map_origin.x + p_map_input->map_rang_x - 1;
        bottom_left.x = top_left.x;
        bottom_right.x = top_right.x;
        top_left.y = p_map_input->map_origin.y;
        top_right.y = top_left.y;
        bottom_left.y = p_map_input->map_origin.y + p_map_data->map_real_y - 1;
        bottom_right.y = bottom_left.y;

#if 0
        if(false != qw_segment_intersection(NULL, point0, point1, &top_left, &top_right)
            || false != qw_segment_intersection(NULL, point0, point1, &bottom_left, &bottom_right)
            || false != qw_segment_intersection(NULL, point0, point1, &top_left, &bottom_left)
            || false != qw_segment_intersection(NULL, point0, point1, &top_right, &bottom_right))
        {
            ret = true; //Interset with any border.
        }
#else
        //判断是否和对角线相交即可
        if (false != qw_segment_intersection(NULL, point0, point1, &top_left, &bottom_right) || \
            false != qw_segment_intersection(NULL, point0, point1, &bottom_left, &top_right) \
        )
        {
            ret = true;
        }
#endif
    }

    return ret;
}

//经纬度坐标转换为像素坐标
//返回值说明当前坐标是否有效
int position_to_pixel(point16_t *point, struct minmea_float *lat_minmea, struct minmea_float *lon_minmea)
{
    // map_input_gps_t *p_map_gps = get_navi_gps_map(false);
    map_input_gps_t *p_map_gps = get_navi_gps();
    map_input_data_t *p_map_input = get_map_input();
    navi_data_t* p_map_data = get_navi_data();
    int32_t org_lon = 0, org_lat = 0; //参考原点的坐标
    point16_t org_point = {0, 0}; //表示当前位置的屏幕坐标
    point16_t position_point = {0, 0};    //以参考原点为原点的线路上的点的相对坐标
    // point16_t zero_point = {0, 0};
    int32_t position_lat = 0, position_lon = 0; //输入坐标转换成的整型

    //Input check.
    if(NULL == point || NULL == lat_minmea || NULL == lon_minmea || position_invalid_check(lat_minmea) || position_invalid_check(lon_minmea))
    {
        return false;
    }

    position_lat = minmea_to_int(lat_minmea, (int32_t)COORDINATE_PRECISION * 10);
    position_lon = minmea_to_int(lon_minmea, (int32_t)COORDINATE_PRECISION * 10);
    if (false != sp_map_data->rotate && false == p_map_input->touch_map) //旋转开且不在触控模式（触控模式下强制北向朝上）
    {
        //获取当前位置的坐标为参考原点
        org_lat = minmea_to_int(&p_map_gps->lat, (int32_t)COORDINATE_PRECISION * 10);
        org_lon = minmea_to_int(&p_map_gps->lon, (int32_t)COORDINATE_PRECISION * 10);
        org_point.x = sp_map_input->map_origin.x + sp_map_input->map_rang_x / 2;
        org_point.y = sp_map_input->map_origin.y + sp_map_data->map_real_y * 2 / 3; //当前GPS定位点屏幕坐标(旋转开启时固定)
    }
    else //旋转关
    {
        if (0 == last_map_zoom && false == p_map_input->touch_map)  //全景且不在触控模式（触控模式下全景也可拖动，要用拖拽后的坐标为参考原点）
        {
            //全景显示时，以屏幕左下角加上线路显示偏移为参考原点
            org_lat = sp_map_data->swc_lat;
            org_lon = sp_map_data->swc_lon;
            org_point.x = sp_map_input->map_origin.x + sp_map_data->show_x_offset_left;
            org_point.y = sp_map_input->map_origin.y + sp_map_data->map_real_y - sp_map_data->show_y_offset_bottom;
        }
        else //标准比例尺或全景触控模式（触控模式下全景也可拖动，要用拖拽后的坐标为参考原点）
        {
            //获取当前位置的坐标为参考原点
            org_lat = minmea_to_int(&p_map_gps->lat, (int32_t)COORDINATE_PRECISION * 10);
            org_lon = minmea_to_int(&p_map_gps->lon, (int32_t)COORDINATE_PRECISION * 10);
            org_point.x = sp_map_input->map_origin.x + sp_map_input->map_rang_x / 2;
            org_point.y = sp_map_input->map_origin.y + sp_map_data->map_real_y / 2; //当前GPS定位点屏幕坐标(屏幕中央)
        }
    }

    //计算北向坐标系的相对于参考原点的偏移坐标
    if(0 != sp_map_data->point_scale)
    {
        position_point.x = (int16_t)((position_lon - org_lon) / (float)sp_map_data->point_scale * 10);
        position_point.y = (int16_t)((position_lat - org_lat) / (float)sp_map_data->point_scale * 10);
    }

    //纬度收缩（纬度越高每度经线距离越短）
    if (0 == last_map_zoom) //全景时使用线路经纬度计算
    {
        position_point.x = (int16_t)(position_point.x * COS(rad((float)sp_map_data->swc_lat / (float)MINMEA_SCALE)));

    }
    else //标准比例尺下使用当前GPS计算
    {
        position_point.x = (int16_t)(position_point.x * COS(rad((float)p_map_gps->lat.value / (float)p_map_gps->lat.scale)));
    }

    //偏移坐标转换为屏幕坐标
    point->x = org_point.x + position_point.x;
    point->y = org_point.y - position_point.y;

    if (false != sp_map_data->rotate) //旋转开启
    {
        //坐标旋转，北向坐标转换为航向坐标
        qw_position_rotate(point, point, &org_point, p_map_gps->course);
    }

    //保存当前定位点屏幕坐标
    sp_map_data->org_point.x = org_point.x;
    sp_map_data->org_point.y = org_point.y;

    //返回坐标是否在屏幕内
    return canvas_check_point(point);
}

int pixel_to_position(struct minmea_float *lat_minmea, struct minmea_float *lon_minmea, point16_t *point)
{
    map_input_gps_t *p_map_gps = get_navi_gps_map(false);
    map_input_data_t *p_map_input = get_map_input();
    navi_data_t* p_map_data = get_navi_data();
    int32_t org_lon = 0, org_lat = 0; //参考原点的坐标
    point16_t org_point = {0, 0}; //表示当前位置的屏幕坐标
    point16_t position_point = {0, 0};    //以参考原点为原点的线路上的点的相对坐标
    int32_t position_lat = 0, position_lon = 0; //输出坐标转换成的整型
    point16_t point_tmp;

    //Input check.
    if(NULL == point || NULL == lat_minmea || NULL == lon_minmea)
    {
        return false;
    }

    //获取当前位置的坐标为参考原点
    org_lat = minmea_to_int(&p_map_gps->lat, (int32_t)COORDINATE_PRECISION * 10);
    org_lon = minmea_to_int(&p_map_gps->lon, (int32_t)COORDINATE_PRECISION * 10);

    if (false != sp_map_input->rotate && false == p_map_input->touch_map) //旋转开
    {
        org_point.x = sp_map_input->map_origin.x + sp_map_input->map_rang_x / 2;
        org_point.y = sp_map_input->map_origin.y + sp_map_data->map_real_y * 2 / 3; //当前GPS定位点屏幕坐标(旋转开启时固定)

        //坐标旋转，北向坐标转换为航向坐标
        qw_position_rotate(&point_tmp, point, &org_point, p_map_gps->course * (-1));
    }
    else
    {
        org_point.x = p_map_input->map_origin.x + p_map_input->map_rang_x / 2;
        org_point.y = p_map_input->map_origin.y + p_map_data->map_real_y / 2; //当前GPS定位点屏幕坐标(屏幕中央)
        memcpy(&point_tmp, point, sizeof(point16_t));
    }

    //计算北向坐标系的相对于参考原点的偏移坐标
    if(0 != sp_map_data->point_scale)
    {
        position_point.x = point_tmp.x - org_point.x;
        position_point.y = point_tmp.y - org_point.y;

        //纬度收缩（纬度越高每度经线距离越短）
        position_point.x = (int16_t)(position_point.x / COS(rad((float)p_map_gps->lat.value / (float)p_map_gps->lat.scale)));

        position_lon = (int32_t)((int64_t)position_point.x * sp_map_data->point_scale / 10);
        position_lat = (int32_t)((int64_t)position_point.y * sp_map_data->point_scale / 10);
    }

    //偏移坐标转换为实际坐标
    position_lat = org_lat - position_lat;
    position_lon += org_lon;
    int_to_minmea(lat_minmea, position_lat, MINMEA_SCALE);
    int_to_minmea(lon_minmea, position_lon, MINMEA_SCALE);

    //返回坐标是否在屏幕内
    return canvas_check_point(point);
}

int position_to_pixel_int(point16_t *point, int32_t lat, int32_t lon)
{
    struct minmea_float lat_minmea;
    struct minmea_float lon_minmea;

    int_to_minmea(&lat_minmea, lat, MINMEA_SCALE);
    int_to_minmea(&lon_minmea, lon, MINMEA_SCALE);
    return position_to_pixel(point, &lat_minmea, &lon_minmea);
}

int position_to_pixel_float(point16_t *point, double lat, double lon)
{
    return position_to_pixel_int(point, (int32_t)(lat * MINMEA_SCALE), (int32_t)(lon * MINMEA_SCALE));
}

//导航数据结构初始化
int map_data_init(void)
{
    int ret = QW_OK;

// #if defined(IGS_DEV) || defined(SIMULATOR)
// 	sp_map_data->scale_len = 80;
// #else
    sp_map_data->scale_len = sp_map_input->map_rang_x / 8;
// #endif
    sp_map_data->map_zoom_flage = false;
    set_zoom(DEFAULT_ZOOM, false);

    //TODO
    return ret;
}

//导航GPS结构初始化
int map_gps_init(void)
{
    int ret = QW_OK;

    map_input_gps_t *p_map_gps = get_navi_gps();
    p_map_gps->lat.value = 0x7fffffff;
    p_map_gps->lon.value = 0x7fffffff;
    p_map_gps->lat.scale = 1;
    p_map_gps->lon.scale = 1;
    p_map_gps->course = 0;
    return ret;
}

static uint16_t get_scale_len(void)
{
    map_input_data_t *p_map_input = get_map_input();
// #if defined(IGS_DEV) || defined(SIMULATOR)
// 	uint16_t scale_len = 80;
// #else
    uint16_t scale_len = p_map_input->map_rang_x / 8;
// #endif
    uint16_t zoom = get_zoom();

#if __LCD_SIZE_MAX
    scale_len = p_map_input->map_rang_x / 8;
#endif

#if __ZOOM_SCALE_9
    if (enum_zoom_500m >= zoom && enum_zoom_2km <= zoom)
    {
        scale_len = scale_len * 5 / 4;
    }
    else if (enum_zoom_5km >= zoom && enum_zoom_20km <= zoom)
    {
        scale_len = scale_len * 25 / 16;
    }
#endif

    return scale_len;
}

//导航画布参数计算
int map_canvas_calc(void)
{
    int ret = QW_OK;
    map_input_data_t *p_map_input = get_map_input();
    navi_data_t* p_map_data = get_navi_data();
    uint32_t x_distance;       //x轴每个像素点对应的距离 *100m
    uint32_t y_distance;       //y轴
    struct minmea_float latitude0, longitude0, latitude1, longitude1;
    float distance;
    uint16_t xsize;
    uint16_t ysize;

    if(p_map_data->map_zoom_flage == false)
    {
        last_map_zoom = get_zoom();
        last_map_zoom_virtual = get_zoom_virtual();
        last_scale_len = get_scale_len();
        last_zoom_distance = get_zoom_dist();
    }

    //比例尺长度
    p_map_data->scale_len = last_scale_len;

    p_map_data->map_real_y = p_map_input->map_rang_y;
    // if (p_map_input->cur_page)
    // {
    //     if (false == p_map_input->route_elevation || false != p_map_input->touch_map ||  !p_map_input->valid_elevation)
    //     {
    //         p_map_data->map_real_y = p_map_input->map_rang_y;
    //     }
    //     else
    //     {
    //         p_map_data->map_real_y = p_map_input->map_rang_y - ROUTE_ELEVATION_HEIGHT;
    //     }
    // }
    // else
    // {
    //     if (p_map_input->touch_map == false && p_map_input->climb_map_enable)
    //     {
    //         p_map_data->map_real_y = p_map_input->map_rang_y - ROUTE_ELEVATION_HEIGHT - ROUTE_CLIMB_INFO_HEIGHT - ROUTE_CLIMBE_PROCESS_HIGHT;
    //     }
    //     else
    //     {
    //         p_map_data->map_real_y = p_map_input->map_rang_y;
    //     }
    // }

    if(0 == last_map_zoom) //全景比例尺
    {
        xsize = p_map_input->map_rang_x - MAP_X_OFFSET * 2;
        ysize = p_map_data->map_real_y - MAP_Y_OFFSET_TOP - MAP_Y_OFFSET_BOTTOM;// - 60;

        //计算X轴的水平距离(计算经度之差)longitude:经度
        int_to_minmea(&latitude0, p_map_data->nec_lat, (int32_t)COORDINATE_PRECISION * 10); //线路精度10000000，地图精度1000000
        int_to_minmea(&longitude0, p_map_data->swc_lon, (int32_t)COORDINATE_PRECISION * 10);
        int_to_minmea(&latitude1, p_map_data->nec_lat, (int32_t)COORDINATE_PRECISION * 10);
        int_to_minmea(&longitude1, p_map_data->nec_lon, (int32_t)COORDINATE_PRECISION * 10);
        latitude1.value = latitude0.value;
        util_pos_simple_distance_get(&distance, &latitude0, &longitude0, &latitude1, &longitude1);   // 100*m
        x_distance =  (uint32_t)(distance / xsize);

        //计算Y轴的垂直距离(计算纬度之差)latitude:纬度
        int_to_minmea(&latitude0, p_map_data->nec_lat, (int32_t)COORDINATE_PRECISION * 10); //线路精度10000000，地图精度1000000
        int_to_minmea(&longitude0, p_map_data->swc_lon, (int32_t)COORDINATE_PRECISION * 10);
        int_to_minmea(&latitude1, p_map_data->swc_lat, (int32_t)COORDINATE_PRECISION * 10);
        int_to_minmea(&longitude1, p_map_data->swc_lon, (int32_t)COORDINATE_PRECISION * 10);
        longitude1.value = longitude0.value;
        util_pos_simple_distance_get(&distance, &latitude0, &longitude0, &latitude1, &longitude1);   // 100*m
        y_distance = (uint32_t)(distance / ysize);

        //确定distance_scale
        p_map_data->distance_scale = MAX(x_distance, y_distance);
        p_map_data->point_scale = POINT_SCALE_UNIT * p_map_data->distance_scale; //用每像素厘米数反算每像素经纬度（1000000 * 100）
        if(0 == p_map_data->rotate && 0 != p_map_data->point_scale) //旋转开启时不偏移
        {
            //x轴左右顶格,y轴需要偏移
            if (x_distance > y_distance)
            {
                p_map_data->center_align_offset_x = 0;
                p_map_data->center_align_offset_y = (int16_t)(ysize - (p_map_data->nec_lat - p_map_data->swc_lat) * 10 / p_map_data->point_scale) / 2;
            }
            else
            {
                p_map_data->center_align_offset_x = (int16_t)(xsize - (p_map_data->nec_lon - p_map_data->swc_lon) * 10 / p_map_data->point_scale
                    * (COS(rad((float)(p_map_data->nec_lat + p_map_data->swc_lat) / 20 / COORDINATE_PRECISION)))) / 2; //纬度收缩（纬度越高每度经线距离越短）
                p_map_data->center_align_offset_y = 0;
            }
        }
        p_map_data->total_distance_scale = p_map_data->distance_scale; //记录全景时的像素距离，用于标准比例尺缩放
        p_map_data->rotate = false; //North-upward in total view.
    }
    else //标准比例尺
    {
        if (0 != p_map_data->scale_len)
        {
            p_map_data->distance_scale = last_zoom_distance / p_map_data->scale_len;    // Calc cm per pixel.
            p_map_data->point_scale = POINT_SCALE_UNIT * p_map_data->distance_scale; // 用每像素厘米数反算每像素经纬度（1000000 * 100）
        }
        if(p_map_data->leave_touch_map == false)
        {
            p_map_data->rotate = p_map_input->rotate; //标准比例尺下旋转跟随设置
        }
    }

    //拖拽模式下强制北向朝上
    if (false != p_map_input->touch_map /*|| p_map_input->status == 0*/)
    {
        p_map_data->rotate = false;
    }

    //计算有效的显示范围
    if(0 == p_map_data->rotate && 0 == last_map_zoom) //旋转关闭且全景显示时加上线路偏移量
    {
        p_map_data->show_x_offset_left = MAP_X_OFFSET + p_map_data->center_align_offset_x;    //与左侧的距离
        p_map_data->show_y_offset_top = MAP_Y_OFFSET_TOP + p_map_data->center_align_offset_y;// + 20;  //与顶端的距离
        p_map_data->show_y_offset_bottom = MAP_Y_OFFSET_BOTTOM + p_map_data->center_align_offset_y;// + 10;  //与底端的距离
    }
    else
    {
        p_map_data->show_x_offset_left = MAP_X_OFFSET;    //与左侧的距离
        p_map_data->show_y_offset_top = MAP_Y_OFFSET_TOP;  //与顶端的距离
        p_map_data->show_y_offset_bottom = MAP_Y_OFFSET_BOTTOM;  //与底端的距离
    }

    return ret;
}

//检查经纬度是否有效
int check_point_valid(int32_t position_lat, int32_t position_lon)
{
    if (0x7fffffff != position_lat && 0x7fffffff != position_lon && (0 != position_lat || 0 != position_lon))
    {
        return true;
    }
    else
    {
        return false;
    }
}

void set_navi_route_arrived(void)
{
    navi_data_t* p_map_data = get_navi_data();
    p_map_data->route_arrived = true;
}

// void set_navi_router_arrived(router_navi_type_t type)
// {
//     if(type == enumROUTER_NAVI_TO_ROUTE || type == enumROUTER_NAVI_TO_START)
//     {
//         return;
//     }
//     else
//     {
//         set_navi_route_arrived();
//     }
// }

void clear_navi_route_arrived(void)
{
    navi_data_t* p_map_data = get_navi_data();
    p_map_data->route_arrived = false;
}
