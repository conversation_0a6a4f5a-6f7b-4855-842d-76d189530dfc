#ifndef NAVI_COMMON_H
#define NAVI_COMMON_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

#define NAVI_FILE_MAJOR_VERSION             0               //导航相关文件主版本号
#define NAVI_FILE_MINOR_VERSION             13              //导航相关文件次版本号
#define NAVI_ROUTE_WP_SAMPLES_NUM           300             //导航线路路点采样最大数量
#define NAVI_ROUTE_SEGMENTS_NUM             100             //导航线路分段最大数量
#define NAVI_TURN_WAYNAME_LEN               32              //导航转向路名长度（不包括'\0'）
#define NAVI_TURN_SEGMENTS_NUM              100             //导航转向分段最大数量
#define NAVI_ROUTE_CP_SAMPLES_NUM           300             //导航线路爬坡点采样最大数量
#define NAVI_ROUTE_CP_SEGMENTS_NUM          100             //导航线路爬坡点分段最大数量
#define NAVI_CLIMB_POS_SEGMENTS_NUM         20              //导航爬坡上坡分段最大数量
#define NAVI_CLIMB_NEG_SEGMENTS_NUM         20              //导航爬坡下坡分段最大数量
#define NAVI_CLIMB_SAMPLES_NUM              120             //导航爬坡采样点最大数量
#define NAVI_SIGN_NAME_LEN                  32              //导航标记点名字最大长度
#define NAVI_SIGN_SEGMENTS_NUM              20              //导航标记点分段最大数量

typedef struct _Bbox
{
    double lng_min;
    double lng_max;
    double lat_min;
    double lat_max;
} Bbox;

typedef struct _Dseg
{
    float dist_min;
    float dist_max;
} Dseg;

typedef struct _Range
{
    uint32_t start;
    uint32_t end;
} Range;

void bbox_update(Bbox *self, double lng_min, double lng_max, double lat_min, double lat_max);

void bbox_copy(Bbox *self, const Bbox *bbox);

void bbox_merge(Bbox *self, const Bbox *bbox);

void bbox_extend(Bbox *self, double lng, double lat);

uint8_t bbox_is_overlap(Bbox *self, const Bbox *bbox);

void dseg_update(Dseg *self, float dist_min, float dist_max);

void dseg_copy(Dseg *self, const Dseg *dseg);

uint8_t dseg_is_contain(Dseg *self, float dist);

void range_update(Range *self, uint32_t start, uint32_t end);

void range_copy(Range *self, const Range *range);

#ifdef __cplusplus
}
#endif

#endif