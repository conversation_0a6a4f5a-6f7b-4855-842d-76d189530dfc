[core]
	repositoryFormatVersion = 1
	filemode = false
	symlinks = false
	ignorecase = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[remote "origin"]
	url = ssh://yanxuqiang@********:29418/wr02
	fetch = +refs/heads/*:refs/remotes/origin/*
[manifest]
	platform = auto
[repo]
	worktree = true
	existingprojectcount = 0
	newprojectcount = 6
[extensions]
	preciousObjects = true
[branch "default"]
	remote = origin
	merge = refs/heads/develop
[repo "syncstate.main"]
	synctime = 2025-07-16T01:55:29.621981+00:00
	version = 1
[repo "syncstate.sys"]
	argv = ['E:\\\\Snowa\\\\.repo\\\\repo/main.py', '--repo-dir=E:\\\\Snowa\\\\.repo', '--wrapper-version=2.48', '--wrapper-path=C:\\\\Users\\\\<USER>\\\\bin\\\\repo', '--', 'sync']
[repo "syncstate.options"]
	jobs = 12
	outermanifest = true
	jobsnetwork = 1
	jobscheckout = 8
	mpupdate = true
	clonebundle = true
	retryfetches = 0
	prune = true
	repoverify = true
	quiet = false
	verbose = false
[repo "syncstate.remote.origin"]
	url = ssh://yanxuqiang@********:29418/wr02
	fetch = +refs/heads/*:refs/remotes/origin/*
[repo "syncstate.repo"]
	worktree = true
	existingprojectcount = 0
	newprojectcount = 6
[repo "syncstate.branch.default"]
	remote = origin
	merge = refs/heads/develop
