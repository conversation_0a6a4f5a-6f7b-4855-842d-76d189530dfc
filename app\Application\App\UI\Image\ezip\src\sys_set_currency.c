#include "lvgl.h" 

#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP ALIGN(4)
#endif

#ifndef eZIP_RGBARGB888A
#define eZIP_RGBARGB888A
#endif


LV_ATTRIBUTE_MEM_ALIGN_EZIP const  uint8_t sys_set_currency_map[] SECTION(".ROM3_IMG_EZIP.sys_set_currency") = { 
    0x00,0x00,0x05,0xb7,0x46,0x08,0x20,0x00,0x00,0x48,0x00,0x48,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0x03,0x00,0x00,0x00,0x94,0x00,0x00,0x02,0xe0,0x00,0x00,0x04,0xd4,
    0x3d,0xf3,0x89,0x89,0xab,0xfa,0xe2,0xf8,0xf7,0x3c,0xf8,0x95,0x1f,0xb8,0x6a,0x1b,0x2b,0x69,0x52,0xa4,0xcc,0x73,0x03,0x8b,0x9a,0x34,0x98,0x92,0x71,0x41,0xff,0xc8,
    0x46,0x53,0x13,0x1b,0xa1,0x26,0xb2,0x52,0xab,0x92,0x18,0x65,0xc1,0x40,0x6b,0x5b,0xa6,0xb5,0x71,0x64,0x98,0x8d,0x31,0xfe,0xab,0x89,0x69,0xb4,0x4d,0x04,0xdd,0xd8,
    0x45,0x17,0x42,0x23,0x2e,0x46,0xdb,0x58,0x36,0x6a,0xb0,0x89,0x33,0x43,0x4b,0x13,0x52,0xa9,0x29,0x3b,0x90,0x26,0xbc,0xeb,0xb9,0x03,0x0c,0x1d,0x66,0xde,0x0c,0xf3,
    0xde,0x9b,0x37,0xef,0xcd,0xbc,0x2f,0xb9,0x73,0x2f,0xf3,0xe7,0xfd,0xf9,0xbc,0x73,0xce,0x3d,0xf7,0x5c,0x24,0x38,0x48,0x6a,0x9f,0xf0,0x69,0x84,0x3d,0x24,0xd0,0x0c,
    0x82,0xca,0x6f,0x35,0x70,0xab,0xe7,0xb6,0x9d,0xdb,0x8e,0xe4,0x97,0x04,0xff,0x11,0x96,0x78,0x74,0x97,0xdb,0x2c,0xb7,0xb8,0x10,0x98,0x12,0x84,0xc9,0xa5,0x3a,0x44,
    0x67,0x83,0xb4,0x60,0xe5,0x35,0x95,0x14,0x90,0xaf,0x57,0xa8,0xf4,0x3f,0x74,0xf0,0x70,0xbf,0x00,0x9e,0x5e,0x85,0x61,0xf6,0x8e,0xc6,0xa1,0xe1,0xb2,0xf6,0x00,0xa3,
    0xd3,0x1f,0xd2,0xdf,0xae,0x03,0xd4,0x38,0x20,0x1a,0x15,0x0d,0x5d,0x44,0x38,0xc2,0xb6,0xd0,0x5a,0xe4,0xd3,0x7d,0xcd,0x37,0xf8,0x49,0x2c,0x4c,0xd7,0x1c,0x0f,0xa8,
    0xa9,0x5f,0x3c,0x4b,0x1a,0x5e,0xe3,0x33,0x3e,0x5f,0x02,0x63,0x1d,0xa1,0x2a,0x9c,0x8d,0x85,0x68,0xca,0x71,0x80,0x9a,0xfa,0xc4,0x4b,0x0a,0xe1,0x1d,0x76,0xa1,0xa7,
    0x4a,0x1e,0xe4,0x04,0x4e,0xc5,0x87,0xe9,0x9c,0x23,0x00,0x31,0x98,0xe7,0xd8,0x8d,0x4e,0xf0,0xb0,0x0d,0xce,0xd2,0x55,0x45,0xc1,0xb1,0xbf,0x3e,0xa0,0x44,0x49,0x00,
    0x3d,0x31,0x20,0x9a,0x34,0x0d,0xef,0xf3,0xb0,0x0b,0xce,0xd5,0x9c,0x06,0xbc,0x3c,0x1d,0xa6,0x31,0x5b,0x01,0xf9,0xfa,0xc4,0x5b,0x7c,0xc4,0x08,0x0f,0xb7,0xc0,0x05,
    0x62,0x0b,0xef,0x8a,0x0d,0xd1,0x68,0xd1,0x01,0xa9,0xc7,0xc5,0xa3,0x3c,0xb5,0x7e,0xca,0xf9,0xc8,0x11,0xb8,0x4c,0xf9,0x20,0x99,0x06,0xe4,0x1b,0x10,0x07,0x18,0xce,
    0x97,0x3c,0x7c,0x1c,0x2e,0x15,0xbb,0x5b,0x87,0x9e,0xbb,0x91,0xc9,0xcc,0xf7,0x55,0xce,0x60,0xbf,0x80,0xfb,0x35,0xc7,0x81,0xbb,0x2d,0x5b,0xe0,0x56,0x0c,0x5b,0x4e,
    0x40,0xbc,0x5b,0x26,0x70,0xa4,0x76,0xf0,0xc4,0x72,0x3e,0xdb,0x07,0x8a,0xc1,0x29,0x3c,0xc4,0xdd,0xb9,0x32,0x81,0xb3,0xa6,0x83,0x3c,0xc9,0x9c,0x34,0xed,0x62,0x12,
    0x0e,0x07,0xb6,0x01,0x94,0xa9,0x38,0xe3,0x6e,0x79,0x38,0xe3,0x56,0x0a,0x75,0xab,0x72,0x86,0x93,0x4c,0xb6,0x97,0x71,0xda,0x90,0x05,0x95,0x51,0x40,0xde,0x4c,0xf6,
    0xdc,0xb6,0xb6,0xc0,0x55,0x36,0x3b,0x95,0x57,0x0a,0x9c,0x95,0x25,0x1b,0x7a,0x36,0x6d,0x41,0x32,0x09,0x64,0xb3,0xfb,0xd5,0xcd,0x79,0x8e,0xa1,0xdc,0x68,0x09,0xf5,
    0xb2,0x9e,0x94,0xdf,0x82,0x38,0x43,0xae,0x34,0x38,0x49,0xd7,0xda,0x82,0x4e,0xd9,0x57,0xe7,0x5b,0x5b,0x19,0x59,0x3e,0x2c,0xce,0x4c,0xac,0xf4,0x77,0x26,0x6c,0xbd,
    0xa9,0xda,0x5d,0xed,0xa8,0x6d,0x68,0xb7,0x88,0x10,0x0e,0xf3,0xeb,0x47,0x94,0x67,0x55,0xfe,0x67,0xa1,0x0b,0xcf,0xfb,0xd1,0x20,0xe6,0xa3,0x67,0x4a,0xfa,0xf4,0xb7,
    0xfa,0x07,0xb1,0xcd,0x1f,0x34,0x7d,0x9c,0xc5,0x3a,0x3c,0xa2,0xeb,0x62,0xab,0x25,0x0b,0xd7,0xc1,0x91,0x92,0xd7,0xb0,0x66,0xc5,0x66,0x54,0xb3,0x00,0xbf,0xa2,0x57,
    0xec,0x32,0x52,0xcf,0x71,0x02,0x9c,0xd4,0xb5,0xfc,0x6c,0xfe,0x5a,0x48,0x60,0xaf,0xa2,0x53,0x02,0x38,0x61,0x34,0xee,0x94,0x55,0x3e,0x44,0x68,0x56,0xb2,0xd5,0x90,
    0x1d,0x58,0x26,0x2d,0xd9,0xce,0x54,0x06,0x20,0x59,0x60,0xf7,0xb8,0xa4,0xb4,0x53,0xd9,0xb8,0x35,0xe3,0x88,0xdd,0x07,0xe7,0xa8,0x3e,0x2d,0x0f,0x5a,0xdd,0xb7,0xb2,
    0x74,0xba,0xb5,0x7b,0xf6,0xb2,0x78,0xcd,0x51,0x53,0xfd,0xf0,0x8e,0x27,0x67,0xcd,0x96,0x6d,0xea,0xc9,0x84,0xcd,0x8a,0x5c,0xa4,0x90,0x49,0x62,0x1e,0x16,0x03,0x22,
    0x50,0xca,0xc5,0x94,0x65,0x1c,0xf5,0x3c,0x2a,0x47,0x45,0x91,0x51,0xbd,0xe0,0xe1,0xd0,0x01,0xe4,0xeb,0x15,0x2a,0xfb,0x5b,0xab,0x87,0x23,0x53,0xff,0x01,0x00,0x00,
    0x24,0xb0,0xd4,0x80,0xe8,0x11,0xc0,0xc7,0x30,0xa1,0xc5,0x99,0x09,0xcc,0x7e,0xb3,0x3f,0xf5,0x7f,0x6d,0x43,0x3b,0x76,0x1e,0xfd,0x11,0x76,0xa9,0x58,0xe7,0xaf,0x4e,
    0xbe,0x0a,0x1c,0x00,0x15,0xff,0x26,0xee,0x47,0x83,0x96,0x1e,0x6f,0x9b,0x3f,0x58,0xf4,0x6b,0x4e,0x02,0x12,0x04,0xbf,0x1d,0x4f,0x78,0x3e,0x7a,0xc6,0xb2,0xe3,0x49,
    0x0b,0xb1,0x43,0x8a,0xda,0x27,0x7c,0xdc,0xd7,0xc3,0x53,0x76,0x40,0x1a,0x61,0x8f,0x87,0x41,0xdf,0xf0,0xab,0x49,0xa0,0xd9,0x8e,0xf8,0x23,0xb5,0xd5,0x3f,0x68,0x9d,
    0x8b,0xed,0xb2,0xc5,0xc5,0xe6,0xab,0x19,0x8e,0x6a,0xc7,0x99,0x64,0xcc,0xb0,0x2b,0x6e,0x58,0xa8,0x5b,0x0a,0xbf,0x34,0x78,0x9e,0xa4,0xab,0xdb,0x8a,0x17,0xa0,0x73,
    0x2a,0x26,0xa7,0xf9,0xed,0x76,0x9d,0xad,0xd0,0x3c,0xc8,0x8e,0x3c,0x27,0x97,0x38,0xfd,0xb9,0x29,0x01,0xed,0xb0,0x2b,0xd3,0x2d,0x24,0x0f,0x72,0x48,0xbc,0xfa,0x5d,
    0xf1,0xbc,0x48,0xff,0x99,0x26,0x86,0x88,0x01,0x09,0xfe,0xf3,0x94,0xc5,0xbf,0x70,0x7d,0x65,0xa9,0x41,0x58,0xe2,0xfe,0xff,0x4e,0xcb,0x83,0x6c,0xca,0x73,0xf4,0x45,
    0x88,0xae,0xad,0xc5,0xee,0x72,0x6b,0xf4,0xf2,0xa0,0x0d,0x06,0xa4,0x61,0x22,0xb9,0xd4,0xe0,0x36,0xeb,0xf9,0x53,0xa6,0x12,0x11,0x1a,0x5f,0x03,0x14,0xf7,0x70,0x64,
    0x68,0x34,0xb5,0x58,0x15,0x02,0x53,0x1e,0x8f,0x8d,0xf1,0x19,0x97,0xd7,0x01,0x11,0x26,0x3d,0x24,0x69,0x9a,0x49,0x84,0xe9,0x52,0xaa,0x60,0xb6,0x54,0x87,0x68,0xed,
    0x82,0x3b,0x2a,0x88,0xb9,0xf4,0xef,0x9d,0x9f,0xac,0xb2,0x9e,0x50,0xfa,0x64,0xc6,0xf2,0xf5,0x8b,0x31,0xfe,0xe4,0x90,0xd9,0x83,0xc7,0xc3,0xe4,0x18,0x33,0x90,0x29,
    0x85,0x81,0xa5,0xca,0x0d,0xbe,0x87,0xd6,0xb4,0x82,0x59,0xf2,0x55,0x5b,0xf7,0x39,0x37,0x94,0x41,0x8b,0x16,0x7b,0x04,0x32,0x12,0xb5,0x24,0x20,0xed,0xc1,0x7a,0xd4,
    0x36,0x23,0xb9,0x8b,0xe0,0x04,0x48,0x86,0xac,0x87,0x70,0x21,0x31,0x4c,0x57,0x32,0xdf,0x5e,0x95,0x2f,0x20,0xbe,0xe2,0xae,0xdb,0x6d,0xb1,0x27,0x5b,0x06,0x6e,0xe0,
    0x21,0xdd,0xa3,0x2a,0xb4,0xc4,0x42,0x74,0x4f,0x17,0x90,0x1a,0x10,0xfb,0x38,0x40,0xfd,0x52,0x89,0xd3,0x16,0x69,0xe8,0x8e,0x45,0xe8,0x62,0xd6,0xa2,0x7d,0xaa,0x32,
    0x14,0xa6,0x6b,0xdc,0x8d,0x54,0x1e,0x1d,0x7c,0xae,0x07,0x27,0x0d,0x50,0xf2,0xbb,0x55,0x38,0x5b,0x61,0x19,0xe1,0x64,0x7c,0x08,0x6f,0xe6,0xdc,0xf6,0x49,0xab,0x2f,
    0x86,0x68,0x8a,0x7f,0x74,0xaa,0x52,0xf8,0x68,0x0a,0x5e,0x61,0xb3,0xc8,0x59,0xee,0xc9,0x9a,0xb8,0x70,0xc0,0x96,0x0b,0xb5,0x83,0xe5,0xed,0x59,0x78,0x91,0xc3,0xca,
    0x77,0x79,0x37,0x0e,0xb3,0xbe,0xa9,0xe0,0x18,0x77,0x73,0x65,0xbc,0xd6,0xea,0xdd,0x0c,0x1c,0x5d,0x0b,0x92,0xda,0x1d,0x10,0xcf,0x30,0xbd,0x1f,0x2a,0xd5,0x72,0x72,
    0x5a,0x90,0xd4,0x74,0x98,0xc6,0x88,0xd0,0x55,0x46,0x64,0x26,0xab,0x08,0x4f,0x16,0x02,0x47,0xea,0x3f,0x25,0xe4,0x91,0xda,0x2f,0x3a,0x85,0xc0,0x08,0xdc,0xad,0xf3,
    0xf1,0x21,0xbc,0x01,0x22,0x51,0xe8,0x0f,0x95,0x7c,0x5f,0x88,0x0d,0xd1,0xa8,0x06,0x74,0xf0,0x70,0xce,0x85,0x60,0xfe,0x21,0x0d,0xdd,0xf1,0x30,0xbd,0x6e,0x04,0xce,
    0xa6,0x00,0x49,0x4d,0x87,0x69,0x4c,0x51,0xd0,0xc6,0xc3,0xab,0xae,0x41,0x43,0xb8,0x00,0x0d,0x2d,0xb1,0x08,0x5d,0x34,0x77,0x98,0x02,0xe5,0xeb,0x13,0x27,0xf9,0x57,
    0xef,0x39,0x18,0xcd,0x0d,0x0e,0x09,0x83,0x89,0x61,0xba,0x62,0x0d,0x67,0x03,0x52,0x8f,0x8b,0x66,0xb1,0x8c,0xd3,0x3c,0xec,0x72,0x10,0x98,0x19,0xf6,0xa1,0x50,0x22,
    0x4c,0x9f,0x59,0x6b,0x88,0x26,0xa4,0x06,0xc4,0x3e,0xbe,0xa8,0x1e,0x1e,0x76,0x97,0x8a,0x0a,0x5b,0xcb,0xb7,0x7c,0x17,0xdf,0x33,0x98,0x4b,0xc5,0xf1,0x54,0x0b,0xb4,
    0xfb,0x6d,0xf1,0x98,0xb2,0x05,0x9d,0x1c,0xd1,0x0e,0x43,0xe0,0x50,0x11,0x79,0x2c,0xf2,0xf1,0xaf,0xf3,0x55,0x47,0x85,0x86,0x89,0x44,0x84,0xc6,0x8b,0x1f,0xca,0x2c,
    0xd6,0xce,0xa0,0xa8,0xab,0x59,0x80,0x9f,0xe7,0x8c,0xbd,0x44,0x68,0x96,0x61,0x4b,0xbe,0xcd,0xad,0x9e,0x6f,0xae,0x86,0xcf,0x98,0xef,0x9c,0x8b,0xdc,0xe6,0xb9,0xdd,
    0xe2,0x76,0x5b,0x4e,0xa4,0x82,0x70,0x93,0x5d,0xfa,0x8f,0xe9,0x08,0xfd,0x66,0xb7,0x85,0xfe,0x07,0xec,0x38,0xc0,0x84,
};


#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER ALIGN(4)
#endif
LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER const eZIP_RGBARGB888A  lv_img_dsc_t sys_set_currency SECTION(".ROM3_IMG_EZIP_HEADER.sys_set_currency") = { 
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .header.always_zero = 0,
  .header.w = 72,
  .header.h = 72,
  .data_size  = 1463,
  .data = sys_set_currency_map
};
