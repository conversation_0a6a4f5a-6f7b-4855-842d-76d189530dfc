#include <string.h>
#include <stddef.h>
#include <stdbool.h>
#include <math.h>
#include "navi_climb.h"

//导航线路爬坡点采样，所有爬坡点计算完成后，获取最终结果即可，中间结果没有意义
int navi_route_cp_sampler_exec(NaviRouteCpSampler *self, const NaviClimbpoint *cp)
{
    if (self == NULL || cp == NULL)
    {
        return -1;
    }

    //缓冲区未满
    if (self->len < self->capacity)
    {
        navi_route_cp_copy(&self->cp_buf[self->len], cp);
        self->len += 1;

        if (self->len >= 2)
        {
            //计算海拔变化量，海拔变化量一定是正值
            self->ad_buf[self->len-1] = fabsf(self->cp_buf[self->len-1].alt - self->cp_buf[self->len-2].alt);
        }

        return 0;
    }

    //缓冲区已满，丢弃海拔变化量最小的爬坡点，以强迫海拔尽可能大地变化
    uint32_t idx_min = 1;
    float ad_min = self->ad_buf[idx_min];

    //寻找海拔变化量最小的爬坡点
    for (uint32_t i = 2; i < self->len; i++)
    {
        if (self->ad_buf[i] < ad_min)
        {
            idx_min = i;
            ad_min = self->ad_buf[idx_min];
        }
    }

    //更新丢弃的爬坡点的后一个点的爬坡变化量
    if (idx_min < self->len - 1)
    {
        self->ad_buf[idx_min+1] = fabsf(self->cp_buf[idx_min+1].alt - self->cp_buf[idx_min-1].alt);
    }

    //丢弃选中的爬坡点
    for (uint32_t i = idx_min; i < self->len - 1; i++)
    {
        navi_route_cp_copy(&self->cp_buf[i], &self->cp_buf[i+1]);
        self->ad_buf[i] = self->ad_buf[i+1];
    }

    //填入最新的爬坡点
    navi_route_cp_copy(&self->cp_buf[self->len-1], cp);
    self->ad_buf[self->len-1] = fabsf(self->cp_buf[self->len-1].alt - self->cp_buf[self->len-2].alt);

    return 0;
}

//获取导航线路爬坡点采样结果
void navi_route_cp_sampler_data_get(NaviRouteCpSampler *self, NaviClimbSample *sample)
{
    if (self != NULL && sample != NULL)
    {
        sample->len = self->len;
        sample->buf = self->cp_buf;
    }
}

//复制导航线路爬坡点采样结果
void navi_route_cp_sampler_data_copy(NaviRouteCpSampler *self, NaviClimbSample *sample)
{
    if (self != NULL && sample != NULL)
    {
        sample->len = self->len;
        memcpy(sample->buf, self->cp_buf, sizeof(NaviClimbpoint) * self->len);
    }
}

//重置导航线路爬坡点采样器
void navi_route_cp_sampler_reset(NaviRouteCpSampler *self)
{
    if (self != NULL)
    {
        self->len = 0;
    }
}

//导航线路爬坡点压缩
int navi_route_cp_compressor_exec(NaviRouteCpCompressor *self, const NaviWaypointAdc *wpadc, NaviClimbpoint *output)
{
    if (self == NULL || wpadc == NULL || output == NULL)
    {
        return -1;
    }

    if (self->cnt < 2)
    {
        //第一个爬坡点（即起点）必须返回
        if (self->cnt == 0)
        {
            navi_route_cp_update(&self->start, wpadc->dist, wpadc->alt);
            navi_route_cp_copy(output, &self->start);
            self->cnt += 1;
            return 0;
        }
        else
        {
            navi_route_cp_update(&self->last, wpadc->dist, wpadc->alt);
            self->cnt += 1;
            return 1;
        }
    }

    navi_route_cp_update(&self->cur, wpadc->dist, wpadc->alt);
    self->cnt += 1;

    //海拔变化量大于阈值，则返回上一个爬坡点
    if (fabsf(self->cur.alt - self->start.alt) > self->h_thres)
    {
        navi_route_cp_copy(output, &self->last);
        navi_route_cp_copy(&self->start, &self->last);
        navi_route_cp_copy(&self->last, &self->cur);

        return 0;
    }

    //更新上一个爬坡点
    navi_route_cp_copy(&self->last, &self->cur);

    return 1;
}

//重置导航线路爬坡点压缩器
void navi_route_cp_compressor_reset(NaviRouteCpCompressor *self)
{
    if (self != NULL)
    {
        self->cnt = 0;
    }
}

//导航线路爬坡点分段
int navi_route_cp_segmentor_exec(NaviRouteCpSegmentor *self, const NaviClimbpoint *cp)
{
    if (self == NULL || cp == NULL)
    {
        return -1;
    }

    if (self->cnt == 0)
    {
        self->len = 0;
        dseg_update(&self->seg_buf[self->len].dseg, cp->dist, cp->dist);
        self->seg_buf[self->len].range.start = self->cnt;
        self->cnt += 1;
        return 0;
    }

    //更新分段数据
    self->seg_buf[self->len].dseg.dist_max = cp->dist;
    self->cnt += 1;

    if (self->cnt < (self->len + 1) * self->interval)
    {
        //尚未完成一个分段，返回即可
        return 0;
    }

    //完成了一个新的分段
    self->seg_buf[self->len].range.end = self->cnt;
    self->len += 1;

    if (self->len < self->capacity)
    {
        //创建一个新的分段
        dseg_update(&self->seg_buf[self->len].dseg, cp->dist, cp->dist);
        self->seg_buf[self->len].range.start = self->cnt;
    }
    else
    {
        //分段数量已经达到最大数量，则两两合并，以减少一半分段数量
        self->interval *= 2;
        const uint32_t merge_num = self->capacity / 2;

        //两两合并分段
        for (uint32_t i = 0; i < merge_num; i++)
        {
            self->seg_buf[i*2].range.end = self->seg_buf[i*2+1].range.end;
            self->seg_buf[i*2].dseg.dist_max = self->seg_buf[i*2+1].dseg.dist_max;
        }

        //将分段放置到前半部分
        for (uint32_t i = 1; i < merge_num; i++)
        {
            navi_route_cp_segment_copy(&self->seg_buf[i], &self->seg_buf[i*2]);
        }

        self->len = merge_num;

        if (self->capacity % 2 == 0)
        {
            //分段容量为偶数，则两两合并后没有多余的分段，开始新的分段即可
            dseg_update(&self->seg_buf[self->len].dseg, cp->dist, cp->dist);
            self->seg_buf[self->len].range.start = self->cnt;
        }
        else
        {
            //分段容量为奇数，则两两合并后多余一个分段，基于该分段继续进行分段计算
            navi_route_cp_segment_copy(&self->seg_buf[self->len], &self->seg_buf[self->capacity-1]);
        }
    }

    return 0;
}

//导航线路爬坡点分段结束时必须调用，负责处理最后一个未完成分段
void navi_route_cp_segmentor_end(NaviRouteCpSegmentor *self)
{
    if (self != NULL)
    {
        self->seg_buf[self->len].range.end = self->cnt;

        if (self->len > 0)
        {
            const uint32_t cnt = self->seg_buf[self->len].range.end - self->seg_buf[self->len].range.start;

            //最后一个分段范围过小，将其合并到前一个分段
            if (cnt < self->interval / 2)
            {
                self->seg_buf[self->len-1].range.end = self->seg_buf[self->len].range.end;
                self->seg_buf[self->len-1].dseg.dist_max = self->seg_buf[self->len].dseg.dist_max;
            }
            else
            {
                //否则创建一个新的分段，这个分段范围可能小于正常分段
                self->len += 1;
            }
        }
        else
        {
            //一个已完成分段都没有，则必须创建一个
            self->len = 1;
        }
    }
}

//获取导航线路爬坡点分段结果
void navi_route_cp_segmentor_data_get(NaviRouteCpSegmentor *self, NaviRouteCpSegmentArray *array)
{
    if (self != NULL && array != NULL)
    {
        array->len = self->len;
        array->segments = self->seg_buf;
    }
}

//重置导航线路爬坡点分段器
void navi_route_cp_segmentor_reset(NaviRouteCpSegmentor *self)
{
    if (self != NULL)
    {
        self->len = 0;
        self->cnt = 0;
        self->interval = self->INTERVAL;
    }
}

//爬坡合并
int navi_climb_merger_exec(NaviClimbMerger *self, const NaviClimbInfo *ci, NaviClimbInfoList *output)
{
    if (self == NULL || ci == NULL || output == NULL)
    {
        return -1;
    }

    //没有爬坡时，填入一个爬坡，后续才能进行合并
    if (self->len == 0)
    {
        navi_climb_info_copy(&self->buf[self->len], ci);
        self->len += 1;

        return 1;
    }

    uint32_t n_merge = 0;

    //输出确定不能合并的爬坡
    for (uint32_t i = 0; i < self->len; i++)
    {
        //一个爬坡距离当前爬坡距离大于阈值，则该爬坡不能被合并，返回之
        if (ci->start.dist - self->buf[i].end.dist > self->dist_thres)
        {
            n_merge += 1;

            const float dd = self->buf[i].end.dist - self->buf[i].start.dist;
            const float dalt = self->buf[i].end.alt - self->buf[i].start.alt;

            //检查该爬坡是否有效，有效则返回
            if (self->valid_check(dd, dalt) == true)
            {
                if (output->len >= output->capacity)
                {
                    return -1;
                }

                navi_climb_info_copy(output->buf + output->len, &self->buf[i]);
                output->len += 1;
            }
        }
        else
        {
            //某一个爬坡距离当前爬坡距离小于阈值，则后续爬坡距离当前爬坡距离必定小于阈值，无需再做计算
            break;
        }
    }

    //已经输出若干不能合并的爬坡，从缓冲区中清除之
    if (n_merge > 0)
    {
        self->len -= n_merge;
        for (uint32_t i = 0; i < self->len; i++)
        {
            navi_climb_info_copy(&self->buf[i], &self->buf[n_merge+i]);
        }
    }

    uint8_t is_merged = false;

    //检查从哪个爬坡开始，就可以将该爬坡到当前爬坡中间的所有爬坡合并到一起
    for (uint32_t i = 0; i < self->len; i++)
    {
        const float dd = ci->end.dist - self->buf[i].start.dist;
        const float dalt = ci->end.alt - self->buf[i].start.alt;

        //TODO 这里是否应该使用新的接口，只判断坡度是否满足条件即可？
        if (self->valid_check(dd, dalt) == true)
        {
            navi_route_cp_info_copy(&self->buf[i].end, &ci->end);
            self->len = i + 1;
            is_merged = true;
            break;
        }
    }

    //当前爬坡不能和任一爬坡合并，则填入缓冲区，看后续能否被合并
    if (is_merged == false)
    {
        navi_climb_info_copy(&self->buf[self->len], ci);
        self->len += 1;
    }

    //缓冲区已满，则输出/丢弃最早的爬坡
    if (self->len >= self->capacity)
    {
        const float dd = self->buf[0].end.dist - self->buf[0].start.dist;
        const float dalt = self->buf[0].end.alt - self->buf[0].start.alt;

        if (self->valid_check(dd, dalt) == true)
        {
            if (output->len >= output->capacity)
            {
                return -1;
            }

            navi_climb_info_copy(output->buf + output->len, &self->buf[0]);
            output->len += 1;
        }

        self->len -= 1;

        for (uint32_t i = 0; i < self->len; i++)
        {
            navi_climb_info_copy(&self->buf[i], &self->buf[i+1]);
        }
    }

    return 0;
}

//爬坡合并结束时必须调用，处理缓冲区中未合并的爬坡
int navi_climb_merger_end(NaviClimbMerger *self, NaviClimbInfoList *output)
{
    if (self == NULL || output == NULL)
    {
        return -1;
    }

    //输出所有满足条件的爬坡
    for (uint32_t i = 0; i < self->len; i++)
    {
        const float dd = self->buf[i].end.dist - self->buf[i].start.dist;
        const float dalt = self->buf[i].end.alt - self->buf[i].start.alt;

        if (self->valid_check(dd, dalt) == true)
        {
            if (output->len >= output->capacity)
            {
                return -1;
            }

            navi_climb_info_copy(output->buf + output->len, &self->buf[i]);
            output->len += 1;
        }
    }

    return 0;
}

//重置爬坡合并器
void navi_climb_merger_reset(NaviClimbMerger *self)
{
    if (self != NULL)
    {
        self->len = 0;
    }
}

//检查一个爬坡是否是有效的，有效才能参与爬坡合并
//true - 有效
//false - 无效
static int navi_climb_calculator_is_climb_valid(NaviClimbCalculator *self, const NaviClimbInfo * climb)
{
    if (self == NULL || climb == NULL)
    {
        return false;
    }

    //为爬坡计算一个评分
    const float score = (climb->end.dist - climb->start.dist) * (climb->end.alt - climb->start.alt);

    if (fabsf(score) >= self->score_thres)
    {
        return true;
    }

    return false;
}

//爬坡信息计算
//-1 - 输入无效
//0 - 输出有效（即检测到有若干爬坡）
//1 - 无输出
int navi_climb_calculator_exec(NaviClimbCalculator *self, const NaviClimbpoint *cp, NaviClimbInfoList *output)
{
    if (self == NULL || cp == NULL || output == NULL)
    {
        return -1;
    }

    output->len = 0;

    if (self->cnt == 0)
    {
        self->grade_dir = 0;
        navi_route_cp_info_update(&self->last, cp->dist, cp->alt, 0);
        navi_route_cp_info_copy(&self->peak, &self->last);
        navi_route_cp_info_copy(&self->valley, &self->last);
        self->cnt += 1;

        return 1;
    }

    //海拔变化量大于阈值才能进行计算
    if (fabsf(cp->alt - self->last.alt) >= self->h_thres)
    {
        const float grade = (cp->alt - self->last.alt) / (cp->dist - self->last.dist);

        int32_t cur_dir = 0;

        //计算坡度方向
        if (grade >= self->min_grade)
        {
            cur_dir = 1;
        }
        else if (grade <= -self->min_grade)
        {
            cur_dir = -1;
        }

        //之前不是上坡，现在是上坡，则上坡开始
        if (self->grade_dir != 1 && cur_dir == 1)
        {
            self->pos_climb_info.type = enumCLIMB_POS;
            navi_route_cp_info_copy(&self->pos_climb_info.start, &self->valley);
        }
        else if (self->grade_dir == 1 && cur_dir != 1)
        {
            //之前是上坡，现在不是上坡，则上坡结束
            navi_route_cp_info_copy(&self->pos_climb_info.end, &self->last);

            if (navi_climb_calculator_is_climb_valid(self, &self->pos_climb_info) == true)
            {
                //合并本次上坡
                if (navi_climb_merger_exec(&self->pos_merger, &self->pos_climb_info, output) == -1)
                {
                    return -1;
                }
            }
        }

        //之前不是下坡，现在是下坡，则下坡开始
        if (self->grade_dir != -1 && cur_dir == -1)
        {
            self->neg_climb_info.type = enumCLIMB_NEG;
            navi_route_cp_info_copy(&self->neg_climb_info.start, &self->peak);
        }
        else if (self->grade_dir == -1 && cur_dir != -1)
        {
            //之前是下坡，现在不是下坡，则下坡结束
            navi_route_cp_info_copy(&self->neg_climb_info.end, &self->last);

            if (navi_climb_calculator_is_climb_valid(self, &self->neg_climb_info) == true)
            {
                //合并本次下坡
                if (navi_climb_merger_exec(&self->neg_merger, &self->neg_climb_info, output) == -1)
                {
                    return -1;
                }
            }
        }

        //更新信息
        self->grade_dir = cur_dir;
        navi_route_cp_info_update(&self->last, cp->dist, cp->alt, self->cnt);

        navi_route_cp_info_copy(&self->peak, &self->last);
        navi_route_cp_info_copy(&self->valley, &self->last);
    }
    else
    {
        if (cp->alt >= self->peak.alt)
        {
            navi_route_cp_info_update(&self->peak, cp->dist, cp->alt, self->cnt);
        }

        if (cp->alt <= self->valley.alt)
        {
            navi_route_cp_info_update(&self->valley, cp->dist, cp->alt, self->cnt);
        }
    }

    self->cnt += 1;

    if (output->len > 0)
    {
        return 0;
    }
    else
    {
        return 1;
    }
}

//爬坡信息计算结束时必须调用，处理未合并的爬坡
//-1 - 输入无效
//0 - 输出有效（即检测到有若干爬坡）
//1 - 无输出
int navi_climb_calculator_end(NaviClimbCalculator *self, const NaviClimbpoint *cp, NaviClimbInfoList *output)
{
    if (self == NULL || cp == NULL || output == NULL)
    {
        return -1;
    }

    output->len = 0;

    if (self->cnt == 0)
    {
        self->grade_dir = 0;
        navi_route_cp_info_update(&self->last, cp->dist, cp->alt, 0);
        navi_route_cp_info_copy(&self->peak, &self->last);
        navi_route_cp_info_copy(&self->valley, &self->last);
        self->cnt += 1;

        return 1;
    }

    const float grade = (cp->alt - self->last.alt) / (cp->dist - self->last.dist);

    int32_t cur_dir = 0;

    //计算坡度方向
    if (grade >= self->min_grade)
    {
        cur_dir = 1;
    }
    else if (grade <= -self->min_grade)
    {
        cur_dir = -1;
    }

    if (self->grade_dir != 1)
    {
        if (cur_dir == 1)
        {
            self->pos_climb_info.type = enumCLIMB_POS;
            navi_route_cp_info_copy(&self->pos_climb_info.start, &self->valley);
            navi_route_cp_info_update(&self->pos_climb_info.end, cp->dist, cp->alt, self->cnt);

            if (navi_climb_calculator_is_climb_valid(self, &self->pos_climb_info) == true)
            {
                if (navi_climb_merger_exec(&self->pos_merger, &self->pos_climb_info, output) == -1)
                {
                    return -1;
                }
            }
        }
    }
    else
    {
        if (cur_dir == 1)
        {
            navi_route_cp_info_update(&self->pos_climb_info.end, cp->dist, cp->alt, self->cnt);
        }
        else
        {
            navi_route_cp_info_copy(&self->pos_climb_info.end, &self->last);
        }

        if (navi_climb_calculator_is_climb_valid(self, &self->pos_climb_info) == true)
        {
            if (navi_climb_merger_exec(&self->pos_merger, &self->pos_climb_info, output) == -1)
            {
                return -1;
            }
        }
    }

    if (self->grade_dir != -1)
    {
        if (cur_dir == -1)
        {
            self->neg_climb_info.type = enumCLIMB_NEG;
            navi_route_cp_info_copy(&self->neg_climb_info.start, &self->peak);
            navi_route_cp_info_update(&self->neg_climb_info.end, cp->dist, cp->alt, self->cnt);

            if (navi_climb_calculator_is_climb_valid(self, &self->neg_climb_info) == true)
            {
                if (navi_climb_merger_exec(&self->neg_merger, &self->neg_climb_info, output) == -1)
                {
                    return -1;
                }
            }
        }
    }
    else
    {
        if (cur_dir == -1)
        {
            navi_route_cp_info_update(&self->neg_climb_info.end, cp->dist, cp->alt, self->cnt);
        }
        else
        {
            navi_route_cp_info_copy(&self->neg_climb_info.end, &self->last);
        }

        if (navi_climb_calculator_is_climb_valid(self, &self->neg_climb_info) == true)
        {
            if (navi_climb_merger_exec(&self->neg_merger, &self->neg_climb_info, output) == -1)
            {
                return -1;
            }
        }
    }

    self->grade_dir = cur_dir;
    navi_route_cp_info_update(&self->last, cp->dist, cp->alt, self->cnt);
    navi_route_cp_info_copy(&self->peak, &self->last);
    navi_route_cp_info_copy(&self->valley, &self->last);
    self->cnt += 1;

    if (navi_climb_merger_end(&self->pos_merger, output) == -1)
    {
        return -1;
    }

    if (navi_climb_merger_end(&self->neg_merger, output) == -1)
    {
        return -1;
    }

    if (output->len > 0)
    {
        return 0;
    }
    else
    {
        return 1;
    }
}

//重置爬坡信息计算器
void navi_climb_calculator_reset(NaviClimbCalculator *self)
{
    if (self != NULL)
    {
        self->cnt = 0;
        self->grade_dir = 0;

        navi_climb_merger_reset(&self->pos_merger);
        navi_climb_merger_reset(&self->neg_merger);
    }
}

//爬坡分段
int navi_climb_segmentor_exec(NaviClimbSegmentor *self, const NaviClimbInfo *ci)
{
    if (self == NULL || ci == NULL)
    {
        return -1;
    }

    if (self->cnt == 0)
    {
        self->len = 0;
        dseg_update(&self->seg_buf[self->len].dseg, 0.0f, ci->end.dist);
        self->seg_buf[self->len].range.start = self->cnt;
        self->cnt += 1;
        return 0;
    }

    //更新分段数据
    self->seg_buf[self->len].dseg.dist_max = ci->end.dist;
    self->cnt += 1;

    if (self->cnt < (self->len + 1) * self->interval)
    {
        //尚未完成一个分段，返回即可
        return 0;
    }

    //完成一个分段
    self->seg_buf[self->len].range.end = self->cnt;
    self->len += 1;

    if (self->len < self->capacity)
    {
        //创建一个新的分段
        dseg_update(&self->seg_buf[self->len].dseg, ci->end.dist, ci->end.dist);
        self->seg_buf[self->len].range.start = self->cnt;
    }
    else
    {
        //分段数量已经达到最大数量，则两两合并，以减少一半分段数量
        self->interval *= 2;
        const uint32_t merge_num = self->capacity / 2;

        //两两合并
        for (uint32_t i = 0; i < merge_num; i++)
        {
            self->seg_buf[i*2].range.end = self->seg_buf[i*2+1].range.end;
            self->seg_buf[i*2].dseg.dist_max = self->seg_buf[i*2+1].dseg.dist_max;
        }

        //分段移动到前半部分
        for (uint32_t i = 1; i < merge_num; i++)
        {
            navi_climb_segment_copy(&self->seg_buf[i], &self->seg_buf[i*2]);
        }

        self->len = merge_num;

        if (self->capacity % 2 == 0)
        {
            //分段容量为偶数，则两两合并后没有多余的分段，开始新的分段即可
            dseg_update(&self->seg_buf[self->len].dseg, ci->end.dist, ci->end.dist);
            self->seg_buf[self->len].range.start = self->cnt;
        }
        else
        {
            //分段容量为奇数，则两两合并后多余一个分段，基于该分段继续进行分段计算
            navi_climb_segment_copy(&self->seg_buf[self->len], &self->seg_buf[self->capacity-1]);
        }
    }

    return 0;
}

//爬坡分段结束时必须调用，处理最后一个未完成分段
void navi_climb_segmentor_end(NaviClimbSegmentor *self, float dist_max)
{
    if (self != NULL)
    {
        if (dist_max > self->seg_buf[self->len].dseg.dist_max)
        {
            self->seg_buf[self->len].dseg.dist_max = dist_max;
        }

        self->seg_buf[self->len].range.end = self->cnt;

        if (self->len > 0)
        {
            const uint32_t cnt = self->seg_buf[self->len].range.end - self->seg_buf[self->len].range.start;

            //最后一个分段范围过小，将其合并到前一个完成的分段中
            if (cnt < self->interval / 2)
            {
                self->seg_buf[self->len-1].range.end = self->seg_buf[self->len].range.end;
                self->seg_buf[self->len-1].dseg.dist_max = self->seg_buf[self->len].dseg.dist_max;
            }
            else
            {
                //否则创建一个新的分段，其范围可能小于正常分段范围
                self->len += 1;
            }
        }
        else
        {
            //至少创建一个分段
            self->len = 1;
        }
    }
}

//获取爬坡分段结果
void navi_climb_segmentor_data_get(NaviClimbSegmentor *self, NaviClimbSegmentArray *array)
{
    if (self != NULL && array != NULL)
    {
        array->len = self->len;
        array->segments = self->seg_buf;
    }
}

//重置爬坡分段器
void navi_climb_segmentor_reset(NaviClimbSegmentor *self)
{
    if (self != NULL)
    {
        self->len = 0;
        self->cnt = 0;
        self->interval = self->INTERVAL;
    }
}

//计算一个爬坡的数据
int navi_climb_data_calculator_exec(NaviClimbDataCalculator *self, const NaviClimbInfo *info, NaviClimbData *output)
{
    if (self == NULL || info == NULL || output == NULL)
    {
        return -1;
    }

    navi_route_cp_sampler_reset(&self->sampler);

    output->start_dist = info->start.dist;
    output->end_dist = info->end.dist;
    output->start_alt = info->start.alt;
    output->end_alt = info->end.alt;
    output->grade = (info->end.alt - info->start.alt) / (info->end.dist - info->start.dist);

    NaviClimbpoint cp = { 0 };
    NaviClimbpoint last = { 0 };

    //注意：爬坡终点index是包含在爬坡之中的
    for (uint32_t i = info->start.idx; i <= info->end.idx; i++)
    {
        //从临时文件中获取爬坡点
        if (navi_tmp_route_cp_list_get(&self->cp_list, i, &cp) != 0)
        {
            return -1;
        }

        //对爬坡的各爬坡点进行采样
        if (navi_route_cp_sampler_exec(&self->sampler, &cp) != 0)
        {
            return -1;
        }

        if (i == info->start.idx)
        {
            navi_route_cp_copy(&last, &cp);
            output->min_alt = cp.alt;
            output->max_alt = cp.alt;
            output->ascent = 0.0f;
            output->max_grade = 0.0f;
            continue;
        }

        //计算最小和最大海拔
        if (cp.alt < output->min_alt)
        {
            output->min_alt = cp.alt;
        }

        if (cp.alt > output->max_alt)
        {
            output->max_alt = cp.alt;
        }

        //计算总升
        if (info->type == enumCLIMB_POS)
        {
            if (cp.alt > last.alt)
            {
                output->ascent += (cp.alt - last.alt);
            }
        }
        else
        {
            if (cp.alt < last.alt)
            {
                output->ascent += (cp.alt - last.alt);
            }
        }

        const float grade = (cp.alt - last.alt) / (cp.dist - last.dist);

        //到第二个爬坡点才能计算出第一个坡度
        if (i == info->start.idx + 1)
        {
            output->max_grade = grade;
        }
        else
        {
            //从第三个爬坡点开始，检查最大坡度
            if (info->type == enumCLIMB_POS)
            {
                if (grade > output->max_grade)
                {
                    output->max_grade = grade;
                }
            }
            else
            {
                if (grade < output->max_grade)
                {
                    output->max_grade = grade;
                }
            }
        }

        navi_route_cp_copy(&last, &cp);
    }

    //所有爬坡点处理完毕后，获取爬坡点采样
    navi_route_cp_sampler_data_copy(&self->sampler, &output->sample);

    return 0;
}

//重置爬坡数据计算器
void navi_climb_data_calculator_reset(NaviClimbDataCalculator *self)
{
    if (self != NULL)
    {
        navi_route_cp_sampler_reset(&self->sampler);
        navi_tmp_route_cp_list_reset(&self->cp_list);
    }
}

//计算上坡/下坡总体数据
int navi_climb_ensemble_data_calculator_exec(NaviClimbEnsembleDataCalculator *self, const NaviClimbData *data)
{
    if (self == NULL || data == NULL)
    {
        return -1;
    }

    self->num += 1;
    self->h += (data->end_alt - data->start_alt);
    self->dist += (data->end_dist - data->start_dist);

    return 0;
}

//获取上坡/下坡总体数据
void navi_climb_ensemble_data_calculator_data_get(NaviClimbEnsembleDataCalculator *self, NaviClimbEnsembleData *data)
{
    if (self != NULL && data != NULL)
    {
        data->num = self->num;
        data->h = self->h;
        data->dist = self->dist;
    }
}

//重置上坡/下坡总体数据计算器
void navi_climb_ensemble_data_calculator_reset(NaviClimbEnsembleDataCalculator *self)
{
    if (self != NULL)
    {
        self->num = 0;
        self->h = 0.0f;
        self->dist = 0.0f;
    }
}

//爬坡匹配，并计算爬坡进度
int navi_climb_matcher_exec(NaviClimbMatcher *self, float dist, uint8_t is_reverse, NaviClimbProgress *output)
{
    if (self == NULL || output == NULL)
    {
        return -1;
    }

    output->idx = 0xFFFFFFFF;
    output->idx_next = 0xFFFFFFFF;
    output->climb_remain = 0;
    output->d2climb = -1.0f;

    const float total_dist = self->seg_array->segments[self->seg_array->len-1].dseg.dist_max;

    float dist2 = 0.0f;

    if (is_reverse == true)
    {
        dist2 = total_dist - dist;
    }
    else
    {
        dist2 = dist;
    }

    uint8_t is_find = false;
    uint32_t seg_idx = 0;

    //从所有爬坡分段中找到当前距离所处的分段
    for (uint32_t i = 0; i < self->seg_array->len; i++)
    {
        const float dist_min = self->seg_array->segments[i].dseg.dist_min;
        const float dist_max = self->seg_array->segments[i].dseg.dist_max;
        if (dist2 >= dist_min && dist2 <= dist_max)
        {
            is_find = true;
            seg_idx = i;
            break;
        }
    }

    if (is_find == false)
    {
        output->climb_remain = 0xFFFFFFFF;
        return 1;
    }

    NaviClimbData *climb_data = NULL;

    const uint32_t start = self->seg_array->segments[seg_idx].range.start;
    const uint32_t end = self->seg_array->segments[seg_idx].range.end;

    for (uint32_t i = start; i < end; i++)
    {
        //获取爬坡数据
        if (is_reverse == true)
        {
            if (navi_neg_climb_list_get(self->climb_list, i, total_dist, &climb_data) != 0)
            {
                output->idx_next = 0xFFFFFFFF;
                output->climb_remain = 0xFFFFFFFF;
                output->d2climb = -1.0f;
                return -1;
            }
        }
        else
        {
            if (navi_pos_climb_list_get(self->climb_list, i, total_dist, &climb_data) != 0)
            {
                output->idx_next = 0xFFFFFFFF;
                output->climb_remain = 0xFFFFFFFF;
                output->d2climb = -1.0f;
                return -1;
            }
        }

        if (dist < climb_data->start_dist)
        {
            output->d2climb = climb_data->start_dist - dist;
            if (is_reverse == true)
            {
                output->idx_next = self->ensemble->num - 1 - i;
                output->climb_remain = i + 1;
                continue;
            }
            else
            {
                output->idx_next = i;
                output->climb_remain = self->ensemble->num - i;
                break;
            }
        }

        if (dist > climb_data->end_dist)
        {
            if (is_reverse == true)
            {
                break;
            }
            else
            {
                continue;
            }
        }

        //找到当前距离在爬坡中的哪个采样点之后的线段中，从而计算当前距离对应的海拔
        uint32_t sample_idx = 0;
        const NaviClimbSample *sample = &climb_data->sample;

        for (uint32_t j = 0; j < sample->len - 1; j++)
        {
            if (dist >= sample->buf[j].dist && dist <= sample->buf[j+1].dist)
            {
                sample_idx = j;
                break;
            }
        }

        const float dd = sample->buf[sample_idx+1].dist - sample->buf[sample_idx].dist;
        const float dalt = sample->buf[sample_idx+1].alt - sample->buf[sample_idx].alt;
        const float cur_alt = sample->buf[sample_idx].alt + (dist - sample->buf[sample_idx].dist) * dalt / dd;

        if (is_reverse == true)
        {
            output->idx = self->climb_list->len - 1 - i;
            output->d_residual = climb_data->d_acc + climb_data->end_dist - dist;
            output->h_residual = climb_data->h_acc + climb_data->end_alt - cur_alt;
        }
        else
        {
            output->idx = i;
            output->d_residual = self->ensemble->dist - (climb_data->d_acc + (dist - climb_data->start_dist));
            output->h_residual = self->ensemble->h - (climb_data->h_acc + (cur_alt - climb_data->start_alt));
        }

        output->dist_remain = climb_data->end_dist - dist;
        output->h_remain = climb_data->end_alt - cur_alt;

        if (output->idx + 1 < self->ensemble->num)
        {
            output->idx_next = output->idx + 1;
            output->climb_remain = self->ensemble->num - output->idx;
        }
        else
        {
            output->idx_next = 0xFFFFFFFF;
            output->climb_remain = 1;
        }

        output->d2climb = -1.0f;
        return 0;
    }

    return 1;
}

//重置爬坡匹配器
void navi_climb_matcher_reset(NaviClimbMatcher *self)
{
    if (self != NULL)
    {
        navi_climb_list_reset(self->climb_list);
    }
}

//加载指定距离前后的爬坡点
//0 - 加载成功
//else - 加载失败
int navi_route_cp_loader_exec(NaviRouteCpLoader *self, float dist, uint8_t is_reverse)
{
    if (self == NULL)
    {
        return -1;
    }

    const float total_dist = self->seg_array->segments[self->seg_array->len-1].dseg.dist_max;

    if (is_reverse == true)
    {
        dist = total_dist - dist;
    }

    uint8_t is_find = false;
    uint32_t seg_idx = 0;

    //从所有爬坡点分段中找到当前距离所处的分段
    for (uint32_t i = 0; i < self->seg_array->len; i++)
    {
        const float dist_min = self->seg_array->segments[i].dseg.dist_min;
        const float dist_max = self->seg_array->segments[i].dseg.dist_max;
        if (dist >= dist_min && dist <= dist_max)
        {
            is_find = true;
            seg_idx = i;
            break;
        }
    }

    if (is_find == false)
    {
        return 1;
    }

    uint32_t start = self->seg_array->segments[seg_idx].range.start;

    if (self->seg_idx == seg_idx)
    {
        if (dist >= self->ref_dist)
        {
            start = self->ref_idx;
        }
    }

    uint32_t end = self->seg_array->segments[seg_idx].range.end;

    uint32_t idx = 0;

    NaviClimbpoint cp = { 0 };

    //查找距离对应的爬坡点索引
    for (uint32_t i = start; i < end; i++)
    {
        if (navi_route_cp_list_get(self->cp_list, i, &cp) != 0)
        {
            return -1;
        }

        if (cp.dist >= dist)
        {
            idx = i;
            break;
        }
    }

    const uint32_t half = self->capacity / 2;

    Range range = { 0 };

    //计算要加载的爬坡点的范围
    if (idx >= half)
    {
        range.start = idx - half;
    }
    else
    {
        range.start = 0;
    }

    if (self->capacity % 2 == 0)
    {
        end = idx + half;
    }
    else
    {
        end = idx + half + 1;
    }

    if (end <= self->cp_list->len)
    {
        range.end = end;
    }
    else
    {
        range.end = self->cp_list->len;
    }

    const uint32_t capacity = range.end - range.start;

    //尽可能占满缓冲区
    if (capacity < self->capacity)
    {
        const uint32_t mtr = self->capacity - capacity;

        if (range.start > mtr)
        {
            range.start -= mtr;
        }
        else
        {
            range.start = 0;
        }

        if (range.end + mtr <= self->cp_list->len)
        {
            range.end += mtr;
        }
        else
        {
            range.end = self->cp_list->len;
        }
    }

    //缓冲区没有发生变化，不必进行加载
    if (self->range.start == range.start && self->range.end == range.end)
    {
        return 0;
    }

    //将已经加载的爬坡点复制到正确的位置
    if (self->range.start < range.end && self->range.end > range.start)
    {
        //爬坡点前移，则前向遍历，否则爬坡点会被覆盖，导致所有数据相同
        if (range.start >= self->range.start)
        {
            for (uint32_t i = range.start; i < range.end; i++)
            {
                if (i >= self->range.start && i < self->range.end)
                {
                    if (is_reverse == true)
                    {
                        navi_route_cp_copy(&self->buf[self->len-1-i+range.start], &self->buf[self->len-1-i+self->range.start]);
                    }
                    else
                    {
                        navi_route_cp_copy(&self->buf[i-range.start], &self->buf[i-self->range.start]);
                    }
                }
            }
        }
        else
        {
            //爬坡点后移，则后向遍历，否则爬坡点会被覆盖，导致所有数据相同
            for (uint32_t i = range.end - 1; i > range.start; i--)
            {
                if (i >= self->range.start && i < self->range.end)
                {
                    if (is_reverse == true)
                    {
                        navi_route_cp_copy(&self->buf[self->len-1-i+range.start], &self->buf[self->len-1-i+self->range.start]);
                    }
                    else
                    {
                        navi_route_cp_copy(&self->buf[i-range.start], &self->buf[i-self->range.start]);
                    }
                }
            }
        }
    }

    const uint32_t len = range.end - range.start;

    //加载剩余的爬坡点
    for (uint32_t i = range.start; i < range.end; i++)
    {
        if (i >= self->range.start && i < self->range.end)
        {
            continue;
        }

        NaviClimbpoint *cp2 = NULL;

        if (is_reverse == true)
        {
            cp2 = &self->buf[len-1-i+range.start];
        }
        else
        {
            cp2 = &self->buf[i-range.start];
        }

        if (navi_route_cp_list_get(self->cp_list, i, cp2) != 0)
        {
            return -1;
        }

        if (is_reverse == true)
        {
            cp2->dist = total_dist - cp2->dist;
        }
    }

    self->range.start = range.start;
    self->range.end = range.end;
    self->len = self->range.end - self->range.start;
    self->seg_idx = seg_idx;
    self->ref_dist = cp.dist;
    self->ref_idx = idx;

    return 0;
}

//获取加载的爬坡点
void navi_route_cp_loader_data_get(NaviRouteCpLoader *self, NaviRouteCpNearby *cp_nearby)
{
    if (self != NULL && cp_nearby != NULL)
    {
        cp_nearby->buf = self->buf;
        cp_nearby->len = self->len;
    }
}

//重置爬坡点加载器
void navi_route_cp_loader_reset(NaviRouteCpLoader *self)
{
    if (self != NULL)
    {
        self->len = 0;
        self->range.start = 0;
        self->range.end = 0;
        self->seg_idx = 0;
        self->ref_dist = 0.0f;
        self->ref_idx = 0;
    }
}
