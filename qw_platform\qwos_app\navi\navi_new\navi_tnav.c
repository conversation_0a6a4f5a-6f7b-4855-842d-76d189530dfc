#include <string.h>
#include <stddef.h>
#include <stdbool.h>
#include "navi_tnav.h"

//读取tnav文件头，用于检查文件类型和版本号等
int tnav_header_read(TnavReader *self, uint8_t *header)
{
    if (self == NULL || header == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 0) != QW_OK)
    {
        return -1;
    }

    UINT br = 0;

    if (qw_f_read(self->fp, header, 32, &br) != QW_OK || br != 32)
    {
        return -1;
    }

    return 0;
}

//从tnav文件中加载转向分段数组
int tnav_turn_segment_array_read(TnavReader *self, NaviTurnSegmentArray *seg_array)
{
    if (self == NULL || seg_array == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 32) != QW_OK)
    {
        return -1;
    }

    uint32_t buf[3] = { 0 };

    UINT br = 0;

    if (qw_f_read(self->fp, buf, 12, &br) != QW_OK || br != 12)
    {
        return -1;
    }

    const uint32_t check_code = buf[0];
    const uint32_t size = buf[1];
    const uint32_t num = buf[2];

    if (check_code != 0 || size != 16 || num > NAVI_TURN_SEGMENTS_NUM)
    {
        return -1;
    }

    seg_array->len = num;

    if (seg_array->len == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const UINT btr = seg_array->len * 16;

    if (qw_f_read(self->fp, seg_array->segments, btr, &br) != QW_OK || br != btr)
    {
        return -1;
    }

    return 0;
}

//读取转向数量
int tnav_turn_num_read(TnavReader *self, uint32_t *turn_num)
{
    if (self == NULL || turn_num == NULL)
    {
        return -1;
    }

    //32 + (12 + 16 * 100) = 1644
    if (qw_f_lseek(self->fp, 1644) != QW_OK)
    {
        return -1;
    }

    uint32_t buf[3] = { 0 };

    UINT br = 0;

    if (qw_f_read(self->fp, buf, 12, &br) != QW_OK || br != 12)
    {
        return -1;
    }

    const uint32_t check_code = buf[0];
    const uint32_t size = buf[1];
    const uint32_t num = buf[2];

    if (check_code != 1 || size != 80)
    {
        return -1;
    }

    *turn_num = num;

    return 0;
}

//读取指定范围的转向数据
int tnav_turn_data_read(TnavReader *self, uint32_t start, uint32_t end, uint32_t turn_num, NaviTurn *turn_buf)
{
    if (self == NULL || turn_buf == NULL)
    {
        return -1;
    }

    if (start > end || end > turn_num)
    {
        return -1;
    }

    const uint32_t ntr = end - start;

    if (ntr == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    //32 + (12 + 16 * 100) + 12 = 1656
    const uint32_t offset = 1656 + start * 80;

    if (qw_f_lseek(self->fp, offset) != QW_OK)
    {
        return -1;
    }

    const UINT btr = ntr * 80;

    UINT br = 0;

    if (qw_f_read(self->fp, turn_buf, btr, &br) != QW_OK || br != btr)
    {
        return -1;
    }

    return 0;
}

//写入文件头占位符
int tnav_header_placeholder_write(TnavWriter *self)
{
    if (self == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 0) != QW_OK)
    {
        return -1;
    }

    const uint8_t buf[32] = {
        'S', 'h', 'i', 't', ' ', 'H', 'a', 'p', 'p', 'e', 'n', 's', '.',
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 32, &bw) != QW_OK || bw != 32)
    {
        return -1;
    }

    return 0;
}

//写入tnav文件头
int tnav_header_write(TnavWriter *self)
{
    if (self == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 0) != QW_OK)
    {
        return -1;
    }

    UINT bw = 0;

    const uint8_t buf[32] = {
        'T', 'N', 'A', 'V', NAVI_FILE_MAJOR_VERSION, NAVI_FILE_MINOR_VERSION,
        'D', 'e', 's', 'i', 'g', 'n', 'e', 'd', ' ', 'b', 'y', ' ', 'J', 'u', 'n',
        'j', 'i', 'e', ' ', 'D', 'i', 'n', 'g', 0, 0, 0,
    };

    if (qw_f_write(self->fp, buf, 32, &bw) != QW_OK || bw != 32)
    {
        return -1;
    }

    return 0;
}

//向tnav文件中写入转向分段数组
int tnav_turn_segment_array_write(TnavWriter *self, const NaviTurnSegmentArray *seg_array)
{
    if (self == NULL || seg_array == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 32) != QW_OK)
    {
        return -1;
    }

    const uint32_t buf[3] = { 0, 16, seg_array->len };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    if (seg_array->len == 0)
    {
        return 0;
    }

    const UINT btw = seg_array->len * 16;

    if (qw_f_write(self->fp, seg_array->segments, btw, &bw) != QW_OK || bw != btw)
    {
        return -1;
    }

    return 0;
}

//写入转向数量
int tnav_turn_num_write(TnavWriter *self, uint32_t num)
{
    if (self == NULL)
    {
        return -1;
    }

    //32 + (12 + 16 * 100) = 1644
    if (qw_f_lseek(self->fp, 1644) != QW_OK)
    {
        return -1;
    }

    const uint32_t buf[3] = { 1, 80, num };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    return 0;
}

//写入一个转向数据
int tnav_turn_data_write(TnavWriter *self, const NaviTurn *turn)
{
    if (self == NULL || turn == NULL)
    {
        return -1;
    }

    UINT bw = 0;

    if (qw_f_write(self->fp, turn, 80, &bw) != QW_OK || bw != 80)
    {
        return -1;
    }

    return 0;
}

//复制一个转向数据到自身
void navi_turn_copy(NaviTurn *self, const NaviTurn *turn)
{
    if (self != NULL && turn != NULL)
    {
        memcpy(self, turn, sizeof(NaviTurn));
    }
}

//从转向列表中获取指定index的转向数据
int navi_turn_list_get(NaviTurnList *self, uint32_t idx, NaviTurn *output)
{
    if (self == NULL || output == NULL)
    {
        return -1;
    }

    if (idx >= self->len)
    {
        return -1;
    }

    //缓存命中
    for (uint32_t i = 0; i < self->cache.len; i++)
    {
        const uint32_t start = self->cache.turn_buf[i].range.start;
        const uint32_t end = self->cache.turn_buf[i].range.end;

        if (idx >= start && idx < end)
        {
            for (uint32_t j = start; j < end; j++)
            {
                if (idx == j)
                {
                    navi_turn_copy(output, &self->cache.turn_buf[i].buf[j-start]);
                    return 0;
                }
            }
        }
    }

    //缓存未命中
    NaviTurnBuf *turn_buf = &self->cache.turn_buf[self->cache.next];

    //前一个转向也可能用到，故一起加载
    turn_buf->range.start = idx > 0 ? idx - 1 : idx;
    turn_buf->range.end = turn_buf->range.start + turn_buf->capacity;
    if (turn_buf->range.end > self->len)
    {
        turn_buf->range.end = self->len;
    }

    if (tnav_turn_data_read(self->cache.tnav_reader, turn_buf->range.start, turn_buf->range.end, self->len, turn_buf->buf) != 0)
    {
        return -1;
    }

    navi_turn_copy(output, &turn_buf->buf[idx - turn_buf->range.start]);

    if (self->cache.len < self->cache.capacity)
    {
        self->cache.len += 1;
    }

    self->cache.next += 1;
    if (self->cache.next >= self->cache.capacity)
    {
        self->cache.next = 0;
    }

    return 0;
}

//重置转向缓冲区
void navi_turn_buf_reset(NaviTurnBuf *self)
{
    if (self != NULL)
    {
        self->range.start = 0;
        self->range.end = 0;
    }
}

//重置转向缓存
void navi_turn_cache_reset(NaviTurnCache *self)
{
    if (self != NULL)
    {
        self->len = 0;
        self->next = 0;

        for (uint32_t i = 0; i < self->capacity; i++)
        {
            navi_turn_buf_reset(&self->turn_buf[i]);
        }
    }
}

//重置转向list
void navi_turn_list_reset(NaviTurnList *self)
{
    if (self != NULL)
    {
        navi_turn_cache_reset(&self->cache);
    }
}
