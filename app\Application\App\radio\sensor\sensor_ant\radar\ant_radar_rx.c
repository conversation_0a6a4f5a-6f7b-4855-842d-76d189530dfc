/****************************************Copyright (c)****************************************
* <PERSON><PERSON> Technology Co., Ltd
*
*---------------------------------------File Info--------------------------------------------
* File path : \radio\sensor\sensor_ant\radar\ant_radar_rx.c
* Created by : Lxin
* LastEditors: Lxin
* Descriptions :
*--------------------------------------------------------------------------------------------
* History :
* 2022-04-19 19:34:26: Lxin 原始版本
*
*********************************************************************************************/
#if ANT_SENSOR_RADAR_ENABLED
#include "nrf_log.h"
#include "nrf_sdh.h"
#include "nrf_sdh_ant.h"
#include "ant_radar.h"
#include "ant_radar_utils.h"
#include "app_error.h"
#include "ant_parameters.h"
#include "ant_interface.h"
#include "basictype.h"
#include "sensor_ant_common.h"
#include "ant_radar_rx.h"
#include "rttlog.h"
#include "app_timer.h"
#include "qw_sensor_data.h"
#include "cfg_header_def.h"

#define RADAR_DEBUG 0

static void ant_radar_rx_evt_handler(ant_radar_profile_t * p_profile, ant_radar_evt_t event);
//--------------------------------------变量定义-------------------------------------------//
static ant_radar_profile_t m_ant_radar;
static void radar_ant_evt(ant_evt_t *p_ant_evt, void * p_context);
APP_TIMER_DEF(timer_id_radar);
static uint8_t s_is_radar_timer_started = false;
//--------------------------------------函数定义-------------------------------------------//

/**
 * @*********************************************************************************************
 * @description: 加载ANT雷达接收通道默认配置
 * @param {ant_channel_config_t } *p_channel_config
 * @return {*}
 * @*********************************************************************************************
 */
static void LoadChnConf_radar_rx(ant_channel_config_t  *p_channel_config)
{
    p_channel_config->channel_number    = sensor_ant_channel_num_get(SENSOR_TYPE_RADAR);
    p_channel_config->channel_type      = RADAR_DISP_CHANNEL_TYPE;
    p_channel_config->ext_assign        = RADAR_EXT_ASSIGN;
    p_channel_config->rf_freq           = RADAR_ANTPLUS_RF_FREQ;
    p_channel_config->transmission_type = CHAN_ID_TRANS_TYPE;
    p_channel_config->device_type       = RADAR_DEVICE_TYPE;
    p_channel_config->channel_period    = RADAR_MSG_PERIOD;
    p_channel_config->network_number    = ANTPLUS_NETWORK_NUM;
}


static uint8_t get_battery_pct(uint8_t bat_level)
{
    uint8_t bat_pct = 0;
    switch(bat_level)
    {
        case 1:
            bat_pct = 100;
            break;
        case 2:
            bat_pct = 90;
            break;
        case 3:
            bat_pct = 60;
            break;
        case 4:
            bat_pct = 30;
            break;
        case 5:
            bat_pct = 15;
            break;
        default:
            break;
    }
    return bat_pct;
}

/**
 * @*********************************************************************************************
 * @description: 雷达事件处理函数
 * @param {ant_radar_profile_t *} p_profile
 * @param {ant_radar_evt_t} event
 * @return {*}
 * @*********************************************************************************************
 */

static void ant_radar_rx_evt_handler(ant_radar_profile_t * p_profile, ant_radar_evt_t event)
{
    sensor_search_infor_t       sensor_search_infor;
    // sensor_connect_infor_t      *p_sensor_connect       = sensor_connect_infor_get(SENSOR_TYPE_RADAR);
    sensor_connect_infor_t      sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_RADAR, &sensor_connect);

    sensor_module_evt_handler   evt_handler             = sensor_module_evt_handler_get();
    static sensor_saved_work_t  *p_sensor_saved         = NULL;
    static sensor_work_state_t  sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    sensor_module_param_input_t *p_param                = sensor_module_param_input_get();
    sensor_original_data_t      *p_sensor_original_data = sensor_original_data_get();
    static int8_t               index                   = -1;
    static uint8_t              low_power_indicate_flag = FALSE;
    static uint8_t target_number = 0;
    static uint8_t threat_level_max = 0;
    static bool radar_warning = false;

#if RADAR_TIMEOUT_LOG
    static uint32_t last_log_time = 0;
    static bool print_log = false;
#endif

    NRF_LOG_INFO("File: %s, Function: %s",  (uint32_t)__FILE__, (uint32_t)__func__);

    sensor_ant_leave_rx_search(SENSOR_TYPE_RADAR);

    if (s_is_radar_timer_started == false)
    {
        app_timer_start(timer_id_radar, 500, NULL, "timer_id_radar");
        s_is_radar_timer_started = true;
    }

    memset ((uint8_t *)&sensor_search_infor, 0x00, sizeof(sensor_search_infor_t));
    switch (event)
    {
        case ANT_RADAR_PAGE_1_UPDATED:
			// if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
			// {
			// 	p_sensor_original_data->radarData.device_error =
			// 			(0 < (p_profile->page_device[0].device_state + p_profile->page_device[1].device_state)) ? 1 : 0;
			// }
    		// break;
       case ANT_RADAR_PAGE_2_UPDATED:
   		    break;
        case ANT_RADAR_PAGE_48_UPDATED:
        case ANT_RADAR_PAGE_49_UPDATED:
        	memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
			memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
			sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
			sensor_search_infor.sensor_type = SENSOR_TYPE_RADAR;

            p_sensor_original_data->radarData.last_timestamp = *p_param->sysTime_s;
            p_sensor_original_data->radarData.is_sensor_error = false;
            p_sensor_original_data->radarData.is_sensor_timeout = false;

		    //更新雷达数据
#if RADAR_TIMEOUT_LOG
			print_log = false;
#endif

#if RADAR_DEBUG == 1
			for (uint8_t i = 0; i < 8; i++)
			{
				p_sensor_original_data->radarData.target[i].timestamp = *p_param->sysTime_s;
				p_sensor_original_data->radarData.target[i].range = RADAR_TARGET_RAGNE(p_profile, i);
				p_sensor_original_data->radarData.target[i].threat_level = RADAR_TARGET_THREAT_LEVEL(p_profile, i);
			}

			//发送消息
			target_number = 0;
			threat_level_max = 0;
			radar_warning = false;
			for (uint8_t i = 0; i < 8; i++)
			{
				if (RADAR_TARGET_RAGNE(p_profile, i) > 0)
				{
					target_number++;
					if (threat_level_max < RADAR_TARGET_THREAT_LEVEL(p_profile, i))
					{
						threat_level_max = RADAR_TARGET_THREAT_LEVEL(p_profile, i);
					}
				}
			}
#else
			for (uint8_t i = 0; i < 8; i++)
			{
				if (0 < RADAR_TARGET_THREAT_LEVEL(p_profile, i))
				{
                    //下面的逻辑是为了滤除静止目标，但不符合ANT+规范，故去除
#if 0
					if (p_sensor_original_data->radarData.target[i].range == RADAR_TARGET_RAGNE(p_profile, i) &&
						 *p_param->sysTime_s > p_sensor_original_data->radarData.target[i].timestamp + 3)
					{
						//目标3s没动, 没有威胁, 理论上调频连续波雷达无法探测到相对静止的物体, 可认为是传感器误判
						p_sensor_original_data->radarData.target[i].threat_level = 0;
#if RADAR_TIMEOUT_LOG
						print_log = true;
#endif
					}
					else
					{
						if (0 == p_sensor_original_data->radarData.target[i].timestamp ||
							 p_sensor_original_data->radarData.target[i].range != RADAR_TARGET_RAGNE(p_profile, i))
						{
							//初次检测到目标, 或目标状态有更新时记录时间戳
							p_sensor_original_data->radarData.target[i].timestamp = *p_param->sysTime_s;
						}

						p_sensor_original_data->radarData.target[i].range = RADAR_TARGET_RAGNE(p_profile, i);
						p_sensor_original_data->radarData.target[i].threat_level = RADAR_TARGET_THREAT_LEVEL(p_profile, i);
					}
#endif
                    p_sensor_original_data->radarData.target[i].timestamp = *p_param->sysTime_s;
                    p_sensor_original_data->radarData.target[i].range = RADAR_TARGET_RAGNE(p_profile, i);
                    p_sensor_original_data->radarData.target[i].threat_level = RADAR_TARGET_THREAT_LEVEL(p_profile, i);
				}
				else
				{
					memset(&p_sensor_original_data->radarData.target[i], 0, sizeof(ant_radar_target_cal_t));
				}
			}

#if RADAR_TIMEOUT_LOG
			if (print_log && last_log_time + 120 < *p_param->sysTime_s)
			{
				last_log_time = *p_param->sysTime_s;
				ant_radar_target_log_out();
			}
#endif

			//发送消息
			target_number = 0;
			threat_level_max = 0;
			radar_warning = false;
			for (uint8_t i = 0; i < 8; i++)
			{
				if (0 < p_sensor_original_data->radarData.target[i].threat_level)
				{
					target_number++;
					if (threat_level_max < p_sensor_original_data->radarData.target[i].threat_level)
					{
						threat_level_max = p_sensor_original_data->radarData.target[i].threat_level;
					}
				}
			}
#endif

			if (target_number > p_sensor_original_data->radarData.target_number ||
				 threat_level_max > p_sensor_original_data->radarData.threat_level_max ||
				 UINT8_MAX == p_sensor_original_data->radarData.target_number ||
				 UINT8_MAX == p_sensor_original_data->radarData.threat_level_max)
			{
				//发送警告条件: 1. 目标个数增加   2. 目标最高危险等级上升  3.初始化
				radar_warning = true;
			}

			p_sensor_original_data->radarData.target_number = target_number;
			p_sensor_original_data->radarData.threat_level_max = threat_level_max;

			if (radar_warning)
			{
				evt_handler(EVENT_SENSOR_RADAR_WARNING,
						&sensor_search_infor.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, threat_level_max);
			}

			sensor_work_state = SENSOR_WORK_STATE_IDLE;
			p_sensor_saved    = NULL;
			index             = -1;

			if (SENSOR_CONNECT_STATE_CONNECTING == sensor_connect.state)
			{
				sensor_connect.state = SENSOR_CONNECT_STATE_CONNECTED;
                sensor_connect_infor_set(SENSOR_TYPE_RADAR, &sensor_connect);
                if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
                {
                    if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
                    {
                        p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                        p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
                        cfg_mark_update(enum_cfg_ant_ble_dev);
                    }
                    else if (SENSOR_WORK_STATE_FORBIDDEN == sensor_work_state)
                    {
                        sensor_infor_t sensor_infor = {0};
                        sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                        sensor_disconnect(&sensor_infor);
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }
				else
				{
                    if(sensor_disconnect_item_check(&sensor_search_infor))
                    {
                        sensor_disconnect_info_remove(&sensor_search_infor);
                        sensor_infor_t sensor_infor = {0};
                        sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                        sensor_disconnect(&sensor_infor);
                        return;
                    }
					sensor_saved_work_infor_add(&sensor_search_infor);
					sensor_search_infor_del(&sensor_search_infor);
				}

				if (NULL != evt_handler)
				{
                    evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, TRUE);
					evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, 0);
					evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, SENSOR_TYPE_RADAR, SENSOR_RADIO_TYPE_ANT, TRUE);
				}

				low_power_indicate_flag = TRUE;
			}
            else if (SENSOR_CONNECT_STATE_CONNECTED == sensor_connect.state)
            {
                if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
                {
                    if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
                    {
                        p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                        p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }
            }
			break;
        case ANT_RADAR_PAGE_80_UPDATED:
        case ANT_RADAR_PAGE_81_UPDATED:
            if (sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
            {
                if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
                {
#if SENSOR_DEVICE_INFO_ENABLED
                    if (p_sensor_saved->sensor_manufacturer[index].manufacturer_ant != p_profile->page_80.manufacturer_id ||
                            p_sensor_saved->sensor_serial[index].serial_ant != p_profile->page_81.serial_number ||
                            p_sensor_saved->sensor_hw_version[index].version_ant != p_profile->page_80.hw_revision ||
                            p_sensor_saved->sensor_model[index].model_ant != p_profile->page_80.model_number ||
                            p_sensor_saved->sensor_sw_version[index].version_ant != ((((uint16_t)p_profile->page_81.sw_revision_major) << 8) | p_profile->page_81.sw_revision_minor))
                    {
                        p_sensor_saved->sensor_manufacturer[index].manufacturer_ant = p_profile->page_80.manufacturer_id;
                        p_sensor_saved->sensor_serial[index].serial_ant       = p_profile->page_81.serial_number;
                        p_sensor_saved->sensor_hw_version[index].version_ant      = p_profile->page_80.hw_revision;
                        p_sensor_saved->sensor_model[index].model_ant        = p_profile->page_80.model_number;
                        p_sensor_saved->sensor_sw_version[index].version_ant      = ((((uint16_t)p_profile->page_81.sw_revision_major) << 8) | p_profile->page_81.sw_revision_minor);
                        evt_handler(EVENT_SENSOR_MANUFACTURER_RECEIVED, NULL, sensor_search_infor.sensor_type, 0, index);
                    }
#endif
                }
                sensor_saved_work_infor_release_write_lock(index);
			}
    		break;
        case ANT_RADAR_PAGE_82_UPDATED:
            if (p_profile->page_82.descriptive_bit_field.items.battery_status >= SENSOR_BATT_NEW &&
                    p_profile->page_82.descriptive_bit_field.items.battery_status <= SENSOR_BATT_CRITICAL)
            {
                p_sensor_original_data->battery_list.radar = (SENSOR_BATT_MAX - p_profile->page_82.descriptive_bit_field.items.battery_status) * 20;
            }

        	if (p_profile->page_82.descriptive_bit_field.items.battery_status  == 5 && TRUE == low_power_indicate_flag)
			{
				low_power_indicate_flag = FALSE;
				if (NULL != evt_handler)
				{
					evt_handler(EVENT_SENSOR_LOW_POWER, &sensor_search_infor.sensor_id, SENSOR_TYPE_RADAR, SENSOR_RADIO_TYPE_ANT, 0);//雷达低电量警告
				}
			}
			if (NULL != p_sensor_saved)
			{
				p_sensor_saved->battery_voltage[index] = get_battery_pct(p_profile->page_82.descriptive_bit_field.items.battery_status);
			}
    		break;
        case ANT_RADAR_PAGE_87_UPDATED:
            if (p_sensor_original_data->radarData.is_sensor_error == false)
            {
                p_sensor_original_data->radarData.is_sensor_error = true;
                evt_handler(EVENT_SENSOR_RADAR_ERROR,
                        &sensor_search_infor.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, 0);
            }
            break;
//        case ANT_RADAR_PAGE_70_UPDATED:
//    		break;
//        case ANT_RADAR_PAGE_REQUEST_SUCCESS:
//    		break;
//        case ANT_RADAR_PAGE_REQUEST_FAILED:
//    		break;
//        case ANT_RADAR_SHUTDOWN_DEVICE:
//    		break;
        default:
            break;
    }

    if (p_sensor_original_data->radarData.last_timestamp == 0xFFFFFFFF)
    {
        p_sensor_original_data->radarData.last_timestamp = *p_param->sysTime_s;
    }

    if (*p_param->sysTime_s > p_sensor_original_data->radarData.last_timestamp + 2)
    {
        if (p_sensor_original_data->radarData.is_sensor_timeout == false)
        {
            p_sensor_original_data->radarData.is_sensor_timeout = true;
            evt_handler(EVENT_SENSOR_RADAR_TIMEOUT,
                        &sensor_search_infor.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, 0);
        }
    }

    if (p_sensor_original_data->radarData.is_sensor_error == true || p_sensor_original_data->radarData.is_sensor_timeout == true)
    {
        p_sensor_original_data->radarData.target_number = 0;
        p_sensor_original_data->radarData.threat_level_max = 0;
        //TODO clear targets shown
    }
}

/**
 * @*********************************************************************************************
 * @description: 雷达ANT事件处理函数
 * @param {ant_evt_t} *p_ant_evt
 * @param {void *} p_context
 * @return {*}
 * @*********************************************************************************************
 */
static void radar_ant_evt(ant_evt_t *p_ant_evt, void * p_context)
{
    sensor_search_infor_t     sensor_search_infor;
    sensor_connect_infor_t    sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_RADAR, &sensor_connect);

    sensor_module_evt_handler evt_handler             = sensor_module_evt_handler_get();
    sensor_saved_work_t       *p_sensor_saved         = NULL;
    sensor_original_data_t    *p_sensor_original_data = sensor_original_data_get();
    sensor_work_state_t       sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    int8_t                    index                   = -1;
    ret_code_t                err_code                = NRF_SUCCESS;
    bool manual_connect_status_sent = false;

    sensor_systime_update();

    memset ((uint8_t *)&sensor_search_infor, 0x00, sizeof(sensor_search_infor_t));
    ant_radar_disp_evt_handler(p_ant_evt, p_context);

    if (p_ant_evt->channel == m_ant_radar.channel_number)
    {
        switch (p_ant_evt->event)
        {
            case EVENT_CHANNEL_CLOSED:
                memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
                memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
                sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
                sensor_search_infor.sensor_type = SENSOR_TYPE_RADAR;

            	sensor_ant_leave_rx_search(SENSOR_TYPE_RADAR);

                err_code = sd_ant_channel_unassign(m_ant_radar.channel_number);
                APP_ERROR_CHECK(err_code);
                m_ant_radar.channel_number = 0;
                sensor_ant_channel_num_unassign(SENSOR_TYPE_RADAR);

                bool forbidden_mask_radar = sensor_connect_infor_get_forbidden_mask(SENSOR_TYPE_RADAR);
                bool forbidden_mask_light = sensor_connect_infor_get_forbidden_mask(SENSOR_TYPE_LIGHT);
                if(sensor_connect_infor_get(SENSOR_TYPE_RADAR, &sensor_connect))
                {
                    sensor_connect_infor_clear(SENSOR_TYPE_RADAR);
                    if (NULL != evt_handler && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)
                    {
                        manual_connect_status_sent = true;
                        evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, &sensor_connect.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                    }
                }

                if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
                {
                    if (SENSOR_WORK_STATE_IDLE != sensor_work_state)
                    {
                        p_sensor_saved->rssi             [index] = 0;
                        p_sensor_saved->battery_voltage  [index] = 0xff;
                        p_sensor_saved->sensor_work_state[index] = SENSOR_WORK_STATE_SAVED;
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }
                if((forbidden_mask_radar == false && forbidden_mask_light == false && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)
                    || sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)
                {
                    sensor_wait_connect_info_loadfrom_search_info(&sensor_search_infor);
                    //connected状态下断连，检索saved数组是否有同类型sensor并进行连接
                    sensor_connect_from_saved_info(sensor_search_infor.sensor_type);
                }

                if (NULL != evt_handler)
                {
                    evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                    if (!manual_connect_status_sent)
                    {
                        evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                    }
                }

                memset(&p_sensor_original_data->radarData, 0xff, sizeof(ant_radar_data_t));
                p_sensor_original_data->radarData.is_sensor_error = false;
                p_sensor_original_data->radarData.is_sensor_timeout = false;
                if (s_is_radar_timer_started == true)
                {
                    app_timer_stop(timer_id_radar);
                    s_is_radar_timer_started = false;
                }
                p_sensor_original_data->battery_list.radar = 0;
                break;
            case EVENT_RX_FAIL_GO_TO_SEARCH:
                // sd_ant_channel_close(RADAR_CHANNEL_NUMBER);
                // sensor_ant_close(SENSOR_TYPE_RADAR);
            	sensor_ant_enter_rx_search(SENSOR_TYPE_RADAR);
                break;
            case EVENT_RX_SEARCH_TIMEOUT:
            default:
                break;
        }
    }
}

NRF_SDH_ANT_OBSERVER(m_radar_ant_observer, ANT_RADAR_ANT_OBSERVER_PRIO, radar_ant_evt, &m_ant_radar);

/**
 * @*********************************************************************************************
 * @description: 初始化ANT雷达通道
 * @param {ant_id_t} *id
 * @return {*}
 * @*********************************************************************************************
 */
void ant_radar_rx_profile_setup(ant_id_t *id)
{
    sensor_connect_infor_t    sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_RADAR, &sensor_connect);

    ant_channel_config_t channel_config;

    memcpy ((uint8_t *)&sensor_connect.sensor_id.ant_id, (uint8_t *)id, sizeof(ant_id_t));
    sensor_connect_infor_set(SENSOR_TYPE_RADAR, &sensor_connect);

    /*
    //device num的组成
    //1byte   1byte    |     1byte      |      1byte                  从左到右高到低
    //   device id     | device type    |MSN:extended device number LSN:Transmission Type
    */
    uint16_t sensor_id = (uint16_t)id->id;
    uint8_t trans_type = CHAN_ID_TRANS_TYPE;
    if (id->id > 0xffff)
    {
        trans_type = id->trans_type;
    }

    //加载参数
    LoadChnConf_radar_rx(&channel_config);
    channel_config.device_number     = sensor_id;
    channel_config.transmission_type = trans_type;

    ant_radar_disp_init(&m_ant_radar, (const ant_channel_config_t *)&channel_config, ant_radar_rx_evt_handler);
}

static void radar_timeout_handler(void *p_context)
{
    sensor_module_evt_handler   evt_handler             = sensor_module_evt_handler_get();
    sensor_module_param_input_t *p_param                = sensor_module_param_input_get();
    sensor_original_data_t      *p_sensor_original_data = sensor_original_data_get();

    if (p_sensor_original_data->radarData.last_timestamp == 0xFFFFFFFF)
    {
        p_sensor_original_data->radarData.last_timestamp = *p_param->sysTime_s;
    }

    if (*p_param->sysTime_s > p_sensor_original_data->radarData.last_timestamp + 2)
    {
        if (p_sensor_original_data->radarData.is_sensor_timeout == false)
        {
            p_sensor_original_data->radarData.is_sensor_timeout = true;
            evt_handler(EVENT_SENSOR_RADAR_TIMEOUT, NULL, SENSOR_TYPE_RADAR, SENSOR_RADIO_TYPE_ANT, 0);
        }
    }

    if (p_sensor_original_data->radarData.is_sensor_error == true || p_sensor_original_data->radarData.is_sensor_timeout == true)
    {
        p_sensor_original_data->radarData.target_number = 0;
        p_sensor_original_data->radarData.threat_level_max = 0;
        //TODO clear targets shown
    }
}

/**
 * @*********************************************************************************************
 * @description: 开启RADAR通道
 * @param {*}
 * @return {*}
 * @*********************************************************************************************
 */
void ant_radar_rx_open(void)
{
    ret_code_t             err_code          = NRF_SUCCESS;

    err_code = ant_radar_disp_open(&m_ant_radar);
    APP_ERROR_CHECK(err_code);

    app_timer_create(&timer_id_radar, APP_TIMER_MODE_REPEATED, radar_timeout_handler);
    s_is_radar_timer_started = false;
}

uint8_t ant_radar_target_cnt_get()
{
    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_RADAR, SENSOR_CONNECT_STATE_CONNECTED);

    if (res)
    {
        sensor_original_data_t * p_sensor_original_data = sensor_original_data_get();
        return p_sensor_original_data->radarData.target_number;
    }
    else
    {
    	return 0;
    }
}

uint8_t ant_radar_warning_level_get()
{
    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_RADAR, SENSOR_CONNECT_STATE_CONNECTED);

    if (res)
    {
        sensor_original_data_t * p_sensor_original_data = sensor_original_data_get();
        return p_sensor_original_data->radarData.threat_level_max;
    }
    else
    {
    	return 0;
    }
}

void ant_radar_target_info_get(uint8_t index, uint8_t* level, float* range)
{
    sensor_original_data_t * p_sensor_original_data = sensor_original_data_get();

    if (NULL == level || NULL == range)
    {
    	return;
    }

    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_RADAR, SENSOR_CONNECT_STATE_CONNECTED);

    if (res && 8 > index)
    {
        *level = p_sensor_original_data->radarData.target[index].threat_level;
        if (0 != (*level))
        {
            *range = (float)(RADAR_RANGE_UNITS * p_sensor_original_data->radarData.target[index].range);
            return;
        }
    }

    //all else
    *level = 0;
    *range = 0;
}

void ant_radar_target_log_out()
{
#if RADAR_TIMEOUT_LOG
    sensor_connect_infor_t sensor_connect;
    bool found = sensor_connect_infor_get(SENSOR_TYPE_RADAR, &sensor_connect);
	// sensor_connect_infor_t *p_sensor_connect = sensor_connect_infor_get(SENSOR_TYPE_RADAR);
    sensor_original_data_t * p_sensor_original_data = sensor_original_data_get();
	ant_radar_profile_t * p_profile = &m_ant_radar;

	if (found && SENSOR_CONNECT_STATE_CONNECTED == sensor_connect.state)
	{
		for (uint8_t i = 0; i < 8; i++)
		{
			log_file_printf("%u:[%u-%u-%u]  ", i + 1, RADAR_TARGET_THREAT_LEVEL(p_profile, i),
					RADAR_TARGET_RAGNE(p_profile, i), RADAR_TARGET_SPEED(p_profile, i));
		}
		log_file_printf("\n");
		log_file_printf("%u - %u\n", p_sensor_original_data->radarData.target_number,
				p_sensor_original_data->radarData.threat_level_max);
		for (uint8_t i = 0; i < 8; i++)
		{
			log_file_printf("%u:[%u-%u-%lu]  ", i + 1, p_sensor_original_data->radarData.target[i].threat_level,
					p_sensor_original_data->radarData.target[i].range, p_sensor_original_data->radarData.target[i].timestamp);
		}
		log_file_printf("\n");
		log_file_store();
	}
#endif
}

#endif
