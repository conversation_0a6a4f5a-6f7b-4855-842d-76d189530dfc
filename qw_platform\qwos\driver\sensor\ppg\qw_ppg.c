/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   qw_ppg.c
@Time    :   2024/12/12 11:04:15
*
**************************************************************************/

#include "qw_ppg.h"
#include "algo_ppg_task.h"
#include "ppg.h"
//#include "algo_service_component_common.h"
//#include "algo_service_task.h"
//#include "algo_service_adapter.h"
#include "algo_move_detect.h"
#include "cellwise_charger_cw6307.h"
#include "goodix_util.h"
#include "qw_sensor_common_config.h"
#include "qw_sensor_user.h"
#include "qw_system_params.h"
#include "subscribe_commu.h"
#include "subscribe_service.h"
#include "qw_circbuf.h"
#include "lsm6dso_config.h"
#include "qwos_task_def.h"
#include "../../qw_algo/algo_hai/algo_hai.h"
#include <string.h>
#include <finsh.h>

/***************************************************************
FRONT_NOLIVE_REPORT_WEAR_STATE,未识别到活体,但处于遮挡状态，
且活体重试次数少于RETRY_LIVE_WEAR_OFF_TIMES,则先上报该佩戴.
***************************************************************/
#define FRONT_NOLIVE_REPORT_WEAR_STATE PPG_WEAR_OFF
//#define FRONT_NOLIVE_REPORT_WEAR_STATE   PPG_DUBIOUS_WEAR_ON
/***************************************************************
FRONT_LIVE_REPORT_WEAR_STATE,识别到活体,
但活体重试次数少于 RETRY_LIVE_WEAR_ON_TIMES,则先上报该佩戴.
***************************************************************/
#define FRONT_LIVE_REPORT_WEAR_STATE PPG_WEAR_ON

#define IR_WEAR_ON_REPORT_WEAR_STATE PPG_DUBIOUS_WEAR_ON

#define SYSTEM_CHARGE_EVENT          0   // 充电状态改变上报
#define DRV_PPG_WEAR_EVENT           1   // 佩戴状态改变上报
#define SPORT_STATE_CHANGED          2   // 运动状态改变上报
#define ALGO_HAI_EVENT               3   // 活动强度算法事件

#define RETRY_LIVE_WEAR_ON_TIMES     1   // 活体检测连续几次判断活体，认为是真实活体
#define RETRY_LIVE_WEAR_OFF_TIMES    2   // 活体检测连续几次判断非活体，认为是真实非活体
#define RETRY_SOFTADT_TIMES          2   // 汇顶活体算法库测量超时，允许连续超时最大次数

#define IGNORE_LIVEDET_IN_SPORTTING  //开运动以后忽略活体

#undef FAKE_ZERO_XYZ

#if 0
#define GSENSOR_OFFSET_X             (18)
#define GSENSOR_OFFSET_Y             (-5)
#define GSENSOR_OFFSET_Z             (-20)
#else
#define GSENSOR_OFFSET_X             (0)
#define GSENSOR_OFFSET_Y             (0)
#define GSENSOR_OFFSET_Z             (0)
#endif

/*手动测量时若检测到未佩戴，重新判断一次活体*/
#define REDETECT_OFFBODY_IN_MEASURE 1

#define DRV_PPG_MQ_MAX_MSGS_SIZE \
    (100)   //考虑到多种应用同时响应(HR+HRV+SPO2),消息队列要求比较大

#define PPG_IRQ_THREAD_NAME "ppg_hp_task"

struct ppg_irq_msg_t
{
    uint32_t irq_type;    // 中断类型
    uint32_t irq_cnt;     // 中断计数
    uint64_t timestamp;   // 中断触发时间戳
};

// 测试命令开关宏定义
#define PPG_TEST_CMD_ENABLE  0  // 1:开启测试命令, 0:关闭测试命令


#ifdef REDETECT_OFFBODY_IN_MEASURE
// gh_hook_wear_result变量已改为static，不再需要extern声明
// 改用本地变量local_gh_wear_result来保存汇顶库的佩戴状态
#endif
#if RETRY_LIVE_WEAR_ON_TIMES > 1
static int onbody_cnt = 0;   //识别到活体后重试确认真活体次数
#endif
#if RETRY_LIVE_WEAR_OFF_TIMES > 1
static int offbody_cnt = 0;   //识别到非活体后重新确认次数.
#endif
static bool s_acc_subed = false;
static rt_mutex_t apt_acc_lock;
static drv_ppg_local_variable_t s_drv_ppg_lv;
static struct qw_sensor_device ppg_sensor;
/*acc_circle_buf,创建一个acc的circle buffer，
内存地址指向acc_raw_data,先进先出，避免数据溢出丢数据*/
static struct sensor_accel sensor_acc_cache[ACC_DATA_BUFFER_SIZE];    //acc原始数据
static struct circbuf_s acc_circle_buf;

static move_level_t g_move_level = MOVE_LEVEL_NONE;   //move detect state.
static uint8_t feed_size_cnt = 0;
#if (ACC_SAMPLING_FREQ == 26)
static uint8_t re_sampling_size = 0;
#endif
static bool is_factory_mode = false;
static bool need_report_hr_raw_data = false;
#ifdef USING_DVT2_LEDV3
static uint8_t goodix_config_index = PPG_APPLICATION_INDEX_DAILY;
#endif
static rt_sem_t ppg_int_sem = RT_NULL; // 中断触发信号量
static int ppg_irq_cnt=0;
static uint64_t irq_event_ts = 0;   //ppg irq event timestamp,unit as ms.


/*************************************************************
睡眠阶段,若放生红外遮挡->未遮挡->遮挡，则重新出发活体检测
避免睡眠后把手表放床上也识别不出非活体.
运动过程中不判断活体.
*************************************************************/
static bool is_sporting = false;

// 本地保存的汇顶库佩戴状态，用于逻辑判断
static ppg_wear_state_e local_gh_wear_result = IR_WEAR_OFF;

// 活动强度算法相关变量
static void *hai_algo_handle = NULL;  // 活动强度算法句柄
static hai_status_t current_hai_state = QW_INTENSITY_OTHER;  // 当前活动强度状态
static bool hai_force_wear_off = false;  // HAI算法设置为未佩戴标志，防止PPG覆盖。true为HAI算法高优先级，false则会被PPG覆盖


static void stop_soft_adt(void);
static void process_offbody_event(int type, int value);
static int32_t drv_ppg_start(uint32_t fun);
static int32_t drv_ppg_stop(uint32_t fun);
static void hai_state_changed_callback(hai_status_t intensity, void *user_data);
static void update_wearstate_by_hai_algo(hai_status_t hai_state);
static int32_t drv_ppg_task_haiStateChangeProcess(drv_ppg_msg_t *msg);
static int32_t get_ppg_rawdata_channels(uint32_t *ch0, uint32_t *ch1, uint32_t *ch2, uint32_t *ch3);

// PPG原始数据缓存相关变量
static struct sensor_ppg ppg_rawdata_cache[PPG_RAWDATA_CACHE_SIZE];
static uint32_t ppg_rawdata_write_index = 0;
static uint32_t ppg_rawdata_read_index = 0;
static rt_mutex_t ppg_rawdata_mutex = RT_NULL;
static bool force_ppg_always_on = false;  // 强制PPG持续工作标志

// PPG原始数据缓存相关函数声明
static void ppg_rawdata_cache_init(void);
static void ppg_rawdata_cache_deinit(void);
static void ppg_rawdata_cache_put(const struct sensor_ppg *ppg_data);

// 疑似佩戴状态检测相关函数声明
static void suspected_wear_detection_start(void);
static void suspected_wear_detection_stop(void);
static void suspected_wear_process_ppg_data(const struct sensor_ppg *ppg_data);

// PPG持续输出控制变量
static bool ppg_continuous_output = false;

// 疑似佩戴状态检测相关变量
static bool suspected_wear_detection_active = false;  // 疑似佩戴检测激活标志
static uint32_t suspected_wear_sample_count = 0;      // 疑似佩戴检测样本计数
static uint64_t suspected_wear_current_sum = 0;       // current值累计和
static uint32_t suspected_wear_valid_samples = 0;     // 有效样本数量
#define SUSPECTED_WEAR_SAMPLE_TARGET 10               // 目标采样数量
#define SUSPECTED_WEAR_CURRENT_MIN 11000              // current最小阈值
#define SUSPECTED_WEAR_CURRENT_MAX 44000              // current最大阈值

/************************************************
 *@function: qw_ppg_activate
 *@brief: 激活或停用PPG传感器
 *@return: 0表示成功，其他值表示失败
*******************************************/
static int qw_ppg_activate(struct sensor_lowerhalf_s *lower, struct qw_sensor_device *sns,
                           bool enable)
{
    // 激活或停用ppg
    // return gh3220_activate(enable);
    return 0;
}

/************************************************
 *@function: g_qw_ppg_ops
 *@brief: PPG传感器操作函数集合
 *@return: 无
*******************************************/
const struct sensor_ops_s g_qw_ppg_ops = {
    .activate = qw_ppg_activate,   // 激活函数
    // 其他操作函数
};

static const char *get_adt_status_str(ppg_wear_state_e status)
{
    switch (status)
    {
    case IR_WEAR_OFF:
        return "IR_OFF";
    case IR_WEAR_ON:
        return "IR_ON";
    case LIVING_WEAR_OFF:
        return "LIVE_OFF";
    case LIVING_WEAR_ON:
        return "LIVE_ON";
    default:
        return "UNKOWN";
    }
    return "UNKOWN";
}

/************************************************************************
 *@function:static bool need_live_wear_detect(void)
 *@brief:是否需要进行活体佩戴检测,用户未进入睡眠且活体检测开关打开则进行活体检测.
 *@return:返回false则表示不需要活体检测
*************************************************************************/
static bool need_live_wear_detect(void)
{
#if DRV_PPG_WEAR_NADT_EN
#ifdef USING_DVT2_LEDV3
    if (PPG_APPLICATION_INDEX_SPORT == goodix_config_index)
        return false;
#endif
    if (IR_WEAR_OFF == local_gh_wear_result)
        return false;
    if (!get_live_wear_switch())
    {
        return false;
    }
#ifdef IGNORE_LIVEDET_IN_SPORTTING
    if (is_sporting)
        return false;
#endif
    if (get_sleep_status())
        return false;
    return true;
#else
    return false;
#endif
}

bool get_nadt_start_flag(void)
{
    return s_drv_ppg_lv.nadt_start_flag;
}

void set_nadt_start_flag(bool onoff)
{
    s_drv_ppg_lv.nadt_start_flag = onoff;
}

/**
 * @brief PPG模块的初始化
 *
 * @return int32_t 返回操作结果
 */
static int32_t drv_ppg_init(void)
{
    return ppg_init();
}

/**
 * @brief PPG模块的反初始化
 *
 * @return int32_t 返回操作结果
 */
static int32_t drv_ppg_deinit(void)
{
    return ppg_deinit();
}

#ifdef REDETECT_OFFBODY_IN_MEASURE
static void retry_Gh3x2xDemoStartSamplingADT()
{
    DRV_PPG_LOG_W("try detect body: gh_lib=%s, local_ppg=%s",
                  get_adt_status_str(local_gh_wear_result),
                  get_wear_status_str(s_drv_ppg_lv.ppg_wear_status));

    // 修复逻辑漏洞：处理汇顶库状态与本地状态不一致的情况
    if (LIVING_WEAR_OFF == local_gh_wear_result)
    {
        // 汇顶库检测到非活体，重新进行活体检测
        stop_soft_adt();
        send_msg_to_drv_ppg_task(OPT_PPG_MSG_TYPE_NADT_ONCE, 0, NULL);
        DRV_PPG_LOG_W("retry ADT for LIVING_WEAR_OFF");
    }
    else if (LIVING_WEAR_ON == local_gh_wear_result && PPG_WEAR_OFF == s_drv_ppg_lv.ppg_wear_status)
    {
        // 修复关键漏洞：汇顶库检测到活体但本地状态是未佩戴，重新进行检测
        stop_soft_adt();
        send_msg_to_drv_ppg_task(OPT_PPG_MSG_TYPE_NADT_ONCE, 0, NULL);
        DRV_PPG_LOG_W("retry ADT for state mismatch: gh_lib=LIVING_WEAR_ON but local=PPG_WEAR_OFF");
    }
    else if (IR_WEAR_ON == local_gh_wear_result && PPG_WEAR_OFF == s_drv_ppg_lv.ppg_wear_status)
    {
        // 红外遮挡但本地状态是未佩戴，也需要重新检测
        stop_soft_adt();
        send_msg_to_drv_ppg_task(OPT_PPG_MSG_TYPE_NADT_ONCE, 0, NULL);
        DRV_PPG_LOG_W("retry ADT for IR_WEAR_ON but local=PPG_WEAR_OFF");
    }
}
#endif

static bool is_need_gsensor(const uint32_t request_algo_mask)
{
    const uint32_t acc_mask = (PPG_OUTTYPE_HR | PPG_OUTTYPE_HRV | PPG_OUTTYPE_SPO2
                               | PPG_OUTTYPE_SOFT_ADT | PPG_OUTTYPE_ADT);
    return (0 != (request_algo_mask & acc_mask));
}

static void restart_ir_adt_algo(bool force)
{
    DRV_PPG_LOG_W("restart ir adt detect @%s", get_adt_status_str(local_gh_wear_result));
    if (force || (IR_WEAR_ON == local_gh_wear_result)
        || (LIVING_WEAR_OFF == local_gh_wear_result))
    {
        move_detect_stop();
        drv_ppg_stop(PPG_OUTTYPE_ADT);
        drv_ppg_start(PPG_OUTTYPE_ADT);
        DRV_PPG_LOG_W("ir adt start");
    }
}

static uint64_t get_diff_by_unsigned(uint64_t t1, uint64_t t2)
{
    return (t1 >= t2) ? (t1 - t2) : (t2 - t1);
}

int32_t ppg_read_acc_fifo(uint64_t ppg_event_ts, STGsensorRawdata *out_accel, uint8_t len)
{
    int acc_cnt = 0;
#if __GS_NONSYNC_READ_EN__
    if ((NULL == out_accel) || !circbuf_is_init(&acc_circle_buf))
    {
        return RT_ERROR;
    }
    struct sensor_accel acc = {0};
    rt_mutex_take(apt_acc_lock, RT_WAITING_FOREVER);
    while (!circbuf_is_empty(&acc_circle_buf) && acc_cnt < len)
    {
        circbuf_read(&acc_circle_buf, &acc, sizeof(acc));
        out_accel[acc_cnt].sXAxisVal = convert_goodix_accel4G(acc.x);
        out_accel[acc_cnt].sYAxisVal = convert_goodix_accel4G(acc.y);
        out_accel[acc_cnt].sZAxisVal = convert_goodix_accel4G(acc.z);
        acc_cnt++;
    }
    rt_mutex_release(apt_acc_lock);
    // rt_kprintf("ppg get acc_cnt:%d\n", acc_cnt);
#else
    extern int ppg_irq_request_acc_fifo(struct sensor_accel * out_acc, int max);
    acc_cnt = ppg_irq_request_acc_fifo(sensor_acc_cache, ACC_DATA_BUFFER_SIZE);
    // rt_kprintf("%s cnt:%d\n", __func__, cnt);
    for (int i = 0; i < acc_cnt; i++)
    {
        out_accel[i].sXAxisVal = convert_goodix_accel4G(sensor_acc_cache[i].x);
        out_accel[i].sYAxisVal = convert_goodix_accel4G(sensor_acc_cache[i].y);
        out_accel[i].sZAxisVal = convert_goodix_accel4G(sensor_acc_cache[i].z);
    }
#endif
    return acc_cnt;
}

static void feed_move_detect(const struct sensor_accel *acc,const int size)
{
    if(NULL==acc)
    return;
    for (int i = 0; i < size; i++)
    {
        move_level_t move_lv = move_detect_feed_acc(acc->timestamp, acc->x, acc->y, acc->z);
        if (g_move_level != move_lv)
        {
            if (MOVE_LEVEL_NONE != move_lv)
            {
                DRV_PPG_LOG_I("move level:%d", move_lv);
                //检测到移动，清除HAI强制未佩戴标志，重新进行一次活体判断.
                hai_force_wear_off = false;
                restart_ir_adt_algo(false);
            }
            g_move_level = move_lv;
        }
    }
}
/************************************************************************
 *@function:static void algo_ppg_acc_raw_cb(const void *in, uint32_t len)
 *@brief:acc event callback, acc && ppg frame need about at the same time.
 *@param:in: acc raw data,len: acc raw data length
*************************************************************************/
static void algo_ppg_acc_raw_cb(const void *in, uint32_t len)
{
    //DRV_PPG_LOG_D("[%s]len=%d", __FUNCTION__, len); // 调试日志，打印函数名和输入数据长度
    const int elem_size = sizeof(struct sensor_accel);
    // 计算输入数据长度与加速度计数据结构大小的余数
    if ((NULL == in) || (0 == len) || (0 != (len % elem_size)))   // 如果余数不为0，说明数据长度不正确
    {
        DRV_PPG_LOG_E("acc len:%d error", len);                   // 错误日志，打印错误长度
        return;                                                   // 返回，不处理错误数据
    }
    // 获取加速度计数据的互斥锁，确保数据处理的线程安全
    rt_mutex_take(apt_acc_lock, RT_WAITING_FOREVER);
    const int cnt = len / elem_size;
    // 计算输入数据包含的加速度计数据个数

    circbuf_overwrite(&acc_circle_buf, in, len);
    rt_mutex_release(apt_acc_lock);

    feed_move_detect(in, cnt);

    // 将加速度数据接入活动强度算法
    if (hai_algo_handle != NULL)
    {
        const struct sensor_accel *acc_data = (const struct sensor_accel *)in;
        for (int i = 0; i < cnt; i++)
        {
            // 将加速度数据从mg转换为m/s²（1g = 9.8m/s²，1mg = 0.0098m/s²）
            float acc_x = acc_data[i].x * 0.0098f;
            float acc_y = acc_data[i].y * 0.0098f;
            float acc_z = acc_data[i].z * 0.0098f;

            // 接入活动强度算法
            qw_activity_intensity_process(acc_data[i].timestamp, acc_x, acc_y, acc_z);
        }
    }

#ifdef DRV_PPG_USE_ACC_INT
    uint8_t ppg_drv_int_source = DRV_PPG_INT_SOURCE_ACC;
    send_msg_to_drv_ppg_task(GH_DRV_MSG_TYPE_DRV_INTERRUPT, sizeof(ppg_drv_int_source), &ppg_drv_int_source);
#endif
}

/************************************************************************
 *@function:static bool auto_sub_gsensor()
 *@brief:订阅/取消订阅acc
 *@return: acc订阅被订阅状态.
*************************************************************************/
static bool auto_sub_gsensor()
{
    const bool need_acc = is_need_gsensor(s_drv_ppg_lv.request_mask);
    if (need_acc && !s_acc_subed)
    {
        optional_config_t config = {.sampling_rate = 26};
        int ret = qw_dataserver_subscribe_id(DATA_ID_RAW_ACC, algo_ppg_acc_raw_cb,
                                      &config);
        DRV_PPG_LOG_W("auto_sub_gsensor @0x%x,ret:%d", s_drv_ppg_lv.request_mask, ret);
        if (0 == ret)
        {
            s_acc_subed = true;
        }
    }
    else if (!need_acc && s_acc_subed)
    {
        int ret = qw_dataserver_unsubscribe_id(DATA_ID_RAW_ACC, algo_ppg_acc_raw_cb);
        DRV_PPG_LOG_W("auto_unsub_gsensor @0x%x ret:%d", s_drv_ppg_lv.request_mask, ret);
        if (0 == ret)
        {
            s_acc_subed = false;
        }
    }
    return s_acc_subed;
}

/**
 * @brief 开启PPG模块的操作
 *
 * @param fun 要开启的对应功能
 * @return int32_t 返回操作结果，成功返回PPG_OK，失败返回PPG_ERRINVAL
 */
static int32_t drv_ppg_start(const uint32_t fun)
{
    if ((s_drv_ppg_lv.online_mask & fun) == fun)
    {
        return PPG_ERRALREADYOPEN;
    }

    s_drv_ppg_lv.request_mask |= fun;
    auto_sub_gsensor();

    // 如果启用了强制模式，绕过佩戴检测限制
    if (force_ppg_always_on)
    {
        DRV_PPG_LOG_W("PPG force mode active, bypassing wear detection for fun:%d", fun);
    }
    else
    {
#if (DRV_PPG_WEAR_OFF_AUTO_CLOSE_APP)
        if (PPG_WEAR_OFF == s_drv_ppg_lv.ppg_wear_status)
        {
            if ((fun == PPG_OUTTYPE_HR) || (fun == PPG_OUTTYPE_HRV)
                || (fun == PPG_OUTTYPE_SPO2))
            {
                DRV_PPG_LOG_E("drv_ppg_start wearoff auto fun:%d", fun);
#ifdef REDETECT_OFFBODY_IN_MEASURE
                retry_Gh3x2xDemoStartSamplingADT();
#endif
                return PPG_ERRNOLIVENESS;
            }
        }
#endif
    }
    s_drv_ppg_lv.online_mask |= fun;
    DRV_PPG_LOG_E("[%s] FunID = %d", __FUNCTION__, fun);

    return ppg_start(fun);
}

/**
 * @brief PPG模块驱动中断处理
 *
 */
static void drv_ppg_run(uint64_t ts)
{
#ifdef PPG_IRQ_EVENT_TRACE
    rt_kprintf("drv_ppg_run @%d\n", (uint32_t) get_boot_msec());
#endif
    ppg_interruptProcess(ts);
}

/**
 * @brief 停止PPG模块的操作
 *
 * @param fun 要停止的对应功能
 * @return int32_t 返回操作结果，成功返回RT_EOK，失败返回PPG_ERRINVAL
 */
static int32_t drv_ppg_stop(uint32_t fun)
{
    if ((s_drv_ppg_lv.request_mask & fun) == 0)
    {
        return PPG_ERRALREADYCLOSE;
    }
    if ((s_drv_ppg_lv.online_mask & fun) == 0)
    {
        s_drv_ppg_lv.request_mask &= ~fun;
        return PPG_ERRALREADYCLOSE;
    }

    DRV_PPG_LOG_E("[%s] FunID = %d", __FUNCTION__, fun);

    // 清除状态
    s_drv_ppg_lv.request_mask &= ~fun;
    s_drv_ppg_lv.online_mask &= ~fun;

    return ppg_stop(fun);
}

/**
 * @brief 驱动自动停止PPG模块的操作
 *
 * @param fun 要停止的对应功能
 * @return int32_t 返回操作结果，成功返回RT_EOK，失败返回PPG_ERRINVAL
 */
static int32_t drv_ppg_auto_stop(uint32_t fun)
{
#if (DRV_PPG_WEAR_OFF_AUTO_CLOSE_APP)
    if ((s_drv_ppg_lv.online_mask & fun) == 0)
    {
        return PPG_ERRALREADYCLOSE;
    }

    // 清除状态
    s_drv_ppg_lv.online_mask &= ~fun;

    return ppg_stop(fun);
#else
    return RT_EOK;
#endif
}

static void wear_cap_callback(const void *in, uint32_t len)
{
    struct sensor_cap battery_data = {0};
    battery_data = *(struct sensor_cap *) in;
    //T_LOG_D("[%s]len=%d", __FUNCTION__, len);
    if (s_drv_ppg_lv.charge_state != (uint8_t) battery_data.status)
    {
        s_drv_ppg_lv.charge_state = (uint8_t) battery_data.status;
        send_msg_to_drv_ppg_task(USER_MSG_TYPE_CHARGE_STATE_CHANGE, sizeof(uint8_t),
                                 &s_drv_ppg_lv.charge_state);
    }
}

/**
 * @brief 给drv_ppg_task发送消息API
 *
 * @param type 消息类型
 * @param len  数据长度
 * @param data 数据
 * @return int32_t RT_ERROR ：失败 RT_EOK ：成功
 */
int32_t send_msg_to_drv_ppg_task(uint16_t type, uint16_t len, void *data)
{
    drv_ppg_msg_t msg = {0};
    int ret = 0;

    if (!s_drv_ppg_lv.thread_init_ok)
    {
        DRV_PPG_LOG_E("[%s]thread not init", __FUNCTION__);
        return RT_ERROR;
    }
    if (len && (NULL == data))
    {
        DRV_PPG_LOG_E("[%s]data null", __FUNCTION__);
        return RT_ERROR;
    }

    msg.type = type;
    msg.len = len;
    if (len > 0)
    {
        memset(msg.data, 0, sizeof(msg.data));
        memcpy(msg.data, data, len);
    }
    ret = rt_mq_send(s_drv_ppg_lv.drv_ppg_mq, &msg, sizeof(drv_ppg_msg_t));
    if (RT_EOK != ret)
    {
        DRV_PPG_LOG_E("[%s]rt_mq_send fail %d", __FUNCTION__, ret);
    }
    return ret;
}

#ifdef DRV_PPG_POLLING_INT_TIMER
static void drv_ppg_guard_timer_event_hd(void *parameter)
{
    uint8_t ppg_drv_int_source = DRV_PPG_INT_SOURCE_PPG;
    send_msg_to_drv_ppg_task(GH_DRV_MSG_TYPE_DRV_INTERRUPT, sizeof(ppg_drv_int_source),
                             &ppg_drv_int_source);
    DRV_PPG_LOG_E("into ppg guard timer,maybe lose acc event\n");
}

static void drv_ppg_del_guard_timer(void)
{
    if (s_drv_ppg_lv.drv_ppg_guard_timer)
    {
        rt_timer_delete(s_drv_ppg_lv.drv_ppg_guard_timer);
        s_drv_ppg_lv.drv_ppg_guard_timer = NULL;
    }
}

static void drv_ppg_restart_guard_timer(void)
{
    if (s_drv_ppg_lv.drv_ppg_guard_timer)
    {
        rt_timer_stop(s_drv_ppg_lv.drv_ppg_guard_timer);
        rt_timer_start(s_drv_ppg_lv.drv_ppg_guard_timer);
    }
}

static void drv_ppg_create_guard_timer(void)
{
    s_drv_ppg_lv.drv_ppg_guard_timer = rt_timer_create("drv_ppg_guard",
                                                       drv_ppg_guard_timer_event_hd,
                                                       RT_NULL, DRV_PPG_POLLING_INT_TIMER,
                                                       RT_TIMER_FLAG_PERIODIC
                                                           | RT_TIMER_FLAG_SOFT_TIMER);
    if (s_drv_ppg_lv.drv_ppg_guard_timer)
    {
        rt_timer_start(s_drv_ppg_lv.drv_ppg_guard_timer);
    }
}
#else
#define drv_ppg_del_guard_timer()
#define drv_ppg_restart_guard_timer()
#define drv_ppg_create_guard_timer()
#endif

static void drv_ppg_wear_timer_event_hd(void *parameter)
{
    DRV_PPG_LOG_E("detect live timeout!!!\n");
#if (DRV_PPG_WEAR_NADT_EN)
    send_msg_to_drv_ppg_task(OPT_PPG_MSG_TYPE_TIMEOUT_CB, 0, NULL);
#endif
}

static void drv_ppg_del_wear_timer(void)
{
    if (s_drv_ppg_lv.drv_ppg_wear_timer)
    {
        rt_timer_delete(s_drv_ppg_lv.drv_ppg_wear_timer);
        s_drv_ppg_lv.drv_ppg_wear_timer = NULL;
    }
}

static void drv_ppg_create_wear_timer(void)
{
    s_drv_ppg_lv.drv_ppg_wear_timer = rt_timer_create("drv_ppg_wear",
                                                      drv_ppg_wear_timer_event_hd,
                                                      RT_NULL,
                                                      DRV_PPG_NADT_DET_TIMEOUT * 1000,
                                                      RT_TIMER_FLAG_ONE_SHOT
                                                          | RT_TIMER_FLAG_SOFT_TIMER);
    if (s_drv_ppg_lv.drv_ppg_wear_timer)
    {
        rt_timer_start(s_drv_ppg_lv.drv_ppg_wear_timer);
    }
}

static int32_t drv_ppg_task_timeoutCBProcess(drv_ppg_msg_t *msg)
{
#if (DRV_PPG_WEAR_NADT_EN)
    s_drv_ppg_lv.nadt_start_flag = 0;
    drv_ppg_stop(PPG_OUTTYPE_SOFT_ADT);
    DRV_PPG_LOG_E("[%s]stop SOFT_ADT", __FUNCTION__);
    //只有疑似佩戴时才再次尝试(红外遮挡)
    if (PPG_DUBIOUS_WEAR_ON == s_drv_ppg_lv.ppg_wear_status)
    {
        if (s_drv_ppg_lv.live_retry_noresult < RETRY_SOFTADT_TIMES)
        {
            s_drv_ppg_lv.live_retry_noresult++;
            send_msg_to_drv_ppg_task(OPT_PPG_MSG_TYPE_NADT_ONCE, 0, NULL);
            DRV_PPG_LOG_E("[timeout]RESTART SOFT_ADT");
        }
        else
        {
            s_drv_ppg_lv.live_retry_noresult = 0;
            process_offbody_event(DRV_PPG_WEAR_EVENT, LIVING_WEAR_ON);
            DRV_PPG_LOG_E("[timeout]force set LIVING_WEAR_ON");
        }
    }
#endif
    return 0;
}

/**
 * @brief 充电状态改变消息处理
 *
 * @param msg 消息数据指针
 */
static int32_t drv_ppg_task_chargeStateChangeProcess(drv_ppg_msg_t *msg)
{
    process_offbody_event(SYSTEM_CHARGE_EVENT, get_charge_status());
    return 0;
}

/**
 * @brief 开启一次活体监测消息处理
 *
 * @param msg 消息数据指针
 */
static int32_t drv_ppg_task_onceNADTProcess(drv_ppg_msg_t *msg)
{
#if (DRV_PPG_WEAR_NADT_EN)
    s_drv_ppg_lv.nadt_start_flag = 1;
    drv_ppg_start(PPG_OUTTYPE_SOFT_ADT);
    drv_ppg_del_wear_timer();
    drv_ppg_create_wear_timer();
    DRV_PPG_LOG_E("[%s]restart SOFT_ADT", __FUNCTION__);
#endif
    return 0;
}

static void stop_soft_adt(void)
{
    drv_ppg_stop(PPG_OUTTYPE_SOFT_ADT);
    drv_ppg_del_wear_timer();
}

static void retry_detect_live_wearon(void)
{
    stop_soft_adt();
#if RETRY_LIVE_WEAR_ON_TIMES > 1
    /*活体检测重试次数,避免*/
    onbody_cnt++;
    if (onbody_cnt >= RETRY_LIVE_WEAR_ON_TIMES)
    {
        s_drv_ppg_lv.ppg_wear_status = PPG_WEAR_ON;
        onbody_cnt = 0;
    }
    else
    {
        s_drv_ppg_lv.ppg_wear_status = FRONT_LIVE_REPORT_WEAR_STATE;
        send_msg_to_drv_ppg_task(OPT_PPG_MSG_TYPE_NADT_ONCE, 0, NULL);
    }
    DRV_PPG_LOG_W("LIVING_WEAR_ON retry:%d @%s", onbody_cnt,
                  get_wear_status_str(s_drv_ppg_lv.ppg_wear_status));

#else
    s_drv_ppg_lv.ppg_wear_status = PPG_WEAR_ON;
#endif
}

static void retry_detect_live_wearoff(void)
{
    /*检测到非活体,有可能误判,多重试次数*/
#if RETRY_LIVE_WEAR_OFF_TIMES > 1
    if (PPG_WEAR_OFF != s_drv_ppg_lv.ppg_wear_status_old)
    {
        if (offbody_cnt < RETRY_LIVE_WEAR_OFF_TIMES)
        {
            //小于重试次数,不更新佩戴状态,重新测算.
            offbody_cnt++;
            send_msg_to_drv_ppg_task(OPT_PPG_MSG_TYPE_NADT_ONCE, 0, NULL);
            DRV_PPG_LOG_W("LIVE_WEAR_OFF retry:%d @%s", offbody_cnt,
                          get_wear_status_str(s_drv_ppg_lv.ppg_wear_status));
        }
        else
        {
            offbody_cnt = 0;
            DRV_PPG_LOG_W("LIVE_WEAR_OFF retry:%d @%s,will set it to wearOFF",
                          offbody_cnt, get_wear_status_str(s_drv_ppg_lv.ppg_wear_status));
            s_drv_ppg_lv.ppg_wear_status = PPG_WEAR_OFF;
            move_detect_start();   //遮挡且未佩戴状态需要启动移动检测.
        }
    }
    else
    {
        offbody_cnt = 0;
    }

#else
    s_drv_ppg_lv.ppg_wear_status = PPG_WEAR_OFF;
#endif
}

//停止所有在线运行的算法
static int stop_all_online_algo(void)
{
    int ret = 0;   // 初始化返回值为0，表示初始状态没有错误
                   // 检查在线掩码是否包含心率（HR）输出类型
    if ((s_drv_ppg_lv.online_mask & PPG_OUTTYPE_HR) == PPG_OUTTYPE_HR)
    {
        // 如果包含，则调用drv_ppg_auto_stop函数停止心率算法
        ret = drv_ppg_auto_stop(PPG_OUTTYPE_HR);
    }
    // 检查在线掩码是否包含心率变异性（HRV）输出类型
    if ((s_drv_ppg_lv.online_mask & PPG_OUTTYPE_HRV) == PPG_OUTTYPE_HRV)
    {
        // 如果包含，则调用drv_ppg_auto_stop函数停止心率变异性算法
        ret = drv_ppg_auto_stop(PPG_OUTTYPE_HRV);
    }
    if ((s_drv_ppg_lv.online_mask & PPG_OUTTYPE_SPO2) == PPG_OUTTYPE_SPO2)
    {
        ret = drv_ppg_auto_stop(PPG_OUTTYPE_SPO2);
    }
    return ret;
}

//使能所有在线运行的算法
static int start_all_online_algo(void)
{
    int ret = 0;   // 初始化返回值为0，表示初始状态没有错误
    if (((s_drv_ppg_lv.request_mask & PPG_OUTTYPE_HR) == PPG_OUTTYPE_HR)
        && ((s_drv_ppg_lv.online_mask & PPG_OUTTYPE_HR) != PPG_OUTTYPE_HR))
    {
        ret = drv_ppg_start(PPG_OUTTYPE_HR);
    }
    if (((s_drv_ppg_lv.request_mask & PPG_OUTTYPE_HRV) == PPG_OUTTYPE_HRV)
        && ((s_drv_ppg_lv.online_mask & PPG_OUTTYPE_HRV) != PPG_OUTTYPE_HRV))
    {
        if (!is_sporting)   //运动状态不测HRV
        {
            ret = drv_ppg_start(PPG_OUTTYPE_HRV);
        }
    }
    if (((s_drv_ppg_lv.request_mask & PPG_OUTTYPE_SPO2) == PPG_OUTTYPE_SPO2)
        && ((s_drv_ppg_lv.online_mask & PPG_OUTTYPE_SPO2) != PPG_OUTTYPE_SPO2))
    {
        if (!is_sporting)   //运动状态不测血氧.
        {
            ret = drv_ppg_start(PPG_OUTTYPE_SPO2);
        }
    }
    return ret;
}

// 定义一个静态函数 check_auto_algo，用于检查并自动启动或停止 PPG 算法
static int check_auto_algo(void)
{
    int ret = 0;   // 初始化返回值为0，表示初始状态没有错误

#if PPG_TEST_CMD_ENABLE
    // 如果启用了强制模式，保持PPG算法运行
    if (force_ppg_always_on)
    {
        DRV_PPG_LOG_D("PPG force mode active, keeping algorithms running");
        move_detect_stop();   // 停止移动检测
        ret = start_all_online_algo();  // 强制启动所有算法
        return ret;
    }
#endif

    // 检查PPG的佩戴状态是否为"佩戴脱落"
    if (PPG_WEAR_ON != s_drv_ppg_lv.ppg_wear_status)
    {
        ret = stop_all_online_algo();
    }
    else
    {
        move_detect_stop();   //非未佩戴状态无需进行移动检测.
        ret = start_all_online_algo();
    }
    return ret;
}

#ifdef USING_DVT2_LEDV3
/************************************************
*@function:static int drv_ppg_task_switch_application_config(drv_ppg_msg_t *msg)
*@brief:根据算法状态切换应用配置.
*@param:msg,保留.
*@return:0,成功;其他,失败.
************************************************/
static int drv_ppg_task_switch_application_config(drv_ppg_msg_t *msg)
{
    int ret = 0;
    ret = stop_all_online_algo();   //关闭所有算法
    restart_ir_adt_algo(true);      //如果当时未佩戴要强制开红外检测
    if (PPG_APPLICATION_INDEX_DAILY == goodix_config_index)   //切换到日常配置重新判断活体
    {
        send_msg_to_drv_ppg_task(OPT_PPG_MSG_TYPE_NADT_ONCE, 0, NULL);
    }
    else
    {
        stop_soft_adt();             //运动配置时不判断活体
    }
    ret = start_all_online_algo();   //启动所有算法
    return ret;
}
#endif

static bool filter_charging(void)
{
#ifndef CONFIG_FORCE_ONBODY
    s_drv_ppg_lv.charge_state = (uint8_t) get_charge_status();
#else
    s_drv_ppg_lv.charge_state = 0;
#endif

    DRV_PPG_LOG_D("%s charge_state %d", __func__, s_drv_ppg_lv.charge_state);
    if (0 == s_drv_ppg_lv.charge_state)
    {
        return false;
    }
    stop_soft_adt();
    s_drv_ppg_lv.nadt_start_flag = 0;
    s_drv_ppg_lv.live_retry_noresult = 0;
    s_drv_ppg_lv.live_retry_result = 0;
    s_drv_ppg_lv.ppg_wear_status = PPG_WEAR_OFF;
    DRV_PPG_LOG_E("[IS_CHARGING]stop SOFT_ADT");
    return true;
}

#ifdef CONFIG_FORCE_ONBODY
static void force_set_onbody()
{
    stop_soft_adt();
    s_drv_ppg_lv.charge_state = 0;
    s_drv_ppg_lv.nadt_start_flag = 0;
    s_drv_ppg_lv.live_retry_noresult = 0;
    s_drv_ppg_lv.live_retry_result = 0;
    s_drv_ppg_lv.ppg_wear_status = PPG_WEAR_ON;
}
#endif

static void process_no_need_live_detect(void)
{
    s_drv_ppg_lv.ppg_wear_status = (IR_WEAR_OFF != local_gh_wear_result)
                                    ? PPG_WEAR_ON : PPG_WEAR_OFF;
    s_drv_ppg_lv.live_retry_noresult = 0;
    s_drv_ppg_lv.live_retry_result = 0;
    s_drv_ppg_lv.nadt_start_flag = 0;
    stop_soft_adt();
}

static void update_wearstate_by_gh_hook(void)
{
    const bool need_live_detect = need_live_wear_detect();
    const int value = local_gh_wear_result;
    DRV_PPG_LOG_D("%s %s need_live: %d", __func__, get_adt_status_str(value), need_live_detect);
    if(!need_live_detect)
    {
        process_no_need_live_detect();
        return;
    }
    switch (value)
    {
    case LIVING_WEAR_ON:
        s_drv_ppg_lv.nadt_start_flag = 0;
        retry_detect_live_wearon();
        break;
    case LIVING_WEAR_OFF:
        s_drv_ppg_lv.nadt_start_flag = 0;
        stop_soft_adt();
        retry_detect_live_wearoff();
        break;
    case IR_WEAR_ON:
        //遮挡阶段.
        s_drv_ppg_lv.live_retry_noresult = 0;
        s_drv_ppg_lv.live_retry_result = 0;
        s_drv_ppg_lv.nadt_start_flag = 0;
        s_drv_ppg_lv.ppg_wear_status = IR_WEAR_ON_REPORT_WEAR_STATE;
        stop_soft_adt();
        send_msg_to_drv_ppg_task(OPT_PPG_MSG_TYPE_NADT_ONCE, 0, NULL);
        DRV_PPG_LOG_W("[IR_WEAR_ON]start SOFT_ADT");
        break;
    case IR_WEAR_OFF:
        s_drv_ppg_lv.ppg_wear_status = PPG_WEAR_OFF;
        s_drv_ppg_lv.live_retry_noresult = 0;
        s_drv_ppg_lv.live_retry_result = 0;
        s_drv_ppg_lv.nadt_start_flag = 0;
        stop_soft_adt();
        DRV_PPG_LOG_E("[IR_WEAR_OFF]stop SOFT_ADT");
        break;
    default:
        return;
    }
}

static void update_wearstate_by_sport(bool is_sport)
{
    DRV_PPG_LOG_W("update_wearstate by sport %s", is_sport ? "true" : "false");
    if (is_sport)
    {
        process_no_need_live_detect();
    }
    else
    {
        update_wearstate_by_gh_hook();
    }
}

/************************************************************************
 *@function: update_wearstate_by_hai_algo
 *@brief: 根据活动强度算法状态更新佩戴状态
 *@param: hai_state: 活动强度算法状态
 *@return: 无
 *************************************************************************/
static void update_wearstate_by_hai_algo(hai_status_t hai_state)
{
    DRV_PPG_LOG_W("update_wearstate by HAI algo: %s", qw_hai_get_state_string(hai_state));

    // 当检测到绝对静止状态时，直接判断为未佩戴，覆盖PPG的佩戴判断
    if (hai_state == QW_INTENSITY_OBJECT_STATIC)
    {
        DRV_PPG_LOG_W("HAI detected object static, force set wear OFF");

        // 如果疑似佩戴检测正在进行，让它完成后再处理
        if (suspected_wear_detection_active)
        {
            DRV_PPG_LOG_I("Suspected wear detection in progress, let it complete");
            // 不立即设置状态，等疑似佩戴检测完成后会自动处理
            return;
        }

        hai_force_wear_off = true;  // 设置强制未佩戴标志
        s_drv_ppg_lv.ppg_wear_status = PPG_WEAR_OFF;
        stop_soft_adt();
        move_detect_start();  // 静止状态需要启动移动检测
        suspected_wear_detection_stop();  // 停止疑似佩戴检测
    }
    else if (hai_state == QW_INTENSITY_SUSPECTED_STATIC)
    {
        // 疑似静止状态：启动PPG原始数据检测
        DRV_PPG_LOG_W("HAI detected suspected static, start PPG current detection");
        suspected_wear_detection_start();
    }
    else
    {
        // 其他状态下，清除强制标志，恢复正常PPG判断
        DRV_PPG_LOG_W("HAI state changed to non-static, clear hai_force_wear_off flag");
        hai_force_wear_off = false;
        suspected_wear_detection_stop();  // 停止疑似佩戴检测
        update_wearstate_by_gh_hook();
    }
}


static void process_offbody_event(int type, int value)
{
#ifdef CONFIG_FORCE_ONBODY
    send_force_onbody_event();
#else
    /*处理汇顶ADT算法上报的结果*/
    /*即便是放在充电座上也有概率识别到未遮挡,尤其是手表充电座平滑且白色的,
    所以这里把充电状态判断放最前面*/
    if (filter_charging())
    {
        move_detect_stop();   //充电状态无需进行移动检测
        goto exit_update;
    }
    /*PPG上报状态*/
    if (DRV_PPG_WEAR_EVENT == type)
    {
        // 如果HAI算法强制设置为未佩戴，则忽略PPG的佩戴判断
        if (hai_force_wear_off)
        {
            DRV_PPG_LOG_W("HAI force wear OFF active, ignore PPG wear event");
            goto exit_update;
        }
        update_wearstate_by_gh_hook();
    }
    else if (SYSTEM_CHARGE_EVENT == type)
    {
        if (!filter_charging())
        {
            /*如果在充电,直接返回未佩戴,离开充电座重新判断活体*/
            DRV_PPG_LOG_W("restart ir adt detect");
            restart_ir_adt_algo(false);
        }
    }
    else if (SPORT_STATE_CHANGED == type)
    {
        update_wearstate_by_sport(is_sporting);
    }
    else if (ALGO_HAI_EVENT == type)
    {
        update_wearstate_by_hai_algo((hai_status_t)value);
    }
#endif
exit_update:
    if (s_drv_ppg_lv.ppg_wear_status != s_drv_ppg_lv.ppg_wear_status_old)
    {
        DRV_PPG_LOG_W("BODY: %s ==>%s",
                      get_wear_status_str(s_drv_ppg_lv.ppg_wear_status_old),
                      get_wear_status_str(s_drv_ppg_lv.ppg_wear_status));
        s_drv_ppg_lv.ppg_wear_status_old = s_drv_ppg_lv.ppg_wear_status;
        algo_ppg_wear_set(s_drv_ppg_lv.ppg_wear_status);
        check_auto_algo();
        qw_ppg_algo_wear_data_t algo_wear_result = {0};
        algo_wear_result.wearState = s_drv_ppg_lv.ppg_wear_status;
        send_msg_to_algo_ppg_task(APT_MSG_TYPE_WEAR_ALGO_RESULT_REPORT,
                                  sizeof(qw_ppg_algo_wear_data_t), &algo_wear_result);
    }
}

/**
 * @brief 佩戴状态改变消息处理
 *
 * @param msg 消息数据指针
 */
static int32_t drv_ppg_task_wearStateChangeProcess(drv_ppg_msg_t *msg)
{
    if (msg == NULL)
    {
        DRV_PPG_LOG_E("wearState msg->data is NULL");
        return 0;
    }
    const ppg_wear_state_e wear_result = *((ppg_wear_state_e *) msg->data);

    // 更新本地保存的汇顶库佩戴状态
    local_gh_wear_result = wear_result;

    DRV_PPG_LOG_D("GHLib WearEvent:%s", get_adt_status_str(wear_result));
    process_offbody_event(DRV_PPG_WEAR_EVENT, wear_result);
    return 0;
}

static int32_t sport_state_changed_msg_process(drv_ppg_msg_t *msg)
{
    process_offbody_event(SPORT_STATE_CHANGED, NULL);
    return 0;
}

/************************************************************************
 *@function: hai_state_changed_callback
 *@brief: 活动强度算法状态变化回调函数
 *@param: intensity: 当前活动强度状态, user_data: 用户数据指针
 *@return: 无
 *************************************************************************/
static void hai_state_changed_callback(hai_status_t intensity, void *user_data)
{
    //DRV_PPG_LOG_I("HAI state changed: %s", qw_hai_get_state_string(intensity));

    // 更新当前状态
    current_hai_state = intensity;

    // 发送消息到PPG任务进行处理
    hai_status_t hai_state = intensity;
    send_msg_to_drv_ppg_task(USER_MSG_TYPE_ALGO_HAI_STATE_CHANGE,
                             sizeof(hai_status_t), &hai_state);
}

/************************************************************************
 *@function: drv_ppg_task_haiStateChangeProcess
 *@brief: 活动强度算法状态变化消息处理
 *@param: msg: 消息数据指针
 *@return: 0表示成功
 *************************************************************************/
static int32_t drv_ppg_task_haiStateChangeProcess(drv_ppg_msg_t *msg)
{
    if (msg == NULL)
    {
        DRV_PPG_LOG_E("HAI state msg->data is NULL");
        return 0;
    }

    const hai_status_t hai_state = *((hai_status_t *) msg->data);
    //DRV_PPG_LOG_D("HAI StateEvent: %s", qw_hai_get_state_string(hai_state));

    // 通过统一的事件处理函数处理
    process_offbody_event(ALGO_HAI_EVENT, hai_state);
    return 0;
}
/**
 * @brief HR_RAW发布订阅消息处理
 *
 * @param msg 消息数据指针
 */
static int32_t drv_ppg_task_hrRawProcess(drv_ppg_msg_t *msg)
{
    int32_t ret = 0;
    if (NULL == msg)
        return -1;
    struct sensor_ppg *raw_pub = (struct sensor_ppg *) msg->data;
#if PPG_TEST_CMD_ENABLE
    // 如果启用了强制模式，将数据存储到缓存中
    if (force_ppg_always_on)
    {
        // 添加时间戳
        raw_pub->timestamp = sensor_get_timestamp_ms() * 1000; // 转换为微秒
        ppg_rawdata_cache_put(raw_pub);
        DRV_PPG_LOG_D("PPG rawdata cached: ch0=%u, ch1=%u, ch2=%u, ch3=%u",
                      raw_pub->ppg[0], raw_pub->ppg[1], raw_pub->ppg[2], raw_pub->ppg[3]);
    }
#endif

    // 如果疑似佩戴检测激活，处理PPG数据
    if (suspected_wear_detection_active)
    {
        DRV_PPG_LOG_D("Processing PPG data for suspected wear detection: count=%d/10",
                      suspected_wear_sample_count + 1);
        suspected_wear_process_ppg_data(raw_pub);
    }

    ret = qw_dataserver_publish_id(DATA_ID_RAW_PPG_HR, raw_pub, sizeof(struct sensor_ppg));
    if (ret != 0)
    {
        DRV_PPG_LOG_E("ppg publish out erro ret:%d", ret);
    }
    return ret;
}

static void inc_hr_subs_num(uint32_t mode,bool only_raw_data)
{
    DRV_PPG_LOG_W("inc_hr_subs_num mode:%d,only_raw_data:%d\n",mode,only_raw_data);
    switch (mode){
        case HR_ALGO_MODE_BG:
#if (SUBS_UNSUBS_IN_PAIRS)
            only_raw_data ? (s_drv_ppg_lv.ppg_hr_rawdata_subs_num++)
                                    : (s_drv_ppg_lv.ppg_hr_subs_num++);
#else
            only_raw_data ? (s_drv_ppg_lv.ppg_hr_rawdata_subs_num = 1)
                                    : (s_drv_ppg_lv.ppg_hr_subs_num = 1);
#endif
            break;
        case HR_ALGO_MODE_MANUAL:
        case HR_ALGO_MODE_SPORT:
#if (SUBS_UNSUBS_IN_PAIRS)
            only_raw_data ? (s_drv_ppg_lv.ppg_hr_rawdata_subs_num++)
                                    : (s_drv_ppg_lv.ppg_hr_run_subs_num++);
#else
            only_raw_data ? (s_drv_ppg_lv.ppg_hr_rawdata_subs_num = 1)
                                    : (s_drv_ppg_lv.ppg_hr_run_subs_num = 1);
#endif
            break;
        case HR_ALGO_MODE_MAX:
        default:
            break;
    }
}

static void dec_hr_subs_num(uint32_t mode, bool only_raw_data)
{
    DRV_PPG_LOG_W("dec_hr_subs_num mode:%d,only_raw_data:%d\n", mode, only_raw_data);
    switch (mode)
    {
    case HR_ALGO_MODE_BG:
#if (SUBS_UNSUBS_IN_PAIRS)
        only_raw_data ? (s_drv_ppg_lv.ppg_hr_rawdata_subs_num--)
                                : (s_drv_ppg_lv.ppg_hr_subs_num--);
#else
        only_raw_data ? (s_drv_ppg_lv.ppg_hr_rawdata_subs_num = 0)
                                : (s_drv_ppg_lv.ppg_hr_subs_num = 0);
#endif
        break;
    case HR_ALGO_MODE_MANUAL:
    case HR_ALGO_MODE_SPORT:
#if (SUBS_UNSUBS_IN_PAIRS)
        only_raw_data ? (s_drv_ppg_lv.ppg_hr_rawdata_subs_num--)
                                : (s_drv_ppg_lv.ppg_hr_run_subs_num--);
#else
        only_raw_data ? (s_drv_ppg_lv.ppg_hr_rawdata_subs_num = 0)
                                : (s_drv_ppg_lv.ppg_hr_run_subs_num = 0);
#endif
        break;
    case HR_ALGO_MODE_MAX:
    default:
        break;
    }
}

/**
 * @brief 请求订阅消息处理
 *
 * @param msg 消息数据指针
 */
static int32_t drv_ppg_task_mqSubsProcess(drv_ppg_msg_t *msg)
{
    int32_t ret = 0;
    qw_request_data_t *msg_data = (qw_request_data_t *) msg->data;

    //DRV_PPG_LOG_D("[%s]%d %d %d", __FUNCTION__, msg_data->qwDataType, msg_data->sampInterval, msg_data->reportInterval);
    DRV_PPG_LOG_E("[%s]%d %d %d %d %d", __FUNCTION__, msg_data->qwDataType,
                  s_drv_ppg_lv.ppg_hr_subs_num, s_drv_ppg_lv.ppg_wear_subs_num,
                  s_drv_ppg_lv.ppg_spo2_subs_num, s_drv_ppg_lv.ppg_hrv_subs_num);

    switch (msg_data->qwDataType)
    {
    case DATA_ID_RAW_PPG_HR:
    {
#ifdef USING_DVT2_LEDV3
        //inc_hr_subs_num(msg_data->run_mode, msg_data->with_raw_data);
        inc_hr_subs_num(HR_ALGO_MODE_BG, msg_data->with_raw_data);
#else
        inc_hr_subs_num(HR_ALGO_MODE_BG, msg_data->with_raw_data);
#endif

        rt_kprintf("ppg_hr_subs_num:%d ppg_hr_rawdata_subs_num:%d\n",
                   s_drv_ppg_lv.ppg_hr_subs_num, s_drv_ppg_lv.ppg_hr_rawdata_subs_num);
        drv_ppg_start(PPG_OUTTYPE_HR);
    }
    break;

#ifdef PPG_ALGO_HRV_EN
    case DATA_ID_RAW_PPG_HRV:
    {
        if (0 == s_drv_ppg_lv.ppg_hrv_subs_num)
        {
            drv_ppg_start(PPG_OUTTYPE_HRV);
        }
#if (SUBS_UNSUBS_IN_PAIRS)
        s_drv_ppg_lv.ppg_hrv_subs_num++;
#else
        s_drv_ppg_lv.ppg_hrv_subs_num = 1;
#endif
    }
    break;
#endif

#ifdef PPG_ALGO_SPO2_EN
    case DATA_ID_RAW_PPG_SPO2:
    {
        if (0 == s_drv_ppg_lv.ppg_spo2_subs_num)
        {
            drv_ppg_start(PPG_OUTTYPE_SPO2);
        }
#if (SUBS_UNSUBS_IN_PAIRS)
        s_drv_ppg_lv.ppg_spo2_subs_num++;
#else
        s_drv_ppg_lv.ppg_spo2_subs_num = 1;
#endif
    }
    break;
#endif

    case DATA_ID_RAW_PPG_WEAR:
    {
        if (0 == s_drv_ppg_lv.ppg_wear_subs_num)
        {
            restart_ir_adt_algo(true);
#ifndef CONFIG_FORCE_ONBODY
            optional_config_t config = {.sampling_rate = 0};
            ret = qw_dataserver_subscribe_id(DATA_ID_CAP, wear_cap_callback,
                                          &config);
            DRV_PPG_LOG_D("[%s]WEAR CAP subscribe ret=%d", __FUNCTION__, ret);
            if (ret != 0)
            {
                DRV_PPG_LOG_E("[%s]WEAR CAP subscribe erro ret:%d", __FUNCTION__, ret);
            }
#endif
        }
#if (SUBS_UNSUBS_IN_PAIRS)
        s_drv_ppg_lv.ppg_wear_subs_num++;
#else
        s_drv_ppg_lv.ppg_wear_subs_num = 1;
#endif
    }
    break;
    default:
        break;
    }

    return ret;
}

/**
 * @brief 取消订阅消息处理
 *
 * @param msg 消息数据指针
 */
static int32_t drv_ppg_task_mqUnsubsProcess(drv_ppg_msg_t *msg)
{
    int32_t ret = 0;
    qw_request_data_t *msg_data = (qw_request_data_t *) msg->data;

    //DRV_PPG_LOG_D("[%s]%d %d %d", __FUNCTION__, msg_data->qwDataType, msg_data->sampInterval, msg_data->reportInterval);
    DRV_PPG_LOG_E("[%s]%d %d %d %d %d", __FUNCTION__, msg_data->qwDataType,
                  s_drv_ppg_lv.ppg_hr_subs_num, s_drv_ppg_lv.ppg_wear_subs_num,
                  s_drv_ppg_lv.ppg_spo2_subs_num, s_drv_ppg_lv.ppg_hrv_subs_num);

    switch (msg_data->qwDataType)
    {
    case DATA_ID_RAW_PPG_HR:
    {
#ifdef USING_DVT2_LEDV3
        //dec_hr_subs_num(msg_data->run_mode, msg_data->with_raw_data);
        dec_hr_subs_num(HR_ALGO_MODE_BG, msg_data->with_raw_data);
#else
        dec_hr_subs_num(HR_ALGO_MODE_BG, msg_data->with_raw_data);
#endif
        rt_kprintf("unsub ppg_hr_subs_num:%d ppg_hr_rawdata_subs_num:%d\n",
                   s_drv_ppg_lv.ppg_hr_subs_num, s_drv_ppg_lv.ppg_hr_rawdata_subs_num);

        if (0 == s_drv_ppg_lv.ppg_hr_subs_num && 0 == s_drv_ppg_lv.ppg_hr_rawdata_subs_num
            && 0 == s_drv_ppg_lv.ppg_hr_run_subs_num)
        {
            ret = drv_ppg_stop(PPG_OUTTYPE_HR);
            if (ret != 0)
            {
                DRV_PPG_LOG_E("[%s]HR stop ret:%d", __FUNCTION__, ret);
                return -1;
            }
        }
    }
    break;

#ifdef PPG_ALGO_HRV_EN
    case DATA_ID_RAW_PPG_HRV:
    {
        if (0 == s_drv_ppg_lv.ppg_hrv_subs_num)
        {
            DRV_PPG_LOG_E("[%s]HRV unsubscribe erro no ppg_hrv_subs", __FUNCTION__);
            return -1;
        }
        else
        {
            s_drv_ppg_lv.ppg_hrv_subs_num--;
            if (0 == s_drv_ppg_lv.ppg_hrv_subs_num)
            {
                ret = drv_ppg_stop(PPG_OUTTYPE_HRV);
                if (ret != 0)
                {
                    //DRV_PPG_LOG_E("[%s]HRV unsubscribe erro ret:%d", __FUNCTION__, ret);
                    return -1;
                }
            }
        }
    }
    break;
#endif

#ifdef PPG_ALGO_SPO2_EN
    case DATA_ID_RAW_PPG_SPO2:
    {
        if (0 == s_drv_ppg_lv.ppg_spo2_subs_num)
        {
            DRV_PPG_LOG_E("[%s]SPO2 unsubscribe erro no ppg_spo2_subs", __FUNCTION__);
            return -1;
        }
        else
        {
            s_drv_ppg_lv.ppg_spo2_subs_num--;
            if (0 == s_drv_ppg_lv.ppg_spo2_subs_num)
            {
                ret = drv_ppg_stop(PPG_OUTTYPE_SPO2);
                if (ret != 0)
                {
                    //DRV_PPG_LOG_E("[%s]SPO2 unsubscribe erro ret:%d", __FUNCTION__, ret);
                    return -1;
                }
            }
        }
    }
    break;
#endif

    case DATA_ID_RAW_PPG_WEAR:
    {
        if (0 == s_drv_ppg_lv.ppg_wear_subs_num)
        {
            DRV_PPG_LOG_E("[%s]WEAR unsubscribe erro no ppg_wear_subs", __FUNCTION__);
            return -1;
        }
        else
        {
            s_drv_ppg_lv.ppg_wear_subs_num--;
            if (0 == s_drv_ppg_lv.ppg_wear_subs_num)
            {
                ret = drv_ppg_stop(PPG_OUTTYPE_ADT);
                if (ret != 0)
                {
                    DRV_PPG_LOG_E("[%s]WEAR unsubscribe erro ret:%d", __FUNCTION__, ret);
                    return -1;
                }
            }
        }
    }
    break;
    default:
        break;
    }

    return ret;
}

/**
 * @brief drv_ppg_task线程消息处理
 *
 * @param msg 消息数据指针
 */
static int32_t drv_ppg_task_mqProcess(drv_ppg_msg_t *msg)
{
    int32_t ret = 0;

    switch (msg->type)
    {
    case DRV_PPG_MSG_TYPE_SUBS:
    {
        ret = drv_ppg_task_mqSubsProcess(msg);
    }
    break;

    case DRV_PPG_MSG_TYPE_UNSUBS:
    {
        ret = drv_ppg_task_mqUnsubsProcess(msg);
    }
    break;

    case GH_DRV_MSG_TYPE_DRV_INTERRUPT:
    {
        drv_ppg_restart_guard_timer();

        struct ppg_irq_msg_t *irq_msg = (struct ppg_irq_msg_t *)msg->data;
#ifdef PPG_IRQ_EVENT_TRACE
        rt_kprintf("PPG_IRQ:%d ts@%d.%03d\n", irq_msg->irq_cnt,
        (int) (irq_msg->timestamp / 1000), (int) (irq_msg->timestamp % 1000));
#endif
        if (irq_msg != NULL)
        {
            drv_ppg_run(irq_msg->timestamp);
        }
        else
        {
            drv_ppg_run(sensor_get_timestamp_ms());
        }
    }
    break;

    case GH_DRV_MSG_TYPE_HR_RAW_REPORT:
    {
        ret = drv_ppg_task_hrRawProcess(msg);
    }
    break;

    case GH_DRV_MSG_TYPE_WEAR_STATE_CHANGE:
    {
        ret = drv_ppg_task_wearStateChangeProcess(msg);
    }
    break;
    case USER_MSG_TYPE_SPORT_STATE_CHANGED:
    {
        ret = sport_state_changed_msg_process(msg);
    }
    break;
    case USER_MSG_TYPE_CHARGE_STATE_CHANGE:
    {
        ret = drv_ppg_task_chargeStateChangeProcess(msg);
    }
    break;
    case USER_MSG_TYPE_ALGO_HAI_STATE_CHANGE:
    {
        ret = drv_ppg_task_haiStateChangeProcess(msg);
    }
    break;

    case OPT_PPG_MSG_TYPE_TIMEOUT_CB:
    {
        ret = drv_ppg_task_timeoutCBProcess(msg);
    }
    break;
    case OPT_PPG_MSG_TYPE_NADT_ONCE:
    {
        ret = drv_ppg_task_onceNADTProcess(msg);
    }
    break;
#ifdef USING_DVT2_LEDV3
    case USER_MSG_TYPE_SWITCH_APPLICATION:
    {
        ret = drv_ppg_task_switch_application_config(msg);
    }
    break;
#endif
    default:
        break;
    }

    return ret;
}


/************************************************************************
 *@function:static int32_t drv_ppg_factory_process(void)
 *@brief:汇顶工厂模式消息处理
 *@return:成功返回RT_EOK,失败返回其他值
*************************************************************************/
static int32_t drv_ppg_factory_process(drv_ppg_msg_t *msg)
{
    int32_t ret = 0;
    static bool stop_app = false;
    if (!stop_app)
    {
        drv_ppg_stop(~0);
        stop_app = true;
    }
    switch (msg->type)
    {
    case GH_DRV_MSG_TYPE_DRV_INTERRUPT:
    {
        drv_ppg_run(0);
    }
    break;

    default:
        break;
    }

    return ret;
}

/************************************************************************
 *@function:static int drv_thread_pre_init(void)
 *@brief:ppg 驱动线程初始化之前要先初始化汇顶驱动
 *@return:成功返回RT_EOK,失败返回其他值
*************************************************************************/
static int drv_thread_pre_init(void)
{
    int ret = drv_ppg_init();
    if (ret != RT_EOK)
    {
        DRV_PPG_LOG_E("PPG_Init failed\n");
    }
    else
    {
        s_drv_ppg_lv.thread_init_ok = TRUE;
#ifdef CONFIG_FORCE_ONBODY
        s_drv_ppg_lv.ppg_wear_status = PPG_WEAR_ON;
#else
        s_drv_ppg_lv.ppg_wear_status = (LIVING_WEAR_ON == local_gh_wear_result)
                                         ? PPG_WEAR_ON
                                         : PPG_WEAR_OFF;
#endif
        s_drv_ppg_lv.ppg_wear_status_old = PPG_WEAR_STATE_UNINIT;
        DRV_PPG_LOG_E("drv_ppg_task_init OK");
        //创建守护定时，防止acc数据未按预计发布，而导致ppg不工作。
        drv_ppg_del_guard_timer();
        drv_ppg_create_guard_timer();

        // 初始化PPG原始数据缓存
        ppg_rawdata_cache_init();
    }
	fat_dev_info_t fat_dev_info={0};
	fat_dev_info.dev_type = FAT_DEV_PPG;
	fat_dev_info.dev_id = 0;
	fat_dev_info.dev_state = ret==RT_EOK?1:0;
	fat_set_dev_init_info(fat_dev_info);
    return ret;
}


#ifdef USING_DVT2_LEDV3
static void auto_switch_ppg_application_config_index(void)
{
    uint8_t app_index = is_sporting ? PPG_APPLICATION_INDEX_SPORT : PPG_APPLICATION_INDEX_DAILY;
    if (app_index != goodix_config_index)
    {
        goodix_config_index = app_index;
        DRV_PPG_LOG_W("set goodix_config_index:%d\n", goodix_config_index);
        send_msg_to_drv_ppg_task(USER_MSG_TYPE_SWITCH_APPLICATION, 0, NULL);
    }
}

/************************************************
*@function:int get_ppg_application_config_index(void);
*@brief:PPG方案3支持日常/运动两种模式
*@param:1:运动配置 0:日常配置
************************************************/
int get_ppg_application_config_index(void)
{
    return (0 == goodix_config_index) ? PPG_APPLICATION_INDEX_DAILY : PPG_APPLICATION_INDEX_SPORT;
}

#endif

#ifdef IGNORE_LIVEDET_IN_SPORTTING
static void sport_state_chg_callback(const void *data, uint32_t len)
{
    static bool old_is_sporting = false;
    if (NULL == data || len != sizeof(algo_sports_ctrl_t))
    {
        DRV_PPG_LOG_W("sport_state_chg_callback erro len:%u", len);
        return;
    }
    const algo_sports_ctrl_t *sport_remind = (algo_sports_ctrl_t *) data;
    const saving_status_e sport_status = sport_remind->saving_status;
    switch (sport_status)
    {
    case enum_status_free:
        is_sporting = false;
        break;
    case enum_status_ready:
        is_sporting = true;
        break;
    default:
        break;
    }
    if (old_is_sporting != is_sporting)
    {
#ifdef USING_DVT2_LEDV3
        auto_switch_ppg_application_config_index();
#endif
        old_is_sporting = is_sporting;
        send_msg_to_drv_ppg_task(USER_MSG_TYPE_SPORT_STATE_CHANGED, 0, NULL);
    }
}


static void monitor_sport_state(void)
{
    optional_config_t config = {.sampling_rate = 0};
    int ret = qw_dataserver_subscribe_id(DATA_ID_EVENT_SPORTS_CTRL,
        sport_state_chg_callback, &config);
    DRV_PPG_LOG_W("sub %s ret:%d", CONFIG_QW_EVENT_NAME_SPORTS_CTRL, ret);

}
#endif


/**
 * @brief drv_ppg_task线程实体
 *
 * @param parameter 未使用
 */
static void drv_ppg_thread_entry(void *parameter)
{
#ifdef IGNORE_LIVEDET_IN_SPORTTING
    monitor_sport_state();
#endif
    while (1)
    {
        drv_ppg_msg_t msg = {0};
        if (rt_mq_recv(s_drv_ppg_lv.drv_ppg_mq, &msg, sizeof(drv_ppg_msg_t),
                       RT_WAITING_FOREVER)
            == RT_EOK)
        {
            is_factory_mode = get_system_params()->boot_env.is_factory_mode;
            //DRV_PPG_LOG_W("[%s] msg_type=%d", __FUNCTION__, msg.type );
            if (!is_factory_mode)
            {
                drv_ppg_task_mqProcess(&msg);
            }
            else
            {
                drv_ppg_factory_process(&msg);
            }
        }
    }
}

/************************************************************************
 *@function:const char *get_wear_status_str(uint8_t status)
 *@brief:将佩戴状态转换为字符串
 *@param:佩戴状态
 *@return:返回佩戴状态字符串
*************************************************************************/
const char *get_wear_status_str(uint8_t status)
{
    switch (status)
    {
    case PPG_WEAR_ON:
        return "ON";
    case PPG_DUBIOUS_WEAR_ON:
        return "DUBIOUS_ON";
    case PPG_WEAR_OFF:
        return "OFF";
    default:
        return "UNKOWN";
    }
    return "UNKOWN";
}


/************************************************************************
 *@function:void ppg_bottom_irq_request(void)
 *@brief:ppg中断处理函数,函数中不能有阻塞操作,spi读写接口有阻塞操作.
 *@return:None
*************************************************************************/
void ppg_irq_request(void)
{
    ppg_irq_cnt++;
    //update_gh3x2x_irq_ts(sensor_get_timestamp_ms());
    update_gh3x2x_irq_ts(ppg_irq_cnt);
    if (ppg_int_sem)
    {
        rt_sem_release(ppg_int_sem);
    }
}

/************************************************************************
 *@function: ppg_user_irq_handler
 *@brief: 优化的PPG中断处理函数，替代gh_demo_user.c中的处理
 *@param: arg - 中断参数
 *@return: none
 *************************************************************************/
extern uint8_t g_uchGh3x2xIntCallBackIsCalled;
void ppg_user_irq_handler(void *arg)
{
//  uint32_t ts1 = (uint32_t) get_boot_msec();
    g_uchGh3x2xIntCallBackIsCalled = 1;
    ppg_irq_request();
//  uint32_t ts2 = (uint32_t) get_boot_msec();
//  rt_kprintf("ppg irq cost:%u\n", ts2 - ts1);
}

static void send_ppg_irq_event(void)
{
    struct ppg_irq_msg_t ppg_irq_msg;
    ppg_irq_msg.irq_type = DRV_PPG_INT_SOURCE_PPG;
    ppg_irq_msg.irq_cnt = ppg_irq_cnt;
    ppg_irq_msg.timestamp = irq_event_ts;
    send_msg_to_drv_ppg_task(GH_DRV_MSG_TYPE_DRV_INTERRUPT, sizeof(ppg_irq_msg), &ppg_irq_msg);
}

static void ppg_irq_task_entry(void *param)
{
    while (1)
    {
        if (!ppg_int_sem)
            continue;
        rt_sem_take(ppg_int_sem, RT_WAITING_FOREVER);
        send_ppg_irq_event();
    }
}

static void ppg_hpwork_task_init(void)
{
    if (ppg_int_sem == NULL)
    {
        ppg_int_sem = rt_sem_create("ppg_hp_sem", 0, RT_IPC_FLAG_FIFO);
        RT_ASSERT(ppg_int_sem);
    }
    rt_thread_t ppg_hp_thread = rt_thread_find(LSM6DSO_TASK_NAME);
    if (ppg_hp_thread == RT_NULL)
    {
        ppg_hp_thread = rt_thread_create(PPG_IRQ_THREAD_NAME, ppg_irq_task_entry, RT_NULL, 2 * 1024, PPG_IRQ_THREAD_PRIORITY, 10);
        if (ppg_hp_thread != RT_NULL)
        {
            rt_thread_startup(ppg_hp_thread);
        }
    }
    else
    {
        rt_thread_startup(ppg_hp_thread);
    }
}

/************************************************
*@function:void update_gh3x2x_irq_ts(uint64_t event_ts)
*@brief:设置ppg产生中断的时间戳，因为Gh3x2xDemoInterruptProcess不能在中断
上下文执行，所以需要通过这个函数将时间戳传递给Gh3x2xDemoInterruptProcess函数。
*@param:event_ts: 记录中断产生的时间戳.
************************************************/
void update_gh3x2x_irq_ts(uint64_t event_ts)
{
    irq_event_ts = event_ts;
}

uint64_t get_gh3x2x_irq_ts(void)
{
    return irq_event_ts;
}

/************************************************
*@function:bool need_report_hr_rawdata(void)
*@brief:判断是否需要上报HR原始数据,因为心率算法需要PPG原始数据，
应用层也有单独使用ppg原始数据的,这里需要判断如果仅有心率算法需要则不上报始数据.
*@return:true: 需要上报HR原始数据，false:不需要上报HR原始数据.
************************************************/
bool need_report_hr_rawdata(void)
{
#if PPG_TEST_CMD_ENABLE
    // 如果启用了强制模式，总是需要上报原始数据
    if (force_ppg_always_on)
    {
        return true;
    }
#endif

    // 如果疑似佩戴检测激活，需要上报原始数据
    if (suspected_wear_detection_active)
    {
        return true;
    }

    return s_drv_ppg_lv.ppg_hr_rawdata_subs_num > 0;
}

#ifdef CONFIG_PPG_CAPTURE

static bool ghealth_is_ready = false;
static bool ble_is_ready=false;

/************************************************************************
 *@function:bool ghealth_protocal_is_ready(void)
 *@brief:ghealth app 是否已经连接
 *@return:false:未连接，true:已连接且可用
*************************************************************************/
bool ghealth_protocal_is_ready(void)
{
    return ghealth_is_ready;
}

void process_goodix_connect_event(uint8_t event)
{
    DRV_PPG_LOG_W("%s  event:%d", __func__, event);
    if (BLE_CONNECT_STATE_GOODIX_READY == event)
    {
        //开启心率推送timer.
        ghealth_is_ready = true;
    }
    else if (BLE_CONNECT_STATE_CONNECTED == event)
    {
        //蓝牙连接成功后,判断是否要蓝牙推送心率
        ble_is_ready = true;
    }
    else if (BLE_CONNECT_STATE_DISCONNECTED == event)
    {
        ghealth_is_ready = false;
        ble_is_ready = false;
    }
}

#endif

/************************************************
 *@function: qw_ppg_register
 *@brief: 注册PPG传感器
 *@return: 0表示成功，其他值表示失败
*******************************************/
static int qw_ppg_register(void)
{
    int ret = 0;
    set_live_wear_switch(1);   // 默认开启实时佩戴检测
    circbuf_init(&acc_circle_buf, sensor_acc_cache, sizeof(sensor_acc_cache));
#ifdef CONFIG_PPG_CAPTURE
    update_capture_flag(CAPTURE_MASK_HR,true);
#endif
    memset(&ppg_sensor, 0, sizeof(ppg_sensor));
    ppg_sensor.attach.hw_driver.ops = &g_qw_ppg_ops;
    ppg_sensor.attach.hw_driver.type = SENSOR_TYPE_PPG;
    ppg_sensor.attach.hw_driver.uncalibrated = false;   //未校准
    ppg_sensor.attach.hw_driver.nbuffer = 1;    //sensor_accel circular buffer count.
    //rt_thread_delay(2500);
    ret = qw_sensor_register(&ppg_sensor, 0);   // 注册传感器
    if (ret < 0)
    {
        DRV_PPG_LOG_E("register ppg fail: %d", ret);   // 注册失败日志
    }

    memset(&s_drv_ppg_lv, 0, sizeof(drv_ppg_local_variable_t));
    apt_acc_lock = rt_mutex_create("ppg_gsensor", RT_IPC_FLAG_FIFO);
    RT_ASSERT(apt_acc_lock);


    // 初始化活动强度算法
    hai_algo_handle = qw_activity_intensity_init(hai_state_changed_callback, NULL);
    if (hai_algo_handle != NULL)
    {
        DRV_PPG_LOG_I("HAI algorithm initialized successfully");
    }
    else
    {
        DRV_PPG_LOG_E("HAI algorithm initialization failed");
    }

    // 创建ppg算法线程消息队列
    s_drv_ppg_lv.drv_ppg_mq = rt_mq_create("drv_ppg_mq", sizeof(drv_ppg_msg_t),
                                           DRV_PPG_MQ_MAX_MSGS_SIZE, RT_IPC_FLAG_FIFO);
    RT_ASSERT(s_drv_ppg_lv.drv_ppg_mq);

    // 创建ppg算法线程
    if (rt_thread_find("drv_ppg_task") == RT_NULL)
    {
        s_drv_ppg_lv.drv_ppg_thread = rt_thread_create("drv_ppg_task",
                                                       drv_ppg_thread_entry, RT_NULL,
                                                       DRV_PPG_THREAD_STACK_SIZE+8192,
                                                       HIGH__ALGO_TASK_PRIORITY_L1,
                                                       RT_THREAD_TICK_DEFAULT);
        RT_ASSERT(s_drv_ppg_lv.drv_ppg_thread);
    }
    // 启动线程
    rt_thread_startup(s_drv_ppg_lv.drv_ppg_thread);
    ppg_hpwork_task_init();
    ret = drv_thread_pre_init();
    if (RT_EOK != ret)
    {
        DRV_PPG_LOG_E("drv_thread_pre_init @:%d\n", ret);
    }
    return ret;   // 返回注册结果
}

#ifndef CONFIG_FACTORY_VERSION
INIT_DEVICE_EXPORT(qw_ppg_register);
#endif


#include "rtthread.h"
#include "register.h"  // 包含IRQn定义

// 定义目标中断列表及其名称
typedef struct
{
    IRQn_Type irq;
    const char *name;
} irq_debug_t;

irq_debug_t irq_list[] = {{SysTick_IRQn, "SysTick(tick)"},   // 系统滴答
                          {LPTIM2_IRQn, "LPTIMER"},          // LPTIMER中断
                          {HCPU2LCPU_IRQn, "Mailbox"},       // Mailbox中断
                          {BLE_MAC_IRQn, "BLE"},             // BLE中断
                          {GPIO2_IRQn, "GPIO"},              // GPIO中断
                          {USART4_IRQn, "USART4_IRQn"},
                          {I2C4_IRQn, "I2C4_IRQn"},
                          {I2C6_IRQn, "I2C6_IRQn"},
                          {SDADC_IRQn, "SDADC_IRQn"},
                          {QSPI4_IRQn, "QSPI4_IRQn"},
                          {AON_IRQn, "AON_IRQn"}
};

void print_irq_priorities(void)
{
    rt_kprintf("LCPU中断优先级列表:\n");
    for (int i = 0; i < sizeof(irq_list) / sizeof(irq_list[0]); i++)
    {
        IRQn_Type irq = irq_list[i].irq;
        const char *name = irq_list[i].name;
#if 0
        // 计算IPR寄存器地址和偏移
        uint32_t ipr_reg_idx = irq / 4;
        uint32_t offset = (irq % 4) * 8;
        volatile uint32_t *ipr_reg = (volatile uint32_t *)(0xE000E400 + ipr_reg_idx * 4);
        // 提取优先级（低8位）
        uint8_t priority = (*ipr_reg >> offset) & 0xFF;
        rt_kprintf("%-12s (IRQn=%-3d): 优先级=0x%02X\n", name, irq, priority);

#else
        uint32_t PriorityGroup = NVIC_GetPriorityGrouping(), pPreemptPriority, pSubPriority;
        HAL_NVIC_GetPriority(irq, PriorityGroup, &PriorityGroup, &pSubPriority);
        rt_kprintf("%-12s (IRQn=%-3d): 优先级=0x%02X\n", name, irq, PriorityGroup);
#endif
    }
}

// 注册为命令，通过串口调用
MSH_CMD_EXPORT(print_irq_priorities, Print LCPU IRQ priorities);

/************************************************************************
 *@function: static void ppg_rawdata_cache_init(void)
 *@brief: 初始化PPG原始数据缓存
 *@return: 无
 *************************************************************************/
static void ppg_rawdata_cache_init(void)
{
    if (ppg_rawdata_mutex == RT_NULL)
    {
        ppg_rawdata_mutex = rt_mutex_create("ppg_raw_mutex", RT_IPC_FLAG_FIFO);
        if (ppg_rawdata_mutex == RT_NULL)
        {
            DRV_PPG_LOG_E("Failed to create PPG rawdata mutex");
            return;
        }
    }

    ppg_rawdata_write_index = 0;
    ppg_rawdata_read_index = 0;
    memset(ppg_rawdata_cache, 0, sizeof(ppg_rawdata_cache));
    DRV_PPG_LOG_I("PPG rawdata cache initialized");
}

/************************************************************************
 *@function: static void ppg_rawdata_cache_deinit(void)
 *@brief: 反初始化PPG原始数据缓存
 *@return: 无
 *************************************************************************/
static void ppg_rawdata_cache_deinit(void)
{
    if (ppg_rawdata_mutex != RT_NULL)
    {
        rt_mutex_delete(ppg_rawdata_mutex);
        ppg_rawdata_mutex = RT_NULL;
    }

    ppg_rawdata_write_index = 0;
    ppg_rawdata_read_index = 0;
    DRV_PPG_LOG_I("PPG rawdata cache deinitialized");
}

/************************************************************************
 *@function: static void suspected_wear_detection_start(void)
 *@brief: 启动疑似佩戴状态检测
 *@return: 无
 *************************************************************************/
static void suspected_wear_detection_start(void)
{
    if (suspected_wear_detection_active)
    {
        DRV_PPG_LOG_W("Suspected wear detection already active");
        return;
    }

    // 重置检测变量
    suspected_wear_detection_active = true;
    suspected_wear_sample_count = 0;
    suspected_wear_current_sum = 0;
    suspected_wear_valid_samples = 0;

    DRV_PPG_LOG_I("Suspected wear detection started, target samples: %d", SUSPECTED_WEAR_SAMPLE_TARGET);

    // 确保PPG处于工作状态以获取原始数据
    if ((s_drv_ppg_lv.online_mask & PPG_OUTTYPE_HR) == 0)
    {
        DRV_PPG_LOG_I("Starting HR for suspected wear detection");
        drv_ppg_start(PPG_OUTTYPE_HR);
    }
}

/************************************************************************
 *@function: static void suspected_wear_detection_stop(void)
 *@brief: 停止疑似佩戴状态检测
 *@return: 无
 *************************************************************************/
static void suspected_wear_detection_stop(void)
{
    if (!suspected_wear_detection_active)
    {
        return;
    }

    suspected_wear_detection_active = false;
    suspected_wear_sample_count = 0;
    suspected_wear_current_sum = 0;
    suspected_wear_valid_samples = 0;

    DRV_PPG_LOG_I("Suspected wear detection stopped");
}

/************************************************************************
 *@function: static void suspected_wear_process_ppg_data(const struct sensor_ppg *ppg_data)
 *@brief: 处理疑似佩戴状态的PPG数据
 *@param: ppg_data - PPG数据指针
 *@return: 无
 *************************************************************************/
static void suspected_wear_process_ppg_data(const struct sensor_ppg *ppg_data)
{
    if (!suspected_wear_detection_active || ppg_data == NULL)
    {
        return;
    }

    suspected_wear_sample_count++;

    // 检查current值是否有效（大于0）
    if (ppg_data->current > 0)
    {
        suspected_wear_current_sum += ppg_data->current;
        suspected_wear_valid_samples++;

        DRV_PPG_LOG_D("Suspected wear sample %d: current=%u, valid_samples=%d",
                      suspected_wear_sample_count, ppg_data->current, suspected_wear_valid_samples);
    }

    // 达到目标采样数量，进行判断
    if (suspected_wear_sample_count >= SUSPECTED_WEAR_SAMPLE_TARGET)
    {
        bool is_wearing = false;

        if (suspected_wear_valid_samples > 0)
        {
            uint32_t avg_current = suspected_wear_current_sum / suspected_wear_valid_samples;

            DRV_PPG_LOG_I("Suspected wear detection completed: avg_current=%u, valid_samples=%d/%d",
                          avg_current, suspected_wear_valid_samples, suspected_wear_sample_count);

            // 判断current是否在20000-40000范围内
            if (avg_current >= SUSPECTED_WEAR_CURRENT_MIN && avg_current <= SUSPECTED_WEAR_CURRENT_MAX)
            {
                is_wearing = true;
                DRV_PPG_LOG_W("Suspected wear: PPG current in range [%d-%d], continue normal PPG detection",
                              SUSPECTED_WEAR_CURRENT_MIN, SUSPECTED_WEAR_CURRENT_MAX);
            }
            else
            {
                is_wearing = false;
                DRV_PPG_LOG_W("Suspected wear: PPG current out of range [%d-%d], force set wear OFF",
                              SUSPECTED_WEAR_CURRENT_MIN, SUSPECTED_WEAR_CURRENT_MAX);
            }
        }
        else
        {
            // 没有有效的current数据，判断为未佩戴
            is_wearing = false;
            DRV_PPG_LOG_W("Suspected wear: No valid current data, force set wear OFF");
        }

        // 根据判断结果设置佩戴状态
        if (is_wearing)
        {
            // 继续正常PPG判断
            DRV_PPG_LOG_W("Suspected wear: current in range, clear hai_force_wear_off flag");
            hai_force_wear_off = false;
            update_wearstate_by_gh_hook();
        }
        else
        {
            // 强制设置为未佩戴
            hai_force_wear_off = true;
            s_drv_ppg_lv.ppg_wear_status = PPG_WEAR_OFF;
            stop_soft_adt();
            move_detect_start();
            DRV_PPG_LOG_I("current data is wearoff");

            // 通过统一的事件处理机制触发状态变化通知
            process_offbody_event(ALGO_HAI_EVENT, QW_INTENSITY_OBJECT_STATIC);
        }

        // 停止检测
        suspected_wear_detection_stop();
    }
}

#if PPG_TEST_CMD_ENABLE
/************************************************************************
 *@function: static void ppg_rawdata_cache_put(const struct sensor_ppg *ppg_data)
 *@brief: 向PPG原始数据缓存中添加数据
 *@param: ppg_data - PPG数据指针
 *@return: 无
 *************************************************************************/
static void ppg_rawdata_cache_put(const struct sensor_ppg *ppg_data)
{
    if (ppg_rawdata_mutex == RT_NULL || ppg_data == RT_NULL)
    {
        return;
    }

    rt_mutex_take(ppg_rawdata_mutex, RT_WAITING_FOREVER);

    // 复制数据到缓存
    memcpy(&ppg_rawdata_cache[ppg_rawdata_write_index], ppg_data, sizeof(struct sensor_ppg));

    // 更新写索引
    ppg_rawdata_write_index = (ppg_rawdata_write_index + 1) % PPG_RAWDATA_CACHE_SIZE;

    // 如果缓存满了，移动读索引
    if (ppg_rawdata_write_index == ppg_rawdata_read_index)
    {
        ppg_rawdata_read_index = (ppg_rawdata_read_index + 1) % PPG_RAWDATA_CACHE_SIZE;
    }

    rt_mutex_release(ppg_rawdata_mutex);
}

/************************************************************************
 *@function: int32_t qw_ppg_force_enable_rawdata(bool enable)
 *@brief: 强制启用PPG原始数据采集，绕过佩戴检测限制
 *@param: enable - true:启用强制模式，false:关闭强制模式
 *@return: 0表示成功，其他值表示失败
 *************************************************************************/
int32_t qw_ppg_force_enable_rawdata(bool enable)
{
    DRV_PPG_LOG_W("PPG force rawdata mode: %s", enable ? "ENABLED" : "DISABLED");

    force_ppg_always_on = enable;

    if (enable)
    {
        // 初始化PPG原始数据缓存
        ppg_rawdata_cache_init();

        // 强制启动HR功能以获取PPG原始数据
        drv_ppg_start(PPG_OUTTYPE_HR);

        // 设置需要上报原始数据标志
        need_report_hr_raw_data = true;

        DRV_PPG_LOG_I("PPG force mode enabled, HR started for rawdata collection");
    }
    else
    {
        // 关闭强制模式
        need_report_hr_raw_data = false;

        // 反初始化缓存
        ppg_rawdata_cache_deinit();

        DRV_PPG_LOG_I("PPG force mode disabled");
    }

    return 0;
}

/************************************************************************
 *@function: uint32_t qw_ppg_get_rawdata_count(void)
 *@brief: 获取当前缓存中PPG原始数据的个数
 *@return: 缓存中数据的个数
 *************************************************************************/
uint32_t qw_ppg_get_rawdata_count(void)
{
    if (ppg_rawdata_mutex == RT_NULL)
    {
        return 0;
    }

    rt_mutex_take(ppg_rawdata_mutex, RT_WAITING_FOREVER);

    uint32_t count;
    if (ppg_rawdata_write_index >= ppg_rawdata_read_index)
    {
        count = ppg_rawdata_write_index - ppg_rawdata_read_index;
    }
    else
    {
        count = PPG_RAWDATA_CACHE_SIZE - ppg_rawdata_read_index + ppg_rawdata_write_index;
    }

    rt_mutex_release(ppg_rawdata_mutex);
    return count;
}

/************************************************************************
 *@function: int32_t qw_ppg_get_rawdata(struct sensor_ppg *ppg_data, uint32_t max_count)
 *@brief: 获取PPG原始数据
 *@param: ppg_data - 输出PPG数据的缓冲区
 *@param: max_count - 最大获取的数据个数
 *@return: 实际获取到的数据个数，负数表示错误
 *************************************************************************/
int32_t qw_ppg_get_rawdata(struct sensor_ppg *ppg_data, uint32_t max_count)
{
    if (ppg_rawdata_mutex == RT_NULL || ppg_data == RT_NULL || max_count == 0)
    {
        DRV_PPG_LOG_E("Invalid parameters for getting PPG rawdata");
        return -1;
    }

    rt_mutex_take(ppg_rawdata_mutex, RT_WAITING_FOREVER);

    uint32_t available_count = 0;
    if (ppg_rawdata_write_index >= ppg_rawdata_read_index)
    {
        available_count = ppg_rawdata_write_index - ppg_rawdata_read_index;
    }
    else
    {
        available_count = PPG_RAWDATA_CACHE_SIZE - ppg_rawdata_read_index + ppg_rawdata_write_index;
    }

    uint32_t copy_count = (available_count < max_count) ? available_count : max_count;
    uint32_t copied = 0;

    for (uint32_t i = 0; i < copy_count; i++)
    {
        memcpy(&ppg_data[i], &ppg_rawdata_cache[ppg_rawdata_read_index], sizeof(struct sensor_ppg));
        ppg_rawdata_read_index = (ppg_rawdata_read_index + 1) % PPG_RAWDATA_CACHE_SIZE;
        copied++;
    }

    rt_mutex_release(ppg_rawdata_mutex);

    DRV_PPG_LOG_D("Retrieved %d PPG rawdata samples", copied);
    return copied;
}

/************************************************************************
 *@function: static void ppg_rawdata_test_cmd(int argc, char **argv)
 *@brief: PPG原始数据测试命令
 *@param: argc - 参数个数, argv - 参数数组
 *@return: 无
 *************************************************************************/
static void ppg_rawdata_test_cmd(int argc, char **argv)
{
    if (argc < 2)
    {
        rt_kprintf("Usage: ppg_test <enable|disable|get|status|start|stop>\n");
        rt_kprintf("  enable  - Enable PPG force rawdata mode\n");
        rt_kprintf("  disable - Disable PPG force rawdata mode\n");
        rt_kprintf("  get     - Get PPG rawdata samples\n");
        rt_kprintf("  status  - Show PPG rawdata status\n");
        rt_kprintf("  start   - Start continuous PPG data output\n");
        rt_kprintf("  stop    - Stop continuous PPG data output\n");
        return;
    }

    if (strcmp(argv[1], "enable") == 0)
    {
        int32_t ret = qw_ppg_force_enable_rawdata(true);
        rt_kprintf("PPG force rawdata mode enabled, ret=%d\n", ret);
    }
    else if (strcmp(argv[1], "disable") == 0)
    {
        int32_t ret = qw_ppg_force_enable_rawdata(false);
        rt_kprintf("PPG force rawdata mode disabled, ret=%d\n", ret);
    }
    else if (strcmp(argv[1], "get") == 0)
    {
        struct sensor_ppg ppg_data[10];
        int32_t count = qw_ppg_get_rawdata(ppg_data, 10);
        rt_kprintf("Retrieved %d PPG samples:\n", count);

        for (int i = 0; i < count; i++)
        {
            rt_kprintf("Sample %d: ch0=%u, ch1=%u, ch2=%u, ch3=%u, current=%u, ts=%llu\n",
                       i, ppg_data[i].ppg[0], ppg_data[i].ppg[1],
                       ppg_data[i].ppg[2], ppg_data[i].ppg[3],
                       ppg_data[i].current, ppg_data[i].timestamp);
        }
    }
    else if (strcmp(argv[1], "status") == 0)
    {
        uint32_t count = qw_ppg_get_rawdata_count();
        rt_kprintf("PPG rawdata status:\n");
        rt_kprintf("  Force mode: %s\n", force_ppg_always_on ? "ENABLED" : "DISABLED");
        rt_kprintf("  Cache count: %u\n", count);
        rt_kprintf("  Write index: %u\n", ppg_rawdata_write_index);
        rt_kprintf("  Read index: %u\n", ppg_rawdata_read_index);
        rt_kprintf("  PPG wear status: %s\n", get_wear_status_str(s_drv_ppg_lv.ppg_wear_status));
        rt_kprintf("  Continuous output: %s\n", ppg_continuous_output ? "RUNNING" : "STOPPED");
    }
    else if (strcmp(argv[1], "start") == 0)
    {
        if (ppg_continuous_output)
        {
            rt_kprintf("PPG continuous output already running\n");
            return;
        }

        // 启用强制模式
        int32_t ret = qw_ppg_force_enable_rawdata(true);
        if (ret != 0)
        {
            rt_kprintf("Failed to enable PPG force mode: %d\n", ret);
            return;
        }

        ppg_continuous_output = true;
        rt_kprintf("PPG continuous output started. Use 'ppg_test stop' to stop.\n");

        // 开始持续输出数据
        uint32_t output_count = 0;
        while (ppg_continuous_output)
        {
            struct sensor_ppg ppg_data[5];
            int32_t count = qw_ppg_get_rawdata(ppg_data, 5);

            if (count > 0)
            {
                for (int i = 0; i < count; i++)
                {
                    output_count++;
                    rt_kprintf("PPG[%u]: ch0=%u, ch1=%u, ch2=%u, ch3=%u, current=%u, ts=%llu\n",
                               output_count,
                               ppg_data[i].ppg[0], ppg_data[i].ppg[1],
                               ppg_data[i].ppg[2], ppg_data[i].ppg[3],
                               ppg_data[i].current, ppg_data[i].timestamp);
                }
            }

            rt_thread_mdelay(200); // 200ms间隔
        }

        rt_kprintf("PPG continuous output stopped. Total output: %u samples\n", output_count);
    }
    else if (strcmp(argv[1], "stop") == 0)
    {
        if (!ppg_continuous_output)
        {
            rt_kprintf("PPG continuous output not running\n");
            return;
        }

        ppg_continuous_output = false;
        rt_kprintf("PPG continuous output stop requested\n");
    }
    else
    {
        rt_kprintf("Unknown command: %s\n", argv[1]);
    }
}

// 注册PPG测试命令
MSH_CMD_EXPORT_ALIAS(ppg_rawdata_test_cmd, ppg_test, PPG rawdata test command);
#endif  // QMC6308_TEST_CMD_ENABLE
