/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   QwItemLap.cpp
@Time    :   2024/12/10 19:29:39
* 
**************************************************************************/

#include "QwItemLap.h"

static const char g_lap_counting[] = "_lap_counting";
TM_KEY(g_lap_counting)

void QwItemLap::setup()
{
    removeAll();
	add(background_);
	add(text_idx_);
	add(text_timer_);
	add(text_main_);
	add(text_sub_);
	add(text_sub2_);

    background_.setXY(0, 0);
    background_.setWidthHeight(*this);

	text_idx_.setWidthHeight(40, 36);
    text_idx_.setAlign(ALIGN_IN_LM, 36);
	text_idx_.setTextFont(&NUMBER_NO_40_FONT);
	text_idx_.setTextAlignment(CENTER);
	text_idx_.setLabelAlpha(LV_OPA_TRANSP);
	text_idx_.setColor(lv_color_white());

	text_timer_.setTextFont(&NUMBER_NO_40_FONT);
	text_timer_.setTextAlignment(LEFT);
	text_timer_.setLabelAlpha(LV_OPA_TRANSP);
	text_timer_.setColor(lv_color_white());

    text_main_.setTextFont(&NUMBER_NO_40_FONT);
	text_main_.setTextAlignment(RIGHT);
	text_main_.setLabelAlpha(LV_OPA_TRANSP);
	text_main_.setColor(lv_color_white());

    text_sub_.setTextFont(&NUMBER_NO_40_FONT);
	text_sub_.setTextAlignment(LEFT);
	text_sub_.setLabelAlpha(LV_OPA_TRANSP);
	text_sub_.setColor(lv_color_white());

    text_sub2_.setTextFont(&NUMBER_NO_40_FONT);
	text_sub2_.setTextAlignment(RIGHT);
	text_sub2_.setLabelAlpha(LV_OPA_TRANSP);
	text_sub2_.setColor(lv_color_white());
};

void QwItemLap::set_index(int idx)
{
    char text[5];
    if ((idx % 2) == 0)
    {
        background_.setColor(lv_color_hex(0x4d4d4d));
        background_.invalidate();
    }
    else
    {
        background_.setColor(lv_color_black());
        background_.invalidate();
    }
    sprintf_array(text, "%02d", idx + 1);
    text_idx_.setTypedDynamicText(text);
    text_idx_.invalidate();
}

void QwItemLap::set_lap_timer(const char* timer)
{
    // char text[20];
    // int hour = timer / 3600;
    // int min = (timer - hour * 3600) / 60;
    // int sec = timer - hour * 3600 - min * 60;
    // sprintf_array(text, "%02d:%02d:%02d", (hour > 99 ? 99 : hour), min, sec);
    text_timer_.setTypedDynamicText(timer);
    text_timer_.resizeToCurrentTextWithAlignment();
    text_timer_.setAlign(ALIGN_IN_LT, 102, 16);
}
// 第一个数据
void QwItemLap::set_lap_main_text(const char* text)
{
    text_main_.setTypedDynamicText(text);
    text_main_.resizeToCurrentTextWithAlignment();
    text_main_.setAlign(ALIGN_IN_RT, -36, 16);
}
// 第二个数据
void QwItemLap::set_lap_sub_text(const char* text)
{
    text_sub_.setTypedDynamicText(text);
    text_sub_.resizeToCurrentTextWithAlignment();
    text_sub_.setAlign(ALIGN_IN_LB, 102, -16);
}
// 第三个数据
void QwItemLap::set_lap_sub2_text(const char* text)
{
    // rt_kprintf("@@@text %s\n", text);
    text_sub2_.setTypedDynamicText(text);
    text_sub2_.resizeToCurrentTextWithAlignment();
    text_sub2_.setAlign(ALIGN_IN_RB, -36, -16);
}

void QwItemLap::set_is_icon(const void* img)
{
    removeAll();
    add(background_);
	add(icon_);
    add(text_idx_);

    background_.setXY(0, 0);
    background_.setWidthHeight(*this);
    background_.setColor(lv_color_black());
    background_.invalidate();

    Bitmap bmp(img);
    icon_.setBitmap(bmp);
    icon_.setAlign(ALIGN_IN_TM, 0, 20);

    text_idx_.setWidthHeight(466,52);
	text_idx_.setTextFont(&NUMBER_NO_40_FONT);
	text_idx_.setTextAlignment(CENTER);
	text_idx_.setLabelAlpha(LV_OPA_TRANSP);
	text_idx_.setColor(lv_color_white());
    text_idx_.setAlign(ALIGN_IN_TM, 0, 76);
    text_idx_.setTypedDynamicText(_TM(g_lap_counting));
}

void QwItemLap::create_blank_box()
{
    add(blank_box_);
    blank_box_.setXY(0, 0);
    blank_box_.setWidthHeight(*this);
    blank_box_.setColor(lv_color_black());
}