#include "lvgl.h" 

#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP ALIGN(4)
#endif

#ifndef eZIP_RGBARGB888A
#define eZIP_RGBARGB888A
#endif


LV_ATTRIBUTE_MEM_ALIGN_EZIP const  uint8_t green_map[] SECTION(".ROM3_IMG_EZIP.green") = { 
    0x00,0x00,0x03,0x08,0x46,0x08,0x20,0x00,0x00,0x2c,0x00,0x2c,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0x02,0x00,0x00,0x00,0x74,0x00,0x00,0x02,0x10,0x1a,0x33,0xd6,0x69,
    0x9c,0x09,0xe2,0xf8,0xcc,0xda,0x7c,0x42,0xc8,0x0f,0x10,0xd1,0x7d,0x24,0xe5,0x09,0xc4,0x95,0xd4,0x14,0x5c,0x43,0x43,0xc7,0x89,0x36,0x87,0x04,0x05,0x20,0x78,0x87,
    0x3b,0x5d,0x44,0x28,0x40,0xe2,0xd2,0xa2,0xa3,0xa3,0xa1,0x39,0x0a,0xea,0x94,0x87,0x72,0xa2,0xc4,0x5c,0x87,0xf2,0x00,0x51,0x14,0x41,0xec,0xb9,0x19,0x63,0x4b,0x4e,
    0x88,0x73,0x8e,0x13,0x5f,0x76,0x47,0x48,0xbb,0xac,0xb3,0x9e,0x9f,0xc7,0xff,0x99,0x5d,0x2f,0x00,0x00,0x44,0x18,0xc3,0xca,0x4f,0x30,0xe7,0x74,0xac,0x55,0x42,0x5c,
    0x01,0x82,0x25,0x1e,0x2a,0x72,0x5b,0x20,0x04,0x47,0xae,0x23,0x41,0x0b,0x10,0x9a,0xdc,0x75,0xb9,0x6d,0x20,0x51,0xbd,0x35,0xeb,0xdd,0xd6,0xe6,0xa1,0x9d,0xd5,0x67,
    0x26,0xe0,0x83,0x47,0x6b,0x9d,0x00,0xb7,0x08,0x60,0x03,0x47,0xbc,0x07,0xcf,0xe1,0xe7,0x81,0x2b,0x04,0xba,0x38,0x5e,0xf0,0xae,0x73,0x05,0xde,0x77,0xad,0x4d,0x50,
    0x78,0xc8,0x5e,0x97,0x61,0x12,0x86,0x70,0x07,0x3e,0x55,0xaa,0x45,0xef,0x72,0xa2,0xc0,0x87,0x0f,0xff,0xbd,0xf3,0x94,0xff,0x95,0xbb,0x6b,0x90,0x8f,0xdd,0x58,0xbe,
    0x3a,0xaa,0x94,0x9e,0xef,0xc7,0x06,0xde,0x7b,0x98,0x29,0x83,0xa2,0x73,0xcc,0x28,0x9f,0x51,0xa4,0x02,0x3e,0x6e,0x9f,0x94,0x5e,0x6a,0xc3,0x7e,0xa7,0x86,0x4a,0xe0,
    0xb7,0xfd,0x19,0x15,0x7d,0xcb,0x1b,0x36,0x8c,0x1c,0x8a,0x2f,0xf1,0x99,0x29,0xc2,0x9c,0x58,0xa7,0x9c,0x58,0x3b,0x30,0x05,0xe3,0x84,0x3c,0xe3,0x84,0xdc,0x4d,0x1d,
    0x61,0x79,0xca,0x69,0xc1,0xbe,0xca,0x03,0x77,0x92,0x22,0x8d,0x83,0x34,0x2b,0xaf,0x06,0x34,0x30,0xf2,0xf1,0x53,0xbf,0xa6,0xb1,0xbf,0x1a,0x74,0x95,0xff,0xeb,0x5f,
    0x68,0x36,0x6d,0x22,0xda,0xbe,0x5a,0x8c,0x57,0x8f,0x1e,0x49,0x48,0xe9,0xd2,0x05,0x36,0x4a,0xc4,0xb0,0x9c,0xbe,0xd5,0x70,0xb0,0x28,0xe4,0x57,0x67,0xc7,0xb1,0xb5,
    0x90,0xad,0x2f,0xc2,0xb2,0x82,0xe9,0x6a,0x31,0x36,0x15,0xed,0x0d,0x26,0xb6,0xdc,0xe6,0x23,0xe6,0xe5,0x80,0x31,0x02,0x96,0x8d,0x0c,0x68,0x6e,0x11,0x23,0xca,0x16,
    0x71,0xae,0x63,0xb7,0x74,0x4a,0xb6,0xa4,0x8a,0xd1,0x9e,0xed,0x3a,0x76,0xb0,0x9f,0xd5,0x1c,0x36,0xaa,0x18,0xc2,0xaa,0x82,0xcd,0xb7,0x21,0x26,0xac,0x2a,0xfc,0x52,
    0x30,0x84,0x18,0x96,0x24,0xe9,0x8a,0xc6,0x00,0x33,0xab,0x44,0xb8,0x60,0x50,0x84,0x0b,0x2a,0xfa,0x60,0x34,0x43,0xc3,0xe0,0x28,0x83,0xe4,0x10,0x2e,0x7a,0xf2,0x29,
    0x6e,0x88,0x09,0xab,0x0a,0xcf,0x0d,0x0c,0x21,0x86,0xa6,0x48,0xc2,0x35,0x48,0x11,0xae,0x44,0xb8,0x61,0x50,0x84,0x1b,0xac,0x61,0xaa,0x9b,0xa3,0x61,0xaa,0x2b,0x39,
    0xeb,0x0a,0xce,0x04,0xb4,0x2f,0xc1,0x40,0xc2,0xaa,0xe4,0x60,0x4e,0xce,0xba,0x0c,0xd8,0xfc,0x5c,0x09,0xab,0x0a,0xcf,0x01,0x2e,0xf4,0x07,0x7e,0x65,0xc4,0xd8,0x59,
    0xc4,0x4f,0x6d,0xbf,0x3a,0x10,0xee,0xaa,0xff,0x77,0xdf,0x4b,0xf7,0x0f,0x00,0x00,0x45,0x08,0x6d,0xdf,0xb5,0x36,0x01,0xf1,0x3b,0xe8,0x68,0x44,0x1f,0xab,0x45,0xef,
    0x52,0xba,0x18,0x1f,0xdf,0x7f,0xb4,0x7f,0x70,0xb3,0xa6,0x19,0xee,0x4d,0x75,0xa1,0xfb,0x21,0xfa,0x47,0xc5,0xaf,0x58,0xbe,0x3a,0x22,0x7e,0x1e,0x6d,0x02,0xcb,0x7f,
    0xc2,0x14,0x1f,0xeb,0x01,0xae,0x94,0x9e,0xef,0xc1,0xc7,0x6d,0x6d,0x62,0xcb,0x2c,0x01,0x53,0x12,0xb0,0xd8,0x49,0xe9,0xa5,0xc6,0x42,0xf9,0x32,0x75,0x58,0x66,0x08,
    0x58,0xde,0x0c,0x27,0xd8,0xc1,0xa3,0x75,0x4a,0x80,0x3b,0xd3,0x61,0xa5,0xb3,0xe3,0x05,0x6f,0x77,0xd0,0x35,0x95,0x34,0x29,0x98,0x30,0x8d,0x48,0xb3,0xcf,0x24,0xd8,
    0xa1,0x11,0x8e,0x6c,0xef,0x61,0xa6,0x0c,0x8a,0xce,0x31,0xc5,0x6f,0xc7,0x4d,0x30,0xd1,0xec,0x20,0x19,0xa4,0x8a,0x70,0x5c,0xd3,0xb6,0xaf,0x16,0xa5,0xbc,0xe4,0x59,
    0xba,0xc4,0xc7,0xdf,0x60,0x53,0x45,0xb8,0xa7,0x4e,0xcb,0xe2,0xa2,0xf0,0x90,0x63,0xb1,0x3c,0xa1,0xd7,0x7f,0x07,0x3e,0x55,0xa2,0x45,0x21,0xdd,0x94,0x0c,0xc6,0x09,
    0xb9,0xce,0x09,0xb9,0xc5,0xaf,0x71,0x63,0x54,0xa9,0xc8,0xab,0xe7,0x09,0x57,0x9c,0x58,0x17,0xac,0xd5,0xeb,0xd1,0x9f,0x71,0x0c,0x2b,0x3f,0xc1,0x9c,0xd3,0xb1,0x56,
    0x09,0x71,0x85,0x49,0x96,0x78,0xa8,0xc8,0x6d,0x81,0x89,0x9c,0xe0,0xe6,0x04,0x2d,0xf6,0xd0,0xe4,0xae,0xcb,0x6d,0x03,0x89,0xea,0xad,0x59,0xef,0xb6,0x36,0x0f,0xed,
    0xac,0x3e,0xff,0x00,0x4a,0x98,0x11,0x6f,
};


#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER ALIGN(4)
#endif
LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER const eZIP_RGBARGB888A  lv_img_dsc_t green SECTION(".ROM3_IMG_EZIP_HEADER.green") = { 
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .header.always_zero = 0,
  .header.w = 44,
  .header.h = 44,
  .data_size  = 776,
  .data = green_map
};
