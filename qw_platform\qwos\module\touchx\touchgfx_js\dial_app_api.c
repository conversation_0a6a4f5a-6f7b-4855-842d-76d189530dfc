#include "dial_app_api.h"
#include "dial_common_api.h"
#include "qwos.h"
#include "qw_fs.h"
#include "igs_dev_config.h"

uint32_t get_dial_cfg_theme_color_num_to_app(uint32_t goodsId, const char *dialType)
{
    uint32_t colorNum = 0;
	char cfgPath[50] = {0};
#ifdef SIMULATOR
	sprintf_array(cfgPath, "./Dial/%u%s/config.cfg", goodsId, dialType);
#else
	sprintf_array(cfgPath, "%sDial/%u%s/config.cfg", BASE_PATH, goodsId, dialType);
#endif
    get_dial_value_from_cfg(cfgPath, "color_num", &colorNum, NULL);
    return colorNum;
}

void set_dial_cfg_color_inuse_index_to_app(uint32_t goodsId, const char *dialType, uint32_t index)
{
	char cfgPath[50] = {0};
#ifdef SIMULATOR
	sprintf_array(cfgPath, "./Dial/%u%s/config.cfg", goodsId, dialType);
#else
	sprintf_array(cfgPath, "%sDial/%u%s/config.cfg", BASE_PATH, goodsId, dialType);
#endif
    set_dial_value_in_cfg(cfgPath, "color_inuse_index", index, OPERATION_MODIFY);
}

uint32_t get_dial_cfg_color_inuse_index_to_app(uint32_t goodsId, const char *dialType)
{
    uint32_t colorInuseIndex = 0;
	char cfgPath[50] = {0};
#ifdef SIMULATOR
	sprintf_array(cfgPath, "./Dial/%u%s/config.cfg", goodsId, dialType);
#else
	sprintf_array(cfgPath, "%sDial/%u%s/config.cfg", BASE_PATH, goodsId, dialType);
#endif
    get_dial_value_from_cfg(cfgPath, "color_inuse_index", &colorInuseIndex, NULL);
    return colorInuseIndex;
}

void get_edit_color_type_to_app(uint32_t *colorType, uint32_t *goodsid, const char *dialType)
{
    if (NULL == goodsid || NULL == colorType)
        return;
    uint32_t editColorTypeNum = get_dial_cfg_theme_color_num(*goodsid);
    uint32_t goodsId = 0;

    if (goodsid == NULL)
        goodsId = get_current_dial_goodsid();
    else
        goodsId = *goodsid;

    for(int i = 0; i < editColorTypeNum; i++)
    {
        char cfgPath[50] = {0};
        char editColorTypeStr[20] = {0};
#ifdef SIMULATOR
        sprintf_array(cfgPath, "./Dial/%u%s/config.cfg", goodsId, dialType);
#else
        sprintf_array(cfgPath, "%s/Dial/%u%s/config.cfg", BASE_PATH, goodsId, dialType);
#endif
        sprintf_array(editColorTypeStr, "color%d", i);
        get_dial_value_from_cfg(cfgPath, editColorTypeStr, &colorType[i], NULL);
    }
}

uint32_t get_dial_edit_type_num_to_app(uint32_t *goodsid, const char *dialType)
{
    if(goodsid == NULL || dialType == NULL) return 0;
        
    uint32_t editDataTypeNum = 0;
    char cfgPath[50] = {0};
    uint32_t goodsId = 0;
    goodsId = *goodsid;
#ifdef SIMULATOR
    sprintf_array(cfgPath, "./Dial/%u%s/config.cfg", goodsId, dialType);
#else
    sprintf_array(cfgPath, "%sDial/%u%s/config.cfg", BASE_PATH, goodsId, dialType);
#endif
    get_dial_value_from_cfg(cfgPath, "edit_data_type_num", &editDataTypeNum, NULL);

    return editDataTypeNum;
}

void get_edit_data_type_to_app(uint32_t *dataType, uint32_t *goodsid, const char *dialType)
{
    uint32_t editDataTypeNum = get_dial_edit_type_num(goodsid);
    uint32_t goodsId = 0;

    if (goodsid == NULL)
        goodsId = get_current_dial_goodsid();
    else
        goodsId = *goodsid;

    for(int i = 0; i < editDataTypeNum; i++)
    {
        char cfgPath[50] = {0};
        char editDataTypeStr[20] = {0};
#ifdef SIMULATOR
        sprintf_array(cfgPath, "./Dial/%u%s/config.cfg", goodsId, dialType);
#else
        sprintf_array(cfgPath, "%sDial/%u%s/config.cfg", BASE_PATH, goodsId, dialType);
#endif
        sprintf_array(editDataTypeStr, "edit_data_type%d", i);
        get_dial_value_from_cfg(cfgPath, editDataTypeStr, &dataType[i], NULL);
        // rt_kprintf("get_edit_data_type:%u\n", dataType[i]);
    }
}

uint32_t get_dial_data_num_to_app(uint32_t *goodsid, const char *dialType)
{
    uint32_t dataNum = 0;
    char cfgPath[50] = {0};
    uint32_t goodsId = 0;
    if (goodsid == NULL)
        goodsId = get_current_dial_goodsid();
    else
        goodsId = *goodsid;

#ifdef SIMULATOR
    sprintf_array(cfgPath, "./Dial/%u%s/config.cfg", goodsId, dialType);
#else
    sprintf_array(cfgPath, "%sDial/%u%s/config.cfg", BASE_PATH, goodsId, dialType);
#endif

    get_dial_value_from_cfg(cfgPath, "edit_data_num", &dataNum, NULL);
    return dataNum;
}

void get_using_data_type_to_app(uint32_t *dataType, uint32_t *goodsid, const char *dialType)
{
    uint32_t editDataTypeNum = get_dial_edit_type_num_to_app(goodsid, dialType);
    uint32_t goodsId = 0;

    if (goodsid == NULL)
        goodsId = get_current_dial_goodsid();
    else
        goodsId = *goodsid;

    for(int i = 0; i < editDataTypeNum; i++)
    {
        char cfgPath[50] = {0};
        char editDataTypeStr[20] = {0};
#ifdef SIMULATOR
        sprintf_array(cfgPath, "./Dial/%u%s/config.cfg", goodsId, dialType);
#else
        sprintf_array(cfgPath, "%s/Dial/%u%s/config.cfg", BASE_PATH, goodsId, dialType);
#endif
        sprintf_array(editDataTypeStr, "edit_data%d", i);
        get_dial_value_from_cfg(cfgPath, editDataTypeStr, &dataType[i], NULL);
        // rt_kprintf("get_edit_data_type:%u\n", dataType[i]);
    }
}

void set_dial_data_type_to_app(uint32_t index, uint32_t dataType, uint32_t *goodsid, const char *dialType)
{
    char cfgPath[50] = {0};
    char dataStr[20] = {0};
    uint32_t goodsId = 0;
    if (goodsid == NULL)
        goodsId = get_current_dial_goodsid();
    else
        goodsId = *goodsid;

#ifdef SIMULATOR
	sprintf_array(cfgPath, "./Dial/%u%s/config.cfg", goodsId, dialType);
#else
	sprintf_array(cfgPath, "%sDial/%u%s/config.cfg", BASE_PATH, goodsId, dialType);
#endif
    sprintf_array(dataStr, "edit_data%u", index);
    rt_kprintf("set_dial_data_typeindex:%u, dataType%u\n",index, dataType);
    set_dial_value_in_cfg(cfgPath, dataStr, dataType, OPERATION_MODIFY);
}