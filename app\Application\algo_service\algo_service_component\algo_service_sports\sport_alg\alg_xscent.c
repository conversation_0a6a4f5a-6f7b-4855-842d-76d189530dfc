/************************************************************************
*
* Copyright(c) 2025, igpsport Software Co., Ltd.
* All Rights Reserved.
* @File    :   alg_xscent.c
*
**************************************************************************/
#include <stddef.h>
#include <math.h>
#include "alg_xscent.h"

int xscent_calculator_exec(XscentCalculator *self, const XscentCalcInput *input)
{
    if (self == NULL || input == NULL)
    {
        return -1;
    }

    if (input->alt < -999.0f)
    {
        self->cnt = 0;
        return 0;
    }

    if (self->cnt == 0)
    {
        self->last.timestamp = input->timestamp;
        self->last.dist = input->dist;
        self->last.alt = input->alt;
        self->cnt += 1;
        return 0;
    }

    if (input->timestamp < self->last.timestamp || input->timestamp - self->last.timestamp > 30)
    {
        self->cnt = 0;
        return 0;
    }

    if (input->dist < self->last.dist + 4.0f)
    {
        return 0;
    }

    self->last.timestamp = input->timestamp;
    self->last.dist = input->dist;
    self->cnt += 1;

    float dalt = 0.0f;

    if (fabsf(input->alt - self->last.alt) >= 0.5f)
    {
        dalt = input->alt - self->last.alt;
        self->last.alt = input->alt;
    }

    if (dalt > 0.0f)
    {
        self->ascent += dalt;
    }
    else
    {
        self->descent += dalt;
    }

    return 0;
}

void xscent_calculator_data_get(XscentCalculator *self, XscentCalcData *data)
{
    if (self != NULL && data != NULL)
    {
        data->ascent = self->ascent;
        data->descent = self->descent;
    }
}

void xscent_calculator_reset(XscentCalculator *self)
{
    if (self != NULL)
    {
        self->last.timestamp = 0;
        self->last.dist = 0.0f;
        self->last.alt = 0.0f;
        self->ascent = 0.0f;
        self->descent = 0.0f;
        self->cnt = 0;
    }
}

void xscent_calculator_dump(XscentCalculator *self, XscentCalculatorDump *dump)
{
    if (self != NULL && dump != NULL)
    {
        dump->ascent = self->ascent;
        dump->descent = self->descent;
    }
}

void xscent_calculator_restore(XscentCalculator *self, const XscentCalculatorDump *dump)
{
    if (self != NULL && dump != NULL)
    {
        self->ascent = dump->ascent;
        self->descent = dump->descent;
        self->cnt = 0;
    }
}
