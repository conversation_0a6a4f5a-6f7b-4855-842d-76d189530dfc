#ifndef _DIAL_APP_API_H_
#define _DIAL_APP_API_H_
#include "stdint.h"
#ifdef __cplusplus
extern "C" {
#endif

uint32_t get_dial_cfg_theme_color_num_to_app(uint32_t goodsId, const char *dialType);
void set_dial_cfg_color_inuse_index_to_app(uint32_t goodsId, const char *dialType, uint32_t index);
uint32_t get_dial_cfg_color_inuse_index_to_app(uint32_t goodsId, const char *dialType);
void get_edit_color_type_to_app(uint32_t *colorType, uint32_t *goodsid, const char *dialType);
uint32_t get_dial_edit_type_num_to_app(uint32_t *goodsid, const char *dialType);
void get_edit_data_type_to_app(uint32_t *dataType, uint32_t *goodsid, const char *dialType);
uint32_t get_dial_data_num_to_app(uint32_t *goodsid, const char *dialType);
void get_using_data_type_to_app(uint32_t *dataType, uint32_t *goodsid, const char *dialType);
void set_dial_data_type_to_app(uint32_t index, uint32_t dataType, uint32_t *goodsid, const char *dialType);

#ifdef __cplusplus
}
#endif

#endif

