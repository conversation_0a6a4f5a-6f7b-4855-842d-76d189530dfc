﻿  js_data_servers.c
  touchgfx_js.c
  touchgfx_js_api.c
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\js_data_servers.c(259,30): warning C4013: “battery_get_level”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\js_data_servers.c(303,30): warning C4013: “battery_get_status”未定义；假设外部返回 int
E:\Snowa\qw_platform\qwos\module\touchx\touchgfx_js\js_data_servers.c(339,25): warning C4013: “get_weather_data_valid”未定义；假设外部返回 int
  JsApp.cpp
  JsCircle.cpp
  JsImg.cpp
  PM_Base.cpp
  MvcApp.cpp
  AodScreen.cpp
  Launcher.cpp
  LauncherView.cpp
  PersonalizationView.cpp
  AlarmMenuView.cpp
  AlipayMenuView.cpp
  StopWatchRecordView.cpp
  TimerAppReadyView.cpp
  DialLoadingView.cpp
  DialDataSelect.cpp
  DialDataSelectModel.cpp
  DialDataSelectView.cpp
  DialDataSelectViewModel.cpp
  EditDialDataComponentView.cpp
  EditDialThemeView.cpp
  SelectDialView.cpp
  gui_event_service.cpp
  project.vcxproj -> E:\Snowa\app\project\WR02_Win32\project\simulator\Debug\WR02_Win.exe
