/************************************************************************
*
* Copyright(c) 2025, igpsport Software Co., Ltd.
* All Rights Reserved.
* @File    :   alg_xscent.h
*
**************************************************************************/
#ifndef ALG_XSCENT_H
#define ALG_XSCENT_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef struct _XscentCalcInput
{
    uint32_t timestamp;
    float dist;
    float alt;
} XscentCalcInput;

typedef struct _XscentCalcData
{
    float ascent;
    float descent;
} XscentCalcData;

typedef struct _XscentCalculatorDump
{
    float ascent;
    float descent;
} XscentCalculatorDump;

typedef struct _XscentCalculator
{
    XscentCalcInput last;
    float ascent;
    float descent;
    uint32_t cnt;
} XscentCalculator;

int xscent_calculator_exec(XscentCalculator *self, const XscentCalcInput *input);

void xscent_calculator_data_get(XscentCalculator *self, XscentCalcData *data);

void xscent_calculator_reset(XscentCalculator *self);

void xscent_calculator_dump(XscentCalculator *self, XscentCalculatorDump *dump);

void xscent_calculator_restore(XscentCalculator *self, const XscentCalculatorDump *dump);

#ifdef __cplusplus
}
#endif

#endif