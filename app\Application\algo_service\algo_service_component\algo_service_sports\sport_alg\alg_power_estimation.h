/************************************************************************​
*Copyright(c) 2025, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#ifndef ALG_POWER_ESTIMATION_H
#define ALG_POWER_ESTIMATION_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

typedef struct _PowerEstimatorInit
{
    float body_weight;              //体重（kg）
    float bike_weight;              //车重（kg）
} PowerEstimatorInit;

typedef struct _PowerEstimatorInput
{
    uint32_t timestamp;             //时间戳（s）
    float grade;                    //坡度，实际值，比如3%的坡，这里就是0.03
    float spd;                      //速度（m/s）
} PowerEstimatorInput;

typedef struct _PowerEstimator
{
    float body_weight;              //体重（kg）
    float bike_weight;              //车重（kg）
    float spd;                      //速度（m/s）
    uint32_t cnt;
    uint32_t timestamp;
} PowerEstimator;

void power_estimator_init(PowerEstimator *self, const PowerEstimatorInit *init);

float power_estimator_exec(PowerEstimator *self, const PowerEstimatorInput *input);

#ifdef __cplusplus
}
#endif

#endif