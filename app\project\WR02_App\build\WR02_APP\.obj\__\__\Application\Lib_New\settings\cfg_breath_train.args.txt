--target=arm-arm-none-eabi -c -xc -std=c11 -I. -I../../../sifli/rtos/rtthread/include -Iapplications -Iboard -Iboard/ports -Ilinker_scripts -I../../../sifli/customer/boards/common -I../../../sifli/customer/boards/include -I../../../sifli/customer/boards/include/config/sf32lb55x -I../../../sifli/customer/boards/watch_wr02 -I../../../sifli/customer/peripherals/pmic_controller -I../../../sifli/rtos/rtthread/bsp/sifli/drivers -I../../../sifli/rtos/rtthread/bsp/sifli/drivers/config/sf32lb55x -I../../../sifli/drivers/cmsis/sf32lb55x -I../../../sifli/drivers/cmsis/Include -I../../../sifli/external/CMSIS/Include -I../../../sifli/drivers/Include -I../../../sifli/external/CMSIS/CMSIS/Include -I../../../sifli/external/CMSIS/RTOS2/Include -I../../../sifli/middleware/include -I../../../sifli/middleware/ipc_queue/common -I../../../sifli/middleware/ipc_queue/porting/os -I../../../sifli/middleware/ipc_queue/porting/os/rtthread -I../../../sifli/middleware/ipc_queue/porting/sf32lb55x -I../../../sifli/middleware/ipc_queue/porting/sf32lb55x/hcpu -I../../../sifli/middleware/sifli_lib/lib -I../../../sifli/rtos/os_adaptor/src -I../../../sifli/rtos/os_adaptor/inc -I../../../sifli/rtos/rtthread/libcpu/arm/Cortex-M33 -I../../../sifli/rtos/rtthread/libcpu/arm/common -I../../../sifli/rtos/rtthread/components/dfs/include -I../../../sifli/rtos/rtthread/components/dfs/filesystems/devfs -I../../../sifli/rtos/rtthread/components/dfs/filesystems/elmfat -I../../../sifli/rtos/rtthread/components/dfs/filesystems/dhara/dhara -I../../../sifli/rtos/rtthread/components/drivers/include -I../../../sifli/rtos/rtthread/components/drivers/spi -I../../../sifli/rtos/rtthread/components/finsh -I../../../sifli/rtos/rtthread/components/libc/compilers/armlibc -I../../../sifli/rtos/rtthread/components/libc/compilers/common -I../../../sifli/rtos/rtthread/components/libc/pthreads -I../../../sifli/rtos/rtthread/components/libc/time -I../../../sifli/rtos/rtthread/components/utilities/llt_mem -I../../../sifli/rtos/rtthread/components/utilities/ulog -I../../../sifli/rtos/rtthread/components/utilities/ulog/backend -IRTE/_WR02_APP -I../../Application/App -I../../Application/Mem_Manager -I../../Application/App/common -I../../Application/Lib_New/data_utility/inc -I../../Application/Lib_New/utility -I../../Application/Lib_New/arm_math -I../../../sifli/customer/peripherals/firmware_ota -I../../Application/Lib_New/md5 -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/timer -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/util -I../../../sifli/external/nRF5_SDK_16.0.0/config -I../../../sifli/external/nRF5_SDK_16.0.0/modules -I../../../sifli/external/nRF5_SDK_16.0.0/components/softdevice/s332/headers -I../../../sifli/external/nRF5_SDK_16.0.0/components/softdevice/s332/headers/nrf52 -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/log -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/experimental_section_vars -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/log/src -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/atomic_fifo -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/atomic -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/atomic_flags -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/balloc -I../../../sifli/external/nRF5_SDK_16.0.0/stm32_hw_adapter -I../../../sifli/external/FreeRTOS/include/freertos -I../../Application/Drivers/driver_api -I../../Application/App/filesystem_module -I../../Application/Log -I../../Application/App/basic_app_module -I../../Application/App/touchx_gui/touchx_embed -I../../Application/App/touchx_gui -I../../Application/App/UI/FreeTypeFont -I../../../sifli/external/nRF5_SDK_16.0.0/external/fprintf -I../../Application/App/sys_time -I../../../sifli/middleware/bluetooth/ble_service -I../../../sifli/external/FlashDB/inc -I../../../sifli/external/FlashDB/port/fal/inc -I../../../sifli/external/nRF5_SDK_16.0.0/external/utf_converter -I../../../sifli/external/nRF5_SDK_16.0.0/components -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_bpwr -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_bsc -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_di2 -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_fe -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_hrm -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_lev -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_light -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_radar -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_shft -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/bp_ctf -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_bsc/pages -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_bsc/utils -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_di2/pages -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_hrm/pages -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_hrm/utils -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_lev/pages -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_bpwr/pages -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_bpwr/utils -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_shft/pages -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_radar/pages -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_radar/utils -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_common/ant_request_controller -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_common/pages -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_channel_config -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_key_manager -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_state_indicator -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_key_manager/config -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/ble_advertising -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/ble_db_discovery -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/ble_link_ctx_manager -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/common -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/nrf_ble_gatt -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/nrf_ble_gq -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/nrf_ble_qwr -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/nrf_ble_scan -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/peer_manager -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/ble_services/ble_ancs_c -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/ble_services/ble_bas -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/ble_services/ble_bas_c -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/ble_services/ble_cscs_c -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/ble_services/ble_cts_c -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/ble_services/ble_dis -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/ble_services/ble_dis_c -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/ble_services/ble_hrs -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/ble_services/ble_hrs_c -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/ble_services/ble_nus -I../../../sifli/external/nRF5_SDK_16.0.0/components/ble/ble_services/ble_nus_c -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/crc16 -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/fds -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/fstorage -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/hardfault -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/memobj -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/mutex -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/queue -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/sensorsim -I../../../sifli/external/nRF5_SDK_16.0.0/components/libraries/strerror -I../../../sifli/external/nRF5_SDK_16.0.0/components/softdevice/common -I../../../sifli/external/nRF5_SDK_16.0.0/components/serialization/common -I../../../sifli/external/nRF5_SDK_16.0.0/components/serialization/common/transport -I../../../sifli/external/nRF5_SDK_16.0.0/components/serialization/common/transport/ser_phy -I../../../sifli/external/nRF5_SDK_16.0.0/components/serialization/common/transport/ser_phy/config -I../../../sifli/external/nRF5_SDK_16.0.0/components/serialization/common/struct_ser/ant -I../../../sifli/external/nRF5_SDK_16.0.0/components/serialization/common/struct_ser/ble -I../../../sifli/external/nRF5_SDK_16.0.0/components/serialization/application/hal -I../../../sifli/external/nRF5_SDK_16.0.0/components/serialization/application/codecs/ant/middleware -I../../../sifli/external/nRF5_SDK_16.0.0/components/serialization/application/codecs/ant/serializers -I../../../sifli/external/nRF5_SDK_16.0.0/components/serialization/application/codecs/ble/serializers -I../../../sifli/external/nRF5_SDK_16.0.0/components/serialization/application/transport -I../../Application/App/radio/sensor/sensor_ant/ant_common -I../../Application/App/radio/ble -I../../../sifli/rtos/rtthread/components/drivers/include/drivers -I../../Application/App/radio/nanopb -I../../Application/App/radio/nanopb/ble -I../../Application/App/radio/nanopb/common -I../../Application/App/radio/nanopb/config -I../../Application/App/radio/nanopb/dev_status -I../../Application/App/radio/nanopb/factory -I../../Application/App/radio/nanopb/firmware -I../../Application/App/radio/nanopb/log -I../../Application/App/radio/nanopb/map_new -I../../Application/App/radio/nanopb/route -I../../Application/App/radio/nanopb/sensor -I../../Application/App/radio/nanopb/team_info -I../../Application/App/radio/nanopb/theme -I../../Application/App/radio/nanopb/training -I../../Application/App/radio/nanopb/wifi -I../../Application/App/radio/nanopb/back_services -I../../Application/App/radio/nanopb/cycling_data -I../../Application/App/radio/nanopb/dev_ver_info -I../../Application/App/radio/nanopb/file_operation -I../../Application/App/radio/nanopb/intelligent_notification_service -I../../Application/App/radio/nanopb/language_pack -I../../Application/App/radio/nanopb/peripheral -I../../Application/App/radio/nanopb/real_time_trace -I../../Application/App/radio/nanopb/route_book -I../../Application/App/radio/nanopb/user_config -I../../Application/App/radio/sensor/sensor_ant/ant_search -I../../Application/App/radio/sensor/sensor_ant/bpwr -I../../Application/App/radio/sensor/sensor_ant/bsc -I../../Application/App/radio/sensor/sensor_ant/cad -I../../Application/App/radio/sensor/sensor_ant/di2 -I../../Application/App/radio/sensor/sensor_ant/fe -I../../Application/App/radio/sensor/sensor_ant/hrm -I../../Application/App/radio/sensor/sensor_ant/lev -I../../Application/App/radio/sensor/sensor_ant/light -I../../Application/App/radio/sensor/sensor_ant/radar -I../../Application/App/radio/sensor/sensor_ant/shft -I../../Application/App/radio/sensor/sensor_ant/spd -I../../Application/App/radio/sensor/sensor_ble -I../../Application/App/radio/application -I../../Application/App/radio/ble_communicate -I../../Application/App/radio/ble_communicate_single -I../../Application/App/UI/GUICtrl/MsgBox -I../../Application/App/UI -I../../Application/App/UI/GUICtrl -I../../Application/App/UI/Image -I../../Application/App/UI/ViewData -I../../Application/App/UI/Utility -I../../Application/App/UI/Page -I../../Application/App/UI/GUICtrl/AppList -I../../Application/App/UI/GUICtrl/Console -I../../Application/App/UI/GUICtrl/Label -I../../Application/App/PageManager -I../../Application/Lib_New/gui/lvgl -I../../Application/Lib_New/stop_watch -I../../Application/Lib_New/timer_clock -I../../Application/Lib_New/fit_codec/fit_lib/inc_fit_codec -I../../Application/Lib_New/fit_codec -I../../../sifli/external/rlottie/src/vector -I../../../sifli/external/rlottie/src/vector/freetype -I../../../sifli/external/rlottie -I../../Application/Lib_New/sports_data -I../../Application/Lib_New/focus -I../../../sifli/customer/peripherals/mdc1a00/include -I../../Application/App/key_module -I../WR02_Boot/src/erpc_client_server -I../../../sifli/external/erpc/infra -I../../../sifli/external/erpc/port -I../../../sifli/external/erpc/config -I../../../sifli/external/erpc/setup -I../../../sifli/external/erpc/transports -I../../../sifli/drivers/Multicore_Drivers/DataServer -I../../../sifli/drivers/Multicore_Drivers/System -I../../../sifli/rtos/rtthread/components/drivers/rtc -I../../Application/Task_Thread -I../../Application/App/wifi_module -I../../../sifli/external/RTL8189FTV_SDK/include -I../../../sifli/external/erpc -I../../../qw_platform/qwos/driver/sensor/gps -I../../../qw_platform/qwos/driver/sensor/gps/airoha_epo -I../../../qw_platform/qwos/driver/sensor/gps/airoha_gps -I../../../qw_platform/qwos/driver/sensor/gps/airoha_gps/downloadfw/inc -I../../../qw_platform/qwos/driver/sensor/gps/airoha_gps/downloadfw/inc/hdl_ports -I../../Application/Drivers/sw_version -I../../Application/sensor_algorithm/sensor -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_ppg -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_barometer -I../../Application/sensor_algorithm/algorithm -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_charger -I../../Application/Lib_New/ride_data -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_coulombmeter -I../../../sifli/customer/peripherals/dc009s -I../../../sifli/customer/peripherals/AW86224 -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_gps -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_magsensor -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_knob -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_beep -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_vibration -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_button -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_radio -I../../../sifli/customer/peripherals/mt3503 -I../../../sifli/rtos/rtthread/components/drivers/sensors -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_imu -I../../Application/Lib_3rd -I../../Application/Lib_3rd/GoMore/SDK -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_sensor_manager -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_lcd -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_filesys -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_paymodule -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_tp -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_ltr -I../../../sifli/customer/peripherals/LTRX140A -I../../Application/Lib_New/navi -I../../Application/Lib_New/data_filter -I../../../sifli/external/CmBacktrace-v1.3.0 -I../../../sifli/customer/peripherals/encipher_ic -I../../../sifli/customer/peripherals/encipher_ic/include -I../../../sifli/customer/peripherals/encipher_ic/hal/bluetooth -I../../../sifli/customer/peripherals/encipher_ic/hal/device_info -I../../../sifli/customer/peripherals/encipher_ic/hal/os -I../../../sifli/customer/peripherals/encipher_ic/hal/se/hsc32i1/hw -I../../../sifli/customer/peripherals/encipher_ic/hal/se/hsc32i1/softse -I../../../sifli/customer/peripherals/encipher_ic/hal/se/se_v1 -I../../Application/App/radio/ble_lb55x -I../../../sifli/customer/peripherals/encipher_ic/hal -I../../Application/App/UI/Page/AppMenu/Alipay -I../../../sifli/middleware/algorithm/common -I../../Application/Lib_New -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_ppg_demo -I../../../sifli/customer/peripherals/TP_cst9217 -I../../../sifli/customer/boards/common/sw_uart -I../../../sifli/drivers/multicore_drivers/Drivers/multicore_swuart -I../../../sifli/drivers/multicore_drivers/Drivers/multicore_filesys -I../../Application/sensorhub_v2/hcpu -I../../Application/sensorhub_v2 -I../../../qw_platform/qwos/inc -I../../../qw_platform/qwos_app -I../../../qw_platform/qwos_app/log -I../../../qw_platform/qwos -I../../../qw_platform/qwos/module/touchx/touchgfx/include -I../../../qw_platform/qwos/module/touchx/lv_engine -I../../Application/App/UI/Page/FactoryPage/FactoryBaroMeter -I../../Application/App/UI/Page/FactoryPage/FactoryGpsSignal -I../../Application/App/UI/Page/FactoryPage/FactoryGpsStartTest -I../../Application/App/UI/Page/FactoryPage/FactoryHrmSpo -I../../Application/App/UI/Page/FactoryPage/FactoryTest -I../../Application/subscribe_service -I../../Application/algo_service/algo_service_component -I../../Application/algo_service/algo_service_task -I../../Application/algo_service/algo_service_adapter -I../../Application/algo_service/algo_service_config -I../../Application/Lib_3rd/GoMore -I../../Application/service -I../../Application/App/UI/Page/FactoryPage/BatteryChargingStatus -I../../../sifli/customer/peripherals/factory_id -I../../../sifli/drivers/Multicore_Drivers/Drivers/multicore_factory_id -I../../Application/service/common/inc -I../../Application/service/daily_activity -I../../Application/service/health -I../../../qw_platform/qwos/framework/kvdb -I../../Application/service/datetime -I../../../qw_platform/qwos/module/touchx_external/media -I../../../qw_platform/qwos/module/touchx_external/rlottie -I../../../qw_platform/qwos/module/touchx_external/rlottie/inc -I../../../qw_platform/qwos/module/touchx_external/rlottie/src -I../../../qw_platform/qwos/module/touchx_external/rlottie/src/vector -I../../../qw_platform/qwos/module/touchx_external/rlottie/src/vector/freetype -I../../../qw_platform/qwos/module/touchx_external -I../../../qw_platform/qwos/module/qw_syslog -I../../../qw_platform/qwos/driver/sensor -I../../../qw_platform/qwos/base -I../../Application/service/userinformation -I../../Application/service/subscribe_data -I../../Application/algo_service/algo_service_component/algo_service_sports -I../../Application/algo_service/algo_service_component/algo_service_altitude -I../../Application/Lib_New/ride_data/digital_algorithm -I../../Application/algo_service/algo_service_component/algo_service_gps -I../../../qw_platform/qwos/module/touchx -I../../Application/App/basic_app_module/data_collect_module -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_rd/pages -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_profiles/ant_rd -I../../Application/App/radio/sensor/sensor_ant/rd -I../../../qw_platform/qwos/module/gps -I../../Application/App/basic_app_module/battery_srv_module -I../../../qw_platform/qwos_app/power_onoff_manager -I../../../qw_platform/qwos_app/GUI -I../../../qw_platform/qwos_app/GUI/QwWidget -I../../Application/Lib_New/settings -I../../Application/algo_service/algo_service_component/algo_service_coodix -I.. -I../../Application/service/wristup -I../../../qw_platform/qwos_app/fit -I../../Application/App/basic_app_module/activity_record -I../../../qw_platform/qwos_app/sensor_data_srv -I../../Application/App/radio/nanopb/schedule -I../../Application/App/radio/sensor/sensor_ble/light -I../../Application/App/radio/sensor/sensor_ble/bas -I../../Application/App/radio/sensor/sensor_ble/bsc -I../../Application/App/radio/sensor/sensor_ble/cad -I../../Application/App/radio/sensor/sensor_ble/hrm -I../../Application/App/radio/sensor/sensor_ble/spd -I../../../qw_platform/qwos/framework/pm_manager -I../../../qw_platform/qwos/framework/msg_services -I../../../sifli/external/SEGGER_SystemView-master/SystemView_Src/SEGGER -I../../../sifli/external/SEGGER_SystemView-master/SystemView_Src/Config -I../../../qw_platform/qwos/middleware/qw_com -I../../Application/App/basic_app_module/alarm_clock_app -I../../Application/App/basic_app_module/timer_app -I../../../sifli/external/nRF5_SDK_16.0.0/components/ant/ant_search_config -I../../Application/Lib_New/workout/inc -I../../../qw_platform/qwos_app/GUI/QwWidget/QwWorkoutStep -I../../Application/App/basic_app_module/auto_wheel_perimeter_srv -I../../../qw_platform/qwos/middleware/system_stress/inc -I../../Application/Lib_New/workout -I../../Application/Drivers/beep -I../../Application/Drivers/motor -I../../Application/Drivers/player -I../../../qw_platform/qwos_app/sports_data -I../../../qw_platform/qwos/module/sports_data -I../../../qw_platform/qwos/module/fit/inc_fit_codec -I../../../qw_platform/qwos_app/trace -I../../../sifli/external/RTL8189FTV_SDK/platform/sdio/include -I../../../qw_platform/qwos/framework/kvdb/flash_kv -I../../../qw_platform/qwos/driver/sensor/qw_sensor_manager -I../../Application/App/radio/nanopb/watch_config -I../../Application/App/radio/nanopb/health -I../../../sifli/middleware/bluetooth/internal -I../../../qw_platform/qwos_app/GUI/QwMsgBox -I../../Application/service/developer -I../../../qw_platform/qwos_app/firmware_iap -I../../Application/algo_service/algo_service_statistics -I../../Application/App/radio/nanopb/watch_dial -I../../Application/App/basic_app_module/heart_push -I../../../qw_platform/qwos_app/navi -I../../../qw_platform/qwos_app/navi/navi_new -I../../../qw_platform/qwos_app/navi/navi_new/port -I../../../qw_platform/qwos_app/navi/navi_new/others -I../../../qw_platform/qwos_app/navi/navi_new/external -I../../../sifli/external/minizip -I../../../sifli/external/zlib -I../../../qw_platform/qwos_app/navi/map -I../../../qw_platform/qwos_app/navi/track_new -I../../../qw_platform/qwos_app/navi/track_new/port -I../../../qw_platform/qwos_app/navi/navi_new/canvas -I../../Application/App/radio/application/production_test -I../../../qw_platform/qwos/framework/qw_env -I../../../sifli/rtos/rtthread/components/drivers/include/ipc -I../../../qw_platform/qwos_app/firmware_iap/Qboot -I../../Application/App/basic_app_module/navi_srv_module -I../../Application/App/radio/nrf_dfu_uart -I../../Application/service/sport_ability -I../../../qw_platform/qwos/module/ota -I../../../qw_platform -I../../../qw_platform/qwos/framework/cross_core_msh -I../../../qw_platform/qwos_app/factory_data -I../../Application/App/UI/Page/FactoryPage/FactoryQrCode -I../../Application/App/UI/Page/FactoryPage/FactoryCompassAlign -I../../Application/App/UI/Page/FactoryPage/FactoryANTBLE/AddSensor -I../../Application/App/UI/Page/FactoryPage/FactoryANTBLE/SensorMenu -I../../Application/App/UI/Page/FactoryPage/FactoryANTBLE/Sensors -I../../Application/App/UI/Page/FactoryPage/FactorySensorLink -I../../Application/App/UI/Page/FactoryPage/FactoryANTBLE/EditSensor/Crank -I../../Application/App/UI/Page/FactoryPage/FactoryANTBLE/EditSensor -I../../../sifli/drivers/hal_epic_ex -I../../Application/App/radio/nanopb/sport_page_field -I../../Application/App/basic_app_module/hydration_remind -I../../../qw_platform/qwos/module/argument_prase -I../../../sifli/external/bsdiff_upgrade -I../../../sifli/external/bsdiff_upgrade/bsdiff -I../../../sifli/external/bsdiff_upgrade/lib -I../../../sifli/external/bsdiff_upgrade/lzma -I../../Application/App/radio/nanopb/media -I../../Application/App/basic_app_module/music_control -I../../Application/algo_service/algo_service_component/algo_service_sports/sport_alg -I../../Application/algo_service/algo_service_component/algo_service_sports/sport_alg/port -I../../../qw_platform/qwos/driver/sensor/light/w1160 -I../../Application/App/basic_app_module/factory_module/burn_test -I../../../qw_algo/navigation/navi -DSIFLI_VERSION="33619975" -D__FILE__="__FILE_NAME__" -DSF32LB55X -DUSE_HAL_DRIVER -DLB55X_CHIP_ID="3" -DARM_MATH_LOOPUNROLL -DSOC_BF0_HCPU -DUSE_FULL_ASSERT -DSIFLI_BUILD="\"000000\"" -DRT_USING_ARM_LIBC -DIGS_DEV -DAPP_TIMER_V2 -DUSING_MINI_RLOTTIE -D_ARM_LIBCPP_EXTERNAL_THREADS -DUSING_RTOS -DLOTTIE_JSON_SUPPORT -DMEMCPY_NON_DMA="1" -DPSRAM_CACHE_WB="1" -DSOC_SF32LB55X -DBLE_STACK_SUPPORT_REQD -DANT_STACK_SUPPORT_REQD -DNRF_SD_BLE_API_VERSION="7" -DBLE_AND_ANT -DSVCALL_AS_NORMAL_FUNCTION -DIGS_DEV="1" -DQW_OK="0" -DQW_ERROR="-1" -DUSE_LIB_GM_COLLECT -DUSE_CPU_MEM_MGR -DUSE_NEW_SENSOR_MODULE -D__NAVI="1" -DTEMP_SHOW_GPS_START="0" -mfloat-abi=hard -mfpu=fpv5-sp-d16 -mcpu=cortex-m33+nodsp -mlittle-endian -Oz -ffunction-sections -funsigned-char -fshort-enums -fshort-wchar -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -xc -std=c11 --target=arm-arm-none-eabi -mcpu=cortex-m33 -mfpu=fpv5-sp-d16 -mfloat-abi=hard -c -fno-rtti -funsigned-char -fshort-enums -fshort-wchar -Werror -Wno-builtin-macro-redefined -Warray-bounds -Wformat-security -Wpointer-arith -g -o ./build/WR02_APP/.obj/__/__/Application/Lib_New/settings/cfg_breath_train.o -MMD ./../../Application/Lib_New/settings/cfg_breath_train.c