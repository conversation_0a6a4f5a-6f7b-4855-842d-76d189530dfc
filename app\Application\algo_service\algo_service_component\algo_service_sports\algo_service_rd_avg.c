﻿/*************************************************************************
 * @file algo_service_rd_avg.c
 * <AUTHOR> (<EMAIL>)
 * @brief 平均跑步动态算法组件实现
 * @version 0.1
 * @date 2025-01-13
 *
 * @copyright Copyright (c) 2024-2025, <PERSON>han Qiwu Technology Co., Ltd
 *
 ************************************************************************/
#include "algo_service_rd_avg.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_task.h"
#include "max_min.h"
#include "qw_time_util.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"

// 输入数据
typedef struct
{
    uint32_t distance;           // 总距离100 * m
    uint32_t distance_lap;       // 圈距离 100 * m
    uint32_t distance_pre_lap;   // 前圈距离 100 * m
    uint16_t vertical_oscillation;     //0.1mm
    uint16_t ground_contact_time;      //0.1ms
    uint16_t stance_time_percent;      //0.01%
    uint16_t ground_contact_balance;   //0.01%
    uint16_t vertical_ratio;           //0.01%
    uint16_t step_length;              //mm
    uint16_t cadence;                  //spm
    uint8_t step_count;                //127 rollover. 增量值，累计值在下面平均跑步动态algo_rd_avg_pub_t结构体中的step_count
    saving_status_e saving_status;     //数据记录的状态
} algo_rd_avg_sub_t;

static algo_rd_avg_sub_t s_algo_in = {0};

// 发布数据
static algo_rd_avg_pub_t s_algo_out = {0};

// 中间数据
static max_min_uint16_t s_max_min_vertical_oscillation = {0};
static max_min_uint16_t s_max_min_vertical_oscillation_lap = {0};
static max_min_uint16_t s_max_min_gct = {0};
static max_min_uint16_t s_max_min_gct_lap = {0};
static max_min_uint16_t s_max_min_stp = {0};
static max_min_uint16_t s_max_min_stp_lap = {0};
static max_min_uint16_t s_max_min_gcb = {0};
static max_min_uint16_t s_max_min_gcb_lap = {0};
static max_min_uint16_t s_max_min_vertical_ratio = {0};
static max_min_uint16_t s_max_min_vertical_ratio_lap = {0};
static max_min_uint16_t s_max_min_step_length = {0};
static max_min_uint16_t s_max_min_step_length_lap = {0};
static max_min_uint16_t s_max_min_cadence = {0};
static max_min_uint16_t s_max_min_cadence_lap = {0};

static uint8_t s_last_step_count = 0;

// 本算法打开标记
static bool s_is_open = false;

/**
 * @brief 算法处理
 *
 * @param algo_out 输出数据
 * @param algo_in 输入数据
 */
static void algo_rd_avg_deal(algo_rd_avg_pub_t *algo_out, const algo_rd_avg_sub_t *algo_in)
{
    if (enum_status_saving == algo_in->saving_status)
    {
        if (0xff != algo_in->step_count)
        {
            max_min_uint16_update(&s_max_min_vertical_oscillation, algo_in->vertical_oscillation);
            algo_out->avg_vertical_oscillation = s_max_min_vertical_oscillation.avg;
            max_min_uint16_update(&s_max_min_vertical_oscillation_lap, algo_in->vertical_oscillation);
            algo_out->avg_vertical_oscillation_lap = s_max_min_vertical_oscillation_lap.avg;

            max_min_uint16_update(&s_max_min_gct, algo_in->ground_contact_time);
            algo_out->avg_ground_contact_time = s_max_min_gct.avg;
            max_min_uint16_update(&s_max_min_gct_lap, algo_in->ground_contact_time);
            algo_out->avg_ground_contact_time_lap = s_max_min_gct_lap.avg;

            max_min_uint16_update(&s_max_min_stp, algo_in->stance_time_percent);
            algo_out->avg_stance_time_percent = s_max_min_stp.avg;
            max_min_uint16_update(&s_max_min_stp_lap, algo_in->stance_time_percent);
            algo_out->avg_stance_time_percent_lap = s_max_min_stp_lap.avg;

            max_min_uint16_update(&s_max_min_gcb, algo_in->ground_contact_balance);
            algo_out->avg_ground_contact_balance = s_max_min_gcb.avg;
            max_min_uint16_update(&s_max_min_gcb_lap, algo_in->ground_contact_balance);
            algo_out->avg_ground_contact_balance_lap = s_max_min_gcb_lap.avg;

            max_min_uint16_update(&s_max_min_vertical_ratio, algo_in->vertical_ratio);
            algo_out->avg_vertical_ratio = s_max_min_vertical_ratio.avg;
            max_min_uint16_update(&s_max_min_vertical_ratio_lap, algo_in->vertical_ratio);
            algo_out->avg_vertical_ratio_lap = s_max_min_vertical_ratio_lap.avg;

            max_min_uint16_update(&s_max_min_step_length, algo_in->step_length);
            algo_out->avg_step_length = s_max_min_step_length.avg;
            algo_out->max_step_length = s_max_min_step_length.max;
            max_min_uint16_update(&s_max_min_step_length_lap, algo_in->step_length);
            algo_out->avg_step_length_lap = s_max_min_step_length_lap.avg;
            algo_out->max_step_length_lap = s_max_min_step_length_lap.max;

            max_min_uint16_update(&s_max_min_cadence, algo_in->cadence);
            algo_out->avg_cadence = s_max_min_cadence.avg;
            algo_out->max_cadence = s_max_min_cadence.max;
            max_min_uint16_update(&s_max_min_cadence_lap, algo_in->cadence);
            algo_out->avg_cadence_lap = s_max_min_cadence_lap.avg;
            algo_out->max_cadence_lap = s_max_min_cadence_lap.max;

            algo_out->step_count += ((uint32_t) algo_in->step_count + 128 - s_last_step_count) % 128;
            algo_out->step_count_lap += ((uint32_t) algo_in->step_count + 128 - s_last_step_count) % 128;
            s_last_step_count = algo_in->step_count;

            // 平均步长
            if (0 != algo_out->step_count)
            {
                algo_out->avg_step_length = (uint16_t) (algo_in->distance * 10 / algo_out->step_count);
            }

            if (0 != algo_out->step_count_lap)
            {
                algo_out->avg_step_length_lap = (uint16_t) (algo_in->distance_lap * 10 / algo_out->step_count_lap);
            }

            if (0 != algo_out->step_count_pre_lap)
            {
                algo_out->avg_step_length_pre_lap = (uint16_t) (algo_in->distance_pre_lap * 10 / algo_out->step_count_pre_lap);
            }

            // 步长异常处理
            if (algo_out->avg_step_length > algo_out->max_step_length)
            {
                algo_out->avg_step_length = algo_out->max_step_length;
            }

            if (algo_out->avg_step_length_lap > algo_out->max_step_length_lap)
            {
                algo_out->avg_step_length_lap = algo_out->max_step_length_lap;
            }

            if (algo_out->avg_step_length_pre_lap > algo_out->max_step_length_pre_lap)
            {
                algo_out->avg_step_length_pre_lap = algo_out->max_step_length_pre_lap;
            }
        }
    }
}

/**
 * @brief 算法控制
 *
 * @param algo_out 输出数据
 * @param ctrl_type 控制类型
 */
static void algo_rd_avg_ctrl(algo_rd_avg_pub_t *algo_out, ctrl_type_e ctrl_type)
{
    if (enum_ctrl_start == ctrl_type)
    {
        memset(algo_out, 0xff, sizeof(algo_rd_avg_pub_t));
        algo_out->step_count = 0;
        algo_out->step_count_lap = 0;
        algo_out->step_count_pre_lap = 0;
        max_min_uint16_init(&s_max_min_vertical_oscillation);
        max_min_uint16_init(&s_max_min_vertical_oscillation_lap);
        max_min_uint16_init(&s_max_min_gct);
        max_min_uint16_init(&s_max_min_gct_lap);
        max_min_uint16_init(&s_max_min_stp);
        max_min_uint16_init(&s_max_min_stp_lap);
        max_min_uint16_init(&s_max_min_gcb);
        max_min_uint16_init(&s_max_min_gcb_lap);
        max_min_uint16_init(&s_max_min_vertical_ratio);
        max_min_uint16_init(&s_max_min_vertical_ratio_lap);
        max_min_uint16_init(&s_max_min_step_length);
        max_min_uint16_init(&s_max_min_step_length_lap);
        max_min_uint16_init(&s_max_min_cadence);
        max_min_uint16_init(&s_max_min_cadence_lap);
        s_last_step_count = 0;
    }
    else if (enum_ctrl_lap == ctrl_type)
    {
        algo_out->avg_vertical_oscillation_pre_lap = algo_out->avg_vertical_oscillation_lap;
        algo_out->avg_ground_contact_time_pre_lap = algo_out->avg_ground_contact_time_lap;
        algo_out->avg_stance_time_percent_pre_lap = algo_out->avg_stance_time_percent_lap;
        algo_out->avg_ground_contact_balance_pre_lap = algo_out->avg_ground_contact_balance_lap;
        algo_out->avg_vertical_ratio_pre_lap = algo_out->avg_vertical_ratio_lap;
        algo_out->avg_step_length_pre_lap = algo_out->avg_step_length_lap;
        algo_out->max_step_length_pre_lap = algo_out->max_step_length_lap;
        algo_out->avg_cadence_pre_lap = algo_out->avg_cadence_lap;
        algo_out->max_cadence_pre_lap = algo_out->max_cadence_lap;
        algo_out->step_count_pre_lap = algo_out->step_count_lap;
        algo_out->avg_vertical_oscillation_lap = 0xffff;
        algo_out->avg_ground_contact_time_lap = 0xffff;
        algo_out->avg_stance_time_percent_lap = 0xffff;
        algo_out->avg_ground_contact_balance_lap = 0xffff;
        algo_out->avg_vertical_ratio_lap = 0xffff;
        algo_out->avg_step_length_lap = 0xffff;
        algo_out->max_step_length_lap = 0xffff;
        algo_out->avg_cadence_lap = 0xffff;
        algo_out->max_cadence_lap = 0xffff;
        algo_out->step_count_lap = 0;
        max_min_uint16_init(&s_max_min_vertical_oscillation_lap);
        max_min_uint16_init(&s_max_min_gct_lap);
        max_min_uint16_init(&s_max_min_stp_lap);
        max_min_uint16_init(&s_max_min_gcb_lap);
        max_min_uint16_init(&s_max_min_vertical_ratio_lap);
        max_min_uint16_init(&s_max_min_step_length_lap);
        max_min_uint16_init(&s_max_min_cadence_lap);
    }
}

/**
 * @brief 算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_running_dynamics_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_RD_AVG;
    head.input_type = DATA_ID_ALGO_RUNNING_DYNAMICS;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_distance_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_RD_AVG;
    head.input_type = DATA_ID_ALGO_DISTANCE;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法控制订阅处理
 *
 * @param in 控制数据
 * @param len 数据长度
 */
static void algo_sports_ctrl_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_RD_AVG;
    head.input_type = DATA_ID_EVENT_SPORTS_CTRL;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_rd_avg_out_callback(const void *out, uint32_t len)
{
    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_ALGO_RD_AVG, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("%s publish error", __FUNCTION__);
    }
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_ALG_NAME_RUNNING_DYNAMICS,
        .topic_id = DATA_ID_ALGO_RUNNING_DYNAMICS,
        .callback = algo_running_dynamics_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_distance",
        .topic_id = DATA_ID_ALGO_DISTANCE,
        .callback = algo_distance_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
};

/**
 * @brief 算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_rd_avg_init(void)
{
    return 0;
}

/**
 * @brief 算法open
 *
 * @return int32_t 结果
 */
static int32_t algo_rd_avg_open(void)
{
    if (s_is_open)
    {
        ALGO_COMP_LOG_W("%s already open", __FUNCTION__);
        return 0;
    }

    ALGO_COMP_LOG_D("%s open", __FUNCTION__);

    s_is_open = true;
    memset(&s_algo_in, 0xff, sizeof(s_algo_in));
    s_algo_in.saving_status = 0;
    memset(&s_algo_out, 0xff, sizeof(algo_rd_avg_pub_t));
    s_algo_out.step_count = 0;
    s_algo_out.step_count_lap = 0;
    s_algo_out.step_count_pre_lap = 0;
    max_min_uint16_init(&s_max_min_vertical_oscillation);
    max_min_uint16_init(&s_max_min_vertical_oscillation_lap);
    max_min_uint16_init(&s_max_min_gct);
    max_min_uint16_init(&s_max_min_gct_lap);
    max_min_uint16_init(&s_max_min_stp);
    max_min_uint16_init(&s_max_min_stp_lap);
    max_min_uint16_init(&s_max_min_gcb);
    max_min_uint16_init(&s_max_min_gcb_lap);
    max_min_uint16_init(&s_max_min_vertical_ratio);
    max_min_uint16_init(&s_max_min_vertical_ratio_lap);
    max_min_uint16_init(&s_max_min_step_length);
    max_min_uint16_init(&s_max_min_step_length_lap);
    max_min_uint16_init(&s_max_min_cadence);
    max_min_uint16_init(&s_max_min_cadence_lap);
    s_last_step_count = 0;

    return algo_topic_list_subscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
}

/**
 * @brief 算法feed
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_rd_avg_feed(uint32_t input_type, void *data, uint32_t len)
{
    algo_rd_avg_sub_t *algo_in = &s_algo_in;
    algo_rd_avg_pub_t *algo_out = &s_algo_out;

    switch (input_type)
    {
    case DATA_ID_ALGO_RUNNING_DYNAMICS:
    {
        const algo_running_dynamics_pub_t *rd_data = (algo_running_dynamics_pub_t *) data;
        algo_in->vertical_oscillation = rd_data->vertical_oscillation;
        algo_in->ground_contact_time = rd_data->ground_contact_time;
        algo_in->stance_time_percent = rd_data->stance_time_percent;
        algo_in->ground_contact_balance = rd_data->ground_contact_balance;
        algo_in->vertical_ratio = rd_data->vertical_ratio;
        algo_in->step_length = rd_data->step_length;
        algo_in->cadence = rd_data->cadence;
        algo_in->step_count = rd_data->step_count;
    }
    break;
    case DATA_ID_ALGO_DISTANCE:
    {
        const algo_distance_pub_t *distance_data = (algo_distance_pub_t *) data;
        algo_in->distance = distance_data->distance;
        algo_in->distance_lap = distance_data->distance_lap;
        algo_in->distance_pre_lap = distance_data->distance_pre_lap;
    }
    break;
    case DATA_ID_EVENT_SPORTS_CTRL:
    {
        const algo_sports_ctrl_t *sports_ctrl = (algo_sports_ctrl_t *) data;
        algo_in->saving_status = sports_ctrl->saving_status;

        if (enum_ctrl_null == sports_ctrl->ctrl_type)
        {
            //算法处理
            algo_rd_avg_deal(algo_out, algo_in);
        }
        else
        {
            //算法控制
            algo_rd_avg_ctrl(algo_out, sports_ctrl->ctrl_type);
        }

        //数据发布
        algo_rd_avg_out_callback(algo_out, sizeof(algo_rd_avg_pub_t));
    }
    break;
    default:
        break;
    }

    return 0;
}

/**
 * @brief 算法close
 *
 * @return int32_t 结果
 */
static int32_t algo_rd_avg_close(void)
{
    int ret = -1;

    if (!s_is_open)
    {
        ALGO_COMP_LOG_W("%s already close", __FUNCTION__);
        return 0;
    }

    algo_topic_list_unsubscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));

    s_is_open = false;
    ALGO_COMP_LOG_D("%s close", __FUNCTION__);
    return 0;
}

// 算法组件实现
static algo_compent_ops_t s_rd_avg_algo = {
    .init = algo_rd_avg_init,
    .open = algo_rd_avg_open,
    .feed = algo_rd_avg_feed,
    .close = algo_rd_avg_close,
    .ioctl = NULL,
};

/**
 * @brief 组件注册
 *
 * @return int32_t 结果
 */
int32_t register_rd_avg_algo(void)
{
    algo_compnent_register(ALGO_TYPE_RD_AVG, &s_rd_avg_algo);
    return 0;
}