/************************************************************************
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
*@File : navi_tclm.c
 @Time : 2025/05/12 16:41:53
************************************************************************/

#include <string.h>
#include <stddef.h>
#include <stdbool.h>
#include "navi_tclm.h"

typedef int (*tclm_climb_read_t)(TclmReader *self, uint32_t idx, float total_dist, NaviClimbData *climb);

//读取文件头
int tclm_header_read(TclmReader *self, uint8_t *header)
{
    if (self == NULL || header == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 0) != QW_OK)
    {
        return -1;
    }

    UINT br = 0;

    if (qw_f_read(self->fp, header, 32, &br) != QW_OK || br != 32)
    {
        return -1;
    }

    return 0;
}

//读取上坡总体数据，在读取其他数据之前必须先读取总体数据
int tclm_pos_ensemble_data_read(TclmReader *self, NaviClimbEnsembleData *data)
{
    if (self == NULL || data == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 32) != QW_OK)
    {
        return -1;
    }

    uint8_t buf[24] = { 0 };
    UINT br = 0;

    if (qw_f_read(self->fp, buf, 24, &br) != QW_OK || br != 24)
    {
        return -1;
    }

    const uint32_t check_code = *((uint32_t *)buf);
    const uint32_t size = *((uint32_t *)buf + 1);
    const uint32_t num = *((uint32_t *)buf + 2);

    if (check_code != 0 || size != 12 || num != 1)
    {
        return -1;
    }

    memcpy(data, buf + 12, 12);

    //获取上坡总数
    self->pos_num = data->num;

    return 0;
}

//读取下坡总体数据，在读取其他数据之前必须先读取总体数据
int tclm_neg_ensemble_data_read(TclmReader *self, NaviClimbEnsembleData *data)
{
    if (self == NULL || data == NULL)
    {
        return -1;
    }

    //32 + (12 + 12)
    if (qw_f_lseek(self->fp, 56) != QW_OK)
    {
        return -1;
    }

    uint8_t buf[24] = { 0 };
    UINT br = 0;

    if (qw_f_read(self->fp, buf, 24, &br) != QW_OK || br != 24)
    {
        return -1;
    }

    const uint32_t check_code = *((uint32_t *)buf);
    const uint32_t size = *((uint32_t *)buf + 1);
    const uint32_t num = *((uint32_t *)buf + 2);

    if (check_code != 1 || size != 12 || num != 1)
    {
        return -1;
    }

    memcpy(data, buf + 12, 12);

    //获取下坡总数
    self->neg_num = data->num;

    return 0;
}

//读取线路爬坡点采样
int tclm_route_cp_sample_read(TclmReader *self, NaviClimbSample *sample)
{
    if (self == NULL || sample == NULL)
    {
        return -1;
    }

    //32 + (12 + 12) + (12 + 12)
    if (qw_f_lseek(self->fp, 80) != QW_OK)
    {
        return -1;
    }

    uint32_t buf[3] = { 0 };
    UINT br = 0;

    if (qw_f_read(self->fp, buf, 12, &br) != QW_OK || br != 12)
    {
        return -1;
    }

    const uint32_t check_code = buf[0];
    const uint32_t size = buf[1];
    const uint32_t num = buf[2];

    if (check_code != 2 || size != 8 || num > NAVI_ROUTE_CP_SAMPLES_NUM)
    {
        return -1;
    }

    sample->len = num;

    if (sample->len == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const UINT btr = sample->len * 8;

    if (qw_f_read(self->fp, sample->buf, btr, &br) != QW_OK || br != btr)
    {
        return -1;
    }

    return 0;
}

//读取线路爬坡点分段
int tclm_route_cp_segment_array_read(TclmReader *self, NaviRouteCpSegmentArray *array)
{
    if (self == NULL || array == NULL)
    {
        return -1;
    }

    //32 + (12 + 12) + (12 + 12) + (12 + 300 * 8) = 2492
    if (qw_f_lseek(self->fp, 2492) != QW_OK)
    {
        return -1;
    }

    uint32_t buf[3] = { 0 };
    UINT br = 0;

    if (qw_f_read(self->fp, buf, 12, &br) != QW_OK || br != 12)
    {
        return -1;
    }

    const uint32_t check_code = buf[0];
    const uint32_t size = buf[1];
    const uint32_t num = buf[2];

    if (check_code != 3 || size != 16 || num > NAVI_ROUTE_CP_SEGMENTS_NUM)
    {
        return -1;
    }

    array->len = num;

    if (array->len == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const uint32_t btr = array->len * 16;

    if (qw_f_read(self->fp, array->segments, btr, &br) != QW_OK || br != btr)
    {
        return -1;
    }

    return 0;
}

//读取上坡分段
int tclm_pos_segment_array_read(TclmReader *self, NaviClimbSegmentArray *array)
{
    if (self == NULL || array == NULL)
    {
        return -1;
    }

    //32 + (12 + 12) + (12 + 12) + (12 + 300 * 8) + (12 + 100 * 16) = 4104
    if (qw_f_lseek(self->fp, 4104) != QW_OK)
    {
        return -1;
    }

    uint32_t buf[3] = { 0 };
    UINT br = 0;

    if (qw_f_read(self->fp, buf, 12, &br) != QW_OK || br != 12)
    {
        return -1;
    }

    const uint32_t check_code = buf[0];
    const uint32_t size = buf[1];
    const uint32_t num = buf[2];

    if (check_code != 4 || size != 16 || num > NAVI_CLIMB_POS_SEGMENTS_NUM)
    {
        return -1;
    }

    array->len = num;

    if (array->len == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const UINT btr = array->len * 16;

    if (qw_f_read(self->fp, array->segments, btr, &br) != QW_OK || br != btr)
    {
        return -1;
    }

    return 0;
}

//读取下坡分段
int tclm_neg_segment_array_read(TclmReader *self, NaviClimbSegmentArray *array)
{
    if (self == NULL || array == NULL)
    {
        return -1;
    }

    //32 + (12 + 12) + (12 + 12) + (12 + 300 * 8) + (12 + 100 * 16) + (12 + 20 * 16) = 4436
    if (qw_f_lseek(self->fp, 4436) != QW_OK)
    {
        return -1;
    }

    uint32_t buf[3] = { 0 };
    UINT br = 0;

    if (qw_f_read(self->fp, buf, 12, &br) != QW_OK || br != 12)
    {
        return -1;
    }

    const uint32_t check_code = buf[0];
    const uint32_t size = buf[1];
    const uint32_t num = buf[2];

    if (check_code != 5 || size != 16 || num > NAVI_CLIMB_NEG_SEGMENTS_NUM)
    {
        return -1;
    }

    array->len = num;

    if (array->len == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const UINT btr = array->len * 16;

    if (qw_f_read(self->fp, array->segments, btr, &br) != QW_OK || br != btr)
    {
        return -1;
    }

    return 0;
}

//读取一个爬坡
static int tclm_climb_read(TclmReader *self, uint32_t offset, NaviClimbData *climb)
{
    if (self == NULL || climb == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, offset) != QW_OK)
    {
        return -1;
    }

    uint8_t buf[48] = { 0 };

    UINT br = 0;

    if (qw_f_read(self->fp, buf, 48, &br) != QW_OK || br != 48)
    {
        return -1;
    }
    /*将buf转成_NaviClimbData成员数据,
    可以用memcpy或者结构体映射，结构体映射需要考虑字节序，这里
    都用memcpy处理*/
    memcpy((void *)&climb->start_dist, buf, 44);
    climb->sample.len = *((uint32_t *) buf + 11);

    if (climb->sample.len == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const UINT btr = climb->sample.len * 8;

    if (qw_f_read(self->fp, climb->sample.buf, btr, &br) != QW_OK || br != btr)
    {
        return -1;
    }

    return 0;
}

//读取指定的上坡数据
int tclm_pos_climb_read(TclmReader *self, uint32_t idx, float total_dist, NaviClimbData *climb)
{
    if (self == NULL || climb == NULL)
    {
        return -1;
    }

    if (idx >= self->pos_num)
    {
        return -1;
    }

    //4436 + (12 + 20 * 16) = 4768
    const uint32_t offset = 4768 + 12 + 1008 * idx;

    return tclm_climb_read(self, offset, climb);
}

//读取指定的下坡数据
int tclm_neg_climb_read(TclmReader *self, uint32_t idx, float total_dist, NaviClimbData *climb)
{
    if (self == NULL || climb == NULL)
    {
        return -1;
    }

    if (idx >= self->neg_num)
    {
        return -1;
    }

    //4436 + (12 + 20 * 16) = 4768
    const uint32_t offset = 4768 + 12 + 1008 * self->pos_num + 12 + 1008 * idx;

    if (tclm_climb_read(self, offset, climb) != 0)
    {
        return -1;
    }

    //反转数据
    float tmp = climb->start_dist;
    climb->start_dist = total_dist - climb->end_dist;
    climb->end_dist = total_dist - tmp;

    tmp = climb->start_alt;
    climb->start_alt = climb->end_alt;
    climb->end_alt = tmp;

    climb->ascent = -climb->ascent;
    climb->max_grade = -climb->max_grade;
    climb->grade = -climb->grade;

    climb->h_acc = -climb->h_acc;

    for (uint32_t i = 0; i < climb->sample.len; i++)
    {
        climb->sample.buf[i].dist = total_dist - climb->sample.buf[i].dist;
    }

    NaviClimbpoint cp = { 0 };

    for (uint32_t i = 0; i < climb->sample.len / 2; i++)
    {
        navi_route_cp_copy(&cp, &climb->sample.buf[i]);
        navi_route_cp_copy(&climb->sample.buf[i], &climb->sample.buf[climb->sample.len-1-i]);
        navi_route_cp_copy(&climb->sample.buf[climb->sample.len-1-i], &cp);
    }

    return 0;
}

//读取线路爬坡点数量
int tclm_route_cp_num_read(TclmReader *self, uint32_t *cp_num)
{
    if (self == NULL || cp_num == NULL)
    {
        return -1;
    }

    const uint32_t offset = 4768 + 12 + 1008 * self->pos_num + 12 + 1008 * self->neg_num;

    if (qw_f_lseek(self->fp, offset) != QW_OK)
    {
        return -1;
    }

    uint32_t buf[3] = { 0 };
    UINT br = 0;

    if (qw_f_read(self->fp, buf, 12, &br) != QW_OK || br != 12)
    {
        return -1;
    }

    const uint32_t check_code = buf[0];
    const uint32_t size = buf[1];
    const uint32_t num = buf[2];

    if (check_code != 8 || size != 8)
    {
        return -1;
    }

    //TODO 是否允许数量为零？
    *cp_num = num;

    return 0;
}

//读取指定范围的爬坡点
int tclm_route_cp_read(TclmReader *self, uint32_t start, uint32_t end, uint32_t cp_num, NaviClimbpoint *cp_buf)
{
    if (self == NULL || cp_buf == NULL)
    {
        return -1;
    }

    if (start > end || end > cp_num)
    {
        return -1;
    }

    const uint32_t ntr = end - start;

    if (ntr == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const uint32_t offset = 4768 + 12 + 1008 * self->pos_num + 12 + 1008 * self->neg_num + 12 + start * 8;

    if (qw_f_lseek(self->fp, offset) != QW_OK)
    {
        return -1;
    }

    const UINT btr = ntr * 8;

    UINT br = 0;

    if (qw_f_read(self->fp, cp_buf, btr, &br) != QW_OK || br != btr)
    {
        return -1;
    }

    return 0;
}

//写入文件头占位符
int tclm_header_placeholder_write(TclmWriter *self)
{
    if (self == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 0) != QW_OK)
    {
        return -1;
    }

    const uint8_t buf[32] = {
        'S', 'h', 'i', 't', ' ', 'H', 'a', 'p', 'p', 'e', 'n', 's', '.',
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
    };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 32, &bw) != QW_OK || bw != 32)
    {
        return -1;
    }

    return 0;
}

//写入文件头
int tclm_header_write(TclmWriter *self)
{
    if (self == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 0) != QW_OK)
    {
        return -1;
    }

    UINT bw = 0;

    const uint8_t buf[32] = {
        'T', 'C', 'L', 'M', NAVI_FILE_MAJOR_VERSION, NAVI_FILE_MINOR_VERSION,
        'D', 'e', 's', 'i', 'g', 'n', 'e', 'd', ' ', 'b', 'y', ' ', 'J', 'u', 'n',
        'j', 'i', 'e', ' ', 'D', 'i', 'n', 'g', 0, 0, 0,
    };

    if (qw_f_write(self->fp, buf, 32, &bw) != QW_OK || bw != 32)
    {
        return -1;
    }

    return 0;
}

//写入上坡总体数据
int tclm_pos_ensemble_data_write(TclmWriter *self, const NaviClimbEnsembleData *data)
{
    if (self == NULL || data == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, 32) != QW_OK)
    {
        return -1;
    }

    const uint32_t buf[3] = { 0, 12, 1 };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    if (qw_f_write(self->fp, data, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    return 0;
}

//写入下坡总体数据
int tclm_neg_ensemble_data_write(TclmWriter *self, const NaviClimbEnsembleData *data)
{
    if (self == NULL || data == NULL)
    {
        return -1;
    }

    //32 + (12 + 12)
    if (qw_f_lseek(self->fp, 56) != QW_OK)
    {
        return -1;
    }

    const uint32_t buf[3] = { 1, 12, 1 };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    if (qw_f_write(self->fp, data, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    return 0;
}

//写入线路爬坡点采样
int tclm_route_cp_sample_write(TclmWriter *self, const NaviClimbSample *sample)
{
    if (self == NULL || sample == NULL)
    {
        return -1;
    }

    //32 + (12 + 12) + (12 + 12)
    if (qw_f_lseek(self->fp, 80) != QW_OK)
    {
        return -1;
    }

    const uint32_t buf[3] = { 2, 8, sample->len };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    if (sample->len == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const UINT btw = sample->len * 8;

    if (qw_f_write(self->fp, sample->buf, btw, &bw) != QW_OK || bw != btw)
    {
        return -1;
    }

    return 0;
}

//写入导航线路爬坡点分段
int tclm_route_cp_segment_array_write(TclmWriter *self, const NaviRouteCpSegmentArray *array)
{
    if (self == NULL || array == NULL)
    {
        return -1;
    }

    //32 + (12 + 12) + (12 + 12) + (12 + 300 * 8) = 2492
    if (qw_f_lseek(self->fp, 2492) != QW_OK)
    {
        return -1;
    }

    const uint32_t buf[3] = { 3, 16, array->len };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    if (array->len == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const UINT btw = array->len * 16;

    if (qw_f_write(self->fp, array->segments, btw, &bw) != QW_OK || bw != btw)
    {
        return -1;
    }

    return 0;
}

//写入上坡分段
int tclm_pos_segment_array_write(TclmWriter *self, const NaviClimbSegmentArray *array)
{
    if (self == NULL || array == NULL)
    {
        return -1;
    }

    //32 + (12 + 12) + (12 + 12) + (12 + 300 * 8) + (12 + 100 * 16) = 4104
    if (qw_f_lseek(self->fp, 4104) != QW_OK)
    {
        return -1;
    }

    const uint32_t buf[3] = { 4, 16, array->len };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    if (array->len == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const UINT btw = array->len * 16;

    if (qw_f_write(self->fp, array->segments, btw, &bw) != QW_OK || bw != btw)
    {
        return -1;
    }

    return 0;
}

//写入下坡分段
int tclm_neg_segment_array_write(TclmWriter *self, const NaviClimbSegmentArray *array)
{
    if (self == NULL || array == NULL)
    {
        return -1;
    }

    //32 + (12 + 12) + (12 + 12) + (12 + 300 * 8) + (12 + 100 * 16) + (12 + 20 * 16) = 4436
    if (qw_f_lseek(self->fp, 4436) != QW_OK)
    {
        return -1;
    }

    const uint32_t buf[3] = { 5, 16, array->len };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    if (array->len == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const UINT btw = array->len * 16;

    if (qw_f_write(self->fp, array->segments, btw, &bw) != QW_OK || bw != btw)
    {
        return -1;
    }

    return 0;
}

//写入上坡总数
int tclm_pos_climb_num_write(TclmWriter *self)
{
    if (self == NULL)
    {
        return -1;
    }

    //4436 + (12 + 20 * 16) = 4768
    if (qw_f_lseek(self->fp, 4768) != QW_OK)
    {
        return -1;
    }

    const uint32_t buf[3] = { 6, 1008, self->pos_num };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    return 0;
}

//写入一个爬坡数据
static int tclm_climb_write(TclmWriter *self, uint32_t offset, const NaviClimbData *climb)
{
    if (self == NULL || climb == NULL)
    {
        return -1;
    }

    if (qw_f_lseek(self->fp, offset) != QW_OK)
    {
        return -1;
    }

    UINT bw = 0;

    if (qw_f_write(self->fp, &climb->start_dist, 44, &bw) != QW_OK || bw != 44)
    {
        return -1;
    }

    if (qw_f_write(self->fp, &climb->sample.len, 4, &bw) != QW_OK || bw != 4)
    {
        return -1;
    }

    if (climb->sample.len == 0)
    {
        //TODO 是否允许数量为零？
        return 0;
    }

    const UINT btw = climb->sample.len * 8;

    if (qw_f_write(self->fp, climb->sample.buf, btw, &bw) != QW_OK || bw != btw)
    {
        return -1;
    }

    return 0;
}

//写入一个指定上坡数据
int tclm_pos_climb_write(TclmWriter *self, uint32_t idx, const NaviClimbData *climb)
{
    if (self == NULL || climb == NULL)
    {
        return -1;
    }

    if (idx >= self->pos_num)
    {
        return -1;
    }

    //4436 + (12 + 20 * 16) = 4768
    const uint32_t offset = 4768 + 12 + 1008 * idx;

    return tclm_climb_write(self, offset, climb);
}

//写入下坡总数
int tclm_neg_climb_num_write(TclmWriter *self)
{
    if (self == NULL)
    {
        return -1;
    }

    //4436 + (12 + 20 * 16) = 4768
    const uint32_t offset = 4768 + 12 + 1008 * self->pos_num;

    if (qw_f_lseek(self->fp, offset) != QW_OK)
    {
        return -1;
    }

    const uint32_t buf[3] = { 7, 1008, self->neg_num };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    return 0;
}

//写入一个指定下坡数据
int tclm_neg_climb_write(TclmWriter *self, uint32_t idx, const NaviClimbData *climb)
{
    if (self == NULL || climb == NULL)
    {
        return -1;
    }

    if (idx >= self->neg_num)
    {
        return -1;
    }

    //4436 + (12 + 20 * 16) = 4768
    const uint32_t offset = 4768 + 12 + 1008 * self->pos_num + 12 + 1008 * idx;

    return tclm_climb_write(self, offset, climb);
}

//写入导航线路爬坡点总数
int tclm_route_cp_num_write(TclmWriter *self, uint32_t num)
{
    if (self == NULL)
    {
        return -1;
    }

    //4436 + (12 + 20 * 16) = 4768
    const uint32_t offset = 4768 + 12 + 1008 * self->pos_num + 12 + 1008 * self->neg_num;

    if (qw_f_lseek(self->fp, offset) != QW_OK)
    {
        return -1;
    }

    const uint32_t buf[3] = { 8, 8, num };

    UINT bw = 0;

    if (qw_f_write(self->fp, buf, 12, &bw) != QW_OK || bw != 12)
    {
        return -1;
    }

    return 0;
}

//写入一个爬坡点
int tclm_route_cp_write(TclmWriter *self, const NaviClimbpoint *cp)
{
    if (self == NULL || cp == NULL)
    {
        return -1;
    }

    UINT bw = 0;

    if (qw_f_write(self->fp, cp, 8, &bw) != QW_OK || bw != 8)
    {
        return -1;
    }

    return 0;
}

//获取指定的爬坡点
int navi_route_cp_list_get(NaviRouteCpList *self, uint32_t idx, NaviClimbpoint *output)
{
    if (self == NULL || output == NULL)
    {
        return -1;
    }

    if (idx >= self->len)
    {
        return -1;
    }

    //缓存命中
    for (uint32_t i = 0; i < self->cache.len; i++)
    {
        const uint32_t start = self->cache.cp_buf[i].range.start;
        const uint32_t end = self->cache.cp_buf[i].range.end;

        if (idx >= start && idx < end)
        {
            for (uint32_t j = start; j < end; j++)
            {
                if (idx == j)
                {
                    navi_route_cp_copy(output, &self->cache.cp_buf[i].buf[j-start]);
                    return 0;
                }
            }
        }
    }

    //缓存未命中，则缓存从指定爬坡点开始的若干爬坡点
    NaviRouteCpBuf *cp_buf = &self->cache.cp_buf[self->cache.next];

    cp_buf->range.start = idx;
    cp_buf->range.end = cp_buf->range.start + cp_buf->capacity;

    if (cp_buf->range.end > self->len)
    {
        cp_buf->range.end = self->len;
    }

    if (tclm_route_cp_read(self->cache.tclm_reader, cp_buf->range.start, cp_buf->range.end, self->len, cp_buf->buf) != 0)
    {
        return -1;
    }

    navi_route_cp_copy(output, &cp_buf->buf[idx - cp_buf->range.start]);

    if (self->cache.len < self->cache.capacity)
    {
        self->cache.len += 1;
    }

    self->cache.next += 1;
    if (self->cache.next >= self->cache.capacity)
    {
        self->cache.next = 0;
    }

    //通常，需要使用一个爬坡点时，也需使用其前和其后若干爬坡点，其后若干爬坡点已经加载，需检查和加载其前的若干爬坡点
    if (self->cache.capacity <= 1)
    {
        return 0;
    }

    //计算一个爬坡点的前若干爬坡点范围
    const uint32_t prev_end = idx;
    const uint32_t capacity = self->cache.cp_buf[self->cache.next].capacity;
    const uint32_t prev_start = prev_end > capacity ? prev_end - capacity : 0;

    uint8_t is_prev_valid = false;

    //检查爬坡点的前若干爬坡点是否已经在缓存中
    for (uint32_t i = 0; i < self->cache.len; i++)
    {
        const uint32_t start = self->cache.cp_buf[i].range.start;
        const uint32_t end = self->cache.cp_buf[i].range.end;

        if (prev_start >= start && prev_end <= end)
        {
            is_prev_valid = true;
            break;
        }
    }

    //已经缓存，无需加载
    if (is_prev_valid == true)
    {
        return 0;
    }

    //加载前若干个爬坡点到缓存中
    cp_buf = &self->cache.cp_buf[self->cache.next];

    cp_buf->range.start = prev_start;
    cp_buf->range.end = prev_end;

    if (cp_buf->range.end > self->len)
    {
        cp_buf->range.end = self->len;
    }

    if (tclm_route_cp_read(self->cache.tclm_reader, cp_buf->range.start, cp_buf->range.end, self->len, cp_buf->buf) != 0)
    {
        return -1;
    }

    if (self->cache.len < self->cache.capacity)
    {
        self->cache.len += 1;
    }

    self->cache.next += 1;
    if (self->cache.next >= self->cache.capacity)
    {
        self->cache.next = 0;
    }

    return 0;
}

//获取指定爬坡数据
static int navi_climb_list_get(NaviClimbList *self, uint32_t idx, float total_dist, tclm_climb_read_t climb_read, NaviClimbData **output)
{
    if (self == NULL || climb_read == NULL || output == NULL)
    {
        return -1;
    }

    if (idx >= self->len)
    {
        return -1;
    }

    //缓存命中
    for (uint32_t i = 0; i < self->cache.len; i++)
    {
        const uint32_t start = self->cache.cd_buf[i].range.start;
        const uint32_t end = self->cache.cd_buf[i].range.end;

        if (idx >= start && idx < end)
        {
            for (uint32_t j = start; j < end; j++)
            {
                if (idx == j)
                {
                    *output = &self->cache.cd_buf[i].buf[j-start];
                    return 0;
                }
            }
        }
    }

    //缓存未命中，加载从指定爬坡开始的若干爬坡数据
    NaviClimbDataBuf *cd_buf = &self->cache.cd_buf[self->cache.next];

    cd_buf->range.start = idx;
    cd_buf->range.end = cd_buf->range.start + cd_buf->capacity;

    if (cd_buf->range.end > self->len)
    {
        cd_buf->range.end = self->len;
    }

    uint32_t cnt = 0;

    for (uint32_t i = cd_buf->range.start; i < cd_buf->range.end; i++)
    {
        if (climb_read(self->cache.tclm_reader, i, total_dist, &cd_buf->buf[cnt]) != 0)
        {
            return -1;
        }

        cnt += 1;
    }

    *output = &cd_buf->buf[idx - cd_buf->range.start];

    if (self->cache.len < self->cache.capacity)
    {
        self->cache.len += 1;
    }

    self->cache.next += 1;
    if (self->cache.next >= self->cache.capacity)
    {
        self->cache.next = 0;
    }

    return 0;
}

//获取指定上坡数据
int navi_pos_climb_list_get(NaviClimbList *self, uint32_t idx, float total_dist, NaviClimbData **output)
{
    if (self == NULL || output == NULL)
    {
        return -1;
    }

    return navi_climb_list_get(self, idx, total_dist, tclm_pos_climb_read, output);
}

//获取指定下坡数据
int navi_neg_climb_list_get(NaviClimbList *self, uint32_t idx, float total_dist, NaviClimbData **output)
{
    if (self == NULL || output == NULL)
    {
        return -1;
    }

    return navi_climb_list_get(self, idx, total_dist, tclm_neg_climb_read, output);
}

//复制爬坡点数据
void navi_route_cp_copy(NaviClimbpoint *self, const NaviClimbpoint *cp)
{
    if (self != NULL && cp != NULL)
    {
        memcpy(self, cp, sizeof(NaviClimbpoint));
    }
}

//更新爬坡点数据
void navi_route_cp_update(NaviClimbpoint *self, float dist, float alt)
{
    if (self != NULL)
    {
        self->dist = dist;
        self->alt = alt;
    }
}

//复制导航线路爬坡点分段
void navi_route_cp_segment_copy(NaviRouteCpSegment *self, const NaviRouteCpSegment *segment)
{
    if (self != NULL && segment != NULL)
    {
        memcpy(self, segment, sizeof(NaviRouteCpSegment));
    }
}

//复制爬坡分段
void navi_climb_segment_copy(NaviClimbSegment *self, const NaviClimbSegment *segment)
{
    if (self != NULL && segment != NULL)
    {
        memcpy(self, segment, sizeof(NaviClimbSegment));
    }
}

//重置线路爬坡点缓冲
void navi_route_cp_buf_reset(NaviRouteCpBuf *self)
{
    if (self != NULL)
    {
        self->range.start = 0;
        self->range.end = 0;
    }
}

//重置爬坡数据缓冲
void navi_climb_data_buf_reset(NaviClimbDataBuf *self)
{
    if (self != NULL)
    {
        self->range.start = 0;
        self->range.end = 0;
    }
}

//重置爬坡数据缓存
void navi_climb_data_cache_reset(NaviClimbDataCache *self)
{
    if (self != NULL)
    {
        self->len = 0;
        self->next = 0;
        for (uint32_t i = 0; i < self->capacity; i++)
        {
            navi_climb_data_buf_reset(self->cd_buf + i);
        }
    }
}

//重置爬坡list
void navi_climb_list_reset(NaviClimbList *self)
{
    if (self != NULL)
    {
        navi_climb_data_cache_reset(&self->cache);
    }
}

//重置线路爬坡点缓存
void navi_route_cp_cache_reset(NaviRouteCpCache *self)
{
    if (self != NULL)
    {
        self->len = 0;
        self->next = 0;

        for (uint32_t i = 0; i < self->capacity; i++)
        {
            navi_route_cp_buf_reset(self->cp_buf + i);
        }
    }
}

//重置线路爬坡点list
void navi_route_cp_list_reset(NaviRouteCpList *self)
{
    if (self != NULL)
    {
        navi_route_cp_cache_reset(&self->cache);
    }
}
