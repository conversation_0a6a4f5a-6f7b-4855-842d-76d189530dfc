﻿/**
 * @file algo_service_grade_avg.c
 * <AUTHOR> (<EMAIL>)
 * @brief 平均坡度算法组件实现
 * @version 0.1
 * @date 2024-12-09
 *
 * @copyright Copyright (c) 2024-2025, <PERSON>han <PERSON>wu Technology Co., Ltd
 *
 */
#include "algo_service_grade_avg.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_task.h"
#include "max_min.h"
#include "qw_time_util.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"
#include "alg_xscent.h"
#include "service_datetime.h"
#include "data_check.h"

// 输入数据
typedef struct
{
    int16_t grade;            //坡度,  100 * %
    int16_t vertical_speed;   //垂直速度,  1000 * m/s,
    int16_t altitude;         //海拔,  100 * m
    int16_t diff_altitude;    //本次计算的两个点的高度差  100 * m
    int32_t diff_distance;    //本次的距离差  100 * m
    uint32_t distance;
    uint32_t distance_lap;
    saving_status_e saving_status;   //数据记录的状态
} algo_grade_avg_sub_t;

static algo_grade_avg_sub_t s_algo_in = {0};

// 发布数据
static algo_grade_avg_pub_t s_algo_out = {0};

// 中间数据
static XscentCalculator session_xscent_calculator = { 0 };
static XscentCalculator lap_xscent_calculator = { 0 };

// 中间数据
static max_min_uint16_t s_max_min_pos = {0};
static max_min_uint16_t s_max_min_neg = {0};
static max_min_uint16_t s_max_min_pos_lap = {0};
static max_min_uint16_t s_max_min_neg_lap = {0};
static max_min_uint16_t s_max_min_pos_vspd = {0};
static max_min_uint16_t s_max_min_neg_vspd = {0};
static max_min_uint16_t s_max_min_pos_vspd_lap = {0};
static max_min_uint16_t s_max_min_neg_vspd_lap = {0};
static int32_t s_total_ascent = 0;                    //总升，cm。用于计算平均坡度，不等于输出的总升总降
static int32_t s_total_descent = 0;                   //总降，cm。用于计算平均坡度，不等于输出的总升总降
static int32_t s_total_ascent_lap = 0;                //圈总升，cm。用于计算平均坡度，不等于输出的总升总降
static int32_t s_total_descent_lap = 0;               //圈总降，cm。用于计算平均坡度，不等于输出的总升总降
static int16_t s_first_valid_altitude = 0x7fff;       //第一个有效海拔, cm
static int16_t s_last_valid_altitude = 0x7fff;        //最后一个有效海拔, cm
static int16_t s_first_valid_altitude_lap = 0x7fff;   //圈第一个有效海拔, cm
static int16_t s_last_valid_altitude_lap = 0x7fff;    //圈最后一个有效海拔, cm

// 本算法打开标记
static bool s_is_open = false;

/**
 * @brief 算法处理
 *
 * @param algo_out 输出数据
 * @param algo_in 输入数据
 */
static void algo_grade_avg_deal(algo_grade_avg_pub_t *algo_out, const algo_grade_avg_sub_t *algo_in)
{
    int32_t tmp = 0;
    uint16_t rc_dist_delta = 0;

    if (enum_status_saving == algo_in->saving_status)
    {
        //总升&总降
        XscentCalcInput xscent_calc_input_session = { 0 };
        XscentCalcInput xscent_calc_input_lap = { 0 };
        xscent_calc_input_session.timestamp = service_datetime_get_fit_time();
        xscent_calc_input_lap.timestamp = service_datetime_get_fit_time();
        xscent_calc_input_session.dist = (float)algo_in->distance / 100.0f;
        xscent_calc_input_lap.dist = (float)algo_in->distance_lap / 100.0f;

        //检查海拔是否有效，该数据项仅在海拔校准后生效，否则为无效值（0x7fffffff）
        if (altitude_check(algo_in->altitude) == true)
        {
            xscent_calc_input_session.alt = (float)algo_in->altitude / 100.0f;
            xscent_calc_input_lap.alt = (float)algo_in->altitude / 100.0f;
        }
        else
        {
            //规定海拔小于-999.0m为无效值
            xscent_calc_input_session.alt = -1000.0f;
            xscent_calc_input_lap.alt = -1000.0f;
        }

        xscent_calculator_exec(&session_xscent_calculator, &xscent_calc_input_session);
        xscent_calculator_exec(&lap_xscent_calculator, &xscent_calc_input_lap);

        XscentCalcData xscent_calc_data_session = { 0 };
        XscentCalcData xscent_calc_data_lap = { 0 };
        xscent_calculator_data_get(&session_xscent_calculator, &xscent_calc_data_session);
        xscent_calculator_data_get(&lap_xscent_calculator, &xscent_calc_data_lap);

        algo_out->total_ascent = (uint16_t)xscent_calc_data_session.ascent;
        algo_out->total_descent = (uint16_t)-xscent_calc_data_session.descent;
        algo_out->total_ascent_lap = (uint16_t)xscent_calc_data_lap.ascent;
        algo_out->total_descent_lap = (uint16_t)-xscent_calc_data_lap.descent;

        //平均坡度
        if (0x7fff != algo_in->grade)
        {
            // 最大上下坡坡度
            if (0 < algo_in->grade)   //上坡
            {
                max_min_uint16_update(&s_max_min_pos, (uint16_t) algo_in->grade);
                algo_out->max_pos_grade = (int16_t) s_max_min_pos.max;
                max_min_uint16_update(&s_max_min_pos_lap, (uint16_t) algo_in->grade);
                algo_out->max_pos_grade_lap = (int16_t) s_max_min_pos_lap.max;
            }
            else if (0 > algo_in->grade)   //下坡
            {
                max_min_uint16_update(&s_max_min_neg, (uint16_t) (algo_in->grade * (-1)));
                algo_out->max_neg_grade = (int16_t) s_max_min_neg.max * (-1);
                max_min_uint16_update(&s_max_min_neg_lap, (uint16_t) (algo_in->grade * (-1)));
                algo_out->max_neg_grade_lap = (int16_t) s_max_min_neg_lap.max * (-1);
            }
        }

        if (0x7fff != algo_in->diff_altitude && 0x7fffffff != algo_in->diff_distance)
        {
            // 总升总降
            if (0 < algo_in->diff_altitude)                                  //上坡
            {
                s_total_ascent += algo_in->diff_altitude;                    //累计总升高度
                algo_out->uphill_distance += algo_in->diff_distance;         //累计上坡距离
                s_total_ascent_lap += algo_in->diff_altitude;                //累计总升高度
                algo_out->uphill_distance_lap += algo_in->diff_distance;     //累计上坡距离
            }
            else if (0 > algo_in->diff_altitude)                             //下坡
            {
                s_total_descent += algo_in->diff_altitude;                   //累计总降高度
                algo_out->downhill_distance += algo_in->diff_distance;       //累计下坡距离
                s_total_descent_lap += algo_in->diff_altitude;               //累计总降高度
                algo_out->downhill_distance_lap += algo_in->diff_distance;   //累计下坡距离
            }

            //使用总升高度和上坡距离计算平均坡度
            //上坡平均坡度
            if (0 != algo_out->uphill_distance)
            {
                tmp = (int64_t) s_total_ascent * 10000 / algo_out->uphill_distance;   //平均上坡坡度= 上坡距离/总升高度  100*%

                //平均坡度大于最大坡度，则用此时上坡高度变化量/最大坡度，反推出一个上坡距离变化量
                //用这个反推的上坡距离变化量代替实际的上坡距离变化量，进而避免平均坡度大于最大坡度的问题
                if (tmp > algo_out->max_pos_grade)
                {
                    if (0 == algo_out->max_pos_grade)
                    {
                        algo_out->max_pos_grade = (int16_t) tmp;
                    }
                    else
                    {
                        rc_dist_delta = algo_in->diff_altitude * 10000 / algo_out->max_pos_grade;   //重算上坡距离变化量

                        if (rc_dist_delta > algo_in->diff_distance)
                        {
                            algo_out->uphill_distance += (rc_dist_delta - algo_in->diff_distance);
                        }

                        tmp = (int64_t) s_total_ascent * 10000 / algo_out->uphill_distance;
                    }
                }

                algo_out->avg_pos_grade = (int16_t) tmp;
            }
            else
            {
                algo_out->avg_pos_grade = 0;
            }

            if (0 != algo_out->uphill_distance_lap)
            {
                tmp = (int64_t) s_total_ascent_lap * 10000 / algo_out->uphill_distance_lap;   //平均上坡坡度= 上坡距离/总升高度  100*%

                //平均坡度大于最大坡度，则用此时上坡高度变化量/最大坡度，反推出一个上坡距离变化量
                //用这个反推的上坡距离变化量代替实际的上坡距离变化量，进而避免平均坡度大于最大坡度的问题
                if (tmp > algo_out->max_pos_grade_lap)
                {
                    if (0 == algo_out->max_pos_grade_lap)
                    {
                        algo_out->max_pos_grade_lap = (int16_t) tmp;
                    }
                    else
                    {
                        rc_dist_delta = algo_in->diff_altitude * 10000 / algo_out->max_pos_grade_lap;   //重算上坡距离变化量

                        if (rc_dist_delta > algo_in->diff_distance)
                        {
                            algo_out->uphill_distance_lap += (rc_dist_delta - algo_in->diff_distance);
                        }

                        tmp = (int64_t) s_total_ascent_lap * 10000 / algo_out->uphill_distance_lap;
                    }
                }

                algo_out->avg_pos_grade_lap = (int16_t) tmp;
            }
            else
            {
                algo_out->avg_pos_grade_lap = 0;
            }

            //下坡平均坡度
            if (0 != algo_out->downhill_distance)
            {
                tmp = (int64_t) s_total_descent * 10000 / algo_out->downhill_distance;   //平均下坡坡度= 下坡距离/总降高度  100%

                if (tmp < algo_out->max_neg_grade)
                {
                    algo_out->max_neg_grade = (int16_t) tmp;
                }

                algo_out->avg_neg_grade = (int16_t) tmp;
            }
            else
            {
                algo_out->avg_neg_grade = 0;
            }

            if (0 != algo_out->downhill_distance_lap)
            {
                tmp = (int64_t) s_total_descent_lap * 10000 / algo_out->downhill_distance_lap;   //平均下坡坡度= 下坡距离/总降高度  100%

                if (tmp < algo_out->max_neg_grade_lap)
                {
                    algo_out->max_neg_grade_lap = (int16_t) tmp;
                }

                algo_out->avg_neg_grade_lap = (int16_t) tmp;
            }
            else
            {
                algo_out->avg_neg_grade_lap = 0;
            }

            // 总升总降使用新的算法，这里不再赋值
            // algo_out->total_ascent = (uint16_t) (abs((s_total_ascent / 100)));             // m
            // algo_out->total_descent = (uint16_t) (abs((s_total_descent / 100)));           // m
            // algo_out->total_ascent_lap = (uint16_t) (abs((s_total_ascent_lap / 100)));     // m
            // algo_out->total_descent_lap = (uint16_t) (abs((s_total_descent_lap / 100)));   // m

            //平均坡度过大的情况
            if (algo_out->avg_pos_grade > algo_out->max_pos_grade)
            {
                algo_out->avg_pos_grade = algo_out->max_pos_grade;
            }

            if (algo_out->avg_neg_grade < algo_out->max_neg_grade)
            {
                algo_out->avg_neg_grade = algo_out->max_neg_grade;
            }

            if (algo_out->avg_pos_grade_lap > algo_out->max_pos_grade_lap)
            {
                algo_out->avg_pos_grade_lap = algo_out->max_pos_grade_lap;
            }

            if (algo_out->avg_neg_grade_lap < algo_out->max_neg_grade_lap)
            {
                algo_out->avg_neg_grade_lap = algo_out->max_neg_grade_lap;
            }
        }

        //平均垂直速度
        if (0x7fff != algo_in->vertical_speed)
        {
            if (0 < algo_in->vertical_speed)
            {
                max_min_uint16_update(&s_max_min_pos_vspd, (uint16_t) algo_in->vertical_speed);
                algo_out->avg_pos_vertical_speed = (int16_t) s_max_min_pos_vspd.avg;
                algo_out->max_pos_vertical_speed = (int16_t) s_max_min_pos_vspd.max;
                max_min_uint16_update(&s_max_min_pos_vspd_lap, (uint16_t) algo_in->vertical_speed);
                algo_out->avg_pos_vertical_speed_lap = (int16_t) s_max_min_pos_vspd_lap.avg;
                algo_out->max_pos_vertical_speed_lap = (int16_t) s_max_min_pos_vspd_lap.max;
            }
            else if (0 > algo_in->vertical_speed)
            {
                max_min_uint16_update(&s_max_min_neg_vspd, (uint16_t) (algo_in->vertical_speed * (-1)));
                algo_out->avg_neg_vertical_speed = (int16_t) s_max_min_neg_vspd.avg * (-1);
                algo_out->max_neg_vertical_speed = (int16_t) s_max_min_neg_vspd.max * (-1);
                max_min_uint16_update(&s_max_min_neg_vspd_lap, (uint16_t) (algo_in->vertical_speed * (-1)));
                algo_out->avg_neg_vertical_speed_lap = (int16_t) s_max_min_neg_vspd_lap.avg * (-1);
                algo_out->max_neg_vertical_speed_lap = (int16_t) s_max_min_neg_vspd_lap.max * (-1);
            }
        }

        //总平均坡度
        if (0x7fff != algo_in->altitude)
        {
            if (0x7fff == s_first_valid_altitude)
            {
                s_first_valid_altitude = algo_in->altitude;   //记录第一个有效海拔
            }

            if (0x7fff == s_first_valid_altitude_lap)
            {
                s_first_valid_altitude_lap = algo_in->altitude;   //记录第一个有效海拔
            }

            s_last_valid_altitude = algo_in->altitude;       //记录最后一个有效海拔
            s_last_valid_altitude_lap = algo_in->altitude;   //记录最后一个有效海拔
        }

        if (0 != algo_in->distance && 0xffffffff != algo_in->distance && 0x7fff != s_first_valid_altitude && 0x7fff != s_last_valid_altitude)
        {
            algo_out->avg_grade = (int16_t) ((int64_t) (s_last_valid_altitude - s_first_valid_altitude) * 10000 / (int32_t) algo_in->distance);
        }

        if (0 != algo_in->distance_lap && 0xffffffff != algo_in->distance_lap && 0x7fff != s_first_valid_altitude_lap && 0x7fff != s_last_valid_altitude_lap)
        {
            algo_out->avg_grade_lap = (int16_t) ((int64_t) (s_last_valid_altitude_lap - s_first_valid_altitude_lap) * 10000 / (int32_t) algo_in->distance_lap);
        }
    }
}

/**
 * @brief 算法控制
 *
 * @param algo_out 输出数据
 * @param ctrl_type 控制类型
 */
static void algo_grade_avg_ctrl(algo_grade_avg_pub_t *algo_out, ctrl_type_e ctrl_type)
{
    if (enum_ctrl_start == ctrl_type)
    {
        //输出数据初始化
        memset(algo_out, 0, sizeof(algo_grade_avg_pub_t));

        //中间数据初始化
        xscent_calculator_reset(&session_xscent_calculator);
        xscent_calculator_reset(&lap_xscent_calculator);
        max_min_uint16_init(&s_max_min_pos);
        max_min_uint16_init(&s_max_min_neg);
        max_min_uint16_init(&s_max_min_pos_lap);
        max_min_uint16_init(&s_max_min_neg_lap);
        max_min_uint16_init(&s_max_min_pos_vspd);
        max_min_uint16_init(&s_max_min_neg_vspd);
        max_min_uint16_init(&s_max_min_pos_vspd_lap);
        max_min_uint16_init(&s_max_min_neg_vspd_lap);
        s_total_ascent = 0;
        s_total_descent = 0;
        s_total_ascent_lap = 0;
        s_total_descent_lap = 0;
        s_first_valid_altitude = 0x7fff;
        s_last_valid_altitude = 0x7fff;
        s_first_valid_altitude_lap = 0x7fff;
        s_last_valid_altitude_lap = 0x7fff;
    }
    else if (enum_ctrl_lap == ctrl_type)
    {
        //复制到前圈
        algo_out->avg_grade_pre_lap = algo_out->avg_grade_lap;
        algo_out->avg_pos_grade_pre_lap = algo_out->avg_pos_grade_lap;
        algo_out->avg_neg_grade_pre_lap = algo_out->avg_neg_grade_lap;
        algo_out->max_pos_grade_pre_lap = algo_out->max_pos_grade_lap;
        algo_out->max_neg_grade_pre_lap = algo_out->max_neg_grade_lap;
        algo_out->avg_pos_vertical_speed_pre_lap = algo_out->avg_pos_vertical_speed_lap;
        algo_out->avg_neg_vertical_speed_pre_lap = algo_out->avg_neg_vertical_speed_lap;
        algo_out->max_pos_vertical_speed_pre_lap = algo_out->max_pos_vertical_speed_lap;
        algo_out->max_neg_vertical_speed_pre_lap = algo_out->max_neg_vertical_speed_lap;
        algo_out->total_ascent_pre_lap = algo_out->total_ascent_lap;
        algo_out->total_descent_pre_lap = algo_out->total_descent_lap;
        algo_out->uphill_distance_pre_lap = algo_out->uphill_distance_lap;
        algo_out->downhill_distance_pre_lap = algo_out->downhill_distance_lap;

        //圈输出数据初始化
        algo_out->avg_grade_lap = 0;
        algo_out->avg_pos_grade_lap = 0;
        algo_out->avg_neg_grade_lap = 0;
        algo_out->max_pos_grade_lap = 0;
        algo_out->max_neg_grade_lap = 0;
        algo_out->avg_pos_vertical_speed_lap = 0;
        algo_out->avg_neg_vertical_speed_lap = 0;
        algo_out->max_pos_vertical_speed_lap = 0;
        algo_out->max_neg_vertical_speed_lap = 0;
        algo_out->total_ascent_lap = 0;
        algo_out->total_descent_lap = 0;
        algo_out->uphill_distance_lap = 0;
        algo_out->downhill_distance_lap = 0;

        //圈中间数据初始化
        xscent_calculator_reset(&lap_xscent_calculator);
        max_min_uint16_init(&s_max_min_pos_lap);
        max_min_uint16_init(&s_max_min_neg_lap);
        max_min_uint16_init(&s_max_min_pos_vspd_lap);
        max_min_uint16_init(&s_max_min_neg_vspd_lap);
        s_total_ascent_lap = 0;
        s_total_descent_lap = 0;
        s_first_valid_altitude_lap = 0x7fff;
        s_last_valid_altitude_lap = 0x7fff;
    }
}

/**
 * @brief 算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_grade_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_GRADE_AVG;
    head.input_type = DATA_ID_ALGO_GRADE;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_altitude_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_GRADE_AVG;
    head.input_type = DATA_ID_ALGO_ALTITUDE;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_distance_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_GRADE_AVG;
    head.input_type = DATA_ID_ALGO_DISTANCE;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_speed_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_GRADE_AVG;
    head.input_type = DATA_ID_ALGO_SPEED;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法控制订阅处理
 *
 * @param in 控制数据
 * @param len 数据长度
 */
static void algo_sports_ctrl_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_GRADE_AVG;
    head.input_type = DATA_ID_EVENT_SPORTS_CTRL;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_grade_avg_out_callback(const void *out, uint32_t len)
{
    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_ALGO_GRADE_AVG, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("%s publish error", __FUNCTION__);
    }
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_grade",
        .topic_id = DATA_ID_ALGO_GRADE,
        .callback = algo_grade_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_altitude",
        .topic_id = DATA_ID_ALGO_ALTITUDE,
        .callback = algo_altitude_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_distance",
        .topic_id = DATA_ID_ALGO_DISTANCE,
        .callback = algo_distance_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_speed",
        .topic_id = DATA_ID_ALGO_SPEED,
        .callback = algo_speed_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
};

/**
 * @brief 算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_grade_avg_init(void)
{
    return 0;
}

/**
 * @brief 算法open
 *
 * @return int32_t 结果
 */
static int32_t algo_grade_avg_open(void)
{
    if (s_is_open)
    {
        ALGO_COMP_LOG_W("%s already open", __FUNCTION__);
        return 0;
    }

    ALGO_COMP_LOG_D("%s open", __FUNCTION__);

    s_is_open = true;
    memset(&s_algo_out, 0, sizeof(algo_grade_avg_pub_t));

    //中间数据初始化
    xscent_calculator_reset(&session_xscent_calculator);
    xscent_calculator_reset(&lap_xscent_calculator);
    max_min_uint16_init(&s_max_min_pos);
    max_min_uint16_init(&s_max_min_neg);
    max_min_uint16_init(&s_max_min_pos_lap);
    max_min_uint16_init(&s_max_min_neg_lap);
    max_min_uint16_init(&s_max_min_pos_vspd);
    max_min_uint16_init(&s_max_min_neg_vspd);
    max_min_uint16_init(&s_max_min_pos_vspd_lap);
    max_min_uint16_init(&s_max_min_neg_vspd_lap);
    s_total_ascent = 0;
    s_total_descent = 0;
    s_total_ascent_lap = 0;
    s_total_descent_lap = 0;
    s_first_valid_altitude = 0x7fff;
    s_last_valid_altitude = 0x7fff;
    s_first_valid_altitude_lap = 0x7fff;
    s_last_valid_altitude_lap = 0x7fff;

    return algo_topic_list_subscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
}

/**
 * @brief 算法feed
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_grade_avg_feed(uint32_t input_type, void *data, uint32_t len)
{
    algo_grade_avg_sub_t *algo_in = &s_algo_in;
    algo_grade_avg_pub_t *algo_out = &s_algo_out;

    switch (input_type)
    {
    case DATA_ID_ALGO_GRADE:
    {
        const algo_grade_pub_t *grade_data = (algo_grade_pub_t *) data;
        algo_in->grade = grade_data->grade;
        algo_in->vertical_speed = grade_data->vertical_speed;
        algo_in->diff_altitude = grade_data->diff_altitude;
    }
    break;
    case DATA_ID_ALGO_ALTITUDE:
    {
        const algo_altitude_pub_t *altitude_data = (algo_altitude_pub_t *) data;
        algo_in->altitude = altitude_data->altitude;
    }
    break;
    case DATA_ID_ALGO_DISTANCE:
    {
        const algo_distance_pub_t *distance_data = (algo_distance_pub_t *) data;
        algo_in->distance = distance_data->distance;
        algo_in->distance_lap = distance_data->distance_lap;
    }
    break;
    case DATA_ID_ALGO_SPEED:
    {
        const algo_speed_pub_t *speed_data = (algo_speed_pub_t *) data;
        algo_in->diff_distance = (int32_t) speed_data->distance_delta;
    }
    break;
    case DATA_ID_EVENT_SPORTS_CTRL:
    {
        const algo_sports_ctrl_t *sports_ctrl = (algo_sports_ctrl_t *) data;
        algo_in->saving_status = sports_ctrl->saving_status;

        //算法控制
        algo_grade_avg_ctrl(algo_out, sports_ctrl->ctrl_type);

        //数据发布
        if (sports_ctrl->ctrl_type == enum_ctrl_null)
        {
            //算法处理
            algo_grade_avg_deal(algo_out, algo_in);

            //数据发布
            algo_grade_avg_out_callback(algo_out, sizeof(algo_grade_avg_pub_t));
        }
        else if (sports_ctrl->ctrl_type == enum_ctrl_lap)
        {
            //记圈后马上数据发布
            algo_grade_avg_out_callback(algo_out, sizeof(algo_grade_avg_pub_t));
        }
    }
    break;
    default:
        break;
    }
    return 0;
}

/**
 * @brief 算法close
 *
 * @return int32_t 结果
 */
static int32_t algo_grade_avg_close(void)
{
    if (!s_is_open)
    {
        ALGO_COMP_LOG_W("%s already close", __FUNCTION__);
        return 0;
    }

    algo_topic_list_unsubscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));

    s_is_open = false;
    ALGO_COMP_LOG_D("%s close", __FUNCTION__);
    return 0;
}

// 算法组件实现
static algo_compent_ops_t s_grade_avg_algo = {
    .init = algo_grade_avg_init,
    .open = algo_grade_avg_open,
    .feed = algo_grade_avg_feed,
    .close = algo_grade_avg_close,
    .ioctl = NULL,
};

/**
 * @brief 组件注册
 *
 * @return int32_t 结果
 */
int32_t register_grade_avg_algo(void)
{
    algo_compnent_register(ALGO_TYPE_GRADE_AVG, &s_grade_avg_algo);
    return 0;
}