/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   cfg_gps.c
@Time    :   2025/01/07 11:32:04
*
**************************************************************************/

#include "cfg_gps.h"
#include "cfg_log.h"
#include "qw_general.h"
#include "kvdb.h"
#include "qw_gps.h"
#include "igs_dev_config.h"
#include "qw_system_params.h"
#include <string.h>

static qw_dev_cfg_t* p_cfg = NULL;

static bool cfg_gps_encode(uint8_t* encode_data, uint32_t encode_data_len,
	uint8_t* encode_buf, uint32_t encode_buf_size, uint32_t* encode_ret_size)
{
    memcpy(encode_buf, encode_data, encode_data_len);
	*encode_ret_size = encode_data_len;
	return true;
}

static bool cfg_gps_decode(uint8_t* decode_data, uint32_t decode_data_len,
	uint8_t* decode_buf, uint32_t decode_buf_len)
{
    cfg_gps_t temp = {0};
    memcpy(&temp, decode_data, decode_data_len > sizeof(cfg_gps_t) ? sizeof(cfg_gps_t) : decode_data_len);

    if (p_cfg->firmware_update.cfg_version != SYS_CONFIG_VERSION)
    {
        cfg_gps_default(&temp);
    }
    else
    {
        if (temp.gps_prefer_mode >= _enum_gps_prefer_max)
        {
            temp.gps_prefer_mode = enum_gps_prefer_auto;
        }
        if (temp.user_define_mode >= _enum_gps_prefer_max)
        {
            temp.user_define_mode = enum_gps_prefer_auto;
        }
        if (temp.user_define_band > enum_gps_band_l1l5)
        {
            temp.user_define_band = enum_gps_band_l1;
        }
        if (temp.user_define_mode >= enum_gps_mode_end)
        {
            temp.user_define_mode = enum_gps_mode_2;
        }
    }

    // set_epo_valid_time(temp.agps_get_time);
    memcpy(decode_buf, &temp, sizeof(cfg_gps_t));
    return true;
}

static bool cfg_gps_default_config(void* value, uint32_t value_size)
{
    if (value == NULL || value_size == 0) {
        return false;
    }

    cfg_gps_default(&p_cfg->gps);
    memcpy(value, s_cfg_store_table[enum_cfg_gps].dat, value_size);
    return true;
}

static const kv_node_config cfg_gps_config = {
    .encode = cfg_gps_encode,
    .decode = cfg_gps_decode,
    .default_config = cfg_gps_default_config,
};

static int cfg_gps_init(void* handler)
{
    p_cfg = get_cfg();
    
    void* data_ptr = NULL;
    const char* key = s_cfg_store_table[enum_cfg_gps].name;
    void *data_addr = s_cfg_store_table[enum_cfg_gps].dat;
    uint32_t size = s_cfg_store_table[enum_cfg_gps].len * s_cfg_store_table[enum_cfg_gps].num;

    kvlist_node_open(handler, key, &cfg_gps_config, size, data_addr);
    cfg_mark_reset(enum_cfg_gps);
    return 0;
}
INIT_FS_KV_EXPORT(cfg_gps_init); 

/**
 * @brief 初始化
 * @param cfg 参数指针
 */
void cfg_gps_default(cfg_gps_t* cfg)
{
    if (cfg != NULL)
    {
        memset(cfg, 0, sizeof(cfg_gps_t));
        cfg->gps_prefer_mode = enum_gps_prefer_precise;
        cfg->user_define_mode = enum_gps_prefer_auto;
        cfg->user_define_band = enum_gps_band_l1;
        cfg->user_define_mode = enum_gps_mode_2;

        CFG_SETTING_LOG_I("cfg_gps_t reset default, version:%d->%d", 
            p_cfg->firmware_update.cfg_version, SYS_CONFIG_VERSION);
    }
    cfg_mark_reset(enum_cfg_gps);
}

/**
 * @brief 获取当前gps策略
 * @return gps策略
 */
gps_prefer_mode_e cfg_get_gps_prefer_mode(void)
{
    return (gps_prefer_mode_e)p_cfg->gps.gps_prefer_mode;
}

/**
 * @brief 设置gps策略
 * @param gps策略
 */
void cfg_set_gps_prefer_mode(gps_prefer_mode_e mode)
{
    if(mode < _enum_gps_prefer_max) 
    {
        p_cfg->gps.gps_prefer_mode = mode;
        cfg_mark_update(enum_cfg_gps);
    }
}

/**
 * @brief gps获取时间
 * @return gps获取时间
 */
uint32_t get_cfg_gps_get_time(void)
{
    return p_cfg->gps.gps_get_time;
}

/**
 * @brief 设置gps获取时间
 * @param time gps策略
 */
void set_cfg_gps_get_time(uint32_t gps_time)
{
    p_cfg->gps.gps_get_time = gps_time;
    cfg_mark_update(enum_cfg_gps);
}

/**
 * @brief agps获取时间
 * @return agps获取时间
 */
uint32_t get_cfg_agps_get_time(void)
{
    return p_cfg->gps.agps_get_time;
}

/**
 * @brief 设置agps获取时间
 * @param time agps策略
 */
void set_cfg_agps_get_time(uint32_t gps_time)
{
    p_cfg->gps.agps_get_time = gps_time;
    cfg_mark_update(enum_cfg_gps);
}

/**
 * @brief gps最近地址有效性
 * @param lat 纬度
 * @param lon 经度
 */
void get_cfg_gps_backup_position(struct minmea_float* lat, struct minmea_float* lon)
{
    if (lat != NULL && lon != NULL)
    {
        memcpy(lat, &p_cfg->gps.lat, sizeof(struct minmea_float));
        memcpy(lon, &p_cfg->gps.lon, sizeof(struct minmea_float));
    }
}

/**
 * @brief gps最近地址有效性
 * @param lat 纬度
 * @param lon 经度
 */
void set_cfg_gps_backup_position(struct minmea_float* lat, struct minmea_float* lon)
{
    if (lat != NULL && lon != NULL)
    {
        memcpy(&p_cfg->gps.lat, lat, sizeof(struct minmea_float));
        memcpy(&p_cfg->gps.lon, lon, sizeof(struct minmea_float));
        cfg_mark_update(enum_cfg_gps);
    }
}

/**
 * @brief gps最近地址有效性
 * @return 有效性
 */
bool cfg_gps_backup_position_invalid(void)
{
    return (position_invalid_check(&p_cfg->gps.lat) || position_invalid_check(&p_cfg->gps.lon));
}