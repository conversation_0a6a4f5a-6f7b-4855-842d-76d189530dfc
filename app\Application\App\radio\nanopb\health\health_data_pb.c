/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   health_data_pb.c
@Time    :   2025/03/19 16:30:00
*
**************************************************************************/

#include "health_data_pb.h"
#include "watch_health_data.pb.h"
#include "watch_health_file.pb.h"
#include "pb.h"
#include "pb_encode.h"
#include "pb_decode.h"
#include "pb_encode_common.h"
#include "pb_decode_common.h"
#include "ble_nus_srv.h"
#include "service_daily_activity.h"
#include "crc8.h"
#include "ble_cmd_response.h"
#include "thread_proto_service.h"
#include "qw_fs.h"
#include "service_config.h"
#include "service_health_proto_sync.h"
#include "cfg_header_def.h"
#include "ble_service_data_sync.h"
#include "hydration_remind.h"
#include "data_sync_remind/data_sync_remind.h"

#define HEALTH_PB_LBL             LOG_LVL_DBG
#define HEALTH_PB_TAG             "HEALTH_PB"

#if (HEALTH_PB_LBL >= LOG_LVL_DBG)
    #define HEALTH_PB_LOG_D(...)        QW_LOG_D(HEALTH_PB_TAG, __VA_ARGS__)
#else
    #define HEALTH_PB_LOG_D(...)
#endif

#if (HEALTH_PB_LBL >= LOG_LVL_INFO)
    #define HEALTH_PB_LOG_I(...)        QW_LOG_I(HEALTH_PB_TAG, __VA_ARGS__)
#else
    #define HEALTH_PB_LOG_I(...)
#endif

#if (HEALTH_PB_LBL >= LOG_LVL_WARNING)
    #define HEALTH_PB_LOG_W(...)        QW_LOG_W(HEALTH_PB_TAG, __VA_ARGS__)
#else
    #define HEALTH_PB_LOG_W(...)
#endif

#if (HEALTH_PB_LBL >= LOG_LVL_ERROR)
    #define HEALTH_PB_LOG_E(...)        QW_LOG_E(HEALTH_PB_TAG, __VA_ARGS__)
#else
    #define HEALTH_PB_LOG_E(...)
#endif

#define HEALTH_FILE_LENGTH_MAX SIZE_MAX

static uint8_t health_data_index = 0;          //健康数据索引
static uint8_t health_target_index = 0;        //健康目标索引
static uint8_t health_alarm_index = 0;         //健康闹钟索引
static uint8_t health_file_list_index = 0;     //健康文件索引

static uint8_t open_flag = false;              //文件打开状态
static uint8_t trans_cmp = false;              //文件传输完成状态
static uint16_t read_file_len = 0;
static QW_FIL *fdst;
static uint32_t health_file_data_size = 0;
//static sync_dly_type_t daily_activity_data;
static uint32_t file_sync_start_time[PROTO_HEALTH_DATA_SYNC_FULL] = {0};

static uint8_t file_sync_type = HEALTH_RECORD_INDEX_MAX;    //文件同步类型


// 获取文件同步开始时间
uint32_t file_sync_start_time_get(HEALTH_DATA_SYNC_E health_data_sync_type)
{
    return file_sync_start_time[health_data_sync_type];
}

// 设置文件同步开始时间
void file_sync_start_time_set(HEALTH_DATA_SYNC_E health_data_sync_type, uint32_t start_time)
{
    file_sync_start_time[health_data_sync_type] = start_time;
}

static bool daily_target_message_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    uint8_t status = false;
    watch_daily_target_message *daily_target_message_st = (watch_daily_target_message *)*arg;

    for (uint8_t i = 0; i < _WATCH_DAILY_ACTIVITY_TYPE_MAX; i++)
    {
        daily_target_message_st[i].daily_activity_type = i + 1;
        switch (daily_target_message_st[i].daily_activity_type)
        {
            case WATCH_DAILY_ACTIVITY_TYPE_WATCH_DAILY_ACTIVITY_TYPE_STEP:
            {
                daily_target_message_st[i].has_goal_value = true;
                daily_target_message_st[i].goal_value = daily_goal_get_step();
                daily_target_message_st[i].has_status = true;
                daily_target_message_st[i].status = daily_goal_switch_get_step();
            }
            break;

            case WATCH_DAILY_ACTIVITY_TYPE_WATCH_DAILY_ACTIVITY_TYPE_CALORIES:
            {
                daily_target_message_st[i].has_goal_value = true;
                daily_target_message_st[i].goal_value = daily_goal_get_cal();
                daily_target_message_st[i].has_status = true;
                daily_target_message_st[i].status = daily_goal_switch_get_cal();
            }
            break;

            case WATCH_DAILY_ACTIVITY_TYPE_WATCH_DAILY_ACTIVITY_TYPE_INTENSE_TIME:
            {
                daily_target_message_st[i].has_goal_value = true;
                daily_target_message_st[i].goal_value = daily_goal_get_intense_time();
                daily_target_message_st[i].has_status = true;
                daily_target_message_st[i].status = daily_goal_switch_get_intense_time();
            }
            break;

            case WATCH_DAILY_ACTIVITY_TYPE_WATCH_DAILY_ACTIVITY_TYPE_ACTIVE_HOUR:
            {
                daily_target_message_st[i].has_goal_value = true;
                daily_target_message_st[i].goal_value = daily_goal_get_active_hour();
            }
            break;

            default:
            {

            }
            break;
        }

        status = pb_encode_tag_for_field(stream, field);
        status &= pb_encode_submessage(stream, watch_daily_target_message_fields, &daily_target_message_st[i]);
    }

    return status;
}

static void health_history_sync_request(watch_health_data_message * health_data_msg)
{
    memset(file_sync_start_time, 0xff, sizeof(file_sync_start_time));

    for (uint8_t i = 0; i < health_data_index; i++)
    {
        switch (health_data_msg[i].health_data_type)
        {
            case WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_SLEEP:
            {
                if (health_data_msg[i].has_timestamp)
                {
                    file_sync_start_time_set(PROTO_HEALTH_DATA_SYNC_SLEEP, health_data_msg[i].timestamp);
                }
                else
                {
                    file_sync_start_time_set(PROTO_HEALTH_DATA_SYNC_SLEEP, 0);
                }

                if (health_data_index == 1)
                {
                    service_proto_health_sync_event_send(PROTO_HEALTH_DATA_SYNC_SLEEP);
                }
            }
            break;

            case WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_STRESS:
            {
                if (health_data_msg[i].has_timestamp)
                {
                    file_sync_start_time_set(PROTO_HEALTH_DATA_SYNC_STRESS, health_data_msg[i].timestamp);
                }
                else
                {
                    file_sync_start_time_set(PROTO_HEALTH_DATA_SYNC_STRESS, 0);
                }

                if (health_data_index == 1)
                {
                    service_proto_health_sync_event_send(PROTO_HEALTH_DATA_SYNC_STRESS);
                }
            }
            break;

            case WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_HEART_RATE:
            {
                if (health_data_msg[i].has_timestamp)
                {
                    file_sync_start_time_set(PROTO_HEALTH_DATA_SYNC_HR, health_data_msg[i].timestamp);
                }
                else
                {
                    file_sync_start_time_set(PROTO_HEALTH_DATA_SYNC_HR, 0);
                }

                if (health_data_index == 1)
                {
                    service_proto_health_sync_event_send(PROTO_HEALTH_DATA_SYNC_HR);
                }
            }
            break;

            case WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_SPO2:
            {
                if (health_data_msg[i].has_timestamp)
                {
                    file_sync_start_time_set(PROTO_HEALTH_DATA_SYNC_SPO2, health_data_msg[i].timestamp);
                }
                else
                {
                    file_sync_start_time_set(PROTO_HEALTH_DATA_SYNC_SPO2, 0);
                }

                if (health_data_index == 1)
                {
                    service_proto_health_sync_event_send(PROTO_HEALTH_DATA_SYNC_SPO2);
                }
            }
            break;

            case WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_HEART_RATE_VARIABILITY:
            {
                if (health_data_msg[i].has_timestamp)
                {
                    file_sync_start_time_set(PROTO_HEALTH_DATA_SYNC_HRV, health_data_msg[i].timestamp);
                }
                else
                {
                    file_sync_start_time_set(PROTO_HEALTH_DATA_SYNC_HRV, 0);
                }

                if (health_data_index == 1)
                {
                    service_proto_health_sync_event_send(PROTO_HEALTH_DATA_SYNC_HRV);
                }
            }
            break;

            case WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_DAILY_ACTIVITY:
            {
                if (health_data_msg[i].has_timestamp)
                {
                    file_sync_start_time_set(PROTO_HEALTH_DATA_SYNC_STEP, health_data_msg[i].timestamp);
                }
                else
                {
                    file_sync_start_time_set(PROTO_HEALTH_DATA_SYNC_STEP, 0);
                }

                if (health_data_index == 1)
                {
                    service_proto_health_sync_event_send(PROTO_HEALTH_DATA_SYNC_STEP);
                }
            }
            break;

            case WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_RESTING_HR:
            {
                if (health_data_msg[i].has_timestamp)
                {
                    file_sync_start_time_set(PROTO_HEALTH_DATA_SYNC_REST_HR, health_data_msg[i].timestamp);
                }
                else
                {
                    file_sync_start_time_set(PROTO_HEALTH_DATA_SYNC_REST_HR, 0);
                }

                if (health_data_index == 1)
                {
                    service_proto_health_sync_event_send(PROTO_HEALTH_DATA_SYNC_REST_HR);
                }
            }
            break;

            default:
            {

            }
            break;
        }
    }

    if (health_data_index > 1)
    {
        service_proto_health_sync_event_send(PROTO_HEALTH_DATA_SYNC_FULL);
    }
}
#if 0
#include <finsh.h>

static void health_sync_test(int argc, char* argv[])
{
    if (argc < 1) {
        return;
    }

    if (strcmp(argv[1], "step") == 0) {
        file_sync_start_time_set(PROTO_HEALTH_DATA_SYNC_STEP, 0);
        service_proto_health_sync_event_send(PROTO_HEALTH_DATA_SYNC_STEP);
    } else if (strcmp(argv[1], "hr") == 0) {
        file_sync_start_time_set(PROTO_HEALTH_DATA_SYNC_HR, 0);
        service_proto_health_sync_event_send(PROTO_HEALTH_DATA_SYNC_HR);
    } else if (strcmp(argv[1], "sleep") == 0) {

    } else if (strcmp(argv[1], "spo2") == 0) {
        file_sync_start_time_set(PROTO_HEALTH_DATA_SYNC_SPO2, 0);
        service_proto_health_sync_event_send(PROTO_HEALTH_DATA_SYNC_SPO2);
    } else if (strcmp(argv[1], "stress") == 0) {

    } else if (strcmp(argv[1], "hrv") == 0) {

    }
}

MSH_CMD_EXPORT(health_sync_test, health_test_sync_func);
#endif

static void daily_activity_target_send(void)
{
    watch_health_msg health_msg_st;
    watch_daily_target_message daily_target_message_st[_WATCH_DAILY_ACTIVITY_TYPE_ARRAYSIZE];
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    uint8_t pb_crc = 0;

    memset(&health_msg_st, 0, sizeof(watch_health_msg));
    memset(&daily_target_message_st, 0, sizeof(daily_target_message_st));

    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

    health_msg_st.daily_target_msg.arg = &daily_target_message_st;
    health_msg_st.daily_target_msg.funcs.encode = &daily_target_message_encode;

    health_msg_st.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_HEALTH;
    health_msg_st.health_data_operate_type = WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_DLY_ACT_TARGET_GET;
    health_msg_st.version = 1;

    //编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    //编码
    pb_encode(&encode_stream, watch_health_msg_fields, &health_msg_st);
    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch0();
    //对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);
    ble_cmd_end_tx(health_msg_st.service_type, 0, health_msg_st.health_data_operate_type, 0, encode_stream.bytes_written, pb_crc);

}

static void daily_activity_target_set(watch_daily_target_message* daily_target_message_st)
{
    for (uint8_t i = 0; i < health_target_index; i++)
    {
        switch (daily_target_message_st[i].daily_activity_type)
        {
            case WATCH_DAILY_ACTIVITY_TYPE_WATCH_DAILY_ACTIVITY_TYPE_STEP:
            {
                if (daily_target_message_st[i].has_goal_value)
                {
                    daily_goal_set_step(daily_target_message_st[i].goal_value);
                }

                if (daily_target_message_st[i].has_status)
                {
                    daily_goal_switch_set_step(daily_target_message_st[i].status);
                }

                if (daily_target_message_st[i].has_goal_value || daily_target_message_st[i].has_status)
                {
                    service_sync_target_to_algo();
                }
            }
            break;

            case WATCH_DAILY_ACTIVITY_TYPE_WATCH_DAILY_ACTIVITY_TYPE_CALORIES:
            {
                if (daily_target_message_st[i].has_goal_value)
                {
                    daily_goal_set_cal(daily_target_message_st[i].goal_value);
                }

                if (daily_target_message_st[i].has_status)
                {
                    daily_goal_switch_set_cal(daily_target_message_st[i].status);
                }

                if (daily_target_message_st[i].has_goal_value || daily_target_message_st[i].has_status)
                {
                    service_sync_target_to_algo();
                }
            }
            break;

            case WATCH_DAILY_ACTIVITY_TYPE_WATCH_DAILY_ACTIVITY_TYPE_INTENSE_TIME:
            {
                if (daily_target_message_st[i].has_goal_value)
                {
                    daily_goal_set_intense_time(daily_target_message_st[i].goal_value);
                }

                if (daily_target_message_st[i].has_status)
                {
                    daily_goal_switch_set_intense_time(daily_target_message_st[i].status);
                }

                if (daily_target_message_st[i].has_goal_value || daily_target_message_st[i].has_status)
                {
                    service_sync_target_to_algo();
                }
            }
            break;

            case WATCH_DAILY_ACTIVITY_TYPE_WATCH_DAILY_ACTIVITY_TYPE_ACTIVE_HOUR:
            {
                if (daily_target_message_st[i].has_goal_value)
                {
                    daily_goal_set_active_hour(daily_target_message_st[i].goal_value);
                }
            }
            break;

            default:
            {

            }
            break;
        }
    }
}

static bool daily_alarm_message_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    uint8_t status = false;
    watch_daily_alarm_message *daily_alarm_message_st = (watch_daily_alarm_message *)*arg;

    for (uint8_t i = 0; i < _WATCH_DAILY_ALARM_TYPE_MAX; i++)
    {
        daily_alarm_message_st[i].daily_alarm_type = i + 1;
        switch (daily_alarm_message_st[i].daily_alarm_type)
        {
            case WATCH_DAILY_ALARM_TYPE_WATCH_DAILY_ALARM_TYPE_STAND:
            {
                daily_alarm_message_st[i].has_alarm_switch = true;
                daily_alarm_message_st[i].alarm_switch = sedentary_sw_get();
                daily_alarm_message_st[i].has_alarm_time_start = true;
                daily_alarm_message_st[i].alarm_time_start = sedentary_starttime_get();
                daily_alarm_message_st[i].has_alarm_time_end = true;
                daily_alarm_message_st[i].alarm_time_end = sedentary_endtime_get();
                daily_alarm_message_st[i].has_dnd_start = true;
                daily_alarm_message_st[i].dnd_start = sedentary_midrest_starttime_get();
                daily_alarm_message_st[i].has_dnd_end = true;
                daily_alarm_message_st[i].dnd_end = sedentary_midrest_endtime_get();
                daily_alarm_message_st[i].has_dnd_switch = true;
                daily_alarm_message_st[i].dnd_switch = sedentary_midrest_sw_get();
            }
            break;

            case WATCH_DAILY_ALARM_TYPE_WATCH_DAILY_ALARM_TYPE_DRINK:
            {
                daily_alarm_message_st[i].has_alarm_switch = true;
                daily_alarm_message_st[i].alarm_switch = hydration_sw_get();
                daily_alarm_message_st[i].has_alarm_time_start = true;
                daily_alarm_message_st[i].alarm_time_start = hydration_starttime_get();
                daily_alarm_message_st[i].has_alarm_time_end = true;
                daily_alarm_message_st[i].alarm_time_end = hydration_endtime_get();
                daily_alarm_message_st[i].has_alarm_interval = true;
                daily_alarm_message_st[i].alarm_interval = hydration_frequency_get();
                daily_alarm_message_st[i].has_dnd_switch = true;
                daily_alarm_message_st[i].dnd_switch = hydration_midrest_sw_get();
                daily_alarm_message_st[i].has_dnd_start = true;
                daily_alarm_message_st[i].dnd_start = hydration_midrest_starttime_get();
                daily_alarm_message_st[i].has_dnd_end = true;
                daily_alarm_message_st[i].dnd_end = hydration_midrest_endtime_get();
            }
            break;

            default:
            {

            }
            break;
        }

        status = pb_encode_tag_for_field(stream, field);
        status &= pb_encode_submessage(stream, watch_daily_alarm_message_fields, &daily_alarm_message_st[i]);
    }

    return status;
}

static void daily_activity_alarm_send(void)
{
    watch_health_msg health_msg_st;
    watch_daily_alarm_message daily_alarm_message_st[_WATCH_DAILY_ALARM_TYPE_ARRAYSIZE];
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    uint8_t pb_crc = 0;

    memset(&health_msg_st, 0, sizeof(watch_health_msg));
    memset(&daily_alarm_message_st, 0, sizeof(daily_alarm_message_st));

    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

    health_msg_st.daily_alarm_msg.arg = &daily_alarm_message_st;
    health_msg_st.daily_alarm_msg.funcs.encode = &daily_alarm_message_encode;

    health_msg_st.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_HEALTH;
    health_msg_st.health_data_operate_type = WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_DLY_ACT_ALARM_GET;
    health_msg_st.version = 1;

    //编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    //编码
    pb_encode(&encode_stream, watch_health_msg_fields, &health_msg_st);
    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch0();
    //对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);
    ble_cmd_end_tx(health_msg_st.service_type, 0, health_msg_st.health_data_operate_type, 0, encode_stream.bytes_written, pb_crc);
}

static void daily_activity_alarm_set(watch_daily_alarm_message* daily_alarm_message_st)
{
    for (uint8_t i = 0; i < health_alarm_index && i < _WATCH_DAILY_ALARM_TYPE_ARRAYSIZE; i++)
    {
        switch(daily_alarm_message_st[i].daily_alarm_type)
        {
            case WATCH_DAILY_ALARM_TYPE_WATCH_DAILY_ALARM_TYPE_STAND:
            {
                if (daily_alarm_message_st[i].has_alarm_switch)
                {
                    sedentary_sw_set((uint8_t )daily_alarm_message_st[i].alarm_switch);
                }

                if (daily_alarm_message_st[i].has_alarm_time_start)
                {
                    sedentary_starttime_set((uint16_t )daily_alarm_message_st[i].alarm_time_start);
                }

                if (daily_alarm_message_st[i].has_alarm_time_end)
                {
                    sedentary_endtime_set((uint16_t )daily_alarm_message_st[i].alarm_time_end);
                }

                if (daily_alarm_message_st[i].has_dnd_switch)
                {
                    if (daily_alarm_message_st[i].dnd_switch)
                    {
                        sedentary_midrest_sw_set(true);
                    }
                    else
                    {
                        sedentary_midrest_sw_set(false);
                    }
                }

                if (daily_alarm_message_st[i].has_dnd_start && daily_alarm_message_st[i].has_dnd_end)
                {
                    sedentary_midrest_starttime_set((uint16_t )daily_alarm_message_st[i].dnd_start);
                    sedentary_midrest_endtime_set((uint16_t )daily_alarm_message_st[i].dnd_end);
                }
            }
            break;

            case WATCH_DAILY_ALARM_TYPE_WATCH_DAILY_ALARM_TYPE_DRINK:
            {
                if (daily_alarm_message_st[i].has_alarm_switch)
                {
                    hydration_sw_set((uint8_t )daily_alarm_message_st[i].alarm_switch);
                }

                if (daily_alarm_message_st[i].has_alarm_time_start)
                {
                    hydration_starttime_set((uint16_t )daily_alarm_message_st[i].alarm_time_start);
                }

                if (daily_alarm_message_st[i].has_alarm_time_end)
                {
                    hydration_endtime_set((uint16_t )daily_alarm_message_st[i].alarm_time_end);
                }

                if (daily_alarm_message_st[i].has_alarm_interval)
                {
                    hydration_frequency_set((uint16_t )daily_alarm_message_st[i].alarm_interval);
                }

                if (daily_alarm_message_st[i].has_dnd_switch)
                {
                    hydration_midrest_sw_set((uint8_t )daily_alarm_message_st[i].dnd_switch);
                }

                if (daily_alarm_message_st[i].has_dnd_start && daily_alarm_message_st[i].has_dnd_end)
                {
                    hydration_midrest_starttime_set((uint16_t )daily_alarm_message_st[i].dnd_start);
                    hydration_midrest_endtime_set((uint16_t )daily_alarm_message_st[i].dnd_end);
                }
            }
            break;

            default:
            {

            }
            break;
        }
    }
}

static bool health_file_list_message_encode(pb_ostream_t *stream, const pb_field_t *field, void *const *arg)
{
    uint8_t status = true;
    watch_health_data_file_list_message *health_file_list_msg_st = (watch_health_data_file_list_message *)*arg;

    QW_DIR dir;
    QW_FILINFO file_info;
    QW_FRESULT res;
    if (QW_OK != qw_f_opendir(&dir, SYNC_TEMP_PATH))
    {
        return status;
    }

    while(1)
    {
        res = qw_f_readdir(&dir, &file_info);
        if (res != QW_OK || file_info.fname[0] == 0)
        {
            break;
        }

        if (file_info.fattrib & QW_AM_DIR)
        {
            continue;
        }

        char* ext = strrchr(file_info.fname, '.');
        if (ext && strcmp(ext, ".fit") == 0)
        {
            if (!strcmp(file_info.fname, "dlyact.fit"))
            {
                health_file_list_msg_st[health_file_list_index].has_health_data_type = true;
                health_file_list_msg_st[health_file_list_index].health_data_type = WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_DAILY_ACTIVITY;
                health_file_list_msg_st[health_file_list_index].file_name.arg = file_info.fname;
                health_file_list_msg_st[health_file_list_index].file_name.funcs.encode = &encode_string;

                status = pb_encode_tag_for_field(stream, field);
                status &= pb_encode_submessage(stream, watch_health_data_file_list_message_fields, &health_file_list_msg_st[health_file_list_index]);
                health_file_list_index++;
            }
            if (!strcmp(file_info.fname, "sync-hr.fit"))
            {
                health_file_list_msg_st[health_file_list_index].has_health_data_type = true;
                health_file_list_msg_st[health_file_list_index].health_data_type = WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_HEART_RATE;
                health_file_list_msg_st[health_file_list_index].file_name.arg = file_info.fname;
                health_file_list_msg_st[health_file_list_index].file_name.funcs.encode = &encode_string;

                status = pb_encode_tag_for_field(stream, field);
                status &= pb_encode_submessage(stream, watch_health_data_file_list_message_fields, &health_file_list_msg_st[health_file_list_index]);
                health_file_list_index++;
            }
            if (!strcmp(file_info.fname, "sync-spo2.fit"))
            {
                health_file_list_msg_st[health_file_list_index].has_health_data_type = true;
                health_file_list_msg_st[health_file_list_index].health_data_type = WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_SPO2;
                health_file_list_msg_st[health_file_list_index].file_name.arg = file_info.fname;
                health_file_list_msg_st[health_file_list_index].file_name.funcs.encode = &encode_string;

                status = pb_encode_tag_for_field(stream, field);
                status &= pb_encode_submessage(stream, watch_health_data_file_list_message_fields, &health_file_list_msg_st[health_file_list_index]);
                health_file_list_index++;
            }
            if (!strcmp(file_info.fname, "sync-stress.fit"))
            {
                health_file_list_msg_st[health_file_list_index].has_health_data_type = true;
                health_file_list_msg_st[health_file_list_index].health_data_type = WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_STRESS;
                health_file_list_msg_st[health_file_list_index].file_name.arg = file_info.fname;
                health_file_list_msg_st[health_file_list_index].file_name.funcs.encode = &encode_string;

                status = pb_encode_tag_for_field(stream, field);
                status &= pb_encode_submessage(stream, watch_health_data_file_list_message_fields, &health_file_list_msg_st[health_file_list_index]);
                health_file_list_index++;
            }
            if (!strcmp(file_info.fname, "sync-sleep.fit"))
            {
                health_file_list_msg_st[health_file_list_index].has_health_data_type = true;
                health_file_list_msg_st[health_file_list_index].health_data_type = WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_SLEEP;
                health_file_list_msg_st[health_file_list_index].file_name.arg = file_info.fname;
                health_file_list_msg_st[health_file_list_index].file_name.funcs.encode = &encode_string;

                status = pb_encode_tag_for_field(stream, field);
                status &= pb_encode_submessage(stream, watch_health_data_file_list_message_fields, &health_file_list_msg_st[health_file_list_index]);
                health_file_list_index++;
            }
            if (!strcmp(file_info.fname, "sync-hrv.fit"))
            {
                health_file_list_msg_st[health_file_list_index].has_health_data_type = true;
                health_file_list_msg_st[health_file_list_index].health_data_type = WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_HEART_RATE_VARIABILITY;
                health_file_list_msg_st[health_file_list_index].file_name.arg = file_info.fname;
                health_file_list_msg_st[health_file_list_index].file_name.funcs.encode = &encode_string;

                status = pb_encode_tag_for_field(stream, field);
                status &= pb_encode_submessage(stream, watch_health_data_file_list_message_fields, &health_file_list_msg_st[health_file_list_index]);
                health_file_list_index++;
            }
            if (!strcmp(file_info.fname, "sync-resthr.fit"))
            {
                health_file_list_msg_st[health_file_list_index].has_health_data_type = true;
                health_file_list_msg_st[health_file_list_index].health_data_type = WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_RESTING_HR;
                health_file_list_msg_st[health_file_list_index].file_name.arg = file_info.fname;
                health_file_list_msg_st[health_file_list_index].file_name.funcs.encode = &encode_string;

                status = pb_encode_tag_for_field(stream, field);
                status &= pb_encode_submessage(stream, watch_health_data_file_list_message_fields, &health_file_list_msg_st[health_file_list_index]);
                health_file_list_index++;
            }
        }
    }

    return status;
}

static void health_file_list_send(void)
{
    watch_health_msg health_msg_st;
    watch_health_data_file_list_message health_file_list_msg_st[HEALTH_FILE_NUM_MAX];
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    uint8_t pb_crc = 0;

    memset(&health_msg_st, 0, sizeof(watch_health_msg));
    memset(&health_file_list_msg_st, 0, sizeof(health_file_list_msg_st));

    ble_data_var_tx_get(&data, &length, BLE_NUS_CH2_UUID);

    health_msg_st.file_list_msg.arg = &health_file_list_msg_st;
    health_msg_st.file_list_msg.funcs.encode = &health_file_list_message_encode;

    health_msg_st.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_HEALTH;
    health_msg_st.health_data_operate_type = WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_HISTORY_LIST_GET;
    health_msg_st.version = 1;
    health_msg_st.has_file_list_num = true;
    health_msg_st.file_list_num.has_file_num = true;
    health_msg_st.file_list_num.file_num = get_health_sync_file_count(SYNC_TEMP_PATH);

    //编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    //编码
    pb_encode(&encode_stream, watch_health_msg_fields, &health_msg_st);
    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch2();
    //对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);
    ble_cmd_end_tx(health_msg_st.service_type, 0, health_msg_st.health_data_operate_type, 0, encode_stream.bytes_written, pb_crc);

}

static bool health_data_message_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    watch_health_data_message *health_data_message_st = (watch_health_data_message *)*arg;

    if (health_data_index >= _WATCH_HEALTH_DATA_TYPE_ARRAYSIZE)
    {
        return false;
    }

    memset(&health_data_message_st[health_data_index], 0, sizeof(watch_health_data_message));

    if (!pb_decode(stream, watch_health_data_message_fields, &health_data_message_st[health_data_index]))
    {
        return false;
    }
    health_data_index++;
    return true;
}

static bool daily_target_message_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    watch_daily_target_message *daily_target_message_st = (watch_daily_target_message *)*arg;

    if (health_target_index >= _WATCH_DAILY_ACTIVITY_TYPE_ARRAYSIZE)
    {
        return false;
    }

    memset(&daily_target_message_st[health_target_index], 0, sizeof(watch_daily_target_message));

    if (!pb_decode(stream, watch_daily_target_message_fields, &daily_target_message_st[health_target_index]))
    {
        return false;
    }

    health_target_index++;

    return true;
}

static bool daily_alarm_message_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    watch_daily_alarm_message *daily_alarm_message_st = (watch_daily_alarm_message *)*arg;

    if (health_alarm_index >= _WATCH_DAILY_ALARM_TYPE_ARRAYSIZE)
    {
        return false;
    }

    memset(&daily_alarm_message_st[health_alarm_index], 0, sizeof(watch_daily_alarm_message));

    if (!pb_decode(stream, watch_daily_alarm_message_fields, &daily_alarm_message_st[health_alarm_index]))
    {
        return false;
    }
    health_alarm_index++;
    return true;
}

static bool health_file_list_message_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    watch_health_data_file_list_message *health_file_list_msg_st = (watch_health_data_file_list_message *)*arg;

    if (health_file_list_index >= _WATCH_HEALTH_DATA_TYPE_ARRAYSIZE)
    {
        return false;
    }

    memset(&health_file_list_msg_st[health_file_list_index], 0, sizeof(watch_health_data_file_list_message));

    if (!pb_decode(stream, watch_health_data_file_list_message_fields, &health_file_list_msg_st[health_file_list_index]))
    {
        return false;
    }
    if (health_file_list_msg_st[health_file_list_index].has_health_data_type)
    {
        // file_sync_type = health_file_list_msg_st[health_file_list_index].health_data_type;
        switch (health_file_list_msg_st[health_file_list_index].health_data_type)
        {
            case WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_SLEEP:
            {
                file_sync_type = SLEEP_RECORD_INDEX;
            }
            break;

            case WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_STRESS:
            {
                file_sync_type = STRESS_RECORD_INDEX;
            }
            break;

            case WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_HEART_RATE:
            {
                file_sync_type = HR_RECORD_INDEX;
            }
            break;

            case WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_SPO2:
            {
                file_sync_type = SPO2_RECORD_INDEX;
            }
            break;

            case WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_HEART_RATE_VARIABILITY:
            {
                file_sync_type = HRV_RECORD_INDEX;
            }
            break;

            case WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_DAILY_ACTIVITY:
            {
                file_sync_type = STEP_RECORD_INDEX;
            }
            break;

            case WATCH_HEALTH_DATA_TYPE_WATCH_HEALTH_DATA_RESTING_HR:
            {
                file_sync_type = REST_HR_RECORD_INDEX;
            }
            break;

            default:
            {
                file_sync_type = HEALTH_RECORD_INDEX_MAX;
            }
            break;
        }
    }
    health_file_list_index++;
    return true;
}

/************************************************************************
 *@function:void health_data_decode(uint8_t* pb_buffer, uint16_t buffer_length, END_TYPE end_type);
 *@brief:解析健康数据
 *@param: uint8_t* pb_buffer pb数据指针, uint16_t buffer_length 数据长度, END_TYPE end_type 数据类型
 *@return:null
*************************************************************************/
void health_data_decode(uint8_t* pb_buffer, uint16_t buffer_length, END_TYPE end_type)
{
    uint8_t status = false;
    watch_health_msg health_msg_st;
    watch_health_data_message health_data_message_st[_WATCH_HEALTH_DATA_TYPE_ARRAYSIZE];
    watch_daily_target_message daily_target_message_st[_WATCH_DAILY_ACTIVITY_TYPE_ARRAYSIZE];
    watch_daily_alarm_message daily_alarm_message_st[_WATCH_DAILY_ALARM_TYPE_ARRAYSIZE];
    watch_health_data_file_list_message health_file_list_message_st[_WATCH_HEALTH_DATA_TYPE_ARRAYSIZE];

    health_data_index = 0;
    health_target_index = 0;
    health_alarm_index = 0;
    health_file_list_index = 0;

    memset(&health_msg_st, 0, sizeof(watch_health_msg));
    memset(&health_data_message_st, 0, sizeof(watch_health_data_message));
    memset(&daily_target_message_st, 0, sizeof(watch_daily_target_message));
    memset(&daily_alarm_message_st, 0, sizeof(watch_daily_alarm_message));
    memset(&health_file_list_message_st, 0, sizeof(watch_health_data_file_list_message));

    health_msg_st.health_data_msg.arg = &health_data_message_st;
    health_msg_st.health_data_msg.funcs.decode = &health_data_message_decode;

    health_msg_st.daily_target_msg.arg = &daily_target_message_st;
    health_msg_st.daily_target_msg.funcs.decode = &daily_target_message_decode;

    health_msg_st.daily_alarm_msg.arg = &daily_alarm_message_st;
    health_msg_st.daily_alarm_msg.funcs.decode = &daily_alarm_message_decode;

    health_msg_st.file_list_msg.arg = &health_file_list_message_st;
    health_msg_st.file_list_msg.funcs.decode = &health_file_list_message_decode;

    pb_istream_t decode_stream = pb_istream_from_buffer(pb_buffer, buffer_length);
    status = pb_decode(&decode_stream, watch_health_msg_fields, &health_msg_st);

    if (true == status)
    {
        switch(health_msg_st.health_data_operate_type)
        {
            case WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_REAL_TIME_GET:
            {
                //暂不允许支持获取实时最新数据，只允许获取历史数据
            }
            break;

            case WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_HISTORY_GET:
            {
                health_history_sync_request(health_data_message_st);
                ble_cmd_success_status_tx(health_msg_st.service_type, 0, health_msg_st.health_data_operate_type, 0);
            }
            break;

            case WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_DLY_ACT_TARGET_GET:
            {
                daily_activity_target_send();
            }
            break;

            case WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_DLY_ACT_TARGET_SET:
            {
                daily_activity_target_set(daily_target_message_st);
                ble_cmd_success_status_tx(health_msg_st.service_type, 0, health_msg_st.health_data_operate_type, 0);
            }
            break;

            case WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_DLY_ACT_ALARM_GET:
            {
                daily_activity_alarm_send();
            }
            break;

            case WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_DLY_ACT_ALARM_SET:
            {
                daily_activity_alarm_set(daily_alarm_message_st);
                ble_cmd_success_status_tx(health_msg_st.service_type, 0, health_msg_st.health_data_operate_type, 0);
            }
            break;

            case WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_HISTORY_LIST_GET:
            {
                health_file_list_send();
            }
            break;

            case WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_HISTORY_FILE_GET:
            {

            }
            break;

            default:
            {
                ble_cmd_status_tx(health_msg_st.service_type, 0, health_msg_st.health_data_operate_type, 0, enumINVALID_CMD);
            }
            break;
        }
    }
}

/************************************************************************
 *@function:void health_data_sync_request(void);
 *@brief: 上报健康数据同步请求
 *@param: null
 *@return:null
*************************************************************************/
void health_data_sync_request(void)
{
    // watch_health_msg health_msg_st;
    // uint8_t *data = NULL;
    // uint16_t *length = NULL;

    // memset(&health_msg_st, 0, sizeof(watch_health_msg));

    // ble_data_var_tx_get(&data, &length, BLE_NUS_CH1_UUID);

    // health_msg_st.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_HEALTH;
    // health_msg_st.health_data_operate_type = WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_DLY_ACT_DATA_SYNC;
    // health_msg_st.version = 1;

    // //编码缓存
    // pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX);
    // //编码
    // pb_encode(&encode_stream, watch_health_msg_fields, &health_msg_st);
    // *length = encode_stream.bytes_written;
    // ble_nus_data_tx_ch1();
    ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_HEALTH, 0,
        WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_DLY_ACT_DATA_SYNC, 0xff, 0);

}

/************************************************************************
 *@function:void health_file_package_state_send(void);
 *@brief: 上报健康历史文件打包完成状态
 *@param: uint8_t state 打包完成状态  见 HEALTH_SYNC_FLAG_E
 *@return:null
*************************************************************************/
void health_file_package_state_send(void)
{
    HEALTH_PB_LOG_I("health_file_package_state_send!!!!!!");
    ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_HEALTH, 0,
        WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_HISTORY_FILE_PACKAGED, 0xff, 0);
}

#include "qw_time_util.h"
/************************************************************************
 *@function:void health_sync_minute_set(void);
 *@brief: 设置健康数据同步 每小时的分钟数
 *@param: null
 *@return:null
*************************************************************************/
void health_sync_minute_set(void)
{
    time_t real_time = get_sec_from_rtc();
    uint8_t minute = (uint8_t)((real_time / 60) % 60);
    set_health_sync_minute(minute);
    health_data_sync_time_send(minute);
}

// 上电后获取健康数据同步时间，并传递给小核
int health_sync_minute_init(void)
{
    uint8_t minute = get_health_sync_minute();
    health_data_sync_time_send(minute);

    return 0;
}

INIT_APP_EXPORT(health_sync_minute_init);

#include "ble_interflow_single.h"
#include "file_upload_pb_encode.h"

static bool sync_index_2_name(HEALTH_SYNC_INDEX_E sync_idx, MY_TCHAR* name)
{
    bool ret = false;
    switch (sync_idx)
    {
        case STEP_RECORD_INDEX:
        {
            strcpy(name, DAILY_ACTIVITY_FIT_PATH);
            ret = true;
        }
        break;
        case HR_RECORD_INDEX:
        {
            strcpy(name, HR_FIT_PATH);
            ret = true;
        }
        break;
        case SPO2_RECORD_INDEX:
        {
            strcpy(name, SPO2_FIT_PATH);
            ret = true;
        }
        break;
        case SLEEP_RECORD_INDEX:
        {
            strcpy(name, SLEEP_FIT_PATH);
            ret = true;
        }
        break;
        case STRESS_RECORD_INDEX:
        {
            strcpy(name, STRESS_FIT_PATH);
            ret = true;
        }
        break;
        case HRV_RECORD_INDEX:
        {
            strcpy(name, HRV_FIT_PATH);
            ret = true;
        }
        break;
        case REST_HR_RECORD_INDEX:
        {
            strcpy(name, REST_HR_FIT_PATH);
            ret = true;
        }
        break;
        default:
        {
            ret = false;
        }
        break;
    }
    return ret;
}

static uint32_t health_file_size_get(HEALTH_SYNC_INDEX_E sync_idx, uint32_t* sum32)
{
    QW_FIL* fp = NULL;
    MY_TCHAR name[52] = {0};
    int ret = 0;

    if (sync_index_2_name(sync_idx, name) == false)
    {
        return 0;
    }
    ret = qw_f_open(&fp, name, QW_FA_READ);
    if (ret != 0)
    {
        HEALTH_PB_LOG_E("health file open fail:%s", name);
        return 0;
    }
    uint32_t health_file_obj_size = fp->obj.objsize;
#ifdef USING_FILE_VERIFY
    #define BUF_READ_SIZE  2048
    unsigned int br = 0;
    uint8_t *buff = rt_malloc(BUF_READ_SIZE);
    while(1)
    {
        if (FR_OK == qw_f_read(fp, buff, BUF_READ_SIZE, &br))
        {
            if (0 == br)
            {
                break;
            }
            else
            {
                for (uint16_t i = 0; i < br; i++)
                {
                    *sum32 += buff[i];
                }
            }
        }
        else
        {
            break;
        }
    }
#endif
    qw_f_close(fp);
    return health_file_obj_size;
}

static bool health_data_read_file_direct(HEALTH_SYNC_INDEX_E sync_idx, uint8_t *data)
{
//#define FILE_READ_PACKET_SIZE	512
//#define MTU_SEND_PACKET_SIZE	244
#define FILE_READ_512_SIZE		1536   // (8*244)
#define FILE_REMIND_SIZE		416   // (8*244 - 3*512)
    char fnamestr[52] = {0};
    unsigned int tt = 0;
    memset(fnamestr, 0 ,52);

    // if (NULL == fdst)
    // {
    //     fdst = QW_FIL_FD_MALLOC(sizeof(QW_FIL));
    //     if (NULL == fdst)
    //     {
    //         return false;
    //     }
    // }
    if (false == open_flag)
    {
        if (sync_index_2_name(sync_idx, fnamestr) == false)
        {
            return false;
        }
        if (FR_OK == qw_f_open(&fdst, fnamestr, QW_FA_READ))
        {
            open_flag = true;
            health_file_data_size = 0;
        }
    }
    if (true == open_flag)
    {
        if (!qw_f_eof(fdst))
        {
            qw_f_read(fdst, data + read_file_len, FILE_READ_512_SIZE, &tt);
            read_file_len += tt;
            health_file_data_size += tt;
            if ((tt < FILE_READ_512_SIZE) || (qw_f_size(fdst) == health_file_data_size))
            {
                qw_f_close(fdst);
                // QW_FIL_FD_FREE(fdst);
                fdst = NULL;
                open_flag = false;
                trans_cmp = true;
                sync_index_2_name(sync_idx, fnamestr);
                qw_f_unlink(fnamestr);
                data_sync_remind_set_last_sync_time();
            }
            else
            {
                qw_f_read(fdst, data + read_file_len, FILE_REMIND_SIZE, &tt);
                read_file_len += tt;
                health_file_data_size += tt;
                if ((tt < FILE_REMIND_SIZE) || (qw_f_size(fdst) == health_file_data_size))
                {
                    qw_f_close(fdst);
                    // QW_FIL_FD_FREE(fdst);
                    fdst = NULL;
                    open_flag = false;
                    trans_cmp = true;
                    sync_index_2_name(sync_idx, fnamestr);
                    qw_f_unlink(fnamestr);
                    data_sync_remind_set_last_sync_time();
                }
            }
        }
        else
        {
            qw_f_close(fdst);
            // QW_FIL_FD_FREE(fdst);
            fdst = NULL;
            open_flag = false;
            trans_cmp = true;
            sync_index_2_name(sync_idx, fnamestr);
            qw_f_unlink(fnamestr);
            data_sync_remind_set_last_sync_time();
            health_file_data_size = 0;
        }
    }
    return true;
}

static bool health_file_send_raw(bool isStart)
{
    uint8_t *data = NULL;
    uint16_t *length = NULL;
    bool ret = false;
    uint32_t sum32 = 0;
    ble_data_var_tx_get_and_ret(&data, &length, BLE_NUS_CH2_UUID);
    read_file_len = 0;
    if (isStart)
    {
        uint32_t file_size = health_file_size_get(file_sync_type, &sum32);
        if (file_size)
        {
            uint16_t encoded_length = file_upload_pb_encode(&data[PB_LENGTH_FIELD_SIZE], ONE_FRAME_DATA_LENGTH_MAX_CH2_TX - PB_LENGTH_FIELD_SIZE, file_size, 0, 0, NULL, sum32);
            if (encoded_length)
            {
                data[0] = (encoded_length>>24)&0xff;
                data[1] = (encoded_length>>16)&0xff;
                data[2] = (encoded_length>>8)&0xff;
                data[3] = (encoded_length)&0xff;

                *length = PB_LENGTH_FIELD_SIZE + encoded_length;
                ret = health_data_read_file_direct(file_sync_type, data + *length);
                *length += read_file_len;
            }
        }
    }
    else
    {
        ret = health_data_read_file_direct(file_sync_type, data);
        if (ret)
        {
            *length = read_file_len;
        }
    }

    if (ret)
    {
        ble_nus_data_tx_ch2();
        if (isStart)
        {
            ble_cmd_end_data_stream_type_tx(service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_HEALTH, DATA_STREM_UPLOAD_MODE, \
                0, WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_HISTORY_FILE_GET, 0, *length, enumFILE_END, 0);
        }
    }
    else
    {
        ble_cmd_err_status_tx(service_type_index_enum_SERVICE_TYPE_INDEX_WATCH_HEALTH, 0, WATCH_HEALTH_DATA_OPERATE_TYPE_enum_WATCH_HEALTH_DATA_OPERATE_TYPE_HISTORY_FILE_GET, 0);
    }
    return ret;
}

static bool transfor_start = true;
uint8_t health_file_transfor(uint8_t service_type, uint8_t op_type)
{
    if (transfor_start && ble_tx_channel_is_free(BLE_NUS_CH2_UUID))
    {
        if (!health_file_send_raw(transfor_start))
        {
            trans_cmp = false;
            transfor_start = true;
            return true;
        }
        else
        {
            transfor_start = false;
            if (trans_cmp)
            {
                trans_cmp = false;
                transfor_start = true;
                return true;
            }
        }
    }
    else
    {
        if (open_flag && ble_tx_channel_is_free(BLE_NUS_CH2_UUID))
        {
            if (false == trans_cmp)
            {
                if (health_file_send_raw(transfor_start))
                {
                    if (trans_cmp)
                    {
                        trans_cmp = false;
                        transfor_start = true;
                        return true;
                    }
                }
                else
                {
                    trans_cmp = false;
                    transfor_start = true;
                    return true;
                }
            }
        }
    }
    return false;
}

void health_file_transfor_abort(uint8_t service_type, uint8_t op_type)
{
    if (open_flag)
    {
        transfor_start = true;
        qw_f_close(fdst);
        QW_FIL_FD_FREE(fdst);
        fdst = NULL;
        open_flag = false;
        read_file_len = 0;
        trans_cmp = true;
        health_file_data_size = 0;
    }
}

