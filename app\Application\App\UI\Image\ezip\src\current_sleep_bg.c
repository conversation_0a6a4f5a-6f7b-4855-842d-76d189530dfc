#include "lvgl.h" 

#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP ALIGN(4)
#endif

#ifndef eZIP_RGBARGB888A
#define eZIP_RGBARGB888A
#endif


LV_ATTRIBUTE_MEM_ALIGN_EZIP const  uint8_t current_sleep_bg_map[] SECTION(".ROM3_IMG_EZIP.current_sleep_bg") = { 
    0x00,0x00,0x00,0xa2,0x46,0x08,0x20,0x00,0x01,0x1a,0x00,0x08,0x00,0x00,0x00,0x00,0x00,0x08,0x00,0x01,0x00,0x00,0x00,0x44,0x3d,0x7b,0x35,0x43,0x18,0x08,0xa2,0x28,
    0x7c,0x67,0x88,0xc0,0xe1,0x52,0x01,0x1d,0x20,0xe9,0x87,0xa2,0xe8,0x07,0x49,0x07,0xa9,0x20,0x0e,0x87,0x08,0x3b,0xec,0x83,0x0e,0xc8,0xb8,0xf3,0x97,0xb0,0xe2,0x7c,
    0xc9,0x1d,0x00,0x00,0x35,0x55,0x71,0xb9,0x9d,0x34,0x1d,0xcf,0xb2,0x32,0x4b,0x36,0x09,0x00,0xfe,0x12,0x9b,0xc2,0x57,0x6d,0xef,0xc5,0x9e,0xf7,0x97,0xfd,0x22,0x73,
    0x95,0xc5,0x81,0xc7,0x01,0xb0,0x6f,0x6f,0xec,0x53,0x63,0xf3,0xf0,0xf1,0x25,0x43,0x64,0x00,0x24,0x68,0x6d,0xa9,0x8d,0xf1,0xf1,0xbb,0x04,0x00,0x59,0xb1,0x29,0xb3,
    0xf3,0x0a,0x00,0xb2,0x79,0x1f,0x6c,0x00,0x20,0x6d,0xa7,0xf1,0xd5,0xdb,0x2a,0xdc,0x07,0x1b,0x00,0xc8,0x19,0x83,0x17,0xce,0xdb,0x00,0xd2,0xcf,0xdb,0x5f,0x5b,0x96,
    0x31,0xb5,
};


#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER ALIGN(4)
#endif
LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER const eZIP_RGBARGB888A  lv_img_dsc_t current_sleep_bg SECTION(".ROM3_IMG_EZIP_HEADER.current_sleep_bg") = { 
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .header.always_zero = 0,
  .header.w = 282,
  .header.h = 8,
  .data_size  = 162,
  .data = current_sleep_bg_map
};
