#ifndef NAVI_TCLM_H
#define NAVI_TCLM_H

#ifdef __cplusplus
extern "C" {
#endif

#include "navi_common.h"
#include "qw_fs.h"

//用于读取tclm文件
typedef struct _TclmReader
{
    QW_FIL *fp;                     //文件指针
    uint32_t pos_num;               //上坡数量
    uint32_t neg_num;               //下坡数量
} TclmReader;

//用于写tclm文件
typedef struct _TclmWriter
{
    QW_FIL *fp;                     //文件指针
    uint32_t pos_num;               //上坡数量
    uint32_t neg_num;               //下坡数量
} TclmWriter;

//爬坡总体数据
typedef struct _NaviClimbEnsembleData
{
    uint32_t num;                   //爬坡数量
    float h;                        //总爬升，即各爬坡海拔变化量之和（m）
    float dist;                     //总距离，即各爬坡距离变化量之和（m）
} NaviClimbEnsembleData;

//导航线路爬坡点
typedef struct _NaviClimbpoint
{
    float dist;                     //距离（在线路上距离起点的距离，m）
    float alt;                      //海拔（m）
} NaviClimbpoint;

//爬坡采样
typedef struct _NaviClimbSample
{
    NaviClimbpoint *buf;            //缓冲区，保存采样的爬坡点
    uint32_t len;                   //爬坡点采样数量
} NaviClimbSample;

//指定距离前后的爬坡点
typedef struct _NaviRouteCpNearby
{
    NaviClimbpoint *buf;
    uint32_t len;
} NaviRouteCpNearby;

//导航线路爬坡点分段
typedef struct _NaviRouteCpSegment
{
    Dseg dseg;                      //距离范围
    Range range;                    //索引范围
} NaviRouteCpSegment;

//导航线路爬坡点分段数组
typedef struct _NaviRouteCpSegmentArray
{
    NaviRouteCpSegment *segments;   //缓冲区，保存分段
    uint32_t len;                   //分段数量
} NaviRouteCpSegmentArray;

//导航线路爬坡点缓冲
typedef struct _NaviRouteCpBuf
{
    NaviClimbpoint *buf;            //缓冲区，保存缓存的爬坡点
    Range range;                    //缓存的爬坡点的范围
    uint32_t capacity;              //缓冲区容量
} NaviRouteCpBuf;

//导航线路爬坡点缓存
typedef struct _NaviRouteCpCache
{
    NaviRouteCpBuf *cp_buf;         //缓冲区，有若干爬坡点缓冲
    TclmReader *tclm_reader;        //用于从tclm文件中读取爬坡点到缓冲中
    uint32_t capacity;              //缓冲区容量
    uint32_t len;                   //已使用的爬坡点缓冲数量
    uint32_t next;                  //下一个要使用的爬坡点缓冲
} NaviRouteCpCache;

//导航线路爬坡点list，一个抽象列表，通过它可从tclm文件中获取任一爬坡点
typedef struct _NaviRouteCpList
{
    NaviRouteCpCache cache;
    uint32_t len;                   //爬坡点总数
} NaviRouteCpList;

//爬坡分段
typedef struct _NaviClimbSegment
{
    Dseg dseg;
    Range range;
} NaviClimbSegment;

//爬坡分段数组
typedef struct _NaviClimbSegmentArray
{
    NaviClimbSegment *segments;     //缓冲区，保存分段
    uint32_t len;                   //分段数量
} NaviClimbSegmentArray;

//爬坡数据
typedef struct _NaviClimbData
{
    NaviClimbSample sample;         //爬坡点采样
    float start_dist;               //开始距离，即在线路上到起点的距离（m）
    float end_dist;                 //结束距离（m）
    float start_alt;                //起始海拔（m）
    float end_alt;                  //结束海拔（m）
    float min_alt;                  //最小海拔（m）
    float max_alt;                  //最大海拔（m）
    float ascent;                   //总升（m）
    float max_grade;                //最大坡度
    float grade;                    //平均坡度
    float h_acc;                    //到达该爬坡时，已经过的爬坡的海拔变化量之和
    float d_acc;                    //到达该爬坡时，已经过的爬坡的距离变化量之和
} NaviClimbData;

//爬坡数据缓冲
typedef struct _NaviClimbDataBuf
{
    NaviClimbData *buf;             //缓冲区，保存爬坡数据
    Range range;                    //缓存的爬坡数据范围
    uint32_t capacity;              //缓存区容量
} NaviClimbDataBuf;

//爬坡数据缓存
typedef struct _NaviClimbDataCache
{
    NaviClimbDataBuf *cd_buf;       //缓冲区，有若干爬坡数据缓冲
    TclmReader *tclm_reader;        //用于从tclm文件中读取爬坡数据
    uint32_t capacity;              //缓冲区容量
    uint32_t len;                   //已使用的爬坡数据缓冲数量
    uint32_t next;                  //下一个要使用的爬坡数据缓冲
} NaviClimbDataCache;

//爬坡list，一个抽象列表，通过它可以从tclm文件中获取任一爬坡数据
typedef struct _NaviClimbList
{
    NaviClimbDataCache cache;
    uint32_t len;
} NaviClimbList;

int tclm_header_read(TclmReader *self, uint8_t *header);

int tclm_pos_ensemble_data_read(TclmReader *self, NaviClimbEnsembleData *data);

int tclm_neg_ensemble_data_read(TclmReader *self, NaviClimbEnsembleData *data);

int tclm_route_cp_sample_read(TclmReader *self, NaviClimbSample *sample);

int tclm_route_cp_segment_array_read(TclmReader *self, NaviRouteCpSegmentArray *array);

int tclm_pos_segment_array_read(TclmReader *self, NaviClimbSegmentArray *array);

int tclm_neg_segment_array_read(TclmReader *self, NaviClimbSegmentArray *array);

int tclm_pos_climb_read(TclmReader* self, uint32_t idx, float total_dist, NaviClimbData* climb);

int tclm_neg_climb_read(TclmReader* self, uint32_t idx, float total_dist, NaviClimbData* climb);

int tclm_route_cp_num_read(TclmReader *self, uint32_t *cp_num);

int tclm_route_cp_read(TclmReader *self, uint32_t start, uint32_t end, uint32_t cp_num, NaviClimbpoint *cp_buf);

int tclm_header_placeholder_write(TclmWriter *self);

int tclm_header_write(TclmWriter *self);

int tclm_pos_ensemble_data_write(TclmWriter *self, const NaviClimbEnsembleData *data);

int tclm_neg_ensemble_data_write(TclmWriter *self, const NaviClimbEnsembleData *data);

int tclm_route_cp_sample_write(TclmWriter *self, const NaviClimbSample *sample);

int tclm_route_cp_segment_array_write(TclmWriter *self, const NaviRouteCpSegmentArray *array);

int tclm_pos_segment_array_write(TclmWriter *self, const NaviClimbSegmentArray *array);

int tclm_neg_segment_array_write(TclmWriter *self, const NaviClimbSegmentArray *array);

int tclm_pos_climb_num_write(TclmWriter *self);

int tclm_pos_climb_write(TclmWriter *self, uint32_t idx, const NaviClimbData *climb);

int tclm_neg_climb_num_write(TclmWriter *self);

int tclm_neg_climb_write(TclmWriter *self, uint32_t idx, const NaviClimbData *climb);

int tclm_route_cp_num_write(TclmWriter *self, uint32_t num);

int tclm_route_cp_write(TclmWriter *self, const NaviClimbpoint *cp);

int navi_route_cp_list_get(NaviRouteCpList *self, uint32_t idx, NaviClimbpoint *output);

int navi_pos_climb_list_get(NaviClimbList *self, uint32_t idx, float total_dist, NaviClimbData **output);

int navi_neg_climb_list_get(NaviClimbList *self, uint32_t idx, float total_dist, NaviClimbData **output);

void navi_route_cp_copy(NaviClimbpoint *self, const NaviClimbpoint *cp);

void navi_route_cp_update(NaviClimbpoint *self, float dist, float alt);

void navi_route_cp_segment_copy(NaviRouteCpSegment *self, const NaviRouteCpSegment *segment);

void navi_climb_segment_copy(NaviClimbSegment *self, const NaviClimbSegment *segment);

void navi_route_cp_buf_reset(NaviRouteCpBuf *self);

void navi_climb_data_buf_reset(NaviClimbDataBuf *self);

void navi_climb_data_cache_reset(NaviClimbDataCache *self);

void navi_climb_list_reset(NaviClimbList *self);

void navi_route_cp_cache_reset(NaviRouteCpCache *self);

void navi_route_cp_list_reset(NaviRouteCpList *self);

#ifdef __cplusplus
}
#endif

#endif