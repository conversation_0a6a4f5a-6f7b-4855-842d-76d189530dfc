/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   algo_service_gps_data.c
@Time    :   2024/12/17 14:09:56
*
**************************************************************************/

#include "algo_service_gps_data.h"
#include "activity_fit_simulator.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_sports/algo_service_sport_status.h"
#include "algo_service_task.h"
#include "back_service.h"
#include "gnss_epo_download.h"
#include "gui_event_service.h"
#include "qw_timer.h"
#include "subscribe_data/subscribe_data.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"
#include "view_page_model_sports.h"
#include "gps_recv.h"
#include "qw_fs.h"

#define GPS_REQ_EPO_DELAY_80M 4800000
#define GPS_REQ_EPO_DELAY_2H 7200000

// 本算法打开标记
static bool s_is_gps_data_open = false;
static int s_gps_signal = -1;
static qw_timer gps_timer;
static bool gps_timer_init_flag = false;
static bool gps_timer_start_flag = false;

/**
 * 获取gps当前状态
 */
bool get_gps_open_status(void)
{
    return s_is_gps_data_open;
}

/**
 * @brief 设置GPS信号强度
 */
static void set_gps_signal(int signal_lvl)
{
    if (get_sport_status() >= enum_status_saving && get_sport_type_is_outdoor(get_current_sport_mode()))
    {
        if (s_gps_signal <= 0 && signal_lvl > 0)
        {
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_SPORTING_GPS_LOCATE, NULL);
        }
        else if (s_gps_signal > 0 && signal_lvl <= 0)
        {
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_SPORTING_GPS_LOSS, NULL);
        }
    }

    s_gps_signal = signal_lvl;
}

/**
 * @brief 获取GPS信号强度
 */
int get_gps_signal(void)
{
    return s_gps_signal;
}

/**
 * @brief gps速度过滤算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_gps_data_in_callback(const void *in, uint32_t len)
{
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_GPS_DATA;
    head.input_type = 0;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief gps速度过滤算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_gps_data_out_callback(const void *out, uint32_t len)
{
    if (qw_dataserver_publish_id(DATA_ID_ALGO_GPS_DATA, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("%s publish error", __FUNCTION__);
    }
}

/**
 * @brief gps位置请求
 */
void gps_epo_location_request()
{
    uint32_t cur_s = time(0) - service_datetime_get_timezone();
    uint32_t interval_time = 60 * 10;   // 10min
    if ((cur_s - get_cfg_gps_get_time()) >= interval_time)
    {
        position_data_get_cmd();
    }
}

/**
 * @brief gps星历文件更新
 */
void gps_epo_file_update()
{
    gnss_epo_file_id_t file_id = gps_update_flag_get();
    if (file_id == GNSS_EPO_FILE_ID_MAX)
    {
        return;
    }

    // 起定时器，防止EPO更新失败
    if (gps_timer_init_flag && !gps_timer_start_flag)
    {
        qw_timer_start(&gps_timer, GPS_REQ_EPO_DELAY_2H, NULL, "epo_request");
        gps_timer_start_flag = true;
    }

    // 当前没有在注入EPO文件并且没有正在下载，请求更新；否则重新起定时器
    if (!epo_file_aiding_state_get(file_id) && !gnss_epo_downloading_state_get(file_id))
    {
        epo_request_file_from_app(file_id, gnss_epo_time_get(file_id));
    }
}

/**
 * @brief gps星历文件请求回调
 */
static void gps_epo_request_callback(void *parameter)
{
    gps_timer_start_flag = false;
    gps_epo_file_update();
}

/**
 * @brief 订阅时钟回调函数
 * 
 * @param data 时钟数据
 * @param len 数据长度
 */
static void service_gps_epo_download_callback(const void *data, uint32_t len)
{
    if (len != sizeof(algo_timer_event_pub_t))
    {
        ALGO_COMP_LOG_E("service_gps_epo_download_callback erro len:%d", len);
        return;
    }

    gnss_epo_update_flag_mark();
    algo_timer_event_pub_t *pdata = (algo_timer_event_pub_t *) data;
    if (pdata->event_type == TIMER_EVENT_NEW_DAY && gps_timer_init_flag)
    {
        // 服务器00:10同步EPO文件，APP 01:00从服务器缓存文件，设备01:20请求文件
        qw_timer_start(&gps_timer, GPS_REQ_EPO_DELAY_80M, NULL, "epo_request");
        gps_timer_start_flag = true;
    }
}

/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "drv_gps", 
        .topic_id = DATA_ID_RAW_GPS,
        .callback = algo_gps_data_in_callback,
    },
};
/**
 * @brief 订阅算法定义
 */
static algo_topic_node_t s_algo_topic_node_timer[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_event_timer", 
        .topic_id = DATA_ID_ALGO_EVENT_TIMER,
        .callback = service_gps_epo_download_callback,
    },
};

/**
 * @brief gps速度过滤算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_gps_data_init(void)
{
    if (gps_timer_init_flag)
    {
        ALGO_COMP_LOG_W("%s already init", __FUNCTION__);
        return 0;
    }
    
    ALGO_COMP_LOG_D("%s init", __FUNCTION__);
    
    gps_timer_init_flag = true;

    /* 初始化gps_timer */
    qw_timer_init(&gps_timer, QW_TIMER_FLAG_ONE_SHOT | QW_TIMER_FLAG_SOFT_TIMER, gps_epo_request_callback);

    /* 订阅时钟 */
    return algo_topic_list_subscribe(s_algo_topic_node_timer, sizeof(s_algo_topic_node_timer) / sizeof(s_algo_topic_node_timer[0]));
}

/**
 * @brief 打开gps速度过滤算法
 *
 * @return int32_t 结果
 */
static int32_t algo_gps_data_open(void)
{
    if (s_is_gps_data_open)
    {
        ALGO_COMP_LOG_W("%s already open", __FUNCTION__);
        return 0;
    }
    
    ALGO_COMP_LOG_D("%s open", __FUNCTION__);

    s_is_gps_data_open = true;

    return algo_topic_list_subscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
}

/**
 * @brief feedgps速度过滤算法
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_gps_data_feed(uint32_t input_type, void *data, uint32_t len)
{
    gps_pub_t *indata = (gps_pub_t *) data;
    gps_pub_t outdata;
    memcpy(&outdata, indata, sizeof(gps_pub_t));
    set_gps_signal(indata->signal);          // 设置gps信号
    activity_simulator_feed_gps(&outdata);   // 模拟器数据
    algo_gps_data_out_callback(&outdata, len);
    return 0;
}

/**
 * @brief 关闭gps速度过滤算法
 *
 * @return int32_t 结果
 */
static int32_t algo_gps_data_close(void)
{
    if (!s_is_gps_data_open)
    {
        ALGO_COMP_LOG_W("%s already close", __FUNCTION__);
        return 0;
    }
    
#ifndef SIMULATOR
    set_gps_signal(0);
#endif   // !SIMULATOR

    algo_topic_list_unsubscribe(s_algo_topic_node, sizeof(s_algo_topic_node) / sizeof(s_algo_topic_node[0]));
    
    s_is_gps_data_open = false;
    ALGO_COMP_LOG_D("%s close", __FUNCTION__);
    return 0;
}

// gps速度过滤算法组件实现
static algo_compent_ops_t s_gps_data_algo = {
    .init = algo_gps_data_init,
    .open = algo_gps_data_open,
    .feed = algo_gps_data_feed,
    .close = algo_gps_data_close,
    .ioctl = NULL,
};

/**
 * @brief gpsgps速度过滤组件注册
 *
 * @return int32_t 结果
 */
int32_t register_gps_data_algo(void)
{
    algo_compnent_register(ALGO_TYPE_GPS_DATA, &s_gps_data_algo);
    return 0;
}
