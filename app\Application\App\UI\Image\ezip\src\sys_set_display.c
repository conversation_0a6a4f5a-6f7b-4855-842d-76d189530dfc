#include "lvgl.h" 

#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP ALIGN(4)
#endif

#ifndef eZIP_RGBARGB888A
#define eZIP_RGBARGB888A
#endif


LV_ATTRIBUTE_MEM_ALIGN_EZIP const  uint8_t sys_set_display_map[] SECTION(".ROM3_IMG_EZIP.sys_set_display") = { 
    0x00,0x00,0x07,0x33,0x46,0x08,0x20,0x00,0x00,0x48,0x00,0x48,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0x03,0x00,0x00,0x00,0x94,0x00,0x00,0x03,0x94,0x00,0x00,0x06,0x4c,
    0x3d,0xab,0x8b,0x8d,0xa2,0xfa,0xe2,0xe7,0xcc,0x6e,0x4b,0x0b,0xff,0x42,0x5b,0xf2,0xaf,0x0d,0x0f,0x88,0xd2,0xa7,0xf2,0xd0,0x26,0x04,0x63,0xc5,0x08,0xbb,0x5d,0x79,
    0x51,0xf0,0x81,0x48,0xa3,0x11,0x1a,0x13,0x41,0x43,0x62,0x82,0x24,0x2a,0x51,0xa4,0x20,0xc4,0x26,0x86,0xf0,0x26,0x20,0x26,0x86,0xf2,0xf1,0x40,0xf5,0x45,0x48,0x48,
    0x94,0xdd,0x6e,0x31,0x69,0x02,0x4a,0x13,0xea,0x07,0xe1,0x01,0x4c,0xe1,0x81,0x40,0xd1,0x2e,0x04,0x65,0x4b,0x5b,0xf6,0x78,0xee,0x76,0x6f,0x77,0x3b,0x9d,0xdd,0x9d,
    0xd9,0xb9,0x33,0xfb,0xe5,0x49,0xee,0xce,0xec,0xec,0xec,0xb9,0xf7,0xfc,0xe6,0x9c,0xdf,0x39,0xe7,0x0e,0x44,0x28,0x20,0x59,0x10,0xa2,0xa5,0x88,0xd0,0x02,0x04,0xcd,
    0x1a,0x42,0x13,0x11,0x2c,0x06,0x0d,0x1a,0x31,0x06,0x0b,0x09,0xa1,0x21,0x71,0x1b,0xf1,0x78,0xc4,0xe3,0x36,0x8f,0x5b,0x3c,0xae,0xf3,0x95,0x2b,0x48,0x30,0x58,0x15,
    0x85,0x81,0x5b,0x6b,0xf1,0xa1,0xca,0x35,0xe5,0x15,0xa0,0xf9,0x61,0x6a,0xf2,0x10,0xac,0x61,0xe3,0x7d,0x6c,0xe0,0xf3,0x7c,0xa9,0xd1,0xae,0x4e,0x46,0x2f,0xc8,0x1f,
    0xa7,0x2b,0x09,0x7a,0x47,0x02,0x78,0xa7,0xe8,0x00,0xaa,0x0d,0xd3,0x12,0x9e,0xb4,0x83,0x0d,0x59,0xcf,0xa0,0xac,0x70,0x78,0xba,0xe3,0xec,0x91,0x07,0x47,0x7d,0x78,
    0xa1,0xe0,0x01,0xaa,0xeb,0xa3,0x97,0xf8,0xb0,0x99,0xc7,0x2b,0x79,0x70,0xd6,0x53,0x1c,0xa6,0x9f,0x8e,0x06,0xf0,0x4a,0xc1,0x01,0x54,0x17,0xa2,0xd7,0x38,0x84,0xb6,
    0xf1,0x44,0xcf,0xe4,0x9b,0xe3,0x98,0xd3,0x3e,0xb9,0xd7,0x8e,0xfb,0x0a,0x02,0xa0,0xba,0x20,0xbd,0x0c,0x1e,0xf8,0x88,0xf9,0xa0,0xad,0x90,0x12,0x01,0x1b,0x1c,0x7a,
    0x1c,0x83,0x2d,0xf7,0x03,0xf8,0x47,0x5e,0x00,0x5a,0x10,0xa4,0xa7,0x35,0x0d,0x3e,0xe3,0xd3,0x0e,0x28,0x50,0x61,0xa3,0x47,0x20,0x06,0x6f,0x70,0xc8,0x9d,0x73,0x15,
    0xa0,0xda,0x10,0xbd,0xcb,0xa4,0xb8,0x9f,0x4f,0x2b,0xa1,0x08,0x44,0x24,0x8b,0x51,0x3f,0xf6,0x3a,0x0e,0xd0,0xff,0x7e,0xa4,0xff,0x7b,0x27,0xe1,0x10,0x2b,0x5b,0x0f,
    0x45,0x26,0xd9,0x40,0x42,0x05,0xc5,0x9d,0x9f,0x8b,0xba,0xaf,0xf9,0xf4,0x49,0x28,0x52,0xe1,0x0c,0xb7,0x26,0x5d,0xb8,0xd9,0x02,0xa8,0x3e,0x4c,0x6f,0x71,0x66,0xf8,
    0x0a,0x8a,0x5c,0x04,0x27,0x31,0x71,0xb7,0x19,0x11,0xb7,0x96,0x73,0x96,0x0a,0xd3,0xc7,0xa5,0x00,0x4e,0xa2,0xfa,0x6e,0xf0,0x68,0x70,0xc4,0xe8,0x37,0x2d,0xc7,0xda,
    0xa6,0x9b,0xb5,0xee,0x2b,0x05,0x70,0x52,0x40,0x6a,0xe7,0x24,0xb3,0xd3,0x76,0x88,0xc5,0xc1,0x41,0xd8,0x01,0x25,0x2a,0xcc,0x47,0xcb,0x52,0x2b,0x6e,0xcd,0x6a,0x58,
    0x95,0x32,0x38,0x71,0x4f,0xd2,0x60,0x57,0x4e,0x1e,0x54,0x2a,0x84,0x6c,0xca,0x8b,0x10,0xda,0x64,0x83,0xab,0x99,0x4d,0xe5,0x6e,0x82,0x33,0x79,0xb9,0x3f,0xdf,0x3d,
    0xdb,0x56,0xd3,0x1e,0x24,0x8a,0xc0,0x8a,0x49,0xf8,0xd9,0xad,0x3a,0x27,0xe2,0x4f,0x2e,0xa9,0xe6,0x40,0x18,0xbc,0xad,0xab,0xf3,0x02,0x52,0x45,0x0c,0x1a,0xc5,0x7e,
    0x52,0x56,0x0f,0x12,0x15,0xb2,0x5b,0xe0,0x44,0x7b,0x76,0xcf,0xfc,0x7e,0x6c,0x4f,0xde,0xbc,0x68,0x1c,0x61,0x43,0xd6,0x10,0x8b,0xf7,0x56,0x45,0xd8,0x3e,0x28,0xaa,
    0x1e,0xd7,0x65,0x04,0x48,0x74,0xe5,0x0c,0xce,0xfe,0xb2,0x04,0x67,0x8a,0x7b,0x02,0x8b,0xce,0xd0,0x5c,0x6f,0xba,0x1b,0x12,0x5b,0x16,0x95,0xe5,0x0a,0x90,0x90,0xb1,
    0x6a,0x58,0xa9,0xa5,0xdd,0xec,0x52,0xb0,0x9f,0x23,0x38,0x45,0x90,0xae,0x9e,0x5b,0x9c,0xc8,0x7a,0x0f,0xb6,0xfb,0xe2,0xf3,0xa8,0xcc,0x80,0x84,0xb0,0xdc,0xd8,0x83,
    0xa6,0x76,0x02,0x6d,0x83,0x33,0xd6,0x33,0x45,0xb2,0xf2,0x58,0xdd,0x99,0x19,0xa8,0x8a,0x96,0xd5,0x30,0x06,0x49,0x62,0xf6,0xb6,0xac,0x32,0x0d,0x8e,0x3c,0x9f,0x6c,
    0x3d,0x0f,0x35,0xaa,0x32,0x1f,0x42,0xb3,0xc7,0x68,0x0f,0x99,0x0f,0xdb,0x6c,0xbb,0x27,0x67,0xa0,0xd8,0xed,0xe1,0xa4,0x21,0x43,0xe7,0xa7,0x40,0xc8,0xb0,0x78,0xad,
    0x71,0xc9,0xf4,0xbd,0x22,0xbd,0xcf,0xfb,0xf0,0xa8,0x69,0x70,0xa4,0x88,0x39,0xb3,0x3d,0x08,0x0b,0xf2,0xcf,0xac,0x3a,0xa8,0xb6,0x8f,0x2e,0xaa,0xd8,0x60,0x37,0x5a,
    0xbc,0x90,0xaa,0xce,0x2e,0x25,0x06,0x38,0xad,0x3f,0x21,0xc3,0x1e,0xfd,0xab,0x19,0x06,0xe7,0x7d,0x15,0x9a,0x53,0xbd,0x61,0x86,0x61,0xfc,0x5d,0x84,0x92,0xfc,0x3d,
    0xe7,0x47,0xfb,0xf9,0x9b,0x33,0x3c,0xd4,0x01,0x70,0xe2,0x2a,0xf5,0x1c,0xb4,0x59,0xa5,0x76,0xb9,0x58,0xc9,0x41,0x52,0x26,0x86,0xfa,0x67,0x55,0xc8,0xc2,0x23,0xc4,
    0xf5,0x54,0x40,0x25,0x07,0x19,0x19,0xad,0x27,0x63,0x07,0xc0,0x11,0x32,0xc7,0xa3,0x7b,0xe3,0x79,0x48,0x79,0xc9,0x9e,0x00,0x42,0x1a,0xae,0x37,0x44,0x18,0x7a,0xff,
    0xf5,0xa7,0x60,0xfc,0xfb,0x9e,0xf8,0x3d,0xc2,0x2b,0xe4,0x10,0xdf,0xc5,0x90,0x00,0xeb,0xf9,0x2b,0x9d,0x4e,0xb5,0x7d,0x6b,0x92,0x9c,0x77,0x30,0x6b,0x77,0x3b,0x9d,
    0x8e,0x53,0x3d,0x47,0x70,0x88,0x95,0xb4,0x2c,0xfe,0x5b,0xbd,0xa9,0x6b,0x86,0x0e,0xbd,0x4e,0x07,0x0a,0xc6,0x69,0x0f,0xfa,0xc9,0x85,0x77,0xe5,0x86,0x4d,0xa9,0x55,
    0x71,0xb3,0x89,0x8d,0x17,0x8a,0xf3,0xc3,0xd4,0xe4,0x26,0x38,0x76,0x0b,0x47,0x37,0x9b,0xd8,0x7f,0x01,0x44,0x60,0xa9,0xeb,0xa3,0xad,0x7c,0xf8,0x02,0x5c,0x90,0x68,
    0xcf,0x6e,0x18,0xeb,0xd9,0x63,0x5b,0x4f,0x55,0x67,0x17,0x54,0x77,0xee,0x76,0x7c,0xbd,0x9a,0xf8,0x20,0x00,0x3f,0xb8,0x24,0x2a,0xc0,0x91,0x7a,0x26,0x2f,0xf7,0xbb,
    0x03,0x10,0xbb,0xd1,0x4a,0x37,0xc0,0x51,0x6d,0xd0,0xc4,0x90,0xf3,0x00,0x79,0x17,0x84,0x68,0x29,0x1f,0x1b,0x55,0x18,0x6f,0xb4,0xe0,0xd4,0x30,0x50,0x6d,0xd0,0xe4,
    0xd0,0x79,0xcb,0x6b,0xb0,0x0c,0x10,0x22,0xb4,0xd8,0x5d,0x68,0xc4,0x8f,0x19,0x8d,0xa8,0x39,0x10,0x76,0xdc,0x23,0x33,0x71,0x9b,0xb8,0x2e,0xd6,0xe0,0x6d,0x5d,0x6d,
    0x99,0x32,0x35,0x26,0xa0,0x66,0x27,0xc3,0x26,0xf5,0x77,0x55,0xfc,0xe3,0x5a,0x38,0x12,0x44,0x34,0x0d,0xa1,0xc9,0xc9,0x85,0x89,0x6c,0x33,0xed,0xae,0xd6,0x9f,0xa0,
    0x69,0xa9,0x68,0x71,0x40,0x37,0xc2,0xb0,0x97,0x10,0x16,0x8b,0x34,0x96,0x73,0x8c,0xb2,0xd1,0x12,0x04,0x3d,0x27,0xe8,0x17,0xee,0x6d,0x59,0xa5,0x94,0xa8,0x53,0x01,
    0x97,0xeb,0x30,0x5a,0x43,0xf5,0xa6,0xae,0xdc,0x1e,0x0e,0xc1,0x0d,0xaf,0x0a,0x82,0x36,0x4b,0x82,0x02,0xac,0x31,0x50,0x17,0x66,0x02,0x70,0x55,0x64,0x6c,0xe8,0x40,
    0x08,0xd7,0x34,0x24,0x58,0x08,0xff,0x49,0x1a,0x07,0x82,0xab,0x1a,0x7f,0x34,0xb8,0x35,0x61,0x6a,0x38,0x16,0x45,0x25,0xed,0x81,0x5f,0x91,0xdb,0x0c,0x72,0xfb,0xc9,
    0x3c,0xd8,0xee,0xb3,0xcd,0x45,0xbc,0x6e,0xc7,0xbb,0x22,0x2e,0x5f,0xe6,0x6a,0x53,0x9e,0xe4,0xae,0x08,0xd2,0x54,0x95,0x19,0x1d,0x94,0x8b,0xb2,0xd5,0x78,0xe4,0x36,
    0x40,0x76,0x42,0xcd,0xad,0x26,0x95,0xdd,0x66,0x40,0x02,0x74,0xdb,0x8d,0x1e,0x4c,0x84,0x95,0xa8,0xb8,0x45,0xc5,0x2b,0x33,0x8e,0x08,0x13,0xb3,0x40,0x09,0x50,0x45,
    0x35,0x2c,0xc1,0x11,0x7a,0xf4,0x3a,0x95,0x36,0xa9,0x04,0x71,0x0e,0x10,0x1c,0x24,0x90,0x7a,0xce,0x69,0x70,0x52,0x45,0x5f,0xf6,0x4b,0x03,0x45,0x0d,0x93,0xca,0x4d,
    0xf2,0x1e,0xa3,0x3a,0x46,0xdf,0xde,0xa8,0xf6,0x2c,0xd6,0x1f,0x9f,0x40,0xd4,0x41,0xd7,0x9d,0x02,0xc8,0x08,0x1c,0x59,0xf6,0xa7,0x1a,0x9c,0x8b,0x61,0xe2,0xff,0x46,
    0x6d,0x8c,0x22,0x90,0x7a,0x93,0xdb,0x1d,0x04,0x57,0xdc,0x04,0x47,0xd5,0x93,0xd6,0x17,0x89,0x12,0x24,0x25,0xe1,0x16,0x83,0xd3,0xd3,0x00,0x71,0xa1,0x38,0xe8,0x56,
    0x73,0x68,0x06,0x1c,0x09,0x6c,0x36,0x43,0x85,0x1e,0x23,0xfe,0xb2,0xdd,0x10,0x13,0xdc,0x8c,0x04,0xf0,0xe4,0xf4,0x83,0xa8,0x8a,0xc2,0x40,0x74,0x9e,0x03,0x1e,0xa4,
    0xeb,0x89,0xac,0x80,0x23,0xcf,0x45,0x6b,0x92,0xa9,0x87,0x92,0xfa,0x14,0xef,0x12,0x74,0xcf,0x20,0xeb,0x5b,0x6b,0xf1,0x21,0x17,0x42,0x41,0x27,0x6a,0x1d,0x69,0x9c,
    0xd9,0xb0,0xd2,0x7b,0x5d,0xf4,0xd8,0x1e,0x53,0x7d,0xa0,0xf4,0x24,0x99,0xe9,0x72,0x6f,0xde,0xe1,0x52,0xa4,0x1d,0x0f,0xcf,0x08,0xe5,0x84,0x5b,0x9d,0xe6,0x5f,0x03,
    0xaa,0x6b,0x9d,0x1a,0x07,0xb7,0x37,0xf4,0x20,0xa9,0xe0,0x35,0x76,0x94,0x2e,0xc3,0x3d,0xe9,0x4a,0x4a,0xb2,0x76,0xb9,0x0a,0x73,0xf1,0x51,0x4e,0xed,0x67,0x0d,0x01,
    0x1a,0x09,0xe0,0x1d,0x3e,0x1c,0x2f,0x5f,0x74,0xe0,0xee,0x78,0x05,0x7c,0x90,0xf6,0xad,0x46,0x62,0xef,0xe3,0x60,0xbe,0xd7,0xa9,0xdf,0x15,0x34,0x4a,0xe5,0x8e,0xc8,
    0x63,0xd8,0xfe,0xf7,0x0b,0x78,0x37,0x23,0x40,0xa3,0x3e,0xbc,0xc0,0x87,0x53,0x65,0xe8,0x3d,0x5f,0x72,0x5a,0x3f,0x91,0x81,0xb8,0x93,0x52,0x1f,0xa4,0x66,0xd2,0xe0,
    0xf7,0x7c,0xae,0x57,0xd6,0x3f,0xd9,0x52,0xbc,0x22,0x19,0x8c,0xf8,0x60,0x05,0x87,0x0f,0x99,0x02,0x48,0x48,0x6d,0x88,0x76,0x72,0xb8,0xed,0x2d,0x93,0x1d,0xc3,0xd6,
    0x7b,0x7e,0x1c,0xca,0x92,0xfa,0x67,0x4b,0x7d,0x1f,0x05,0xf9,0xcf,0xed,0x25,0x1d,0x59,0x08,0xaf,0x32,0xad,0x7c,0x6b,0xea,0xd5,0xf3,0x2c,0xce,0x8a,0xc1,0x16,0x46,
    0x6e,0xa4,0x64,0x3d,0x87,0xe0,0x3d,0x33,0xe0,0xa4,0xf5,0xa0,0x04,0x1f,0xbd,0xc8,0x7c,0xf4,0x43,0xb9,0x7a,0x4e,0x46,0x0f,0x8a,0x67,0xb5,0x00,0x9e,0x63,0xf4,0x3a,
    0x4a,0x08,0x9b,0x41,0xc1,0x39,0x56,0xc0,0x11,0xf2,0x2f,0x00,0x45,0xc8,0x22,0xf5,0x7d,0xb4,0x81,0x00,0x4e,0x41,0x71,0xcb,0x91,0x88,0x0f,0xde,0x01,0x44,0xb2,0xfa,
    0x47,0x2d,0xdb,0x0d,0xa3,0x7e,0xec,0xc5,0x18,0xac,0x61,0x24,0x47,0x8a,0x10,0x98,0x3f,0x21,0x06,0x1b,0x23,0x7e,0x7c,0x3b,0x17,0x70,0x4c,0x01,0x14,0x07,0x29,0x80,
    0xe7,0x1e,0xc7,0xa0,0x8d,0x41,0x0a,0x15,0x0b,0x32,0x88,0x70,0x74,0x62,0x02,0x96,0x45,0x02,0x78,0xc2,0x96,0x1e,0xab,0x7f,0xa8,0x0d,0xd1,0x4e,0x9e,0x7c,0x6f,0xc1,
    0x02,0x03,0x70,0x89,0x5d,0xa5,0x8b,0xbd,0xe6,0xac,0x22,0x7d,0xd6,0xa5,0x3e,0x48,0xcd,0xa4,0xc1,0x2e,0x3e,0xed,0x28,0x18,0x64,0x08,0x6e,0xf2,0x67,0x77,0xa4,0x1d,
    0x0f,0x2b,0x06,0x3c,0x77,0xa9,0x0f,0xd3,0xb3,0x44,0xb0,0x95,0x4f,0x37,0xe6,0x0d,0x17,0x82,0x6f,0x98,0x5d,0xbe,0xe3,0x50,0x3a,0xe9,0x90,0x47,0xda,0x97,0x86,0x20,
    0x3d,0x31,0x8e,0xb0,0x81,0xb5,0xad,0x63,0x85,0x01,0x07,0xf1,0x88,0xf2,0xb8,0xc8,0xde,0x32,0xa0,0x11,0xf4,0xff,0x15,0xc0,0xa0,0x0b,0x21,0xab,0x56,0x16,0x9d,0xa1,
    0xb9,0x63,0xd5,0xb0,0x92,0x10,0x96,0xb3,0xf6,0x66,0xbe,0xb4,0x54,0x5c,0xe6,0xd1,0xc8,0x63,0x8e,0x89,0x39,0xa3,0x0c,0x40,0x84,0xef,0x1a,0xe6,0xf3,0x1b,0x7c,0xf3,
    0x35,0xe6,0x94,0xab,0x9c,0x8d,0x7e,0x63,0x2f,0xf9,0xc5,0x6d,0x0f,0xfd,0x17,0xfc,0x0d,0xeb,0xe9,
};


#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER ALIGN(4)
#endif
LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER const eZIP_RGBARGB888A  lv_img_dsc_t sys_set_display SECTION(".ROM3_IMG_EZIP_HEADER.sys_set_display") = { 
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .header.always_zero = 0,
  .header.w = 72,
  .header.h = 72,
  .data_size  = 1843,
  .data = sys_set_display_map
};
