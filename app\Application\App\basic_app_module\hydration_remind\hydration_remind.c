/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   hydration_remind.c
@Time    :   2025/08/06 11:13:40
*
**************************************************************************/
#include "hydration_remind.h"
#ifndef SIMULATOR
#include "algo_service_sport_status.h"
#include "app_sensor_hub_service.h"
#include "cfg_header_def.h"
#include "gui_event_service.h"
#include "igs_dev_config.h"
#include "kvdb.h"
#include "service_gui_health.h"
#include "service_health.h"
#include "qw_system_params.h"
#include "sensorhub_common.h"
#include "service_config.h"
#include "service_datetime.h"
#include "service_event.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"
#include "alarm_clock_app/alarm_manager.h"
#endif

#ifndef SIMULATOR
static alarm_id_t hydration_alarm;
#endif

static void hyration_delete_timer_task(void);
static void hyration_create_timer_task(void);

#ifndef SIMULATOR
/************************************************************************
 * @function: is_in_time_period
 * @brief:    检查当前时间是否在指定时间段内（支持跨天）
 * @param:    current_time - uint16_t, 输入, 当前时间（分钟表示）
 * @param:    start_time   - uint16_t, 输入, 时间段开始时间（分钟）
 * @param:    end_time     - uint16_t, 输入, 时间段结束时间（分钟）
 * @return:   bool - true:在时间段内, false:不在时间段内
 *************************************************************************/
static inline bool is_in_time_period(uint16_t current_time, uint16_t start_time, uint16_t end_time)
{
    if (start_time <= end_time)
    {
        /* 不跨天情况 */
        return ((current_time > start_time) && (current_time < end_time));
    }
    else
    {
        /* 跨天情况 */
        return ((current_time > start_time) || (current_time < end_time));
    }
}

/************************************************************************
 * @function: hydration_remind_valid
 * @brief:    判断是否应该触发喝水提醒
 * @return:   bool - true:需要提醒, false:不需要提醒
 * @note:     本函数每分钟调用一次，自动维护提醒间隔计时
 *************************************************************************/
static bool hydration_remind_valid(void)
{
    config_hydration_t *hydration_config = &get_cfg()->hydration_cfg;

    qw_tm_t cur_time = {0};
    service_datetime_get(&cur_time);
    uint16_t current_time = cur_time.tm_hour * 60 + cur_time.tm_min;
    HYDRATION_LOG_D("hydration_remind_valid: current_time:%d freq:%d sw:%d",current_time,hydration_config->hydration_freq,hydration_config->hydration_switch);
    HYDRATION_LOG_D("start:%d end:%d",hydration_config->hydration_starttime,hydration_config->hydration_endtime);
    HYDRATION_LOG_D("midday: start:%d end:%d sw:%d",hydration_config->middayrest_starttime,hydration_config->middayrest_endtime,hydration_config->middayrest_switch);


    /* 喝水提醒开关关闭 */
    if (!hydration_config->hydration_switch)
    {
        HYDRATION_LOG_D("hydration_remind_valid: sw close");
        return false;
    }

    /* 检查是否在有效提醒时间段 */
    if (!is_in_time_period(current_time, hydration_config->hydration_starttime, hydration_config->hydration_endtime))
    {
        HYDRATION_LOG_D("hydration_remind_valid not in time");
        return false;
    }

    /* 检查是否在午休时间段 */
    if (hydration_config->middayrest_switch && is_in_time_period(current_time, hydration_config->middayrest_starttime, hydration_config->middayrest_endtime))
    {
        HYDRATION_LOG_D("hydration_remind_valid in reset_time");
        return false;
    }

    /* 进入有效提醒区域，更新计时器 */

    HYDRATION_LOG_D("hydration_remind_valid true");
    return true;
}

static void hydration_remind_nofity(rt_alarm_t alarm, time_t timestamp)
{
    hyration_delete_timer_task();
    hyration_create_timer_task();
    if ((enum_status_free == get_sport_status() || enum_status_ready == get_sport_status())
            && service_gui_get_sleep_status() != SLEEP_ENTER_EVENT)
    {
        bool hydration_valid = hydration_remind_valid();
        if (hydration_valid)
        {
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_DRINK_WATER, NULL);
        }
    }
    else
    {
        HYDRATION_LOG_D("hydration nofity erro : IN SPORT OR SLEEP");
    }
}

#endif

void hydration_remind_subscribe(void)
{
    #ifndef SIMULATOR
    hyration_create_timer_task();
    HYDRATION_LOG_D("hydration_remind_subscribe");
    #endif
}

#ifdef  IGS_DEV
static alarm_id_t hydration_alarm;
#endif

//创建一个定时任务（利用闹钟）
static void hyration_create_timer_task()
{
    #ifdef  IGS_DEV
    qw_tm_t now_tm = {0};
    service_datetime_get(&now_tm);
    uint32_t cur_time = now_tm.tm_hour * 3600 + now_tm.tm_min * 60;

    config_hydration_t *hydration_config = &get_cfg()->hydration_cfg;
    uint32_t start_time = hydration_config->hydration_starttime * 60;
    uint32_t end_time = hydration_config->hydration_endtime * 60;
    uint32_t next_time = cur_time + hydration_config->hydration_freq * 60;

    // 如果当前时间不在提醒时间范围内
    if (!is_in_time_period(cur_time / 60, hydration_config->hydration_starttime, hydration_config->hydration_endtime)) {
        // 如果当前时间小于开始时间，则设置闹钟到开始时间
        if (cur_time < start_time) {
            next_time = start_time;
        }
        // 如果当前时间大于结束时间，则设置闹钟到第二天的开始时间
        else {
            next_time = start_time;
        }
    }
    // 如果在提醒时间范围内，但下一次提醒时间超出范围
    else if (next_time > end_time) {
        // 如果开始时间小于结束时间（不跨天），则设置闹钟到第二天的开始时间
        if (start_time < end_time) {
            next_time = start_time;
        }
        // 如果开始时间大于结束时间（跨天），则设置闹钟到当天的开始时间
        else {
            next_time = start_time;
        }
    }

    hydration_alarm = alarm_manager_create(next_time, 0x80, hydration_remind_nofity);
    alarm_manager_enable(hydration_alarm, true);

    HYDRATION_LOG_D("now : %d ,next remind time :%d", cur_time, next_time);
#endif
}

//删除一个定时任务（利用闹钟）
static void hyration_delete_timer_task()
{
#ifdef  IGS_DEV
    alarm_manager_enable(hydration_alarm, false);
    alarm_manager_delete(hydration_alarm);
#endif
}

void hyration_remind_reset()
{
    hyration_delete_timer_task();
    hydration_remind_subscribe();
}