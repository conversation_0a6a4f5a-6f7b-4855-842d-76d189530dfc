/************************************************************************
*
* Copyright(c) 2025, igpsport Software Co., Ltd.
* All Rights Reserved.
* @File    :   alg_grade.c
*
**************************************************************************/
#include <math.h>
#include <stddef.h>
#include <string.h>
#include <stdbool.h>
#include "alg_grade.h"

//计算海拔变化方向
//1 - 增加（上坡）
//0 - 平路
//-1 - 减小（下坡）
static inline int dalt_dir_calc(float dalt)
{
    if (dalt > 0.01f)
    {
        return 1;
    }
    else if (dalt < -0.01f)
    {
        return -1;
    }
    else
    {
        return 0;
    }
}

//计算坡度方向
//0 - 平路
//1 - 上坡
//-1 - 下坡
int grade_dir_calculator_exec(GradeDirCalculator *self, float dalt)
{
    if (self == NULL)
    {
        return 0;
    }

    //对海拔变化量进行平滑
    dalt = maf_f32_exec(&self->dalt_maf, dalt);

    //将海拔变化量填入缓冲区
    if (self->len < self->capacity)
    {
        self->buf[self->len] = dalt;
        self->len += 1;
    }
    else
    {
        for (uint32_t i = 0; i < self->capacity - 1; i++)
        {
            self->buf[i] = self->buf[i+1];
        }
        self->buf[self->capacity-1] = dalt;
    }

    //缓冲区未满，则直接判定为平路
    if (self->len < self->capacity)
    {
        return 0;
    }

    //检查缓冲区中的海拔变化量是否是相同方向
    int is_all_same = true;
    const int start_dir = dalt_dir_calc(self->buf[0]);

    for (uint32_t i = 1; i < self->capacity; i++)
    {
        if (dalt_dir_calc(self->buf[i]) != start_dir)
        {
            is_all_same = false;
            break;
        }
    }

    //缓冲区中海拔变化方向相同，则输出坡度方向，否则判定为平路
    if (is_all_same == true)
    {
        return start_dir;
    }
    else
    {
        return 0;
    }
}

//重置坡度方向计算
void grade_dir_calculator_reset(GradeDirCalculator *self)
{
    if (self != NULL)
    {
        self->len = 0;
        maf_f32_reset(&self->dalt_maf);
    }
}

//坡度计算
//0 - 计算成功
//-1 - 输入无效
int grade_calculator_exec(GradeCalculator *self, const GradeCalcInput *input, GradeCalcOutput *output)
{
    if (self == NULL || input == NULL || output == NULL)
    {
        return -1;
    }

    output->grade = 0.0f;
    output->dalt = 0.0f;

    if (input->alt < -999.0f)
    {
        grade_calculator_reset(self);
        return 0;
    }

    if (self->cnt == 0)
    {
        memcpy(&self->last, input, sizeof(GradeCalcInput));
        self->cnt += 1;
        return 0;
    }

    //检查时间戳
    if (input->timestamp < self->last.timestamp || input->timestamp - self->last.timestamp > 30)
    {
        grade_calculator_reset(self);
        return 0;
    }

    //检查输入距离
    if (input->dist < self->last.dist)
    {
        grade_calculator_reset(self);
        return 0;
    }

    //计算差分数据
    const float dd = input->dist - self->last.dist;
    const float dalt = input->alt - self->last.alt;

    memcpy(&self->last, input, sizeof(GradeCalcInput));
    self->cnt += 1;

    if (dd < 0.01f || fabsf(dalt / dd) > 0.4f)
    {
        if (self->timecnt < self->timeout)
        {
            self->timecnt += 1;
            output->grade = self->grade;
            return 0;
        }
        else
        {
            grade_calculator_reset(self);
            return 0;
        }
    }
    else
    {
        self->timecnt = 0;
    }

    //差分数据填入缓冲区
    if (self->len < self->capacity)
    {
        self->buf[self->len].dd = dd;
        self->buf[self->len].dalt = dalt;
        self->len += 1;
    }
    else
    {
        for (uint32_t i = 0; i < self->capacity - 1; i++)
        {
            memcpy(&self->buf[i], &self->buf[i+1], sizeof(GradeDiffData));
        }
        self->buf[self->capacity-1].dd = dd;
        self->buf[self->capacity-1].dalt = dalt;
    }

    //计算本次坡度方向
    const int grade_dir = grade_dir_calculator_exec(&self->dir_calculator, dalt);

    if (grade_dir == 0)
    {
        self->grade = 0.0f;
        return 0;
    }

    float dd_sum = 0.0f;
    float dalt_sum = 0.0f;

    for (uint32_t i = 0; i < self->len; i++)
    {
        dd_sum += self->buf[i].dd;
        dalt_sum += self->buf[i].dalt;
    }

    //计算平均坡度
    const float avg_grade = dalt_sum / dd_sum;

    if (fabsf(avg_grade) < 0.005f)
    {
        self->grade = 0.0f;
        return 0;
    }

    output->grade = avg_grade;
    output->dalt = dalt;

    self->grade = output->grade;

    return 0;
}

//重置坡度计算
void grade_calculator_reset(GradeCalculator *self)
{
    if (self != NULL)
    {
        self->last.timestamp = 0;
        self->last.dist = 0.0f;
        self->last.alt = 0.0f;
        self->len = 0;
        self->grade = 0.0f;
        self->timecnt = 0;
        self->cnt = 0;
        grade_dir_calculator_reset(&self->dir_calculator);
    }
}
