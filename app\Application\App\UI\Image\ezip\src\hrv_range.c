#include "lvgl.h" 

#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP ALIGN(4)
#endif

#ifndef eZIP_RGBARGB888A
#define eZIP_RGBARGB888A
#endif


LV_ATTRIBUTE_MEM_ALIGN_EZIP const  uint8_t hrv_range_map[] SECTION(".ROM3_IMG_EZIP.hrv_range") = { 
    0x00,0x00,0x0a,0xcf,0x46,0x08,0x20,0x00,0x01,0x52,0x00,0x20,0x00,0x00,0x00,0x00,0x00,0x20,0x00,0x01,0x00,0x00,0x00,0x90,0x3d,0xf3,0x8f,0x91,0xaa,0xba,0xe2,0xf8,
    0x39,0x67,0x10,0xb1,0x52,0x2c,0x48,0xd5,0xa2,0x94,0xa2,0x52,0x2a,0x01,0x4a,0xdd,0x20,0x74,0xc5,0xd5,0x2d,0x6e,0xc1,0x45,0x60,0x67,0x34,0x58,0x13,0x5a,0x12,0x53,
    0x5b,0x9b,0xfe,0x32,0x36,0xf6,0x47,0x48,0x6c,0x63,0x4c,0x0d,0x36,0xa1,0x8d,0xa9,0x21,0x31,0x35,0xb1,0x25,0xa4,0xf5,0x47,0x99,0x85,0xc5,0x2d,0x88,0xb0,0x16,0x11,
    0x41,0x82,0x52,0x4b,0x4b,0xb1,0x91,0x60,0xb4,0xd2,0xa2,0x56,0x5a,0xc4,0xd2,0xb2,0x73,0x4f,0xbf,0x6f,0x66,0xc0,0x7d,0xef,0x9e,0xc1,0xc9,0x3e,0xfe,0x3c,0x93,0xf0,
    0x66,0xf6,0x7e,0xde,0xbd,0xf7,0x0d,0x7f,0x7c,0xe6,0x7b,0xcf,0x01,0x00,0x00,0x00,0xe5,0xeb,0xc6,0x1c,0x57,0x66,0x9e,0x4f,0xf5,0x97,0xaa,0xf6,0xe0,0xed,0x3e,0x8c,
    0x3d,0x43,0x03,0x5e,0xf5,0x71,0x1a,0x78,0xef,0xa9,0xc6,0x9d,0x39,0x73,0xe6,0xec,0x34,0x31,0xcb,0x47,0x93,0xf0,0xb6,0x8c,0x55,0xbf,0x44,0x22,0xef,0x0c,0x64,0x81,
    0xe8,0x26,0x8c,0x2f,0x6e,0xb4,0x9e,0x2a,0xaf,0x14,0xa1,0x47,0x53,0x20,0xd0,0x48,0x65,0xfd,0x15,0x93,0x7c,0x97,0x58,0xf7,0xa6,0x90,0x6a,0x1b,0x13,0xdd,0x79,0xaa,
    0x67,0x97,0xf4,0x20,0x2d,0xa8,0x7f,0xa1,0xd4,0x43,0xe3,0xef,0xf1,0xd5,0x0f,0xa2,0x5f,0x69,0x66,0xdc,0x99,0x33,0x67,0xce,0xf2,0xb2,0x46,0x3e,0xaa,0xbf,0x96,0x11,
    0xd3,0xe3,0x59,0x89,0x42,0x68,0xc3,0x12,0x89,0xe2,0xe3,0x0f,0xa3,0xf5,0x98,0xa7,0x57,0x3f,0x14,0xa8,0x1c,0x33,0x2a,0xd6,0x1e,0x28,0x2d,0xd1,0xea,0x10,0x24,0x8a,
    0x87,0x58,0x91,0x1d,0x17,0xe2,0x8b,0x06,0xec,0x9b,0x5a,0xee,0x56,0x56,0xb9,0xd7,0xb0,0x2e,0x36,0xe1,0x75,0x60,0x07,0x9b,0x19,0x77,0xe6,0xcc,0x99,0xb3,0xbc,0xac,
    0x91,0x8f,0x02,0xe9,0x94,0x9a,0xf3,0x78,0x75,0xc4,0x34,0x94,0xea,0xf2,0x7d,0x21,0x5a,0x2e,0x84,0xbb,0x98,0xf4,0x61,0x51,0x3a,0x9e,0x5e,0x2f,0x8c,0xc6,0x5e,0x45,
    0x66,0xfa,0x4e,0x3c,0x45,0xdb,0xeb,0x32,0xed,0xb5,0xf7,0xe2,0x0d,0x29,0x91,0xc2,0xfe,0x35,0x23,0x8b,0x6e,0xcb,0xac,0x34,0x01,0x5f,0x36,0x59,0xac,0xdc,0xd4,0xb8,
    0x33,0x67,0xce,0x9c,0xe5,0x64,0x0d,0x7d,0x54,0x15,0x28,0xfd,0x18,0x77,0x3c,0x02,0xbb,0x1d,0x49,0x8f,0x57,0x86,0x2b,0xf1,0xcd,0xc4,0xb2,0x34,0x5a,0x2f,0xe8,0xcc,
    0x9a,0xf0,0x24,0x92,0x2f,0x64,0x5d,0xdb,0x8b,0xf8,0xe5,0x38,0x31,0xd3,0x1d,0x4a,0xfa,0x40,0xf4,0xec,0xc2,0xe3,0x94,0xa8,0x03,0xc2,0xae,0x3e,0xfb,0x90,0xda,0x2e,
    0x08,0xbb,0xa4,0xb7,0x30,0xcb,0x3d,0xc9,0x1f,0x99,0x95,0x8a,0x2c,0xbc,0x06,0xc3,0x87,0x9a,0x1a,0x77,0xe6,0xcc,0x99,0xb3,0x3c,0xec,0x54,0x3e,0x22,0x9d,0x56,0x3b,
    0x9e,0x4b,0x39,0x29,0x86,0x66,0x8e,0xee,0xc5,0xe4,0x76,0xa4,0xce,0x97,0xe2,0xbd,0x68,0x29,0xc6,0x1f,0x0a,0xcc,0xa9,0x05,0x91,0x2a,0xcf,0x67,0xe2,0x05,0xac,0x7a,
    0x7b,0x62,0xcd,0x8c,0xe9,0xaf,0x4d,0x26,0x0a,0xcb,0xfa,0x48,0xcc,0x15,0x24,0x69,0xe5,0x5e,0x58,0xf8,0x6f,0x27,0x13,0x29,0x56,0xae,0x1b,0x59,0x77,0xa4,0xbf,0x0f,
    0x4f,0x04,0xbb,0x0a,0x5b,0x97,0x9b,0x19,0x77,0xe6,0xcc,0x99,0xb3,0xfc,0xcc,0xf6,0xd1,0xfb,0x49,0x95,0x57,0x71,0xa0,0xa3,0xe9,0xc4,0x59,0x39,0x47,0x95,0x17,0x41,
    0x85,0x3f,0x30,0xca,0x07,0xb3,0x6a,0x32,0x95,0xee,0x38,0x8d,0x6a,0x72,0x3c,0xdf,0x4c,0x22,0xaf,0xc4,0xf3,0xf8,0xdb,0x90,0xef,0xfd,0x71,0xc5,0x81,0x2f,0xc6,0x65,
    0x36,0x0f,0x48,0xd2,0xf5,0xa3,0xbd,0x2e,0x21,0x09,0x77,0xc7,0x5f,0xa8,0x82,0x4d,0x42,0x19,0x5f,0xec,0xed,0x66,0xc6,0x9d,0x39,0x73,0xe6,0x2c,0x0f,0x0b,0x4c,0x43,
    0x1b,0xf9,0x08,0x02,0x6b,0xc1,0xf5,0x72,0xe2,0x4a,0x24,0x44,0x92,0x42,0x11,0xe1,0xf1,0x39,0xa4,0xca,0x3d,0x46,0x63,0xea,0x7b,0x18,0x7f,0x30,0x0e,0xa9,0x3c,0x06,
    0x72,0xed,0xc4,0x5e,0x56,0x69,0x61,0x4e,0x5d,0xbe,0x1b,0xe3,0xc7,0x48,0x9e,0x9d,0x7a,0x60,0xcf,0xbf,0x67,0x45,0xba,0x9b,0xb5,0xb0,0x33,0x63,0xf1,0x49,0xac,0xdc,
    0x2a,0x4a,0xab,0x9b,0x19,0x77,0xe6,0xcc,0x99,0xb3,0xbc,0x4c,0xaa,0x89,0x33,0xf6,0xd1,0x09,0xf9,0x62,0xee,0x4a,0xa6,0xc2,0xb1,0x4c,0x82,0x1d,0x85,0x45,0x6f,0xb0,
    0xd2,0x2d,0xac,0xd7,0x56,0x6f,0x16,0xf5,0x34,0x68,0x74,0x6d,0x64,0x95,0x03,0x46,0xa3,0xeb,0x1b,0xcc,0xb2,0x3c,0x2e,0xeb,0x86,0x09,0x58,0xf3,0x6a,0x24,0xd5,0xd4,
    0x5e,0x35,0x91,0x72,0xdc,0xfd,0xc2,0x2e,0x25,0x65,0x7e,0x4c,0xa5,0x70,0xb8,0xa9,0x71,0x67,0xce,0x9c,0x39,0xcb,0xc1,0x20,0xbb,0xb3,0x20,0xb7,0xc5,0x96,0x8f,0x94,
    0xf4,0x0a,0xdc,0x31,0x95,0x87,0xc4,0xcd,0x22,0xc0,0x22,0x64,0xba,0x15,0xf3,0xf6,0xc6,0x1e,0xc5,0xd1,0x9d,0x79,0x85,0x51,0x5a,0x18,0x8b,0x35,0xe7,0x52,0xbd,0x59,
    0x94,0x11,0x73,0x67,0xfd,0x53,0x9f,0x51,0x6b,0x4d,0x4a,0x01,0xdd,0x48,0xaa,0x6f,0x46,0x22,0x45,0xc4,0x7d,0x31,0xf3,0xd0,0x53,0xb1,0xc1,0x0c,0xd2,0x4a,0xb9,0x99,
    0x71,0x67,0xce,0x9c,0x39,0xcb,0xcb,0x82,0x86,0x12,0xf8,0xae,0xac,0x8f,0xea,0xaf,0x22,0x52,0xe0,0xc3,0x1c,0xa8,0x3f,0x35,0x87,0x68,0x34,0x56,0xed,0x22,0x2b,0x8d,
    0x12,0xb7,0x43,0xda,0x97,0x40,0xd0,0xbd,0x86,0xcc,0xb1,0x1e,0xaf,0xc7,0x9a,0xaf,0x19,0xec,0x6b,0x4a,0xf2,0x13,0xa3,0x66,0xfa,0x29,0xcc,0x99,0x45,0x1c,0x97,0x02,
    0xc4,0x78,0xe0,0xa4,0x50,0xd1,0x85,0x87,0xf8,0x8d,0x70,0xe1,0x48,0x53,0xe3,0xce,0x9c,0x39,0x73,0x96,0x83,0xe1,0x58,0x3e,0x1c,0xd7,0x2f,0x30,0x17,0xe2,0x34,0x1a,
    0xf4,0xb3,0x10,0xe5,0x64,0xa1,0x38,0x8d,0x56,0x9b,0x45,0xac,0xbf,0x17,0xe1,0x97,0x8d,0x79,0x49,0x7a,0xfc,0x79,0x9c,0x52,0xc3,0x27,0x70,0xed,0x60,0x3b,0x8d,0xce,
    0xc7,0xdb,0x3e,0x61,0xdd,0x12,0xed,0xc5,0xa1,0x88,0xcb,0x6f,0x99,0xe4,0x9f,0x4d,0x88,0x94,0xa7,0xe1,0xc1,0xa6,0x33,0x67,0x0d,0xdf,0x68,0xdc,0x99,0x33,0x67,0xce,
    0xf2,0x31,0x65,0x24,0x4e,0xe5,0x1d,0x48,0x88,0x2f,0x19,0xc7,0x69,0x1c,0xdd,0xe5,0x17,0x01,0x30,0x53,0xb0,0xbc,0x00,0xd7,0xf9,0xac,0xb1,0x7c,0x61,0xd1,0x0e,0xe2,
    0x4a,0x01,0x7b,0x6d,0x88,0x11,0x8e,0xe7,0x1a,0x7a,0x91,0x7e,0xdf,0xb0,0xbb,0xf8,0x21,0x6e,0x66,0xa9,0x4e,0x56,0xa5,0x56,0x44,0x60,0xe3,0x7b,0x19,0x22,0x55,0xc4,
    0x6b,0x2c,0xb7,0x0a,0x1f,0xdf,0x6b,0x66,0xdc,0x99,0x33,0x67,0xce,0xf2,0x30,0x88,0xed,0x1c,0x55,0x5d,0x04,0x8b,0x75,0x1b,0x62,0x9b,0x85,0xb7,0xd1,0x48,0x88,0x6b,
    0x8c,0x3a,0x67,0x11,0x6f,0x9b,0x71,0xd3,0x7e,0x23,0x59,0x16,0xd9,0xea,0xfd,0x84,0x80,0xa3,0x3e,0xb7,0xab,0xd9,0x17,0x0a,0x5d,0x90,0xeb,0x9b,0x48,0xc5,0x5b,0x8d,
    0xa6,0x55,0x17,0xd6,0x7b,0x94,0x85,0xfe,0x95,0x21,0x1f,0x8a,0x45,0xaa,0xdc,0x82,0x4d,0x2e,0xc5,0xcd,0xe5,0xa6,0xc6,0x9d,0x39,0x73,0xe6,0x2c,0x2f,0x93,0x44,0x7a,
    0xb4,0x0d,0xc7,0xe6,0x3d,0x56,0x52,0x85,0x28,0x8d,0x14,0x18,0x2e,0x44,0x52,0xed,0x24,0x51,0xab,0xd6,0x3a,0x97,0x55,0xfa,0xb1,0xf0,0x53,0xc6,0x7a,0x89,0xcc,0xd7,
    0x0a,0xcb,0x3f,0x52,0x8f,0x00,0x2b,0x57,0xc5,0xac,0x56,0xa3,0x8b,0xa7,0x2a,0xeb,0x0c,0x36,0xea,0xb0,0x48,0xa9,0xc5,0x48,0xa4,0x4a,0x95,0x12,0x1e,0xa2,0x1b,0x1f,
    0xfe,0xdb,0xcc,0xb8,0x33,0x67,0xce,0x9c,0xe5,0x61,0x4a,0x61,0x14,0x6c,0x74,0x83,0x9a,0x92,0xe2,0xab,0x91,0x56,0x47,0x30,0x49,0x8f,0xc1,0x8a,0x4c,0xb4,0x11,0xc2,
    0x3c,0x60,0x94,0x10,0x8a,0x81,0x2c,0xf9,0xf2,0x27,0xf1,0xaf,0x4d,0x8d,0x66,0x51,0x20,0xa4,0x65,0xa6,0x37,0x58,0x78,0x7b,0xbc,0x57,0xa5,0xc4,0xa4,0xbf,0x86,0x84,
    0xdf,0x4d,0x0b,0x9b,0x3e,0x0c,0x5a,0x4a,0x89,0x34,0x50,0x65,0x06,0x16,0x1a,0xc7,0x43,0xd2,0x05,0xdd,0x46,0xe3,0xce,0x9c,0x39,0x73,0x96,0x97,0x25,0x35,0x49,0x1c,
    0xa7,0xb7,0xe2,0xd8,0xfc,0x97,0xd8,0x7b,0xfd,0x25,0x2b,0x71,0x2a,0x15,0xc6,0xe2,0x6d,0x8e,0x1a,0x49,0x15,0xe9,0xb1,0x13,0xc9,0xf6,0x3d,0x11,0xee,0x33,0x4a,0x0b,
    0x48,0x8f,0x5c,0x16,0x92,0xb7,0xd2,0xfd,0x2f,0x3a,0x03,0x47,0x7d,0x24,0xdf,0xf8,0xf9,0xf0,0x6c,0x9f,0xe1,0xa4,0x5b,0xcf,0x52,0xb6,0x6a,0xb7,0x48,0xa9,0x2f,0xa6,
    0x44,0x0a,0xeb,0x77,0xc1,0xee,0xab,0x39,0x50,0x7f,0xfa,0x5e,0x7b,0xdc,0x99,0x33,0x67,0xce,0x72,0x31,0xa5,0x8f,0x2a,0xc9,0x42,0xbc,0xc7,0xf5,0x4a,0xd2,0xcf,0x91,
    0xca,0x99,0x98,0xdb,0x6b,0x27,0x44,0x5a,0x0f,0xf6,0x9a,0xd9,0x2c,0x52,0x23,0x8d,0x2a,0x5f,0x86,0x6b,0xab,0x75,0x3c,0xc7,0xfe,0x89,0x10,0x0f,0x60,0xee,0x4e,0xa3,
    0xc9,0x54,0x42,0x4a,0x2d,0x2b,0xd1,0x7f,0x52,0xfb,0x84,0xca,0x47,0x38,0x68,0x51,0xeb,0x8d,0xb3,0xaa,0x48,0x43,0xd0,0x56,0x44,0xeb,0x31,0xb8,0x39,0xb5,0x49,0xa3,
    0x71,0x67,0xce,0x9c,0x39,0xcb,0xcb,0x92,0x86,0x10,0xd2,0xe3,0xd3,0x22,0xf2,0xd7,0xb8,0xef,0x93,0x34,0x92,0x8c,0xc4,0xc9,0x61,0x3c,0x8e,0xd9,0xd7,0xaa,0x18,0xb5,
    0x4c,0x0d,0x0b,0x90,0x20,0x0f,0x0b,0xf3,0x96,0x78,0x9e,0x26,0x7b,0xad,0x86,0xf1,0xde,0xc9,0x3c,0xc3,0x30,0xaa,0xd5,0x39,0x8d,0xbd,0x2a,0xd3,0xa1,0xcc,0x4b,0x42,
    0x5d,0x96,0x29,0x26,0x52,0x54,0xd1,0xe7,0x91,0xa4,0xff,0x74,0x52,0xa4,0x92,0x14,0x74,0x89,0x57,0x33,0x9e,0x30,0x55,0x80,0x6d,0x30,0xee,0xcc,0x99,0x33,0x67,0xb9,
    0x18,0xd3,0x05,0xd0,0xd1,0x7c,0x1c,0x9b,0x8d,0x84,0xa8,0x1d,0xb8,0x1f,0x3e,0x94,0x0d,0x56,0x6d,0x14,0x0b,0x3d,0x81,0x04,0x79,0xd0,0x48,0xbe,0x49,0xdd,0xd4,0x5a,
    0x6f,0x32,0xd2,0x66,0x4b,0xa3,0x34,0x8a,0xcb,0x3e,0x48,0x76,0x57,0xdc,0xcf,0x92,0xae,0xe4,0xd9,0x45,0xe9,0x7f,0xe9,0x7d,0xe8,0xdc,0xa4,0xc9,0xc4,0x74,0xc6,0x49,
    0x99,0x4b,0xdd,0xca,0xe7,0xe2,0x0b,0xaf,0x49,0x47,0x64,0x9e,0x65,0x8d,0x3b,0x73,0xe6,0xcc,0x59,0x5e,0xa6,0x95,0xaa,0x60,0x37,0xe1,0xa6,0xfd,0x46,0x9d,0xb3,0x88,
    0xa4,0x17,0xd7,0x53,0x43,0xb8,0x14,0x32,0x6c,0x67,0x1a,0x62,0xa5,0x51,0x08,0x56,0x0f,0x41,0x88,0xcf,0xc6,0x2c,0x69,0x08,0x85,0x6e,0x18,0xf0,0xdf,0xe9,0x54,0x49,
    0x67,0x63,0x4e,0xd2,0x64,0xea,0x36,0x1a,0x53,0x33,0xb0,0xd6,0x58,0x4b,0xcc,0x38,0xce,0x27,0xc2,0xde,0xca,0x54,0xd9,0x97,0x12,0x29,0x59,0x45,0x5b,0x4e,0xba,0x58,
    0x56,0xe7,0xcb,0x99,0x33,0x67,0xce,0xf2,0x30,0xbe,0x10,0xe3,0x9d,0x5c,0x88,0xd3,0x68,0x50,0x9a,0x0b,0x29,0xf6,0x43,0x70,0x9b,0xe2,0xf5,0x92,0xe3,0x3e,0xad,0x65,
    0xae,0x1c,0x4a,0x0b,0x1b,0x1e,0x4b,0x98,0xc6,0xdd,0x78,0xac,0xf3,0x69,0xb0,0x29,0x5a,0x28,0xc4,0x62,0xae,0x84,0x22,0x92,0xea,0x1f,0x31,0x79,0xb7,0x21,0xe6,0x12,
    0x26,0x97,0x21,0xd9,0x4a,0x7a,0x9c,0xce,0xd3,0xa0,0x0b,0xb3,0x75,0x58,0xa9,0x47,0xe2,0x9e,0xcc,0x22,0xd7,0x20,0x42,0x0f,0xcf,0x8e,0x3b,0x73,0xe6,0xcc,0x59,0x7e,
    0xa6,0x48,0x74,0xba,0x11,0xd6,0x7c,0x35,0x3e,0x9e,0x27,0xe9,0xd1,0xaa,0xa7,0xea,0x44,0xa4,0xdb,0x36,0x36,0xc4,0x1c,0x28,0x94,0x38,0xd0,0xeb,0x2c,0x85,0xed,0xf1,
    0x5e,0xf5,0xd2,0x42,0xa0,0xa3,0x99,0x48,0x3c,0x02,0xc9,0xf7,0x26,0x2b,0xf9,0x52,0xe0,0x56,0xcc,0x39,0x8f,0x8d,0xda,0x28,0x55,0xeb,0xba,0xf4,0x34,0x65,0xea,0xba,
    0x62,0xfd,0x92,0x24,0x6d,0x7d,0xe4,0xe8,0x6e,0x67,0xce,0x9c,0x39,0x3b,0x9d,0x2c,0x68,0xf8,0x38,0x2c,0x36,0x07,0xdc,0xe8,0xd4,0xd3,0x3c,0x8c,0xbf,0x2b,0x22,0x7d,
    0x76,0x2d,0x33,0x11,0x9b,0xbc,0x95,0x76,0x1e,0x0d,0xc5,0x66,0x4b,0xb8,0x60,0x34,0xba,0xb4,0xd2,0x82,0x23,0x78,0x0b,0x59,0xcd,0x22,0x96,0x44,0x88,0xdb,0x91,0x56,
    0xf7,0x18,0x4d,0x26,0x5b,0xe6,0x1c,0x3e,0x86,0xeb,0xf5,0x9c,0x49,0xa3,0xa6,0x48,0x95,0xc2,0x6c,0xcc,0x38,0x13,0xbf,0x24,0xbd,0xce,0x9c,0x39,0x73,0x76,0x3a,0x19,
    0x5e,0x25,0xa4,0xd5,0xf5,0x50,0xcf,0xeb,0x46,0x52,0xbd,0x0d,0x2c,0x16,0xb3,0x16,0x2e,0xc3,0xf5,0x4a,0x51,0x23,0x3d,0x2a,0x75,0xe1,0xba,0x1b,0xe9,0x77,0x67,0x2c,
    0x37,0xe9,0x52,0xe6,0x95,0x90,0xe9,0xb1,0x4c,0x84,0x1d,0x89,0x79,0x37,0xb2,0x8a,0x51,0x6b,0xd5,0xab,0xf0,0x36,0x12,0x69,0x74,0x6d,0xb4,0x57,0x05,0xf2,0x25,0xda,
    0x44,0x2c,0xfb,0x3f,0x50,0xa4,0xd8,0x04,0x37,0x8b,0xf5,0x6b,0xe1,0xcc,0x99,0x33,0x67,0x83,0x66,0x38,0x46,0x8f,0x67,0xe2,0xd9,0x5c,0x88,0x85,0x88,0x23,0xf8,0x82,
    0xa4,0x7b,0x2e,0x5c,0xd8,0x12,0x27,0xc1,0xfe,0xa4,0x21,0xf4,0xb8,0x0a,0x1d,0x4e,0x3b,0x94,0x86,0x21,0x1d,0x7e,0xd1,0x4e,0x8f,0x3c,0x1d,0x7c,0x9a,0x9d,0x46,0xab,
    0xe9,0xf6,0x59,0x62,0xdd,0x6b,0x25,0x69,0x66,0xeb,0x7b,0x85,0x8b,0x70,0xff,0x75,0x4c,0x76,0x3d,0x38,0x25,0xd2,0xa0,0xdc,0x81,0x6f,0x3b,0x8e,0x59,0x9f,0x8c,0x63,
    0xb2,0x33,0x67,0xce,0x9c,0x0d,0x9e,0x25,0x9d,0x75,0x98,0x6a,0x1d,0x92,0xe0,0x41,0xa3,0x35,0x75,0x2b,0x6b,0xdc,0x10,0x0a,0xa4,0x53,0x60,0xcc,0x99,0x6a,0xa4,0x51,
    0xd6,0x50,0x82,0x2c,0x77,0x41,0xd0,0x2f,0xc4,0xcb,0x05,0x1c,0xcf,0xf5,0x97,0xa2,0x74,0x3c,0x43,0x46,0x63,0xbc,0x68,0x09,0x36,0x84,0xd0,0x8e,0xbd,0x26,0xe0,0xe3,
    0x13,0x46,0xf2,0x4d,0x8e,0xfb,0x4f,0x22,0x8d,0xbe,0xfa,0x81,0x22,0x65,0x0a,0xdf,0xc2,0xe5,0x67,0x76,0xc9,0xc3,0x99,0x33,0x67,0xce,0x06,0xc9,0x42,0x98,0x80,0xd4,
    0xd9,0x6e,0xa6,0xc7,0x5a,0xfd,0xf3,0xcf,0x24,0xba,0xcd,0x4e,0xb7,0xfa,0x88,0x30,0x1d,0x49,0x89,0x4b,0xe9,0x6c,0x25,0xbe,0xd9,0x6a,0x08,0x69,0xd0,0x99,0x10,0xdf,
    0x64,0x31,0x52,0x71,0xb2,0x17,0x8e,0xfa,0x5b,0x90,0x8c,0xf7,0x19,0x89,0xf9,0x0e,0xac,0xf9,0x40,0x6c,0xc9,0x30,0x0e,0xe3,0x1d,0x56,0xa3,0x0b,0xb3,0xe6,0xa5,0x45,
    0xca,0x3c,0xb7,0xf6,0x1f,0xc1,0x9b,0xe2,0x7b,0x9d,0x39,0x73,0xe6,0x2c,0x0f,0x93,0x2e,0x16,0x5e,0x03,0xf1,0x1d,0xca,0x24,0xbd,0x02,0x2e,0xb7,0x58,0xc7,0x69,0xa4,
    0xd1,0x69,0xb8,0x7f,0x3a,0x15,0xc4,0xee,0xd4,0x2b,0x3d,0x8f,0xbd,0xfe,0x60,0xd8,0x7c,0x29,0xc6,0x1f,0xc2,0x11,0x3e,0x64,0x1a,0x5d,0xe7,0x63,0xaf,0x05,0xb0,0x69,
    0x2c,0x58,0xd2,0xd9,0x55,0x21,0x32,0xad,0x8f,0x58,0x85,0x8b,0xa4,0xfc,0x3b,0xbb,0xae,0x1b,0x6e,0x4b,0x89,0x54,0x43,0xf8,0xba,0xaa,0x2e,0xb7,0x7e,0x65,0x9c,0x39,
    0x73,0xe6,0x6c,0xb0,0x0c,0x69,0x6e,0x22,0x44,0xd5,0x06,0xf1,0xc5,0xe9,0x91,0xaa,0x69,0x34,0xf9,0xb4,0x23,0x3e,0xba,0x27,0x8c,0x57,0x71,0xa0,0xa3,0x99,0x49,0x23,
    0xb0,0xe6,0x22,0xeb,0x78,0x0e,0xb3,0x5d,0x59,0x97,0x69,0xdc,0xb4,0x22,0x2e,0x2a,0x6b,0x1f,0x89,0xbc,0x62,0x1c,0xdd,0x6f,0xc7,0xb3,0xdc,0x1f,0x8f,0xf3,0xc5,0xb8,
    0xce,0x86,0xee,0xad,0x52,0xc0,0xc2,0xcc,0xd1,0x3e,0x5c,0x5f,0xfd,0x43,0xa4,0xcf,0x28,0xb2,0x3a,0x73,0xe6,0xcc,0xd9,0xa0,0x99,0x52,0xa5,0x04,0x5e,0x86,0xa8,0xde,
    0x4e,0xcd,0x60,0x1a,0x0a,0xba,0x04,0x16,0xba,0xdb,0x10,0x62,0x0b,0xae,0x97,0x13,0x57,0xba,0xcd,0x66,0x11,0xd3,0x73,0x48,0xc0,0x7b,0x0c,0x31,0x7f,0x5f,0x98,0x1f,
    0x34,0x4a,0x0e,0x63,0x70,0xa4,0x9f,0x87,0x84,0x6b,0x1c,0xf7,0xf9,0xf3,0xb5,0x34,0xca,0x1b,0xe3,0xbd,0x34,0x11,0xfd,0x3a,0x48,0xfd,0xa0,0x51,0x0a,0xf8,0x32,0xab,
    0xdc,0x7b,0x52,0xa4,0x58,0xe8,0xab,0x78,0xbb,0xcf,0xfc,0x95,0x71,0xe6,0xcc,0x99,0xb3,0x41,0x32,0x24,0xd4,0x49,0xac,0xdc,0x2a,0x1a,0x0b,0x4c,0x6a,0xb5,0x51,0x24,
    0x4f,0xde,0x69,0x08,0xf1,0x47,0x98,0xbb,0x92,0xa9,0x70,0x2c,0x3d,0x1e,0x46,0x61,0xd1,0x1b,0xad,0x74,0x1b,0x54,0xdb,0xea,0x73,0x7b,0x8c,0xe7,0x2b,0x62,0xfc,0x29,
    0x88,0xef,0x80,0xf1,0x23,0xf0,0x4d,0x08,0xf6,0xa7,0x46,0xe2,0x9c,0x80,0x89,0xd7,0xe0,0x09,0x1b,0xd5,0x75,0xe9,0x44,0x5d,0x57,0x06,0xd8,0xf5,0x99,0x46,0xd1,0xd5,
    0x99,0x33,0x67,0xce,0x06,0xc3,0xf0,0x5a,0xa6,0xcc,0x8f,0xa9,0x14,0x0e,0xa7,0x13,0x22,0x9d,0x05,0x51,0x2e,0xc6,0x87,0xbb,0x8c,0x04,0x7b,0x45,0xf5,0x9e,0x21,0x71,
    0xdd,0x14,0x11,0xb1,0x26,0x30,0xe6,0xbd,0x71,0xea,0xa4,0x3b,0x31,0xbe,0x22,0x5e,0x2f,0x8c,0x85,0x44,0xe7,0x5a,0x75,0x58,0xb0,0xce,0xfa,0xec,0xcd,0x46,0xe2,0x5c,
    0xce,0x42,0xa7,0xa8,0xeb,0xd2,0x3d,0x27,0x86,0xfe,0x0f,0xd6,0xf1,0x43,0x4c,
};


#ifndef LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER
#define LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER ALIGN(4)
#endif
LV_ATTRIBUTE_MEM_ALIGN_EZIP_HEADER const eZIP_RGBARGB888A  lv_img_dsc_t hrv_range SECTION(".ROM3_IMG_EZIP_HEADER.hrv_range") = { 
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .header.always_zero = 0,
  .header.w = 338,
  .header.h = 32,
  .data_size  = 2767,
  .data = hrv_range_map
};
