/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   music_control.c
@Time    :   2025/08/14 14:53:22
*
**************************************************************************/
#include "music_control.h"
#ifndef SIMULATOR
#include "ble_data_inf.h"
#include "ble_media_srv.h"
#endif
#include <rtthread.h>
#include <string.h>
#include <stdio.h>

#define TRACK_LEN  128
#define ARTIST_LEN 128

// 模块内部状态
static struct
{
    bool bt_connected;
    PlayerState state;
    char track_name[TRACK_LEN];
    char artist[ARTIST_LEN];
    uint16_t play_cur_progress;
    uint16_t play_total_progress;
    uint8_t volume;
    uint8_t total_volume_level;   //最大音量等级（苹果0，安卓非0）
    bool is_apple_device;         //是否是苹果设备
    MusicInfoUpdateResult update_res[MUSIC_INFO_MAX];
} music_ctrl;


static struct rt_mutex music_update_res_mutex;   // 互斥锁
static bool update_res_is_init = false;

static void Set_DateUpdate(MusicInfoType type, MusicInfoUpdateResult res)
{
    rt_mutex_take(&music_update_res_mutex, RT_WAITING_FOREVER);   // 获取锁
    if (type == MUSIC_INFO_MAX)
    {
        for (int i = 0; i < MUSIC_INFO_MAX; i++)
            music_ctrl.update_res[i] = res;
    }
    else
    {
        music_ctrl.update_res[type] = res;
    }
    rt_mutex_release(&music_update_res_mutex);   // 释放锁
}

static MusicInfoUpdateResult Get_DateUpdate(MusicInfoType type)
{
    rt_mutex_take(&music_update_res_mutex, RT_WAITING_FOREVER);   // 获取锁
    MusicInfoUpdateResult ret = music_ctrl.update_res[type];
    rt_mutex_release(&music_update_res_mutex);                    // 释放锁
    return ret;
}

// ================= 初始化函数 =================
/**
 * @brief 初始化音乐控制模块
 * @details 重置所有状态变量，设置初始状态为断开连接
 */
void MusicCtrl_Init(void)
{
    MUSIC_CTRL_LOG_D("MusicCtrl_Init");
    if (!update_res_is_init)
    {
        memset(&music_ctrl, 0, sizeof(music_ctrl));
        update_res_is_init = !update_res_is_init;
        rt_mutex_init(&music_update_res_mutex, "music_update_res_mutex", RT_IPC_FLAG_FIFO);
    }

    //获取蓝牙数据，组装到 music_ctrl 中
#ifndef SIMULATOR
    music_ctrl.bt_connected = (bool) g_device_get_ble_connect_status();
    if(music_ctrl.bt_connected)
    {
        media_service_info_t media_srv_info;
        ble_media_info_get(&media_srv_info);
        music_ctrl.state = media_srv_info.play_status;
        snprintf(music_ctrl.track_name, TRACK_LEN, "%s", media_srv_info.title_name);
        snprintf(music_ctrl.artist, ARTIST_LEN, "%s", media_srv_info.artist_name);
        music_ctrl.play_cur_progress = media_srv_info.track_time_elapsed;
        music_ctrl.play_total_progress = media_srv_info.track_time_total;
        music_ctrl.is_apple_device = media_srv_info.ams_exists;
        music_ctrl.volume = media_srv_info.volume_level;
        music_ctrl.total_volume_level = media_srv_info.volume_max;
    }
    else
    {
        memset(&music_ctrl, 0, sizeof(music_ctrl));
    }

#else
    music_ctrl.bt_connected = true;
    music_ctrl.state = MUSIC_PLAYER_STATE_PLAYING;
    snprintf(music_ctrl.track_name, TRACK_LEN, "Track Name Artist Name Artist Name Artist Name");
    snprintf(music_ctrl.artist, ARTIST_LEN, "Artist Name Artist Name Artist Name Artist Name");
    music_ctrl.play_cur_progress = 300;
    music_ctrl.play_total_progress = 600;
    music_ctrl.volume = 10;
    music_ctrl.is_apple_device = true;
    music_ctrl.total_volume_level = 16;
#endif

    MUSIC_CTRL_LOG_D("state:%d, track_name:%s, \n artist:%s, \n progress:%d/%d, volume:%d/%d,\n is_apple_device:%d",
                     music_ctrl.state,
                     music_ctrl.track_name,
                     music_ctrl.artist,
                     music_ctrl.play_cur_progress, music_ctrl.play_total_progress,
                     music_ctrl.volume, music_ctrl.total_volume_level,
                     music_ctrl.is_apple_device
                    );
}

// ================= 控制命令实现 UI调用=================
/**
 * @brief 增加音量
 * @return MusicCtrlStatus 操作状态
 */
MusicCtrlStatus MusicCtrl_VolumeUp(void)
{
#ifndef SIMULATOR
    if (music_ctrl.is_apple_device)
    {
        ble_media_play_control(BLE_APP_MEDIA_CMD_VOLUME_UP, 0);
        MUSIC_CTRL_LOG_D("ui notify ble VolumeUp");
    }
    else
    {
        uint8_t dx = music_ctrl.total_volume_level / 15;
        ble_media_play_control(BLE_APP_MEDIA_CMD_VOLUME_UP, dx);
        MUSIC_CTRL_LOG_D("ui notify ble VolumeUp:%d",dx);
    }
#else
    if (music_ctrl.volume < music_ctrl.total_volume_level)
        music_ctrl.volume++;
    Set_DateUpdate(MUSIC_INFO_VOLUME, MUSIC_INFO_UPDATE_SUCCES);
#endif   // !SIMULATOR
    return MUSIC_CTRL_SUCCESS;
}

/**
 * @brief 降低音量
 * @return MusicCtrlStatus 操作状态
 */
MusicCtrlStatus MusicCtrl_VolumeDown(void)
{
#ifndef SIMULATOR
    if (music_ctrl.is_apple_device)
    {
        ble_media_play_control(BLE_APP_MEDIA_CMD_VOLUME_DOWN, 0);
        MUSIC_CTRL_LOG_D("ui notify ble VolumeDown");
    }
    else
    {
        uint8_t dx = music_ctrl.total_volume_level / 15;
        ble_media_play_control(BLE_APP_MEDIA_CMD_VOLUME_DOWN, dx);
        MUSIC_CTRL_LOG_D("ui notify ble VolumeDown:%d",dx);
    }
#else
    if (music_ctrl.volume > 0)
        music_ctrl.volume--;
    Set_DateUpdate(MUSIC_INFO_VOLUME, MUSIC_INFO_UPDATE_SUCCES);
#endif   // !SIMULATOR
    return MUSIC_CTRL_SUCCESS;
}

/**
 * @brief 切换到下一首曲目
 * @return MusicCtrlStatus 操作状态
 */
MusicCtrlStatus MusicCtrl_NextTrack(void)
{
#ifndef SIMULATOR
    ble_media_play_control(BLE_APP_MEDIA_CMD_NEXT, 0);
#else
#endif   // !SIMULATOR
    MUSIC_CTRL_LOG_D("ui notify ble NextTrack");
    return MUSIC_CTRL_SUCCESS;
}

/**
 * @brief 切换到上一首曲目
 * @return MusicCtrlStatus 操作状态
 */
MusicCtrlStatus MusicCtrl_PreviousTrack(void)
{
#ifndef SIMULATOR
    ble_media_play_control(BLE_APP_MEDIA_CMD_PREVIOUS, 0);
#else
#endif   // !SIMULATOR
    MUSIC_CTRL_LOG_D("ui notify ble PrevTrack");
    return MUSIC_CTRL_SUCCESS;
}

/**
 * @brief 播放/暂停切换
 * @return MusicCtrlStatus 操作状态
 */
MusicCtrlStatus MusicCtrl_PlayPause(void)
{
#ifndef SIMULATOR
    if (music_ctrl.state == MUSIC_PLAYER_STATE_PLAYING)
    {
        MUSIC_CTRL_LOG_D("ui notify ble PAUSE");
        ble_media_play_control(BLE_APP_MEDIA_CMD_PAUSE, 0);
    }
    else
    {
        MUSIC_CTRL_LOG_D("ui notify ble Play");
        ble_media_play_control(BLE_APP_MEDIA_CMD_PLAY, 0);
    }
#else
    if (music_ctrl.state == MUSIC_PLAYER_STATE_PLAYING)
    {
        music_ctrl.state = MUSIC_PLAYER_STATE_PAUSED;
    }
    else
    {
        music_ctrl.state = MUSIC_PLAYER_STATE_PLAYING;
    }
    Set_DateUpdate(MUSIC_INFO_STATE, MUSIC_INFO_UPDATE_SUCCES);
#endif   // !SIMULATOR
    return MUSIC_CTRL_SUCCESS;
}

// ================= 状态获取接口 UI调用=================

bool MusicCtrl_IsConnected(void)
{
    MUSIC_CTRL_LOG_D("ui get bt_connected:%d",music_ctrl.bt_connected);
    return music_ctrl.bt_connected;
}

PlayerState MusicCtrl_GetPlayerState(void)
{
    MUSIC_CTRL_LOG_D("ui get state:%d",music_ctrl.state);
    return music_ctrl.state;
}

const char *MusicCtrl_GetTrackName(void)
{
    MUSIC_CTRL_LOG_D("ui get track_name:%s",music_ctrl.track_name);
    return music_ctrl.track_name;
}

const char *MusicCtrl_GetArtistName(void)
{
    MUSIC_CTRL_LOG_D("ui get artist:%s",music_ctrl.artist);
    return music_ctrl.artist;
}

uint8_t MusicCtrl_GetPlayProgress(void)
{
    uint8_t ret = 0;
    if(music_ctrl.play_total_progress == 0)
    {
        ret = 0;
    }
    else
    {
        ret = music_ctrl.play_cur_progress * 100 / music_ctrl.play_total_progress;
    }
    MUSIC_CTRL_LOG_D("ui get progress:%d",ret);
    return ret;
}

uint8_t MusicCtrl_GetVolume(void)
{
    float cur_volume_pct = 0.0f;
    if (music_ctrl.is_apple_device)
    {
        cur_volume_pct = (float)music_ctrl.volume * 100.0f / 16.0f;
        MUSIC_CTRL_LOG_D("apple device, volume = %f",cur_volume_pct);
    }
    else
    {
        cur_volume_pct = (float)music_ctrl.volume * 100.0f / (float)music_ctrl.total_volume_level;
        MUSIC_CTRL_LOG_D("android device, volume = %.1f",cur_volume_pct);
    }
    return cur_volume_pct;
}

MusicInfoUpdateResult MusicCtrl_GetUpdate(MusicInfoType type)
{
    MusicInfoUpdateResult ret = Get_DateUpdate(type);
    Set_DateUpdate(type, MUSIC_INFO_UPDATE_NONE);
    return ret;
}

void MusicCtrl_UpdateCheck(void)
{
    /*这个函数每1s被调用一次，
    若连接状态发生了变化：
    1、连接
        获取播放状态（查询蓝牙）
        获取播放信息（查询蓝牙：进度、音量、歌曲名、歌手）
    2、断开
        重置播放信息
        重置播放状态
    若连接状态未变化：
    若播放状态更新
        获取播放状态（查询蓝牙）
        获取播放信息（查询蓝牙：进度、音量、歌曲名、歌手）
    若歌曲名更新
        获取播放信息（查询蓝牙：歌曲名、歌手）
    若音量更新
        获取播放信息（查询蓝牙：音量）
    若进度更新
        获取播放信息（查询蓝牙：进度）
    */
}

// ================= 蓝牙事件处理 被蓝牙调用=================

void MusicCtrl_OnBluetoothEventUpdated(MusicInfoType type)
{
    MUSIC_CTRL_LOG_D("ble update notify TYPE:%d",type);
    MusicCtrl_Init();
    Set_DateUpdate(type, MUSIC_INFO_UPDATE_SUCCES);
}

//应用层判断光旋扭固定时间内连续转3次--------------------------
// 定义关键宏
#define TRIGGER_ANGLE_THRESHOLD     90     // 触发阈值角度
#define TRIGGER_TIME_THRESHOLD      1000   // 触发时间阈值(ms)
#define CONTINUOUS_ROTATE_THRESHOLD 3      // 连续旋转阈值次数

// 旋钮状态结构体
typedef struct
{
    int32_t last_angle;          // 上一次的角度值
    int64_t last_trigger_time;   // 上一次触发的时间
    int8_t continuous_count;     // 连续旋转计数
    bool last_direction;         // 上一次的旋转方向
} knob_state_t;

static knob_state_t state = {0};

void knob_state_init(void)
{
    state.last_angle = 0;
    state.last_trigger_time = 0;
    state.continuous_count = 0;
    state.last_direction = false;
}

bool handle_knob_rotation(knob_direction_t current_direction, int64_t current_time)
{
    bool triggered = false;

    // 检查方向是否连续
    if (current_direction == state.last_direction)
    {
        // 方向相同，检查时间间隔
        if ((current_time - state.last_trigger_time) <= TRIGGER_TIME_THRESHOLD)
        {
            state.continuous_count++;
            if (state.continuous_count >= CONTINUOUS_ROTATE_THRESHOLD)
            {
                // 达到触发条件
                triggered = true;

                state.continuous_count = 0;   // 重置计数
            }
        }
        else
        {
            // 超过时间阈值，重置计数
            state.continuous_count = 1;
        }
    }
    else
    {
        // 方向改变，重置计数
        state.continuous_count = 1;
        state.last_direction = current_direction;
    }

    state.last_trigger_time = current_time;
    if(!triggered)
    {
        MUSIC_CTRL_LOG_D("knob cnt:%d dir:%d ",state.continuous_count,state.last_direction);
    }
    else
    {
        MUSIC_CTRL_LOG_D("knob notify");
    }
    return triggered;
}
