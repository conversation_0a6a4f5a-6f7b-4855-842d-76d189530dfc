/************************************************************************​
*Copyright(c) 2024, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#ifndef NAVI_PORT_H
#define NAVI_PORT_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include "navi_tcnx.h"
#include "navi_tnav.h"
#include "navi_tclm.h"
#include "navi_tsgn.h"
#include "minmea.h"

#define __NAVI_ROUTE_SIM 1

//导航特定事件发生时的回调函数，用于执行一些和导航模块密切相关但又不属于导航模块的功能
typedef void (*navi_cb_t)(void);

//导航模块回调函数
typedef struct _navi_cbs
{
    navi_cb_t route_use_cb;                     //导航启用时的回调函数
    navi_cb_t route_stop_cb;                    //导航停止时的回调函数
    navi_cb_t route_arrive_cb;                  //导航完成时的回调函数
} navi_cbs_t;

//====================== 用于兼容的结构体 ========================//
//精简后的record数据
typedef struct
{
    int32_t position_lat; // *10000000,
    int32_t position_lon; // *10000000,
    uint32_t distance; // cm,
    int32_t altitude; // cm,
} record_mesg_simple_t;
//=========================== end ==============================//

//导航计算和匹配所需输入
typedef struct _navi_calc_input
{
    //TODO timestamp
    struct minmea_float longitude;
    struct minmea_float latitude;
} navi_calc_input_t;

//导航线路上最匹配的点
typedef struct _navi_route_match_point
{
    double lng;
    double lat;
} navi_route_match_point_t;

//导航偏航规划目标点
typedef struct _navi_route_target_point
{
    double lng;
    double lat;
} navi_route_target_point_t;

//导航计算和匹配的状态
typedef enum _navi_status
{
    enumNAVI_STATUS_NORMAL,                     //导航计算和匹配正常
    enumNAVI_STATUS_BACKWARD,                   //反向骑行
    enumNAVI_STATUS_ARRIVED,                    //已到达终点
    enumNAVI_STATUS_OFF_COURSE,                 //当前已偏航
    enumNAVI_STATUS_STILL,                      //启用路书后一直静止不动
    enumNAVI_STATUS_NOT_FIXED,                  //未定位，无法计算
} navi_status_t;

//当前正在进行的导航（线路）的类型
typedef enum _navi_type
{
    enumNAVI_TYPE_ROUTE,                        //普通路书
    enumNAVI_TYPE_NAVI_TO_ROUTE,                //导航到线路（偏航重规划）
    enumNAVI_TYPE_NAVI_TO_POS,                  //导航到位置点（位置点/POI）
    enumNAVI_TYPE_NAVI_TO_START,                //导航到起点
} navi_type_t;

//转向类型
typedef enum _navi_turn_type
{
    enumNAVI_TURN_LEFT_ROUND        = -4,       //左掉头
    enumNAVI_TURN_LEFT_SHARPLY      = -3,       //向左急转
    enumNAVI_TURN_LEFT_NORMALLY     = -2,       //向左转
    enumNAVI_TURN_LEFT_SLIGHTLY     = -1,       //稍向左转
    enumNAVI_TURN_GO_STRAIGHT       = 0,        //直行
    enumNAVI_TURN_RIGHT_SLIGHTLY    = 1,        //稍向右转
    enumNAVI_TURN_RIGHT_NORMALLY    = 2,        //向右转
    enumNAVI_TURN_RIGHT_SHARPLY     = 3,        //向右急转
    enumNAVI_TRUN_RIGHT_ROUND       = 4,        //右掉头
} navi_turn_type_t;

//导航转向数据（接下来的转向）
typedef struct _navi_turn
{
    navi_turn_type_t type;                      //转向类型
    float dist;                                 //多少距离后转向（m）
    uint32_t idx;                               //转向索引
    char wayname[NAVI_TURN_WAYNAME_LEN+1];      //转向路名
    uint8_t is_valid;                           //转向是否有效
} navi_turn_t;

//导航爬坡数据（正在进行或即将进行的爬坡）
typedef struct _navi_climb
{
    uint32_t idx;                               //当前正在进行的爬坡的索引（0xFFFFFFFF表示无效）
    uint32_t idx_next;                          //下一个爬坡的索引（0xFFFFFFFF表示无效）
    uint32_t climb_remain;                      //剩余爬坡数量（0xFFFFFFFF表示无效），包含当前爬坡
    float dist_remain;                          //当前爬坡剩余距离（m）
    float h_remain;                             //当前爬坡剩余海拔（m）
    float d_residual;                           //剩余的总爬坡距离（m）
    float h_residual;                           //剩余的总爬坡爬升（m）
    float d2climb;                              //当前没有进行爬坡时，距离接下来的那个爬坡的距离（m），小于0表示无效
    uint8_t is_valid;                           //是否正在进行爬坡
} navi_climb_t;

//导航标记点数据（接下来的标记点）
typedef struct _navi_sign
{
    uint32_t idx;                               //标记点索引
    float dist;                                 //多少距离后到达标记点（m）
    char name[NAVI_SIGN_NAME_LEN+1];            //标记点名称
    uint8_t is_valid;                           //标记点是否有效
} navi_sign_t;

//导航进度
typedef struct _navi_progress
{
    navi_route_match_point_t match_point;       //匹配点
    navi_route_target_point_t target_point;     //偏航规划目标点
    navi_type_t type;                           //导航类型
    navi_status_t status;                       //导航状态
    navi_turn_t turn;                           //下一个转向数据
    navi_climb_t climb;                         //当前爬坡进度
    navi_sign_t sign;                           //下一个标记点
    float dist;                                 //当前在线路的距离，未定位时数据无效（m）
    float dist2dest;                            //到终点的距离（m）
} navi_progress_t;

int navi_init(const navi_cbs_t *cbs);

void navi_uninit(void);

void navi_calc_input(const navi_calc_input_t *input);

int route_use(const char *path, uint8_t is_reverse);

int route_preview(const char *path);

void navi_route_update(void);

void stop_course(void);

void stop_preview(void);

int back_to_start(void);

const navi_progress_t* navi_progress_get(void);

const NaviRouteWpNearby* navi_route_wp_nearby_get(void);

const NaviRouteData* navi_route_data_get(void);

const NaviRouteWpSample* navi_route_wp_sample_get(void);

const NaviClimbEnsembleData* navi_climb_ensemble_data_get(void);

const NaviClimbSample* navi_climb_sample_get(void);

const NaviClimbData* navi_climb_data_get(uint32_t idx);

const NaviRouteCpNearby* navi_route_cp_nearby_get(void);

const NaviSignNearby* navi_sign_nearby_get(void);

uint32_t navi_turn_num_get(void);

const navi_turn_t* navi_turn_get(uint32_t idx);

void navi_route_process_enter(void);

uint8_t navi_route_is_processing(void);

uint8_t navi_is_using(const char *path);

uint8_t navi_is_using2(const char *name);

void navi_ctrl_mode_set(int ctrl_mode);

int navi_ctrl_mode_get(void);

int navi_route_sim_get(void);

void navi_route_sim_set(int route_sim);

#ifdef __cplusplus
}
#endif

#endif
