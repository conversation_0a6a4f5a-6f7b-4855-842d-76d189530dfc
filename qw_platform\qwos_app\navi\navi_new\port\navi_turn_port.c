#include <string.h>
#include <stddef.h>
#include <stdbool.h>
#include <math.h>
#include "navi_turn_port.h"
#include "navi_util.h"

// #define NAVI_NEW
#define NAVI_TURN_CALC_WAYNAME              0

#if NAVI_TURN_CALC_WAYNAME
#include "qw_router_port.h"
#endif

#ifdef NAVI_NEW
#include "rtthread.h"
#endif

#ifdef IGS_DEV
#include "mem_section.h"
#endif

#ifdef IGS_DEV
static TnavReader s_tnav_reader;
static TnavWriter s_tnav_writer;
#else
static TnavReader s_tnav_reader = { 0 };
static TnavWriter s_tnav_writer = { 0 };
#endif

//导航转向计算器
static NaviTurnCalculator s_navi_turn_calculator = { 0 };

//导航转向分段器
#ifdef IGS_DEV
L2_RET_BSS_SECT_BEGIN(turn_segmentor)
ALIGN(4) static NaviTurnSegment s_seg_buf[NAVI_TURN_SEGMENTS_NUM];
L2_RET_BSS_SECT_END
static NaviTurnSegmentor s_navi_turn_segmentor;
#else
static NaviTurnSegment s_seg_buf[NAVI_TURN_SEGMENTS_NUM] = { 0 };
static NaviTurnSegmentor s_navi_turn_segmentor = {
    .seg_buf = s_seg_buf,
    .capacity = NAVI_TURN_SEGMENTS_NUM,
    .INTERVAL = NAVI_TURN_SEGMENT_INTERVAL,
};
#endif

//导航转向分段
#ifdef IGS_DEV
static NaviTurnSegmentArray s_navi_turn_segment_array;
#else
static NaviTurnSegmentArray s_navi_turn_segment_array = {
    .segments = s_seg_buf,
    .len = NAVI_TURN_SEGMENTS_NUM,
};
#endif

//导航转向list
#ifdef IGS_DEV
L2_RET_BSS_SECT_BEGIN(turn_list)
ALIGN(4) static NaviTurn s_turn_cache[NAVI_TURN_CACHE_BUF_NUM][NAVI_TURN_CACHE_BUF_SIZE];
L2_RET_BSS_SECT_END
static NaviTurnBuf s_turn_buf[NAVI_TURN_CACHE_BUF_NUM];
static NaviTurnList s_navi_turn_list;
#else
static NaviTurn s_turn_cache[NAVI_TURN_CACHE_BUF_NUM][NAVI_TURN_CACHE_BUF_SIZE] = { 0 };
static NaviTurnBuf s_turn_buf[NAVI_TURN_CACHE_BUF_NUM] = {
    [0] = {
        .buf = *(s_turn_cache + 0),
        .capacity = NAVI_TURN_CACHE_BUF_SIZE,
    },
    [1] = {
        .buf = *(s_turn_cache + 1),
        .capacity = NAVI_TURN_CACHE_BUF_SIZE,
    },
    [2] = {
        .buf = *(s_turn_cache + 2),
        .capacity = NAVI_TURN_CACHE_BUF_SIZE,
    },
    [3] = {
        .buf = *(s_turn_cache + 3),
        .capacity = NAVI_TURN_CACHE_BUF_SIZE,
    },
};

static NaviTurnList s_navi_turn_list = {
    .cache = {
        .turn_buf = s_turn_buf,
        .tnav_reader = &s_tnav_reader,
        .capacity = NAVI_TURN_CACHE_BUF_NUM,
    },
    .len = 0,
};
#endif

//导航转向匹配器
#ifdef IGS_DEV
static NaviTurnMatcher s_navi_turn_matcher;
#else
static NaviTurnMatcher s_navi_turn_matcher = {
    .turn_list = &s_navi_turn_list,
    .seg_array = &s_navi_turn_segment_array,
};
#endif

static char s_tnav_path[100] = { 0 };

//重置导航转向模块
static void navi_turn_reset(void)
{
    navi_turn_calculator_reset(&s_navi_turn_calculator);
    navi_turn_segmentor_reset(&s_navi_turn_segmentor);
    navi_turn_list_reset(&s_navi_turn_list);

    if (s_tnav_reader.fp != NULL)
    {
        qw_f_close(s_tnav_reader.fp);
        s_tnav_reader.fp = NULL;
    }
}

//上电后调用，初始化导航转向模块
void navi_turn_init(void)
{
#ifdef IGS_DEV
    s_tnav_reader.fp = NULL;
    s_tnav_writer.fp = NULL;

    navi_turn_calculator_reset(&s_navi_turn_calculator);

    s_navi_turn_segmentor.seg_buf = s_seg_buf;
    s_navi_turn_segmentor.capacity = NAVI_TURN_SEGMENTS_NUM;
    s_navi_turn_segmentor.INTERVAL = NAVI_TURN_SEGMENT_INTERVAL;
    navi_turn_segmentor_reset(&s_navi_turn_segmentor);

    s_navi_turn_segment_array.segments = s_seg_buf;
    s_navi_turn_segment_array.len = NAVI_TURN_SEGMENTS_NUM;

    for (uint32_t i = 0; i < NAVI_TURN_CACHE_BUF_NUM; i++)
    {
        s_turn_buf[i].buf = *(s_turn_cache + i);
        s_turn_buf[i].capacity = NAVI_TURN_CACHE_BUF_SIZE;
    }
    s_navi_turn_list.cache.turn_buf = s_turn_buf;
    s_navi_turn_list.cache.tnav_reader = &s_tnav_reader;
    s_navi_turn_list.cache.capacity = NAVI_TURN_CACHE_BUF_NUM;
    s_navi_turn_list.len = 0;
    navi_turn_list_reset(&s_navi_turn_list);

    s_navi_turn_matcher.turn_list = &s_navi_turn_list;
    s_navi_turn_matcher.seg_array = &s_navi_turn_segment_array;

#else
    navi_turn_reset();
#endif
}

//关机前调用，执行一些清理操作
void navi_turn_uninit(void)
{
    if (s_tnav_reader.fp != NULL)
    {
        qw_f_close(s_tnav_reader.fp);
        s_tnav_reader.fp = NULL;
    }
}

//检查导航文件对应的tnav文件是否有效，有效则后续可以直接使用，不必再进行计算
//true - 有效
//false - 无效
int navi_turn_is_tnav_valid(const char *path)
{
    if (path == NULL || strlen(path) > 94)
    {
        return false;
    }

    //逻辑上s_tnav_reader.fp必须为NULL
    if (s_tnav_reader.fp != NULL)
    {
        return false;
    }

    uint8_t header[32] = { 0 };

    sprintf(s_tnav_path, "%s.tnav", path);

    //文件打开失败（通常是因为不存在），则视为无效
    if (qw_f_open(&s_tnav_reader.fp, s_tnav_path, QW_FA_READ) != QW_OK)
    {
        s_tnav_reader.fp = NULL;
        return false;
    }

    //文件头读取失败（文件损坏），则视为无效
    if (tnav_header_read(&s_tnav_reader, header) != 0)
    {
        goto tnav_invalid;
    }

    //文件标识符不一致，则文件无效
    if (header[0] != 'T' || header[1] != 'N' || header[2] != 'A' || header[3] != 'V')
    {
        goto tnav_invalid;
    }

    //文件版本不一致，则文件无效
    if (header[4] != NAVI_FILE_MAJOR_VERSION || header[5] != NAVI_FILE_MINOR_VERSION)
    {
        goto tnav_invalid;
    }

    qw_f_close(s_tnav_reader.fp);
    s_tnav_reader.fp = NULL;

    return true;

tnav_invalid:
    qw_f_close(s_tnav_reader.fp);
    s_tnav_reader.fp = NULL;
    return false;
}

//tnav文件有效，则启用导航文件时只需加载tnav文件即可，无需再次计算
int navi_turn_tnav_load(const char *path)
{
    if (path == NULL || strlen(path) > 94)
    {
        return -1;
    }

    navi_turn_reset();

    sprintf(s_tnav_path, "%s.tnav", path);

    if (qw_f_open(&s_tnav_reader.fp, s_tnav_path, QW_FA_READ) != QW_OK)
    {
        s_tnav_reader.fp = NULL;
        return -1;
    }

    if (tnav_turn_segment_array_read(&s_tnav_reader, &s_navi_turn_segment_array) != 0)
    {
        goto err_handler;
    }

    if (tnav_turn_num_read(&s_tnav_reader, &s_navi_turn_list.len) != 0)
    {
        goto err_handler;
    }

    s_navi_turn_list.cache.tnav_reader = &s_tnav_reader;

    return 0;

err_handler:
    qw_f_close(s_tnav_reader.fp);
    s_tnav_reader.fp = NULL;

    return -1;
}

//开始转向处理时调用，做一些初始化工作
int navi_turn_process_start(const char *path)
{
    if (path == NULL || strlen(path) > 94)
    {
        return -1;
    }

    sprintf(s_tnav_path, "%s.tnav", path);

#ifdef NAVI_NEW
    rt_kprintf("[NAVI_NEW] tnav start, writer.fp = %x, reader.fp = %x\n", s_tnav_writer.fp, s_tnav_reader.fp);
#endif

    if (qw_f_open(&s_tnav_writer.fp, s_tnav_path, QW_FA_CREATE_ALWAYS | QW_FA_WRITE) != QW_OK)
    {
        s_tnav_writer.fp = NULL;
        return -1;
    }

#ifdef NAVI_NEW
    rt_kprintf("[NAVI_NEW] tnav created, writer.fp = %x, reader.fp = %x\n", s_tnav_writer.fp, s_tnav_reader.fp);
#endif

    s_navi_turn_segment_array.len = NAVI_TURN_SEGMENTS_NUM;
    s_navi_turn_list.len = 0;

    //写入占位用的数据
    if (tnav_header_placeholder_write(&s_tnav_writer) != 0)
    {
        goto err_handler;
    }

    if (tnav_turn_segment_array_write(&s_tnav_writer, &s_navi_turn_segment_array) != 0)
    {
        goto err_handler;
    }

    if (tnav_turn_num_write(&s_tnav_writer, 0) != 0)
    {
        goto err_handler;
    }

    navi_turn_reset();

#if NAVI_TURN_CALC_WAYNAME
    router_wayname_calc_start();
#endif
    return 0;

err_handler:
    qw_f_close(s_tnav_writer.fp);
    s_tnav_writer.fp = NULL;
    qw_f_unlink(s_tnav_path);
    return -1;
}

//导航转向处理
int navi_turn_process(const NaviWaypointAdc *wpadc)
{
    if (wpadc == NULL)
    {
        return -1;
    }

    const NaviWaypointDc wpdc = {
        .lng = wpadc->lng,
        .lat = wpadc->lat,
        .dist = wpadc->dist,
        .course = wpadc->course,
    };

    NaviTurnEx turn_ex = { 0 };
    NaviTurn turn = { 0 };

    //转向计算
    if (navi_turn_calculator_exec(&s_navi_turn_calculator, &wpdc, &turn_ex) != 0)
    {
        return 0;
    }

    //排除直行
    if (fabsf(turn_ex.angle) < NAVI_TURN_GO_STRAIGHT_ANGLE_THRES)
    {
        return 0;
    }

    turn.start_dist = turn_ex.start.dist;
    turn.end_dist = turn_ex.end.dist;
    turn.angle = turn_ex.angle;
    turn.dist = turn.end_dist - turn.start_dist;

#if NAVI_TURN_CALC_WAYNAME
    router_wayname_calc_exec(turn_ex.end.lng, turn_ex.end.lat, turn_ex.end.course, turn.wayname);
    router_wayname_calc_exec(turn_ex.start.lng, turn_ex.start.lat, navi_util_course_reverse_calc(turn_ex.start.course), turn.wayname2);
#endif
    //将转向写入文件
    if (tnav_turn_data_write(&s_tnav_writer, &turn) != 0)
    {
#if NAVI_TURN_CALC_WAYNAME
        router_wayname_calc_end();
#endif
        goto err_handler;
    }

    s_navi_turn_list.len += 1;

    //转向分段
    navi_turn_segmentor_exec(&s_navi_turn_segmentor, &turn);

    return 0;

err_handler:
    qw_f_close(s_tnav_writer.fp);
    s_tnav_writer.fp = NULL;
    qw_f_unlink(s_tnav_path);
    return -1;
}

//转向处理完成时调用
int navi_turn_process_end(const NaviWaypointAdc *wpadc)
{
    if (wpadc == NULL)
    {
        return -1;
    }

    if (navi_turn_process(wpadc) != 0)
    {
        return -1;
    }

    NaviTurnEx turn_ex = { 0 };
    NaviTurn turn = { 0 };

    //获取最后一个转向（如果有的话）
    if (navi_turn_calculator_end(&s_navi_turn_calculator, &turn_ex) == 0)
    {
        if (fabsf(turn_ex.angle) >= NAVI_TURN_GO_STRAIGHT_ANGLE_THRES)
        {
            turn.start_dist = turn_ex.start.dist;
            turn.end_dist = turn_ex.end.dist;
            turn.angle = turn_ex.angle;
            turn.dist = turn.end_dist - turn.start_dist;

#if NAVI_TURN_CALC_WAYNAME
            router_wayname_calc_exec(turn_ex.end.lng, turn_ex.end.lat, turn_ex.end.course, turn.wayname);
            router_wayname_calc_exec(turn_ex.start.lng, turn_ex.start.lat, navi_util_course_reverse_calc(turn_ex.start.course), turn.wayname2);
#endif
            if (tnav_turn_data_write(&s_tnav_writer, &turn) != 0)
            {
#if NAVI_TURN_CALC_WAYNAME
                router_wayname_calc_end();
#endif
                goto err_handler;
            }

            s_navi_turn_list.len += 1;

            navi_turn_segmentor_exec(&s_navi_turn_segmentor, &turn);
        }
    }
#if NAVI_TURN_CALC_WAYNAME
    router_wayname_calc_end();
#endif

    //完成转向分段
    navi_turn_segmentor_end(&s_navi_turn_segmentor, wpadc->dist);

    navi_turn_segmentor_data_get(&s_navi_turn_segmentor, &s_navi_turn_segment_array);

    if (tnav_turn_segment_array_write(&s_tnav_writer, &s_navi_turn_segment_array) != 0)
    {
        goto err_handler;
    }

    if (tnav_turn_num_write(&s_tnav_writer, s_navi_turn_list.len) != 0)
    {
        goto err_handler;
    }

    //最后写入文件头
    if (tnav_header_write(&s_tnav_writer) != 0)
    {
        goto err_handler;
    }

    qw_f_close(s_tnav_writer.fp);
    s_tnav_writer.fp = NULL;

#ifdef NAVI_NEW
    rt_kprintf("[NAVI_NEW] tnav end, writer.fp = %x, reader.fp = %x\n", s_tnav_writer.fp, s_tnav_reader.fp);
#endif

    if (qw_f_open(&s_tnav_reader.fp, s_tnav_path, QW_FA_READ) != QW_OK)
    {
        s_tnav_reader.fp = NULL;
        return -1;
    }

#ifdef NAVI_NEW
    rt_kprintf("[NAVI_NEW] tnav reopened, writer.fp = %x, reader.fp = %x\n", s_tnav_writer.fp, s_tnav_reader.fp);
#endif

    s_navi_turn_list.cache.tnav_reader = &s_tnav_reader;

    return 0;

err_handler:
    qw_f_close(s_tnav_writer.fp);
    s_tnav_writer.fp = NULL;
    qw_f_unlink(s_tnav_path);
    return -1;
}

//中止导航转向处理
void navi_turn_process_terminate(void)
{
    if (s_tnav_writer.fp != NULL)
    {
        qw_f_close(s_tnav_writer.fp);
        s_tnav_writer.fp = NULL;
        qw_f_unlink(s_tnav_path);
    }
}

//导航转向处理成功，但是由于外部原因无法使用，退出
void navi_turn_process_exit(void)
{
    if (s_tnav_reader.fp != NULL)
    {
        qw_f_close(s_tnav_reader.fp);
        s_tnav_reader.fp = NULL;
        qw_f_unlink(s_tnav_path);
    }
}

//转向匹配
int navi_turn_match(float dist, uint8_t is_reverse, NaviTurn *output, uint32_t *idx)
{
    return navi_turn_matcher_exec(&s_navi_turn_matcher, dist, is_reverse, output, idx);
}

//停止导航转向匹配
void navi_turn_match_stop(void)
{
    if (s_tnav_reader.fp != NULL)
    {
        qw_f_close(s_tnav_reader.fp);
        s_tnav_reader.fp = NULL;
    }
}

//获取导航线路转向数量实现
uint32_t navi_turn_num_get_impl(void)
{
    return s_navi_turn_list.len;
}

//获取指定索引转向实现
int navi_turn_get_impl(uint32_t idx, uint8_t is_reverse, NaviTurn *turn)
{
    if (is_reverse == true)
    {
        return navi_turn_list_get(&s_navi_turn_list, s_navi_turn_list.len - 1 - idx, turn);
    }
    else
    {
        return navi_turn_list_get(&s_navi_turn_list, idx, turn);
    }
}
