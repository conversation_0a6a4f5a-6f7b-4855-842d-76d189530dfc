/**************************************************************
 * @file algo_service_config.c
 * <AUTHOR> (chenyuan<PERSON>@igpsport.com)
 * @brief 算法配置接口
 * @version 0.1
 * @date 2024-11-04
 *
 * @copyright Copyright (c) 2024-2025, Wuhan Qiwu Technology Co., Ltd
 *
**************************************************************/

#include "algo_service_config.h"
#include "algo_service_component_common.h"
#include "math.h"
#include "qw_sensor_common_config.h"
#include "subscribe_service.h"

// 配置示例
deploy_config_t g_deply_config[] = {
    // 算法数据
    [DATA_ID_ALGO_INVAILD] = {DATA_ID_ALGO_INVAILD, CONFIG_QW_ALG_NAME_NULL, CPU_CORE_MAX, {0}},
    [DATA_ID_ALGO_TEST] = {DATA_ID_ALGO_TEST, CONFIG_QW_ALG_NAME_TESE, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_HRM] = {DATA_ID_ALGO_HRM, CONFIG_QW_ALG_NAME_HRM, CPU_CORE_LCPU, {1}},
    [DATA_ID_ALGO_SPO2] = {DATA_ID_ALGO_SPO2, CONFIG_QW_ALG_NAME_SPO2, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_SLEEP_STAGE] = {DATA_ID_ALGO_SLEEP_STAGE, CONFIG_QW_ALG_NAME_SLEEP, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_STEP_COUNT] = {DATA_ID_ALGO_STEP_COUNT, CONFIG_QW_ALG_NAME_STEP, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_REST_HR] = {DATA_ID_ALGO_REST_HR, CONFIG_QW_ALG_NAME_REST_HR, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_ALLDAYSTAMINA] = {DATA_ID_ALGO_ALLDAYSTAMINA, CONFIG_QW_ALG_NAME_ALLDAYSTAMINA, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_STRESS_SCORE] = {DATA_ID_ALGO_STRESS_SCORE, CONFIG_QW_ALG_NAME_STRESS_SCORE, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_HRV_EVALUATE] = {DATA_ID_ALGO_HRV_EVALUATE, CONFIG_QW_ALG_NAME_HRV_EVALUATE, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_FITNESS] = {DATA_ID_ALGO_FITNESS, CONFIG_QW_ALG_NAME_FITNESS, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_ACTIVITY_INTENSE] = {DATA_ID_ALGO_ACTIVITY_INTENSE, CONFIG_QW_ALG_NAME_ACTIVITY, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_CALORIES] = {DATA_ID_ALGO_CALORIES, CONFIG_QW_ALG_NAME_CALORIES, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_DAILY_ACCUM] = {DATA_ID_ALGO_DAILY_ACCUM, CONFIG_QW_ALG_NAME_DAILY_ACCUM, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_DAILY_INCREASE] = {DATA_ID_ALGO_DAILY_INCREASE, CONFIG_QW_ALG_NAME_DAILY_INCREASE, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_EVENT_TIMER] = {DATA_ID_ALGO_EVENT_TIMER, CONFIG_QW_ALG_NAME_EVENT_TIMER, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_RAISE_WRIST] = {DATA_ID_ALGO_RAISE_WRIST, CONFIG_QW_ALG_NAME_RAISE_WRIST, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_STRESS] = {DATA_ID_ALGO_STRESS, CONFIG_QW_ALG_NAME_STRESS, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_HRV] = {DATA_ID_ALGO_HRV, CONFIG_QW_ALG_NAME_HRV, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_RHR] = {DATA_ID_ALGO_RHR, CONFIG_QW_ALG_NAME_RHR, CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_GPS_DATA] = {DATA_ID_ALGO_GPS_DATA, "algo_gps_data", CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_ALTITUDE] = {DATA_ID_ALGO_ALTITUDE, "algo_altitude", CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_ALTITUDE_AVG] = {DATA_ID_ALGO_ALTITUDE_AVG, "algo_altitude_avg", CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_ALTITUDE_DOT] = {DATA_ID_ALGO_ALTITUDE_DOT, "algo_altitude_dot", CPU_CORE_LCPU, {0}},

    [DATA_ID_ALGO_SPEED] = {DATA_ID_ALGO_SPEED, "algo_speed", CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_SPEED_MAX] = {DATA_ID_ALGO_SPEED_MAX, "algo_speed_max", CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_SPEED_AVG] = {DATA_ID_ALGO_SPEED_AVG, "algo_speed_avg", CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_DISTANCE] = {DATA_ID_ALGO_DISTANCE, "algo_distance", CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_ODOMETER] = {DATA_ID_ALGO_ODOMETER, "algo_odometer", CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_HEART_RATE] = {DATA_ID_ALGO_HEART_RATE, "algo_heart_rate", CPU_CORE_LCPU, {0}},
    [DATA_ID_ALGO_HEART_RATE_AVG] = {DATA_ID_ALGO_HEART_RATE_AVG, "algo_heart_rate_avg", CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_CADENCE] = {DATA_ID_ALGO_CADENCE, "algo_cadence", CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_CADENCE_AVG] = {DATA_ID_ALGO_CADENCE_AVG, "algo_cadence_avg", CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_POWER] = {DATA_ID_ALGO_POWER, "algo_power", CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_POWER_AVG] = {DATA_ID_ALGO_POWER_AVG, "algo_power_avg", CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_GRADE] = {DATA_ID_ALGO_GRADE, "algo_grade", CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_GRADE_AVG] = {DATA_ID_ALGO_GRADE_AVG, "algo_grade_avg", CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_POSITION] = {DATA_ID_ALGO_POSITION, CONFIG_QW_ALG_NAME_POSITION, CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_SPORTS_CALORIES] = {DATA_ID_ALGO_SPORTS_CALORIES, CONFIG_QW_ALG_NAME_SPORTS_CALORIES, CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_MOVE_STATUS] = {DATA_ID_ALGO_MOVE_STATUS, CONFIG_QW_ALG_NAME_MOVE_STATUS, CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_POOL_SWIM] = {DATA_ID_ALGO_POOL_SWIM, CONFIG_QW_ALG_NAME_POOL_SWIM, CPU_CORE_HCPU, {0}},

    [DATA_ID_ALGO_RUNNING_DYNAMICS] = {DATA_ID_ALGO_RUNNING_DYNAMICS, CONFIG_QW_ALG_NAME_RUNNING_DYNAMICS, CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_RD_AVG] = {DATA_ID_ALGO_RD_AVG, CONFIG_QW_ALG_NAME_RD_AVG, CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_JUMPROPE] = {DATA_ID_ALGO_JUMPROPE, CONFIG_QW_ALG_NAME_COUNT_TIMES, CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_CHART_ALT] = {DATA_ID_ALGO_CHART_ALT, CONFIG_QW_ALG_NAME_CHART_ALT, CPU_CORE_HCPU, {0}},

    [DATA_ID_ALGO_TIMER] = {DATA_ID_ALGO_TIMER, "algo_timer", CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_LAP_COUNT] = {DATA_ID_ALGO_LAP_COUNT, "algo_lap_count", CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_AUTO_SPORTS_CTRL] = {DATA_ID_ALGO_AUTO_SPORTS_CTRL, CONFIG_QW_ALG_NAME_AUTO_SPORTS_CTRL, CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_BUNNY] = {DATA_ID_ALGO_BUNNY, CONFIG_QW_ALG_NAME_BUNNY, CPU_CORE_HCPU, {0}},
    [DATA_ID_ALGO_TL_TREND] = {DATA_ID_ALGO_TL_TREND, CONFIG_QW_ALG_NAME_TL_TREND, CPU_CORE_LCPU, {0}},

    // 驱动数据
    [DATA_ID_RAW_ACC] = {DATA_ID_RAW_ACC, CONFIG_QW_SENSOR_NAME_ACC, CPU_CORE_LCPU, {26, 52}},
    [DATA_ID_RAW_GYRO] = {DATA_ID_RAW_GYRO, CONFIG_QW_SENSOR_NAME_GYRO, CPU_CORE_LCPU, {26, 52}},

    [DATA_ID_ALGO_PPG_HR] = {DATA_ID_ALGO_PPG_HR, CONFIG_QW_SENSOR_NAME_PPG_ALGO_HR, CPU_CORE_LCPU, {1}},
    [DATA_ID_ALGO_PPG_HRV] = {DATA_ID_ALGO_PPG_HRV, CONFIG_QW_SENSOR_NAME_PPG_ALGO_HRV, CPU_CORE_LCPU, {1}},
    [DATA_ID_ALGO_PPG_SPO2] = {DATA_ID_ALGO_PPG_SPO2, CONFIG_QW_SENSOR_NAME_PPG_ALGO_SPO2, CPU_CORE_LCPU, {1}},
    [DATA_ID_ALGO_PPG_WEAR] = {DATA_ID_ALGO_PPG_WEAR, CONFIG_QW_SENSOR_NAME_PPG_ALGO_WEAR, CPU_CORE_LCPU, {0}},

    [DATA_ID_RAW_PPG_HR] = {DATA_ID_RAW_PPG_HR, CONFIG_QW_SENSOR_NAME_PPG_RAW_HR, CPU_CORE_LCPU, {25}},
    [DATA_ID_RAW_PPG_HRV] = {DATA_ID_RAW_PPG_HRV, CONFIG_QW_SENSOR_NAME_PPG_RAW_HRV, CPU_CORE_LCPU, {25}},
    [DATA_ID_RAW_PPG_SPO2] = {DATA_ID_RAW_PPG_SPO2, CONFIG_QW_SENSOR_NAME_PPG_RAW_SPO2, CPU_CORE_LCPU, {25}},
    [DATA_ID_RAW_PPG_WEAR] = {DATA_ID_RAW_PPG_WEAR, CONFIG_QW_SENSOR_NAME_PPG_RAW_WEAR, CPU_CORE_LCPU, {25}},

    [DATA_ID_BROMETER] = {DATA_ID_BROMETER, CONFIG_QW_SENSOR_NAME_BARO, CPU_CORE_LCPU, {0}},
    [DATA_ID_RAW_GPS] = {DATA_ID_RAW_GPS, "drv_gps", CPU_CORE_HCPU, {0}},
    [DATA_ID_RAW_MAG] = {DATA_ID_RAW_MAG, CONFIG_QW_SENSOR_NAME_MAG, CPU_CORE_LCPU, {50}},
    [DATA_ID_LIGHT] = {DATA_ID_LIGHT, CONFIG_QW_SENSOR_NAME_LIGHT, CPU_CORE_LCPU, {1}},
    [DATA_ID_CAP] = {DATA_ID_CAP, CONFIG_QW_SENSOR_NAME_CAP, CPU_CORE_LCPU, {0}},
    [DATA_ID_OTS] = {DATA_ID_OTS, CONFIG_QW_SENSOR_NAME_OTS, CPU_CORE_LCPU, {0}},

    // 传感器数据（暂定）
    [DATA_ID_RAW_FE_SENSOR] = {DATA_ID_RAW_FE_SENSOR, "ble_fe_sensor", CPU_CORE_HCPU, {0}},         // Fitness Equipment（骑行台，跑步机？）传感器
    [DATA_ID_RAW_LEV_SENSOR] = {DATA_ID_RAW_LEV_SENSOR, "ble_lev_sensor", CPU_CORE_HCPU, {0}},      // Light Electric Vehicle（电助力）传感器
    [DATA_ID_RAW_PWR_SENSOR] = {DATA_ID_RAW_PWR_SENSOR, "ble_pwr_sensor", CPU_CORE_HCPU, {0}},      // 功率计
    [DATA_ID_RAW_SPD_SENSOR] = {DATA_ID_RAW_SPD_SENSOR, "ble_spd_sensor", CPU_CORE_HCPU, {0}},      // 速度传感器
    [DATA_ID_RAW_CBSC_SENSOR] = {DATA_ID_RAW_CBSC_SENSOR, "ble_cbsc_sensor", CPU_CORE_HCPU, {0}},   // 速度踏频二合一传感器
    [DATA_ID_RAW_HR_SENSOR] = {DATA_ID_RAW_HR_SENSOR, "ble_hr_sensor", CPU_CORE_HCPU, {0}},         // 心率传感器
    [DATA_ID_RAW_CAD_SENSOR] = {DATA_ID_RAW_CAD_SENSOR, "ble_cad_sensor", CPU_CORE_HCPU, {0}},      // 心率传感器
    [DATA_ID_RAW_RD_SENSOR] = {DATA_ID_RAW_RD_SENSOR, "ble_rd_sensor", CPU_CORE_HCPU, {0}},         // 跑步动态数据

    // 用户状态数据
    [DATA_ID_STATUS_CYCLE] = {DATA_ID_STATUS_CYCLE, "sta_cycle", CPU_CORE_HCPU, {0}},
    [DATA_ID_STATUS_DATA_COLLECT_CTRL] = {DATA_ID_STATUS_DATA_COLLECT_CTRL, "sta_data_collect_ctrl", CPU_CORE_HCPU, {0}},   // 算法/传感器数据采集控制
    [DATA_ID_STATUS_LCPU] = {DATA_ID_STATUS_LCPU, "sta_lcpu", CPU_CORE_LCPU, {0}},                                          // LCPU 开关机状态
    [DATA_ID_STATUS_HCPU] = {DATA_ID_STATUS_HCPU, "sta_hcpu", CPU_CORE_HCPU, {0}},                                          // HCPU 开关机状态

    // 事件或则通知数据
    [DATA_ID_EVENT_ONE_MIN_REACH] =         {DATA_ID_EVENT_ONE_MIN_REACH,           CONFIG_QW_EVENT_NAME_ONE_MINUTE, CPU_CORE_LCPU, {0}},
    [DATA_ID_EVENT_DAILY_REMIND] =          {DATA_ID_EVENT_DAILY_REMIND,            CONFIG_QW_EVENT_NAME_REMIND, CPU_CORE_LCPU, {0}},
    [DATA_ID_EVENT_DEVELOPER_CHG] =         {DATA_ID_EVENT_DEVELOPER_CHG,           CONFIG_QW_EVENT_NAME_DEVELOPER, CPU_CORE_HCPU, {0}},
    [DATA_ID_EVENT_WAKEUP] =                {DATA_ID_EVENT_WAKEUP,                  CONFIG_QW_EVENT_NAME_WAKEUP, CPU_CORE_LCPU, {0}},
    [DATA_ID_EVENT_HRM_MANUAL_MEASURE] =    {DATA_ID_EVENT_HRM_MANUAL_MEASURE,      CONFIG_QW_EVENT_NAME_HR_MANUAL_MEASURE, CPU_CORE_HCPU, {0}},
    [DATA_ID_EVENT_ALTITUDE_CAL] =          {DATA_ID_EVENT_ALTITUDE_CAL,            CONFIG_QW_EVENT_NAME_CLICK_ALT, CPU_CORE_HCPU, {0}},
    [DATA_ID_EVENT_HRM_REMIND] =            {DATA_ID_EVENT_HRM_REMIND,              CONFIG_QW_EVENT_NAME_HR_REMIND, CPU_CORE_LCPU, {0}},
    [DATA_ID_EVENT_ACTIVETYPE_CHG] =        {DATA_ID_EVENT_ACTIVETYPE_CHG,          CONFIG_QW_EVENT_NAME_ACTIVETYPE, CPU_CORE_LCPU, {0}},
    [DATA_ID_EVENT_FITNESS_EVENT] =         {DATA_ID_EVENT_FITNESS_EVENT,           CONFIG_QW_EVENT_NAME_FITNESS_EVENT, CPU_CORE_HCPU, {0}},
    [DATA_ID_EVENT_SPO2_MANUAL_MEASURE] =   {DATA_ID_EVENT_SPO2_MANUAL_MEASURE,     CONFIG_QW_EVENT_NAME_SPO2_MANUAL_MEASURE, CPU_CORE_HCPU, {0}},
    [DATA_ID_EVENT_STRESS_MANUAL_MEASURE] = {DATA_ID_EVENT_STRESS_MANUAL_MEASURE,   CONFIG_QW_EVENT_NAME_STRESS_MANUAL_MEASURE, CPU_CORE_HCPU, {0}},
    [DATA_ID_EVENT_STRESS_REMIND] =         {DATA_ID_EVENT_STRESS_REMIND,           CONFIG_QW_EVENT_NAME_STRESS_REMIND, CPU_CORE_LCPU, {0}},
    [DATA_ID_EVENT_RD_DRV] =                {DATA_ID_EVENT_RD_DRV,                  CONFIG_QW_EVENT_NAME_RD_DRV, CPU_CORE_HCPU, {0}},
    [DATA_ID_EVENT_COUNTTIMES_DRV] =        {DATA_ID_EVENT_COUNTTIMES_DRV,          CONFIG_QW_EVENT_NAME_COUNTTIMES_DRV, CPU_CORE_HCPU, {0}},
    [DATA_ID_EVENT_SPORTS_CALORIES_DRV] =   {DATA_ID_EVENT_SPORTS_CALORIES_DRV,     CONFIG_QW_EVENT_NAME_CALORIES_DRV, CPU_CORE_HCPU, {0}},
    [DATA_ID_EVENT_MOVE_STATUS_DRV] =       {DATA_ID_EVENT_MOVE_STATUS_DRV,         CONFIG_QW_EVENT_NAME_MOVE_STATUS_DRV, CPU_CORE_HCPU, {0}},
    [DATA_ID_EVENT_FITNESS_SAVE_END] =      {DATA_ID_EVENT_FITNESS_SAVE_END,        CONFIG_QW_EVENT_NAME_FITNESS_SAVE_END, CPU_CORE_HCPU, {0}},
    [DATA_ID_EVENT_TIME_SYNC] =             {DATA_ID_EVENT_TIME_SYNC,               CONFIG_QW_EVENT_NAME_SYNC_TIME, CPU_CORE_HCPU, {0}},
    [DATA_ID_EVENT_BACKLIGHT_STATUS] =      {DATA_ID_EVENT_BACKLIGHT_STATUS,        CONFIG_QW_EVENT_NAME_BK_STATUS, CPU_CORE_HCPU, {0}},
    [DATA_ID_EVENT_LCPU_ENV_UPDATE] =       {DATA_ID_EVENT_LCPU_ENV_UPDATE,         CONFIG_QW_EVENT_NAME_LCPU_ENV_UPDATE, CPU_CORE_LCPU, {0}},
    [DATA_ID_EVENT_NAVI] =                  {DATA_ID_EVENT_NAVI,                    CONFIG_QW_EVENT_NAME_NAVI, CPU_CORE_HCPU, {0}},
    [DATA_ID_EVENT_POOL_SWIM_DRV] =         {DATA_ID_EVENT_POOL_SWIM_DRV,           CONFIG_QW_EVENT_NAME_POOL_SWIM_DRV, CPU_CORE_HCPU, {0}},
    [DATA_ID_EVENT_SPORTS_CTRL] =           {DATA_ID_EVENT_SPORTS_CTRL,             CONFIG_QW_EVENT_NAME_SPORTS_CTRL, CPU_CORE_HCPU, {0}},
    [DATA_ID_EVENT_MOVE_STATUS_CTRL] =      {DATA_ID_EVENT_MOVE_STATUS_CTRL,        CONFIG_QW_EVENT_NAME_MOVE_STATUS_CTRL, CPU_CORE_HCPU, {0}},
    [DATA_ID_EVENT_GM_PREVIOUS_SAVE] =      {DATA_ID_EVENT_GM_PREVIOUS_SAVE,        CONFIG_QW_EVENT_NAME_GM_PREVIOUS_SAVE, CPU_CORE_LCPU, {0}},
    [DATA_ID_EVENT_MEDIA_CTRL] =            {DATA_ID_EVENT_MEDIA_CTRL,              CONFIG_QW_EVENT_NAME_MEDIA_CTRL, CPU_CORE_HCPU, {0}},
};

/**
 * @brief 检测配置是否支持
 *
 * @param topic_name 主题名
 * @param config 配置
 *
 * @return 支持 : 主题名映射的ID 不支持 : DATA_ID_ALGO_INVAILD
 */
uint32_t check_config_is_support(const char *topic_name, const optional_config_t *config)
{
    if (topic_name == NULL)
    {
        return DATA_ID_ALGO_INVAILD;
    }
    for (int i = 0; i < sizeof(g_deply_config) / sizeof(g_deply_config[0]); i++)
    {
        // 判断名称是否匹配
        if (strcmp(topic_name, g_deply_config[i].algo_name) != 0)
        {
            continue;
        }
        if (config == NULL || fabs(config->sampling_rate) < FLOAT_EQUL_THRESHOLD)
        {
            return g_deply_config[i].data_id;
        }
        for (int j = 0; j < SAMPLING_RATE_NUM; j++)
        {
            // 判断采样率是否匹配
            if (fabs(config->sampling_rate - g_deply_config[i].sampling_rate[j]) < FLOAT_EQUL_THRESHOLD)
            {
                return g_deply_config[i].data_id;
            }
        }
    }
    return DATA_ID_ALGO_INVAILD;
}

/**
 * @brief 检测配置是否支持
 *
 * @param topic_id 主题id
 * @param config 配置
 *
 * @return 支持 : true 不支持 : false
 */
bool check_config_is_support_id(uint32_t topic_id, const optional_config_t *config)
{
    if (topic_id <= DATA_ID_ALGO_INVAILD || topic_id >= DATA_ID_MAX)
    {
        return false;
    }
    if (config == NULL)
    {
        return true; // 取消订阅或获取状态判断
    }
    if (fabs(config->sampling_rate) < FLOAT_EQUL_THRESHOLD)
    {
        return true; // 采样率0判断
    }
    for (int j = 0; j < SAMPLING_RATE_NUM; j++)
    {
        // 判断采样率是否匹配
        if (fabs(config->sampling_rate - g_deply_config[topic_id].sampling_rate[j]) < FLOAT_EQUL_THRESHOLD)
        {
            return true;
        }
    }
    return false;
}

/**
 * @brief 获取id对应的部署配置
 *
 * @param id 数据id
 * @return const deploy_config_t* 部署配置
 */
const deploy_config_t *get_deploy_config_of_id(uint32_t id)
{
    if (hw_version_get() == HARDWARE_VERSION_A5)
    {
        if (id == DATA_ID_LIGHT)
        {
            g_deply_config[id].cpu_id = CPU_CORE_HCPU;
        }
    }
    return &g_deply_config[id];
}
