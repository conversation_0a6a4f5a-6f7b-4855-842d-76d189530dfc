/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   burn_test.c
@Time    :   2025/06/13 10:10:20
*
**************************************************************************/

#include "burn_test.h"
#include "gui_event_service.h"
#ifdef IGS_DEV
#include "beep.h"
#include "drv_beep.h"
#include "motor.h"
#include "drv_motor.h"
#include "rtdef.h"
#include "cfg_header_def.h"
#include "sensor_service_module/sensor_control_app.h"
#include "poweroff_ctrl.h"
#include "ble_data_inf.h"
#include "ble_test_cmd.h"
#include "boot_switch.h"
#include "gui_event_service.h"
#include "gps_dev.h"
#include "service_gui_health.h"
#include "qw_system_params.h"
#include "kvdb.h"
#include "subscribe_service.h"
#include "qw_sensor.h"
#include "subscribe_data.h"
#include <rtthread.h>
#include <rtdevice.h>
#include "drivers/mmcsd_core.h"
#endif

#define BURN_TASK_INTERVAL_MS 10         // 老化测试线程执行间隔
#define SECNODS_TO_TICK(s) (s * 1000 / BURN_TASK_INTERVAL_MS) // 秒转换为tick

#define DEFAULT_BURN_REBOOT_COUNT      3  // 默认老化重启次数
#define DEFAULT_BURN_TIMES_COUNT       3  // 默认老化测试次数

rt_thread_t burn_test_thread = RT_NULL;
static bool burn_test_running_status = false;

//需要保存的数据
typedef struct
{
    uint8_t test_status;             // 测试状态
    uint8_t test_loop;               // 测试循环次数
    uint8_t _current_reboot_count;   // 当前reboot次数
    uint8_t _target_reboot_count;    // 目标reboot次数
    uint32_t _target_brun_fun;       // 目标执行的老化测试项MAP列表，最多31项
    uint32_t _target_burn_en;        // 目标老化测试项执行使能MAP列表，最多31项
    uint8_t  _target_brun_count[32]; // 目标测试项老化测试次数
    uint8_t  _target_brun_parm1[32]; // 目标测试项老化测试参数一
    uint8_t  _target_brun_parm2[32]; // 目标测试项老化测试参数二
} cfg_burn_t;

typedef struct
{
    uint32_t _target_burn_now;       // 目标老化测试项执行完毕MAP列表，最多31项
    int8_t   _target_brun_sta[32];   // 目标老化测试进度，状态
    int16_t  _target_brun_loop[32];  // 目标测试项老化测试次数
} cfg_burn_sta_t;

typedef struct {
    uint8_t  cycle;     // 鸣叫次数
    uint8_t  vol_level; // 音量
    uint16_t on_time;   // 鸣叫时间
    uint16_t off_time;  // 间隔时间
} beep_param_t;


typedef struct {
    uint8_t cycle;     // 老化测试次数
    uint8_t on_time;   // 老化测试时间，单位秒
} lcd_burn_param_t;

//用户注册的回调
static void (*burn_report_callback)(void) = RT_NULL;

#define BURN_KEY              "burn_info"

static bool burn_default_config(void *value, uint32_t value_size);

static void *burn_info_store_handler = NULL;
static cfg_burn_t *p_burn_info = NULL;

static const kv_node_config burn_config = {
    .encode = NULL,
    .decode = NULL,
    .default_config = burn_default_config,
};

// 默认参数实例
static const cfg_burn_t s_default_config = {
    .test_status = BURN_TEST_NOT_BEGIN,                // 默认测试状态为未开始
    .test_loop = DEFAULT_BURN_TIMES_COUNT,             // 默认老化测试循环次数
    ._current_reboot_count = 0,
    ._target_reboot_count = DEFAULT_BURN_REBOOT_COUNT, // 默认目标重启次数
    ._target_brun_fun = DEFAULT_BURN_FUN,
    ._target_burn_en  = DEFAULT_BURN_FUN,
    ._target_brun_count = DEFAULT_BURN_COUNT_ARRAY,    // 目标测试项老化测试次数
    ._target_brun_parm1 = DEFAULT_BURN_PARM1_ARRAY,    // 目标测试项老化测试参数一
    ._target_brun_parm2 = DEFAULT_BURN_PARM2_ARRAY,    // 目标测试项老化测试参数二
};

static cfg_burn_sta_t burn_state = {
    ._target_burn_now = 0,
};

static const beep_param_t default_beep_param = {
    .cycle = 10,
    .on_time = 100,
    .off_time = 900,
    .vol_level = 3
};

static const beep_param_t default_motor_param = {
    .cycle = 10,
    .on_time = 100,
    .off_time = 1400,
    .vol_level = 3
};

static const lcd_burn_param_t default_lcd_param = {
    .cycle = 10,   // 进行10次循环
    .on_time = 6,  // 单次老化测试6S
};

static void kvdb_save_burn_reboot_info(void);

static int8_t test_beep(int tick)
{
    int8_t brun_sta = -1;
 #ifdef IGS_DEV
    if (tick == -1)
    {
        // 蜂鸣器老化测试停止
        rt_device_t beep_device = rt_device_find("beep");
        if(beep_device)
        {
            rt_device_control(beep_device, RT_DEVICE_CTRL_STOP, NULL);
        }
        return brun_sta;
    }

    if(p_burn_info == NULL)  return brun_sta;

    int test_loop = p_burn_info->test_loop*p_burn_info->_target_brun_count[ITEM_BEEP];

    if ((tick % SECNODS_TO_TICK(10)) == 0)
    {
        FACTORY_LOG_I("Burn Beep %d->%d...",test_loop,burn_state._target_brun_loop[ITEM_BEEP]);
    }

    if((test_loop>0)&&(test_loop>=burn_state._target_brun_loop[ITEM_BEEP]))
    {
        brun_sta = (burn_state._target_brun_loop[ITEM_BEEP]*100)/test_loop;
        uint32_t play_time_ms = (default_beep_param.on_time + default_beep_param.off_time) * default_beep_param.cycle / BURN_TASK_INTERVAL_MS;
        // 播放完一个周期重新打开一次
        if (tick % play_time_ms == 0) {
            // 未完成配置的老化次数
            if(burn_state._target_brun_loop[ITEM_BEEP] < test_loop)
            {
                // 蜂鸣器老化测试播放：播放完成自动停止
                rt_device_t beep_device = rt_device_find("beep");
                if(beep_device)
                {
                    beep_work_param_t beep_param = { 0 };
                    beep_param.freq = 1000;
                    beep_param.duty_cycle = 90;
                    beep_param.voltage_multiplier = default_beep_param.vol_level;
                    beep_param.beep = default_beep_param.on_time;
                    beep_param.interval = default_beep_param.off_time;
                    beep_param.repeat = default_beep_param.cycle;
                    beep_param.open = true;
                    rt_device_control(beep_device, RT_DEVICE_CTRL_CONFIG, (void*)&beep_param);
                }
                burn_state._target_brun_loop[ITEM_BEEP]++;
            }
        }
    }
#endif
    return brun_sta;
}

static int8_t test_motor(int tick)
{
    int8_t brun_sta = -1;
#ifdef IGS_DEV
    if (tick == -1)
    {
        rt_device_t motor_device = rt_device_find("motor");
        if(motor_device)
        {
            rt_device_control(motor_device, RT_DEVICE_CTRL_STOP, NULL);
        }
        return brun_sta;
    }

    if(p_burn_info == NULL)  return brun_sta;

    int test_loop = p_burn_info->test_loop*p_burn_info->_target_brun_count[ITEM_MOTOR];

    if ((tick % SECNODS_TO_TICK(10)) == 0)
    {
        FACTORY_LOG_I("Burn Motor %d->%d...",test_loop,burn_state._target_brun_loop[ITEM_MOTOR]);
    }
    if((test_loop>0)&&(test_loop>=burn_state._target_brun_loop[ITEM_MOTOR]))
    {
        brun_sta = (burn_state._target_brun_loop[ITEM_MOTOR]*100)/test_loop;
        uint32_t play_time_ms = (default_motor_param.on_time + default_motor_param.off_time) * default_motor_param.cycle / BURN_TASK_INTERVAL_MS;
        if (tick % play_time_ms == 0) // 播放完一个周期重新打开一次
        {
            // 未完成配置的老化次数
            if(burn_state._target_brun_loop[ITEM_MOTOR] < test_loop)
            {
                rt_device_t motor_device = rt_device_find("motor");
                if(motor_device)
                {
                    motor_work_param_t motor_param = { 0 };
                    motor_param.freq = FREQ_DI;
                    motor_param.duty_cycle = 90;
                    motor_param.voltage_multiplier = default_motor_param.vol_level;
                    motor_param.beep = default_motor_param.on_time;
                    motor_param.interval = default_motor_param.off_time;
                    motor_param.repeat = default_motor_param.cycle;
                    motor_param.open = true;
                    rt_device_control(motor_device, RT_DEVICE_CTRL_CONFIG, (void*)&motor_param);
                }
                burn_state._target_brun_loop[ITEM_MOTOR]++;
            }
        }
    }
#endif
    return brun_sta;
}


static int8_t test_lcd(int tick)
{
    int8_t brun_sta = -1;
#ifdef IGS_DEV
    if (tick == -1) return brun_sta;
    if(p_burn_info == NULL)  return brun_sta;

    int test_loop = p_burn_info->test_loop*p_burn_info->_target_brun_count[ITEM_LCD];
    if ((tick % SECNODS_TO_TICK(10)) == 0)
    {
        FACTORY_LOG_I("Burn Lcd %d->%d...",test_loop,burn_state._target_brun_loop[ITEM_LCD]);
    }
    if((test_loop>0)&&(test_loop>=burn_state._target_brun_loop[ITEM_LCD]))
    {
        brun_sta = (burn_state._target_brun_loop[ITEM_LCD]*100)/test_loop;
        int test_time = default_lcd_param.cycle * default_lcd_param.on_time;
        //uint32_t play_time_ms = (default_motor_param.on_time + default_motor_param.off_time) * default_motor_param.cycle / BURN_TASK_INTERVAL_MS;
        if ((tick % SECNODS_TO_TICK(test_time)) == 0)
        {
            // 未完成配置的老化次数
            if(burn_state._target_brun_loop[ITEM_LCD] < test_loop)
            {
                // 已经使用另外的界面实现LCD老化，这里只提供进度和终止控制逻辑
                burn_state._target_brun_loop[ITEM_LCD]++;
            }
        }
    }
#endif
    return brun_sta;
}

static int8_t test_gps(int tick)
{
    int8_t brun_sta = -1;
#ifdef IGS_DEV
    static bool gps_on = false;
    if (tick == -1)
    {
        if(gps_on)
        {
            GPS_DEV_POWER_MODE mode = GPS_DEV_POWER_MODE_POWER_OFF;
            gps_dev_control(GPS_DEV_CMD_SWITCH_POWER_MODE, (void *)&mode);
            gps_on = false; // 停止GPS
        }
        return brun_sta;
    }

    if(p_burn_info == NULL)  return brun_sta;

    int test_loop = p_burn_info->test_loop*p_burn_info->_target_brun_count[ITEM_GPS];
    if ((tick % SECNODS_TO_TICK(10)) == 0)
    {
        FACTORY_LOG_I("Burn Gps %d->%d...",test_loop,burn_state._target_brun_loop[ITEM_GPS]);
    }
    if((test_loop>0)&&(test_loop>=burn_state._target_brun_loop[ITEM_GPS]))
    {
        brun_sta = (burn_state._target_brun_loop[ITEM_GPS]*100)/test_loop;
        int test_time = 120;
        if (tick % SECNODS_TO_TICK((test_time/2)) == 30) // 每60秒开关GPS一次
        {
            // 未完成配置的老化次数
            if(burn_state._target_brun_loop[ITEM_GPS] < test_loop)
            {
                if (!gps_on)
                {
                    GPS_DEV_POWER_MODE mode = GPS_DEV_POWER_MODE_NORMAL;
                    gps_dev_control(GPS_DEV_CMD_SWITCH_POWER_MODE, (void *)&mode);
                    gps_on = true;
                }
                else
                {
                    GPS_DEV_POWER_MODE mode = GPS_DEV_POWER_MODE_POWER_OFF;
                    gps_dev_control(GPS_DEV_CMD_SWITCH_POWER_MODE, (void *)&mode);
                    gps_on = false;
                    burn_state._target_brun_loop[ITEM_GPS]++;
                }
            }
        }
    }
#endif
    return brun_sta;
}

static int8_t test_nrf52832(int tick)
{
    int8_t brun_sta = -1;
#ifdef IGS_DEV
    static bool nrf52832_on = false;
    if (tick == -1)
    {
        if(nrf52832_on)
        {
            sensor_control_event(SENSOR_APP_EVENT_CLOSE_ANT_FIND);
            nrf52832_on = false; // 停止
        }
        return brun_sta;
    }

    if(p_burn_info == NULL)  return brun_sta;

    int test_loop = p_burn_info->test_loop*p_burn_info->_target_brun_count[ITEM_BLE];
    if ((tick % SECNODS_TO_TICK(10)) == 0)
    {
        FACTORY_LOG_I("Burn Ble %d->%d...",test_loop,burn_state._target_brun_loop[ITEM_BLE]);
    }
    if((test_loop>0)&&(test_loop>=burn_state._target_brun_loop[ITEM_BLE]))
    {
        brun_sta = (burn_state._target_brun_loop[ITEM_BLE]*100)/test_loop;
        int test_time = 60;
        if (tick % SECNODS_TO_TICK((test_time/2)) == 15) // 每30秒开关一次
        {
            // 未完成配置的老化次数
            if(burn_state._target_brun_loop[ITEM_BLE] < test_loop)
            {
                if (!nrf52832_on)
                {
                    sensor_control_event(SENSOR_APP_EVENT_OPEN_ANT_FIND);
                    nrf52832_on = true;
                }
                else
                {
                    sensor_control_event(SENSOR_APP_EVENT_CLOSE_ANT_FIND);
                    nrf52832_on = false;
                    burn_state._target_brun_loop[ITEM_BLE]++;
                }
            }
        }
    }
#endif
    return brun_sta;
}


static void drv_accel_callback(const void* in, uint32_t len)
{
    // 六轴数据不处理
}
static void drv_gyro_callback(const void* in, uint32_t len)
{
    // 六轴数据不处理
}
static int8_t test_att(int tick)
{
    int8_t brun_sta = -1;
#ifdef IGS_DEV
    static bool att_on = false;
    if (tick == -1)
    {
        if(att_on) att_on = false; // 停止
        return brun_sta;
    }

    if(p_burn_info == NULL)  return brun_sta;

    int test_loop = p_burn_info->test_loop*p_burn_info->_target_brun_count[ITEM_ACC];
    if ((tick % SECNODS_TO_TICK(10)) == 0)
    {
        FACTORY_LOG_I("Burn Att %d->%d...",test_loop,burn_state._target_brun_loop[ITEM_ACC]);
    }
    if((test_loop>0)&&(test_loop>=burn_state._target_brun_loop[ITEM_ACC]))
    {
        brun_sta = (burn_state._target_brun_loop[ITEM_ACC]*100)/test_loop;
        int test_time = 30;
        if (tick % SECNODS_TO_TICK((test_time/2)) == 0) // 每15秒开关一次
        {
            // 未完成配置的老化次数
            if(burn_state._target_brun_loop[ITEM_ACC] < test_loop)
            {
                if (!att_on) {
                    //开启六轴
                    #ifdef IGS_DEV
                        optional_config_t config =
                        {
                            .sampling_rate = 26,
                        };
                        int32_t ret = qw_dataserver_subscribe_id(DATA_ID_RAW_ACC, drv_accel_callback, &config);
                        if (ret != 0)
                        {
                            FACTORY_LOG_D("FAT:","test_att erro ret:%d\n", ret);
                        }
                        ret = qw_dataserver_subscribe_id(DATA_ID_RAW_GYRO, drv_gyro_callback, &config);
                        if (ret != 0)
                        {
                            FACTORY_LOG_D("FAT:","test_att erro ret:%d\n", ret);
                        }
                    #endif
                    att_on = true;
                }
                else
                {
                    //关闭六轴
                    #ifdef IGS_DEV
                        int32_t ret = qw_dataserver_unsubscribe_id(DATA_ID_RAW_ACC, drv_accel_callback);
                        if (ret == 0)
                        {
                            FACTORY_LOG_D("FAT:","test_att::sensor_accel unsub\n");
                        }
                        ret = qw_dataserver_unsubscribe_id(DATA_ID_RAW_GYRO, drv_gyro_callback);
                        if (ret == 0)
                        {
                            FACTORY_LOG_D("FAT:","test_att::sensor_gyro unsub\n");
                        }
                    #endif
                    att_on = false;
                    burn_state._target_brun_loop[ITEM_ACC]++;
                }
            }
        }
    }
#endif
    return brun_sta;
}

#ifdef IGS_DEV
uint8_t emmc_test_rand(void)
{
    srand(time(0));
    // 生成一个0到255之间的随机数
    return rand() % 256;
}

#define MTD_DEV             "sd0"
#define SD_PAGE_SIZE        (512)

rt_err_t emmc_test_fun(void)
{
    rt_err_t res = RT_EOK;
    rt_device_t dev = rt_device_find(MTD_DEV);
    if(dev == RT_NULL) return RT_ERROR;
    static bool first = true;
    if (first)
    {
        first = false;
        res = rt_device_open(dev, RT_DEVICE_FLAG_RDWR);
        if (res == RT_EOK || res == -RT_EBUSY)
        {
            //FACTORY_LOG_I("open sd0 ok\n");
        }
        else
        {
            FACTORY_LOG_E("open sd0 err\n");
            return res;
        }
    }
    rt_off_t offset = 7 + RT_MMCSD_USER_OFFSET - (RT_MMCSD_USER_OFFSET + 63); //SD_PAGE_SIZE
    rt_uint8_t *sector = (rt_uint8_t *)rt_malloc(SD_PAGE_SIZE);
    if (sector == RT_NULL)
    {
        FACTORY_LOG_E("allocate partition sector buffer failed!");
        return -RT_ENOMEM;
    }

    uint8_t emmc_rand = emmc_test_rand();
    memset(sector, emmc_rand,SD_PAGE_SIZE);        // 赋值随机数
    rt_device_write(dev, offset, sector, 1);      // 写入随机数
    memset(sector, 0,SD_PAGE_SIZE);                // 清除
    rt_device_read(dev, offset, sector, 1);       // 读取数据

    // 数据一致性判断
    int i = 0;
    for(;i < SD_PAGE_SIZE;i++)
    {
        if(sector[i] != emmc_rand) break;
    }
    rt_free(sector);

    if(i==SD_PAGE_SIZE)
    {
      FACTORY_LOG_D("emmc_test:check sd0 data ok\n");
      res = RT_EOK;
    }
    else{
      FACTORY_LOG_D("emmc_test:check sd0 data err\n");
      res = RT_ERROR;
    }

    SCB_CleanDCache();
    SCB_InvalidateDCache();
    return res;
}
#endif

static int8_t test_emmc(int tick)
{
    int8_t brun_sta = -1;
#ifdef IGS_DEV
    if (tick == -1) return brun_sta;
    if(p_burn_info == NULL)  return brun_sta;

    int test_loop = p_burn_info->test_loop*p_burn_info->_target_brun_count[ITEM_EMMC];
    if ((tick % SECNODS_TO_TICK(10)) == 0)
    {
        FACTORY_LOG_I("Burn Emmc %d->%d...",test_loop,burn_state._target_brun_loop[ITEM_EMMC]);
    }
    if((test_loop>0)&&(test_loop>=burn_state._target_brun_loop[ITEM_EMMC]))
    {
        brun_sta = (burn_state._target_brun_loop[ITEM_EMMC]*100)/test_loop;
        int test_time = 30;
        if (tick % SECNODS_TO_TICK((test_time)) == 0) // 每30秒测试一次
        {
            // 未完成配置的老化次数
            if(burn_state._target_brun_loop[ITEM_EMMC] < test_loop)
            {
                rt_err_t res = emmc_test_fun();
                FACTORY_LOG_I("emmc_test:res = 0x%08X\n",res);
                burn_state._target_brun_loop[ITEM_EMMC]++;
            }
        }
    }
#endif
    return brun_sta;
}

static int8_t test_ppg(int tick)
{
    int8_t brun_sta = -1;
    static uint8_t live_wear_switch = 0; // 记录活体检测开关设置
#ifdef IGS_DEV
    static bool ppg_on = false;
    if (tick == -1) {
        if(ppg_on)
        {
            set_system_params()->wear_state = 0;
            service_gui_manual_measure(HEARTRATE, false);
            if (live_wear_switch != 0)
            {
                set_live_wear_switch(live_wear_switch); // 恢复活体检测开关设置
            }
            ppg_on = false; // 停止
        }
        return brun_sta;
    }

    if(p_burn_info == NULL)  return brun_sta;

    int test_loop = p_burn_info->test_loop*p_burn_info->_target_brun_count[ITEM_PPG];
    if ((tick % SECNODS_TO_TICK(10)) == 0) {
        FACTORY_LOG_I("Burn Ppg %d->%d...",test_loop,burn_state._target_brun_loop[ITEM_PPG]);
    }
    if((test_loop>0)&&(test_loop>=burn_state._target_brun_loop[ITEM_PPG]))
    {
        brun_sta = (burn_state._target_brun_loop[ITEM_PPG]*100)/test_loop;
        int test_time = 40;
        if (tick % SECNODS_TO_TICK((test_time/2)) == 10) // 每20秒开关一次
        {
            // 未完成配置的老化次数
            if(burn_state._target_brun_loop[ITEM_PPG] < test_loop)
            {
                if (!ppg_on)
                {
                    live_wear_switch = get_live_wear_switch();  // 获取活体检测开关状态
                    if (live_wear_switch != 0)
                    {
                        set_live_wear_switch(0);                // 关闭活体检测
                    }
                    set_system_params()->wear_state = 1;
                    service_gui_manual_measure(HEARTRATE, true);
                    ppg_on = true;
                }
                else
                {
                    set_system_params()->wear_state = 0;
                    service_gui_manual_measure(HEARTRATE, false);
                    if (live_wear_switch != 0)
                    {
                        set_live_wear_switch(live_wear_switch); // 恢复活体检测开关设置
                    }
                    ppg_on = false;
                    burn_state._target_brun_loop[ITEM_PPG]++;
                }
            }
        }
    }
#endif
    return brun_sta;
}

static bool test_item_entry(int tick)
{
    int burn_sta = 0;
    if((p_burn_info == NULL)||(p_burn_info->_target_burn_en == 0))  return false;
    // 根据配置项进行老化测试
    if(p_burn_info->_target_burn_en&(0x00000001 << ITEM_BEEP))
    {
        burn_state._target_brun_sta[ITEM_BEEP] = test_beep(tick);                                  // ITEM_BEEP测试进度细节
        if(burn_state._target_brun_sta[ITEM_BEEP]==100) burn_sta |= (0x00000001 << ITEM_BEEP);   // ITEM_BEEP测试完成
    }
    if(p_burn_info->_target_burn_en&(0x00000001 << ITEM_MOTOR))
    {
        burn_state._target_brun_sta[ITEM_MOTOR] = test_motor(tick);                                // ITEM_MOTOR测试进度细节
        if(burn_state._target_brun_sta[ITEM_MOTOR]==100) burn_sta |= (0x00000001 << ITEM_MOTOR); // ITEM_MOTOR测试完成
    }
    if(p_burn_info->_target_burn_en&(0x00000001 << ITEM_LCD))
    {
        burn_state._target_brun_sta[ITEM_LCD] = test_lcd(tick);                                    // ITEM_LCD测试进度细节
        if(burn_state._target_brun_sta[ITEM_LCD]==100) burn_sta |= (0x00000001 << ITEM_LCD);     // ITEM_LCD测试完成
    }
    if(p_burn_info->_target_burn_en&(0x00000001 << ITEM_GPS))
    {
        burn_state._target_brun_sta[ITEM_GPS] = test_gps(tick);                                    // ITEM_GPS测试进度细节
        if(burn_state._target_brun_sta[ITEM_GPS]==100) burn_sta |= (0x00000001 << ITEM_GPS);     // ITEM_GPS测试完成
    }
    if(p_burn_info->_target_burn_en&(0x00000001 << ITEM_BLE))
    {
      burn_state._target_brun_sta[ITEM_BLE] = test_nrf52832(tick);                               // ITEM_BLE测试进度细节
      if(burn_state._target_brun_sta[ITEM_BLE]==100) burn_sta |= (0x00000001 << ITEM_BLE);     // ITEM_BLE测试完成
    }
    if(p_burn_info->_target_burn_en&(0x00000001 << ITEM_PPG))
    {
        burn_state._target_brun_sta[ITEM_PPG] = test_ppg(tick);                                    // ITEM_PPG测试进度细节
        if(burn_state._target_brun_sta[ITEM_PPG]==100) burn_sta |= (0x00000001 << ITEM_PPG);     // ITEM_PPG测试完成
    }
    if(p_burn_info->_target_burn_en&(0x00000001 << ITEM_ACC))
    {
      burn_state._target_brun_sta[ITEM_ACC] = test_att(tick);                                      // ITEM_ACC测试进度细节
      if(burn_state._target_brun_sta[ITEM_ACC] == 100) burn_sta |= (0x00000001 << ITEM_ACC);       // ITEM_ACC测试完成
    }
    if(p_burn_info->_target_burn_en&(0x00000001 << ITEM_EMMC))
    {
      burn_state._target_brun_sta[ITEM_EMMC] = test_emmc(tick);                                    // ITEM_EMMC测试进度细节
      if(burn_state._target_brun_sta[ITEM_EMMC] == 100) burn_sta |= (0x00000001 << ITEM_EMMC);     // ITEM_EMMC测试完成
    }

    burn_state._target_burn_now = burn_sta;

    return (burn_state._target_burn_now == p_burn_info->_target_burn_en);
}

static void burn_test_thread_entry(void *parameter)
{
    FACTORY_LOG_I("Burn test thread started");
    int loop_count = 0;
    while(1)
    {
        if (burn_test_running_status&&(p_burn_info != NULL))
        {
            // 老化重启未完成，继续老化重启
            if (burn_state._target_brun_loop[ITEM_REBOOT] < p_burn_info->_target_reboot_count)
            {
                rt_thread_mdelay(6000); // 等待6秒
                loop_count = 0;
                p_burn_info->_current_reboot_count = burn_state._target_brun_loop[ITEM_REBOOT] + 1;
                kvdb_save_burn_reboot_info();                     // 保存数据到kvdb                // 尝试主动上报老化测试进度
                FACTORY_LOG_I("Burn reboot %02d//%02d...",burn_state._target_brun_loop[ITEM_REBOOT],p_burn_info->_target_reboot_count);
                if(burn_report_callback != RT_NULL) burn_report_callback();
                request_reboot(NULL);
            }
            else
            {
                // 执行老化测试，返回测试是否结束
                if(test_item_entry(loop_count++))                 // 执行测试项
                {
                    burn_test_running_status = false;             // 停止老化测试
                    p_burn_info->test_status = BURN_TEST_END;     // 设置老化测试状态为结束
                    p_burn_info->_current_reboot_count = 0;
                    kvdb_save_burn_reboot_info();                 // 保存数据到kvdb
                    FACTORY_LOG_I("Burn test finished!!");
                }
                // 上报数据
                if ((loop_count % SECNODS_TO_TICK(10)) == 0)
                {
                    FACTORY_LOG_I("Burn report...");
                    // 尝试主动上报老化测试进度
                    if(burn_report_callback != RT_NULL) burn_report_callback();
                }
                rt_thread_mdelay(BURN_TASK_INTERVAL_MS);
            }
        }
        else
        {
            test_item_entry(-1); // 停止测试项
            // 尝试主动上报老化测试结果
            if(burn_report_callback != RT_NULL) burn_report_callback();
            FACTORY_LOG_I("Burn test thread stop");
            rt_thread_suspend(rt_thread_self());
            rt_schedule();
        }
    }
}

static uint8_t create_burn_test_thread(void)
{
#ifdef IGS_DEV
    if (burn_test_thread == RT_NULL)
    {
        // 创建老化测试线程
        burn_test_thread = rt_thread_create(
            "burn_test",                // 线程名称
            burn_test_thread_entry,     // 线程入口函数
            NULL,                       // 入口函数参数
            4096,                       // 线程栈大小
            23,                         // 线程优先级
            10                          // 时间片
        );
        rt_thread_startup(burn_test_thread);
    }
    else
    {
        rt_thread_resume(burn_test_thread);
    }
#endif
    return AT_STATUS_SUCCESS;
}
// 准备老化测试 shell cmd: at_test AT+BURN+READY
static void burn_test_ready(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
#ifdef IGS_DEV
    char buffer[44] = {0};
    uint32_t burn_fun = DEFAULT_BURN_FUN;//老化支持的测试项
    uint32_t burn_en = 0;                //当前开启的老化测试项
    uint32_t burn_now = 0;                //当前开启的老化测试项
    uint32_t burn_time = 0;              //老化测试时间
    uint8_t  reboot_progres = 0;         //重启测试进度
    uint8_t  burn_min_progres = 0;       //当前老化测试进度
    uint8_t  burn_power = 80;            //当前电量
    uint8_t  burn_times = 0;             //TO DO 老化测试次数
    if ((get_burn_state() == BURN_TEST_RUNNING))
    {
        status = -1;
        reboot_progres = get_burn_progress();
        burn_min_progres = get_burn_item_min_progress();
        if(p_burn_info != NULL)
        {
            burn_en = p_burn_info->_target_burn_en;
        }
        burn_now = burn_state._target_burn_now;
        burn_time = 0; //TO DO 老化测试时间
    }
    // else if() // 是否处在未充电状态
    // {
    //     status = -2;
    // }
    // else if() // 是否电量不满足条件
    // {
    //     status = -3;
    // }

    if(status==0)  // 进入老化测试界面
    {
        const char *page = "FAT_AgingTest";
        submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)page);
    }

    snprintf(buffer, sizeof(buffer), "%08X%08X%08X%08X%02X%02X%02X%02X",burn_fun,burn_en,burn_now,burn_time,reboot_progres,burn_min_progres,burn_power,burn_times);
#endif
    generate_at_response(cmd->func, cmd->cmd, status, (const uint8_t*)buffer, strlen(buffer), response, response_len);
    //FACTORY_LOG_I("Burn ready = %d",burn_test_running_status);
}

// 开始老化测试 shell cmd: at_test AT+BURN+START
static void burn_test_start(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    if(p_burn_info == NULL)
    {
        FACTORY_LOG_E("p_burn_info is null!");
        status = AT_STATUS_INVALID_DATA; // 无效的参数
        goto end;
    }
    // 若带参数，解析参数
    if (cmd->data_count>=(34*2))
    {
        int data_len = cmd->data_count / 2;
        uint8_t *data = (rt_uint8_t *)rt_malloc((((data_len+3)>>2)<<2));
        if (data == RT_NULL)
        {
            FACTORY_LOG_E("allocate partition data buffer failed!");
            status = AT_STATUS_INVALID_DATA; // 无效的参数
            goto end;
        }
        for (uint8_t j = 0; j < data_len; j++)
        {
            if (sscanf((const char*)(cmd->data + j * 2), "%2hhx", &data[j]) != 1) {
                FACTORY_LOG_E("Parse error: Invalid hex character in data at position %d", j * 2);
                status = AT_STATUS_INVALID_DATA; // 无效的参数
                goto end;
            }
        }
        p_burn_info->_target_reboot_count = data[0];             // 目标reboot次数
        p_burn_info->test_loop = data[1],                        // 默认老化测试循环次数
        memcpy(p_burn_info->_target_brun_count,&data[2],32);     // 拷贝配置
        rt_free(data);
    }

    // 开始老化测试
    p_burn_info->test_status = BURN_TEST_RUNNING;    // 设置老化测试状态为测试中
    burn_test_running_status = true;
    p_burn_info->_current_reboot_count = 0;          // 设置当前reboot次数,清零
    p_burn_info->_target_brun_fun = DEFAULT_BURN_FUN;                             // 默认支持的老化测试项
    if((p_burn_info->_target_reboot_count == 0)||(p_burn_info->_target_reboot_count > 40))  // 目标reboot次数不合法，恢复默认
    {
        p_burn_info->_target_reboot_count = DEFAULT_BURN_REBOOT_COUNT;
    }
    if((p_burn_info->test_loop == 0)||(p_burn_info->test_loop > 40))                        // 目标老化测试循环次数不合法，恢复默认
    {
        p_burn_info->test_loop = DEFAULT_BURN_REBOOT_COUNT;
    }
    // 填充测试项使能位，老化测试循环次数不合法，恢复默认
    p_burn_info->_target_burn_en = 0;
    for (uint8_t k = 0; k <= ITEM_FACTORY_FUN_MAX; k++)
    {
        if(p_burn_info->_target_brun_fun&((uint32_t)0x00000001 << k))       //支持当前项
        {
            if((p_burn_info->_target_brun_count[k] > 0)&&(p_burn_info->_target_brun_count[k] <= 40))
            {
                p_burn_info->_target_burn_en |= ((uint32_t)0x00000001 << k); //开启当前项
            }
            else
            {
                p_burn_info->_target_brun_count[k] = 0;            //记录当前项的循环次数
            }
        }
        else
        {
            p_burn_info->_target_brun_count[k] = 0;                //记录当前项的循环次数
        }
    }

    // 初始化进度，状态相关数据
    burn_state._target_burn_now = 0;
    memset(burn_state._target_brun_sta,0,32);
    memset(burn_state._target_brun_loop,0,32);
    FACTORY_LOG_I("Burn start cmd->data_count = %d",cmd->data_count,p_burn_info->_target_brun_fun,p_burn_info->_target_burn_en);
    for (uint8_t k = 0; k <= ITEM_FACTORY_FUN_MAX; k++)
    {
        if(p_burn_info->_target_brun_fun&((uint32_t)0x00000001 << k))   //支持当前项
        {
            if(p_burn_info->_target_burn_en&((uint32_t)0x00000001 << k))//当前开启项
                burn_state._target_brun_sta[k] = 0;           //测试未打开
            else
                burn_state._target_brun_sta[k] = -2;          //测试未打开
        }
        else
            burn_state._target_brun_sta[k] = -3;              //测试不支持
    }

#ifdef IGS_DEV
    kvdb_save_burn_reboot_info();                             // 保存数据到kvdb
    status = create_burn_test_thread();                       // 创建老化测试线程
#endif

    // 应答
    char buffer[20] = {0};
    snprintf(buffer, sizeof(buffer), "%08X%08X",p_burn_info->_target_brun_fun,p_burn_info->_target_burn_en);
    generate_at_response(cmd->func, cmd->cmd, status, (const uint8_t*)buffer, strlen(buffer), response, response_len);
    return;
end:
    generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
}

// 暂停老化测试 shell cmd: at_test AT+BURN+STOP
static void burn_test_stop(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
#ifdef IGS_DEV
    // 挂起老化测试线程
    if ((burn_test_thread != RT_NULL) && burn_test_running_status && (p_burn_info != NULL))
    {
        burn_test_running_status = false;               // 暂停老化测试
        burn_state._target_brun_loop[ITEM_REBOOT] = 0;
        p_burn_info->test_status = BURN_TEST_NOT_BEGIN; // 设置老化测试状态为异常结束
        p_burn_info->_current_reboot_count = 0;         // 重新开始
        kvdb_save_burn_reboot_info();                   // 保存数据到kvdb
    }
    else
    {
        status = AT_STATUS_FAIL;
    }
#endif
    // 返回结果
    char buffer[68] = {0};
    int buffer_len = 0;
    for (uint8_t i = 0; i <= ITEM_FACTORY_FUN_MAX; i++)
    {
        sprintf((char *)buffer + buffer_len, "%02X", burn_state._target_brun_sta[i]);
        buffer_len += 2;
    }
    generate_at_response(cmd->func, cmd->cmd, status, (const uint8_t*)buffer, strlen(buffer), response, response_len);
    //FACTORY_LOG_I("Burn stop = %d",burn_test_running_status);
}

//获取老化重启进度信息
uint8_t get_burn_progress(void)
{
    if((p_burn_info != NULL) && (p_burn_info->_target_reboot_count > 0) && (burn_state._target_brun_loop[ITEM_REBOOT] <= p_burn_info->_target_reboot_count))
    {
        return (uint8_t)(burn_state._target_brun_loop[ITEM_REBOOT] * 100.0 / p_burn_info->_target_reboot_count);
    }
    return 0;
}

//获取老化测试是否完成信息
bool get_burn_over_state(void)
{
    if((p_burn_info != NULL)&&(p_burn_info->_target_burn_en != 0))
    {
        return (burn_state._target_burn_now == p_burn_info->_target_burn_en);
    }
    return false;
}

//获取老化测试项进度信息
uint8_t get_burn_item_progress(int item)
{
    if((item < 32)&&(burn_state._target_brun_sta[item] >= 0)&&(burn_state._target_brun_sta[item] <= 100))
    {
        return (uint8_t)burn_state._target_brun_sta[item];
    }
    return 0;
}

//获取老化测试项最慢进度信息
uint8_t get_burn_item_min_progress()
{
    if((p_burn_info != NULL)&&(p_burn_info->_target_burn_en != 0))
    {
        int8_t min_progress = 100;
        for (uint8_t k = 0; k <= ITEM_FACTORY_FUN_MAX; k++)
        {
            if(p_burn_info->_target_burn_en&((uint32_t)0x00000001 << k)) //开启当前项
            {
                if((burn_state._target_brun_sta[k]>=0)&&(burn_state._target_brun_sta[k]<100)&&(burn_state._target_brun_sta[k] < min_progress))
                {
                    min_progress = burn_state._target_brun_sta[k];
                }
            }
        }
        return min_progress;
    }
    return 0;
}

// 上报老化进度 shell cmd: at_test AT+BURN+REPORT
extern cmd_context_manager_t g_cmd_context_manager; // 声明全局命令上下文管理器
/**
 * @brief  注册按键中断
 *
 * @param  callback
 * @return int
 */
int burn_module_register_callback(void (*callback)(void))
{
	if(callback == RT_NULL)
	{
		return -1;
	}
	burn_report_callback = callback;
	return 0;
}

static void burn_test_report_callback(void)
{
    uint8_t status = AT_STATUS_SUCCESS;
    //上报老化项的进度
    char buffer[64] = {0};
    uint8_t buffer_len = 0;
    uint8_t progress = get_burn_progress();             // 计算进度百分比
    if(progress < 100)                                  // 重启老化百分比
    {
        buffer_len = 2;
        snprintf((char *)buffer,buffer_len + 1, "%02X", progress);
    }
    else
    {
        burn_state._target_brun_sta[0] = 0x64;
        for (uint8_t i = 0; i <= ITEM_FACTORY_FUN_MAX; i++)
        {
            sprintf((char *)buffer + buffer_len, "%02X", burn_state._target_brun_sta[i]);
            buffer_len += 2;
        }
    }
    // 获取当前命令上下文
    cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
    if (context)
    {
        generate_at_response(context->cmd->func,
                           context->cmd->cmd,
                           status,
                           (const uint8_t*)buffer, (uint8_t)buffer_len,
                           context->response,
                           context->response_len);

        // 数据上报
        ble_debug_data_notif(0xFF, context->response,(uint8_t)*context->response_len);
    }
}
static void burn_test_report(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
#ifdef IGS_DEV
    if(cmd->data_count>0)
    {
        if (strcmp((const char *)cmd->data, "01") == 0)
        {
            // 创建命令上下文
            cmd_context_t *context = cmd_context_create(&g_cmd_context_manager, cmd, response, response_len);
            if (context == NULL)
            {
                status = AT_STATUS_FAIL;
                burn_module_register_callback(NULL);
            }
            else
                burn_module_register_callback(burn_test_report_callback);
        }
        else if (strcmp((const char *)cmd->data, "00") == 0)
        {
            burn_module_register_callback(NULL);
            // 释放当前命令上下文
            cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
            if (context) cmd_context_release(&g_cmd_context_manager, context);
        }
    }
    else
    {
        //上报老化项的进度
        char buffer[64] = {0};
        uint8_t buffer_len = 0;
        uint8_t progress = get_burn_progress();             // 计算进度百分比
        if(progress < 100)                                  // 重启老化百分比
        {
            buffer_len = 2;
            snprintf((char *)buffer,buffer_len, "%02X", progress);
        }
        else
        {
            for (uint8_t i = 0; i <= ITEM_FACTORY_FUN_MAX; i++)
            {
                sprintf((char *)buffer + buffer_len, "%02X", burn_state._target_brun_sta[i]);
                buffer_len += 2;
            }
        }
        generate_at_response(cmd->func, cmd->cmd, status, (const uint8_t*)buffer, buffer_len, response, response_len);
        return;
    }
#endif
    generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
}

// static void burn_test_switch(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
// {
//     uint8_t status = AT_STATUS_SUCCESS;
//     generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
// }


// 设置老化参数 shell cmd: at_test AT+BURN+CONFIG=01 00 0000000000000000000000000000000000000000000000000000000000000000
// 获取老化参数 shell cmd: at_test AT+BURN+CONFIG=00
static void burn_test_config(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    if ((p_burn_info == NULL)||(cmd->data_count < 1))
    {
        status = AT_STATUS_INVALID_DATA; // 无效的参数
        goto end;
    }

    uint8_t value = 0;
    if (sscanf((const char *)cmd->data, "%2hhx", &value) != 1)
    {
        FACTORY_LOG_E("Parse error: Invalid hex character:%s",cmd->data);
        status = AT_STATUS_FAIL;
        goto end;
    }
    if (value == AT_OP_DIR_WRITE)            // 设置老化参数
    {
        if (cmd->data_count != (32*6+4))
        {
            status = AT_STATUS_INVALID_DATA; // 无效的参数
            goto end;
        }
        int data_len =  cmd->data_count/2;
        uint8_t *data = (rt_uint8_t *)rt_malloc(data_len);
        if (data == RT_NULL)
        {
            FACTORY_LOG_E("allocate partition data buffer failed!");
            goto end;
        }
        for (uint8_t j = 1; j < data_len; j++)
        {
            if (sscanf((const char*)(cmd->data + j * 2), "%2hhx", &data[j]) != 1)
            {
                FACTORY_LOG_E("Parse error: Invalid hex character in data at position %d", j * 2);
                goto end;
            }
        }

        // 将数据写入KVDB
        p_burn_info->_target_burn_en = 0;
        for (uint8_t k = 0; k <= ITEM_FACTORY_FUN_MAX; k++)
        {
            if(p_burn_info->_target_brun_fun&((uint32_t)0x00000001 << k))                 // 支持当前项
            {
                p_burn_info->_target_brun_count[k] = data[2+k*3];               // 设置测试项的循环次数
                p_burn_info->_target_brun_parm1[k] = data[2+k*3 + 1];           // 设置测试项老化测试参数一
                p_burn_info->_target_brun_parm2[k] = data[2+k*3 + 2];           // 设置测试项老化测试参数二
                if(p_burn_info->_target_brun_count[k]>0)
                {
                    p_burn_info->_target_burn_en |= (0x00000001 << k);          // 设置测试项的使能
                }
                FACTORY_LOG_I("Parser:%02d:%02d，%02d，%02d\r\n",k,data[2+k*3],data[2+k*3 + 1],data[2+k*3 + 2]);
            }
        }
        //TO DO 屏蔽老化测试过程中修改参数：可以在APP端做
        p_burn_info->test_status = BURN_TEST_NOT_BEGIN;
        p_burn_info->test_loop = data[1],                                       // 设置老化测试循环次数
        p_burn_info->_target_reboot_count = data[2];                            // 目标reboot次数
        p_burn_info->_current_reboot_count = 0;
        kvdb_save_burn_reboot_info();                                           // 保存

        rt_free(data);
    }
    // 返回老化设置参数
    char buffer[32*6 + 4] = {0};
    int buffer_len = 0;
    sprintf((char *)buffer + buffer_len, "%02X", p_burn_info->test_loop);
    buffer_len += 2;
    for (uint8_t i = 0; i <= ITEM_FACTORY_FUN_MAX; i++)
    {
        sprintf((char *)buffer + buffer_len, "%02X%02X%02X", p_burn_info->_target_brun_count[i], p_burn_info->_target_brun_parm1[i], p_burn_info->_target_brun_parm2[i]);
        buffer_len += 6;
    }
    generate_at_response(cmd->func, cmd->cmd, status, (const uint8_t*)buffer, buffer_len, response, response_len);
    return;
end:
    generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
}

// // 设置老化参数 shell cmd: at_test AT+BURN+REBOOT=01 000003E8
// // 获取老化参数 shell cmd: at_test AT+BURN+REBOOT=00
// static void burn_test_reboot_param(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
// {
//     uint8_t status = AT_STATUS_SUCCESS;
//     if (cmd->data_count < 1) {
//         status = AT_STATUS_INVALID_DATA; // 无效的参数
//         goto end;
//     }

//     uint8_t value = 0;
//     if (sscanf((const char *)cmd->data, "%2hhx", &value) != 1) {
//         FACTORY_LOG_E("Parse error: Invalid hex character:%s",cmd->data);
//         status = AT_STATUS_FAIL;
//         goto end;
//     }
//     if (value == AT_OP_DIR_WRITE) { // 设置老化参数
//         if (cmd->data_count != 10) {
//             status = AT_STATUS_INVALID_DATA; // 无效的参数
//             goto end;
//         }

//         uint32_t reboot_count = 0;
//         if (sscanf((const char *)&cmd->data[2], "%8X", &reboot_count) != 1) {
//             FACTORY_LOG_E("Parse error: Invalid hex character:%s",&cmd->data[2]);
//             status = AT_STATUS_FAIL;
//             goto end;
//         }
//         //TO DO 屏蔽老化测试过程中修改参数：可以在APP端做
//         p_burn_info->test_status = BURN_TEST_NOT_BEGIN;
//         p_burn_info->_current_reboot_count = 0;
//         p_burn_info->_target_reboot_count = reboot_count;
//         if (p_burn_info->_target_reboot_count < 1 || p_burn_info->_target_reboot_count > 255) {
//             status = AT_STATUS_INVALID_DATA; // 无效的参数
//             goto end;
//         }
//         kvdb_save_burn_reboot_info(); // 保存重启参数到kvdb
//         goto end;
//     } else { // 获取老化参数
//         char buffer[20] = {0};
//         snprintf(buffer, sizeof(buffer), "%02X%08X%08X",p_burn_info->test_status,p_burn_info->_current_reboot_count,p_burn_info->_target_reboot_count);
//         generate_at_response(cmd->func, cmd->cmd, status, (const uint8_t*)buffer, strlen(buffer), response, response_len);
//         return;
//     }

// end:
//     generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
// }

// // 设置老化参数 shell cmd: at_test AT+BURN+BEEP=01 00000000 00000000 00000000
// // 获取老化参数 shell cmd: at_test AT+BURN+BEEP=00
// static void burn_test_beep_param(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
// {
//     uint8_t status = AT_STATUS_SUCCESS;
//     // TODO: beep param set/get

//     generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
// }

// // 设置老化参数 shell cmd: at_test AT+BURN+MOTOR=01 00000000 00000000 00000000
// // 获取老化参数 shell cmd: at_test AT+BURN+MOTOR=00
// static void burn_test_motor_param(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
// {
//     uint8_t status = AT_STATUS_SUCCESS;
//     // TODO: motor param set/get

//     generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
// }

// // 设置老化参数 shell cmd: at_test AT+BURN+LCD=01 00000000 00000000 00000000
// // 获取老化参数 shell cmd: at_test AT+BURN+LCD=00
// static void burn_test_lcd_param(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
// {
//     uint8_t status = AT_STATUS_SUCCESS;
//     // TODO: lcd param set/get

//     generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
// }

// // 设置老化参数 shell cmd: at_test AT+BURN+EMMC=01 00000000 00000000 00000000
// // 获取老化参数 shell cmd: at_test AT+BURN+EMMC=00
// static void burn_test_emmc_param(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
// {
//     uint8_t status = AT_STATUS_SUCCESS;
//     // TODO: emmc param set/get

//     generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
// }

// // 设置老化参数 shell cmd: at_test AT+BURN+ATT=01 00000000 00000000 00000000
// // 获取老化参数 shell cmd: at_test AT+BURN+ATT=00
// static void burn_test_att_param(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
// {
//     uint8_t status = AT_STATUS_SUCCESS;
//     // TODO: att param set/get

//     generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
// }

// // 设置老化参数 shell cmd: at_test AT+BURN+PPG=01 00000000 00000000 00000000
// // 获取老化参数 shell cmd: at_test AT+BURN+PPG=00
// static void burn_test_ppg_param(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
// {
//     uint8_t status = AT_STATUS_SUCCESS;
//     // TODO: ppg param set/get

//     generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
// }

// // 设置老化参数 shell cmd: at_test AT+BURN+GPS=01 00000000 00000000 00000000
// // 获取老化参数 shell cmd: at_test AT+BURN+GPS=00
// static void burn_test_gps_param(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
// {
//     uint8_t status = AT_STATUS_SUCCESS;
//     // TODO: gps param set/get

//     generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
// }

// // 设置老化参数 shell cmd: at_test AT+BURN+BLE=01 00000000 00000000 00000000
// // 获取老化参数 shell cmd: at_test AT+BURN+BLE=00
// static void burn_test_ble_param(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
// {
//     uint8_t status = AT_STATUS_SUCCESS;
//     // TODO: ble param set/get

//     generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
// }


const production_test_entry_t burn_test_table[] = {
    {"READY", burn_test_ready},
    {"START", burn_test_start},
    {"STOP", burn_test_stop},
    {"REPORT", burn_test_report},
    {"CONFIG", burn_test_config},

    //{"SWITCH", burn_test_switch},
    // // 详细参数配置
    // {"REBOOT", burn_test_reboot_param},
    // {"BEEP", burn_test_beep_param},
    // {"MOTOR", burn_test_motor_param},
    // {"LCD", burn_test_lcd_param},
    // {"EMMC", burn_test_emmc_param},
    // {"ATT", burn_test_att_param},
    // {"PPG", burn_test_ppg_param},
    // {"GPS", burn_test_gps_param},
    // {"BLE", burn_test_ble_param},
    // //......
    {NULL, NULL}  // 结束标记
};

void burn_test_entry(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    FACTORY_LOG_I("burn test %s", cmd->cmd);

    #define CMD_TABLE_SIZE (sizeof(burn_test_table)/sizeof(burn_test_table[0]))

    // 查找并执行对应命令
    for (size_t i = 0; i < CMD_TABLE_SIZE; ++i)
    {
        if (strcmp(cmd->cmd, burn_test_table[i].func_name) == 0)
        {
            burn_test_table[i].test_func(cmd, response, response_len);
            break;
        }
    }
}

// 获取老化测试状态
uint8_t get_burn_state(void)
{
    if(p_burn_info != NULL)
    {
        return p_burn_info->test_status;
    }
    return BURN_TEST_NOT_BEGIN;
}


/**
 * @brief RTC默认保存值初始化
 * @param value 默认参数句柄 cfg_burn_t*
 * @param value_size 参数大小
 * @return 返回值
 */
static bool burn_default_config(void *value, uint32_t value_size)
{
    FACTORY_LOG_I("cfg burn default value\n");
    if (value != NULL)
    {
        memcpy(value, (const void*)&s_default_config, sizeof(cfg_burn_t));
    }
    return true;
}

static void kvdb_save_burn_reboot_info(void)
{
    if(burn_info_store_handler == NULL)  return;
    p_burn_info = (cfg_burn_t*)kvlist_node_get_data_ptr(burn_info_store_handler, BURN_KEY);
    FACTORY_LOG_I("kvdb save %08x burn status %d, current count %d, target count %d",p_burn_info,p_burn_info->test_status, p_burn_info->_current_reboot_count, p_burn_info->_target_reboot_count);
    kvlist_node_set(burn_info_store_handler, BURN_KEY, (const void *)p_burn_info, sizeof(cfg_burn_t));
    kvlist_flush(burn_info_store_handler, BURN_KEY);
}

static int kvdb_burn_info_init(void *handler)
{
    burn_info_store_handler = handler;
    kvlist_node_open(handler, BURN_KEY, &burn_config, sizeof(cfg_burn_t), NULL);
    p_burn_info = (cfg_burn_t*)kvlist_node_get_data_ptr(handler, BURN_KEY);
#ifdef FACTORY_MODE_ENABLE
    FACTORY_LOG_I("burn test status %d, current count %d, target count %d", p_burn_info->test_status, p_burn_info->_current_reboot_count, p_burn_info->_target_reboot_count);
    burn_state._target_brun_loop[ITEM_REBOOT] = p_burn_info->_current_reboot_count;
    if (p_burn_info->test_status == BURN_TEST_RUNNING)
    {
        burn_test_running_status = true;
        create_burn_test_thread(); // 如果已经开始了老化测试，则重启之后自动创建老化测试线程
    }
    else
    {
        burn_test_running_status = false;
    }
#else
    // 用户版本正常不应该进入，若进入进入只进行一次测试
    p_burn_info->test_status = BURN_TEST_NOT_BEGIN;
    burn_test_running_status = false;
    p_burn_info->_current_reboot_count = 0;
    burn_state._target_brun_loop[ITEM_REBOOT] = 0;
    p_burn_info->_target_reboot_count = 1;
#endif
    return 0;
}
INIT_FS_KV_EXPORT(kvdb_burn_info_init);
