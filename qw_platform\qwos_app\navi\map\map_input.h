﻿/************************************************************************​
*Copyright(c) 2024, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
/*
地图绘制
相关数据
 */

#ifndef _MAP_INPUT_H_
#define _MAP_INPUT_H_

#if defined(__cplusplus)
extern "C"
{
#endif

#include "minmea.h"
#include "gps_distance.h"
#include "gps_bearing.h"
#include "qw_general.h"
// #include "qw_router_port.h"

//Compile config options.
#define __MAP_POI 1 //加载地图poi数据
#define __CONTOUR 1 //加载等高线地图
#define __ZOOM_SCALE_9 1 //地图比例尺扩充 从OSM zoom lev9开始

#define __OFF_COURSE_ROUTER 1 //偏航线路规划
#define __NAVI_ROUTE_WAY 1 //线路路网匹配

#if __OFF_COURSE_ROUTER
#define __FREE_ROUTER 1 //自由线路规划
#else
#define __FREE_ROUTER 0
#endif

#define __NAVI_DEBUG 0

//NAVI_SIM
#define __NAVI_SIM 0

#define __QW_LAB_ALL_IN_ONE 0

#define COORDINATE_PRECISION  1000000.0f //坐标精度
#define POINT_SCALE_UNIT    9 //赤道附近1cm的经纬度值 * 1000000 * 100
#if __ZOOM_SCALE_9
#define ZOOM_NUM 9
#define DEFAULT_ZOOM 7
#define MAX_ZOOM_20K 0 //1:最大比例尺20km, 0:最大比例尺10km
#else
#define ZOOM_NUM 5
#define DEFAULT_ZOOM 3
#endif
#ifdef IGS_DEV
#define MAP_BUFFER_SIZE 8096//1024 //地图加载临时缓冲区大小
#else
#define MAP_BUFFER_SIZE 8096 //地图加载临时缓冲区大小
#endif
#define PATH_NAME_LENGTH 100 //记录文件名称级路径的长度  注意长度为4的倍数
#define WAY_NAME_LEN 32      //路名长度
#ifdef IGS_DEV
#define MAX_WAY_NAMES 10      //最大存储路名个数
#define MAX_POI_NAMES 20      //最大POI显示个数
#else
#define MAX_WAY_NAMES 100      //最大存储路名个数
#define MAX_POI_NAMES 200      //最大POI显示个数
#define MAX_WAY_NAME_SIZE 3200//路名最大长度
#define MAX_WAY_REF_SIZE 128 //最大reference长度(reference是道路编号，如G107，没有路名时可代替路名显示)
#endif
#if __QW_LAB_ALL_IN_ONE
#define ROUTE_ELEVATION_HEIGHT 120 //线路海拔窗口高度
#else
#define ROUTE_ELEVATION_HEIGHT 70 //线路海拔窗口高度
#define ROUTE_CLIMB_INFO_HEIGHT 44 //线路爬升信息窗口高度
#define ROUTE_CLIMB_CHART_HEIGHT 70 //线路爬升图标窗口高度
#define ROUTE_CLIMBE_PROCESS_HIGHT 26 //线路爬升进度窗口高度
#define STATUSBAR_HEIGHT 40
#define CILMB_PRO_TITLE_HEIGHT 44
#define CILMB_PRO_TEXT_HEIGHT 75
#define CILMB_PRO_CANVAS_HEIGHT 144
#endif
#define MAP_X_OFFSET            10 //边框宽度 起终点图标在点的上方，X和Y_TOP要多预留一些
#define MAP_Y_OFFSET_TOP        15 //边框宽度 起终点图标在点的上方，X和Y_TOP要多预留一些
#define MAP_Y_OFFSET_BOTTOM     5//10 //边框宽度
#define MINMEA_SCALE 10000000 //route模块minmea结构中的scale固定使用10000000
#define MAX_WAYPOINT 300 //全局最大路点数
#define MAX_WAYPOINT_NEARBY 300 //附近最大路点数
#define WAYPOINT_MIN_INTERVAL 1500 //最小路点间隔50米 //使用DP算法调整为10米
#define WAYPOINT_MIN_INTERVAL_COURSE 500 //航向计算最小路点间隔
#define MAX_SIGN_NUM 20 //最大标记点个数
#define AUTO_ZOOM_DIST 100 //转向点自动缩放距离100米
#define AUTO_ZOOM_TIME_TURN 3000 //转向点自动缩放恢复时间3秒
#define AUTO_ZOOM_TIME_ZOOM 30000 //手动缩放后自动恢复默认比例尺时间30秒

#define TRACK_NAME_DEFAULT "navi_track.cnx"

typedef enum
{
    enum_zoom_total = 0,
    enum_zoom_20km,
    enum_zoom_10km,
    enum_zoom_5km,
    enum_zoom_2km,
    enum_zoom_1km,
    enum_zoom_500m,
    enum_zoom_200m,
    enum_zoom_100m,
    enum_zoom_50m,
} zoom_e;

//GPS数据锁定，防止绘图过程中GPS数据被更新
typedef struct
{
    struct minmea_float lat;
    struct minmea_float lon;
    int32_t course; //航向（0~359度，以真北为参考基准）
    uint8_t status; //0未定位 1已定位。用于未定位时显示不同的航向指针的效果
} map_input_gps_t;

//Input structure.
typedef struct
{
    //GPS data
    struct minmea_float gps_lat;
    struct minmea_float gps_lon;
    int32_t course; //航向（0~359度，以真北为参考基准）
    uint8_t status; //0未定位 1已定位。用于未定位时显示不同的航向指针的效果

    //设备保存的经纬度 * 10000000，用于开机未定位时显示地图
    int32_t saved_lat;
    int32_t saved_lon;

    uint32_t map_poi_swtich; //地图POI（图标）显示开关。每个bit表示一种POI
    uint32_t map_poi_name_swtich; //地图POI名称显示开关。每个bit表示一种POI
    uint32_t runtime_ms; //开机累计时间毫秒

    point16_t map_origin; //画布起位置
    uint16_t map_rang_x; //画布尺寸
    uint16_t map_rang_y;
    uint8_t rotate; //是否旋转
    uint8_t climb_map_enable;
    uint8_t is_night; //是否夜间
    uint8_t on_save; //是否已开始记录
    uint8_t unit_style_dist; //距离公英制 1公制 0英制
    uint8_t unit_style_alt; //高度公英制 1公制 0英制
    uint8_t meter_page;
    uint8_t cur_page; //当前页面 处于地图页还是爬坡地图页  0 爬坡地图页  1 地图页
    uint8_t turning_indicator; //转向提示开关
    uint8_t prompt_enable_page; //转向提示页面
    uint8_t prompt_screen;  //转向闪屏
    uint8_t statusbar; //状态栏
    uint8_t route_elevation; //显示线路海拔
    uint8_t valid_elevation; //有效海拔
    uint8_t off_course_route; //偏航规划
    uint8_t auto_zoom; //自动缩放
    uint8_t way_name; //显示路名
    uint8_t team_mate; //显示队友
    uint8_t touch_enable; //是否开启触屏(屏幕图标个数会发生变化)
    uint8_t touch_map; //是否可以拖动地图
    uint8_t contour; //是否显示等高线
    uint8_t map_location_point; //是否显示位置点
    uint8_t has_radar; //是否显示雷达
    uint8_t ctrl_mode; //是否处于控制模式（控制模式时可进行缩放）
    char route_dir[PATH_NAME_LENGTH];
    char map_dir[PATH_NAME_LENGTH];
} map_input_data_t;

typedef struct
{
    int32_t swc_lon; //最小经度（*10000000）
    int32_t nec_lon; //最大经度（*10000000）
    int32_t swc_lat; //最小纬度（*10000000）
    int32_t nec_lat; //最大纬度（*10000000）
    uint32_t distance_scale;     //每个像素点对应的距离 100*m
    uint32_t point_scale;        //每个像素点对应的经纬度值*1000000*100
    uint32_t total_distance_scale; //全景图下每个像素点对应的距离 100*m
    uint16_t map_real_y; //除去海拔图/爬坡信息的地图实际高度
    //各因素偏移分量（单位：像素）
    int16_t center_align_offset_x;          //由于X Y轴缩放比一致,可能导致地图不居中,所以需要偏移
    int16_t center_align_offset_y;
    //计算后的偏移总量（单位：像素）
    int16_t show_x_offset_left;          //可显示范围与左侧的距离
    int16_t show_x_offset_right;          //可显示范围与右侧的距离 //暂未用到
    int16_t show_y_offset_top;      //与顶端的距离
    int16_t show_y_offset_bottom;    //与底端的距离
    point16_t org_point; //当前GPS定位点屏幕坐标(旋转开启时固定)
    uint16_t zoom; //缩放等级，0：全景，1：800m，2：300m，3：120m，4：80m，5：50m
    uint16_t zoom_update; //缩放等级更新（用于地图加载过程中发生的zoom切换），0：全景，1：800m，2：300m，3：120m，4：80m，5：50m
    uint16_t scale_len; //比例尺像素长度
    uint8_t rotate; //input结构中的rotate是旋转的配置，这里是根据实际情况决策的rotate状态
    uint8_t with_route; //是否有线路
    uint8_t no_transition_effect; //不显示过渡效果（从10km或全景比例尺zoom_out到50m比例尺的操作时，绘制中间帧会错乱，因为坐标转换会超出int16_t范围）
    uint8_t poi_info_show;
    uint8_t map_lock; //地图锁。绘制与更新互斥
    uint8_t draw_after_update; //需要在更新之后绘制
    uint8_t draw_trigger; //触发一次绘制
    uint8_t processing; //正在处理中
    uint8_t is_need_copy_map; //是否需要拷贝地图数据
    uint8_t route_to_location; //是否导航到位置点
    uint8_t route_to_start; //是否导航到起点
    uint8_t map_zoom_flage; //地图缩放
    uint8_t route_arrived; //是否到达终点
    uint8_t leave_touch_map;//离开触控地图
} navi_data_t;

//获取GPS数据
map_input_gps_t* get_navi_gps(void);
map_input_gps_t* get_navi_gps_view(void); //预览GPS坐标，用于地图拖拽
map_input_gps_t* get_navi_gps_map(bool updata_map); //地图GPS坐标，实际或预览

//获取导航输入结构
map_input_data_t* get_map_input(void);

navi_data_t* get_navi_data(void);

int navi_gps_init(void);

void navi_gps_lock(void);

//获取缩放等级
uint16_t get_zoom(void);

uint16_t get_last_zoom(void);
uint16_t get_last_zoom_virtual(void);
uint32_t get_last_zoom_distance(void);

//设置缩放等级
void set_zoom(uint16_t zoom, bool is_location);

//放大（拉近）（+）
uint16_t zoom_in(bool mediate);

//缩小（推远）（-）
uint16_t zoom_out(bool is_press_machine_button, bool mediate);

//获取地图触发绘制标志
uint8_t get_map_draw_trigger(void);

//获取比例尺距离
uint32_t get_zoom_dist(void);

uint32_t get_zoom_dist_with_zoom(uint16_t zoom);

//Get virtual zoom when total view.
uint16_t get_zoom_virtual(void);

//检查点是否在画布区域内
int canvas_check_point(const point16_t *point);

//检查矩形是否在画布区域内
int map_rect_check(const rect16_t *rect , bool is_check_way_name);

//Check line segment whether or not across screen.
int canvas_line_check(const point16_t *point0, const point16_t *point1);

//经纬度坐标转换为像素坐标
//返回值说明当前坐标是否有效
int position_to_pixel(point16_t *point, struct minmea_float *lat_minmea, struct minmea_float *lon_minmea);
int position_to_pixel_int(point16_t *point, int32_t lat, int32_t lon);
int position_to_pixel_float(point16_t *point, double lat, double lon);

//像素坐标转换为经纬度坐标
int pixel_to_position(struct minmea_float *lat_minmea, struct minmea_float *lon_minmea, point16_t *point);

//地图数据结构初始化
int map_data_init(void);

//地图GPS结构初始化
int map_gps_init(void);

//地图画布参数计算
int map_canvas_calc(void);

//检查经纬度是否有效
int check_point_valid(int32_t position_lat, int32_t position_lon);

void navi_route_lat_lng_init(void);
// void router_route_lat_lng_init(router_navi_type_t navi_type);

void navi_route_lat_lng_deinit(void);
// void router_route_lat_lng_deinit(router_navi_type_t navi_type);

void set_navi_route_arrived(void);
// void set_navi_router_arrived(router_navi_type_t type);
void clear_navi_route_arrived(void);

#if defined(__cplusplus)
}
#endif

#endif //_NAVI_H_
