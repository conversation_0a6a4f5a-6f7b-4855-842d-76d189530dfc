/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   at_cmd_table.c
@Time    :   2025/01/06 13:55:20
*
**************************************************************************/
#include "rtthread.h"
#include "key_module.h"
#include "qw_sensor_user.h"
#include "qw_system_params.h"
#include "burn_test.h"
#include "cfg_header_def.h"
#include "../factory_module/factory_mudule.h"
#include "gui_event_service.h"
#ifdef IGS_DEV
#include "beep.h"
#include "drv_beep.h"
#include "motor.h"
#include "drv_motor.h"
#include "rtdef.h"
#include "remind_response_app/remind_response_app.h"
#include "poweroff_ctrl.h"
#include "ble_data_inf.h"
#include "ble_test_cmd.h"
#include "boot_switch.h"
#include "service_gui_health.h"
#include "cross_core_msh.h"
#include "factory_data.h"
#include "mt3503.h"
#include "mag_data_module/compass_algorithm.h"
#include "mag_data_module/compass_ctrl.h"
#endif

// 声明全局命令上下文管理器
extern cmd_context_manager_t g_cmd_context_manager;

// 产测数据
factory_test_t fat_dat = { 0 };

// 产线测试函数实现
static void production_test_beep(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    // 半成品/成品测试跳转到蜂鸣器界面
    fat_dat.info.item = ITEM_BEEP;
    if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
        submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_page_list[FAT_MENU_TYPE_BEEP]);

    uint8_t status = AT_STATUS_SUCCESS;
    // TODO: 实现蜂鸣器测试
    #ifdef IGS_DEV
    beep_work_param_t beep_param = { 0 };
    beep_param.freq = 1000;
    beep_param.duty_cycle = 90;
    beep_param.voltage_multiplier = 3;
    beep_param.beep = 300;
    beep_param.interval = 0;
    beep_param.repeat = 10;
    beep_param.open = true;
    rt_device_t beep_device = rt_device_find("beep");
    if(beep_device)
    {
        rt_device_control(beep_device, RT_DEVICE_CTRL_CONFIG, (void*)&beep_param);
    }
    #endif
    generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
}

static void production_test_motor(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    // 半成品/成品测试跳转到马达界面
    fat_dat.info.item = ITEM_MOTOR;
    if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
        submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_page_list[FAT_MENU_TYPE_MOTOR]);

    uint8_t status = AT_STATUS_SUCCESS;
    // TODO: 实现马达测试
    #ifdef IGS_DEV
    motor_work_param_t motor_param = { 0 };
    motor_param.freq = FREQ_DI;
    motor_param.duty_cycle = 90;
    motor_param.voltage_multiplier = 3;
    motor_param.beep = 300;
    motor_param.interval = 0;
    motor_param.repeat = 10;
    motor_param.open = true;
    rt_device_t motor_device = rt_device_find("motor");
    if(motor_device)
    {
        rt_device_control(motor_device, RT_DEVICE_CTRL_CONFIG, (void*)&motor_param);
    }
    #endif
    generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
}

static void key_test_callback(uint8_t key, uint8_t key_event)
{
    if(key_event!=0) return;
    FACTORY_LOG_I("key: %d, key_event: %d\n", key, key_event);
    uint8_t status = AT_STATUS_SUCCESS;
    //uint8_t data[2] = {key, key_event};
    // TODO: 发送封装4位自检结果字符串
    char buffer[8] = {0};
#ifdef IGS_DEV
	snprintf(buffer, sizeof(buffer), "%02X%02X",key, key_event);
#endif

    // 获取当前命令上下文
    cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
    if (context) {
        generate_at_response(context->cmd->func,
                           context->cmd->cmd,
                           status,
                           (const uint8_t*)buffer, (uint8_t)strlen(buffer),
                           context->response,
                           context->response_len);

        // 数据上报
        ble_debug_data_notif(0xFF, context->response,(uint8_t)*context->response_len);
    }
}

static void production_test_key(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    if (strcmp(cmd->cmd, "START") == 0)
    {
        // 半成品/成品测试跳转到测试界面
        fat_dat.info.item = ITEM_KEY;
        if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
            submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_page_list[FAT_MENU_TYPE_KEY]);

        // 创建命令上下文
        cmd_context_t *context = cmd_context_create(&g_cmd_context_manager, cmd, response, response_len);
        if (context == NULL) {
            status = AT_STATUS_FAIL;
            goto end;
        }

        key_module_register_callback(key_test_callback);
    }
    else if (strcmp(cmd->cmd, "STOP") == 0)
    {
        key_module_register_callback(NULL);
        // 释放当前命令上下文
        cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
        if (context) {
            cmd_context_release(&g_cmd_context_manager, context);
        }
        // 半成品/成品测试跳转到工模界面
        if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
            submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_main_page);
    }
end:
    generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
}


static void knob_test_callback(uint8_t key, int16_t data_x, int16_t data_y)
{
    FACTORY_LOG_I("key: %d, data_x: %d\n", key, data_x);
    uint8_t status = AT_STATUS_SUCCESS;
    //uint8_t data[2] = {key, key_event};
    // TODO: 发送封装4位自检结果字符串
    char buffer[8] = {0};
#ifdef IGS_DEV
	snprintf(buffer, sizeof(buffer), "%04X%04X",key, data_x);
#endif

    // 获取当前命令上下文
    cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
    if (context) {
        generate_at_response(context->cmd->func,
                           context->cmd->cmd,
                           status,
                           (const uint8_t*)buffer, (uint8_t)strlen(buffer),
                           context->response,
                           context->response_len);

        // 数据上报
        ble_debug_data_notif(0xFF, context->response,(uint8_t)*context->response_len);
    }
}

static void production_test_knob(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    if (strcmp(cmd->cmd, "START") == 0)
    {
        // 半成品/成品测试跳转到测试界面
        fat_dat.info.item = ITEM_KNOB;
        if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
            submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_page_list[FAT_MENU_TYPE_KEY]);

        // 创建命令上下文
        cmd_context_t *context = cmd_context_create(&g_cmd_context_manager, cmd, response, response_len);
        if (context == NULL) {
            status = AT_STATUS_FAIL;
            goto end;
        }

        //knob_module_register_callback(knob_test_callback);
    }
    else if (strcmp(cmd->cmd, "STOP") == 0)
    {
        //knob_module_register_callback(NULL);
        // 释放当前命令上下文
        cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
        if (context) {
            cmd_context_release(&g_cmd_context_manager, context);
        }

        // 半成品/成品测试跳转到工模界面
        if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
            submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_main_page);
    }
    else if (strcmp(cmd->cmd, "CALIB") == 0)
    {
        // 光旋钮校正
        if (cmd->data_count < 1) {
            status = AT_STATUS_FAIL;
            goto end;
        }

        int reg_value = 0;
        if (sscanf((const char *)cmd->data, "%4X", &reg_value) != 1) {
            FACTORY_LOG_E("Parse error: Invalid hex character:%s",cmd->data);
            status = AT_STATUS_FAIL;
            goto end;
        }
#ifdef IGS_DEV
        if (mt3503_control(MT3503_CMD_SET_RES_X, (void *)&reg_value) != 0)
        {
            FACTORY_LOG_E("mt3503_res_x_set failed!");
        }
        else
        {
            FACTORY_LOG_I("mt3503_res_x_set: res_x=%04X", reg_value);
        }
#endif
    }
end:
    generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
}


static void production_test_lcd(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    // TODO: 实现LCD测试
    if (cmd->data_count == 0) {
        if (strcmp(cmd->cmd, "START") == 0)
        {
            // 半成品/成品测试跳转到测试界面
            fat_dat.info.item = ITEM_LCD;
            if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
                submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_page_list[FAT_MENU_TYPE_BRIGHTBESS]);

            // 创建命令上下文
            cmd_context_t *context = cmd_context_create(&g_cmd_context_manager, cmd, response, response_len);
            if (context == NULL) {
                status = AT_STATUS_FAIL;
                goto end;
            }
#ifdef IGS_DEV

#endif
        }
        else if (strcmp(cmd->cmd, "STOP") == 0)
        {
#ifdef IGS_DEV

#endif
            // 释放当前命令上下文
            cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
            if (context) {
                cmd_context_release(&g_cmd_context_manager, context);
            }

            // 半成品/成品测试跳转到工模界面
            if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
                submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_main_page);
            }
    }
    else
    {
        // 开启LCD陪测测试
        if((fat_dat.info.mode == STEP_CP)&&(fat_dat.info.sub_mode == CP_STEP_LCD))
        {
            // 半成品/成品测试跳转到测试界面
            fat_dat.info.item = ITEM_LCD;
            if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
                submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_page_list[FAT_MENU_TYPE_BRIGHTBESS]);

            uint8_t value = 0;
            if (sscanf((const char *)cmd->data, "%2hhx", &value) != 1) {
                FACTORY_LOG_E("Parse error: Invalid hex character:%s",cmd->data);
                status = AT_STATUS_FAIL;
                goto end;
            }
            if (value == 0) {
                // Turn off LCD
                // TODO: Add LCD off implementation
                FACTORY_LOG_I("LCD OFF\n");
            } else if (value == 1) {
                // Turn on LCD
                // TODO: Add LCD on implementation
                FACTORY_LOG_I("LCD ON\n");
            } else {
                status = AT_STATUS_FAIL;
            }
        }
    }
end:
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, NULL, 0, response, response_len);
}



#ifdef IGS_DEV
static struct sensor_light g_ltr_data = {0};
static void light_callback(const void *in, uint32_t len)
{
    if (in == NULL)
    {
        FACTORY_LOG_E("FAT_DATA:", "\n light data is null \n");
        return;
    }

    memcpy(&g_ltr_data, in, len);
    //FACTORY_LOG_I("light_callback %f  %f\n", g_ltr_data.light, g_ltr_data.ir);
    uint8_t status = AT_STATUS_SUCCESS;

    char buffer[40] = {0};
#ifdef IGS_DEV
	snprintf(buffer, sizeof(buffer), "%f,%f",g_ltr_data.light, g_ltr_data.ir);
#endif
    // 获取当前命令上下文
    cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
    if (context) {
        generate_at_response(context->cmd->func,
                           context->cmd->cmd,
                           status,
                           (const uint8_t*)buffer, (uint8_t)strlen(buffer),
                           context->response,
                           context->response_len);
        // 数据上报
        ble_debug_data_notif(0xFF, context->response,(uint8_t)*context->response_len);
    }
}
#endif
static void production_test_light(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;

    if (cmd->data_count == 0) {
        if (strcmp(cmd->cmd, "START") == 0)
        {
            // 半成品/成品测试跳转到测试界面
            fat_dat.info.item = ITEM_LIGHT;
            if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
                submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_page_list[FAT_MENU_TYPE_LIGHT]);

            // 创建命令上下文
            cmd_context_t *context = cmd_context_create(&g_cmd_context_manager, cmd, response, response_len);
            if (context == NULL) {
                status = AT_STATUS_FAIL;
                goto end;
            }
#ifdef IGS_DEV
            optional_config_t config = {.sampling_rate = 1};
            int32_t ret = qw_dataserver_subscribe_id(DATA_ID_LIGHT,light_callback, &config);
            if (ret != 0)
            {
                FACTORY_LOG_E("FAC_LIGHT", "qw_dataserver_subscribe error:%d\n", ret);
                goto end;
            }
#endif
        }
        else if (strcmp(cmd->cmd, "STOP") == 0)
        {
#ifdef IGS_DEV
            int ret = qw_dataserver_unsubscribe_id(DATA_ID_LIGHT, light_callback);
            if (ret != 0)
            {
                FACTORY_LOG_E("FAC_LIGHT", "qw_dataserver_unsubscribe error:%d\n", ret);
                goto end;
            }
#endif
            // 释放当前命令上下文
            cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
            if (context) {
                cmd_context_release(&g_cmd_context_manager, context);
            }

            // 半成品/成品测试跳转到工模界面
            if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
                submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_main_page);
            }
    }
    else
    {
        // TODO: 实现LIGHT校正
        int light_value = 0;
        if (sscanf((const char *)cmd->data, "%4X", &light_value) != 1) {
            FACTORY_LOG_E("Parse error: Invalid hex character:%s",cmd->data);
            status = AT_STATUS_FAIL;
            goto end;
        }
        if ((light_value>0)&&(light_value<60000)) {
            FACTORY_LOG_I("cmd_w1160 calibrate %d\n",light_value);
            // 将light_value转换为字符串
            char light_value_str[100];
            snprintf(light_value_str, sizeof(light_value_str), "cmd_w1160 calibrate %d", light_value);
            // 调用 lcpu 的 msh 命令
            send_lcpu_msh(light_value_str);
        } else {
            status = AT_STATUS_FAIL;
        }
    }
end:
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, NULL, 0, response, response_len);
}


#ifdef IGS_DEV
static float g_altitude = 0.0f;
static float g_pressure = 0.0f;
static float g_temperature = 0.0f;
static const float sea_level_pressure = 101325.0f;   // 海平面标准大气压，单位：Pa
static float calculate_altitude(float pressure_pa)
{
    // 使用国际标准大气压公式计算海拔高度
    // 海拔高度公式：h = 44330.77 * (1 - (P / P0) ^ 0.190263)
    // 其中 P 是当前气压（单位：Pa），P0 是海平面标准大气压（101325 Pa）
    // const float sea_level_pressure = 101325.0f;   // 海平面标准大气压，单位：Pa
    return 44330.77f * (1.0f - pow(pressure_pa / sea_level_pressure, 0.190263f));
}
static void bar_callback(const void *in, uint32_t len)
{
    if (in == NULL)
    {
        return;
    }
    const struct sensor_baro *barometer_data = in;
    g_pressure = barometer_data->pressure * 100;
    g_altitude = calculate_altitude(g_pressure);
    g_temperature = barometer_data->temperature;

    //FACTORY_LOG_I("bar_callback %f  %f  %f \n", g_pressure, g_altitude, g_temperature);
    uint8_t status = AT_STATUS_SUCCESS;

    char buffer[64] = {0};
#ifdef IGS_DEV
	snprintf(buffer, sizeof(buffer), "%f,%f,%f",g_pressure, g_altitude, g_temperature);
#endif
    // 获取当前命令上下文
    cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
    if (context) {
        generate_at_response(context->cmd->func,
                           context->cmd->cmd,
                           status,
                           (const uint8_t*)buffer, (uint8_t)strlen(buffer),
                           context->response,
                           context->response_len);
        // 数据上报
        ble_debug_data_notif(0xFF, context->response,(uint8_t)*context->response_len);
    }
}
#endif
static void production_test_bar(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    // TODO: 实现BAR校正
    if (cmd->data_count == 0) {
        if (strcmp(cmd->cmd, "START") == 0)
        {
            // 半成品/成品测试跳转到测试界面
            fat_dat.info.item = ITEM_BAROMETER;
            if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
                submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_page_list[FAT_MENU_TYPE_BARO]);

            // 创建命令上下文
            cmd_context_t *context = cmd_context_create(&g_cmd_context_manager, cmd, response, response_len);
            if (context == NULL) {
                status = AT_STATUS_FAIL;
                goto end;
            }
#ifdef IGS_DEV
            optional_config_t config = {.sampling_rate = 0};
            int32_t ret = qw_dataserver_subscribe_id(DATA_ID_BROMETER,bar_callback, &config);
            if (ret != 0)
            {
                FACTORY_LOG_E("FAC_LIGHT", "qw_dataserver_subscribe error:%d\n", ret);
                goto end;
            }
#endif
        }
        else if (strcmp(cmd->cmd, "STOP") == 0)
        {
#ifdef IGS_DEV
            int ret = qw_dataserver_unsubscribe_id(DATA_ID_BROMETER, bar_callback);
            if (ret != 0)
            {
                FACTORY_LOG_E("FAC_LIGHT", "qw_dataserver_unsubscribe error:%d\n", ret);
                goto end;
            }
#endif
            // 释放当前命令上下文
            cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
            if (context) {
                cmd_context_release(&g_cmd_context_manager, context);
            }

            // 半成品/成品测试跳转到工模界面
            if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
                submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_main_page);
        }
    }
    else
    {
        int bar_value = 0;
        if (sscanf((const char *)cmd->data, "%8X", &bar_value) != 1) {
            FACTORY_LOG_E("Parse error: Invalid hex character:%s",cmd->data);
            status = AT_STATUS_FAIL;
            goto end;
        }
        if ((bar_value>0)&&(bar_value<20000000)) {
            FACTORY_LOG_I("bar calibrate %d\n",bar_value);
            // 将bar_value转换为字符串
            char bat_value_str[100];
            snprintf(bat_value_str, sizeof(bat_value_str), "bar calibrate %d", bar_value);
            // 调用 lcpu 的 msh 命令
            send_lcpu_msh(bat_value_str);
        } else {
            status = AT_STATUS_FAIL;
        }
    }
end:
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, NULL, 0, response, response_len);
}


struct acc_gyro_data_t
{
    float acc_x;
    float acc_y;
    float acc_z;
    float gyro_x;
    float gyro_y;
    float gyro_z;
};
static struct acc_gyro_data_t data_ = { 0 };
static void drv_accel_callback(const void* in, uint32_t len)
{
    if (in == NULL) return;
    struct sensor_accel* data = (struct sensor_accel*)in;
    data_.acc_x = data->x;
    data_.acc_y = data->y;
    data_.acc_z = data->z;

    uint8_t status = AT_STATUS_SUCCESS;

    char buffer[120] = {0};
#ifdef IGS_DEV
	snprintf(buffer, sizeof(buffer), "%f,%f,%f,%f,%f,%f",data_.acc_x, data_.acc_y, data_.acc_z,data_.gyro_x,data_.gyro_y,data_.gyro_z);
#endif
    // 获取当前命令上下文
    cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
    if (context) {
        generate_at_response(context->cmd->func,
                           context->cmd->cmd,
                           status,
                           (const uint8_t*)buffer, (uint8_t)strlen(buffer),
                           context->response,
                           context->response_len);
        // 数据上报
        ble_debug_data_notif(0xFF, context->response,(uint8_t)*context->response_len);
    }
}

static void drv_gyro_callback(const void* in, uint32_t len)
{
    if (in == NULL) return;
    struct sensor_gyro* data = (struct sensor_gyro*)in;
    data_.gyro_x = data->x;
    data_.gyro_y = data->y;
    data_.gyro_z = data->z;
}

static void production_test_att(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    // TODO: 实现ACC校正
    if (cmd->data_count == 0) {
        if (strcmp(cmd->cmd, "START") == 0)
        {
            // 半成品/成品测试跳转到测试界面
            fat_dat.info.item = ITEM_ACC;
            if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
                submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_page_list[FAT_MENU_TYPE_ATT]);

            // 创建命令上下文
            cmd_context_t *context = cmd_context_create(&g_cmd_context_manager, cmd, response, response_len);
            if (context == NULL) {
                status = AT_STATUS_FAIL;
                goto end;
            }
#ifdef IGS_DEV
            optional_config_t config = {
                .sampling_rate = 26,
            };
            int32_t ret = qw_dataserver_subscribe_id(DATA_ID_RAW_ACC, drv_accel_callback, &config);
            if (ret != 0)
            {
                FACTORY_LOG_E("FAT:","algo_accel_open erro ret:%d\n", ret);
            }
            ret = qw_dataserver_subscribe_id(DATA_ID_RAW_GYRO, drv_gyro_callback, &config);
            if (ret != 0)
            {
                FACTORY_LOG_E("FAT:","algo_accel_open erro ret:%d\n", ret);
            }
#endif
        }
        else if (strcmp(cmd->cmd, "STOP") == 0)
        {
#ifdef IGS_DEV
            int32_t ret = qw_dataserver_unsubscribe_id(DATA_ID_RAW_ACC, drv_accel_callback);
            if (ret == 0)
            {
                FACTORY_LOG_E("FAT:","SixAxisSensorDataModel::sensor_accel unsub\n");
            }
            ret = qw_dataserver_unsubscribe_id(DATA_ID_RAW_GYRO, drv_gyro_callback);
            if (ret == 0)
            {
                FACTORY_LOG_E("FAT:","SixAxisSensorDataModel::sensor_gyro unsub\n");
            }
#endif
            // 释放当前命令上下文
            cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
            if (context) {
                cmd_context_release(&g_cmd_context_manager, context);
            }

            // 半成品/成品测试跳转到工模界面
            if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
                submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_main_page);
        }
    }
    else
    {
        int acc_calib[9] = {0};
        if (sscanf((const char *)cmd->data, "%8X,%8X,%8X,%8X,%8X,%8X,%8X,%8X,%8X", &acc_calib[0], &acc_calib[1], &acc_calib[2], \
                                                                                &acc_calib[3], &acc_calib[4], &acc_calib[5], \
                                                                                &acc_calib[6], &acc_calib[7], &acc_calib[8]) != 1) {
            FACTORY_LOG_E("Parse error: Invalid hex character:%s",cmd->data);
            status = AT_STATUS_FAIL;
            goto end;
        }
        FACTORY_LOG_I("att calibrate\n");
        // 将acc_value转换为字符串
        char acc_calib_str[100];
        snprintf(acc_calib_str, sizeof(acc_calib_str), "att calibrate %8X %8X %8X %8X %8X %8X %8X %8X %8X", acc_calib[0], acc_calib[1], acc_calib[2], \
                                                                                acc_calib[3], acc_calib[4], acc_calib[5], \
                                                                                acc_calib[6], acc_calib[7], acc_calib[8]);
        // 调用 lcpu 的 msh 命令
        send_lcpu_msh(acc_calib_str);
    }
end:
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, NULL, 0, response, response_len);
}

static void compass_test_callback(int compass_align,int compass_angle)
{
    FACTORY_LOG_I("compass_callback %d  %d\n", compass_align, compass_angle);
    uint8_t status = AT_STATUS_SUCCESS;

    char buffer[40] = {0};
#ifdef IGS_DEV
	snprintf(buffer, sizeof(buffer), "%d,%d",compass_align, compass_angle);
#endif
    // 获取当前命令上下文
    cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
    if (context) {
        generate_at_response(context->cmd->func,
                           context->cmd->cmd,
                           status,
                           (const uint8_t*)buffer, (uint8_t)strlen(buffer),
                           context->response,
                           context->response_len);
        // 数据上报
        ble_debug_data_notif(0xFF, context->response,(uint8_t)*context->response_len);
    }
}

static void production_test_mag(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    // TODO: 实现MAG校正
    if (cmd->data_count == 0) {
        if (strcmp(cmd->cmd, "START") == 0)
        {
            // 半成品/成品测试跳转到测试界面
            fat_dat.info.item = ITEM_GEOMAGNET;
            if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
                submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_page_list[FAT_MENU_TYPE_MAG]);

            // 创建命令上下文
            cmd_context_t *context = cmd_context_create(&g_cmd_context_manager, cmd, response, response_len);
            if (context == NULL) {
                status = AT_STATUS_FAIL;
                goto end;
            }
#ifdef IGS_DEV
    #ifdef CONFIG_ECOMPASS_FORCE_CALIBRATE
            reset_ecompass_status();//重新校准.
    #endif
            start_compass();
            compass_module_register_callback(compass_test_callback);
#endif
        }
        else if (strcmp(cmd->cmd, "STOP") == 0)
        {
#ifdef IGS_DEV
            compass_module_register_callback(NULL);
            stop_compass();
#endif
            // 释放当前命令上下文
            cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
            if (context) {
                cmd_context_release(&g_cmd_context_manager, context);
            }

            // 半成品/成品测试跳转到工模界面
            if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
                submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_main_page);
        }
    }
    else
    {
        int mag_calib[9] = {0};
        if (sscanf((const char *)cmd->data, "%8X,%8X,%8X,%8X,%8X,%8X,%8X,%8X,%8X", &mag_calib[0], &mag_calib[1], &mag_calib[2], \
                                                                                &mag_calib[3], &mag_calib[4], &mag_calib[5], \
                                                                                &mag_calib[6], &mag_calib[7], &mag_calib[8]) != 1) {
            FACTORY_LOG_E("Parse error: Invalid hex character:%s",cmd->data);
            status = AT_STATUS_FAIL;
            goto end;
        }
        FACTORY_LOG_I("mag calibrate\n");
        // 将mag_value转换为字符串
        char mag_calib_str[100];
        snprintf(mag_calib_str, sizeof(mag_calib_str), "mag calibrate %8X %8X %8X %8X %8X %8X %8X %8X %8X", mag_calib[0], mag_calib[1], mag_calib[2], \
                                                                                mag_calib[3], mag_calib[4], mag_calib[5], \
                                                                                mag_calib[6], mag_calib[7], mag_calib[8]);
        // 调用 lcpu 的 msh 命令
        send_lcpu_msh(mag_calib_str);
    }
end:
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, NULL, 0, response, response_len);
}


// #include "drivers/usb_common.h"
// #include "drivers/usb_device.h"
// #include "drv_gpio.h"
// #include "drv_common.h"
// #include "dfs_fs.h"
// #include "drv_io.h"

// /************************************************************************
//  *@brief: USB设备类型枚举
//  *@description: 定义USB设备的连接类型
// *************************************************************************/
// typedef enum {
//     USB_DEV_NONE = 0,        // 未连接
//     USB_DEV_CHARGER = 1,     // 充电器
//     USB_DEV_COMPUTER = 2     // 电脑
// } usb_dev_type_e;
// static uint8_t usbd_registered = false; // 确保 sifli_usbd_register 只运行一次
// /************************************************************************s
//  *@function: usb_insert_check
//  *@brief: 检测USB插入状态及类型
//  *@param: void
//  *@return: usb_dev_type_e 类型的枚举值
// *************************************************************************/
// static usb_dev_type_e usb_insert_check(void)
// {
//     uint8_t charge_status;
//     int8_t insert_num = 0;

//     //注册usb
//     if (!usbd_registered)
//     {
//         extern int sifli_usbd_register(void);
//         sifli_usbd_register();
//         usbd_registered = true;
//     }
//     //等待5秒
//     rt_thread_mdelay(2000);

//     udcd_t dcd = (udcd_t)rt_device_find("usbd");
//     if (dcd == RT_NULL)
//     {
//         return USB_DEV_NONE;
//     }

//     // 检查USB设备状态
//     udevice_t dev = rt_usbd_find_device(dcd);
//     if (dev != RT_NULL)
//     {
//         // 检查USB设备状态
//         if (dev->state == USB_STATE_CONFIGURED)
//         {
//             return USB_DEV_COMPUTER;  // USB已配置，说明是电脑
//         }
//         else if (dev->state == USB_STATE_POWERED ||
//                 dev->state == USB_STATE_DEFAULT)
//         {
//             return USB_DEV_CHARGER;  // 只有供电状态，说明是充电器
//         }
//     }

//     // 有电源但未识别到USB设备，判定为充电器
//     return USB_DEV_CHARGER;
// }

#ifdef IGS_DEV
//U盘测试(4G以下，后面改KB)
#include <dfs_fs.h>
static uint32_t upan_cap = 157286400;
static uint32_t upan_size = 3559696384;
int get_upan_info(void)
{
    struct statfs buffer;
    int result = dfs_statfs(NULL, &buffer);
    if (result != 0)
    {
        FACTORY_LOG_E("dfs_statfs failed.\n");
        return -1;
    }
    upan_cap = (uint32_t)(((long long)buffer.f_bsize) * ((long long)buffer.f_bfree));
    upan_size = (uint32_t)(((long long)buffer.f_bsize) * ((long long)buffer.f_blocks));
    return 0;
}
//INIT_APP_EXPORT(get_upan_info);

uint32_t get_upan_total_size(void)
{
    // TODO: 获取UPAN总空间大小,单位B
    return upan_size;
}
uint32_t get_upan_used_size(void)
{
    // TODO: 获取UPAN已使用空间大小,单位B
    return upan_cap;
}
#endif

static void production_test_upan(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
#if 0
    // TODO: 实现U盘测试
    if (cmd->data_count < 1) {
        status = AT_STATUS_FAIL;
        goto end;
    }
    uint8_t value = 0;
    if (sscanf(cmd->data, "%2hhx", &value) != 1) {
        FACTORY_LOG_E("Parse error: Invalid hex character:%s",cmd->data);
        status = AT_STATUS_FAIL;
        goto end;
    }
    if (value == 0) {
        // 移除U盘
        FACTORY_LOG_I("UPAN UMOUNT\n");
        //extern void usbd_deinit(void);
        //usbd_deinit();
    } else if (value == 1) {
        // 挂载U盘
        FACTORY_LOG_I("UPAN MOUNT\n");
        // if(usb_insert_check() == USB_DEV_COMPUTER)
        // {
        //     // 检测USB模式有效
        // }
    } else {
        status = AT_STATUS_FAIL;
    }
end:
#else
    FACTORY_LOG_I("upan\n");
    // TODO: 发送封装16位自检结果字符串
    char buffer[18] = {0};
#ifdef IGS_DEV
    //get_upan_info();
	snprintf(buffer, sizeof(buffer), "%08X%08X", get_upan_total_size(), get_upan_used_size());
#endif
#endif
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, (const uint8_t*)buffer, (uint8_t)strlen(buffer), response, response_len);
}

static struct sensor_cap _data = {0};
static void cap_callback(const void *data, uint32_t len)
{
    if (NULL == data || sizeof(struct sensor_cap) != len)
    {
        return;
    }
    memcpy(&_data, data, len);
    FACTORY_LOG_I("cap_callback %d  %d  %d  %d\n", _data.rawdata[0], _data.rawdata[1], _data.rawdata[2], _data.rawdata[3]);
    uint8_t status = AT_STATUS_SUCCESS;

    char buffer[40] = {0};
#ifdef IGS_DEV
	snprintf(buffer, sizeof(buffer), "%d,%d,%d,%d,%d",_data.status,_data.rawdata[0], _data.rawdata[1], _data.rawdata[2], _data.rawdata[3]);
#endif
    // 获取当前命令上下文
    cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
    if (context) {
        generate_at_response(context->cmd->func,
                           context->cmd->cmd,
                           status,
                           (const uint8_t*)buffer, (uint8_t)strlen(buffer),
                           context->response,
                           context->response_len);
        // 数据上报
        ble_debug_data_notif(0xFF, context->response,(uint8_t)*context->response_len);
    }
}

//获取充电状态
static void production_test_bat(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    if (cmd->data_count == 0) {
        if (strcmp(cmd->cmd, "START") == 0)
        {
            // 半成品/成品测试跳转到测试界面
            fat_dat.info.item = ITEM_BATTERY;
            if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
                submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_page_list[FAT_MENU_TYPE_BAT]);

            // 创建命令上下文
            cmd_context_t *context = cmd_context_create(&g_cmd_context_manager, cmd, response, response_len);
            if (context == NULL) {
                status = AT_STATUS_FAIL;
                goto end;
            }
#ifdef IGS_DEV
            optional_config_t config = {.sampling_rate = 0};
            int ret = qw_dataserver_subscribe_id(DATA_ID_CAP, cap_callback, &config);
            if (ret != 0)
            {
                FACTORY_LOG_E("FAC_BAT", "qw_dataserver_subscribe error:%d\n", ret);
            }
#endif
        }
        else if (strcmp(cmd->cmd, "STOP") == 0)
        {
#ifdef IGS_DEV
            int ret = qw_dataserver_unsubscribe_id(DATA_ID_CAP, cap_callback);
            if (ret != 0)
            {
                FACTORY_LOG_E("FAC_BAT", "qw_dataserver_unsubscribe error:%d\n", ret);
            }
#endif
            // 释放当前命令上下文
            cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
            if (context) {
                cmd_context_release(&g_cmd_context_manager, context);
            }

            // 半成品/成品测试跳转到工模界面
            if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
                submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_main_page);
        }
    }
    else
    {
        uint8_t value = 0;
        if (sscanf((const char *)cmd->data, "%2hhx", &value) != 1) {
            FACTORY_LOG_E("Parse error: Invalid hex character:%s",cmd->data);
            status = AT_STATUS_FAIL;
            goto end;
        }
        char buffer[12] = {0};
        if (value == 0) {
            // 返回充电状态
            FACTORY_LOG_I("CHARGE+STATE\n");
        #ifdef IGS_DEV
            snprintf(buffer, sizeof(buffer), "%d",_data.status);
        #endif
        } else if (value == 1) {
            // 返回充电电流
            FACTORY_LOG_I("CHARGE+CURRENT\n");
        #ifdef IGS_DEV
            snprintf(buffer, sizeof(buffer), "%d",_data.rawdata[2]);
        #endif
        } else {
            status = AT_STATUS_FAIL;
        }
    }
end:
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, NULL, 0, response, response_len);
}

//关机测试
static void production_test_shutdown(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    FACTORY_LOG_I("shutdown\n");
    request_poweroff(NULL);
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, NULL, 0, response, response_len);
}

//重启测试
static void production_test_reboot(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    FACTORY_LOG_I("reboot\n");
    request_reboot(NULL);
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, NULL, 0, response, response_len);
}

uint8_t get_factory_master_mode(void)
{
    // TODO: 将支持的陪测模式转成BIT标志位
    return 0xFF;
}

uint32_t get_pacb_check_result(void)
{
    // TODO: 将自检结果转成BIT标志位
    return get_system_params()->check_result;
}
//自检完成后测试
static void production_test_ready(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    FACTORY_LOG_I("ready\n");
    // 测试模式解析
    if (cmd->data_count < 1) {
        status = AT_STATUS_FAIL;
        goto end;
    }
    uint8_t value = 0;
    if (sscanf((const char *)cmd->data, "%2hhx", &value) != 1) {
        FACTORY_LOG_E("Parse error: Invalid hex character:%s",cmd->data);
        status = AT_STATUS_FAIL;
        goto end;
    }
    if (value == 0) {
        // PPG/LCD陪测板模式
        FACTORY_LOG_I("Master Mode\n");
        char buffer[4] = {0};
    #ifdef IGS_DEV
        snprintf(buffer, sizeof(buffer), "%02X", get_factory_master_mode());
    #endif
        generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, (const uint8_t*)buffer, (uint8_t)strlen(buffer), response, response_len);
        return;
    }
    else
    {
        // 厂测模式
        FACTORY_LOG_I("Slave Mode\n");
        // TODO: 发送封装8位自检结果字符串
        char buffer[10] = {0};
    #ifdef IGS_DEV
        snprintf(buffer, sizeof(buffer), "%08X", get_pacb_check_result());
    #endif
        generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, (const uint8_t*)buffer, (uint8_t)strlen(buffer), response, response_len);
        return;
    }
end:
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, NULL, 0, response, response_len);
}

//工厂数据
static void production_test_data(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    char buffer[240] = {0};
    if (strcmp(cmd->cmd, "R") == 0)
    {
        // 读取工厂数据
    #ifdef IGS_DEV
        // 设备UUID
        char uuid_str[(CFG_UID_DATA_LEN<<1)+1]={0};    //设备UUID HEX字符串
        ALIGN(4) uint8_t uid[CFG_UID_DATA_LEN] = {0}; //设备UUID
        HAL_EFUSE_Read(0, uid, CFG_UID_DATA_LEN);
        snprintf(uuid_str, sizeof(uuid_str), "%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X",uid[0],uid[1],uid[2],uid[3],uid[4],uid[5],uid[6],uid[7],uid[8],uid[9],uid[10],uid[11],uid[12],uid[13],uid[14],uid[15]);
        snprintf(buffer+strlen(buffer), sizeof(uuid_str), "%s", uuid_str);
        // SN 字符串
        snprintf(buffer+strlen(buffer), CFG_SN_STR_LEN + 2, ",%s", factory_data_get_sn());
        // KEY
        char key_str[(CFG_KEY_DATA_LEN<<1)+1]={0};    //设备密匙 KEY HEX字符串
        uint8_t *key = factory_data_get_key(); //CFG_KEY_DATA_LEN
        snprintf(key_str, sizeof(key_str), "%02X%02X%02X%02X%02X%02X%02X%02X",key[0],key[1],key[2],key[3],key[4],key[5],key[6],key[7]);
        snprintf(buffer+strlen(buffer), sizeof(key_str) + 1, ",%s", key_str);
        // LIC 字符串
        snprintf(buffer+strlen(buffer), CFG_GOMORE_LICENSE_LEN + 2, ",%s", factory_data_get_gomore_license());
    #endif
    }
    else if (strcmp(cmd->cmd, "W") == 0)
    {
        // TODO:写入工厂数据
    #ifdef IGS_DEV
        // 将逗号替换成结束符实现将字符串分段
        if(cmd->data[CFG_SN_STR_LEN]==0x2c) cmd->data[CFG_SN_STR_LEN] = 0;
        if(cmd->data[CFG_SN_STR_LEN+(CFG_KEY_DATA_LEN<<1) + 1] == 0x2c) cmd->data[CFG_SN_STR_LEN+(CFG_KEY_DATA_LEN<<1) + 1] = 0;
        // SN  10
        char* sn_str = (char*)&cmd->data[0];
        FACTORY_LOG_I("sn_str:%s\n",sn_str);
        // KEY 16
        char* key_str = (char*)&cmd->data[CFG_SN_STR_LEN+1];                          // 偏移=SN字符串长度加一个逗号长度
        FACTORY_LOG_I("key_str:%s\n",key_str);
        // LIC 64
        char* lic_str = (char*)&cmd->data[CFG_SN_STR_LEN+(CFG_KEY_DATA_LEN<<1) + 2];  // 偏移=SN字符串长度加KEY数据的HEX字符串长度加两个逗号长度
        FACTORY_LOG_I("lic_str:%s\n",lic_str);

        //存储AT指令下发的数据
#ifdef USE_FACTORY_DATA_FLASH
        // 将数据写入FLASH
        if(factory_data_write((char*)cmd->data) != 0)
        {
            status = AT_STATUS_FAIL;
            goto end;
        }
#endif
        // // 写入SN序列号到KV:前3位屏蔽
        // if(app_write_factory_sn_to_kv((char*)cmd->data) != 0)
        // {
        //     status = AT_STATUS_FAIL;
        // }
    #endif
    }
end:
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, (const uint8_t*)buffer, (uint8_t)strlen(buffer), response, response_len);
}

static bool factory_app_link_state = false;
//连接产测上位机开始测试：单板，半成品连接上位机后，即使无屏依旧开机
bool get_factory_app_link_state(void)
{
    return factory_app_link_state;
}

//开始测试
static void production_test_start(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    FACTORY_LOG_I("start\n");

    // 测试模式解析
    if (cmd->data_count < 1) {
        status = AT_STATUS_FAIL;
        goto end;
    }
    uint8_t value = 0;
    if (sscanf((const char *)cmd->data, "%2hhx", &value) != 1) {
        FACTORY_LOG_E("Parse error: Invalid hex character:%s",cmd->data);
        status = AT_STATUS_FAIL;
        goto end;
    }

    factory_app_link_state = true;     // 开始产测
    fat_dat.info.mode = STEP_NULL;
    fat_dat.info.busy = true;
    if (strcmp(cmd->cmd, "TEST") == 0) // 开始陪测模式
    {
        fat_dat.info.mode = STEP_CP;
        fat_dat.info.sub_mode = CP_STEP_NULL;
        if (value == CP_STEP_BAT) {
            // 设置为BAT陪测板模式
            FACTORY_LOG_I("BAT CP Mode\n");
            fat_dat.info.sub_mode = CP_STEP_BAT; /* 电池 */
        }
        else if (value == CP_STEP_LCD) {
            // 设置为LCD陪测板模式
            FACTORY_LOG_I("LCD CP Mode\n");
            fat_dat.info.sub_mode = CP_STEP_LCD; /* 屏幕 */
        }
        else if (value == CP_STEP_PPG) {
            // 设置为PPG陪测板模式
            FACTORY_LOG_I("PPG CP Mode\n");
            fat_dat.info.sub_mode = CP_STEP_PPG; /* PPG小板 */
#ifdef IGS_DEV
            //set_live_wear_switch(0); // 关闭活体检测
            char buffer[10] = {0};
            #ifdef IGS_DEV
            snprintf(buffer, sizeof(buffer), "%08X", get_pacb_check_result()); //只保留自检项
            #endif
            generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, (const uint8_t*)buffer, (uint8_t)strlen(buffer), response, response_len);
            return;
#endif
        }
    }
    else
    {
        if (value == STEP_CP) {
            // 设置为CP陪测板模式
            FACTORY_LOG_I("CP Mode\n");
            fat_dat.info.mode = STEP_CP;   /* 组件来料测试 */
        }
        else if (value == STEP_PCBA) {
            // 设置为PCBA单板测试模式
            FACTORY_LOG_I("PCBA Mode\n");
            fat_dat.info.mode = STEP_PCBA;  /* PCBA单板测试 */
        }
        else if (value == STEP_SFG) {
            // 设置为半成品测试模式
            FACTORY_LOG_I("SFG Mode\n");
            fat_dat.info.mode = STEP_SFG;   /* 半成品测试 */
        }
        else if (value == STEP_FG) {
            // 设置为成品测试模式
            FACTORY_LOG_I("FG Mode\n");
            fat_dat.info.mode = STEP_FG;    /* 成品测试 */
        }else if (value == STEP_OLD) {
            // 设置为老化测试模式
            FACTORY_LOG_I("OLD Mode\n");
            fat_dat.info.mode = STEP_OLD;   /* 老化测试 */
        }else if (value == STEP_PK) {
            // 设置为出厂测试模式
            FACTORY_LOG_I("PK Mode\n");
            fat_dat.info.mode = STEP_PK;    /* 出厂测试 */
        }else {
            status = AT_STATUS_FAIL;
        }

        // 产测模式
        if(fat_dat.info.mode > STEP_CP){
            // TODO: 发送设备信息
            char buffer[240] = {0};
        #ifdef IGS_DEV
            buffer[0] = ','; // 分割符
            // 设备SN
            #ifdef USE_SN_OLD
            snprintf(&buffer[1], sizeof(buffer), "%x%x", get_serial_number_high(), get_serial_number_low());
            #else
            //generate_sn_str(&buffer[1], get_serial_product_type(), get_serial_number_low());
            snprintf(&buffer[1], 11, "%s", factory_data_get_sn());
            #endif
            // 设备MAC地址
            uint8_t ble_mac[6] = {0};
            g_device_get_ble_mac_addr(ble_mac, 6);
            snprintf(buffer+strlen(buffer), 13 + 1, ",%02X%02X%02X%02X%02X%02X", ble_mac[5], ble_mac[4], ble_mac[3], ble_mac[2], ble_mac[1], ble_mac[0]);

            // 设备名称

            char name_temp[CFG_NAME_LEN] = {0};
            get_product_name(name_temp, CFG_NAME_LEN);
            snprintf(buffer+strlen(buffer), CFG_NAME_LEN + 1, ",%s", name_temp);
            // 设备ID = 厂商ID - 设备型号
            snprintf(buffer+strlen(buffer), 9 + 1, ",%04X%04X", get_manufacturer_id(), get_product_id());
            // 设备BLE固件版本
            uint16_t data = get_ble_soc_version();
            snprintf(buffer+strlen(buffer), 7 + 1, ",V %d.%02d", data / 100, data % 100);
            // 设备GPS固件版本
            data = get_gps_soc_version();
            snprintf(buffer+strlen(buffer), 7 + 1, ",V %d.%02d", data / 100, data % 100);
            // 设备软件版本
            data = get_major_app_version();
            snprintf(buffer+strlen(buffer), 7 + 1, ",V %d.%02d", data / 100, data % 100);
            // 设备硬件版本
            data = get_hardware_version();
            snprintf(buffer+strlen(buffer), 7 + 1, ",V %d.%02d", data / 100, data % 100);
            // BOOT版本号
            char boot_version[40]={0};//BOOT版本号
            get_boot_version_str(boot_version);
            snprintf(buffer+strlen(buffer), sizeof(boot_version) + 1, ",%s", boot_version);

            // 设备编译日期
            char build_time[40]={0};  //构建信息
            snprintf(build_time, sizeof(build_time), "%s-%s",get_firmware_build_date(), get_firmware_build_time());
            snprintf(buffer+strlen(buffer), sizeof(build_time) + 1, ",%s", build_time);
            // 设备UUID
            char uuid_str[33]={0};    //设备UUID HEX字符串
            ALIGN(4) uint8_t uid[16] = {0}; //设备UUID
            HAL_EFUSE_Read(0, uid, 16);
            snprintf(uuid_str, sizeof(uuid_str), "%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X",uid[0],uid[1],uid[2],uid[3],uid[4],uid[5],uid[6],uid[7],uid[8],uid[9],uid[10],uid[11],uid[12],uid[13],uid[14],uid[15]);
            snprintf(buffer+strlen(buffer), sizeof(uuid_str) + 1, ",%s", uuid_str);
        #endif
            generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, (const uint8_t*)buffer, (uint8_t)strlen(buffer), response, response_len);
            return;
        }
    }
end:
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, NULL, 0, response, response_len);
}


//PPG
static uint8_t ppg_test_live_wear_switch = 0; // 记录产测模式PPG活体检测设置
static bool ppg_test_on = false;     // 记录产测模式PPG的开启状态
static void production_test_ppg(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    FACTORY_LOG_I("ppg\n");
    if (cmd->data_count == 0) {
        if (strcmp(cmd->cmd, "START") == 0)
        {
            // 半成品/成品测试跳转到测试界面
            fat_dat.info.item = ITEM_PPG;
            if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
                submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_page_list[FAT_MENU_TYPE_QRCODE]);

            // 创建命令上下文
            cmd_context_t *context = cmd_context_create(&g_cmd_context_manager, cmd, response, response_len);
            if (context == NULL) {
                status = AT_STATUS_FAIL;
                goto end;
            }
#ifdef IGS_DEV

#endif
        }
        else if (strcmp(cmd->cmd, "STOP") == 0)
        {
#ifdef IGS_DEV

#endif
            // 释放当前命令上下文
            cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
            if (context) {
                cmd_context_release(&g_cmd_context_manager, context);
            }

            // 半成品/成品测试跳转到工模界面
            if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
                submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_main_page);
            }
    }
    else
    {
        // 开启PPG陪测测试
        if((fat_dat.info.mode == STEP_CP)&&(fat_dat.info.sub_mode == CP_STEP_PPG))
        {
        #ifdef IGS_DEV
            ppg_test_live_wear_switch = get_live_wear_switch(); // 记录活体检测状态
            ppg_test_on = true;
            set_live_wear_switch(0); // 关闭活体检测
            service_gui_manual_measure(HEARTRATE, true);
        #endif
        }
    }
end:
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, NULL, 0, response, response_len);
}

//结束测试
static void production_test_stop(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    fat_dat.info.mode = STEP_NULL;
    fat_dat.info.busy = true;
	factory_app_link_state = false;  // 退出产测
    if(ppg_test_on)
    {
        ppg_test_on = false;
        set_live_wear_switch(ppg_test_live_wear_switch); // 恢复活体检测状态
        service_gui_manual_measure(HEARTRATE, false);
    }

    FACTORY_LOG_I("stop\n");
    // TODO: 解析保存自检结果
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, NULL, 0, response, response_len);
}

//读取/写入设备MAC地址
static void production_test_mac(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    char buffer[16] = {0};
    if (strcmp(cmd->cmd, "R") == 0)
    {
        // 读取MAC序列号
        // 封装MAC成12位HEX字符串
        uint8_t ble_addr[6] = {0};
        #ifdef IGS_DEV
            g_device_get_ble_mac_addr(ble_addr, 6);
            snprintf(buffer, sizeof(buffer), "%02X%02X%02X%02X%02X%02X", ble_addr[5], ble_addr[4], ble_addr[3], ble_addr[2], ble_addr[1], ble_addr[0]);
        #endif
    }
    else if (strcmp(cmd->cmd, "W") == 0)
    {
        // 当前固定使用NRF52832的MAC地址，不可写入MAC
    }
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, (const uint8_t*)buffer, (uint8_t)strlen(buffer), response, response_len);
}

//读取/写入设备SN序列号
static void production_test_sn(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    char buffer[12] = {0};
    if (strcmp(cmd->cmd, "R") == 0)
    {
        // 读取SN序列号
        // 封装10位SN字符串
    #ifdef IGS_DEV
        #ifdef USE_SN_OLD
        snprintf(buffer, sizeof(buffer), "%x%x", get_serial_number_high(), get_serial_number_low());
        #else
        //generate_sn_str(buffer, get_serial_product_type(), get_serial_number_low());
        snprintf(buffer, 11, "%s", factory_data_get_sn());
        #endif
    #endif
    }
    else if (strcmp(cmd->cmd, "W") == 0)
    {
        // 将数据写入FLASH
        if(factory_data_write((char*)cmd->data) != 0)
        {
            status = AT_STATUS_FAIL;
        }
        // 写入SN序列号到KV:前3位屏蔽
    #ifdef IGS_DEV
        // if(app_write_factory_sn_to_kv((char*)cmd->data) != 0)
        // {
        //     status = AT_STATUS_FAIL;
        // }
    #endif
    }
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, (const uint8_t*)buffer, (uint8_t)strlen(buffer), response, response_len);
}

//读取/写入设备时间
static void production_test_time(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    char buffer[16] = {0}; //20000101000000
    if (strcmp(cmd->cmd, "R") == 0)
    {
        // 读取时间
#ifdef IGS_DEV
        qw_tm_t tm_time;
        service_datetime_get(&tm_time);
        int year = tm_time.tm_year;
        int month = tm_time.tm_mon;
        int day = tm_time.tm_mday;
        int hour = tm_time.tm_hour;
        int min = tm_time.tm_min;
        int sec = tm_time.tm_sec;
        snprintf(buffer, sizeof(buffer), "%04d%02d%02d%02d%02d%02d", year, month, day, hour, min, sec);
#endif
    }
    else if (strcmp(cmd->cmd, "W") == 0)
    {
        // TODO:写入时间
#ifdef IGS_DEV
        char tmp[20] = {0};
        sprintf(tmp, "%s", cmd->data);
        char ptr[5] = {0};
        qw_tm_t str_time_;
        memset(ptr, 0, sizeof(ptr));
        memcpy(ptr, &tmp[0], 4);
        str_time_.tm_year = atoi(ptr);
        memset(ptr, 0, sizeof(ptr));
        memcpy(ptr, &tmp[4], 2);
        str_time_.tm_mon = atoi(ptr);
        memset(ptr, 0, sizeof(ptr));
        memcpy(ptr, &tmp[6], 2);
        str_time_.tm_mday = atoi(ptr);
        memset(ptr, 0, sizeof(ptr));
        memcpy(ptr, &tmp[8], 2);
        str_time_.tm_hour = atoi(ptr);
        memset(ptr, 0, sizeof(ptr));
        memcpy(ptr, &tmp[10], 2);
        str_time_.tm_min = atoi(ptr);
        memset(ptr, 0, sizeof(ptr));
        memcpy(ptr, &tmp[12], 2);
        str_time_.tm_sec = atoi(ptr);
        if (str_time_.tm_year > 2000 && str_time_.tm_year < 3000 && str_time_.tm_mon > 0
            && str_time_.tm_mon < 13 && str_time_.tm_mday > 0 && str_time_.tm_mday < 32 && str_time_.tm_hour < 24
            && str_time_.tm_min < 60 && str_time_.tm_sec < 60)
        {
            service_datetime_set(&str_time_);
        }
        else
        {
            status = AT_STATUS_FAIL;
        }
#endif
    }
end:
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, (const uint8_t*)buffer, (uint8_t)strlen(buffer), response, response_len);
}

static void production_test_mode(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    // TODO: 实现模式跳转
    if (cmd->data_count < 1) {
        status = AT_STATUS_FAIL;
        goto end;
    }
    uint8_t value = 0;
    if (sscanf((const char *)cmd->data, "%2hhx", &value) != 1) {
        FACTORY_LOG_E("Parse error: Invalid hex character:%s",cmd->data);
        status = AT_STATUS_FAIL;
        goto end;
    }
    uint8_t type = 0;
    if (value == 0) {
        // 跳转到正常模式
        FACTORY_LOG_I("Normal Mode\n");
        type = 0;
    } else if (value == 1) {
        // 跳转到工厂模式
        FACTORY_LOG_I("Factory Mode\n");
        type = 1;
    } else {
        status = AT_STATUS_FAIL;
    }

end:
    generate_at_response(cmd->func, cmd->cmd, (uint8_t)status, NULL, 0, response, response_len);

    rt_kprintf("Switch to app type %d...\n", type);
#ifdef IGS_DEV
    //if(boot_switch_get_app_type()!=type)
    {
        boot_switch_to_app(type);
    }
#endif
}

static void production_test_tp(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    if (strcmp(cmd->cmd, "START") == 0)
    {
        // 创建命令上下文
        cmd_context_t *context = cmd_context_create(&g_cmd_context_manager, cmd, response, response_len);
        if (context == NULL) {
            status = AT_STATUS_FAIL;
            goto end;
        }

        //tp_module_register_callback(tp_test_callback);
    }
    else if (strcmp(cmd->cmd, "STOP") == 0)
    {
        //tp_module_register_callback(NULL);
        // 释放当前命令上下文
        cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
        if (context) {
            cmd_context_release(&g_cmd_context_manager, context);
        }
    }
end:
    generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
}

static void production_test_ble(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    if (strcmp(cmd->cmd, "START") == 0)
    {
        // 半成品/成品测试跳转到测试界面
        fat_dat.info.item = ITEM_BLE;
        if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
            submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_page_list[FAT_MENU_TYPE_ANT]);

        // 创建命令上下文
        cmd_context_t *context = cmd_context_create(&g_cmd_context_manager, cmd, response, response_len);
        if (context == NULL) {
            status = AT_STATUS_FAIL;
            goto end;
        }

        //ble_module_register_callback(ble_test_callback);
    }
    else if (strcmp(cmd->cmd, "STOP") == 0)
    {
        //ble_module_register_callback(NULL);
        // 释放当前命令上下文
        cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
        if (context) {
            cmd_context_release(&g_cmd_context_manager, context);
        }

        // 半成品/成品测试跳转到工模界面
        if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
            submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_main_page);
    }
end:
    generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
}

static void production_test_gps(at_command_t *cmd, uint8_t *response, uint16_t *response_len)
{
    uint8_t status = AT_STATUS_SUCCESS;
    if (strcmp(cmd->cmd, "START") == 0)
    {
        // 半成品/成品测试跳转到测试界面
        fat_dat.info.item = ITEM_GPS;
        if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
            submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_page_list[FAT_MENU_TYPE_GPS]);

        // 创建命令上下文
        cmd_context_t *context = cmd_context_create(&g_cmd_context_manager, cmd, response, response_len);
        if (context == NULL) {
            status = AT_STATUS_FAIL;
            goto end;
        }

        //gps_module_register_callback(gps_test_callback);
    }
    else if (strcmp(cmd->cmd, "STOP") == 0)
    {
        //gps_module_register_callback(NULL);
        // 释放当前命令上下文
        cmd_context_t *context = cmd_context_get_current(&g_cmd_context_manager);
        if (context) {
            cmd_context_release(&g_cmd_context_manager, context);
        }

        // 半成品/成品测试跳转到工模界面
        if((fat_dat.info.mode == STEP_SFG)||(fat_dat.info.mode == STEP_FG))
            submit_gui_event(GUI_EVT_SERVICE_PAGE_PUSH, 0,  (void *)g_factory_main_page);
    }
end:
    generate_at_response(cmd->func, cmd->cmd, status, NULL, 0, response, response_len);
}

// 产线测试函数表
const production_test_entry_t production_test_table[] = {
    {"BEEP", production_test_beep},
    {"MOTOR", production_test_motor},
    {"KNOB", production_test_knob},
    {"LIGHT", production_test_light},
    {"BAR", production_test_bar},
    {"ATT", production_test_att},
    {"MAG", production_test_mag},
    {"KEY", production_test_key},
    {"LCD", production_test_lcd},
    {"UPAN", production_test_upan},
    {"PPG", production_test_ppg},     // 支持PPG陪测板
    {"BAT", production_test_bat},
    {"TP", production_test_tp},
    {"BLE", production_test_ble},
    {"GPS", production_test_gps},
    {"READY", production_test_ready},
    {"START", production_test_start},
    {"STOP", production_test_stop},
    {"MAC", production_test_mac},
    {"SN", production_test_sn},
    {"TIME", production_test_time},
    {"DATA", production_test_data},
    {"MODE", production_test_mode},
    {"SHUTDOWN", production_test_shutdown},
    {"REBOOT", production_test_reboot},
    {"BURN", burn_test_entry},
    {NULL, NULL}  // 结束标记
};
