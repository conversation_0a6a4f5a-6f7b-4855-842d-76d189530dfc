/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   MsgBoxService.cpp
@Time    :   2024/12/13 16:08:06
*
**************************************************************************/

#include <new>
#include <stdbool.h>
#include <stdint.h>
#include <string.h>

#include "../basic_app_module/backlight_module/backlight_module.h"
#include "../basic_app_module/focus_mode_srv/focus_mode_srv.h"
#include "MsgBoxDataDef.h"
#include "MsgBoxService.h"
#include "MsgBoxUnion.h"
#include "gui_event_service.h"
#include "qw_os_gui.h"
#include "remind_response_app/remind_response_app.h"
#include "watch_lock_srv/watch_lock_srv.h"
#include "CreateClassInUnion.hpp"

#ifndef SIMULATOR
#include "gui_thread.h"
#endif   // !1 SIMULATOR

#include "radio/application/ble_test_module/button_debug_cmd.h"
#if !defined(SIMULATOR) && defined(BUTTON_DEBUG_MODE) && (BUTTON_DEBUG_MODE == 1)
#include "radio/application/ble_test_module/ui_debug_cmd.h"
#endif

static QwMsgList *p_msg_list = nullptr;
static void *p_evt_manager = nullptr;
static bool g_msg_running = false;

#define MSGBOX_HEAP_NUM 3
static MsgBoxUnion msg_box_heap_[MSGBOX_HEAP_NUM];   // 弹窗联合体

extern "C" {
extern QwMsgPara_t msg_para_list[enumPOPUP_MAX];
}

QwMsgBase *msg_msgbox_new_popout(QwMsgInfo_t *msg_info, ANIMAT_TYPE *slider_type);

/**
 * @brief 弹窗模块初始化, 输入控制结构
 * @param msg_list QwMsgList对象类型
 * @param evt_manager 传入的pagemanager对象
 */

/**
 * @brief 初始化消息框服务
 *
 * @param msg_list 消息列表对象指针
 * @param evt_manager 事件管理器指针
 *
 * 初始化消息框服务，设置消息列表和事件管理器。
 * 如果消息列表有效，会调用其init()方法并设置消息弹出回调函数。
 */
void msg_msgbox_init(void *msg_list, void *evt_manager)
{
    p_msg_list = static_cast<QwMsgList *>(msg_list);
    if (p_msg_list != nullptr)
    {
        p_msg_list->init();
        p_msg_list->set_get_prepare_msg_callback(&msg_msgbox_new_popout);
    }

    p_evt_manager = evt_manager;
}

static void set_msg_timeout(uint16_t type, QwMsgBase *msgbox)
{
    if (msgbox == nullptr)
    {
        return;
    }

    PAGE_TYPE tmp = msgbox->get_msg_info().msg_page_type;
    int time_tick_ = INT_MAX;
    switch (tmp)
    {
    case MSG_TYPE_TOAST:
        time_tick_ = MSG_TOAST_TIP_TIME;
        break;
    case MSG_TYPE_HALF_MESSAGE:
        time_tick_ = MSG_HALF_MESSAGE_SCREEN_TIME;
        break;
    case MSG_TYPE_HALF_TIP:
        time_tick_ = MSG_HALF_SCREEN_TIME;
        break;
    case MSG_TYPE_FULL_TIP:
        time_tick_ = MSG_FULL_SCREEN_TIME;
        break;
    case MSG_TYPE_BOTTOM_TOAST:
        time_tick_ = MSG_BOTTOM_TOAST_TIP_TIME;
        break;
    case MSG_TYPE_FULL_ENTER:
    case MSG_TYPE_FULL_SELECT:
    case MSG_TYPE_OTHER:
        time_tick_ = INT_MAX;
        break;
    default:
        break;
    }

    //特殊弹窗处理
    switch (type)
    {
    case enumPOPUP_ACH_UPDATE:
        time_tick_ = MSG_FULL_SCREEN_TIME;
        break;
    case enumPOPUP_INNER_TRAIN_TIP:
        time_tick_ = MSG_BOTTOM_TOAST_TIP_TIME;
        break;
    case enumPOPUP_ALARM_CLOCK:
        time_tick_ = MSG_ALARM_CLOCK_TIME;
        break;
    case enumPOPUP_INCOMING_CALL:
    case enumPOPUP_BIND_WATCH_FAILED:
    case enumPOPUP_BIND_WATCH_SUCC:
    case enumPOPUP_UNDER_VOLTAGE_OFF:
    case enumPOPUP_LOADING:
    case enumPOPUP_SAVE_RECORD:
    case enumPOPUP_POWER_SAVE_UNLOCK:
    case enumPOPUP_SPORT_OUT_COUNT_DOWN:
    case enumPOPUP_WATCH_UNLOCK:
    case enumPOPUP_SPORT_START_COUNT_DOWN:
        time_tick_ = INT_MAX;
        break;
    case enumPOPUP_NAVI_CHANGE_DIR:
        if (msgbox->get_msg_info().user_data)
        {
            if (static_cast<qw_msg_bottom_toast_info_t *>(msgbox->get_msg_info().user_data)->time_flag_ == false)
            {
                time_tick_ = INT_MAX;
            }
        }
        break;
    case enumPOPUP_FIND_WATCH:
        time_tick_ = MSG_FIND_WATCH_TIME;
        break;
    default:
        break;
    }

    msgbox->get_msg_info().time = time_tick_;
}

static void get_msg_close_animate_type(ANIMAT_TYPE *type, QwMsgBase *msgbox)
{
    if (msgbox == nullptr)
    {
        return;
    }

    PAGE_TYPE tmp = msgbox->get_msg_info().msg_page_type;
    ANIMAT_TYPE anim_type = ANIMAT_TYPE::NONE;
    switch (tmp)
    {
    case MSG_TYPE_TOAST:
    case MSG_TYPE_FULL_TIP:
    case MSG_TYPE_FULL_ENTER:
    case MSG_TYPE_FULL_SELECT:
    case MSG_TYPE_OTHER:
        anim_type = ANIMAT_TYPE::FADE;
        break;
    case MSG_TYPE_HALF_MESSAGE:
    case MSG_TYPE_HALF_TIP:
        anim_type = ANIMAT_TYPE::NORTH;
        break;
    case MSG_TYPE_BOTTOM_TOAST:
        anim_type = ANIMAT_TYPE::SOUTH;
        break;

    default:
        break;
    }

#ifndef SIMULATOR
    if (get_gui_runing_state() != GUI_POWER_MODE::GUI_NORMAL)
    {
        anim_type = ANIMAT_TYPE::NONE;
    }
#endif

    *type = anim_type;
}

//勿扰、睡眠、省电下不允许弹出的弹窗
static GUI_MSGBOX msg_dis_pop_in_dnd_list[] = {
    /*
就寝提醒，
久坐提醒，
喝水提醒，
达标提醒，
数据同步提醒，
舒压提醒，
心率过高过低提醒，
消息提醒，
    */
    enumPOPUP_SLEEP_BED,
    enumPOPUP_HEALTH_SEDENTARY,
    enumPOPUP_DRINK_WATER,
    enumPOPUP_HEALTH_GOAL,
    // 数据同步提醒 有需求 未开始
    enumPOPUP_PRESSURE_REMIND,
    enumPOPUP_HEART_RATE_NOTICE,
    enumPOPUP_INTELLIGENT_NOTIFY,
};

// 勿扰/睡眠/省电 检查
//ret bool 是否可以弹出
static bool msg_allow_pop_in_dnd(GUI_MSGBOX msg_type)
{
    bool dnd_state = get_dnd_state(true);
    bool sleep_state = get_sleep_state(true);
    int power_save = get_power_save_setting();

    if (dnd_state || sleep_state || (power_save != POWER_SAVE_NO))
    {
        for (int i = 0; i < sizeof(msg_dis_pop_in_dnd_list) / sizeof(msg_dis_pop_in_dnd_list[0]); i++)
        {
            if (msg_type == msg_dis_pop_in_dnd_list[i])
            {
                return false;
            }
        }
    }
    return true;
}

/**
 * @brief 重新评估消息优先级-处理特殊情况
 *
 * @param msg_id 消息ID
 * @param msg_para 消息配置参数
 */
static void reevaluate_priority(uint16_t msg_id, QwMsgPara_t *msg_para)
{
    if(p_msg_list)
    {
        // 当灭屏时有智能提醒时，充电亮屏提醒优先级降低
        if(p_msg_list->get_cur_msg() &&
            p_msg_list->get_cur_msg()->get_msg_info().id == enumPOPUP_INTELLIGENT_NOTIFY &&
#ifndef SIMULATOR
            get_gui_runing_state() == GUI_POWER_MODE::GUI_SLEEP &&
#endif
            msg_id == enumPOPUP_CHARGE_REMINDER )
        {
            msg_para->priority = MSG_PRIORITY_6;
            backlight_open_app();
        }
    }
}

/**
 * @brief 处理GUI弹窗消息
 *
 * @param type 弹窗类型（包含标志位）
 * @param data 弹窗关联数据
 *
 * @details
 * 1. 处理弹窗关闭逻辑（enumPOPUP_CLOSE）：
 *    - 根据data参数判断具体关闭哪种弹窗
 *    - 执行关闭动画效果
 * 2. 处理超时关闭逻辑（enumPOPUP_TIMEOUT_CLOSE）
 * 3. 处理新弹窗弹出逻辑（当系统允许弹窗时）
 *
 * @note 仅在IMG_TESTER_ENABLE!=1时生效
 */
void msg_gui_popup_msgbox(uint16_t type, void *data)
{
    // 将传入的type参数与0x7fff进行按位与运算,去除最高位的标志位,得到实际的弹窗类型
    GUI_MSGBOX msg_type = (GUI_MSGBOX) (type & 0x7fff);


    // 检查运行状态，对于非特例弹窗，如果未启用则直接返回
    if (p_msg_list == nullptr)
    {
        return;
    }

    if (msg_type == enumPOPUP_CLOSE)
    {
        int msg_id = 0;
        // 优先处理弹窗关闭
        if ((int) data == LOADING_FAILED)   //加载失败
        {
            if (p_msg_list->get_cur_msg() != nullptr)
            {
                p_msg_list->get_cur_msg()->get_msg_info().user_data = NULL;
            }
            msg_id = enumPOPUP_LOADING;
        }
        else if ((int) data == LOADING_SUCCESS)
        {
            msg_id = enumPOPUP_LOADING;
        }
        else if ((int) data < enumPOPUP_MAX)   //加载成功
        {
            msg_id = (int) data;
        }
        else
        {
            GUI_VIEW_LOG_E("[%s] msg_id =  %d", __FUNCTION__, (int)data);
            RT_ASSERT(false && "[msg_gui_popup_msgbox] msg_id is error");
            return;
        }

        ANIMAT_TYPE anim_type = ANIMAT_TYPE::NONE;
        QwMsgBase *msg_cur_box = p_msg_list->get_cur_msg();
        if (msg_cur_box && p_msg_list->get_slider_busy() == false && msg_cur_box->get_msg_info().id == msg_id)
        {
            get_msg_close_animate_type(&anim_type, msg_cur_box);
        }

        p_msg_list->external_initiative_close(msg_id, (uint16_t) anim_type);   // 关闭当前弹窗
    }
    else if (msg_type == enumPOPUP_TIMEOUT_CLOSE)
    {
        QwMsgBase *msg_cur_box = p_msg_list->get_cur_msg();
        ANIMAT_TYPE anim_type = ANIMAT_TYPE::NONE;
        if (msg_cur_box && p_msg_list->get_slider_busy() == false)
        {
            get_msg_close_animate_type(&anim_type, msg_cur_box);

            p_msg_list->timeout_close((uint8_t) anim_type);   // 关闭当前弹窗
        }
    }
    else if (g_msg_running && msg_allow_pop_in_dnd(msg_type))
    {
        if (msg_type < enumPOPUP_MAX)
        {
            QwMsgPara_t msg_para;
            memcpy(&msg_para, &msg_para_list[msg_type], sizeof(QwMsgPara_t));
            reevaluate_priority(msg_type, &msg_para);//动态调整弹窗优先级

            msg_para.user_data = data;
            p_msg_list->prepare_popout(msg_type, &msg_para);   // 准备弹窗
        }
    }
    else
    {

    }
}

QwMsgBase *msg_msgbox_new_popout(QwMsgInfo_t *msg_info, ANIMAT_TYPE *slider_type)
{
    if (msg_info == nullptr || slider_type == nullptr)
    {
        return nullptr;
    }

    QwMsgBase *msgbox = nullptr;
    ANIMAT_TYPE anim_type = ANIMAT_TYPE::FADE;

    QwMsgBase *msg_cur_box = p_msg_list->get_cur_msg();
    QwMsgBase *msg_prepare_box = p_msg_list->get_prepare_msg();
    uint8_t index = MSGBOX_HEAP_NUM;
    for (uint8_t i = 0; i < MSGBOX_HEAP_NUM; i++)
    {
        QwMsgBase *tmp = reinterpret_cast<QwMsgBase *>(&msg_box_heap_[i]);
        if (tmp != msg_cur_box && tmp != msg_prepare_box)
        {
            index = i;
            break;
        }
    }
    if (index >= MSGBOX_HEAP_NUM)
    {
        assert(false && "[msg_gui_popup_msgbox] useing msg_box_heap_ index is error");
        return nullptr;
    }
    memset(static_cast<void *>(&msg_box_heap_[index]), 0, sizeof(MsgBoxUnion));

    GUI_MSGBOX msg_type = (GUI_MSGBOX) (msg_info->id & 0x7fff);
    switch (msg_type)
    {
#ifdef MSGBOX_TEST
    case enumPOPUP_TEST1:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgGiveUpSport>(&msg_box_heap_[index])); MsgGiveUpSport();
        break;
    case enumPOPUP_TEST2:
        msg_info->id = enumPOPUP_DND_TIP;
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgDND>(&msg_box_heap_[index])); MsgDND();
        break;
    case enumPOPUP_TEST3:
        msg_info->id = enumPOPUP_SENSOR_CONNECT_NOT_IN_MOTION;
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgSensorStateToast>(&msg_box_heap_[index], enumPOPUP_SENSOR_CONNECT_NOT_IN_MOTION));
        break;
    case enumPOPUP_TEST4:
        msg_info->id = enumPOPUP_SPORTING_GPS_LOSS;
        anim_type = ANIMAT_TYPE::NORTH;
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgSportingGpsState>(&msg_box_heap_[index], enumPOPUP_SPORTING_GPS_LOSS));
        break;
#endif
    case enumPOPUP_ALARM_CLOCK:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgAlarmTip>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_TIMER_CLOCK:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgTimerEnd>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_HEALTH_GOAL:
    case enumPOPUP_HEALTH_SEDENTARY:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgHealthGoal>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_TOAST_TIP:
    {
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgToastTip>(&msg_box_heap_[index], msg_type));
        MsgToastTip_Info_t t = *static_cast<MsgToastTip_Info_t *>(msg_info->user_data);
        if (t.type == TOAST_TIP_GENERAL_WATCH_UNLOCK || t.type == TOAST_TIP_POWER_SAVE_MODE_EXIT)
        {
            msgbox->get_msg_info().msg = strdup("MsgToastTip_unlock");
        }
        else
        {
            msgbox->get_msg_info().msg = strdup("MsgToastTip");
        }
    }
    break;
    case enumPOPUP_SENSOR_CONNECT_NOT_IN_MOTION:
    case enumPOPUP_SENSOR_DISCONNECT_NOT_IN_MOTION:
    case enumPOPUP_SENSOR_CONNECT_IN_MOTION:
    case enumPOPUP_SENSOR_DISCONNECT_IN_MOTION:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgSensorStateToast>(&msg_box_heap_[index], msg_type));
        break;
    case enumPOPUP_SPORT_REMIND:
        anim_type = ANIMAT_TYPE::NORTH;
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgSportRemind>(&msg_box_heap_[index], msg_type));
        break;
    case enumPOPUP_GIVE_UP_SPORT:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgGiveUpSport>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_POWER_SAVE:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgPowerSave>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_POWER_SAVE_UNLOCK:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgPowerSaveUnlock>(&msg_box_heap_[index]));
        anim_type = ANIMAT_TYPE::NONE;
        break;
    case enumPOPUP_SPORTING_LAP:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgSportingLap>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_CHARGE_REMINDER:
        // anim_type = ANIMAT_TYPE::NONE;
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgChargeRing>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_UNDER_VOLTAGE_TEN:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgUnderVolTen>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_UNDER_VOLTAGE_TWENTY:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgUnderVolTwenty>(&msg_box_heap_[index], msg_type));
        anim_type = ANIMAT_TYPE::NORTH;
        break;
    case enumPOPUP_UNDER_VOLTAGE_OFF:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgUnderVolOff>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_WATCH_UNLOCK:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgUnlock>(&msg_box_heap_[index]));
        anim_type = ANIMAT_TYPE::NONE;
        break;
    case enumPOPUP_SAVE_RECORD:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgSaveSport>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_ALIPAY:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgAlipay>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_BPWR_CALIB:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgBpwrCalib>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_BPWR_CALIB_RESULT:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgBpwrCalibResult>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_REMOVE_SENSOR:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgRemoveSensor>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_SPORT_OUT_COUNT_DOWN:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgSportOutCountDown>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_SPORT_NO_GPS:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgSportGpsTip>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_RECONNECT_SENSOR:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgReconnectSensor>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_SENSOR_LOW_POWER:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgSensorLowPower>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_SENSOR_CONN_REACHED_LIMIT:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgSensorConnReachedLimit>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_ALTIMETER_ICON:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgAltimterIcon>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_ALTIMETER_ACL:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgAltimterAcl>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_HEART_RATE:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgHeartRate>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_HEART_RATE_NOTICE:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgHeartRateNotice>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_AOD_TIP:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgAOD>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_DECODE_ERR:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgDecodeERR>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_NAVI_BACK:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgNaviBack>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_DND_TIP:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgDND>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_DELETE_FIT:
    case enumPOPUP_MODIFY_WHEEL_PERIMETER:
    case enumPOPUP_FTP_TRAIN_COURSE:
    case enumPOPUP_SENSOR_CONNECT_FEC_POWER_TIP:
    case enumPOPUP_UNBIND:
    case enumPOPUP_FACTORY_RESET_ID_CHANGE:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgTipSelect>(&msg_box_heap_[index], msg_type));
        break;
    case enumPOPUP_LOADING:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgLoading>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_SPO2:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgSpo2>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_PRESSURE:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgPressure>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_PRESSURE_REMIND:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgPressureRemind>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_POWER_CONSUME_REMIND:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgPowerReminder>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_ALARM_REMINDER:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgAlarmReminder>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_VRB_BACKWARD:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgVrbBackward>(&msg_box_heap_[index], msg_type));
        break;
    case enumPOPUP_SLEEP_BED:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgSleepBed>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_DELETE_TRAINING_COURSE:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgDelTrainingCourse>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_TRIATHLON_RESET:
    case enumPOPUP_TRIATHLON_CHANGE:
    case enumPOPUP_TRIATHLON_NEXT:
    case enumPOPUP_TRIATHLON_COMPLETED:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgTriathlonTip>(&msg_box_heap_[index], msg_type));
        break;
    case enumPOPUP_FEC_NOT_CON:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgFecNotCon>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_INNER_TRAIN_TIP:
    case enumPOPUP_TRAIN_COMPLETED:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsginnerTraintip>(&msg_box_heap_[index]));
        anim_type = ANIMAT_TYPE::NONE;
        break;
    case enumPOPUP_BIND_WATCH_SUCC:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgBindWatchSuccess>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_UNBIND_WATCH_APP:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgUnbindWatch>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_BIND_WATCH_FAILED:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgBindWatchFail>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_DELETE_NAVIGATION:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgDelNavi>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_SPORTING_GPS_LOCATE:
    case enumPOPUP_SPORTING_GPS_LOSS:
        anim_type = ANIMAT_TYPE::NORTH;
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgSportingGpsState>(&msg_box_heap_[index], msg_type));
        break;
    case enumPOPUP_NAVI_START:
    case enumPOPUP_NAVI_YAW:
    case enumPOPUP_NAVI_RESTORE_ROUTE:
    case enumPOPUP_NAVI_END:
    case enumPOPUP_NAVI_REMAINING_CLIMB:
    case enumPOPUP_NAVI_CHANGE_DIR:
        anim_type = ANIMAT_TYPE::SOUTH;
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, QwMsgBottomToast>(&msg_box_heap_[index], msg_type));
        break;
    case enumPOPUP_INTELLIGENT_NOTIFY:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgIntelligentNotify>(&msg_box_heap_[index], msg_type));
        break;
    case enumPOPUP_INCOMING_CALL:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgIncomingCall>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_ACH_RESET:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgAchReset>(&msg_box_heap_[index], msg_type));
        break;
    case enumPOPUP_ACH_UPDATE:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgAchUpdate>(&msg_box_heap_[index], msg_type));
        break;
    case enumPOPUP_FTP_UPDATE:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgFtpUpdate>(&msg_box_heap_[index], msg_type));
        break;
    case enumPOPUP_LT_HR_UPDATE:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgLtHrUpdate>(&msg_box_heap_[index], msg_type));
        break;
    case enumPOPUP_MAX_HR_UPDATE:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgMaxHrUpdate>(&msg_box_heap_[index], msg_type));
        break;
    case enumPOPUP_RESET:
    case enumPOPUP_RESTORE_FACTORY:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgReset>(&msg_box_heap_[index], msg_type));
        break;
    case enumPOPUP_FTP_TEST_REMINDER:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgFTPTestReminder>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_SPORT_START_COUNT_DOWN:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgSportStartCountDown>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_TODAY_TRAIN_TIP:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgTodayTrainTip>(&msg_box_heap_[index], msg_type));
        break;
    case enumPOPUP_OTA_FINISH_SUCCESS:
    case enumPOPUP_OTA_FINISH_FAILED:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgOTAFinish>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_ALIPAY_STATUS:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgAlipayStatus>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_ALIPAY_CONFIRM:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgAlipayConfirm>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_FIND_WATCH:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgFindWatch>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_DRINK_WATER:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgDrinkWater>(&msg_box_heap_[index]));
        break;
    case enumPOPUP_DATA_SYNC:
        msgbox = static_cast<QwMsgBase *>(create_in_union<MsgBoxUnion, MsgDataSync>(&msg_box_heap_[index], msg_type));
        break;
    default:
        break;
    }

    if (msgbox != nullptr)
    {
        QW_MSG_LIST_LOG_I("BoxService pop:%s, heap index:%d", msgbox->get_msg_info().msg, index);

#if !defined(SIMULATOR) && defined(BUTTON_DEBUG_MODE) && (BUTTON_DEBUG_MODE == 1)
        ui_report(UI_BLE_DEBUG_REPORT_TYPE_MSGBOX_IN, msgbox->get_msg_info().msg, strlen(msgbox->get_msg_info().msg) + 1);
#endif

        msg_info->msg = msgbox->get_msg_info().msg;
        memcpy(&msgbox->get_msg_info(), msg_info, sizeof(QwMsgInfo_t));
        msgbox->get_msg_info().evt_manager = p_evt_manager;
        set_msg_timeout(msg_info->id, msgbox);

        if (msg_type != enumPOPUP_INTELLIGENT_NOTIFY)
        {
            backlight_open_app();   // 激活背光
        }
        remind_trigger(msg_info->id, true);

#ifndef SIMULATOR
        if (get_gui_runing_state() != GUI_POWER_MODE::GUI_NORMAL)
        {
            anim_type = ANIMAT_TYPE::NONE;   //如果屏幕是关闭状态，则不使用动画
        }
#endif
        *slider_type = anim_type;   //设置滑动类型

        return msgbox;
    }
    else
    {
        assert(0 && "msgbox is null");
    }

    return nullptr;
}

/**
 * @brief 弹窗可用性, 初始化时为不可用
 * @param enable 可用性
 */
void msg_msgbox_enable(bool enable)
{
    g_msg_running = enable;
}

//设置 p_msg_list 弹窗对象的场景
void msg_msgbox_set_p_msg_list_scope(uint16_t scope)
{
    p_msg_list->set_scope(static_cast<MSG_SCOPE>(scope));
}

#ifdef MSGBOX_TEST
static int msg_test(int argc, char **argv)
{
    if (argc < 2)
    {
        QW_MSG_LIST_LOG_D("msg_test argc error");
    }

    for (int i = 1; i < argc; i++)
    {
        int tmp = atoi(argv[i]);
        switch (tmp)
        {
        case 1:
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_TEST1, NULL);
            break;
        case 2:
        {
            static FULL_TIP_TYPE type_ = FULL_TIP_TYPE::TIP_DND;
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_TEST2, &type_);
        }
        break;
        case 3:
        {
            static sensor_search_infor_t sensor_search_infor;
            sensor_search_infor.sensor_type = SENSOR_TYPE_HRM;
            sensor_search_infor.sensor_work_state = SENSOR_WORK_STATE_CONNECTED;
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_TEST3, &sensor_search_infor);
        }
        break;
        case 4:
            submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_TEST4, NULL);
            break;
        case 5:
            backlight_close_app();
            break;
        default:
            break;
        }
    }

    return 0;
}

MSH_CMD_EXPORT(msg_test, msg_test);
#endif   // MSGBOX_TEST
