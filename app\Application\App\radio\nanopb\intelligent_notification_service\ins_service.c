/***************************************Copyright (c)****************************************/
//                              <PERSON>han <PERSON> Technology Co., Ltd
//
//---------------------------------------File Info--------------------------------------------
// File name         : ins.c
// Created by        : jiangzhen
// Descriptions      : 智能通知模块.c文件
//--------------------------------------------------------------------------------------------
// History           :
// 2019-04-06        :原始版本
/*********************************************************************************************/
#include "ble_cmd_response.h"
#include "ble_cmd_common.h"
#include "ble_nus_srv.h"

#include "pb.h"
#include "pb_encode.h"
#include "pb_decode.h"
#include "pb_decode_common.h"
#include "pb_encode_common.h"
#include "ble_peripheral.h"

#include "ins_service.h"
#include "ins_common.h"
#include "ins.pb.h"

#include "crc8.h"
#include "igs_global.h"
#include "notify_service_data_inf.h"
#include "ins_app_list.h"
#include "basictype.h"

#include "gui_event_service.h"

static void utf8_safe_strncpy(uint8_t *dest, const uint8_t *src, size_t dest_size)
{
    if (dest == NULL || src == NULL || dest_size == 0)
    {
        return;
    }

    size_t src_len = strlen((char *)src);
    
    // 当源字符串长度接近或超过缓冲区限制时，需要特殊处理
    if (src_len >= dest_size - 1)
    {
        // 为省略号"..."和结束符预留空间，只复制到dest_size-4
        size_t copy_len = dest_size - 4;
        
        // 复制部分内容
        memcpy(dest, src, copy_len);
        
        // 确保不会截断多字节UTF-8字符
        int last_char_start = copy_len - 1;
        // 从后向前查找，跳过UTF-8续字节(10xxxxxx)
        while (last_char_start >= 0 && (dest[last_char_start] & 0xC0) == 0x80)
        {
            last_char_start--; // 移动到前一个字节
        }
        
        // 如果找到了一个多字节字符的开始，检查它是否完整
        if (last_char_start >= 0)
        {
            unsigned char start_byte = dest[last_char_start];
            int expected_len = 0;
            
            if ((start_byte & 0x80) == 0x00) { // 单字节ASCII (0xxxxxxx)
                expected_len = 1;
            } else if ((start_byte & 0xE0) == 0xC0) { // 双字节UTF-8 (110xxxxx)
                expected_len = 2;
            } else if ((start_byte & 0xF0) == 0xE0) { // 三字节UTF-8 (1110xxxx)
                expected_len = 3;
            } else if ((start_byte & 0xF8) == 0xF0) { // 四字节UTF-8 (11110xxx)
                expected_len = 4;
            } else {
                // 无效的UTF-8字节
                dest[last_char_start] = '\0';
                return;
            }
            
            // 如果字符不完整，回退到上一个完整字符位置
            if (last_char_start + expected_len > copy_len)
            {
                copy_len = last_char_start;
            }
        }
        
        // 添加省略号和结束符
        dest[copy_len] = '.';
        dest[copy_len + 1] = '.';
        dest[copy_len + 2] = '.';
        dest[copy_len + 3] = '\0';
    }
    else
    {
        // 对于短字符串，使用原来的复制逻辑
        size_t copy_len = 0;
        while (copy_len < dest_size - 1 && src[copy_len] != '\0')
        {
            dest[copy_len] = src[copy_len];
            copy_len++;
        }
        dest[copy_len] = '\0';

        // 检查并移除末尾不完整的UTF-8字符
        int len = copy_len;
        if (len > 0)
        {
            int last_char_start = len - 1;
            // 从后向前查找，跳过UTF-8续字节(10xxxxxx)
            while (last_char_start >= 0 && (dest[last_char_start] & 0xC0) == 0x80)
            {
                last_char_start--; // 移动到前一个字节
            }

            // 确保我们没有超出字符串的开头
            if (last_char_start >= 0)
            {
                // 获取最后一个字符的起始字节
                unsigned char start_byte = dest[last_char_start];
                // 期望的字符长度（字节数）
                int expected_len = 0;
                if ((start_byte & 0x80) == 0x00) { // 检查是否为单字节ASCII (0xxxxxxx)
                    expected_len = 1; // 长度为1字节
                } else if ((start_byte & 0xE0) == 0xC0) { // 检查是否为双字节UTF-8 (110xxxxx)
                    expected_len = 2; // 长度为2字节
                } else if ((start_byte & 0xF0) == 0xE0) { // 检查是否为三字节UTF-8 (1110xxxx)
                    expected_len = 3; // 长度为3字节
                } else if ((start_byte & 0xF8) == 0xF0) { // 检查是否为四字节UTF-8 (11110xxx)
                    expected_len = 4; // 长度为4字节
                } else {
                    // 发现无效的UTF-8起始字节
                    dest[last_char_start] = '\0'; // 在此位置截断字符串
                    return; // 提前返回
                }

                // 如果最后一个字符的完整长度超出了已复制的长度
                if (last_char_start + expected_len > len)
                {
                    // 则该字符不完整，在此处截断字符串以移除它
                    dest[last_char_start] = '\0';
                }
            }
        }
    }
}

uint8_t ancs_filter_count = 0;
uint8_t ancs_app_id_count = 0;

ANCS_CATAGORY_ID ancs_filter_catagory_id[ANCS_CATAGORY_ID_ENTERTAINMENT] = {(ANCS_CATAGORY_ID)0};

//-------------------------------------------------------------------------------------------
// Function Name : ancs_catagory_is_filter
// Purpose       : 根据ID判断是否过滤
// Param[in]     : ANCS_CATAGORY_ID catagory_id  
// Param[out]    : None
// Return type   : bool
// Comment       : 2020-12-21
//-------------------------------------------------------------------------------------------
bool ancs_catagory_is_filter(ANCS_CATAGORY_ID catagory_id)
{
    bool flag = false;
    
    switch (catagory_id)
        {
            case ANCS_CATAGORY_ID_INCOMING_CALL:
                if (g_device_get_config_ancs_incoming_call() == 1)
                {
                    flag = true;
                }
                break;
            case ANCS_CATAGORY_ID_MISSED_CALL:
                if (g_device_get_config_ancs_missed_call() == 1)
                {
                    flag = true;
                }
                break;
            case ANCS_CATAGORY_ID_VOICE_MAIL:
                 if (g_device_get_config_ancs_voice_mail() == 1)
                {
                    flag = true;
                }
                break;
            case ANCS_CATAGORY_ID_SOCIAL:
                if (g_device_get_config_ancs_social() == 1)
                {
                    flag = true;
                }
                break;
            case ANCS_CATAGORY_ID_ID_SCHEDULE:
                if (g_device_get_config_ancs_schedule() == 1)
                {
                    flag = true;
                }
                break;
            case ANCS_CATAGORY_ID_ID_EMAIL:
                if (g_device_get_config_ancs_email() == 1)
                {
                    flag = true;
                }
                break;
            case ANCS_CATAGORY_ID_NEWS:
                if (g_device_get_config_ancs_news() == 1)
                {
                    flag = true;
                }
                break;
            case ANCS_CATAGORY_ID_HEALTH_AND_FITNESS:
                if (g_device_get_config_ancs_health() == 1)
                {
                    flag = true;
                }
                break;
            case ANCS_CATAGORY_ID_BUSINESS_AND_FINANCE:
                if (g_device_get_config_ancs_business() == 1)
                {
                    flag = true;
                }
                break;
            case ANCS_CATAGORY_ID_LOCATION:
                if (g_device_get_config_ancs_location() == 1)
                {
                    flag = true;
                }
                break;
            case ANCS_CATAGORY_ID_ENTERTAINMENT:
                if (g_device_get_config_ancs_entertrainment() == 1)
                {
                    flag = true;
                }
                break;
            case ANCS_CATAGORY_ID_OTHER:
                flag = true;
                break;
             default:
                break;
        }

        return flag;
}

//-------------------------------------------------------------------------------------------
// Function Name : ancs_filter_setting
// Purpose       : ANCS过滤功能设置
// Param[in]     : void  
// Param[out]    : None
// Return type   : static
// Comment       : 2020-12-21
//-------------------------------------------------------------------------------------------
static void ancs_filter_setting(void)
{
    if (ancs_filter_catagory_id[0] != ANCS_CATAGORY_ID_OTHER)
    {
        g_device_set_config_ancs_incoming_call(false);
        g_device_set_config_ancs_missed_call(false);
        g_device_set_config_ancs_voice_mail(false);
        g_device_set_config_ancs_social(false);
        g_device_set_config_ancs_schedule(false);
        g_device_set_config_ancs_email(false);
        g_device_set_config_ancs_news(false);
        g_device_set_config_ancs_health(false);
        g_device_set_config_ancs_business(false);
        g_device_set_config_ancs_location(false);
        g_device_set_config_ancs_entertrainment(false);
    }
        
    for (uint8_t i = 0; i < ANCS_CATAGORY_ID_ENTERTAINMENT; i ++)
    {
        switch (ancs_filter_catagory_id[i])
        {
            case ANCS_CATAGORY_ID_INCOMING_CALL:
            	g_device_set_config_ancs_incoming_call(true);
                break;
            case ANCS_CATAGORY_ID_MISSED_CALL:
            	g_device_set_config_ancs_missed_call(true);
                break;
            case ANCS_CATAGORY_ID_VOICE_MAIL:
            	g_device_set_config_ancs_voice_mail(true);
                break;
            case ANCS_CATAGORY_ID_SOCIAL:
            	g_device_set_config_ancs_social(true);
                break;
            case ANCS_CATAGORY_ID_ID_SCHEDULE:
            	g_device_set_config_ancs_schedule(true);
            	break;
            case ANCS_CATAGORY_ID_ID_EMAIL:
            	g_device_set_config_ancs_email(true);
                break;
            case ANCS_CATAGORY_ID_NEWS:
            	g_device_set_config_ancs_news(true);
                break;
            case ANCS_CATAGORY_ID_HEALTH_AND_FITNESS:
            	g_device_set_config_ancs_health(true);
                break;
            case ANCS_CATAGORY_ID_BUSINESS_AND_FINANCE:
            	g_device_set_config_ancs_business(true);
                break;
            case ANCS_CATAGORY_ID_LOCATION:
            	g_device_set_config_ancs_location(true);
                break;
            case ANCS_CATAGORY_ID_ENTERTAINMENT:
            	g_device_set_config_ancs_entertrainment(true);
                break;
             default:
                break;
        }
    }
}

//-------------------------------------------------------------------------------------------
// Function Name : ancs_filter_msg_decode
// Purpose       : ANCS过滤机制增加
// Param[in]     : pb_ostream_t *stream     
//                 const pb_field_t *field  
//                 void *const *arg         
// Param[out]    : None
// Return type   : static
// Comment       : 2020-12-08
//-------------------------------------------------------------------------------------------
static bool ancs_filter_msg_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    ancs_filter_message submsg = {0};  
    char str[APP_ID_NAME_MAX] = {0};

    memset (&submsg, 0, sizeof(ancs_filter_message));
    
    submsg.app_identifier.arg = str;
    submsg.app_identifier.funcs.decode = &decode_string;
        
    if (!pb_decode(stream, ancs_filter_message_fields, &submsg))
        return false; 

    if(ancs_app_id_count == 0)
    {
        ins_app_list_info_clear();
    }
    ins_app_list_info_update((uint8_t *)str, strlen(str) + 1);
    ancs_app_id_count ++;

    if (submsg.has_catagory_id)
    {
        ancs_filter_catagory_id[ancs_filter_count] = submsg.catagory_id;
        ancs_filter_count ++;
    }

    return true;
}

static bool ins_service_app_id_list_encode(pb_ostream_t *stream, const pb_field_t *field, void * const *arg)
{
    uint8_t status = true;
    ancs_filter_message ancs_msg = {0};
    ancs_msg.has_catagory_id = false;
    ancs_msg.app_identifier.funcs.encode = encode_string;

    qw_mlist_t* mnode = NULL;
    qw_mlist_t* mlist = ins_app_list_get_info();
    FOR_MLIST(mnode, mlist){
        ancs_msg.app_identifier.arg = mnode->mnode_data;
        status = pb_encode_tag_for_field(stream, field);
        status &= pb_encode_submessage(stream, ancs_filter_message_fields, &ancs_msg);
    }
    return status;
}

void ins_service_enabled_status_send(void)
{
    uint8_t pb_crc = 0;
    uint8_t *data = NULL;
    uint16_t *length = NULL;

    ins_msg ins_message;

    memset(&ins_message, 0, sizeof(ins_msg));
    ble_data_var_tx_get(&data, &length, BLE_NUS_CH0_UUID);

    // 参数赋值
    ins_message.service_type = service_type_index_enum_SERVICE_TYPE_INDEX_INS;
    ins_message.ins_service_type = INS_SERVICE_TYPE_enum_INS_SERVICE_TYPE_MAIN;
    ins_message.ins_operate_type = INS_OPERATE_TYPE_enum_INS_OPERATE_TYPE_GET_SWITCH_STATUS;
    ins_message.has_config = true;
    ins_message.config = !g_device_get_otherconfig_ins_enable();
    if(!ins_app_list_info_is_empty())
    {
        ins_message.ancs_filter_msg.funcs.encode = ins_service_app_id_list_encode;
    }

    // 编码缓存
    pb_ostream_t encode_stream = pb_ostream_from_buffer(data, ONE_FRAME_DATA_LENGTH_MAX_CH0);
    // 编码
    pb_encode(&encode_stream, ins_msg_fields, &ins_message);

    *length = encode_stream.bytes_written;
    ble_nus_data_tx_ch0();

    // 对pb编码进行crc校验
    pb_crc = CRC_Calc8_Table_L(data, *length);

    // 命令协议 发送通道2
    ble_cmd_end_tx(ins_message.service_type, ins_message.ins_service_type, ins_message.ins_operate_type, 0, encode_stream.bytes_written, pb_crc);
}

//-------------------------------------------------------------------------------------------
// Function Name : ins_service_status_handle
// Purpose       : BLE INS错误状态处理函数
// Param[in]     : uint8_t *buf  
// Param[out]    : None
// Return type   : 
// Comment       : 2019-04-06
//-------------------------------------------------------------------------------------------
void ins_service_status_handle(uint8_t *buf)
{
	ble_status_cmd_st *ble_status_cmd_s = (ble_status_cmd_st *)buf;
	uint8_t status = ble_status_cmd_s->status;

	if (enmuDATA_ERR_STATUS == status)
	{
		switch (ble_status_cmd_s->op_type)
		{
			default:
				break;
		}
	}		
}

//-------------------------------------------------------------------------------------------
// Function Name : ins_service_decode
// Purpose       : INS PB数据解码接口函数
// Param[in]     : uint8_t * pb_buffer     
//                 uint16_t buffer_length  
// Param[out]    : None
// Return type   : 
// Comment       : 2019-04-06
//-------------------------------------------------------------------------------------------
void ins_service_decode(uint8_t * pb_buffer, uint16_t buffer_length, END_TYPE end_type)
{
#define _NAME_MAX_LENGTH		320
    uint8_t status = false;
    ins_msg ins_message;
    ancs_filter_message ancs_filter;

    uint8_t tel_num[30] = {0};
    uint16_t ins_index = g_ins_data_t.index;
    uint8_t *message = MY_MALLOC(512);
    uint8_t *name = MY_MALLOC(_NAME_MAX_LENGTH);

    if((message == NULL) || (name == NULL))
    {
    	ble_cmd_status_tx(service_type_index_enum_SERVICE_TYPE_INDEX_INS, 0, 0, 0, enmuMEMORY_ERR_STATUS);
    	if(message)
    	{
    		MY_FREE(message);
    	}
    	return;
    }

    memset (message, 0x00, 500);
    memset (name, 0x00, _NAME_MAX_LENGTH);
    memset((uint8_t *)&g_ins_data_t, 0, sizeof(ins_data_t));
    g_ins_data_t.index = ins_index;

    memset(&ins_message, 0, sizeof(ins_msg));
    memset(&ancs_filter, 0, sizeof(ancs_filter_message));
    memset(ancs_filter_catagory_id, 0, ANCS_CATAGORY_ID_ENTERTAINMENT);

    ancs_filter_count = 0;
    ancs_app_id_count = 0;
    
    ins_message.ins_data_msg.content.arg = message;
    ins_message.ins_data_msg.content.funcs.decode = &decode_string;

    ins_message.ins_data_msg.name.arg = name;
    ins_message.ins_data_msg.name.funcs.decode = &decode_string;

    ins_message.ins_data_msg.tel_num.arg = tel_num;
    ins_message.ins_data_msg.tel_num.funcs.decode = &decode_string;

    ins_message.ins_data_msg.time.arg = g_ins_data_t.date;
    ins_message.ins_data_msg.time.funcs.decode = &decode_string;

    ins_message.ancs_filter_msg.arg = &ancs_filter;
    ins_message.ancs_filter_msg.funcs.decode = &ancs_filter_msg_decode;

    pb_istream_t decode_stream = pb_istream_from_buffer(pb_buffer, buffer_length);
    
    status = pb_decode(&decode_stream, ins_msg_fields, &ins_message);

    memset(g_ins_data_t.title,0x00,INS_DATA_MAX);
    if (strlen((char *)name) > 0)
    {
    	utf8_safe_strncpy(g_ins_data_t.title, name, INS_DATA_MAX);
    }
    else
    {
    	utf8_safe_strncpy(g_ins_data_t.title, tel_num, INS_DATA_MAX);
    }

    memset(g_ins_data_t.message,0x00,INS_MEG_MAX);
    utf8_safe_strncpy(g_ins_data_t.message, message, INS_MEG_MAX);

    MY_FREE(message);
    MY_FREE(name);

    if (true == status)
    {
        if(ins_message.ins_operate_type != INS_OPERATE_TYPE_enum_INS_OPERATE_TYPE_GET_SWITCH_STATUS){
                ble_cmd_success_status_tx(ins_message.service_type, ins_message.ins_service_type, ins_message.ins_operate_type, 0);
        }
        switch(ins_message.ins_service_type)
        {
            case INS_SERVICE_TYPE_enum_INS_SERVICE_TYPE_MAIN:
                switch(ins_message.ins_operate_type)
                {
                    case INS_OPERATE_TYPE_enum_INS_OPERATE_TYPE_CTRL:
                        if (true == ins_message.has_config)
                        {
                            if (false == ins_message.config)
                            {
                            	g_device_set_otherconfig_ins_enable(true);
                            }
                            else
                            {
                            	g_device_set_otherconfig_ins_enable(false);
                            }
                        }
                        if(ancs_filter_count)
                        {
                            ancs_filter_setting();
                        }
                        break;
                    case INS_OPERATE_TYPE_enum_INS_OPERATE_TYPE_SET_APP_LIST:
                        if (ancs_app_id_count == 0){
                            ins_app_list_info_clear();
                        }
                        ins_app_file_store();
                        break;
                    case INS_OPERATE_TYPE_enum_INS_OPERATE_TYPE_GET_SWITCH_STATUS:
                        ins_service_enabled_status_send();
                        break;
                    default:
                        break;
                }
                break;
            case INS_SERVICE_TYPE_enum_INS_SERVICE_TYPE_CALL:
                switch(ins_message.ins_operate_type)
                {
                    case INS_OPERATE_TYPE_enum_INS_OPERATE_TYPE_INCOMING_CALL:      //手机来电
                        if (NULL != ble_periph_evt_handler)
                        {
                            ble_periph_evt_handler(BLE_PERIPH_ANDROID_INCOMING_ADD_EVT, (void *)&g_ins_data_t);
                        }
                        break;
                    case INS_OPERATE_TYPE_enum_INS_OPERATE_TYPE_ANSWER_CALL:         //手机接听电话
                        if (NULL != ble_periph_evt_handler)
                        {
                            ble_periph_evt_handler(BLE_PERIPH_ANDROID_INCOMING_REMOVE_EVT, (void *)&g_ins_data_t);
                        }
                        break;
                    case INS_OPERATE_TYPE_enum_INS_OPERATE_TYPE_REJECT_CALL:         //手机拒接电话 
                        g_ins_data_t.msg_type = enumMSG_INCOMING_TYPE;
                        g_ins_data_t.index ++;
                        ins_data_save_to_file(&g_ins_data_t);
                        if (NULL != ble_periph_evt_handler)
                        {
                            ble_periph_evt_handler(BLE_PERIPH_ANDROID_INCOMING_REMOVE_EVT, (void *)&g_ins_data_t);
                        }
                        break; 
                    case INS_OPERATE_TYPE_enum_INS_OPERATE_TYPE_CHECK_CALL:             //手机查看电话                   
                        break;
                    default:
                    	break;
                }
                break;
            case INS_SERVICE_TYPE_enum_INS_SERVICE_TYPE_NOTE:
                switch (ins_message.ins_operate_type)
                {
                    case INS_OPERATE_TYPE_enum_INS_OPERATE_TYPE_INCOMING_NOTE:          //手机短信
                    	if (ins_message.ins_data_msg.has_is_app)
                    	{
                    		if (ins_message.ins_data_msg.is_app)
                    		{
                    			g_ins_data_t.msg_type = enumMSG_APP_TYPE;
                    		}
                    		else
                    		{
                    			g_ins_data_t.msg_type = enumMSG_NOTE_TYPE;
                    		}
                    	}
                    	else
                    	{
                    		g_ins_data_t.msg_type = enumMSG_NOTE_TYPE;
                    	}

                        if (NULL != ble_periph_evt_handler)
                        {
                            if (ins_message.ins_data_msg.has_pairing_code && ins_message.ins_data_msg.pairing_code)
                            {
                                ble_periph_evt_handler(BLE_PERIPH_PAIR_CODE_EVT, (void *)&g_ins_data_t);                                
                            }
                            else if (ins_message.ins_data_msg.has_count)
                            {
                                if(ins_message.ins_data_msg.count < 2)
                                {
                                	g_ins_data_t.index ++;
                                	ins_data_save_to_file(&g_ins_data_t);
                                    ble_periph_evt_handler(BLE_PERIPH_ANDROID_SOCIAL_EVT_ADD_EVT, (void *)&g_ins_data_t);
                                }
                            }
                            else
                            {
                            	g_ins_data_t.index ++;
                            	ins_data_save_to_file(&g_ins_data_t);
                                ble_periph_evt_handler(BLE_PERIPH_ANDROID_SOCIAL_EVT_ADD_EVT, (void *)&g_ins_data_t);
                            }
                        }
                        break;
                    case INS_OPERATE_TYPE_enum_INS_OPERATE_TYPE_CHECK_NOTE:                 //手机查看短信
                        break;
                    default:
                        break;
                }
                break;
            case INS_SERVICE_TYPE_enum_INS_SERVICE_TYPE_FIND_WATCH:
                switch(ins_message.ins_operate_type)
                {
                    case INS_OPERATE_TYPE_enum_INS_OPERATE_TYPE_FIND_START:
                        //TODO: 手表开始响铃
                        submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_FIND_WATCH, NULL);
                        break;
                    case INS_OPERATE_TYPE_enum_INS_OPERATE_TYPE_FIND_STOP:
                        submit_gui_event(GUI_EVT_SERVICE_POPOUT, enumPOPUP_CLOSE, (void *) enumPOPUP_FIND_WATCH);
                        //TODO: 手表停止响铃
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
    }
    else
    {
        ble_cmd_err_status_tx(ins_message.service_type, ins_message.ins_service_type, ins_message.ins_operate_type, 0);
    }
}

/************************************************************************
 *@function:void ins_service_call_reject_cmd(void);
 *@brief 上报来电拒接命令
 *@param: null
 *@return: null
*************************************************************************/
void ins_service_call_reject_cmd(void)
{
    if (g_device_get_ble_connect_status())
    {
        ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_INS, INS_SERVICE_TYPE_enum_INS_SERVICE_TYPE_CALL, INS_OPERATE_TYPE_enum_INS_OPERATE_TYPE_DEVICE_REJECT_CALL, 0xff, 0);
    }
}

/************************************************************************
 *@function:void ins_service_call_mute_cmd(void);
 *@brief 上报来电静音命令
 *@param: null
 *@return: null
*************************************************************************/
void ins_service_call_mute_cmd(void)
{
    if (g_device_get_ble_connect_status())
    {
        ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_INS, INS_SERVICE_TYPE_enum_INS_SERVICE_TYPE_CALL, INS_OPERATE_TYPE_enum_INS_OPERATE_TYPE_DEVICE_MUTE_CALL, 0xff, 0);
    }
}

/************************************************************************
 *@function:void ins_service_find_phone_start_cmd(void);
 *@brief 上报查找手机开始命令
 *@param: null
 *@return: null
*************************************************************************/
void ins_service_find_phone_start_cmd(void)
{
    if (g_device_get_ble_connect_status())
    {
        ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_INS, INS_SERVICE_TYPE_enum_INS_SERVICE_TYPE_FIND_PHONE, INS_OPERATE_TYPE_enum_INS_OPERATE_TYPE_FIND_START, 0xff, 0);
    }
}

/************************************************************************
 *@function:void ins_service_find_phone_stop_cmd(void);
 *@brief 上报查找手机结束命令
 *@param: null
 *@return: null
*************************************************************************/
void ins_service_find_phone_stop_cmd(void)
{
    if (g_device_get_ble_connect_status())
    {
        ble_cmd_notice_tx(service_type_index_enum_SERVICE_TYPE_INDEX_INS, INS_SERVICE_TYPE_enum_INS_SERVICE_TYPE_FIND_PHONE, INS_OPERATE_TYPE_enum_INS_OPERATE_TYPE_FIND_STOP, 0xff, 0);
    }
}
