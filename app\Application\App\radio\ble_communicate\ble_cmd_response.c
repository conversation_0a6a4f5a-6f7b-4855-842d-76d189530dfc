/***************************************Copyright (c)****************************************/
//                              <PERSON>han <PERSON> Technology Co., Ltd
//
//---------------------------------------File Info--------------------------------------------
// File name         : ble_cmd_response.c
// Created by        : jiangzhen
// Descriptions      : ble通道1 状态、结束、特殊通知命令公共接口函数.c文件
//--------------------------------------------------------------------------------------------
// History           :
// 2020-05-14        :原始版本
/*********************************************************************************************/

#include "stddef.h"
#include "ble_cmd_response.h"
#include "ble_cmd_common.h"
#include "ble_nus_srv.h"
#include "crc8.h"
#include <string.h>
#include "ble_interflow_single.h"

//-------------------------------------------------------------------------------------------
// Function Name : ble_cmd_status_tx
// Purpose       : 通道1 发送状态命令
// Param[in]     : uint8_t service_type      
//                 uint8_t sub_service_type  
//                 uint8_t op_type           
//                 uint8_t sub_op_type       
//                 STATUS_TYPE status        
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-14
//-------------------------------------------------------------------------------------------
void ble_cmd_status_tx(uint8_t service_type, uint8_t sub_service_type, uint8_t op_type, uint8_t sub_op_type, STATUS_TYPE status)
{
    uint8_t ble_data[DATA_LENGTH_CH1] = {0};
    ble_status_cmd_st *ble_status_cmd_s = (ble_status_cmd_st *)ble_data;
		
    memset(ble_data, 0xff, DATA_LENGTH_CH1);

    ble_status_cmd_s->cmd_type = enum_BLE_STATUS_CMD;
    ble_status_cmd_s->service_type = service_type;
    if (sub_service_type != 0)
    {
        ble_status_cmd_s->sub_service_type = sub_service_type;
    }
    
    ble_status_cmd_s->op_type = op_type;
    if(sub_op_type != 0)
    {
        ble_status_cmd_s->sub_op_type = sub_op_type;
    }
    ble_status_cmd_s->status = status;

    ble_status_cmd_s->crc8 = CRC_Calc8_Table(ble_data, DATA_LENGTH_CH1 - 1);

    ble_nus_data_tx_protocol_header(ble_data);
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_cmd_notice_tx
// Purpose       : 通道1 发送特殊通知命令
// Param[in]     : uint8_t service_type      
//                 uint8_t sub_service_type  
//                 uint8_t op_type           
//                 uint8_t sub_op_type       
//                 uint8_t status            
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-14
//-------------------------------------------------------------------------------------------
void ble_cmd_notice_tx(uint8_t service_type, uint8_t sub_service_type, uint8_t op_type, uint8_t sub_op_type, uint8_t status)
{
    uint8_t ble_data[DATA_LENGTH_CH1] = {0};
    ble_status_cmd_st *ble_status_cmd_s = (ble_status_cmd_st *)ble_data;
		
    memset(ble_data, 0xff, DATA_LENGTH_CH1);

    ble_status_cmd_s->cmd_type = enum_BLE_NOTICE_CMD;
    ble_status_cmd_s->service_type = service_type;
    if (sub_service_type != 0)
    {
        ble_status_cmd_s->sub_service_type = sub_service_type;
    }
    
    ble_status_cmd_s->op_type = op_type;
    // if(sub_op_type != 0)
    // {
        ble_status_cmd_s->sub_op_type = sub_op_type;
    // }
    ble_status_cmd_s->status = status;

    ble_status_cmd_s->crc8 = CRC_Calc8_Table(ble_data, DATA_LENGTH_CH1 - 1);

    ble_nus_data_tx_protocol_header(ble_data);
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_cmd_notice_tx_with_timestamp
// Purpose       : 通道1 发送特殊通知命令，带时间戳
// Param[in]     : uint8_t service_type      
//                 uint8_t sub_service_type  
//                 uint8_t op_type           
//                 uint8_t sub_op_type       
//                 uint8_t status            
//                 uint32_t timestamp
// Param[out]    : None
// Return type   : 
//-------------------------------------------------------------------------------------------
void ble_cmd_notice_tx_with_timestamp(uint8_t service_type, uint8_t sub_service_type, uint8_t op_type, uint8_t sub_op_type, uint8_t status, uint32_t timestamp)
{
    uint8_t ble_data[DATA_LENGTH_CH1] = {0};
    ble_status_cmd_st *ble_status_cmd_s = (ble_status_cmd_st *)ble_data;

    memset(ble_data, 0xff, DATA_LENGTH_CH1);

    ble_status_cmd_s->cmd_type = enum_BLE_NOTICE_CMD;
    ble_status_cmd_s->service_type = service_type;
    if (sub_service_type != 0)
    {
        ble_status_cmd_s->sub_service_type = sub_service_type;
    }
    
    ble_status_cmd_s->op_type = op_type;
    if(sub_op_type != 0)
    {
        ble_status_cmd_s->sub_op_type = sub_op_type;
    }
    ble_status_cmd_s->status = status;
    
    ble_status_cmd_s->reserved3[3] = (uint8_t)((timestamp >> 24) & 0xff);
    ble_status_cmd_s->reserved3[2] = (uint8_t)((timestamp >> 16) & 0xff);
    ble_status_cmd_s->reserved3[1] = (uint8_t)((timestamp >> 8) & 0xff);
    ble_status_cmd_s->reserved3[0] = (uint8_t)(timestamp & 0xff);

    ble_status_cmd_s->crc8 = CRC_Calc8_Table(ble_data, DATA_LENGTH_CH1 - 1);

    ble_nus_data_tx_protocol_header(ble_data);
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_cmd_end_type_tx
// Purpose       : 发送结束命令，命令结束、帧结束、文件结束等
// Param[in]     : uint32_t service_index  
//                 uint32_t sub_service    
//                 uint32_t operate_type   
//                 uint16_t data_length    
//                 END_TYPE end_type       
//                 uint8_t pb_crc          
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-15
//-------------------------------------------------------------------------------------------
void ble_cmd_end_type_tx(uint32_t service_index, uint32_t sub_service, uint32_t operate_type, uint32_t sub_operate_type, uint16_t data_length, END_TYPE end_type, uint8_t pb_crc)
{
	uint8_t ble_data[DATA_LENGTH_CH1] = {0};
	ble_end_cmd_st *ble_end_cmd_s = (ble_end_cmd_st *)ble_data;

	//默认值0xff
	memset(ble_data, 0xff, sizeof(ble_data));

	ble_end_cmd_s->cmd_type = enum_BLE_END_CMD;
	ble_end_cmd_s->service_type = service_index;

	if (0 != sub_service){
		ble_end_cmd_s->sub_service_type = sub_service;
	}

    if (0 != sub_operate_type){
    	ble_end_cmd_s->sub_op_type = sub_operate_type;
    }

	ble_end_cmd_s->op_type = operate_type;
	ble_end_cmd_s->msb_length = (data_length >> 8) & 0x00ff;
	ble_end_cmd_s->lsb_length = data_length & 0x00ff;
	ble_end_cmd_s->total_crc = pb_crc;
	ble_end_cmd_s->end_type = end_type;
	ble_end_cmd_s->crc8 = CRC_Calc8_Table(ble_data, DATA_LENGTH_CH1 - 1);

	ble_nus_data_tx_protocol_header(ble_data);
}

void ble_cmd_end_data_stream_type_tx(uint32_t service_index, uint32_t data_flow,uint32_t sub_service, uint32_t operate_type, uint32_t sub_operate_type, uint16_t data_length, END_TYPE end_type, uint8_t pb_crc)
{
	uint8_t ble_data[DATA_LENGTH_CH1] = {0};
	ble_end_cmd_st *ble_end_cmd_s = (ble_end_cmd_st *)ble_data;

	//默认值0xff
	memset(ble_data, 0xff, sizeof(ble_data));

	ble_end_cmd_s->cmd_type = enum_BLE_END_CMD;
	ble_end_cmd_s->service_type = service_index;
	ble_end_cmd_s->reserved1 = data_flow;
	if (0 != sub_service){
		ble_end_cmd_s->sub_service_type = sub_service;
	}

    if (0 != sub_operate_type){
    	ble_end_cmd_s->sub_op_type = sub_operate_type;
    }

	ble_end_cmd_s->op_type = operate_type;
	ble_end_cmd_s->msb_length = (data_length >> 8) & 0x00ff;
	ble_end_cmd_s->lsb_length = data_length & 0x00ff;
	ble_end_cmd_s->total_crc = pb_crc;
	ble_end_cmd_s->end_type = end_type;
	ble_end_cmd_s->crc8 = CRC_Calc8_Table(ble_data, DATA_LENGTH_CH1 - 1);

	ble_nus_data_tx_protocol_header(ble_data);
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_cmd_end_tx
// Purpose       : 通道1 发送结束命令
// Param[in]     : uint32_t service_index  
//                 uint32_t sub_service    
//                 uint32_t operate_type   
//                 uint16_t data_length    
//                 uint8_t pb_crc          
// Param[out]    : None
// Return type   : 
// Comment       : 2020-05-14
//-------------------------------------------------------------------------------------------
void ble_cmd_end_tx(uint32_t service_index, uint32_t sub_service, uint32_t operate_type, uint32_t sub_operate_type, uint16_t data_length, uint8_t pb_crc)
{
	ble_cmd_end_type_tx(service_index, sub_service, operate_type, sub_operate_type, data_length, enumCMD_END, pb_crc);    
}


//-------------------------------------------------------------------------------------------
// Function Name : ble_cmd_err_status_tx
// Purpose       : 通道1 回复数据错误状态命令
// Param[in]     : ble_end_cmd_st *end_cmd  
// Param[out]    : None
// Return type   : 
// Comment       : 2019-04-01
//-------------------------------------------------------------------------------------------
void ble_cmd_err_status_tx(uint8_t service_type, uint8_t sub_service_type, uint8_t op_type, uint8_t sub_op_type)
{
    ble_cmd_status_tx(service_type, sub_service_type, op_type, sub_op_type, enmuDATA_ERR_STATUS);
}

//-------------------------------------------------------------------------------------------
// Function Name : ble_cmd_success_status_tx
// Purpose       : 通道1 回复成功状态命令
// Param[in]     : uint8_t service_type      
//                 uint8_t sub_service_type  
//                 uint8_t op_type           
//                 uint8_t sub_op_type       
// Param[out]    : None
// Return type   : 
// Comment       : 2019-04-01
//-------------------------------------------------------------------------------------------
void ble_cmd_success_status_tx(uint8_t service_type, uint8_t sub_service_type, uint8_t op_type, uint8_t sub_op_type)
{
    ble_cmd_status_tx(service_type, sub_service_type, op_type, sub_op_type, enumSUCCESS_STATUS);
}
