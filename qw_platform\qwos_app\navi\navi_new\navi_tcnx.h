/************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   navi_tcnx.h
@Time    :   2025/05/12 19:41:53
*
************************************************************/

#ifndef NAVI_TCNX_H
#define NAVI_TCNX_H
#include "navi_common.h"
#include "navi_waypoint.h"
#include "qw_fs.h"

#ifdef __cplusplus
extern "C" {
#endif


//用于读取tcnx文件
typedef struct _TcnxReader
{
    QW_FIL *fp;
} TcnxReader;

//用于写入tcnx文件
typedef struct _TcnxWriter
{
    QW_FIL *fp;
} TcnxWriter;

//导航线路整体数据
typedef struct _NaviRouteData
{
    Bbox bbox;                      //线路bbox
    float dist;                     //总距离（m）
    float ascent;                   //总升（m）
    float descent;                  //总降（m）
    float alt_min;                  //最小海拔（m）
    float alt_max;                  //最大海拔（m）
} NaviRouteData;

//导航线路采样（全局路点）
typedef struct _NaviRouteWpSample
{
    NaviWaypointDc *wpdc_buf;       //缓冲区，保存全局路点
    uint32_t len;                   //全局路点数量
} NaviRouteWpSample;

//指定路点前后的路点
typedef struct _NaviRouteWpNearby
{
    NaviWaypointDc *buf;
    uint32_t len;
} NaviRouteWpNearby;

//导航线路分段
typedef struct _NaviRouteSegment
{
    Bbox bbox;                      //分段的bbox
    Dseg dseg;                      //分段的距离区间
    Range range;                    //分段的路点的索引区间
} NaviRouteSegment;

//导航线路分段数组
typedef struct _NaviRouteSegmentArray
{
    NaviRouteSegment *segments;     //分段
    uint32_t len;                   //分段数量
} NaviRouteSegmentArray;

//线路路点缓冲区，主要用于暂存加载的线路路点（压缩后的路点）
typedef struct _NaviWaypointDcBuf
{
    NaviWaypointDc *buf;            //缓冲区，保存加载的路点
    Range range;                    //缓冲区保存的路点的索引范围
    uint32_t capacity;              //缓冲区容量
} NaviWaypointDcBuf;

//线路路点缓存
typedef struct _NaviWaypointDcCache
{
    NaviWaypointDcBuf *wpdc_buf;    //线路路点缓冲区，可以有多个
    TcnxReader *tcnx_reader;        //用于加载路点
    uint32_t capacity;              //线路路点缓冲区容量
    uint32_t len;                   //已启用的线路路点缓冲区数量
    uint32_t next;                  //下一个要启用的线路路点缓冲区
} NaviWaypointDcCache;

//导航线路路点list，一个抽象list，可通过它获取任一路点数据
typedef struct _NaviRouteWpList
{
    NaviWaypointDcCache cache;      //线路路点缓存
    uint32_t len;                   //导航线路路点总数量
} NaviRouteWpList;

int tcnx_header_read(TcnxReader *self, uint8_t *header);

int tcnx_route_data_read(TcnxReader *self, NaviRouteData *data);

int tcnx_route_wp_sample_read(TcnxReader *self, NaviRouteWpSample *sample);

int tcnx_route_segment_array_read(TcnxReader *self, NaviRouteSegmentArray *seg_array);

int tcnx_route_wp_compressed_num_read(TcnxReader *self, uint32_t *wp_num);

int tcnx_route_wp_compressed_data_read(TcnxReader *self, uint32_t start, uint32_t end, uint32_t wp_num, NaviWaypointDc *wpdc_buf);

int tcnx_header_placeholder_write(TcnxWriter *self);

int tcnx_header_write(TcnxWriter *self);

int tcnx_route_data_write(TcnxWriter *self, const NaviRouteData *data);

int tcnx_route_wp_sample_write(TcnxWriter *self, const NaviRouteWpSample *sample);

int tcnx_route_segment_array_write(TcnxWriter *self, const NaviRouteSegmentArray *seg_array);

int tcnx_route_wp_compressed_num_write(TcnxWriter *self, uint32_t num);

int tcnx_route_wp_compressed_data_write(TcnxWriter *self, const NaviWaypointDc *wpdc);

int navi_route_wp_list_get(NaviRouteWpList *self, uint32_t idx, NaviWaypointDc *output);

void navi_waypoint_dc_buf_reset(NaviWaypointDcBuf *self);

void navi_waypoint_dc_cache_reset(NaviWaypointDcCache *self);

void navi_route_wp_list_reset(NaviRouteWpList *self);

#ifdef __cplusplus
}
#endif

#endif