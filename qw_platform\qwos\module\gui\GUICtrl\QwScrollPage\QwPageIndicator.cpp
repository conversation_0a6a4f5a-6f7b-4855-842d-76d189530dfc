/****************************************Copyright (c)****************************************
* <PERSON><PERSON>wu Technology Co., Ltd
*
*---------------------------------------File Info--------------------------------------------
* File path :
* Created by : Lxin
* LastEditors: Lxin
* Descriptions :
*--------------------------------------------------------------------------------------------
* History :
* 2023-04-28 16:06:01: Lxin 原始版本
*
*********************************************************************************************/
#include "QwPageIndicator.h"

// ========================= TiledImageArc extensions =========================
void TiledImageArc::setBitmapList(const Bitmap* list, uint8_t count)
{
    bitmapList = list;
    bitmapListCount = count;
}

void TiledImageArc::clearBitmapList()
{
    bitmapList = nullptr;
    bitmapListCount = 0;
}

bool TiledImageArc::hasBitmapList() const
{
    return (bitmapList != nullptr) && (bitmapListCount > 0);
}

Bitmap TiledImageArc::getFirstBitmapFromList() const
{
    if (hasBitmapList()) {
        return bitmapList[0];
    }
    return Bitmap();
}

void TiledImageArc::draw(const Rect& invalidatedArea) const
{
    // Determine base tile size (assume uniform sizes across list); fallback to single bitmap
    Bitmap baseBmp = hasBitmapList() ? getFirstBitmapFromList() : bitmap;
    uint16_t bitmapWidth = baseBmp.getWidth();
    uint16_t bitmapHeight = baseBmp.getHeight();

    if (bitmapWidth == 0 || bitmapHeight == 0) {
        return;
    }

    lv_draw_ctx_t* draw_context = lv_get_draw_context();
    Rect clipRect;

    if (!touchx_caculate_context_draw_cliprect(draw_context, invalidatedArea, clipRect)) {
        return;
    }

    Rect mAbs = getAbsoluteRect();
    mAbs.x += xOffset;
    mAbs.y += yOffset;

    lv_draw_img_dsc_t img_dsc;
    lv_draw_img_dsc_init(&img_dsc);
    img_dsc.opa = alpha;
    img_dsc.recolor = lv_color_hex(recolor.color);
    img_dsc.recolor_opa = recolor_alpha;

    lv_area_t clip_area;
    txRect_to_lvArea(clipRect, clip_area);

    lv_area_t coords;
    txRect_to_lvArea(mAbs, coords);

    lv_area_t area;
    area.y1 = coords.y1+4;//
    area.y2 = area.y1 + bitmapHeight - 1;

    const lv_area_t* clip_area_ori = draw_context->clip_area;
    draw_context->clip_area = &clip_area;

    float circleRadius = HAL::DISPLAY_WIDTH / 2 - 12 - bitmapHeight / 2;
    uint32_t tileIndex = 0;
    for (; area.y1 <= coords.y2; area.y1 += (bitmapHeight + TiledImageArc::INDICATOR_SPACE), area.y2 += (bitmapHeight + TiledImageArc::INDICATOR_SPACE), ++tileIndex)
    {
#if 1
        int16_t y = area.y1 + bitmapHeight / 2 - HAL::DISPLAY_HEIGHT / 2;
        float x_f = circleRadius - sqrtf((float)((circleRadius * circleRadius) - (y * y)));
        area.x1 = coords.x1 + (int16_t)(x_f + 0.5f)+4; //整个圆弧像右移动4像素
#else   
        area.x1 = coords.x1;
#endif
        area.x2 = area.x1 + bitmapWidth - 1;

        // Select bitmap per tile (cycle through list if provided)
        const void* drawId = nullptr;
        if (hasBitmapList()) {
            const Bitmap& b = bitmapList[tileIndex % bitmapListCount];
            drawId = b.getId();
        } else {
            drawId = bitmap.getId();
        }
        lv_draw_img(draw_context, &img_dsc, &area, drawId);
    }

    draw_context->clip_area = clip_area_ori;
}

//////////////////////////////////////////////////////////////////

QwPageIndicator::QwPageIndicator() : 
    Container(),
    unselectedPages(),
    selectedPage(),
    numberOfPages(0),
    currentPage(0)
{
    unselectedPages.setXY(0, 0);
    selectedPage.setXY(0, 0);

    Container::add(unselectedPages);
    Container::add(selectedPage);
}

void QwPageIndicator::setNumberOfPages(uint8_t size)
{
    numberOfPages = size;

    // Determine the base bitmap to compute geometry
    Bitmap bmp;
    if (unselectedPages.hasBitmapList()) {
        bmp = unselectedPages.getFirstBitmapFromList();
    } else if (unselectedPages.getBitmap()) {
        bmp = Bitmap(unselectedPages.getBitmap());
    }

    if (bmp.getWidth() > 0 && bmp.getHeight() > 0)
    {
        const int w = bmp.getWidth();
        const int h = bmp.getHeight();
        if (size == 0) {
            unselectedPages.setWidth(0);
            unselectedPages.setHeight(0);
            selectedPage.setWidth(0);
            selectedPage.setHeight(0);
            invalidate();
            setWidthHeight(unselectedPages);
            return;
        }

        unselectedPages.setWidth(w + (size - 1) * TiledImageArc::INDICATOR_SPACE);
        unselectedPages.setHeight(h * size + (size - 1) * TiledImageArc::INDICATOR_SPACE+4);

        selectedPage.setWidth(w + (size - 1) * TiledImageArc::INDICATOR_SPACE);
        selectedPage.setHeight(h+8);
        // adjust size of container according to the actual bitmaps
        invalidate();
        setWidthHeight(unselectedPages);
        if (getParent()) {
            setY((getParent()->getHeight() - getHeight()) / 2);
        }
        // Clamp current page within range
        uint8_t clamped = (size > 0) ? (currentPage < size ? currentPage : (uint8_t)(size - 1)) : 0;
        setCurrentPage(clamped);
        invalidate();
    }
}

void QwPageIndicator::setBitmaps(const Bitmap& normalPage, const Bitmap& highlightedPage)
{
    selectedPage.clearBitmapList();
    unselectedPages.clearBitmapList();

    selectedPage.setBitmap(highlightedPage);
    unselectedPages.setBitmap(normalPage);
    if (numberOfPages > 0)
    {
        setNumberOfPages(numberOfPages);
    }
}

void QwPageIndicator::setBitmaps(const Bitmap* normalPages, uint8_t normalCount, const Bitmap& highlightedPage)
{
    // Configure highlighted bitmap
    selectedPage.clearBitmapList();
    selectedPage.setBitmap(highlightedPage);

    // Configure unselected as a cyclic list
    unselectedPages.setBitmapList(normalPages, normalCount);
    if (numberOfPages > 0)
    {
        setNumberOfPages(numberOfPages);
    }
}

void QwPageIndicator::goUp()
{
    if (numberOfPages == 0) return;
    setCurrentPage((currentPage + 1) % numberOfPages);
}

void QwPageIndicator::goDown()
{
    if (numberOfPages == 0) return;
    setCurrentPage((currentPage + numberOfPages - 1) % numberOfPages);
}

void QwPageIndicator::setCurrentPage(uint8_t page)
{
    if (page < numberOfPages && page != currentPage)
    {
        currentPage = page;
        Bitmap baseBmp;
        if (unselectedPages.hasBitmapList()) {
            baseBmp = unselectedPages.getFirstBitmapFromList();
        } else {
            baseBmp = Bitmap(unselectedPages.getBitmap());
        }
        const int dotHeight = baseBmp.getHeight();

        selectedPage.moveTo(unselectedPages.getX()-4, (page * dotHeight + page * (TiledImageArc::INDICATOR_SPACE))-4);
    }
}

uint8_t QwPageIndicator::getNumberOfPages() const
{
    return numberOfPages;
}

uint8_t QwPageIndicator::getCurrentPage() const
{
    return currentPage;
}
