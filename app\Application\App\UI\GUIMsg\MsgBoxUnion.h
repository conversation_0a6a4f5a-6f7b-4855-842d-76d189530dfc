/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   MsgBoxUnion.h
@Time    :   2025/03/20 19:07:04
*
**************************************************************************/
#pragma once

#include "../../qwos_app/GUI/QwMsgBox/MsgAOD/MsgAOD.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgDecodeERR/MsgDecodeERR.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgFocusOn/MsgDND.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgAltimter/MsgAltimterIcon.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgAltimter/MsgAltimterAcl.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgBpwrCalib/MsgBpwrCalib.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgBpwrCalibResult/MsgBpwrCalibResult.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgGiveUpSport/MsgGiveUpSport.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgPowerSave/MsgPowerSave.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgPowerSave/MsgPowerSaveUnlock.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgReconnectSensor/MsgReconnectSensor.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgRemoveSensor/MsgRemoveSensor.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgSaveSport/MsgSaveSport.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgSensorLowPower/MsgSensorLowPower.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgSensorStateToast/MsgSensorStateToast.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgSportGpsTip/MsgSportGpsTip.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgSportOutCountDown/MsgSportOutCountDown.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgSportingLap/MsgSportingLap.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgHealthGoal/MsgHealthGoal.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgHealthGoal/MsgHeartRate.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgHealthGoal/MsgHeartRateNotice.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgHealthGoal/MsgSpo2.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgHealthGoal/MsgPressure.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgHealthGoal/MsgPressureRemind.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgToastTip/MsgToastTip.h"
#include "../../qwos_app/GUI/QwMsgBox/QwMsgText.h"
#include "../../qwos_app/GUI/QwMsgBox/SensorConnReachedLimit/SensorConnReachedLimit.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgChargeRing/MsgChargeRing.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgChargeRing/MsgUnderVolOff.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgChargeRing/MsgUnderVolTen.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgChargeRing/MsgUnderVolTwenty.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgSportRemind/MsgSportRemind.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgTipSelect/MsgTipSelect.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgLoading/MsgLoading.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgPowerReminder/MsgPowerReminder.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgAlarmReminder/MsgAlarmReminder.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgAlarmTip/MsgAlarmTip.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgVrbBackward/MsgVrbBackward.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgTimerEnd/MsgTimerEnd.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgFocusOn/MsgSleepBed.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgUnlock/MsgUnlock.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgDelTrainingCourse/MsgDelTrainingCourse.h"
#include "../../qwos_app/GUI/QwMsgBox/QwMsgFullScreenKeyTip/QwMsgFullScreenKeyTip.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgFecNotCon/MsgFecNotCon.h"
#include "../../qwos_app/GUI/QwMsgBox/MsginnerTrainTip/MsginnerTraintip.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgTriathlonTip/MsgTriathlonTip.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgBindWatch/MsgBindWatchSuccess.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgBindWatch/MsgBindWatchFail.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgBindWatch/MsgUnbindWatch.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgNavigation/MsgDelNavi.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgSportingGpsState/MsgSportingGpsState.h"
#include "../../qwos_app/GUI/QwMsgBox/QwMsgBottomToast/QwMsgBottomToast.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgIntelligentNotify/MsgIntelligentNotify.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgIncomingCall/MsgIncomingCall.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgAchReset/MsgAchReset.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgAchUpdate/MsgAchUpdate.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgFtpUpdate/MsgFtpUpdate.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgLtHrUpdate/MsgLtHrUpdate.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgMaxHrUpdate/MsgMaxHrUpdate.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgReset/MsgReset.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgFTPTestReminder/MsgFTPTestReminder.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgNaviBack/MsgNaviBack.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgSportStartCountDown/MsgSportStartCountDown.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgTodayTrainTip/MsgTodayTrainTip.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgOTAFinish/MsgOTAFinish.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgAlipay/MsgAlipay.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgAlipayStatus/MsgAlipayStatus.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgAlipayConfirm/MsgAlipayConfirm.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgFind/MsgFindWatch.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgDrinkWater/MsgDrinkWater.h"
#include "../../qwos_app/GUI/QwMsgBox/MsgDataSync/MsgDataSync.h"

union MsgBoxUnion {
public:
	MsgBoxUnion() {}
	~MsgBoxUnion() {}
private:
    QwMsgText test_box;                          // 测试弹窗
    MsgBpwrCalib bpwr_calib_box;                 // 功率校准
    MsgBpwrCalibResult bpwr_calib_result_box;    // 功率校准结果
    MsgRemoveSensor remove_sensor_box;           // 移除传感器
    MsgSensorStateToast sensor_state_box;        // 传感器状态
    MsgSportOutCountDown sport_out_count_down;   // 跑步倒计时
    MsgSportGpsTip sport_gps_tip;                // 跑步GPS提示
    MsgHealthGoal health_goal_box;               // 健康目标
    MsgReconnectSensor reconnect_sensor_box;     // 重新连接传感器
    MsgSensorLowPower sensor_low_power_box;      // 传感器低电量
    MsgGiveUpSport sport_abort_tip_box;          // 运动中止提示
    MsgSaveSport sport_save_box;                 // 保存提示
    MsgSensorConnReachedLimit sensor_conn_reached_limit_box;
    MsgSportingLap sporting_lap_box;
    MsgHeartRate heart_rate_box;
    MsgHeartRateNotice heart_rate_notice_box;
    MsgToastTip toast_tip_box;
    MsgAOD aod_tip_box;   //常亮弹窗提醒
    MsgDecodeERR decode_err_box;   //文件解析失败
    MsgDND dnd_tip_box;   //dnd弹窗提醒
    MsgPowerSave power_save_box; //省电模式弹窗
    MsgPowerSaveUnlock power_save_unlock; //省电模式解锁弹窗
    MsgChargeRing charge_ring; //充电圆环弹窗
    MsgUnderVolOff under_voltage; //低压、关机 弹窗
    MsgUnderVolTwenty under_voltage_twenty; //20%低电量提醒
    MsgSportRemind msg_sport_remind; //运动提醒
    MsgTipSelect msg_tip_select; // 信息确认弹窗(活动文件删除/系统还原/恢复出厂)
    MsgAltimterIcon altimter_icon_box; //高度校准失败
    MsgAltimterAcl altimter_acl_box; //高度校准完成
    MsgLoading loading_box; //加载弹窗
    MsgSpo2 spo2_push_box; //血氧推送弹窗
    MsgPressure pressure_push_box; //压力推送弹窗
    MsgPressureRemind pressure_remind_box; // 压力提醒弹窗
    MsgPowerReminder power_consume_remind_box; // 电量消耗提醒弹窗
    MsgAlarmReminder alarm_reminder_box; // 闹钟时间提醒弹窗
    MsgAlarmTip alarm_tip_box; // 闹钟到期弹窗
    MsgVrbBackward vrb_backward_box; // 虚拟兔子请加速提醒
    MsgTimerEnd timer_end_box; // 计时器到期弹窗
    MsgSleepBed sleep_bed_box; // 就寝提醒弹窗
    MsgUnlock watch_unlock; // 手表解锁动画弹窗
    MsgDelTrainingCourse del_training_course_box; // 删除训练课程弹窗
    MsgFecNotCon msgFecNotCon; // FEC未连接提示
    MsginnerTraintip msginnerTrainTip;
    MsgTriathlonTip msgTriathlonTip; // 铁人三项运动弹窗
    MsgBindWatchSuccess bind_watch_suceess_box; // 手表绑定成功弹窗
    MsgBindWatchFail bind_watch_fail_box; // 手表绑定失败弹窗
    MsgUnbindWatch unbind_watch_box; // app解绑手表弹窗
    MsgSportingGpsState msg_sporting_gps_state; // 运动中gps状态
    QwMsgBottomToast msg_bottom_toast; // 底部提示
    MsgAchReset msg_ach_reset; // 成就重置弹窗
    MsgAchUpdate msg_ach_update; // 成就更新弹窗
    MsgFtpUpdate msg_ftp_update; // FTP更新弹窗
    MsgLtHrUpdate msg_lt_hr_update; // LT HR更新弹窗
    MsgMaxHrUpdate msg_max_hr_update; // MAX HR更新弹窗
    MsgNaviBack msg_navi_back; // 导航返回弹窗
    MsgSportStartCountDown msg_sport_start_count_down; // 运动开始倒计时
    MsgTodayTrainTip msg_today_train_tip; // 今日训练提示
    MsgOTAFinish msg_ota_finish; // OTA升级完成弹窗
    MsgAlipay msg_alipay; // 支付宝弹窗
    MsgAlipayStatus msg_alipay_status; // 支付宝状态弹窗
    MsgAlipayConfirm msg_alipay_confirm; // 支付宝确认弹窗
    MsgFindWatch msg_find_watch; // 查找手表弹窗
    MsgDataSync msg_data_sync; // 数据同步提醒弹窗
    MsgDrinkWater msg_drink_Water;
};
