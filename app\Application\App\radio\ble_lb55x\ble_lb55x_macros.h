/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ble_lb55x_macros.h
@Time    :   2025/07/11 18:39:21
*
**************************************************************************/

#ifndef _BLE_LB55X_MACROS_H_
#define _BLE_LB55X_MACROS_H_
#include "qw_user_debug.h"

#define BLE_PERIPHERAL_HCPU 1
#define BLE_PERIPHERAL_LCPU 2

#define BLE_PERIPHERAL_CHIP BLE_PERIPHERAL_HCPU


/**@brief Macro for converting milliseconds to ticks.
 *
 * @param[in] TIME          Number of milliseconds to convert.
 * @param[in] RESOLUTION    Unit to be converted to in [us/ticks].
 */
#define MSEC_TO_UNITS(TIME, RESOLUTION) (((TIME) * 1000) / (RESOLUTION))

#define UNINT_1_25_MS                   1250
#define BLE_CONNECTION_MIN_INTERVAL     15
#define BLE_CONNECTION_MAX_INTERVAL     30
#define BLE_CONNECTION_TIMEOUT          4000
#define BLE_APP_TIMEOUT_INTERVAL (5000)

#ifdef CONFIG_PPG_BLE_FACTORY_TEST
#define BLE_INTV_MIN                    MSEC_TO_UNITS(8, UNINT_1_25_MS)
#define BLE_INTV_MAX                    MSEC_TO_UNITS(16, UNINT_1_25_MS) // >= 10ms
//#define BLE_INTV_MIN                    MSEC_TO_UNITS(BLE_CONNECTION_MIN_INTERVAL, UNINT_1_25_MS)
//#define BLE_INTV_MAX                    MSEC_TO_UNITS(BLE_CONNECTION_MAX_INTERVAL, UNINT_1_25_MS)
#else
#define BLE_INTV_MIN                    MSEC_TO_UNITS(BLE_CONNECTION_MIN_INTERVAL, UNINT_1_25_MS)
#define BLE_INTV_MAX                    MSEC_TO_UNITS(BLE_CONNECTION_MAX_INTERVAL, UNINT_1_25_MS)
#endif

#define BLE_NUS_UUID_DEBUG      (0x5E)
#define BLE_NUS_UUID_SECURITY   (0x4E)

typedef enum
{
    BLE_NUS_UUID_CH0 = 0x9E,
    BLE_NUS_UUID_CH1 = 0x8E,
    BLE_NUS_UUID_CH2 = 0x7E,
    BLE_NUS_UUID_CH3 = 0x6E
}BLE_APP_NUS_CHANNLE_UUID;

typedef enum {
    BLE_CONNECT_STATE_IDLE = 0,               //未初始化，默认状态
    BLE_CONNECT_STATE_CONNECTING = 1,         //正在连接
    BLE_CONNECT_STATE_CONNECTED = 2,        //连接成功
    BLE_CONNECT_STATE_PB_PROTOCOL_READY = 3,    //    QW私有协议层连接成功
    BLE_CONNECT_STATE_GOODIX_READY = 4,   //汇顶协议层连接成功
    BLE_CONNECT_STATE_DISCONNECTING = 5,      // 正在断开连接
    BLE_CONNECT_STATE_DISCONNECTED = 6,       //断开连接
    BLE_CONNECT_STATE_MAX = 7,                //最大值
} BLE_CONNECT_STATE_E;

#endif // _BLE_LB55X_MACROS_H_