﻿/************************************************************************
*
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   algo_service_auto_sports_ctrl.c
<AUTHOR>   yukai (<EMAIL>)
@Brief    :    自动运动控制算法组件实现
@Date    :   2024/12/27
*
**************************************************************************/

#include "algo_service_auto_sports_ctrl.h"
#include "activity_record/activity_fit_app.h"
#include "algo_service_adapter.h"
#include "algo_service_component_common.h"
#include "algo_service_component_log.h"
#include "algo_service_task.h"
#include "cfg_header_def.h"
#include "gps_bearing.h"
#include "gps_convert.h"
#include "qw_time_util.h"
#include "subscribe_data_protocol.h"
#include "subscribe_service.h"
#include "view_page_model_sports.h"
#include "workout_interface.h"

typedef algo_auto_sports_ctrl_t algo_auto_sports_ctrl_pub_t;

typedef struct
{
    AUTO_PAUSE type;
    uint32_t speed;   // 速度阈值
    uint8_t enable;   // 0:关闭 1:开启
} algo_auto_pause_config_t;

typedef struct
{
    AUTO_RECORD_LAP_TYPE type;
    uint32_t threshold;   // 阈值
    uint8_t enable;       // 0:关闭 1:开启
} algo_auto_lap_config_t;

// 输入数据
typedef struct
{
    int32_t lat;
    int32_t lon;
    uint32_t distance_lap;
    uint32_t total_timer_time_lap;
    uint32_t total_moving_time_lap;
    uint16_t count_group;   // 本组计数
    uint16_t speed; // 速度mm/s
    uint8_t move_status;    // 运动状态
    ctrl_type_e ctrl_type;
    saving_status_e saving_status;
    algo_auto_pause_config_t config_pause;   // 自动暂停配置
    algo_auto_lap_config_t config_lap;       // 自动记录圈配置
} algo_auto_sports_ctrl_sub_t;

static algo_auto_sports_ctrl_sub_t s_algo_in = {0};

// 发布数据
static algo_auto_sports_ctrl_pub_t s_algo_out = {0};

// 中间数据
static int32_t s_lap_lat = 0x7fffffff;
static int32_t s_lap_lon = 0x7fffffff;
uint8_t s_position_valid = false;   //当前位置计圈的数据无效

// 本算法打开标记
static bool s_is_open = false;

// 算法数据初始化
static void auto_sports_ctrl_data_init(void)
{
    algo_auto_sports_ctrl_pub_t *algo_out = &s_algo_out;

    algo_out->ctrl_type = enum_ctrl_null;
    s_lap_lat = 0x7fffffff;
    s_lap_lon = 0x7fffffff;
    s_position_valid = false;
}

/**
 * *********************************************************************************************
 * @name: triangle_h_calc
 * @brief: 计算三角形的高
 * @param {float} a 底边
 * @param {float} b 斜边
 * @param {float} c 斜边
 * @return {*} 高 - cm
 * *********************************************************************************************
 */
static uint32_t triangle_h_calc(float a, float b, float c)
{
    uint32_t h = 0;
    if (a > 0)
    {
        //参见三角形面积计算公式-海伦公式
        float s = (a + b + c) * (a + b - c) * (a + c - b) * (b + c - a);
        if (s > 0.0f)
        {
            h = (uint32_t) (sqrtf(s) / (2.0f * a));
        }
    }
    return h;
}

//检测是否进入位置计圈区域:当前位置与对比点的距离小于10米
static uint8_t position_enter_check(int32_t lap_lat, int32_t lap_lon, int32_t lat, int32_t lon)
{
    static float last_distance = 0.0;
    static int32_t last_lat = 0;
    static int32_t last_lon = 0;
    float last_to_cur_distance = 0.0;
    int h = 0;
    float cur_distance = 0.0;

    if (false != point_valid_check(lat, lon))
    {
        if (false != point_valid_check(last_lat, last_lon))                                          //上一次记录的GPS位置
        {
            util_posint_simple_distance_get(&last_distance, last_lat, last_lon, lap_lat, lap_lon);   // 获取上一次GPS位置和计圈点的距离：100*m
            util_posint_simple_distance_get(&last_to_cur_distance, last_lat, last_lon, lat, lon);    //获取上一次GPS位置和当前GPS位置的距离： 100*m
        }

        //计算本次两个点的距离
        util_posint_simple_distance_get(&cur_distance, lat, lon, lap_lat, lap_lon);   // 100*m

        if ((last_lat != lat) && (last_lon != lon))                                   //上次的gps位置和当前位置不同，就把当前位置记为上一次的位置
        {
            last_lat = lat;
            last_lon = lon;
        }

        if ((fabs(cur_distance) > FLOAT_EQUL_THRESHOLD) && (fabs(last_to_cur_distance) > FLOAT_EQUL_THRESHOLD))
        {
            h = triangle_h_calc(last_to_cur_distance, last_distance, cur_distance);
        }

        if (cur_distance < 1000)   //距离小于10米
        {
            return true;
        }
        else
        {
            if ((h < 1000) && (last_distance > 1000)
                && ((cur_distance * cur_distance) < (last_distance * last_distance + last_to_cur_distance * last_to_cur_distance))
                && ((last_distance * last_distance)
                    < (cur_distance * cur_distance
                       + last_to_cur_distance * last_to_cur_distance)))   //当位置点到两次GPS点轨迹的高小于10m，且两次gps点的夹角也是锐角则为true
            {
                return true;
            }
            return false;
        }
    }
    return false;
}

//检测是否离开该计圈区域:当前位置与对比点的距离大于50米,且时间不能小于10s
static uint8_t position_leave_check(int32_t lap_lat, int32_t lap_lon, int32_t lat, int32_t lon)
{
    float cur_distance = 0.0;

    if (false != point_valid_check(lat, lon))
    {
        //计算本次两个点的距离
        util_posint_simple_distance_get(&cur_distance, lat, lon, lap_lat, lap_lon);   // 100*m

        if (cur_distance > 5000)                                                      //距离大于50米
        {
            return true;
        }
        else
        {
            return false;
        }
    }
    return false;
}

//标定位置计圈的位置
static uint8_t lap_position_set(const algo_auto_sports_ctrl_sub_t *algo_in)
{
    if (false != point_valid_check(algo_in->lat, algo_in->lon))
    {
        s_lap_lat = algo_in->lat;
        s_lap_lon = algo_in->lon;
        return true;
    }
    else
    {
        return false;
    }
}

//位置计圈检测:离开该位置10m以上的距离后，再回到该位置
static int auto_lap_pos_check(const algo_auto_sports_ctrl_sub_t *algo_in)
{
    static uint8_t is_wait_enter = false;   //是否等待进入该位置,false:等待离开
    int ret = false;

    //TODO: 运动状态传递？这个条件应该放在下面的else里，即else if (p_calc_data->algorithm.is_move) //处于运动状态才进行位置计圈
    // if (!p_calc_data->algorithm.is_move)//不处于运动状态就不进行位置计圈
    // {
    //     return ret;
    // }

    if (!s_position_valid)
    {
        if (lap_position_set(algo_in))   //保存当前位置作为计圈原点
        {
            s_position_valid = true;     //当前位置有效
            is_wait_enter = false;
        }
    }
    else                     //判断当前是否满足位置计圈的条件 //else if (p_calc_data->algorithm.is_move) //处于运动状态才进行位置计圈
    {
        if (is_wait_enter)   //等待进入该位置
        {
            if (position_enter_check(s_lap_lat, s_lap_lon, algo_in->lat, algo_in->lon))
            {
                is_wait_enter = false;
                ret = true;
            }
        }
        else   //等待离开该位置
        {
            if (position_leave_check(s_lap_lat, s_lap_lon, algo_in->lat, algo_in->lon))
            {
                is_wait_enter = true;
            }
        }
    }

    return ret;
}

//距离计圈检测：本圈距离大于设定的计圈距离
static int auto_lap_dist_check(const algo_auto_sports_ctrl_sub_t *algo_in)
{
    static uint32_t s_time = 0;
    uint32_t runtime_ms = get_boot_msec();

    if (algo_in->distance_lap >= (uint64_t) algo_in->config_lap.threshold * 100 && 5000 < runtime_ms - s_time)   //本圈距离大于计圈距离
    {
        s_time = runtime_ms;                                                                                     //距离计圈会跳两圈?
        return true;
    }
    else
    {
        return false;
    }
}

//时间计圈检测：本圈时间大于设定的计圈时间
static int auto_lap_time_check(const algo_auto_sports_ctrl_sub_t *algo_in)
{
    static uint32_t s_time = 0;
    uint32_t runtime_ms = get_boot_msec();

    SPORTTYPE sport_type = get_current_sport_mode();   //运动类型
    if (sport_type == SPORTSTYPE_STRENGTH_TRAINING || sport_type == SPORTSTYPE_ROWING_MACHINE || sport_type == SPORTSTYPE_JUMP_ROPE)
    {
        if (get_activity_is_rest_in_group())
        {
            s_time = runtime_ms;
            return false;
        }
        else
        {
            if (algo_in->total_timer_time_lap != UINT32_MAX && algo_in->total_moving_time_lap >= algo_in->config_lap.threshold * 1000
                && 5000 < runtime_ms - s_time)   //本圈时间大于计圈时间
            {
                s_time = runtime_ms;             //距离计圈会跳两圈?
                return true;
            }
            else
            {
                return false;
            }
        }
    }
    else
    {
        if (algo_in->total_timer_time_lap != UINT32_MAX && algo_in->total_timer_time_lap >= algo_in->config_lap.threshold * 1000
            && 5000 < runtime_ms - s_time)   //本圈时间大于计圈时间
        {
            s_time = runtime_ms;             //距离计圈会跳两圈?
            return true;
        }
        else
        {
            return false;
        }
    }
}

//计数计圈检测：本圈时间大于设定的计圈时间
static int auto_lap_count_check(const algo_auto_sports_ctrl_sub_t *algo_in)
{
    static uint32_t s_time = 0;
    uint32_t runtime_ms = get_boot_msec();

    SPORTTYPE sport_type = get_current_sport_mode();   //运动类型
    if (sport_type == SPORTSTYPE_STRENGTH_TRAINING || sport_type == SPORTSTYPE_ROWING_MACHINE || sport_type == SPORTSTYPE_JUMP_ROPE)
    {
        if (get_activity_is_rest_in_group())
        {
            s_time = runtime_ms;
            return false;
        }
        else
        {
            if (algo_in->count_group != UINT16_MAX && algo_in->count_group >= algo_in->config_lap.threshold
                && 5000 < runtime_ms - s_time)   //本圈时间大于计圈时间
            {
                rt_kprintf("[error]auto_lap_count_check count_group = %d threshold = %d\n", algo_in->count_group, algo_in->config_lap.threshold);
                s_time = runtime_ms;   //距离计圈会跳两圈?
                return true;
            }
            else
            {
                return false;
            }
        }
    }
    else
    {
        if (algo_in->count_group != UINT16_MAX && algo_in->count_group >= algo_in->config_lap.threshold && 5000 < runtime_ms - s_time)   //本圈时间大于计圈时间
        {
            s_time = runtime_ms;                                                                                                         //距离计圈会跳两圈?
            return true;
        }
        else
        {
            return false;
        }
    }
}

// 自动计圈
evt_lap_type_e auto_lap_check(const algo_auto_sports_ctrl_sub_t *algo_in)
{
    evt_lap_type_e lap_type = enum_lap_null;
    int ret = false;

    if ((false == algo_in->config_lap.enable) || ((true == get_workout_started()) && (false == is_fec_training())))   //训练开始后，非智能骑行台训练不自动计圈
    {
        s_position_valid = false;
        return false;
    }

    //处于记录状态
    if (enum_status_saving == algo_in->saving_status)
    {
        //当前的计圈状态
        switch (algo_in->config_lap.type)
        {
        case AUTO_RECORD_LAP_DISTANCE:   //距离计圈
            ret = auto_lap_dist_check(algo_in);
            s_position_valid = false;
            lap_type = ret ? enum_lap_distance : enum_lap_null;
            break;
        case AUTO_RECORD_LAP_TIMES:   //时间计圈
            ret = auto_lap_time_check(algo_in);
            s_position_valid = false;
            lap_type = ret ? enum_lap_time : enum_lap_null;
            break;
        case AUTO_RECORD_LAP_POSITION:   //位置计圈
            ret = auto_lap_pos_check(algo_in);
            lap_type = ret ? enum_lap_position : enum_lap_null;
            break;
        case AUTO_RECORD_LAP_NUMS:
            ret = auto_lap_count_check(algo_in);
            s_position_valid = false;
            lap_type = ret ? enum_lap_count : enum_lap_null;
            break;

        default:
            break;
        }
    }
    else if (enum_status_free == algo_in->saving_status)   //解决自动暂停时更新了计圈位置的问题
    {
        s_position_valid = false;                          //位置计圈的初始值不正确
    }

    return lap_type;
}

#define AUTO_PAUSE_TIME    0   //根据需求自动暂停从速度为0到暂停提示一共3s, 静止判断2s,自动暂停判断1s
#define AUTO_CONTINUE_TIME 0   //根据需求自动骑行从速度为1.5m/s到骑行提示一共3s, 运动判断2s,自动骑行判断1s
static uint8_t s_autoPauseTimeout;
static uint8_t s_autoContinueTimeout;

//清除自动暂停的时间
static void auto_pause_clr_time(void)
{
    s_autoPauseTimeout = AUTO_PAUSE_TIME;
    s_autoContinueTimeout = AUTO_CONTINUE_TIME;
}

//自动暂停骑行
static int auto_pause_check(const algo_auto_sports_ctrl_sub_t *algo_in)
{
    int ret = false;
    SPORTTYPE sport_type = get_current_sport_mode();
    int16_t auto_pause_speed = 0x7fff;
    if (algo_in->config_pause.enable)
    {
        auto_pause_speed = (STILLNESS == algo_in->config_pause.type ? 0 : algo_in->config_pause.speed);
    }

    //自动暂停功能未开启则直接退出
    if (0x7fff == auto_pause_speed)
    {
        return ret;
    }

    //处于记录状态
    if (enum_status_saving == algo_in->saving_status)
    {
        //不处于运动状态（速度无效且ant外设无效）
        // if (false == move_status_check(algo_in) || algo_in->speed < auto_pause_speed) //速度小于自动暂停速度则强制暂停
        if (false == algo_in->move_status || algo_in->speed < auto_pause_speed)
        {
            if (0 == s_autoPauseTimeout)
            {
                auto_pause_clr_time();
                ret = true;   //自动暂停骑行
            }
            else
            {
                s_autoPauseTimeout--;
            }
        }
        else
        {
            auto_pause_clr_time();
        }
    }

    return ret;
}

//自动继续骑行
static int auto_resume_check(const algo_auto_sports_ctrl_sub_t *algo_in)
{
    int ret = false;
    SPORTTYPE sport_type = get_current_sport_mode();
    int16_t auto_pause_speed = STILLNESS == algo_in->config_pause.type ? 0 : algo_in->config_pause.speed;

    //当前为自动暂停时,如果处于运动状态,可自动恢复骑行状态,手动暂停只能手动恢复
    if (enum_status_pause_auto == algo_in->saving_status)
    {
        //处于运动状态（ant外设有效或者速度有效）
        // if (false != move_status_check(algo_in) && algo_in->speed >= auto_pause_speed) //在运动状态且速度大于等于自动暂停速度才继续
        if (false != algo_in->move_status && algo_in->speed >= auto_pause_speed)
        {
            if (0 == s_autoContinueTimeout)
            {
                auto_pause_clr_time();
                ret = true;   //自动继续骑行
            }
            else
            {
                s_autoContinueTimeout--;
            }
        }
        else
        {
            auto_pause_clr_time();
        }
    }

    return ret;
}

/**
 * @brief 算法处理
 *
 * @param algo_out 输出数据
 * @param algo_in 输入数据
 */
static void algo_auto_sports_ctrl_deal(algo_auto_sports_ctrl_pub_t *algo_out, const algo_auto_sports_ctrl_sub_t *algo_in)
{
    //清除上一次的事件
    algo_out->ctrl_type = enum_ctrl_null;
    algo_out->lap_type = enum_lap_null;

    if (auto_pause_check(algo_in))                                              //自动暂停功能有效
    {
        algo_out->ctrl_type = enum_ctrl_pause_auto;                             //自动暂停
    }
    else if (auto_resume_check(algo_in))                                        //自动继续功能有效
    {
        algo_out->ctrl_type = enum_ctrl_resume;                                 //继续
    }
    else if (enum_lap_null != (algo_out->lap_type = auto_lap_check(algo_in)))   //自动计圈功能有效
    {
        algo_out->ctrl_type = enum_ctrl_lap;                                    //自动计圈
    }
}

/**
 * @brief 算法控制
 *
 * @param algo_out 输出数据
 * @param ctrl_type 控制类型
 */
// static void algo_auto_sports_ctrl_ctrl(algo_auto_sports_ctrl_pub_t *algo_out, ctrl_type_e ctrl_type)
// {}

/**
 * @brief 算法输入订阅处理
 *
 * @param in 输入数据
 * @param len 输入数据长度
 */
static void algo_gps_data_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_AUTO_SPORTS_CTRL;
    head.input_type = DATA_ID_ALGO_GPS_DATA;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_distance_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_AUTO_SPORTS_CTRL;
    head.input_type = DATA_ID_ALGO_DISTANCE;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_timer_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_AUTO_SPORTS_CTRL;
    head.input_type = DATA_ID_ALGO_TIMER;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_move_status_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_AUTO_SPORTS_CTRL;
    head.input_type = DATA_ID_ALGO_MOVE_STATUS;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

static void algo_jumprope_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_AUTO_SPORTS_CTRL;
    head.input_type = DATA_ID_ALGO_JUMPROPE;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法控制订阅处理
 *
 * @param in 控制数据
 * @param len 数据长度
 */
static void algo_sports_ctrl_in_callback(const void *in, uint32_t len)
{
    // 驱动数据发布后通知算法线程
    algo_svr_head_t head;
    head.cid = ALGO_CMD_ID_FEED;
    head.algo_type = ALGO_TYPE_AUTO_SPORTS_CTRL;
    head.input_type = DATA_ID_EVENT_SPORTS_CTRL;
    if (send_msg_to_algo_fwk(head, in, len) != 0)
    {
        ALGO_COMP_LOG_E("%s send_msg_to_algo_fwk error", __FUNCTION__);
    }
}

/**
 * @brief 算法输出订阅处理
 *
 * @param out 输出数据
 * @param len 输出数据长度
 */
static void algo_auto_sports_ctrl_out_callback(const void *out, uint32_t len)
{
    // 算法输出后发布
    if (qw_dataserver_publish_id(DATA_ID_ALGO_AUTO_SPORTS_CTRL, out, len) != ERRO_CODE_OK)
    {
        ALGO_COMP_LOG_E("%s publish error", __FUNCTION__);
    }
}

/**
 * @brief 订阅算法定义室内
 */
static algo_topic_node_t s_algo_topic_node_indoor[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_distance",
        .topic_id = DATA_ID_ALGO_DISTANCE,
        .callback = algo_distance_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_timer",
        .topic_id = DATA_ID_ALGO_TIMER,
        .callback = algo_timer_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_ALG_NAME_MOVE_STATUS,
        .topic_id = DATA_ID_ALGO_MOVE_STATUS,
        .callback = algo_move_status_in_callback,
    },
};
/**
 * @brief 订阅算法定义室外
 */
static algo_topic_node_t s_algo_topic_node_outdoor[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_gps_data",
        .topic_id = DATA_ID_ALGO_GPS_DATA,
        .callback = algo_gps_data_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_distance",
        .topic_id = DATA_ID_ALGO_DISTANCE,
        .callback = algo_distance_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_timer",
        .topic_id = DATA_ID_ALGO_TIMER,
        .callback = algo_timer_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_ALG_NAME_MOVE_STATUS,
        .topic_id = DATA_ID_ALGO_MOVE_STATUS,
        .callback = algo_move_status_in_callback,
    },
};
/**
 * @brief 订阅算法定义记组运动
 */
static algo_topic_node_t s_algo_topic_node_group[] = {
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_ALG_NAME_COUNT_TIMES,
        .topic_id = DATA_ID_ALGO_JUMPROPE,
        .callback = algo_jumprope_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = "algo_timer",
        .topic_id = DATA_ID_ALGO_TIMER,
        .callback = algo_timer_in_callback,
    },
    {
        .optional_config = DEFAULT_OPTIONAL_CONFIG,
        .topic_name = CONFIG_QW_EVENT_NAME_SPORTS_CTRL,
        .topic_id = DATA_ID_EVENT_SPORTS_CTRL,
        .callback = algo_sports_ctrl_in_callback,
    },
};

/**
 * @brief 算法初始化,上电运行
 *
 * @return int32_t 初始化结果
 */
static int32_t algo_auto_sports_ctrl_init(void)
{
    return 0;
}

//
// 检查特定运动类型是否支持自动暂停功能
static int sport_type_auto_pause_avail(SPORTTYPE sport_type)
{
    int ret = false;

    switch (sport_type)
    {
    case SPORTSTYPE_RUNNING:            // 跑步
    case SPORTSTYPE_PLAYGROUND:         // 操场跑步
    case SPORTSTYPE_TRAIL_RUNNING:      // 越野跑
    case SPORTSTYPE_WALKING:            // 步行
    case SPORTSTYPE_CYCLING:            // 骑行
    case SPORTSTYPE_ROAD_CYCLING:       // 公路骑行
    case SPORTSTYPE_MOUNTAIN_CYCLING:   // 山地骑行
    case SPORTSTYPE_COMMUTING:          // 骑车通勤
    case SPORTSTYPE_TRIP_CYCLING:       // 长途骑行
        ret = true;
        break;

    case SPORTSTYPE_TREADMILL:              // 跑步机
    case SPORTSTYPE_INDOOR_RUNNING:         // 室内步行
    case SPORTSTYPE_INDOOR_CYCLING:         // 室内骑行
    case SPORTSTYPE_POOL_SWIMMING:          // 泳池游泳
    case SPORTSTYPE_OPEN_WATER_SWIMMING:    // 公共水域游泳
    case SPORTSTYPE_STRENGTH_TRAINING:      // 力量训练
    case SPORTSTYPE_INDOOR_AEROBIC:         // 室内有氧
    case SPORTSTYPE_ELLIPTICAL_MACHINE:     // 椭圆机
    case SPORTSTYPE_ROWING_MACHINE:         // 划船机
    case SPORTSTYPE_MOUNTAINEERING:         // 登山
    case SPORTSTYPE_HIKING:                 // 徒步
    case SPORTSTYPE_SKIING:                 // 滑雪
    case SPORTSTYPE_OUTDOOR_AEROBIC:        // 户外有氧
    case SPORTSTYPE_JUMP_ROPE:              // 跳绳
    case SPORTSTYPE_TRIATHLON:              // 铁人三项
    case SPORTSTYPE_COMPOUND_MOTION:        // 复合运动
    case SPORTSTYPE_EXERCISE:               // 锻炼
    case SPORTSTYPE_HIIT:                   // 高强度间歇运动
    case SPORTSTYPE_ROWING_BOAT:            // 赛艇
    case SPORTSTYPE_PULP_BOARD:             // 浆板
    case SPORTSTYPE_FITNESS:                // 健身
    case SPORTSTYPE_SNOWBOARDING:           // 单板滑雪
    case SPORTSTYPE_SNOWBOARDING_2:         // 双板滑雪
    case SPORTSTYPE_CROSS_COUNTRY_SKIING:   // 越野滑雪
    case SPORTSTYPE_YOGA:                   // 瑜伽
    case SPORTSTYPE_PILATES:                // 普拉提
    case SPORTSTYPE_OUTDOOR_FRISBEE:        // 户外飞盘
        ret = false;
        break;

    default:
        break;
    }
    return ret;
}

/**
 * @brief 算法open
 *
 * @return int32_t 结果
 */
static int32_t algo_auto_sports_ctrl_open(void)
{
    if (s_is_open)
    {
        ALGO_COMP_LOG_I("%s already open", __FUNCTION__);
        return 0;
    }

    ALGO_COMP_LOG_D("%s open", __FUNCTION__);

    int32_t ret = -1;
    SPORTTYPE sport_type = get_current_sport_mode();
    s_is_open = true;
    auto_sports_ctrl_data_init();   //算法数据初始化

    if (sport_type == SPORTSTYPE_STRENGTH_TRAINING || sport_type == SPORTSTYPE_ROWING_MACHINE || sport_type == SPORTSTYPE_JUMP_ROPE)
    {
        ret = algo_topic_list_subscribe(s_algo_topic_node_group, sizeof(s_algo_topic_node_group) / sizeof(s_algo_topic_node_group[0]));
    }
    else if (get_sport_type_is_outdoor((SPORTTYPE) get_activity_sport_type()))
    {
        ret = algo_topic_list_subscribe(s_algo_topic_node_outdoor, sizeof(s_algo_topic_node_outdoor) / sizeof(s_algo_topic_node_outdoor[0]));
    }
    else
    {
        ret = algo_topic_list_subscribe(s_algo_topic_node_indoor, sizeof(s_algo_topic_node_indoor) / sizeof(s_algo_topic_node_indoor[0]));
    }

    return ret;
}

/**
 * @brief 算法feed
 *
 * @param input_type feed数据类型
 * @param data feed数据
 * @param len 数据长度
 * @return int32_t 结果
 */
static int32_t algo_auto_sports_ctrl_feed(uint32_t input_type, void *data, uint32_t len)
{
    algo_auto_sports_ctrl_sub_t *algo_in = &s_algo_in;
    algo_auto_sports_ctrl_pub_t *algo_out = &s_algo_out;

    switch (input_type)
    {
    case DATA_ID_ALGO_GPS_DATA:
    {
        const gps_pub_t *gps_data = (const gps_pub_t *) data;
        algo_in->lat = minmea_to_int(&gps_data->latitude, DEF_MINMEA_SCALE);
        algo_in->lon = minmea_to_int(&gps_data->longitude, DEF_MINMEA_SCALE);
        break;
    }
    case DATA_ID_ALGO_DISTANCE:
    {
        const algo_distance_pub_t *distance_data = (const algo_distance_pub_t *) data;
        algo_in->distance_lap = distance_data->distance_lap;
        break;
    }
    case DATA_ID_ALGO_TIMER:
    {
        const algo_timer_pub_t *timer_data = (const algo_timer_pub_t *) data;
        algo_in->total_timer_time_lap = timer_data->timer_lap.timer_time;
        algo_in->total_moving_time_lap = timer_data->timer_lap.moving_time;
        break;
    }
    case DATA_ID_ALGO_MOVE_STATUS:
    {
        const algo_move_status_pub_t *move_data = (const algo_move_status_pub_t *) data;
        algo_in->move_status = move_data->move_status;
        algo_in->speed = move_data->speed;
        break;
    }
    case DATA_ID_ALGO_JUMPROPE:
    {
        const algo_count_times_sports_pub_t *fitness_data = (const algo_count_times_sports_pub_t *) data;
        algo_in->count_group = fitness_data->count_group;
        break;
    }
    case DATA_ID_EVENT_SPORTS_CTRL:
    {
        const algo_sports_ctrl_t *sports_ctrl = (const algo_sports_ctrl_t *) data;
        algo_in->saving_status = sports_ctrl->saving_status;

        if (sports_ctrl->ctrl_type != enum_ctrl_null)
        {
            //算法控制
            // algo_auto_sports_ctrl_ctrl(algo_out, sports_ctrl->ctrl_type);
        }
        else
        {
            // 更新配置
            algo_in->config_pause.enable = get_sport_auto_pause_en(get_current_sport_mode());                                // 是否启用自动暂停
            algo_in->config_pause.type = get_sport_auto_pause_type(get_current_sport_mode());                                // 自动暂停类型
            algo_in->config_pause.speed = get_sport_auto_pause_min_value(get_current_sport_mode());                          // 自动暂停阈值

            algo_in->config_lap.enable = get_auto_record_lap(get_current_sport_mode());                                      // 是否启用自动记圈
            algo_in->config_lap.type = get_auto_record_lap_type(get_current_sport_mode());                                   // 自动记圈类型
            algo_in->config_lap.threshold = get_auto_record_lap_value(get_current_sport_mode(), algo_in->config_lap.type);   // 自动记圈阈值

            //算法处理
            algo_auto_sports_ctrl_deal(algo_out, algo_in);

            //数据发布
            algo_auto_sports_ctrl_out_callback(algo_out, sizeof(algo_auto_sports_ctrl_pub_t));
        }
        break;
    }
    default:
        break;
    }
    return 0;
}

/**
 * @brief 算法close
 *
 * @return int32_t 结果
 */
static int32_t algo_auto_sports_ctrl_close(void)
{
    if (!s_is_open)
    {
        ALGO_COMP_LOG_I("%s already close", __FUNCTION__);
        return 0;
    }

    SPORTTYPE sport_type = get_current_sport_mode();

    if (sport_type == SPORTSTYPE_STRENGTH_TRAINING || sport_type == SPORTSTYPE_ROWING_MACHINE || sport_type == SPORTSTYPE_JUMP_ROPE)
    {
        algo_topic_list_unsubscribe(s_algo_topic_node_group, sizeof(s_algo_topic_node_group) / sizeof(s_algo_topic_node_group[0]));
    }
    else if (get_sport_type_is_outdoor((SPORTTYPE) get_activity_sport_type()))
    {
        algo_topic_list_unsubscribe(s_algo_topic_node_outdoor, sizeof(s_algo_topic_node_outdoor) / sizeof(s_algo_topic_node_outdoor[0]));
    }
    else
    {
        algo_topic_list_unsubscribe(s_algo_topic_node_indoor, sizeof(s_algo_topic_node_indoor) / sizeof(s_algo_topic_node_indoor[0]));
    }

    s_is_open = false;
    ALGO_COMP_LOG_D("%s close", __FUNCTION__);
    return 0;
}

// 算法组件实现
static algo_compent_ops_t s_auto_sports_ctrl_algo = {
    .init = algo_auto_sports_ctrl_init,
    .open = algo_auto_sports_ctrl_open,
    .feed = algo_auto_sports_ctrl_feed,
    .close = algo_auto_sports_ctrl_close,
    .ioctl = NULL,
};

/**
 * @brief 组件注册
 *
 * @return int32_t 结果
 */
int32_t register_auto_sports_ctrl_algo(void)
{
    algo_compnent_register(ALGO_TYPE_AUTO_SPORTS_CTRL, &s_auto_sports_ctrl_algo);
    return 0;
}