0000000000000000000000000000000000000000 6163754d711afe3586024cbf14a5994692d70f29 yanxuqiang <<EMAIL>> 1752630863 +0800
6163754d711afe3586024cbf14a5994692d70f29 6163754d711afe3586024cbf14a5994692d70f29 yanxuqiang <<EMAIL>> 1752631103 +0800
6163754d711afe3586024cbf14a5994692d70f29 **************************************** yanxuqiang <<EMAIL>> 1752636880 +0800	commit: Sonar: 解决QwWidget模块sonar bugs
**************************************** **************************************** yanxuqiang <<EMAIL>> 1752637876 +0800	commit (amend): Sonar: 解决QwWidget模块sonar bugs
**************************************** **************************************** yanxuqiang <<EMAIL>> 1752645199 +0800	commit (amend): Sonar: 解决QwWidget模块sonar bugs
**************************************** 530200e3f4950dbc9b830c16e9c86a38da95a901 yanxuqiang <<EMAIL>> 1752829815 +0800	pull --rebase (start): checkout 530200e3f4950dbc9b830c16e9c86a38da95a901
530200e3f4950dbc9b830c16e9c86a38da95a901 530200e3f4950dbc9b830c16e9c86a38da95a901 yanxuqiang <<EMAIL>> 1752829815 +0800	pull --rebase (finish): returning to refs/heads/develop
530200e3f4950dbc9b830c16e9c86a38da95a901 f5a41e2665b97343c75556056f7991594da8d3dc yanxuqiang <<EMAIL>> 1754358300 +0800	pull --rebase: Fast-forward
f5a41e2665b97343c75556056f7991594da8d3dc 7fdc85bcae041439c530b5effba395495428fed1 yanxuqiang <<EMAIL>> 1754388158 +0800	pull --rebase: Fast-forward
7fdc85bcae041439c530b5effba395495428fed1 bb85d03ef080dfb6fec797809ed473b736611015 yanxuqiang <<EMAIL>> 1754530165 +0800	pull --rebase: Fast-forward
bb85d03ef080dfb6fec797809ed473b736611015 bb85d03ef080dfb6fec797809ed473b736611015 yanxuqiang <<EMAIL>> 1754538820 +0800	reset: moving to HEAD
bb85d03ef080dfb6fec797809ed473b736611015 bc541ce8e83e72ea246d78e4e3aeacade65bde7b yanxuqiang <<EMAIL>> 1754538826 +0800	pull --rebase: Fast-forward
bc541ce8e83e72ea246d78e4e3aeacade65bde7b 70ad1166ccbdcc375745cf000749075e9c9bb360 yanxuqiang <<EMAIL>> 1754538864 +0800	commit: BUG: 解决运动结算页记圈页面数据单位显示不正确问题
70ad1166ccbdcc375745cf000749075e9c9bb360 add245350d84766d2433bd38435057dd0400a91f yanxuqiang <<EMAIL>> 1754538869 +0800	commit (amend): BUG: 解决运动结算页记圈页面数据单位显示不正确问题
add245350d84766d2433bd38435057dd0400a91f add245350d84766d2433bd38435057dd0400a91f yanxuqiang <<EMAIL>> 1754634757 +0800	reset: moving to HEAD
add245350d84766d2433bd38435057dd0400a91f fa675d5059072344049e4ddaa42e7de11de64f4a yanxuqiang <<EMAIL>> 1754634763 +0800	pull --rebase (start): checkout fa675d5059072344049e4ddaa42e7de11de64f4a
fa675d5059072344049e4ddaa42e7de11de64f4a fa675d5059072344049e4ddaa42e7de11de64f4a yanxuqiang <<EMAIL>> 1754634763 +0800	pull --rebase (finish): returning to refs/heads/develop
fa675d5059072344049e4ddaa42e7de11de64f4a 4f040015853d7892e0076730efa4b648dfa1c98f yanxuqiang <<EMAIL>> 1754636515 +0800	commit: GUI: 查找手表弹窗添加震动相关接口
4f040015853d7892e0076730efa4b648dfa1c98f 341c6277838dc13600332ca8c21518584b61bae6 yanxuqiang <<EMAIL>> 1754643396 +0800	pull --rebase: Fast-forward
341c6277838dc13600332ca8c21518584b61bae6 341c6277838dc13600332ca8c21518584b61bae6 yanxuqiang <<EMAIL>> 1754884627 +0800	reset: moving to HEAD
341c6277838dc13600332ca8c21518584b61bae6 fda0dd9edde4443d541aa9c191ef47ca16d76534 yanxuqiang <<EMAIL>> 1754884630 +0800	pull --rebase: Fast-forward
fda0dd9edde4443d541aa9c191ef47ca16d76534 d836528e64980c33f02317914320919ceefadfe5 yanxuqiang <<EMAIL>> 1754884647 +0800	commit: GUI: 优化表盘存储缩略图逻辑
d836528e64980c33f02317914320919ceefadfe5 2a86ac4c4434c9761c332acc30994c10a0e67b84 yanxuqiang <<EMAIL>> 1754884651 +0800	commit (amend): GUI: 优化表盘存储缩略图逻辑
2a86ac4c4434c9761c332acc30994c10a0e67b84 2a86ac4c4434c9761c332acc30994c10a0e67b84 yanxuqiang <<EMAIL>> 1754969149 +0800	reset: moving to HEAD
2a86ac4c4434c9761c332acc30994c10a0e67b84 f37d3051861815001ce373b6923a04ac119d67a4 yanxuqiang <<EMAIL>> 1754969151 +0800	pull --rebase: Fast-forward
f37d3051861815001ce373b6923a04ac119d67a4 89f9687ad6d1289302f6731ff35097475ce5950b yanxuqiang <<EMAIL>> 1754969304 +0800	commit: GUI: 优化表盘存储逻辑,直接把已经显示的表盘buf存起来，不需要再去重新创建
89f9687ad6d1289302f6731ff35097475ce5950b 4bf8145c3745299c7c7bed5dca6f41295ed02924 yanxuqiang <<EMAIL>> 1754969321 +0800	commit (amend): GUI: 优化表盘存储逻辑,直接把已经显示的表盘buf存起来
4bf8145c3745299c7c7bed5dca6f41295ed02924 d0220e867cf4e614bda9e9e4448a08b6743c218b yanxuqiang <<EMAIL>> 1754970234 +0800	pull --rebase (start): checkout d0220e867cf4e614bda9e9e4448a08b6743c218b
d0220e867cf4e614bda9e9e4448a08b6743c218b dcda9ac4e79c2e832456eb4757d3d6a1e019c2df yanxuqiang <<EMAIL>> 1754970234 +0800	pull --rebase (pick): GUI: 优化表盘存储逻辑,直接把已经显示的表盘buf存起来
dcda9ac4e79c2e832456eb4757d3d6a1e019c2df dcda9ac4e79c2e832456eb4757d3d6a1e019c2df yanxuqiang <<EMAIL>> 1754970234 +0800	pull --rebase (finish): returning to refs/heads/develop
dcda9ac4e79c2e832456eb4757d3d6a1e019c2df c04df39d23864af56a5a5cca9d8faaa5b041fc25 yanxuqiang <<EMAIL>> 1755001809 +0800	commit (amend): GUI: 优化表盘存储逻辑
c04df39d23864af56a5a5cca9d8faaa5b041fc25 579f83603306e61776aa2d1643265bae3f94335d yanxuqiang <<EMAIL>> 1755002057 +0800	pull --rebase (start): checkout 579f83603306e61776aa2d1643265bae3f94335d
579f83603306e61776aa2d1643265bae3f94335d 60a568e877efba682044f1fc2ad4c9271623f94a yanxuqiang <<EMAIL>> 1755002057 +0800	pull --rebase (pick): GUI: 优化表盘存储逻辑
60a568e877efba682044f1fc2ad4c9271623f94a 60a568e877efba682044f1fc2ad4c9271623f94a yanxuqiang <<EMAIL>> 1755002057 +0800	pull --rebase (finish): returning to refs/heads/develop
60a568e877efba682044f1fc2ad4c9271623f94a debf61450ed958fa7eaeb7a6cabd45b3450e3afe yanxuqiang <<EMAIL>> 1755072142 +0800	commit (amend): GUI: 优化表盘存储逻辑
debf61450ed958fa7eaeb7a6cabd45b3450e3afe 8b0483635d5913463fb1925719a7874fc7b55a5f yanxuqiang <<EMAIL>> 1755072359 +0800	pull --rebase (start): checkout 8b0483635d5913463fb1925719a7874fc7b55a5f
8b0483635d5913463fb1925719a7874fc7b55a5f de8b9e36cd346f34b37ff064a0d20ee29317d3c9 yanxuqiang <<EMAIL>> 1755072359 +0800	pull --rebase (pick): GUI: 优化表盘存储逻辑
de8b9e36cd346f34b37ff064a0d20ee29317d3c9 de8b9e36cd346f34b37ff064a0d20ee29317d3c9 yanxuqiang <<EMAIL>> 1755072359 +0800	pull --rebase (finish): returning to refs/heads/develop
de8b9e36cd346f34b37ff064a0d20ee29317d3c9 1fc50c5ed54dc1c21cc906a9892ea3ff65d25754 yanxuqiang <<EMAIL>> 1755153857 +0800	commit (amend): GUI: 优化表盘存储逻辑
1fc50c5ed54dc1c21cc906a9892ea3ff65d25754 a281b9a439f03bc4f8bbf537121503d2da73d6f3 yanxuqiang <<EMAIL>> 1755226964 +0800	commit (amend): GUI: 优化表盘存储逻辑
a281b9a439f03bc4f8bbf537121503d2da73d6f3 60ed9a9355ce275e6b67a7f3a535790027d09137 yanxuqiang <<EMAIL>> 1755228514 +0800	pull --rebase (start): checkout 60ed9a9355ce275e6b67a7f3a535790027d09137
60ed9a9355ce275e6b67a7f3a535790027d09137 60ed9a9355ce275e6b67a7f3a535790027d09137 yanxuqiang <<EMAIL>> 1755228514 +0800	pull --rebase (finish): returning to refs/heads/develop
60ed9a9355ce275e6b67a7f3a535790027d09137 f5273f64628bad17b322d7c9791a9f3af0624fe3 yanxuqiang <<EMAIL>> 1755228615 +0800	checkout: moving from develop to wr02_release
f5273f64628bad17b322d7c9791a9f3af0624fe3 f5273f64628bad17b322d7c9791a9f3af0624fe3 yanxuqiang <<EMAIL>> 1755239085 +0800	reset: moving to f5273f64628bad17b322d7c9791a9f3af0624fe3
f5273f64628bad17b322d7c9791a9f3af0624fe3 00e8a42d6ff8fd79c9592f5f4a7a41cbf3f3170d yanxuqiang <<EMAIL>> 1755240136 +0800	commit: GUI: 完成V0.85需求
00e8a42d6ff8fd79c9592f5f4a7a41cbf3f3170d 953aec63cd201932e6bca3487df6512295e1ca77 yanxuqiang <<EMAIL>> 1755240145 +0800	commit (amend): GUI: 完成V0.85需求
953aec63cd201932e6bca3487df6512295e1ca77 ef1a2a44b2eb64358aa5bbc70f4ed45492156f8d yanxuqiang <<EMAIL>> 1755253111 +0800	pull --rebase (start): checkout ef1a2a44b2eb64358aa5bbc70f4ed45492156f8d
ef1a2a44b2eb64358aa5bbc70f4ed45492156f8d ef1a2a44b2eb64358aa5bbc70f4ed45492156f8d yanxuqiang <<EMAIL>> 1755253111 +0800	pull --rebase (finish): returning to refs/heads/wr02_release
ef1a2a44b2eb64358aa5bbc70f4ed45492156f8d f8e6de38d129df8398709e1596f6b5c589f3d69c yanxuqiang <<EMAIL>> 1755309155 +0800	pull --rebase: Fast-forward
f8e6de38d129df8398709e1596f6b5c589f3d69c 6ad1a9f3e7f067b6409b434765f1fcd22a91eddb yanxuqiang <<EMAIL>> 1755323633 +0800	pull --rebase: Fast-forward
6ad1a9f3e7f067b6409b434765f1fcd22a91eddb af148186a0de62f9c27a748f366b9f96496965df yanxuqiang <<EMAIL>> 1755325665 +0800	pull --rebase: Fast-forward
af148186a0de62f9c27a748f366b9f96496965df 562ff7e353491f9d33a4515c863bdac25083b8f6 yanxuqiang <<EMAIL>> 1755329506 +0800	pull --rebase: Fast-forward
562ff7e353491f9d33a4515c863bdac25083b8f6 60ed9a9355ce275e6b67a7f3a535790027d09137 yanxuqiang <<EMAIL>> 1755566787 +0800	checkout: moving from wr02_release to develop
60ed9a9355ce275e6b67a7f3a535790027d09137 562ff7e353491f9d33a4515c863bdac25083b8f6 yanxuqiang <<EMAIL>> 1755566828 +0800	checkout: moving from develop to wr02_release
562ff7e353491f9d33a4515c863bdac25083b8f6 b441b39fd862b30a0dbf07508218067f88d56c3e yanxuqiang <<EMAIL>> 1755566865 +0800	reset: moving to b441b39fd862b30a0dbf07508218067f88d56c3e
b441b39fd862b30a0dbf07508218067f88d56c3e e8f82b685b627b12791608dc23b6628501e2e2ac yanxuqiang <<EMAIL>> 1755566919 +0800	reset: moving to e8f82b685b627b12791608dc23b6628501e2e2ac
e8f82b685b627b12791608dc23b6628501e2e2ac f6bf6843a5790427405546816fd01edec0a7b431 yanxuqiang <<EMAIL>> 1755566928 +0800	pull --rebase: Fast-forward
f6bf6843a5790427405546816fd01edec0a7b431 60ed9a9355ce275e6b67a7f3a535790027d09137 yanxuqiang <<EMAIL>> 1755566940 +0800	checkout: moving from wr02_release to develop
60ed9a9355ce275e6b67a7f3a535790027d09137 1dd74975a7db302c0de525f72fc1754db6a9d5c9 yanxuqiang <<EMAIL>> 1755566944 +0800	pull --rebase: Fast-forward
1dd74975a7db302c0de525f72fc1754db6a9d5c9 fe3761452a20b93219535dfa8a9ff9bbdf2bd75b yanxuqiang <<EMAIL>> 1755573474 +0800	pull --rebase: Fast-forward
fe3761452a20b93219535dfa8a9ff9bbdf2bd75b fe3761452a20b93219535dfa8a9ff9bbdf2bd75b yanxuqiang <<EMAIL>> 1755660903 +0800	reset: moving to HEAD
fe3761452a20b93219535dfa8a9ff9bbdf2bd75b d1f4f78efd07a1a94451636f0c033c6febd69105 yanxuqiang <<EMAIL>> 1755660907 +0800	pull --rebase: Fast-forward
d1f4f78efd07a1a94451636f0c033c6febd69105 6ae8c53875b1d7ba1df16bd33ad4e892bcfba376 yanxuqiang <<EMAIL>> 1755660948 +0800	commit: GUI: 优化各页面切回表盘响应速度
6ae8c53875b1d7ba1df16bd33ad4e892bcfba376 51964016a6b983bb70aa65e2d1d1c48d04790e15 yanxuqiang <<EMAIL>> 1755661438 +0800	commit (amend): GUI: 优化各页面切回表盘响应速度
51964016a6b983bb70aa65e2d1d1c48d04790e15 7e5d10c03a54661a062d79847a246796887f05d1 yanxuqiang <<EMAIL>> 1755679602 +0800	commit (amend): GUI: 优化各页面切回表盘响应速度
7e5d10c03a54661a062d79847a246796887f05d1 62f163b0312265bccb5efff17b6d2d25f5d841af yanxuqiang <<EMAIL>> 1755681197 +0800	commit (amend): GUI: 优化各页面切回表盘响应速度
62f163b0312265bccb5efff17b6d2d25f5d841af 0e63752ef1d433b9ff12dbc1950d687c8a1f0a2f yanxuqiang <<EMAIL>> 1755691025 +0800	pull --rebase (start): checkout 0e63752ef1d433b9ff12dbc1950d687c8a1f0a2f
0e63752ef1d433b9ff12dbc1950d687c8a1f0a2f 0e63752ef1d433b9ff12dbc1950d687c8a1f0a2f yanxuqiang <<EMAIL>> 1755691025 +0800	pull --rebase (finish): returning to refs/heads/develop
0e63752ef1d433b9ff12dbc1950d687c8a1f0a2f 604c190813760715f31e2503e5f464a118c1f58c yanxuqiang <<EMAIL>> 1755693092 +0800	pull --rebase: Fast-forward
604c190813760715f31e2503e5f464a118c1f58c 85098ef4d1fb40609612939d59e8ec5ccaa74f57 yanxuqiang <<EMAIL>> 1755744050 +0800	commit: BUG: 解决表盘功能入口跳转不到对应页面
85098ef4d1fb40609612939d59e8ec5ccaa74f57 4e69dd33a4a50a044ad63361c1b9333f73ed5455 yanxuqiang <<EMAIL>> 1755744316 +0800	pull --rebase (start): checkout 4e69dd33a4a50a044ad63361c1b9333f73ed5455
4e69dd33a4a50a044ad63361c1b9333f73ed5455 4b80a03fd0ee574705524b07fb066436166c2b9a yanxuqiang <<EMAIL>> 1755744316 +0800	pull --rebase (pick): BUG: 解决表盘功能入口跳转不到对应页面
4b80a03fd0ee574705524b07fb066436166c2b9a 4b80a03fd0ee574705524b07fb066436166c2b9a yanxuqiang <<EMAIL>> 1755744316 +0800	pull --rebase (finish): returning to refs/heads/develop
4b80a03fd0ee574705524b07fb066436166c2b9a 4b80a03fd0ee574705524b07fb066436166c2b9a yanxuqiang <<EMAIL>> 1755780185 +0800	reset: moving to HEAD
4b80a03fd0ee574705524b07fb066436166c2b9a 2d48e2fbdbc20d6df53aafbae2282eeeb68674fc yanxuqiang <<EMAIL>> 1755780189 +0800	pull --rebase (start): checkout 2d48e2fbdbc20d6df53aafbae2282eeeb68674fc
2d48e2fbdbc20d6df53aafbae2282eeeb68674fc 2d48e2fbdbc20d6df53aafbae2282eeeb68674fc yanxuqiang <<EMAIL>> 1755780189 +0800	pull --rebase (finish): returning to refs/heads/develop
2d48e2fbdbc20d6df53aafbae2282eeeb68674fc b5b0ded73ad4a27d49f527755797bb6d904f9b3d yanxuqiang <<EMAIL>> 1755780215 +0800	commit: GUI: 修改表盘电量获取接口
b5b0ded73ad4a27d49f527755797bb6d904f9b3d b5b0ded73ad4a27d49f527755797bb6d904f9b3d yanxuqiang <<EMAIL>> 1755831006 +0800	reset: moving to HEAD
b5b0ded73ad4a27d49f527755797bb6d904f9b3d a7616cea04c56349fa034f4757466a504b498187 yanxuqiang <<EMAIL>> 1755831010 +0800	pull --rebase (start): checkout a7616cea04c56349fa034f4757466a504b498187
a7616cea04c56349fa034f4757466a504b498187 af6f39366ee592af541e542a17f43a6da7e55366 yanxuqiang <<EMAIL>> 1755831010 +0800	pull --rebase (pick): GUI: 修改表盘电量获取接口
af6f39366ee592af541e542a17f43a6da7e55366 af6f39366ee592af541e542a17f43a6da7e55366 yanxuqiang <<EMAIL>> 1755831010 +0800	pull --rebase (finish): returning to refs/heads/develop
af6f39366ee592af541e542a17f43a6da7e55366 778c21e2f34c87a9a29b70b51d807e94af3a8c18 yanxuqiang <<EMAIL>> 1755835298 +0800	commit (amend): GUI: 修改表盘电量获取接口
778c21e2f34c87a9a29b70b51d807e94af3a8c18 778c21e2f34c87a9a29b70b51d807e94af3a8c18 yanxuqiang <<EMAIL>> 1756180530 +0800	reset: moving to HEAD
778c21e2f34c87a9a29b70b51d807e94af3a8c18 dee5486f1d9e51d1fbe54b6d07caed1e5df8d07e yanxuqiang <<EMAIL>> 1756180536 +0800	pull --rebase (start): checkout dee5486f1d9e51d1fbe54b6d07caed1e5df8d07e
dee5486f1d9e51d1fbe54b6d07caed1e5df8d07e dee5486f1d9e51d1fbe54b6d07caed1e5df8d07e yanxuqiang <<EMAIL>> 1756180536 +0800	pull --rebase (finish): returning to refs/heads/develop
dee5486f1d9e51d1fbe54b6d07caed1e5df8d07e 209312d9157e812d9ea32ccd0cb0132cdd28defb yanxuqiang <<EMAIL>> 1756180597 +0800	commit: GUI: 设置lcd_task tp_on线程新优先级
209312d9157e812d9ea32ccd0cb0132cdd28defb 209312d9157e812d9ea32ccd0cb0132cdd28defb yanxuqiang <<EMAIL>> 1756189114 +0800	reset: moving to HEAD
209312d9157e812d9ea32ccd0cb0132cdd28defb 209312d9157e812d9ea32ccd0cb0132cdd28defb yanxuqiang <<EMAIL>> 1756189122 +0800	reset: moving to HEAD
209312d9157e812d9ea32ccd0cb0132cdd28defb 209312d9157e812d9ea32ccd0cb0132cdd28defb yanxuqiang <<EMAIL>> 1756190479 +0800	reset: moving to HEAD
209312d9157e812d9ea32ccd0cb0132cdd28defb 74d9f862172c0ae64e4a2eaf233ef01f071931dc yanxuqiang <<EMAIL>> 1756190523 +0800	commit: GUI: 新增标志，判断是否从launcher页面进入该页面
74d9f862172c0ae64e4a2eaf233ef01f071931dc 74d9f862172c0ae64e4a2eaf233ef01f071931dc yanxuqiang <<EMAIL>> 1756278223 +0800	reset: moving to HEAD
74d9f862172c0ae64e4a2eaf233ef01f071931dc 3424613d5b31b53b001fb88c205a2bd090c3811f yanxuqiang <<EMAIL>> 1756278228 +0800	pull --rebase (start): checkout 3424613d5b31b53b001fb88c205a2bd090c3811f
3424613d5b31b53b001fb88c205a2bd090c3811f 3424613d5b31b53b001fb88c205a2bd090c3811f yanxuqiang <<EMAIL>> 1756278228 +0800	pull --rebase (finish): returning to refs/heads/develop
3424613d5b31b53b001fb88c205a2bd090c3811f d5b5c803350517ad647701da0ed8fc9036030c66 yanxuqiang <<EMAIL>> 1756278267 +0800	commit: GUI: 新增表盘Aod配置设置和获取接口
d5b5c803350517ad647701da0ed8fc9036030c66 d5b5c803350517ad647701da0ed8fc9036030c66 yanxuqiang <<EMAIL>> 1756363918 +0800	reset: moving to HEAD
d5b5c803350517ad647701da0ed8fc9036030c66 e817db06ea415720f9989bea40e76f42266a2380 yanxuqiang <<EMAIL>> 1756363925 +0800	pull --rebase: Fast-forward
e817db06ea415720f9989bea40e76f42266a2380 c34b83626a1647f74c16fe841d7ca96a5117209f yanxuqiang <<EMAIL>> 1756365378 +0800	commit: GUI: 扩展blod 56号字体字符，新增hmin
c34b83626a1647f74c16fe841d7ca96a5117209f 253666b16e3f864a5567264f739e3911c20c54e6 yanxuqiang <<EMAIL>> 1756382833 +0800	commit: GUI: 调整页面整体显示，适配整体框架
253666b16e3f864a5567264f739e3911c20c54e6 ff79b1441632f08cf883572e0d8f4d6473ed2811 yanxuqiang <<EMAIL>> 1756519429 +0800	pull --rebase (start): checkout ff79b1441632f08cf883572e0d8f4d6473ed2811
ff79b1441632f08cf883572e0d8f4d6473ed2811 ff79b1441632f08cf883572e0d8f4d6473ed2811 yanxuqiang <<EMAIL>> 1756519429 +0800	pull --rebase (finish): returning to refs/heads/develop
ff79b1441632f08cf883572e0d8f4d6473ed2811 69dede98c442217357a1f3f3a942084c5a99419c yanxuqiang <<EMAIL>> 1756710627 +0800	commit: GUI: 新增表盘选择器控件
