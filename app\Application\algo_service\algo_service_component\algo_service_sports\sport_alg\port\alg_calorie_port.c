/************************************************************************​
*Copyright(c) 2025, igpsport Software Co., Ltd.​
*All Rights Reserved.​
**************************************************************************/
#include "alg_calorie.h"
#include "alg_calorie_port.h"

static CalorieCalculator s_calorie_calculator = { 0 };

void alg_calorie_init(const alg_calorie_init_t *init)
{
    calorie_calculator_init(&s_calorie_calculator, (CalorieCalcInit *)init);
}

float alg_calorie_exec(const alg_calorie_input_t *input)
{
    return calorie_calculator_exec(&s_calorie_calculator, (CalorieCalcInput *)input);
}
