/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   team_info_pb.c
@Time    :   2024/12/25 18:12:50
* 
**************************************************************************/
#include "pb.h"
#include "pb_encode.h"
#include "pb_decode.h"
#include "pb_decode_common.h"
#include "pb_encode_common.h"
#include "ble_cmd_common.h"
#include "team_info.pb.h"
#include "team_info_pb.h"
#include "ble_cmd_response.h"
#include "crc8.h"
#include "basictype.h"


#define NICK_NAME_FROM_APP_MAX_LENGH			60

static uint8_t team_member_num = 0;
team_infor_t g_team_infor;
team_info_msg g_team_info_message;
char nickname[NICK_NAME_FROM_APP_MAX_LENGH] = {0};

//-------------------------------------------------------------------------------------------
// Function Name : team_info_data_pb_decode
// Purpose       : 队友信息数据段解码函数
// Param[in]     : pb_istream_t *stream     
//                 const pb_field_t *field  
//                 void **arg               
// Param[out]    : None
// Return type   : static
// Comment       : 2020-04-24
//-------------------------------------------------------------------------------------------
static bool team_info_data_pb_decode(pb_istream_t *stream, const pb_field_t *field, void **arg)
{
    team_info_data_msg submsg = { "" };
    uint8_t name[NICK_NAME_FROM_APP_MAX_LENGH] = {0};

    submsg.nick_name.arg = name;
    submsg.nick_name.funcs.decode = &decode_string;

    g_team_infor.num = g_team_info_message.member_num;
    if (NULL == g_team_infor.p_mem_infor)
    {
    	g_team_infor.p_mem_infor = MY_MALLOC(g_team_info_message.member_num * sizeof (mem_infor_t));
    }

    if (!pb_decode(stream, team_info_data_msg_fields, &submsg))
    {
    	return false;
    }

    if (NULL == g_team_infor.p_mem_infor)
    {
    	return false;
    }

	if (MEMBER_STATE_TYPE_enum_MEMBER_STATE_TYPE_JOIN == submsg.status)
	{
		memset(nickname, 0x00, NICK_NAME_FROM_APP_MAX_LENGH);
		memcpy(nickname, name, NICK_NAME_FROM_APP_MAX_LENGH - 1);
		gui_msg_func_submit(enumPOPUP_NAVI_TEAM_CHANGE, true, nickname);
		//弹窗提示有新用户加入
		mem_infor_t *p_mem_infor = (mem_infor_t *)(g_team_infor.p_mem_infor + team_member_num * sizeof(mem_infor_t));
		memcpy (p_mem_infor->nick_name, name, NICK_NAME_LENGTH - 1);
		p_mem_infor->latitude = submsg.latitude;
		p_mem_infor->longitude = submsg.longitude;
		p_mem_infor->status = submsg.status;
		p_mem_infor->course = submsg.course;
		team_member_num ++;
	}
	else if (MEMBER_STATE_TYPE_enum_MEMBER_STATE_TYPE_QUIT == submsg.status)
	{
		memset(nickname, 0x00, NICK_NAME_FROM_APP_MAX_LENGH);
		memcpy(nickname, name, NICK_NAME_FROM_APP_MAX_LENGH - 1);
		gui_msg_func_submit(enumPOPUP_NAVI_TEAM_CHANGE, false, nickname);
	}
	else
	{
		mem_infor_t *p_mem_infor = (mem_infor_t *)(g_team_infor.p_mem_infor + team_member_num * sizeof(mem_infor_t));
		memcpy (p_mem_infor->nick_name, name, NICK_NAME_LENGTH - 1);
		p_mem_infor->latitude = submsg.latitude;
		p_mem_infor->longitude = submsg.longitude;
		p_mem_infor->status = submsg.status;
		p_mem_infor->course = submsg.course;
		team_member_num ++;
	}

    return true;    
}


//-------------------------------------------------------------------------------------------
// Function Name : team_info_cmd_handle
// Purpose       : 队友信息PB状态处理函数
// Param[in]     : uint8_t *buf  
// Param[out]    : None
// Return type   : 
// Comment       : 2020-04-24
//-------------------------------------------------------------------------------------------
void team_info_cmd_handle(uint8_t *buf)
{
    ble_status_cmd_st *ble_status_cmd_s = (ble_status_cmd_st *)buf;
    uint8_t status = ble_status_cmd_s->status;

    if (enmuDATA_ERR_STATUS == status)
    {
        switch (ble_status_cmd_s->sub_op_type)
        {
            case TEAM_INFO_OPERATE_TYPE_enum_TEAM_INFO_OPERATE_TYPE_SET:
                break;
            default:
                break;                   
        }
    }
}        

//-------------------------------------------------------------------------------------------
// Function Name : team_info_pb_decode
// Purpose       : 队友信息PB解码接口函数
// Param[in]     : uint8_t * pb_buffer     
//                 uint16_t buffer_length  
//                 END_TYPE end_type       
// Param[out]    : None
// Return type   : 
// Comment       : 2020-04-24
//-------------------------------------------------------------------------------------------
void team_info_pb_decode(uint8_t * pb_buffer, uint16_t buffer_length, END_TYPE end_type)
{  
    uint8_t status = false;
    team_info_data_msg team_info_data_message;

    team_member_num = 0;
    if (g_team_infor.p_mem_infor != NULL)
    {
    	MY_FREE(g_team_infor.p_mem_infor);
    }
    g_team_infor.num = 0;

    memset(&g_team_info_message, 0, sizeof(team_info_msg));
    memset(&team_info_data_message, 0, sizeof(team_info_data_msg));
    memset(&g_team_infor, 0, sizeof(team_infor_t));

    g_team_info_message.team_info_data_message.arg = &team_info_data_message;
    g_team_info_message.team_info_data_message.funcs.decode = &team_info_data_pb_decode;
    
    pb_istream_t decode_stream = pb_istream_from_buffer(pb_buffer, buffer_length);
    
    status = pb_decode(&decode_stream, team_info_msg_fields, &g_team_info_message);

    if (true == status)
    {
        switch (g_team_info_message.team_info_operate_type)
        {
            case TEAM_INFO_OPERATE_TYPE_enum_TEAM_INFO_OPERATE_TYPE_SET:
                ble_cmd_success_status_tx(g_team_info_message.service_type, 0, g_team_info_message.team_info_operate_type, 0);
                break;
            default:
                break;
        }
    }
    else
    {
        ble_cmd_err_status_tx(g_team_info_message.service_type, 0, g_team_info_message.team_info_operate_type, 0);
    }
}

//-------------------------------------------------------------------------------------------
// Function Name : team_info_get
// Purpose       : 应用层获取队友信息接口
// Param[in]     : None
// Param[out]    : None
// Return type   : 
// Comment       : 2020-04-24
//-------------------------------------------------------------------------------------------
team_infor_t* team_infor_get()
{
    return &g_team_infor;
}

/********************************************************************************************
* Function/Macro Name : team_cnt_get
* Purpose : 获取队友数量
* Param[in] :
* param ---
* Param[out] :
* param ---
* Return type :
* Comment : 2022-05-12 10:11:27
********************************************************************************************/
int team_cnt_get()
{
	return g_team_infor.num;
}

/********************************************************************************************
* Function/Macro Name : team_name_get
* Purpose : 获取队友简称
* Param[in] :
* param ---
* Param[out] :
* param ---
* Return type :
* Comment : 2022-05-12 10:11:39
********************************************************************************************/
char* team_name_get(int index)
{
	if (g_team_infor.num > index)
	{
		return (char*)g_team_infor.p_mem_infor[index].nick_name;
	}
	else
	{
		return NULL;
	}
}

/********************************************************************************************
* Function/Macro Name : team_position_get
* Purpose : 获取队友位置
* Param[in] :
* param ---
* Param[out] :
* param ---
* Return type :
* Comment : 2022-05-12 10:11:53
********************************************************************************************/
void team_position_get(int index, double* latitude, double* longitude)
{
	if (g_team_infor.num > index)
	{
		*latitude = g_team_infor.p_mem_infor[index].latitude;
		*longitude = g_team_infor.p_mem_infor[index].longitude;
	}
}

/********************************************************************************************
* Function/Macro Name : team_state_get
* Purpose : 获取队友状态颜色
* Param[in] :
* param ---
* Param[out] :
* param ---
* Return type :
* Comment : 2022-05-12 10:12:03
********************************************************************************************/
int team_state_get(int index)
{
	if (g_team_infor.num > index)
	{
		return (int)g_team_infor.p_mem_infor[index].status;
	}
	else
	{
		return 0;
	}
}
