"_10_s",
"_10s_power",
"_12_hour",
"_15_s",
"_1_step_1_beat",
"_24_hour",
"_2_steps_1_beat",
"_30s_power",
"_30s_vertical_average_speed",
"_3_s",
"_3s_power",
"_5_s",
"_7_day_get_evaluation",
"_about",
"_accessibility",
"_accurate",
"_activity_end_physical_strength",
"_activity_start_physical_strength",
"_add",
"_add_alarm",
"_add_sensors",
"_advanced_rider",
"_advanced_runner",
"_aerobic",
"_aerobic_capacity",
"_aerobic_effect",
"_aerobic_explosive_power",
"_ahead_time",
"_air_pressure",
"_air_pressure_curve_graph",
"_alarm_clock",
"_alarm_clock_editor",
"_alipay",
"_alipay_scan_to_activate_reminder",
"_alipay_unlink_reminder",
"_all_day_measurement",
"_all_goal_reminders",
"_already_exercised",
"_altitude",
"_altitude_1",
"_altitude_calibration",
"_altitude_calibration_progress",
"_anaerobic",
"_anaerobic_capacity",
"_anaerobic_effect",
"_angle",
"_ant+sensor",
"_aod",
"_aod_power_reminder",
"_app_id_changed_factory_reset_clear_all_data_files",
"_application_card",
"_ascent_vertical_average_speed",
"_at_rest",
"_athlete",
"_athlete_level_1",
"_athlete_level_2",
"_athlete_level_3",
"_auto_brightness",
"_auto_detection",
"_auto_detection_1",
"_auto_laps",
"_auto_laps_swim_reminder",
"_auto_lock",
"_auto_page_turning",
"_auto_pause",
"_auto_zoom",
"_automatically_lock_reminder",
"_average_7_day",
"_average_ascent_slope",
"_average_b_o",
"_average_bike_cadence",
"_average_contact_balance",
"_average_contact_time",
"_average_daily_consumption",
"_average_daily_hours",
"_average_daily_sleep",
"_average_daily_steps",
"_average_daily_time",
"_average_descent_slope",
"_average_downhill_speed",
"_average_during_sleep",
"_average_hr",
"_average_hr_p",
"_average_lap_time",
"_average_pace",
"_average_paddle_frequency",
"_average_power",
"_average_resting_hr",
"_average_run_cadence",
"_average_speed",
"_average_stress",
"_average_stride_length",
"_average_swolf",
"_average_vertical_amplitude",
"_average_vertical_stride_ratio",
"_awake",
"_back_homepage",
"_balance",
"_balanced",
"_baro_meter",
"_based_on_sports_records",
"_based_on_training_course",
"_baseline",
"_battery",
"_battery_p",
"_battery_power",
"_battery_time",
"_bedtime_reminder",
"_beep",
"_beginner",
"_behind_time",
"_benchmark_running",
"_best_10km",
"_best_5km",
"_best_full_marathon",
"_best_half_marathon",
"_best_pace",
"_best_ride_power",
"_bike",
"_bike_cadence",
"_bike_cadence_curve_graph",
"_bike_cadence_range",
"_bike_cadence_range_graph",
"_bike_cadence_reminder",
"_bike_cadence_too_fast",
"_bike_cadence_too_low",
"_binding_failed",
"_binding_in_progress",
"_binding_not_completed",
"_binding_successful",
"_blizzard",
"_blood_oxygen_saturation",
"_blood_oxygen_settings",
"_bluetooth_disconnected",
"_bluetooth_pairing",
"_bluetooth_sensor",
"_breath_exercise",
"_breath_exercise_accomplish",
"_breath_screen",
"_breathe_in",
"_breathe_out",
"_breathing_settings",
"_brightness",
"_brightness_control",
"_buzzer",
"_by_consumption",
"_by_distance",
"_by_pace",
"_by_speed",
"_by_time",
"_calib_time",
"_calibrate",
"_calibrate_power_meter",
"_calibrate_the_cycling_platform",
"_calibration_altitude",
"_calibration_failed",
"_calibration_reminder",
"_calibration_successful",
"_calorie_target_reminder",
"_calories",
"_carbohydrate_consumption",
"_change_items_manually",
"_change_time",
"_circle__stroke_count",
"_clcling_vo_2_max",
"_clear",
"_clear_all",
"_cloudy",
"_code_generation_failure_retry_reminder",
"_color_test",
"_commuting_bike",
"_compass_calibration_failed",
"_compass_calibration_successful",
"_completion",
"_compound_exercise",
"_confirm_delete_all_training_course",
"_confirm_delete_current_training_course",
"_confirm_return_starting_point",
"_confirm_to_change_sport",
"_confirm_to_proceed_to_next_sport",
"_confirm_to_reset",
"_confirm_whether_to_pair",
"_connect_app_sync",
"_connected",
"_connected_",
"_connecting",
"_consumed",
"_consumption",
"_consumption_reminder",
"_contact_balance",
"_contact_manufacturer_reminder",
"_contact_time",
"_cool_down",
"_countdown",
"_crank",
"_cur",
"_current_air_pressure",
"_current_altitude",
"_current_continuous_jumps",
"_current_lap_distance",
"_current_test",
"_current_time",
"_custom",
"_cycle",
"_cycling_ability",
"_cycling_enthusiast",
"_cycling_ftp",
"_cycling_ftp_test",
"_cycling_ftp_updated_sync",
"_cycling_platform",
"_cycling_trainer_mode",
"_daily",
"_date_and_time",
"_date_format",
"_date_of_birth",
"_day",
"_day_month_year",
"_deep_sleep",
"_default_on_when_swimming",
"_delete_alarm",
"_delete_all",
"_delete_course",
"_delete_records",
"_delete_reminder",
"_delete_route",
"_descent_vertical_average_speed",
"_destination_time",
"_details",
"_developer_mode",
"_deviation",
"_device_lock",
"_device_name",
"_device_successfully_unlinked_reminder",
"_device_unbound_reminder",
"_dial",
"_disable",
"_discard",
"_discard_exercise_reminder",
"_disconnected",
"_display",
"_distance",
"_distance_calibration",
"_distance_reminder",
"_distance_to_next_waypoint",
"_downhill_distance",
"_downhill_time",
"_downhill_trip_distance",
"_downhill_trip_drop_distance",
"_downhill_trips_number",
"_download_route_from_app",
"_drink_water_reminder",
"_during_activity_min_physical_strength",
"_east",
"_easy",
"_edit_color",
"_edit_component",
"_efficient_training",
"_electronic_compass",
"_elevation",
"_elite_rider",
"_elite_runner",
"_elliptical",
"_enabled",
"_end",
"_end_distance",
"_end_navigation",
"_end_route",
"_end_time",
"_end_training_course",
"_endurance",
"_enter_password",
"_estimated_arrival_time",
"_estimated_time",
"_excellent",
"_excellent_1",
"_exercise",
"_exercise_aod",
"_exercise_history_deleted",
"_exercise_reminder",
"_explosive_power",
"_extreme_power_saving",
"_extreme_power_saving_reminder",
"_extreme_power_saving_toast",
"_factory_mode",
"_factory_reset_in_progress",
"_failed_reminder",
"_failed_to_obtain_barcode",
"_failed_to_obtain_binding_code",
"_failed_to_obtain_help_code",
"_failed_to_obtain_qr_code",
"_fall_asleep_time",
"_fast_bike_cadence",
"_fast_pace",
"_fast_speed",
"_fastest_average_pace",
"_fastest_average_speed",
"_fat_aging_test",
"_fat_consumption",
"_fat_mode_fat_tip",
"_fat_mode_title",
"_fat_mode_user",
"_female",
"_find_phone",
"_find_sports_records",
"_find_training_course",
"_find_watch",
"_finished_charging",
"_flashlight",
"_focus_mode",
"_fog",
"_follow_the_phone",
"_free",
"_free_ride",
"_free_training",
"_freezing_rain",
"_friday",
"_ftp_p",
"_ftp_test_fail_reminder",
"_ftp_test_reminder",
"_ftp_test_sensor_reminder",
"_ftp_test_success_reminder",
"_full_marathon",
"_furthest_run",
"_future_weather",
"_gender",
"_general",
"_general_power_saving",
"_general_power_saving_reminder",
"_get_up_alarm_clock",
"_getting_up_time",
"_go_on",
"_good",
"_good_1",
"_gps",
"_gps_accuracy",
"_gps_calibration",
"_gps_located",
"_gps_location_reminder",
"_gps_positioning",
"_gps_positioning_failure_reminder",
"_gps_signal",
"_gps_signal_lost",
"_gps_start_test",
"_green",
"_group_x",
"_half_marathon",
"_hardware_version",
"_haze",
"_heading",
"_heading_up",
"_headlight",
"_health_monitoring",
"_heart_rate",
"_heart_rate_broadcast",
"_heart_rate_curve_graph",
"_heart_rate_p",
"_heart_rate_range",
"_heart_rate_reminder",
"_heart_rate_settings",
"_heavily_polluted",
"_heavy_rain",
"_heavy_snow",
"_height",
"_help",
"_high",
"_high_run_cadence",
"_highest_climb",
"_highest_elevation",
"_history",
"_hold_power_key_exit_power_saving_mode",
"_hold_power_key_unlock",
"_hour_",
"_hourly_weather",
"_hours",
"_hours_ago",
"_hr_broadcast_power_reminder",
"_hr_too_high",
"_hr_too_high_reminder",
"_hr_too_high_reminder_detail",
"_hr_too_low",
"_hr_too_low_reminder",
"_hr_too_low_reminder_detail",
"_hrv",
"_hrv_average",
"_hrv_range",
"_humidity",
"_id_different",
"_if",
"_improved",
"_incoming_call",
"_indoor_aerobics",
"_indoor_bike",
"_indoor_running",
"_indoor_walk",
"_intelligent",
"_intense_activity_target_reminder",
"_international_athlete",
"_interval",
"_interval_details",
"_interval_training",
"_is_charge",
"_jump_rope",
"_just_now",
"_just_updated",
"_key_test",
"_key_vibration",
"_lactate_threshold_heart_rate",
"_lactate_threshold_hr_measure_for_running",
"_lactate_threshold_pace",
"_language",
"_lap_counting",
"_lap_field",
"_lap_reps",
"_lap_speed",
"_last_7_days",
"_left_hand",
"_left_right_balance",
"_light_rain",
"_light_sensor",
"_light_sleep",
"_light_snow",
"_lightly_polluted",
"_load",
"_load_last_7_days",
"_load_ratio",
"_loading_waiting_reminder",
"_long_distance_bike",
"_long_press_power",
"_longest_ride",
"_longest_swim",
"_longest_time",
"_low",
"_low_battery",
"_low_battery_power",
"_low_battery_power_reminder",
"_low_efficiency",
"_lowest_elevation",
"_mac_address",
"_maintaining_effect",
"_male",
"_manual_calibration",
"_manual_measurement",
"_map_direction",
"_mass_elite_level",
"_mass_level_1",
"_mass_level_2",
"_max_ascent_slope",
"_max_ascent_speed",
"_max_bike_cadence",
"_max_continuous_jumps",
"_max_descent_slope",
"_max_descent_speed",
"_max_heart_rate",
"_max_hr",
"_max_hr_p",
"_max_power",
"_max_run_cadence",
"_max_speed",
"_max_stride_length",
"_maximum_heart_rate",
"_maximum_hr_measure_for_running",
"_measurement_frequency",
"_measuring",
"_metronome",
"_middle",
"_mild",
"_min_bike_cadence",
"_min_heart_rate",
"_min_power",
"_min_run_cadence",
"_minute_",
"_minutes",
"_minutes_ago",
"_moderate",
"_moderate_1",
"_moderate_rain",
"_moderate_snow",
"_moderately_polluted",
"_moment_to_next_waypoint",
"_monday",
"_month_day_year",
"_more",
"_most_jumps",
"_motion_not_available",
"_motor",
"_mountain_bike",
"_mountain_climbing",
"_move_time",
"_multi_frequency_satellite",
"_music_control",
"_navigation",
"_navigation_settings",
"_network_anomaly_reminder",
"_network_instability_reminder",
"_new_achievements",
"_newbie",
"_next_alarm_reminder_toast",
"_nlsd_wear_reminder",
"_no_achievements",
"_no_charge",
"_no_data",
"_no_long_sleep_data",
"_no_nap_data",
"_no_notifications",
"_no_records",
"_no_status",
"_no_training",
"_no_training_plan",
"_no_training_today",
"_no_weather_connect_reminder",
"_none",
"_normal",
"_north",
"_north_up",
"_northeast",
"_northwest",
"_not_connected",
"_not_connected_app",
"_not_disturb_mode",
"_not_disturb_mode_at_certain_times",
"_not_good",
"_not_playing",
"_novice",
"_now",
"_np",
"_number_of_groups",
"_number_of_laps",
"_number_of_times",
"_on_foot",
"_on_friday",
"_on_monday",
"_on_saturday",
"_on_sunday",
"_on_thursday",
"_on_tuesday",
"_on_wednesday",
"_only_in_sport",
"_only_once",
"_open_water_swim",
"_optical_knob",
"_outdoor_aerobics",
"_outside_sensor",
"_overcast",
"_overtraining",
"_pace",
"_pace_curve_graph",
"_pace_range",
"_pace_range_graph",
"_pace_reminder",
"_pace_too_fast",
"_pace_too_slow",
"_paddle_frequency",
"_paired_xxxxx_reminder",
"_pairing",
"_pairing_failed",
"_pairing_successful",
"_parsing_failed",
"_payment_code",
"_payment_code_disabled_reminder",
"_pct_",
"_pedaling_smoothness",
"_per_lap_average_stroke_count",
"_per_trip_average_stroke_count",
"_performance_prediction",
"_personal_achievements",
"_personal_information",
"_personalization",
"_phone_ring_search_starts",
"_physical_fitness",
"_physical_strength",
"_playground_running",
"_please_app_plan",
"_please_connect_smart_cycling_trainer_first",
"_please_go_to_app_planning",
"_please_refresh_and_try_again",
"_please_speed_up",
"_pool_length",
"_pool_swim",
"_poor",
"_position_laps",
"_power",
"_power_calibrated_wait",
"_power_combination_graph",
"_power_consumption_reminder",
"_power_curve_graph",
"_power_distribution_combination_graph",
"_power_kilojoules",
"_power_mode",
"_power_range",
"_power_range_graph",
"_power_reminder",
"_power_saving",
"_power_saving_mode",
"_power_saving_mode_not_available",
"_power_to_body_ratio",
"_power_too_fast",
"_power_too_low",
"_ppg",
"_press_back_button",
"_previous_cycle__trips",
"_previous_downhill_trip_time",
"_previous_group_consumption",
"_previous_group_hr",
"_previous_group_paddle_frequency",
"_previous_group_stroke_count",
"_previous_group_time",
"_previous_group_times_number",
"_previous_lap_average_speed",
"_previous_lap_bike_cadence",
"_previous_lap_consumption",
"_previous_lap_distance",
"_previous_lap_high_hr",
"_previous_lap_high_hr_p",
"_previous_lap_hr",
"_previous_lap_hr_p",
"_previous_lap_max_bike_cadence",
"_previous_lap_max_power",
"_previous_lap_max_speed",
"_previous_lap_pace",
"_previous_lap_power",
"_previous_lap_reserve_hr_p",
"_previous_lap_run_cadence",
"_previous_lap_slope",
"_previous_lap_step_count",
"_previous_lap_stride_length",
"_previous_lap_swolf",
"_previous_lap_time",
"_previous_lap_total_ascent",
"_previous_lap_vertical_average_speed",
"_previous_trip_downhill_distance",
"_previous_trip_downhill_drop_distance",
"_previous_trip_downhill_speed",
"_previous_trip_hr",
"_previous_trip_pace",
"_previous_trip_stroke_count",
"_previous_trip_swolf",
"_procedure",
"_progressive_intensity_test_reminder",
"_prompt_frequency",
"_prompt_method",
"_qrcode",
"_quick",
"_quick_recovery",
"_radar",
"_rainstorm",
"_raise_wrist_light_up_screen",
"_range_1",
"_range_2",
"_range_3",
"_range_4",
"_range_5",
"_range_6",
"_range_during_sleep",
"_reclock",
"_record_time",
"_recovery",
"_recovery_time",
"_recovery_training",
"_red",
"_refresh",
"_relaxation",
"_relaxed",
"_rem",
"_remaining_climbing_height",
"_remaining_distance",
"_remaining_steps",
"_remote_camera",
"_remove",
"_remove_reminder",
"_repeat",
"_replenish_energy",
"_res_x_calib",
"_rescan_code_to_bind_again",
"_reserve_hr_p",
"_reset",
"_reset_reminder",
"_reset_sport",
"_resistance",
"_resistance_mode",
"_rest",
"_rest_day",
"_rest_reminder",
"_rest_time",
"_restart",
"_restart_reminder",
"_resting_hr",
"_restore_factory",
"_restore_factory_reminder",
"_restore_settings",
"_restore_settings_reminder",
"_resume_route",
"_return_exercise_list_reminder",
"_return_to_starting_point",
"_reverse",
"_ride_code",
"_ride_code_first_use_reminder",
"_ride_code_not_activated",
"_ride_code_unavailable_reminder",
"_right_hand",
"_road_bike",
"_route",
"_rowing_machine",
"_run",
"_run_cadence",
"_run_cadence_reminder",
"_run_cadence_too_fast",
"_run_cadence_too_low",
"_run_efficiency",
"_run_max_hr_measure_completed_sync_reminder",
"_run_max_hr_measure_failed_sync_reminder",
"_run_vo_2_max",
"_running_ability",
"_running_dynamics_sensor",
"_running_enthusiast",
"_running_lactate_threshold_heart_rate",
"_running_lactate_threshold_hr_updated_sync",
"_running_maximum_heart_rate",
"_running_maximum_hr_updated_sync",
"_runway_1",
"_runway_2",
"_runway_3",
"_runway_4",
"_runway_5",
"_runway_6",
"_runway_7",
"_runway_8",
"_runway_9",
"_runway_settings",
"_s_frequency_and_m_satellite",
"_saturday",
"_scan_alipay_qr_code_for_help",
"_scan_code_bind_using_alipay",
"_scan_code_download",
"_scan_code_pair",
"_screen",
"_screen_time",
"_searching",
"_second",
"_security_initialization_settings",
"_security_risk_reminder",
"_seek",
"_sensor",
"_sensor_not_detected",
"_sensor_self_check",
"_set_password",
"_set_power",
"_set_resistance",
"_set_slope",
"_settings",
"_severe",
"_severely_polluted",
"_shipping",
"_short_press_power",
"_shortcut_key",
"_show_altitude_map",
"_showers",
"_shut_down",
"_shut_down_reminder",
"_single_frequency_gps",
"_six_axis_sensor",
"_skiing",
"_sleep",
"_sleep_measurement",
"_sleep_mode",
"_sleep_mode_not_available",
"_sleep_note",
"_sleep_plan",
"_sleep_quality",
"_sleep_score",
"_sleep_stage",
"_sleet",
"_slightly_faster",
"_slope",
"_slope_mode",
"_slow",
"_slow_pace",
"_slow_speed",
"_slowly",
"_smart_cycling_trainer",
"_smart_notifications",
"_sn_code",
"_snow_showers",
"_software_version",
"_sound",
"_sound_vibration",
"_south",
"_southeast",
"_southwest",
"_speed",
"_speed&bike_cadence",
"_speed_curve_graph",
"_speed_range",
"_speed_range_graph",
"_speed_reminder",
"_speed_too_fast",
"_speed_too_slow",
"_sporadic_naps",
"_sport_1",
"_sport_2",
"_sport_3",
"_sports_countdown",
"_sports_list",
"_sports_project",
"_sports_records",
"_sports_settings",
"_stand_hours",
"_stand_reminder",
"_start",
"_start_navigation",
"_start_training",
"_start_training_today",
"_starting_time",
"_stay_still",
"_step",
"_step_count",
"_step_count_reminder",
"_step_count_target_reminder",
"_stop_navigation",
"_stopwatch",
"_strength_training",
"_stress",
"_stress_distribution",
"_stress_relief_reminder",
"_stress_reminder_threshold",
"_stress_settings",
"_stride_length",
"_stroke_count",
"_stroke_frequency",
"_sunday",
"_sunrise_time",
"_sunset_time",
"_supply",
"_supply_reminder",
"_swim_style_ratio",
"_switch_barcode",
"_switch_qr_code",
"_switch_transportation_card",
"_switching_environment",
"_system_settings",
"_taillight",
"_target_power",
"_temperature",
"_theme_color",
"_this_circle_stroke_count",
"_this_cycle_trips",
"_this_group_consumption",
"_this_group_hr",
"_this_group_paddle_frequency",
"_this_group_speed",
"_this_group_stroke_count",
"_this_group_time",
"_this_group_times_number",
"_this_group_trip_ropes",
"_this_lap_average_speed",
"_this_lap_bike_cadence",
"_this_lap_consumption",
"_this_lap_contact_balance",
"_this_lap_contact_time",
"_this_lap_high_hr",
"_this_lap_high_hr_p",
"_this_lap_hr",
"_this_lap_hr_p",
"_this_lap_max_bike_cadence",
"_this_lap_max_power",
"_this_lap_max_speed",
"_this_lap_pace",
"_this_lap_power",
"_this_lap_reserve_hr_p",
"_this_lap_run_cadence",
"_this_lap_slope",
"_this_lap_step_count",
"_this_lap_stride_length",
"_this_lap_swolf",
"_this_lap_time",
"_this_lap_total_ascent",
"_this_lap_vertical_amplitude",
"_this_lap_vertical_average_speed",
"_this_section_time",
"_this_trip_downhill_distance",
"_this_trip_downhill_drop_distance",
"_this_trip_downhill_fast_speed",
"_this_trip_downhill_speed",
"_this_trip_downhill_time",
"_this_trip_hr",
"_this_trip_pace",
"_this_trip_stroke_count",
"_threshold",
"_thunderstorm",
"_thursday",
"_time",
"_time_format",
"_time_of_intense_activity",
"_time_of_section_elapsed",
"_time_remaining",
"_time_reminder",
"_time_settings",
"_time_to_next_waypoint",
"_timer",
"_timer_end",
"_timer_set",
"_times",
"_tired",
"_today",
"_today_consumption",
"_today_training",
"_too_many_items_reminder",
"_top",
"_torque_efficiency",
"_total_ascent",
"_total_descent",
"_total_time",
"_touch_panel",
"_touch_screen_lock",
"_touch_tone",
"_track_run",
"_traffic_warning_bar",
"_training",
"_training_completed",
"_training_course",
"_training_end_relaxation_reminder",
"_training_experience",
"_training_interruption",
"_training_load",
"_training_pace",
"_training_plan",
"_training_start_warm_up_reminder",
"_training_status",
"_training_time",
"_triathlon",
"_triathlon_completed",
"_trip_number",
"_trip_rope",
"_tss",
"_tuesday",
"_turn_off",
"_turn_on",
"_turn_on_not_disturb_mode",
"_turn_on_not_disturb_mode_reminder",
"_turn_on_sleep_mode",
"_turn_on_sleep_mode_reminder",
"_turn_reminder",
"_ultra_endurance",
"_unbind",
"_unit",
"_universal",
"_unlinked_reminder",
"_unsupported_reminder",
"_until_press_lap_button",
"_updated_hours_ago",
"_updated_minutes_ago",
"_uphill_distance",
"_user_information",
"_vertical_amplitude",
"_vertical_speed",
"_vertical_stride_ratio",
"_very_tired",
"_vibration",
"_vibration_and_sound",
"_vibration_reminder",
"_view",
"_virtual_rabbit",
"_vo_2_max",
"_vol",
"_walk",
"_warm_up",
"_watch_locked",
"_wear_20_m&h_intensity_outcycling_evaluation",
"_wear_20_m&h_intensity_running_evaluation",
"_wear_7_day_continuous_hrv_reminder",
"_wearing_hand",
"_wearing_reminder",
"_wearing_still_reminder",
"_weather",
"_wednesday",
"_weight",
"_west",
"_wheel_diameter",
"_wheel_diameter_setting",
"_wheel_diameter_update_reminder",
"_whether_to_enter_power_saving_mode",
"_windy",
"_wrong_password",
"_yaw_reminder",
"_year_month_day",
"_yellow",
"_yesterday",
