/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   compass_ctrl.h
@Time    :   2025/01/08 10:15:31
*
**************************************************************************/
#ifndef __COMPASS_CTRL_H__
#define __COMPASS_CTRL_H__

#ifndef SIMULATOR
#include "compass_algorithm.h"
#endif   // !SIMULATOR

#if defined(__cplusplus)
extern "C" {
#endif

    void start_compass(void);
    void stop_compass(void);
    int get_compass_align(void);
    int get_compass_angle(void);

    // 用户回调
    int compass_module_register_callback(void (*callback)(int compass_align,int compass_angle));

#if defined(__cplusplus)
}
#endif

#endif // __COMPASS_CTRL_H__
