/***************************************Copyright (c)****************************************/
//                              <PERSON>han <PERSON> Technology Co., Ltd
//
//---------------------------------------File Info--------------------------------------------
// File name         : ant_fe_rx.c
// Created by        : yukai
// Descriptions      : ANT功率计模块接收.c文件
//--------------------------------------------------------------------------------------------
// History           :
// 2020-10-31        :原始版本
/*********************************************************************************************/

#include "nrf_sdh_ant.h"
#include "app_error.h"
#include "ant_fe.h"
#include "ant_fe_rx.h"
#include "ant_channel_config.h"
#include "qw_sensor_common.h"
#include "qw_sensor_data.h"
#include "sensor_ant_common.h"
#include "ant_fe_pages.h"
#include "basictype.h"
#include "cfg_header_def.h"
#include "ant_request_controller.h"
#include "ant_interface.h"

#if ANT_SENSOR_FEC_ENABLED

extern uint8_t g_Calibration_Key;

//--------------------------------------函数申明-------------------------------------------//
static void ant_fe_rx_evt_handler(ant_fe_profile_t * p_profile, ant_fe_evt_t event);
static void fe_ant_evt(ant_evt_t *p_ant_evt, void * p_context);


//--------------------------------------变量定义-------------------------------------------//
FE_DISP_PROFILE_CONFIG_DEF(m_ant_fe, ant_fe_rx_evt_handler);

static ant_fe_profile_t m_ant_fe;
//static uint32_t s_pwr_cad_ms = 0;
//static uint32_t s_pwr_spd_ms = 0;

//--------------------------------------函数定义-------------------------------------------//
//-------------------------------------------------------------------------------------------
// Function Name : LoadChnConf_fe_rx
// Purpose       : 加载ANT PWR接收通道默认配置
// Param[in]     : ant_channel_config_t  *p_channel_config
// Param[out]    : None
// Return type   : static
// Comment       : 2019-02-27
//-------------------------------------------------------------------------------------------
static void LoadChnConf_fe_rx(ant_channel_config_t  *p_channel_config)
{
    p_channel_config->channel_number    = sensor_ant_channel_num_get(SENSOR_TYPE_FEC);
    p_channel_config->channel_type      = CHANNEL_TYPE_SLAVE;   //ant认证要求使用0x00类型 FE_DISP_CHANNEL_TYPE;
    p_channel_config->ext_assign        = FE_EXT_ASSIGN;
    p_channel_config->rf_freq           = FE_ANTPLUS_RF_FREQ;   ///< Frequency, decimal 57 (2457 MHz).
    p_channel_config->transmission_type = CHAN_ID_TRANS_TYPE;
    p_channel_config->device_type       = FE_DEVICE_TYPE;
    p_channel_config->channel_period    = FE_MSG_PERIOD;        //8086
    p_channel_config->network_number    = ANTPLUS_NETWORK_NUM;
}

//-------------------------------------------------------------------------------------------
// Function Name : ant_fe_rx_evt_handler
// Purpose       : ANT PWR接收通道中断处理函数
// Param[in]     : ant_hrm_profile_t * p_profile
//                 ant_hrm_evt_t event
// Param[out]    : None
// Return type   : static
// Comment       : 2019-02-27
//-------------------------------------------------------------------------------------------
static void ant_fe_rx_evt_handler(ant_fe_profile_t * p_profile, ant_fe_evt_t event)
{
    sensor_search_infor_t       sensor_search_infor;
    // sensor_connect_infor_t      *p_sensor_connect       = sensor_connect_infor_get(SENSOR_TYPE_FEC);
    sensor_connect_infor_t      sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_FEC, &sensor_connect);
    sensor_module_evt_handler   evt_handler             = sensor_module_evt_handler_get();
    sensor_saved_work_t         *p_sensor_saved         = NULL;
    sensor_work_state_t         sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    sensor_module_param_input_t *p_param                = sensor_module_param_input_get();
//    sensor_original_data_t      *p_sensor_original_data = sensor_original_data_get();
    int8_t                      index                   = -1;
//    static uint8_t              low_power_indicate_flag = FALSE;

    sensor_ant_leave_rx_search(SENSOR_TYPE_FEC);

    switch (event)
    {
        case ANT_FE_PAGE_1_UPDATED:
            p_profile->p_disp_cb->calib_stat = FE_DISP_CALIB_NONE;

            //此处可以标记功率校准是否成功或者失败
            if (0 != p_profile->page_1.zero_offset_calib) //1 = Success.
            {
                if (true == g_Calibration_Key)
                {
                    //校准成功,显示该数据
                    evt_handler(EVENT_SENSOR_BPWR_CALIB_SUCCESS, NULL, SENSOR_TYPE_FEC, SENSOR_RADIO_TYPE_ANT, p_profile->page_1.zero_offset);
                    g_Calibration_Key = false;
                }
            }
            else //0 = Failure/Not attempted.
            {
                if (true == g_Calibration_Key)
                {
                    //校准失败,显示错误码
                    evt_handler(EVENT_SENSOR_BPWR_CALIB_FAILED, NULL, SENSOR_TYPE_FEC, SENSOR_RADIO_TYPE_ANT, p_profile->page_1.zero_offset);
                    g_Calibration_Key = false;
                }
            }

            break;

        case ANT_FE_CALIB_TIMEOUT:
        case ANT_FE_CALIB_REQUEST_TX_FAILED:
            //校准失败处理 //不在这里处理 //这里处理超时时间不可控
            break;

        default:
            // never occurred
            break;
    }

    memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
    memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
    sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
    sensor_search_infor.sensor_type = SENSOR_TYPE_FEC;
    // sensor_saved_work_infor_get(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state);

    if (SENSOR_CONNECT_STATE_CONNECTING == sensor_connect.state)
    {
        sensor_connect.state = SENSOR_CONNECT_STATE_CONNECTED;
        sensor_connect_infor_set(SENSOR_TYPE_FEC, &sensor_connect);

        if (sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
        {
            if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
            {
                p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
                cfg_mark_update(enum_cfg_ant_ble_dev);
            }
            else if (SENSOR_WORK_STATE_FORBIDDEN == sensor_work_state)
            {
                sensor_infor_t sensor_infor = {0};
                sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                sensor_disconnect(&sensor_infor);
            }
            sensor_saved_work_infor_release_write_lock(index);
        }
        else
        {
            if(sensor_disconnect_item_check(&sensor_search_infor))
            {
                sensor_disconnect_info_remove(&sensor_search_infor);
                sensor_infor_t sensor_infor = {0};
                sensor_infor.sensor_type = sensor_search_infor.sensor_type;
                sensor_disconnect(&sensor_infor);
                return;
            }
            sensor_saved_work_infor_add(&sensor_search_infor);
            sensor_search_infor_del(&sensor_search_infor);
        }

        if (NULL != evt_handler)
        {
            evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, TRUE);
            evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, 0);
            evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, TRUE);
        }

//        low_power_indicate_flag = TRUE;
    }
    else if (SENSOR_CONNECT_STATE_CONNECTED == sensor_connect.state)
    {
        if(sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
        {
            if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
            {
                p_sensor_saved->sensor_work_state               [index]                   = SENSOR_WORK_STATE_CONNECTED;
                p_sensor_saved->sensor_saved_infor->sensor_infor[index].last_connect_time = *p_param->sysTime_s;
            }
            sensor_saved_work_infor_release_write_lock(index);
        }
    }

    // if (NULL != p_sensor_connect && SENSOR_CONNECT_STATE_CONNECTED == p_sensor_connect->state)
    {
        if (NULL != evt_handler)
        {
            evt_handler(EVENT_SENSOR_DATA_UPDATE, NULL, SENSOR_TYPE_FEC, SENSOR_RADIO_TYPE_ANT, 0);
        }
    }

    if (sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
    {
        if (SENSOR_WORK_STATE_SAVED == sensor_work_state)
        {
#if SENSOR_DEVICE_INFO_ENABLED
            if (p_sensor_saved->sensor_manufacturer[index].manufacturer_ant != p_profile->page_80.manufacturer_id ||
                    p_sensor_saved->sensor_serial[index].serial_ant != p_profile->page_81.serial_number ||
                    p_sensor_saved->sensor_hw_version[index].version_ant != p_profile->page_80.hw_revision ||
                    p_sensor_saved->sensor_model[index].model_ant != p_profile->page_80.model_number ||
                    p_sensor_saved->sensor_sw_version[index].version_ant != ((((uint16_t)p_profile->page_81.sw_revision_major) << 8) | p_profile->page_81.sw_revision_minor))
            {
                p_sensor_saved->sensor_manufacturer[index].manufacturer_ant = p_profile->page_80.manufacturer_id;
                p_sensor_saved->sensor_serial[index].serial_ant       = p_profile->page_81.serial_number;
                p_sensor_saved->sensor_hw_version[index].version_ant      = p_profile->page_80.hw_revision;
                p_sensor_saved->sensor_model[index].model_ant        = p_profile->page_80.model_number;
                p_sensor_saved->sensor_sw_version[index].version_ant      = ((((uint16_t)p_profile->page_81.sw_revision_major) << 8) | p_profile->page_81.sw_revision_minor);
                if (evt_handler != NULL)
                {
                    evt_handler(EVENT_SENSOR_MANUFACTURER_RECEIVED, NULL, sensor_search_infor.sensor_type, 0, index);
                }
            }
#endif
        }
        sensor_saved_work_infor_release_write_lock(index);
    }
}

//-------------------------------------------------------------------------------------------
// Function Name : fe_ant_evt
// Purpose       : FE ANT事件处理函数
// Param[in]     : ant_evt_t *p_ant_evt
//                 void * p_context
// Param[out]    : None
// Return type   : static
// Comment       : 2020-04-16
//-------------------------------------------------------------------------------------------
static void fe_ant_evt(ant_evt_t *p_ant_evt, void * p_context)
{
    sensor_search_infor_t     sensor_search_infor;
    // sensor_connect_infor_t    *p_sensor_connect       = sensor_connect_infor_get(SENSOR_TYPE_FEC);
    sensor_connect_infor_t    sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_FEC, &sensor_connect);
    sensor_module_evt_handler evt_handler             = sensor_module_evt_handler_get();
    sensor_saved_work_t       *p_sensor_saved         = NULL;
    sensor_original_data_t    *p_sensor_original_data = sensor_original_data_get();
    sensor_work_state_t       sensor_work_state       = SENSOR_WORK_STATE_IDLE;
    int8_t                    index                   = -1;
    ret_code_t                err_code                = NRF_SUCCESS;
    bool manual_connect_status_sent = false;

    if (p_ant_evt->channel != m_ant_fe.channel_number)
    {
        return;
    }

    sensor_systime_update();

    memset ((uint8_t *)&sensor_search_infor, 0x00, sizeof(sensor_search_infor_t));
    ant_fe_disp_evt_handler(p_ant_evt, p_context, &p_sensor_original_data->feData);

    if (p_ant_evt->channel == m_ant_fe.channel_number)
    {
        switch (p_ant_evt->event)
        {
            case EVENT_CHANNEL_CLOSED:
                memset ((uint8_t *)&sensor_search_infor, 0, sizeof(sensor_search_infor_t));
                memcpy ((uint8_t *)&sensor_search_infor.sensor_id, (uint8_t *)&sensor_connect.sensor_id, sizeof(sensor_id_t));
                sensor_search_infor.radio_type  = SENSOR_RADIO_TYPE_ANT;
                sensor_search_infor.sensor_type = SENSOR_TYPE_FEC;

            	sensor_ant_leave_rx_search(SENSOR_TYPE_FEC);

                err_code = sd_ant_channel_unassign(m_ant_fe.channel_number);
                APP_ERROR_CHECK(err_code);
                m_ant_fe.channel_number = 0;
                sensor_ant_channel_num_unassign(SENSOR_TYPE_FEC);

                bool forbidden_mask = sensor_connect_infor_get_forbidden_mask(SENSOR_TYPE_FEC);
                if(sensor_connect_infor_get(SENSOR_TYPE_FEC, &sensor_connect))
                {
                    sensor_connect_infor_clear(SENSOR_TYPE_FEC);

                    if (NULL != evt_handler && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)
                    {
                        manual_connect_status_sent = true;
                        evt_handler(EVENT_SENSOR_MANUAL_CONNECT_STATUS, &sensor_connect.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                    }
                }

                if (sensor_saved_work_infor_request_write_lock(&sensor_search_infor, &p_sensor_saved, &index, &sensor_work_state))
                {
                    if (SENSOR_WORK_STATE_IDLE != sensor_work_state)
                    {
                        p_sensor_saved->rssi             [index] = 0;
                        p_sensor_saved->battery_voltage  [index] = 0xff;
                        p_sensor_saved->sensor_work_state[index] = SENSOR_WORK_STATE_SAVED;
                    }
                    sensor_saved_work_infor_release_write_lock(index);
                }

                if((!forbidden_mask && sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTED)       //连接完成后异常断连
                    || (forbidden_mask && sensor_connect.state == SENSOR_CONNECT_STATE_CLOSE_WAIT)
                    || sensor_connect.state == SENSOR_CONNECT_STATE_CONNECTING)                      //连接超时
                {
                    // connected状态下断连，检索saved数组是否有同类型sensor并进行连接
                    sensor_connect_from_saved_info(sensor_search_infor.sensor_type);
                }

                if (NULL != evt_handler)
                {
                    evt_handler(EVENT_SENSOR_STATE_CHANGE, NULL, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                    if (!manual_connect_status_sent)
                    {
                        evt_handler(EVENT_SENSOR_CONNECT_STATUS_CHANGE, &sensor_search_infor.sensor_id, sensor_search_infor.sensor_type, SENSOR_RADIO_TYPE_ANT, FALSE);
                    }
                }

                memset(&p_sensor_original_data->feData, 0xff, sizeof(fe_data_t));
                break;
            case EVENT_RX_FAIL_GO_TO_SEARCH:
                // err_code = sd_ant_channel_close(p_ant_evt->channel);
                // APP_ERROR_CHECK(err_code);
                // sensor_ant_close(SENSOR_TYPE_FEC);
            	sensor_ant_enter_rx_search(SENSOR_TYPE_FEC);
                break;
            case EVENT_RX_SEARCH_TIMEOUT:
                break;
            default:
                break;
         }
    }
}

NRF_SDH_ANT_OBSERVER(m_fe_ant_observer, ANT_FE_ANT_OBSERVER_PRIO, fe_ant_evt, &m_ant_fe);

/**
 * @*********************************************************************************************
 * @description: 设置FEC通道
 * @param {ant_id_t} *id
 * @return {*}
 * @*********************************************************************************************
 */
void ant_fe_rx_profile_setup(ant_id_t *id)
{
    // sensor_connect_infor_t *p_sensor_connect = sensor_connect_infor_get(SENSOR_TYPE_FEC);
    sensor_connect_infor_t    sensor_connect;
    sensor_connect_infor_get(SENSOR_TYPE_FEC, &sensor_connect);
    ant_channel_config_t   channel_config;
    ret_code_t             err_code          = NRF_SUCCESS;

    memcpy ((uint8_t *)&sensor_connect.sensor_id.ant_id, (uint8_t *)id, sizeof(ant_id_t));
    sensor_connect_infor_set(SENSOR_TYPE_FEC, &sensor_connect);

    /*
    //device num的组成
    //1byte   1byte    |     1byte      |      1byte                  从左到右高到低
    //   device id     | device type    |MSN:extended device number LSN:Transmission Type
    */
    uint16_t sensor_id = (uint16_t)id->id;
    uint8_t trans_type = CHAN_ID_TRANS_TYPE;
    if (id->id > 0xffff)
    {
        trans_type = id->trans_type;
    }

    //加载参数
    LoadChnConf_fe_rx(&channel_config);
    channel_config.device_number     = sensor_id;
    channel_config.transmission_type = trans_type;

    err_code = ant_fe_disp_init(&m_ant_fe, (const ant_channel_config_t *)&channel_config, FE_DISP_PROFILE_CONFIG(m_ant_fe));
    APP_ERROR_CHECK(err_code);
}

/**
 * @*********************************************************************************************
 * @description: 开启FE通道
 * @param {*}
 * @return {*}
 * @*********************************************************************************************
 */
void ant_fe_rx_open(void)
{
    ret_code_t             err_code          = NRF_SUCCESS;

    err_code = ant_fe_disp_open(&m_ant_fe);
    APP_ERROR_CHECK(err_code);
}

/*****************************************************************************
 * Function      : ant_fe_control_total_resistance
 * Description   : Set FE-C total_resistance.
 * Input         : uint16_t total_resistance
 * Output        : None
 * Return        :
 * Others        :
 * Record
 * 1.Date        : 20220123
 *   Author      : Andy
 *   Modification: Created function

*****************************************************************************/
ret_code_t ant_fe_control_total_resistance(uint8_t total_resistance)
{
    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_FEC, SENSOR_CONNECT_STATE_CONNECTED);
    ant_fe_page48_data_t page_48;
    ant_fe_profile_t *p_profile = &m_ant_fe;

    page_48.total_resistance = total_resistance;

    if (res)
    {
    	return ant_fe_control_total_resistance_tx(p_profile, &page_48);
    }

    return NRF_SUCCESS;
}

/*****************************************************************************
 * Function      : ant_fe_control_target_power
 * Description   : Set FE-C target power.
 * Input         : uint16_t target_power
 * Output        : None
 * Return        :
 * Others        :
 * Record
 * 1.Date        : 20201125
 *   Author      : Andy
 *   Modification: Created function

*****************************************************************************/
ret_code_t ant_fe_control_target_power(uint16_t target_power)
{
    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_FEC, SENSOR_CONNECT_STATE_CONNECTED);
    ant_fe_page49_data_t page_49;
    ant_fe_profile_t *p_profile = &m_ant_fe;

    page_49.target_power = target_power;

    if (res)
    {
    	return ant_fe_control_target_power_tx(p_profile, &page_49);
    }

    return NRF_SUCCESS;
}

/*****************************************************************************
 * Function      : ant_fe_control_grade
 * Description   : Set FE-C grade.
 * Input         : uint16_t grade
 * Output        : None
 * Return        :
 * Others        :
 * Record
 * 1.Date        : 20220123
 *   Author      : Andy
 *   Modification: Created function

*****************************************************************************/
ret_code_t ant_fe_control_grade(uint16_t grade)
{
    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_FEC, SENSOR_CONNECT_STATE_CONNECTED);
    ant_fe_page51_data_t page_51;
    ant_fe_profile_t *p_profile = &m_ant_fe;
    uint32_t err_code = 0;
    if (res)
    {
        page_51.grade = grade;
        err_code = ant_fe_control_grade_tx(p_profile, &page_51);
    }
    return err_code;
}

/*****************************************************************************
 * Function      : ant_fe_control_user_config
 * Description   : Set FE-C user config.
 * Input         : uint16_t user_weight, uint16_t bike_weight, uint16_t wheel_diameter, uint8_t gear_ratio
 * Output        : None
 * Return        :
 * Others        :
 * Record
 * 1.Date        : 20220510
 *   Author      : Andy
 *   Modification: Created function

*****************************************************************************/
ret_code_t ant_fe_control_user_config(uint16_t user_weight, uint16_t bike_weight, uint16_t wheel_diameter, uint8_t gear_ratio)
{
    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_FEC, SENSOR_CONNECT_STATE_CONNECTED);
    ant_fe_page55_data_t page_55;
    ant_fe_profile_t *p_profile = &m_ant_fe;

    page_55.user_weight = user_weight;
    page_55.bike_weight = bike_weight;
    page_55.wheel_diameter = wheel_diameter;
    page_55.gear_ratio = gear_ratio;

    if (res)
    {
    	return ant_fe_control_user_config_tx(p_profile, &page_55);
    }

    return NRF_SUCCESS;
}

/*****************************************************************************
 * Function      : ant_fe_control_capabilities_request
 * Description   : Request FE-C capabilities.
 * Input         : None
 * Output        : None
 * Return        :
 * Others        :
 * Record
 * 1.Date        : 20220510
 *   Author      : Andy
 *   Modification: Created function

*****************************************************************************/
ret_code_t ant_fe_control_capabilities_request(void)
{
    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_FEC, SENSOR_CONNECT_STATE_CONNECTED);
    ant_request_controller_t fe_capabilities_request;
    ant_fe_profile_t *p_profile = &m_ant_fe;

    if (res)
    {
        ant_request_controller_init(&fe_capabilities_request);
        fe_capabilities_request.page_70.page_number = 54; //Data Page 54 (0x36) – FE Capabilities
        fe_capabilities_request.page_70.command_type = ANT_PAGE70_COMMAND_PAGE_DATA_REQUEST; //Value = 1 (0x01) for Request Data Page
        fe_capabilities_request.page_70.transmission_response.items.transmit_count = 1; //Number of re-transmissions.

        return ant_request_controller_request(&fe_capabilities_request, p_profile->channel_number, &fe_capabilities_request.page_70);
    }

    return NRF_SUCCESS;
}

/*****************************************************************************
 * Function      : ant_fe_zero_offset_calib
 * Description   : FE-C zero offset calibration.
 * Input         :
 * Output        : None
 * Return        :
 * Others        :
 * Record
 * 1.Date        : 20201125
 *   Author      : Andy
 *   Modification: Created function

*****************************************************************************/
ret_code_t ant_fe_zero_offset_calib(void)
{
    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_FEC, SENSOR_CONNECT_STATE_CONNECTED);
    ant_fe_page1_data_t page_1;
    ant_fe_profile_t *p_profile = &m_ant_fe;

    g_Calibration_Key = true;
    page_1.zero_offset_calib = true;
    page_1.spin_down_calib = false;

    if (res)
    {
        return ant_fe_calib_tx(p_profile, &page_1);
    }

    return NRF_SUCCESS;
}

/*****************************************************************************
 * Function      : ant_fe_spin_down_calib
 * Description   : FE-C spin-down calibration.
 * Input         :
 * Output        : None
 * Return        :
 * Others        :
 * Record
 * 1.Date        : 20201125
 *   Author      : Andy
 *   Modification: Created function

*****************************************************************************/
ret_code_t ant_fe_spin_down_calib(void)
{
    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_FEC, SENSOR_CONNECT_STATE_CONNECTED);
    ant_fe_page1_data_t page_1;
    ant_fe_profile_t *p_profile = &m_ant_fe;

    page_1.zero_offset_calib = false;
    page_1.spin_down_calib = true;

    if (res)
    {
        return ant_fe_calib_tx(p_profile, &page_1);
    }

    return NRF_SUCCESS;
}

//FE更新任务
void ant_fe_data_update(uint32_t runtime_ms)
{
    static uint32_t last_time = 0;
    static uint32_t serial = 0;
    bool res = sensor_connect_infor_get_state(SENSOR_TYPE_FEC, SENSOR_CONNECT_STATE_CONNECTED);

    if (!res)
    {
    	return;
    }

    if(0 == last_time)
    {
        last_time = runtime_ms;
    }
    else if(6000 <= runtime_ms - last_time)
    {
        if(0 == serial)
        {
            ant_fe_control_capabilities_request();
            serial = 1;
        }
        else
        {
            // ant_fe_control_user_config(user_weight, bike_weight, wheel_diameter, gear_ratio);
            serial = 0;
        }

        last_time = runtime_ms;
    }
}

#endif //ANT_SENSOR_FEC_ENABLED
