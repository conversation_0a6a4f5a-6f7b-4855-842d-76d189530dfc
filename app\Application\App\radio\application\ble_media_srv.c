/************************************************************************
*
*Copyright(c) 2025, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   ble_media_srv.c
@Time    :   2025/08/12 11:07:46
*
**************************************************************************/

#include <rtthread.h>
#include <rtdevice.h>
#include <board.h>
#include <string.h>
#include "ble_media_srv.h"
#include "bf0_sibles.h"
#include "bf0_ble_gap.h"
#include "ble_lb55x_apple_media_c.h"
#include "algo_service_component_common.h"
#include "media.h"
#include "music_control.h"
#include "subscribe_service.h"

#define BLE_APP_MEDIA_LOG_TAG "APP_MEDIA"

#define BLE_APP_MEDIA_LOG_LVL               LOG_LVL_DBG       //定义log等级

#if (BLE_APP_MEDIA_LOG_LVL >= LOG_LVL_DBG)
    #define BLE_APP_MEDIA_LOG_D(...)        QW_LOG_D(BLE_APP_MEDIA_LOG_TAG, __VA_ARGS__)   //定义debug等级的log输出
#else
    #define BLE_APP_MEDIA_LOG_D(...)
#endif

#if (BLE_APP_MEDIA_LOG_LVL >= LOG_LVL_INFO)
    #define BLE_APP_MEDIA_LOG_I(...)        QW_LOG_I(BLE_APP_MEDIA_LOG_TAG, __VA_ARGS__)   //定义info等级的log输出
#else
    #define BLE_APP_MEDIA_LOG_I(...)
#endif

#if (BLE_APP_MEDIA_LOG_LVL >= LOG_LVL_WARNING)
    #define BLE_APP_MEDIA_LOG_W(...)        QW_LOG_W(BLE_APP_MEDIA_LOG_TAG, __VA_ARGS__)  //定义warning等级的log输出
#else
    #define BLE_APP_MEDIA_LOG_W(...)
#endif

#if (BLE_APP_MEDIA_LOG_LVL >= LOG_LVL_ERROR)
    #define BLE_APP_MEDIA_LOG_E(...)        QW_LOG_E(BLE_APP_MEDIA_LOG_TAG, __VA_ARGS__)  //定义error等级的log输出
#else
    #define BLE_APP_MEDIA_LOG_E(...)
#endif

#if (BLE_APP_MEDIA_LOG_LVL >= LOG_LVL_DBG)
    #define BLE_APP_MEDIA_LOG_HEX(TAG, width, buf, size)      QW_LOG_HEX(TAG, width, buf, size)
#else
    #define BLE_APP_MEDIA_LOG_HEX(TAG, width, buf, size)
#endif

#define BLE_APP_AMS_VOLUME_MAX_GRADE    16

typedef struct
{
    char *pb_state;
    char *pb_rate;
    char *elapsed_time;
} app_ams_pb_info_t;

static media_service_info_t media_svc_info = {0};

static void app_ams_parser_pb_info(app_ams_pb_info_t *info, uint8_t *raw_data)
{
    uint32_t len = 0;
    uint8_t stage = 0;
    info->pb_state = (char *)raw_data;

    while (*(raw_data + len) != 0)
    {
        if (*(raw_data + len) == ',')
        {
            *(raw_data + len) = 0;
            if (stage == 0)
                info->pb_rate = (char *)raw_data + len + 1;
            if (stage == 1)
            {
                info->elapsed_time = (char *)raw_data + len + 1;
                break;
            }

            stage++;
        }
        len++;
    }
}

static void app_ams_player_attribute_display(ble_ams_entity_attr_value_t *value)
{
    if (!value)
    {
        BLE_APP_MEDIA_LOG_E("app_ams_player_attribute_display: value is null");
        return;
    }
    uint8_t *data = mem_malloc(value->len + 1);
    memcpy(data, value->value, value->len);
    data[value->len] = '\0';

    switch (value->attr_id)
    {
        case BLE_AMS_PLAYER_ATTR_ID_NAME:
        {
            memset(media_svc_info.media_player_name, 0, sizeof(media_svc_info.media_player_name));
            strcpy(media_svc_info.media_player_name, (const char *)data);
            BLE_APP_MEDIA_LOG_D("app_ams_player_attribute_display: player name:%s", media_svc_info.media_player_name);
        }
        break;

        case BLE_AMS_PLAYER_ATTR_ID_PB_INFO:
        {
            app_ams_pb_info_t pb_info = {0};
            app_ams_parser_pb_info(&pb_info, data);
            BLE_APP_MEDIA_LOG_D("app_ams_player_attribute_display: pb_state:%s, pb_rate:%s, elapsed_time:%s", pb_info.pb_state, pb_info.pb_rate, pb_info.elapsed_time);

            media_svc_info.elapsed_change_flag = true;
            media_svc_info.play_status = atoi(pb_info.pb_state);
            media_svc_info.track_time_elapsed = (float)atoi(pb_info.elapsed_time);
            BLE_APP_MEDIA_LOG_D("app_ams_player_attribute_display: play_status:%d, track_time_elapsed:%d", media_svc_info.play_status, media_svc_info.track_time_elapsed);
            MusicCtrl_OnBluetoothEventUpdated(MUSIC_INFO_MAX);
        }
        break;

        case BLE_AMS_PLAYER_ATTR_ID_VOL:
        {
            media_svc_info.volume_level = strtof((const char *)data, NULL) * BLE_APP_AMS_VOLUME_MAX_GRADE;
            //TODO: 上报音量事件给应用层
            BLE_APP_MEDIA_LOG_D("app_ams_player_attribute_display: volume_level:%d", media_svc_info.volume_level);
            MusicCtrl_OnBluetoothEventUpdated(MUSIC_INFO_VOLUME);
        }
        break;

        default:
            break;
    }
    mem_free(data);
}

static void app_ams_queue_attribute_display(ble_ams_entity_attr_value_t *value)
{
    if (!value)
    {
        BLE_APP_MEDIA_LOG_E("app_ams_queue_attribute_display: value is null");
        return;
    }

    uint8_t *data = mem_malloc(value->len + 1);
    memcpy(data, value->value, value->len);
    data[value->len] = '\0';

    switch (value->attr_id)
    {
        case BLE_AMS_QUEUE_ATTR_ID_INDEX:
        {
            BLE_APP_MEDIA_LOG_D("app_ams_queue_attribute_display: queue index:%s", data);
        }
        break;

        case BLE_AMS_QUEUE_ATTR_ID_COUNT:
        {
            BLE_APP_MEDIA_LOG_D("app_ams_queue_attribute_display: queue count:%s", data);
        }
        break;

        case BLE_AMS_QUEUE_ATTR_ID_SHUFFLE:
        {
            BLE_APP_MEDIA_LOG_D("app_ams_queue_attribute_display: shuffle:%s", data);
        }
        break;

        case BLE_AMS_QUEUE_ATTR_ID_REPEAT:
        {
            BLE_APP_MEDIA_LOG_D("app_ams_queue_attribute_display: repeat:%s", data);
        }
        break;

        default:
            break;
    }
    mem_free(data);
}

static void app_ams_track_attribute_display(ble_ams_entity_attr_value_t *value)
{
    uint16_t name_len = 0;

    if (!value)
    {
        BLE_APP_MEDIA_LOG_E("app_ams_track_attribute_display: value is null");
        return;
    }

    uint8_t *data = mem_malloc(value->len + 1);
    memcpy(data, value->value, value->len);
    data[value->len] = '\0';

    switch (value->attr_id)
    {
        case BLE_AMS_TRACK_ATTR_ID_ARTIST:
        {
            // if (media_svc_info.song_chage_finish_flag >= 2)
            // {
            //     BLE_APP_MEDIA_LOG_E("app_ams_track_attribute_display: song_chage_finish_flag:%d", media_svc_info.song_chage_finish_flag);
            //     break;
            // }

            // media_svc_info.song_chage_finish_flag++;
            if (value->len >= AMS_MAX_NAME_LENGTH)
            {
                name_len = AMS_MAX_NAME_LENGTH;
            }
            else
            {
                name_len = value->len + 1;
            }
            memset(media_svc_info.artist_name, 0, sizeof(media_svc_info.artist_name));
            snprintf(media_svc_info.artist_name, name_len, "%s", data);
            BLE_APP_MEDIA_LOG_D("app_ams_track_attribute_display: artist_name:%s", media_svc_info.artist_name);
            MusicCtrl_OnBluetoothEventUpdated(MUSIC_INFO_ARTIST);
        }
        break;

        case BLE_AMS_TRACK_ATTR_ID_ALBUM:
        {
            if (value->len >= AMS_MAX_NAME_LENGTH)
            {
                name_len = AMS_MAX_NAME_LENGTH;
            }
            else
            {
                name_len = value->len + 1;
            }
            memset(media_svc_info.album_name, 0, sizeof(media_svc_info.album_name));
            snprintf(media_svc_info.album_name, name_len, "%s", data);
            BLE_APP_MEDIA_LOG_D("app_ams_track_attribute_display: album_name:%s", media_svc_info.album_name);
        }
        break;

        case BLE_AMS_TRACK_ATTR_ID_TILTE:
        {
            // if (media_svc_info.song_chage_finish_flag >= 2)
            // {
            //     BLE_APP_MEDIA_LOG_E("app_ams_track_attribute_display: song_chage_finish_flag:%d", media_svc_info.song_chage_finish_flag);
            //     break;
            // }

            // media_svc_info.song_chage_finish_flag++;

            if (value->len >= AMS_MAX_NAME_LENGTH)
            {
                name_len = AMS_MAX_NAME_LENGTH;
            }
            else
            {
                name_len = value->len + 1;
            }
            memset(media_svc_info.title_name, 0, sizeof(media_svc_info.title_name));
            snprintf(media_svc_info.title_name, name_len, "%s", data);
            BLE_APP_MEDIA_LOG_D("app_ams_track_attribute_display: title_name:%s", media_svc_info.title_name);
            MusicCtrl_OnBluetoothEventUpdated(MUSIC_INFO_TRACK);
        }
        break;

        case BLE_AMS_TRACK_ATTR_ID_DURATION:
        {
            static float total_time = 0.0;
            if (total_time != atof((const char *)data))
            {
                media_svc_info.isSongChanged = true;
                media_svc_info.song_chage_finish_flag = 0;
            }

            total_time = atof((const char *)data);
            media_svc_info.track_time_total = (uint32_t)total_time;
            BLE_APP_MEDIA_LOG_D("app_ams_track_attribute_display: track_time_total:%d", media_svc_info.track_time_total);
            MusicCtrl_OnBluetoothEventUpdated(MUSIC_INFO_MAX);
        }
        break;

        default:
            break;
    }
    mem_free(data);
}

static void service_ble_ams_callback(const void *data, uint32_t len)
{
    //TODO: 收到AMS事件通知，将消息上报给音乐控制处理层
    ble_ams_msg_t *ams_msg = (ble_ams_msg_t *)data;
    // BLE_APP_MEDIA_LOG_D("service_ble_ams_callback: msg_type:%d", ams_msg->msg_type);
    if (ams_msg->msg_type == BLE_AMS_ENTITY_ATTRIBUTE_PAIR_IND)
    {
        ble_ams_entity_attr_value_t *attr_value = ams_msg->msg_data.entity_attr_value;
        BLE_APP_MEDIA_LOG_HEX("attr_value", 16, (uint8_t *)attr_value, sizeof(ble_ams_entity_attr_value_t) + attr_value->len);
        if (media_svc_info.ams_exists == false)
        {
            media_svc_info.ams_exists = true;
            MusicCtrl_OnBluetoothEventUpdated(MUSIC_INFO_MAX);
        }
        switch (attr_value->entity_id)
        {
            case BLE_AMS_ENTITY_ID_PLAYER:
            {
                app_ams_player_attribute_display(attr_value);
            }
            break;

            case BLE_AMS_ENTITY_ID_QUEUE:
            {
                app_ams_queue_attribute_display(attr_value);
            }
            break;

            case BLE_AMS_ENTITY_ID_TRACK:
            {
                app_ams_track_attribute_display(attr_value);
            }
            break;

            default:
                break;
        }
    }
    else if (ams_msg->msg_type == BLE_GAP_DISCONNECTED_IND)
    {
        memset(&media_svc_info, 0, sizeof(media_svc_info));
        MusicCtrl_OnBluetoothEventUpdated(MUSIC_INFO_MAX);
    }
}

/************************************************************************
 *@function:void ble_media_info_get(media_service_info_t *info);
 *@brief:获取音乐控制信息
 *@param: info - 音乐控制信息
 *@return: null
*************************************************************************/
void ble_media_info_get(media_service_info_t *info)
{
    memcpy(info, &media_svc_info, sizeof(media_svc_info));
}

/************************************************************************
 *@function:media_service_info_t *media_info_ptr_get(void);
 *@brief:获取音乐控制信息指针(仅供协议层使用)
 *@param: null
 *@return: media_service_info_t * - 音乐控制信息指针
*************************************************************************/
media_service_info_t *media_info_ptr_get(void)
{
    return &media_svc_info;
}

/************************************************************************
 *@function:void ble_media_info_clear(void);
 *@brief:清空音乐控制信息
 *@param: null
 *@return: null
*************************************************************************/
void ble_media_info_clear(void)
{
    memset(&media_svc_info, 0, sizeof(media_svc_info));
}

/************************************************************************
 *@function:int ble_app_ams_init(void);
 *@brief:初始化ams事件订阅
 *@param: null
 *@return:0 - 成功，其他 - 失败
*************************************************************************/
int ble_app_ams_init(void)
{
    optional_config_t config = {.sampling_rate = 0};
    qw_dataserver_subscribe_id(DATA_ID_EVENT_MEDIA_CTRL, service_ble_ams_callback, &config);
    return 0;
}

/************************************************************************
 *@function:int ble_media_play_control(uint8_t control_type, uint8_t control_value);
 *@brief:音乐播放控制
 *@param: control_type - 控制类型 参照BLE_MEDIA_CMD_E枚举
 *@param: control_value - 控制值
 *@return:0 - 成功
*************************************************************************/
int ble_media_play_control(uint8_t control_type, uint8_t control_value)
{
    if (media_svc_info.ams_exists == true)
    {
        if (control_type >= BLE_AMS_CMD_PLAY && control_type <= BLE_AMS_CMD_TOTAL)
        {
            ble_ams_send_command((ble_ams_cmd_t )control_type);
        }
    }

    // 处理安卓端的音乐控制事件
    media_ctrl_cmd(control_type, control_value);

    return 0;
}

static rt_err_t media_test(int argc, char **argv)
{
    if (argc == 2)
    {
        ble_media_play_control(atoi(argv[1]), 0);
    }
    else if (argc == 3)
    {
        ble_media_play_control(atoi(argv[1]), atoi(argv[2]));
    }
    else
    {
        BLE_APP_MEDIA_LOG_E("media_test: invalid parameter");
    }
    return 0;
}

FINSH_FUNCTION_EXPORT(media_test, test media service);
MSH_CMD_EXPORT(media_test, test media service);
