/************************************************************************
* 
*Copyright(c) 2024, igpsport Software Co., Ltd.
*All Rights Reserved.
@File    :   DialDataSelectModel.cpp
@Time    :   2024/12/13 20:02:17
* 
**************************************************************************/
#include "DialDataSelectModel.h"
#include "../touchgfx_js/touchgfx_js_api.h"

DialDataSelectModel::DialDataSelectModel() :
	set_select_data_type_(this, &DialDataSelectModel::set_select_data_type)
	,dataType_{0}

{

}

DialDataSelectModel::~DialDataSelectModel()
{

}

/**
 * @brief PageModel::setup() 进入页面通知
 */
void DialDataSelectModel::setup()
{
	//The method is an intentionally-blank override.
}

/**
 * @brief PageModel::quit() 退出页面通知
 */
void DialDataSelectModel::quit()
{
	//The method is an intentionally-blank override.
}

/**
 * @brief PageModel::notify() 更新检查, 在 PageBase::notify() 中调用
 */
void DialDataSelectModel::notify()
{
	//The method is an intentionally-blank override.
}


// Notification
void DialDataSelectModel::set_select_data_type(uint8_t t1)
{

	// select_data_type_ = t1;
	set_dial_data_type_by_index(get_dial_edit_data_index(), t1, NULL);
	set_dial_edit_data_index_increase();
}

// Parameters function
